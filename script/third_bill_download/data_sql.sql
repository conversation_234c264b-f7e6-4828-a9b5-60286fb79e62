-- 查找缺失的数据
-- Step 1: 获取所有租户和他们存在的年月
WITH TenantMonths AS (
    SELECT DISTINCT
        tenant_code,
        DATEFROMPARTS(YEAR(sync_time), MONTH(sync_time), 1) AS month_start
    FROM third_meituan_shangou_bill_details WITH(NOLOCK)
    where sync_time >= '2025-04-01'
),

-- Step 2: 生成每个租户对应月份的所有日期
AllDates AS (
    SELECT
        tm.tenant_code,
        DATEADD(DAY, n - 1, tm.month_start) AS date_in_month,
        EOMONTH(tm.month_start) AS month_end
    FROM
        TenantMonths tm
        INNER JOIN (
            SELECT TOP (31) ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) AS n
            FROM sys.all_objects
        ) AS Numbers ON Numbers.n <= DATEDIFF(DAY, tm.month_start, EOMONTH(tm.month_start)) + 1
),

-- Step 3: 获取原始数据中的 (tenant_code, sync_time)
ExistingDates AS (
    SELECT DISTINCT
        tenant_code,
        sync_time
    FROM third_meituan_shangou_bill_details WITH(NOLOCK)
    where sync_time >= '2025-04-01'
)

-- Step 4: 查找所有 AllDates 中不在 ExistingDates 中的记录
SELECT
    ad.tenant_code,
    ad.date_in_month AS missing_date
FROM
    AllDates ad
    LEFT JOIN ExistingDates ed
        ON ad.tenant_code = ed.tenant_code AND ad.date_in_month = ed.sync_time
WHERE
    ed.sync_time IS NULL
ORDER BY
    ad.tenant_code,
    ad.date_in_month;