import os
import random
from DrissionPage import ChromiumPage, ChromiumOptions
import time
from pathlib import Path
from datetime import datetime, timedelta
import common
import re

def run(account, work_type="create"):
    # 获取当前用户的工作目录
    user_home = Path.cwd()

    # 提示用户输入账号和密码
    username = account.get('username')
    password = account.get('password')

    # 指定用户数据路径（根据账号区分）
    user_data_dir = f"meituan_tangou_{username}"
    # user_data_path.mkdir(parents=True, exist_ok=True)
    # 获取 Windows 默认下载目录
    default_download_path = user_home / "Downloads"
    default_download_path.mkdir(parents=True, exist_ok=True)
    default_temp_path = user_home / "Temp"
    default_download_path.mkdir(exist_ok=True)
    print(f'用户信息保存文件夹: {user_data_dir}')
    # 账号 1
    options = (ChromiumOptions()
               .set_browser_path(r'./chrome/chrome.exe')
               .set_tmp_path(default_temp_path)
               .set_user(user_data_dir)
               .set_download_path(default_download_path))

    # 初始化 Drission 和 DrissionPage，并指定用户数据路径
    page = ChromiumPage(addr_or_opts=options)
    page.set.download_path(default_download_path)
    page.set.when_download_file_exists('overwrite')
    page.set.window.normal()
    # 解析日期
    prev_sync_date = account.get("prev-sync-date")
    current_date = datetime.today().date()

    # 计算最早的相差天数
    earliest_days_difference = (current_date - prev_sync_date).days - 1

    try:
        if earliest_days_difference <= 0 and not account.get('task-nos'):
            print(f"上次同步日期 {prev_sync_date}且无待下载任务 本次无需同步，跳过...")
            return
        # 打开目标页面
        page.get("https://e.dianping.com/app/merchant-platform/9a35643e8df8416")
        page.wait.eles_loaded('x://button[.//*[text()="生成账单"] or text()="登录"]')

        # 检查是否重定向到登录页
        if "/login" in page.url or '/logon' in page.url:
            print("检测到登录页面，请手动完成登录...")
            # 发送通知消息
            common.send_oa_msg_with_html(f"美团账号[{username}]账单下载用户登录失效, 请及时处理", "", "13685", "7", "https://moa.9ji.com")
            # 设置登录用户信息
            page.ele('x://input[@id="account"]').input(username, clear=True)
            page.ele('x://input[@id="password"]').input(password, clear=True)
            page.ele('x://button[text()="登录"]').click()
            while "/login" in page.url or '/logon' in page.url:  # 等待用户登录
                time.sleep(1)
            print("登录完成，继续操作...")
            page.get("https://e.dianping.com/app/merchant-platform/9a35643e8df8416")
            page.wait.eles_loaded('x://button[.//*[text()="生成账单"] or text()="登录"]')

        # 隐藏一些干扰元素
        page.run_js("""
            let aside = document.querySelector('.ai-root-aside');
            if (aside) {
                aside.style.display = 'none';
            }
            const commonModal = document.querySelector('div.common-modal');
            if (commonModal) {
              commonModal.style.display = 'none';
            }
        """)

        # 监听接口
        page.listen.start(targets=['/finance/ajax/downloadManagement/list'])
        task_nos = account.get('task-nos', [])
        # 获取当天所在的位置
        def create_task():
            def get_today_index(eles):
                return next((i for i, ele in enumerate(eles) if "today" in ele.attr("class")), -1)

            page.ele('x://div[contains(@class, "account-selector-component-root")]').click()
            page.wait.ele_displayed('x://div[contains(@class, "account-selector-component-root")]/div[contains(@class, "selector-panel")]', timeout=20)
            page.ele('x://div[contains(@class, "account-selector-component-root")]//input[@value="公司账户"]').click()
            page.ele('x://div[contains(@class, "account-selector-component-root")]//input[@value="门店账户"]').click()
            page.ele('x://label[./span[text()="收益明细"]]').click()
            page.ele('x://label[./span[text()="团购"]]').click()

            if not account.get('task-nos'):
                account['task-nos'] = task_nos

            res = page.listen.wait()
            # 遍历索引并点击
            for d_index in range(earliest_days_difference, 0, -1):
                # 点击开始日期输入框
                div_range_xpath = 'x://div[contains(@class, "ant-calendar-date-panel")]'
                day_range_start_input = page.ele("tag:input@@placeholder=开始日期")
                page.wait.ele_displayed(day_range_start_input)
                day_range_start_input.click()
                page.wait.ele_displayed(div_range_xpath)
                # 获取所有可选天xpath
                td_eles_xpath = (
                    'x://div[contains(@class, "ant-calendar-date-panel")]'
                    '//td[@role="gridcell" and not(.//descendant::*[@aria-disabled="true"])]')
                day_td_eles = page.eles(td_eles_xpath)

                # 查找包含 'today' 类的索引
                today_index = get_today_index(day_td_eles)
                if today_index - d_index < 0:
                    prev_month = page.ele(
                        'x://a[contains(@class, "ant-calendar-prev-month-btn")][1]')
                    prev_month.click()
                    time.sleep(0.1)
                    day_td_eles = page.eles(td_eles_xpath)
                    today_index = get_today_index(day_td_eles)
                target_d_index = today_index - d_index
                day_td_eles[target_d_index].click()
                time.sleep(0.1)
                day_td_eles = page.eles(td_eles_xpath)
                day_td_eles[target_d_index].click()
                time.sleep(0.2)
                matched_task = None
                curr_sync_date = current_date - timedelta(days=d_index)
                time_interval = f'{curr_sync_date}  00:00:00至{curr_sync_date}  23:59:59'

                if '/finance/ajax/downloadManagement/list' in res.response.url:
                    task_list = res.response.body.get('data', [])
                    matched_task = next((task for task in task_list if task.get('timeInterval') == time_interval), None)
                if not matched_task:
                    # 点击导出账单按钮
                    page.ele('x://button[.//*[text()="生成账单"]]').click()
                    # 循环等待账单导出任务响应，最多1分钟
                    start_time = time.time()
                    while time.time() - start_time < 60:
                        res = page.listen.wait()
                        if '/finance/ajax/downloadManagement/list' in res.response.url:
                            task_list = res.response.body.get('data', [])
                            matched_task = next((task for task in task_list if task.get('timeInterval') == time_interval), None)
                            if matched_task:
                                break
                        time.sleep(0.05)

                if res is None or '/finance/ajax/downloadManagement/list' not in res.response.url:
                    raise Exception("等待账单导出任务响应超时")
                # 获取任务编码
                account['prev-sync-date'] = curr_sync_date
                print(f'导出账单, 抓取到请求地址: {res.response.url}')

                if not matched_task:
                    raise Exception("找不到导出账单任务")
                task_nos.append({'task-no': matched_task.get('id'), 'sync-date': curr_sync_date})
                common.update_yaml(common.config)

        # 定义刷新逻辑
        def refresh_until_exported():
            res = page.listen.wait()  # 等待并获取一个数据包

            while True:
                if not task_nos:
                    # 处理完所有任务，退出循环
                    break

                print(f'刷新导出列表, 抓取到请求地址: {res.response.url}')
                task_list = res.response.body.get('data', [])
                # 先将 task_list 转换成字典
                task_dict = {task['id']: task for task in task_list}
                # 遍历 task_nos 处理任务
                for task_no_obj in list(task_nos):  # 使用 list() 避免迭代时修改集合
                    task_no = task_no_obj.get('task-no')
                    task = task_dict.get(task_no)
                    if not task:
                        print(f"任务 {task_no} 未找到，跳过...")
                        continue

                    if task['downloadStatus'] == 2:
                        print("账单已导出，准备下载...")
                        MAX_RETRY_DURATION = 90  # 最大重试时长（秒）
                        start_time = time.time()
                        data = {}
                        filepath = None
                        while time.time() - start_time < MAX_RETRY_DURATION:
                            download_btn = page.ele(
                                f'x://tr[@data-row-key={task_no}]/td[last()]//a[text()="下载"]')
                            page.scroll.to_see(download_btn)

                            download_to_file = download_btn.click.to_download()
                            filepath = download_to_file.wait()

                            if isinstance(filepath, str):
                                print(filepath)  # 打印数据包url
                                break
                            else:
                                wait_time = random.uniform(0, 60)  # 0~60 秒的随机等待
                                print(f"{task_no}下载失败,结果:{filepath}，{wait_time:.2f} 秒后重试...")
                                page.quit()
                                time.sleep(wait_time)
                                run(account, work_type=work_type)
                                return
                        if not isinstance(filepath, str):
                            continue
                        # 上传文件
                        upload_result = common.upload_meituan_bill(filepath)
                        if upload_result['code'] >0 or not upload_result['data'] or not upload_result['data']['fileUrl']:
                            continue
                        task_nos.remove(task_no_obj)
                        common.update_yaml(common.config)  # 更新配置文件
                        # 进行同步
                        file_url = upload_result['data']['fileUrl']
                        sync_result = common.download_meituan_bill(account.get('tenant-code'), file_url,
                                                                   bill_file_type=2, sync_time=task_no_obj.get('sync-date'))
                        if sync_result:
                            print(f"账单 {task_no_obj} 同步结果: {sync_result}")
                    elif task['downloadStatus'] == 1:
                        print(f"账单 {task_no_obj} 未导出，正在刷新...")
                    else:
                        print(f"账单 {task_no_obj} 导出失败，直接删除...")
                        task_nos.remove(task_no_obj)
                        common.update_yaml(common.config)  # 更新配置文件
                    time.sleep(random.uniform(5, 30))

        select_page_ele = page.ele('x://div[contains(@class, "page-size-select")]//div[@aria-controls]')
        select_page_ele.wait.clickable()
        select_page_ele.click()
        aria_controls = select_page_ele.attr('aria-controls')
        max_page_record_ele = page.ele(f'x:(//div[@id="{aria_controls}"]//li[@role="option"])[last()]')
        max_page_record_ele.wait.displayed()
        max_page_record_ele.click()

        # 把首次消费掉
        page.listen.wait()
        # 执行刷新逻辑
        if work_type == 'download':
            refresh_until_exported()
        elif work_type == 'create':
            create_task()

    finally:
        # 关闭浏览器
        page.quit()


if __name__ == "__main__":
    # run()
    pass
