import os
from datetime import datetime

import psutil
import requests
import yaml

config_name = 'config.yml'
config = {}  # 初始化为 None


def load_config(config_file_name):
    """读取 YAML 配置文件"""
    global config
    global config_name
    config_name = config_file_name
    with open(config_name, "r", encoding="utf-8") as file:
        config = yaml.safe_load(file)
    return config


def download_meituan_bill(tenant_code, download_file_url, bill_file_type=1, sync_time=None):
    url = f"https://{config.get('moa-url')}/cloudapi_nc/orderservice/admin/api/meituan/finance/downloadBillFile/v1?xservicename=oa-orderservice"
    headers = {
        "Content-Type": "application/json"
    }
    payload = {
        "tenantCode": tenant_code,
        "downloadFileUrl": download_file_url,
        "billFileType": bill_file_type,
        "syncTime": sync_time.strftime("%Y-%m-%d")+' 00:00:00'
    }
    print(f'请求url: {url} 参数: {payload}')
    response = requests.post(url, json=payload, headers=headers)

    return response.json()  # 返回JSON格式的数据
def upload_meituan_bill(file_path):
    url = f"https://{config.get('moa-url')}/cloudapi_nc/orderservice/admin/api/meituan/finance/uploadMeituanFinance?xservicename=oa-orderservice"
    # 准备文件上传
    with open(file_path, 'rb') as f:
        files = {
            'file': (os.path.basename(file_path), f, 'application/octet-stream')
        }

        # 发送 POST 请求上传文件
        response = requests.post(url, files=files)

    # 输出响应结果
    print(f'请求url: {url} 文件: {file_path} 响应状态码: {response.status_code} 响应内容: {response.text}')

    return response.json()  # 返回JSON格式的数据

# 修改 YAML 数据
def update_yaml(new_data):
    with open(config_name, "w", encoding="utf-8") as file:
        yaml.dump(new_data, file, allow_unicode=True, default_flow_style=False)

def kill_process_by_name(name, timeout=5):
    """
    查找所有进程名包含 name 的进程，先尝试优雅终止，超时后强制杀死。
    过滤掉当前脚本进程自身。
    """
    current_pid = os.getpid()
    procs = [p for p in psutil.process_iter(['pid', 'name', 'cmdline'])
             if p.pid != current_pid and (
                name in (p.info['name'] or '') or
                any(name in cmd for cmd in (p.info['cmdline'] or [])))]

    if not procs:
        print(f"没有找到任何包含 '{name}' 的进程。")
        return

    for p in procs:
        try:
            print(f"尝试终止 PID={p.pid} ({p.info['name']}) …")
            p.terminate()  # 发送 SIGTERM
        except Exception as e:
            print(f"无法优雅终止 PID={p.pid}：{e}")

    # 等待进程退出
    gone, alive = psutil.wait_procs(procs, timeout=timeout)
    for p in alive:
        try:
            print(f"PID={p.pid} 未能优雅退出，强制杀死…")
            p.kill()  # 发送 SIGKILL
        except Exception as e:
            print(f"无法强制杀死 PID={p.pid}：{e}")
    print("所有目标进程已停止。")

def send_oa_msg_with_html(content, link, ch999ids, msg_type, host):
    send_harder_url = f"{host}/cloudapi_nc/org_service/api/myMessage/myMessage/oaMessagePush?xservicename=oa-org"
    param = {
        "act": "oaMessagePush",
        "content": content,
        "link": link,
        "ch999Ids": [int(id.strip()) for id in ch999ids.split(',')],
        "msgType": msg_type
    }
    print(f"推送oa的地址:{send_harder_url}, 参数: {param}")
    response = requests.post(send_harder_url, json=param)
    print(f"推送oa结果: {response.text}")