import argparse
import sys

import meituan_shangou
import traceback
import common
import meituan_tuangou

if __name__ == "__main__":
    # 启动脚本前，先检查并关闭已存在的 ollama 进程
    print("检查并关闭已启动的 third_bill_download.exe 进程...")
    common.kill_process_by_name("third_bill_download.exe")
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="运行美团闪购账户任务")
    parser.add_argument("--config", type=str, default="config.yml", help="指定配置文件（默认: config.yml）")
    args = parser.parse_args()

    config_name = args.config
    config = common.load_config(config_name)
    # 访问美团闪购账户
    shangou_accounts = config.get("meituan-shangou", [])
    for account in shangou_accounts:
        try:
            meituan_shangou.run(account, work_type='create')
        except Exception as e:
            # 互不干扰
            print(f"Error create account {account.get('username')}: {e}")
            traceback.print_exc()

    for account in shangou_accounts:
        try:
            meituan_shangou.run(account, work_type='download')
        except Exception as e:
            # 互不干扰
            print(f"Error download account {account.get('username')}: {e}")
            traceback.print_exc()

    # 访问美团团购账户
    tuangou_accounts = config.get("meituan-tuangou", [])
    for account in tuangou_accounts:
        try:
            meituan_tuangou.run(account, work_type='create')
        except Exception as e:
            # 互不干扰
            print(f"Error create account {account.get('username')}: {e}")
            traceback.print_exc()

    for account in tuangou_accounts:
        try:
            meituan_tuangou.run(account, work_type='download')
        except Exception as e:
            # 互不干扰
            print(f"Error download account {account.get('username')}: {e}")
            traceback.print_exc()

    # 退出程序
    # sys.exit()