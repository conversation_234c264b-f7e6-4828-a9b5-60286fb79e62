<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.1.17.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.jiuji.oa</groupId>
    <artifactId>oa-order</artifactId>
    <packaging>pom</packaging>
    <version>1.0.4-SNAPSHOT</version>
    <modules>
        <module>oa-order-service</module>
        <module>oa-order-stub</module>
        <module>oa-order-vo</module>
    </modules>


    <properties>
        <jiuji.common.version>0.0.9-SNAPSHOT</jiuji.common.version>
        <jiuji.foundation.version>0.0.8-SNAPSHOT</jiuji.foundation.version>
        <mybatisplus.version>3.1.0</mybatisplus.version>
        <mybatis.version>1.3.1</mybatis.version>
        <spring-cloud-version>Greenwich.SR4</spring-cloud-version>
        <swagger.version>2.7.0</swagger.version>
        <lombok.version>1.18.10</lombok.version>
        <fastjson.version>1.2.83</fastjson.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <delay.queue.version>1.3.0-RELEASE</delay.queue.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud-version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--依赖统一管理-->
            <dependency>
                <artifactId>common-vo</artifactId>
                <groupId>com.jiuji.tc</groupId>
                <version>${jiuji.common.version}</version>
            </dependency>
            <dependency>
                <artifactId>common-utils</artifactId>
                <groupId>com.jiuji.tc</groupId>
                <version>${jiuji.common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.9.10.8</version>
            </dependency>
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>1.4.19</version>
            </dependency>

            <dependency>
                <groupId>commons-jxpath</groupId>
                <artifactId>commons-jxpath</artifactId>
                <version>1.4.0</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            <!-- Log4j2 异步支持 -->
            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>3.3.6</version>
            </dependency>
            <!--日志  log4j2 升级-->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>2.17.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>2.17.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>2.17.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-jul</artifactId>
                <version>2.17.1</version>
            </dependency>

            <dependency>
                <groupId>com.github.dubasdey</groupId>
                <artifactId>log4j2-jsonevent-layout</artifactId>
                <version>0.0.4</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>2.0</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>1.5.13</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.2</version>
            </dependency>
            <!-- easyexcel 依赖 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>2.2.6</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>2.9.7</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>1.9.3</version>
            </dependency>
            <dependency>
                <groupId>com.doudian</groupId>
                <artifactId>doudian-sdk-java</artifactId>
                <version>1.1.0-20240529192335</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>4.5.12</version>
            </dependency>
            <!--售后服务引入的oa包 start-->
            <dependency>
                <groupId>com.jiuji.cloud</groupId>
                <artifactId>after-cloud</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <!--售后服务引入的oa包 end-->
            <!--三星免息分期加密jar-->
            <dependency>
                <groupId>com.seed</groupId>
                <artifactId>seed</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.openapi</groupId>
                <artifactId>sdk</artifactId>
                <version>1.1.240</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>6.1.0.Final</version>
            </dependency>
            <dependency>
                <groupId>com.meituan</groupId>
                <artifactId>MtOpJavaSDK</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.jiuji.cloud</groupId>
                <artifactId>org-cloud</artifactId>
                <version>0.0.18-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.kuaishou</groupId>
                <artifactId>sdk</artifactId>
                <version>1.0.751</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.shangou</groupId>
                <artifactId>sdk</artifactId>
                <version>1.0.46.1</version>
            </dependency>
            <!-- map struct 需要在 lombok 后不然会构建会报错 -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ch999.common</groupId>
                <artifactId>utils</artifactId>
                <version>1.2-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jiuji.infra</groupId>
                <artifactId>common-delay-queue</artifactId>
                <version>${delay.queue.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.5.7</version>
            </dependency>
            <dependency>
                <groupId>com.jiuji.oa.stock</groupId>
                <artifactId>baozun-util</artifactId>
                <version>0.0.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-starter-consul-config</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-starter-consul-discovery</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                        <groupId>com.baomidou</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>elasticsearch</artifactId>
                        <groupId>org.elasticsearch</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>xstream</artifactId>
                        <groupId>com.thoughtworks.xstream</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>springfox-swagger2</artifactId>
                        <groupId>io.springfox</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>springfox-swagger-ui</artifactId>
                        <groupId>io.springfox</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-context-support</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-cloud-starter-config</artifactId>
                        <groupId>org.springframework.cloud</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-cloud-openfeign-core</artifactId>
                        <groupId>org.springframework.cloud</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-cloud-config-server</artifactId>
                        <groupId>org.springframework.cloud</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-boot-starter-log4j2</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-boot-starter-data-redis</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-boot-starter-aop</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-boot-starter-amqp</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-boot-starter-actuator</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-boot-actuator</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mysql-connector-java</artifactId>
                        <groupId>mysql</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mybatis-plus-boot-starter</artifactId>
                        <groupId>com.baomidou</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mssql-jdbc</artifactId>
                        <groupId>com.microsoft.sqlserver</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>micrometer-registry-prometheus</artifactId>
                        <groupId>io.micrometer</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>micrometer-core</artifactId>
                        <groupId>io.micrometer</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mapstruct-processor</artifactId>
                        <groupId>org.mapstruct</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mapstruct</artifactId>
                        <groupId>org.mapstruct</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j2-jsonevent-layout</artifactId>
                        <groupId>com.github.dubasdey</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jsoup</artifactId>
                        <groupId>org.jsoup</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jedis</artifactId>
                        <groupId>redis.clients</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-module-parameter-names</artifactId>
                        <groupId>com.fasterxml.jackson.module</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-mapper-asl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-datatype-jsr310</artifactId>
                        <groupId>com.fasterxml.jackson.datatype</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-datatype-jdk8</artifactId>
                        <groupId>com.fasterxml.jackson.datatype</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>hutool-all</artifactId>
                        <groupId>cn.hutool</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>hanlp</artifactId>
                        <groupId>com.hankcs</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>foundation-common-utils</artifactId>
                        <groupId>com.jiuji.tc</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>feign-hystrix</artifactId>
                        <groupId>io.github.openfeign</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>feign-httpclient</artifactId>
                        <groupId>io.github.openfeign</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>elasticsearch-rest-high-level-client</artifactId>
                        <groupId>org.elasticsearch.client</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>disruptor</artifactId>
                        <groupId>com.lmax</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-lang</artifactId>
                        <groupId>commons-lang</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-fileupload</artifactId>
                        <groupId>commons-fileupload</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-configuration</artifactId>
                        <groupId>commons-configuration</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-codec</artifactId>
                        <groupId>commons-codec</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>common-utils</artifactId>
                        <groupId>com.jiuji.tc</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>common-message</artifactId>
                        <groupId>com.jiuji.tc</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <artifactId>oa-stock-cloud</artifactId>
                <groupId>com.jiuji.stock</groupId>
                <version>1.2.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.taobao</groupId>
                <artifactId>sdk</artifactId>
                <version>1710301928351-20240807</version>
            </dependency>
            <dependency>
                <groupId>com.esotericsoftware</groupId>
                <artifactId>kryo</artifactId>
                <version>4.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                    <configuration>
                        <attach>true</attach>
                    </configuration>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.flywaydb</groupId>
                    <artifactId>flyway-maven-plugin</artifactId>
                    <version>6.3.2</version>
                    <dependencies>
                        <dependency>
                            <groupId>mysql</groupId>
                            <artifactId>mysql-connector-java</artifactId>
                            <version>8.0.19</version>
                        </dependency>
                    </dependencies>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <repositories>
        <repository>
            <id>jiuji-group</id>
            <url>http://nexus.ch999.cn:8081/nexus/repository/jiuji-group/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>jiuji-releases</id>
            <url>http://nexus.ch999.cn:8081/nexus/repository/jiuji-release/</url>
        </repository>
        <snapshotRepository>
            <id>jiuji-snapshots</id>
            <url>http://nexus.ch999.cn:8081/nexus/repository/jiuji-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
