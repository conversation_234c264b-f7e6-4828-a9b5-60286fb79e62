package com.jiuji.oa.oacore.weborder.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/5/14 11:35
 * @Description
 */
@Data
public class OrderItemVO {
    /**
     * 订单ID
     */
    private String id;

    /**
     * 图片路径
     */
    private String imagePath;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 时间
     */
    private String time;

    /**
     * 价格
     */
    private String price;

    /**
     * 订单类型
     */
    private Integer payType;
    private String payTypeName;

    /**
     * 商品ID
     */
    private String ppriceId;

    private Integer mkcId;
}
