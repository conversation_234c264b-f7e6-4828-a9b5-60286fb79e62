package com.jiuji.oa.oacore.oaorder.res;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 客评相关岗位评分
 * @Author: xiaojianbing
 * @Date  2020/10/28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class EvaluateJobScoreVO implements Serializable {
    private static final long serialVersionUID = 3986302374117996512L;
    private Integer id;
    private Integer evaluateId;
    /**
     * 岗位
     */
    private Integer job;
    private String jobName;
    private Integer userId;
    private String userName;
    private Integer score;
    private Integer score2;
//    private Integer xinshengJob;
    private Integer fen;
    private String inUser;
    private Boolean isXinsheng;
    /**
     * ch999_fen.yanyin
     */
    private String remark;
    private Integer kinds;
    private LocalDateTime fenDate;
    /**
     * 是否无效
     */
    private Boolean invalid;
}
