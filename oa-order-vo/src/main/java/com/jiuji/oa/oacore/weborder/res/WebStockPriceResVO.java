package com.jiuji.oa.oacore.weborder.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商城端员工模式库存查询商品成本
 * <AUTHOR>
 * @date 2024/2/27 16:41
 */
@Data
@EqualsAndHashCode
public class WebStockPriceResVO {
    /**
     * 库存id
     */
    private Integer mkcId;
    /**
     * ppid
     */
    private Integer ppid;
    /**
     * 门店id
     */
    private Integer areaId;
    /**
     * 门店
     */
    private String area;
    /**
     * 成本价
     */
    private BigDecimal inPrice;

    private BigDecimal soPrice;
    /**
     * 入库时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date imeiDate;
}
