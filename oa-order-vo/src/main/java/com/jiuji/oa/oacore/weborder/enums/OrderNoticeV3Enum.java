package com.jiuji.oa.oacore.weborder.enums;

import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * 订单通知v3版本的枚举
 * <AUTHOR>
 * @since 2023/7/11 17:47
 */
@Getter
@AllArgsConstructor
public enum OrderNoticeV3Enum implements CodeMessageEnumInterface {
    /**
     * 订单:
     * @see OrderNoticeService#getOrderNoticeV3(OrderNoticeReq)
     */
    ORDER(1,"新机单","销售","O_{orderId}", OrderNoticeConstant.DEFAULT_NOTICE_FORMAT,"{moaUrl}/order/editorder?SubID={orderId}"),
    LIANGPING_ORDER(2,"良品单","良品","LP_{orderId}", OrderNoticeConstant.DEFAULT_NOTICE_FORMAT,"{moaUrl}/mstockout/editOrder?SubID={orderId}"),
    SHOUHOUYUYUE_ORDER(3,"预约单","维修","YY_{orderId}", OrderNoticeConstant.DEFAULT_NOTICE_FORMAT,"{moaUrl}/new/#/afterService/detail/{orderId}"),
    SHOUHOU_ORDER(4,"售后单","维修","SH_{orderId}", OrderNoticeConstant.DEFAULT_NOTICE_FORMAT,"{moaUrl}/mshouhou/edit/{orderId}"),
    SHOUHOUXIAOJIAN_ORDER(6,"售后小件单","小件","XJ_{orderId}", OrderNoticeConstant.DEFAULT_NOTICE_FORMAT,"{moaUrl}/mSmallpro/add?id={orderId}"),
    HUOSHOU_ORDER(8,"回收订单","回收","HS_{orderId}", OrderNoticeConstant.DEFAULT_NOTICE_FORMAT,"{moaUrl}/new/#/recycle/{orderId}"),
    /**
     * 调拨单
     * @see
     */
    SHOUHOUPEIJIAN_ORDER(5,"售后配件调拨单","旧件","PJDB_{orderId}", "售后单：{orderId} 旧件未调拨，需发往总部",
            "{moaUrl}/mproductKC/kcTransferDetail?sub_id={orderId}"),
    DAJIAN_DIAOBO_ORDER(9,"大件调拨单","大件","DJDB_{orderId}", "{orderMark}机器编号：{orderId}，发往{areaIn}",
            "{moaUrl}/app/native/inventory/bulkyDeliver?type=3&areaIn={areaIn}&areaOut={areaOut}&issend=1&transerType={userId}"),
    XIAOJIAN_DIAOBO_ORDER(10,"小件调拨单","小件","XJDB_{orderId}", "{orderMark}单号：{orderId}，发往{areaIn} {title}",
            "{moaUrl}/app/native/inventory/accessoriesReceivingDetail?orderId={orderId}&keyDiaobo=1"),
    LIANGPING_DIAOBO_ORDER(11,"良品调拨单","良品","LPDB_{orderId}", "{orderMark}机器编号：{orderId}，发往{areaIn}",
            "{moaUrl}/app/native/inventory/bulkyDeliver?type=1&areaIn={areaIn}&areaOut={areaOut}&issend=1&transerType={userId}"),
    YOUPING_DIAOBO_ORDER(12,"优品调拨单","优品","YPDB_{orderId}", "单号：{orderId}，发往{areaIn} {title}",
            "{moaUrl}/app/native/inventory/allocationOfPremiumAccessoriesDetails?orderId={orderId}&printCount=1&printerName=蓝牙"),
    WEIXIU_DIAOBO_ORDER(13,"维修调拨单","维修","WXDB_{orderId}", "{orderMark}单号：{orderId}，发往{areaIn} {title}",
            "{moaUrl}/app/native/inventory/accessoriesReceivingDetail?orderId={orderId}&keyDiaobo=1"),
    HUOSHOU_GOLD(14,"回收黄金订单","回收","HSGOLD_{orderId}",
            "","{moaUrl}/huiShou/recovery-gold/appoint-list/{orderId}?kind=9"),
    HUOSHOU_PENDING_RECOVERTOAREA_NOTIFICATION(15,"回收调拨单","回收","HSTOAREA_{orderId}",
            "目前有[{userId}]个回收商品待发回总部","{moaUrl}/app/native/inventory/allocationGoodProducts?type=1"),
    ;
    /**
     * 状态
     */
    private Integer code;
    /**
     * 名称
     */
    private String message;
    /**
     * 标签
     */
    private String label;
    /**
     * 唯一标识格式化
     */
    private String unKeyFormat;
    /**
     * 名称
     */
    private String titleFormat;
    /**
     * 链接地址
     */
    private String linkFormat;

    public static boolean isDiaoBo(Integer code){
        return Stream.of(SHOUHOUPEIJIAN_ORDER,DAJIAN_DIAOBO_ORDER,XIAOJIAN_DIAOBO_ORDER,LIANGPING_DIAOBO_ORDER,YOUPING_DIAOBO_ORDER,
                WEIXIU_DIAOBO_ORDER,HUOSHOU_PENDING_RECOVERTOAREA_NOTIFICATION).anyMatch(e -> Objects.equals(e.getCode(),code));
    }
}

class OrderNoticeConstant{
    public static String DEFAULT_NOTICE_FORMAT = "单号：{orderId} {area} {userName}";
}
