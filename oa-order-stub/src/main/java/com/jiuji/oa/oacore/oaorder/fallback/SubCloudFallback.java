package com.jiuji.oa.oacore.oaorder.fallback;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jiuji.oa.oacore.oaorder.SubCloud;
import com.jiuji.oa.oacore.oaorder.req.CheckOrderUserReq;
import com.jiuji.oa.oacore.oaorder.req.SubReqVO;
import com.jiuji.oa.oacore.oaorder.req.tax.TaxPiaoVerifyReq;
import com.jiuji.oa.oacore.oaorder.res.CheckOrderUserRes;
import com.jiuji.oa.oacore.oaorder.res.HqLargeScreenOrderCountRes;
import com.jiuji.oa.oacore.oaorder.res.SubIdAndRecoverIdVO;
import com.jiuji.oa.oacore.oaorder.res.tax.TaxPiaoUserVerifyRes;
import com.jiuji.oa.oacore.oaorder.vo.ThirdOrderInfoRes;
import com.jiuji.oa.oacore.promocode.vo.req.ClearPriceCloseServiceReq;
import com.jiuji.oa.oacore.promocode.vo.req.CloseServiceReq;
import com.jiuji.oa.oacore.weborder.req.*;
import com.jiuji.oa.oacore.weborder.res.CreateOaOrderRes;
import com.jiuji.oa.oacore.weborder.res.MyOrderNewRes;
import com.jiuji.oa.oacore.weborder.res.TaxSubInfoRes;
import com.jiuji.oa.oacore.weborder.res.UpdateDouYinMemberRes;
import com.jiuji.oa.oacore.weborder.res.recommendedRes.RecommendedSelectOaRes;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.validation.Valid;
import java.util.List;

/**
 * description: <订单-分布式服务接口-错误返回>
 * translation: <Order-distributed service interface-error return>
 *
 * <AUTHOR>
 * @date 2019/11/27
 * @since 1.0.0
 */
@Component
@Slf4j
public class SubCloudFallback implements SubCloud {
    @Override
    public R<List<ConfigurationInfo>> getActivityConfigurationByChannelIdAndPpId(ActivityConfigurationReq req) {
        log.error("查询活动版本 通过渠道id和ppid,传入参数：{}", JSONUtil.toJsonStr(req));
        return R.error("查询活动版本 通过渠道id和ppid查询失败");
    }

    @Override
    public R<String> clearPriceCloseService(ClearPriceCloseServiceReq closeServiceReq) {
        log.error("订单套餐推荐价格清除关闭服务失败,传入参数：{}", JSONUtil.toJsonStr(closeServiceReq));
        return R.error("订单套餐推荐价格清除关闭服务失败");
    }

    @Override
    public R<UpdateDouYinMemberRes> updateDouYinMember(UpdateDouYinMemberReq req) {
        log.error("抖音会员信息修改,传入参数：{}", JSONUtil.toJsonStr(req));
        return R.error("抖音会员信息修改失败");
    }

    @Override
    public R<RecommendedSelectOaRes> recommendedSelectNoToken(RecommendedSelectAppReq req) {
        log.error("主站套餐查询,传入参数：{}", JSONUtil.toJsonStr(req));
        return R.error("主站套餐查询失败");
    }

    @Override
    public R<String> closeService(CloseServiceReq closeServiceReq) {
        log.error("订单套餐推荐关闭服务失败,传入参数：{}", JSONUtil.toJsonStr(closeServiceReq));
        return R.error("订单套餐推荐关闭服务失败");
    }

    @Override
    public R<CreateOaOrderRes> createOaOrder(CreateOaOrderReq req, String token) {
        log.error("小米生成采购单失败,传入参数：{}", JSONUtil.toJsonStr(req));
        return R.error("小米生成采购单失败");
    }

    @Override
    public R<CreateOaOrderRes> createOaOrder(BigPurchaseOrderReq req, String token) {
        log.error("小米生成大件采购单失败,传入参数：{}", JSONUtil.toJsonStr(req));
        return R.error("小米生成大件采购单失败");
    }

    @Override
    public R<CreateOaOrderRes> createOaOrder(SmallPurchaseOrderReq req, String token) {
        log.error("小米生成小件采购单失败,传入参数：{}", JSONUtil.toJsonStr(req));
        return R.error("小米生成小件采购单失败");
    }

    @Override
    public R<CreateOaOrderRes> createOaOrder(BigReturnOrderReq req, String token) {
        log.error("小米生成大件退货单失败,传入参数：{}", JSONUtil.toJsonStr(req));
        return R.error("小米生成大件退货单失败");
    }

    @Override
    public R<CreateOaOrderRes> createOaOrder(SmallReturnOrderReq req, String token) {
        log.error("小米生成小件退货单失败,传入参数：{}", JSONUtil.toJsonStr(req));
        return R.error("小米生成小件退货单失败");
    }

    @Override
    public R<List<CheckOrderUserRes>> checkOrderUser(CheckOrderUserReq req) {
        return R.error("远程调用/sub/checkOrderUser 失败,参数[model:" + JSONObject.toJSONString(req));
    }

    @Override
    public R<MyOrderNewRes> getMyOrderNew(String userId, int curPage, int rows, int type, boolean recover) {
        return R.error("远程调用/sub/getMyOrderNew 失败");
    }

    /**
     * 获得订单id和良品Id
     *
     * @return SubIdAndRecoverIdRes
     */
    @Override
    public R<SubIdAndRecoverIdVO> getSubIdAndRecoverId(@RequestBody SubReqVO subReq) {
        return R.error("远程调用/sub/getSubIdAndRecoverId 失败,参数[model:" + JSONObject.toJSONString(subReq));
    }

    @Override
    public R<Boolean> existsProcessingOrderInfo(Integer userId) {
        return R.error("远程调用/sub/existsProcessingOrderInfo 失败");
    }


    @Override
    public R<Integer> getAfterSaleQuantity(int subId) {
        return R.error("远程调用/sub/getAfterSaleQuantity 失败 参数：【 subId:"+subId);
    }

    @Override
    public R<TaxSubInfoRes> getTaxSubInfo(Integer subId, Integer type, @RequestHeader("xtenant") long xtenant) {
        return R.error("远程调用/sub/getAfterSaleQuantity 失败 参数：【 subId:"+subId+" , type: " + type);
    }

    @Override
    public R<TaxPiaoUserVerifyRes> listTaxValidBasket(Integer subId, Integer type,long xtenant) {
        return R.error("远程调用/sub/listTaxValidBasket 失败 参数：【 subId:"+subId+" , type: " + type);
    }

    @Override
    public R<Boolean> taxValidAnswer(@Valid TaxPiaoVerifyReq taxPiaoVerifyReq,long xtenant) {
        return R.error("远程调用/sub/taxValidAnswer 失败 参数：【 " + JSON.toJSONString(taxPiaoVerifyReq));
    }

    @Override
    public R<HqLargeScreenOrderCountRes> getHQLargeScreenOrderCount() {
        return R.error("远程调用/sub/getHQLargeScreenOrderCount 失败");
    }

    @Override
    public R<ThirdOrderInfoRes> getThirdOrderInfo(Integer subId, Integer subType, Integer businessType) {
        return R.error("远程调用admin/api/order/getThirdOrderInfo 失败");
    }

    @Override
    public R<Boolean> isNationalSubsidy(Integer subId) {
        return R.error("远程调用api/nationalSubsidyOrder/v1/isNationalSubsidy 参数：失败【 subId:" + subId + "】");
    }
}
