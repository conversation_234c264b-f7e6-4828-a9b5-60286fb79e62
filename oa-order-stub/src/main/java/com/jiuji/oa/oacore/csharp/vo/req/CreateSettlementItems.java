package com.jiuji.oa.oacore.csharp.vo.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreateSettlementItems {
    /**
     * 门店id
     */
    private Integer cashbackAreaId;
    /**
     * 返利金额
     */
    private BigDecimal settlementPrice;
    /**
     * 数量 固定值1
     */
    private Integer count;
    /**
     * 备注
     */
    private String comment;
}
