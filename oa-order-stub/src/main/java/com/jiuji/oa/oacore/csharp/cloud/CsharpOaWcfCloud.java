package com.jiuji.oa.oacore.csharp.cloud;

import cn.hutool.core.lang.Dict;
import com.jiuji.oa.oacore.csharp.cloud.fallback.CsharpOaWcfCloudFallback;
import com.jiuji.oa.oacore.csharp.vo.req.*;
import com.jiuji.oa.oacore.csharp.vo.res.BankPaymentRes;
import com.jiuji.oa.oacore.csharp.vo.res.CheckBeiHuoJiajiFromWuLiuRes;
import com.jiuji.oa.oacore.csharp.vo.res.ModifyBeiHuoJiajiFromWuLiuRes;
import com.jiuji.tc.common.vo.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * C# inwcf服务接口
 * <AUTHOR>
 * @since 2024/3/22 10:12
 */
@FeignClient(name = "csharp-service",url = "${jiuji.sys.moa:}", fallback = CsharpOaWcfCloudFallback.class)
public interface CsharpOaWcfCloud {

    /**
     * 生成采购单
     * @param req
     * @return
     */
    @GetMapping(value = "/platapi_nc/csharp-service/oa/kcApi/GenerateMkcPurcharse", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R<List<Dict>> generateMkcPurcharse(@RequestBody UserReq<GenerateMkcPurcharseReq> req);

    /**
     * 大件出库
     * @param req
     * @return
     */
    @GetMapping(value = "/platapi_nc/csharp-service/oa/kcApi/mkcdhUpV2", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R mkcdhUpV2(@RequestBody OaWcfMkcdhUpV2Req req);


    /**
     * 银行转账查询
     * @param areaId
     * @return
     */
    @GetMapping(value = "/platapi_nc/csharp-service/oa/addorder/BankPayment", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R<BankPaymentRes> bankPayment(@RequestParam("areaid") Integer areaId, @RequestParam("subType") Integer subType);

    /**
     * 备货核对
     * @param req
     * @return
     */
    @PostMapping(value = "/platapi_nc/csharp-service/oa/StockingCheck/StockingCheckSaveByDetail", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R stockingCheckSaveByDetail(@RequestBody List<StockingCheckDetailReq> req);

    /**
     * 检查并获取最近有货跑腿调货门店
     * @param wuliuId
     * @return
     */
    @GetMapping(value = "/platapi_nc/csharp-service/oa/commonApi/CheckBeiHuoJiajiFromWuLiu", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R<CheckBeiHuoJiajiFromWuLiuRes> checkBeiHuoJiajiFromWuLiu(@RequestParam("wuliuId") Integer wuliuId);

    /**
     * 匹配跑腿调拨门店并生成物流单
     * @param wuliuId
     * @return
     */
    @GetMapping(value = "/platapi_nc/csharp-service/oa/commonApi/ModifyBeiHuoJiajiFromWuLiu", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R<ModifyBeiHuoJiajiFromWuLiuRes> modifyBeiHuoJiajiFromWuLiu(@RequestParam("wuliuId") Integer wuliuId);

    /**
     * 生成结算单
     * @param req
     * @return
     */
    @PostMapping(value = "/platapi_nc/csharp-service/ajaxApi/batchCreateSettlement", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R<String> batchCreateSettlement(@RequestBody BatchCreateSettlementReq req);
}
