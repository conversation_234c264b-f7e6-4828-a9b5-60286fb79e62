package com.jiuji.oa.oacore.yearcardtransfer.cloud;

import com.jiuji.oa.oacore.yearcardtransfer.cloud.fallback.YearPackageTransferCloudFallbackFactory;
import com.jiuji.oa.oacore.yearcardtransfer.dto.YearPackageTransferClaimDto;
import com.jiuji.oa.oacore.yearcardtransfer.dto.YearPackageTransferDetailDto;
import com.jiuji.oa.oacore.yearcardtransfer.dto.YearPackageTransferCancelDto;
import com.jiuji.tc.common.vo.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

@FeignClient(name = "OREDERSERVICE",url = "${orderservice_yearpackagetransfercloud_url:}",
        path = "/orderservice/year-package-transfer", fallbackFactory = YearPackageTransferCloudFallbackFactory.class)
public interface YearPackageTransferCloud {

    /**
     * 获取转赠明细信息
     */
    @GetMapping("/transfer-detail")
    R<YearPackageTransferDetailDto> getTransferDetail(
            @RequestParam("transferCode") String transferCode,
            @RequestParam("userId") Integer userId);

    /**
     * 获取转赠明细信息
     */
    @GetMapping("/list-transfer-detail")
    R<List<YearPackageTransferDetailDto>> listTransferDetail(
            @RequestParam("bindBasketId") Collection<Integer> bindBasketIds,
            @RequestParam("userId") Integer userId);

    /**
     * 领取转赠
     */
    @PostMapping("/claim")
    R<YearPackageTransferDetailDto> claimTransfer(
            @RequestBody YearPackageTransferClaimDto dto);

    /**
     * 撤销赠送
     */
    @PostMapping("/revoke")
    R<Boolean> revokeTransfer(
            @RequestBody YearPackageTransferCancelDto dto);
}
