package com.jiuji.oa.oacore.yearcardtransfer.cloud.fallback;

import com.jiuji.oa.oacore.yearcardtransfer.cloud.YearPackageTransferCloud;
import com.jiuji.oa.oacore.yearcardtransfer.dto.YearPackageTransferClaimDto;
import com.jiuji.oa.oacore.yearcardtransfer.dto.YearPackageTransferDetailDto;
import com.jiuji.oa.oacore.yearcardtransfer.dto.YearPackageTransferCancelDto;
import com.jiuji.tc.common.vo.R;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

@Component
@Slf4j
public class YearPackageTransferCloudFallbackFactory implements FallbackFactory<YearPackageTransferCloud> {

    @Override
    public YearPackageTransferCloud create(Throwable cause) {
        return new YearPackageTransferCloud() {
            @Override
            public R<YearPackageTransferDetailDto> getTransferDetail(String transferCode, Integer userId) {
                logError("getTransferDetail", transferCode+"_"+ userId, cause);
                return R.error("获取转赠详情服务暂时不可用，请稍后重试");
            }

            @Override
            public R<List<YearPackageTransferDetailDto>> listTransferDetail(Collection<Integer> bindBasketIds, Integer userId) {
                logError("listTransferDetail", bindBasketIds +"_"+ userId, cause);
                return R.error("获取转赠详情列表服务暂时不可用，请稍后重试");
            }

            @Override
            public R<YearPackageTransferDetailDto> claimTransfer(YearPackageTransferClaimDto dto) {
                logError("claimTransfer", dto, cause);
                return R.error("转赠领取服务暂时不可用，请稍后重试");
            }

            @Override
            public R<Boolean> revokeTransfer(YearPackageTransferCancelDto dto) {
                logError("revokeTransfer", dto, cause);
                return R.error("撤销转赠服务暂时不可用，请稍后重试");
            }

            private void logError(String methodName, Object params, Throwable cause) {
                log.error("调用方法 [{}] 失败，参数: {}, 异常信息: {}", methodName, params, cause.getMessage());
            }
        };
    }
}
