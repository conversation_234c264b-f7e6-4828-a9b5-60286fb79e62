package com.jiuji.oa.oacore.weborder.fallback;

import com.alibaba.fastjson.JSON;
import com.jiuji.oa.oacore.common.res.PageRes;
import com.jiuji.oa.oacore.web.vo.ElectronicTicketShow;
import com.jiuji.oa.oacore.web.vo.SelectElectronicTicketVO;
import com.jiuji.oa.oacore.web.vo.UpdateSubCommentVO;
import com.jiuji.oa.oacore.weborder.WebOrderCloud;
import com.jiuji.oa.oacore.weborder.req.*;
import com.jiuji.oa.oacore.weborder.res.BargainVO;
import com.jiuji.oa.oacore.weborder.res.GiftStockResVO;
import com.jiuji.oa.oacore.weborder.res.ImOrderObjVo;
import com.jiuji.oa.oacore.weborder.res.OrderInfoRes;
import com.jiuji.oa.oacore.weborder.vo.*;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @since 2020/2/28
 */
@Component
@Slf4j
public class WebOrderCloudFallback implements WebOrderCloud {

    @Override
    public R<Integer> retreatCount(Integer userId) {
        return R.error("Cloud 调用失败" +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/retreatCount/v1\"，参数:" +userId);
    }

    @Override
    public R<List<OrderAssistantMenuEnumVO>> getAssistantMenuEnum() {
        return R.error("Cloud 调用失败" +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/getAssistantMenuEnum/v1\"" );
    }

    @Override
    public R<BargainVO> queryImeiForBargain(QueryBO queryBO) {
        return R.error("Cloud 调用失败" +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/queryImeiForBargain/v1\"，参数:" +queryBO);
    }

    @Override
    public R<String> updateSubComment(UpdateSubCommentVO updateSubCommentVO) {
        return R.error("Cloud 调用，修改订单备注," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/updateSubComment/v1\"，参数:" + JSON.toJSONString(updateSubCommentVO));
    }

    @Override
    public R<Set<Integer>> purchaseCareAndIphone(Integer userId) {
        return R.error("Cloud 调用失败" +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/purchaseCareAndIphone/v1\"，参数:" +userId);
    }

    @Override
    public R<Set<Integer>> purchaseCareAndIphoneV2(Integer userId) {
        return R.error("Cloud 调用失败" +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/purchaseCareAndIphone/v2\"，参数:" +userId);
    }

    @Override
    public R<PageRes<XinjiSubVO>> getOrderList(WebOrderQueryReq req) {
        return R.error("Cloud 调用，获取 新机订单列表失败," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/list\"，参数:" + JSON.toJSONString(req));
    }

    @Override
    public R<PendingPaymentVO> getOrderPendingPayment(PendingPaymentReq req) {
        return R.error("Cloud 调用，获取 新机订单列表失败," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/getOrderPendingPayment/v1\"，参数:" + JSON.toJSONString(req));
    }


    @Override
    public R<List<PendingPaymentVO>> getOrderPendingPaymentV2(PendingPaymentReq req) {
        return R.error("Cloud 调用，获取 新机订单列表失败," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/getOrderPendingPayment/v2\"，参数:" + JSON.toJSONString(req));
    }


    @Override
    public R<PageRes<LiangpinSubVO>> getLiangpinList(WebOrderQueryReq req) {
        return R.error("Cloud 调用，获取 良品订单列表失败," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/listLiangpin\"，userId:" + req.getUserId() + ";tag:" + req.getTagType());
    }

    @Override
    public R<PageRes<RecoverSubVO>> getRecoverList(WebOrderQueryReq req) {
        return R.error("Cloud 调用，获取 回收订单列表失败," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/listRecover\"，userId:" + req.getUserId() + ";tag:" + req.getTagType());
    }

    @Override
    public R<PageRes<AfterServiceSubVO>> getAfterServiceRepairList(WebOrderQueryReq req) {
        return R.error("Cloud 调用，获取 售后订单列表失败," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/listAfterServiceRepair\"，userId:" + req.getUserId() + ";tag:" + req.getTagType());
    }

    @Override
    public R<PageRes<AfterServiceSubVO>> getAfterServiceRepairList(WebAfterQueryReq req) {
        return R.error("Cloud 调用/orderservice/InApi/webOrder/listAfterServiceRepair，获取 售后订单列表失败,userId:" + req.getUserId() + ";tag:" + req.getTagType());
    }

    @Override
    public R<PageRes<AfterServiceSubVO>> listAfterAndSmallServiceRepair(WebAfterQueryReq req) {
        return R.error("Cloud 调用/orderservice/InApi/webOrder/listAfterAndSmallServiceRepair，获取 售后订单列表失败,userId:" + req.getUserId() + ";tag:" + req.getTagType());
    }

    @Override
    public R<PageRes<ReservationSubVO>> getReservationList(WebOrderQueryReq req) {
        return R.error("Cloud 调用，获取 售后预约订单列表失败," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/listReservation\"，userId:" + req.getUserId() + ";tag:" + req.getTagType());
    }

    @Override
    public R<OrderCountVO> getOrderCount(WebOrderCountReq req) {
        return R.error("Cloud 调用，获取 订单总数," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/count\"，userId:" + req.getUserId() + ";tag:" + req.getQueryTag());
    }

    @Override
    public R<List<OrderStatusCheckVO>> checkNormal(WebOrderCheckReq req) {
        return R.error("Cloud 调用，获取 订单状态," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/check\"");
    }

    @Override
    public R<List<Ch999UserServiceVO>> listExclusiveCustomerServiceByUserId(Integer userId, Integer count) {
        return R.error("Cloud 调用，获取 用户专属客服," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/listExclusiveCustomerServiceByUserId\"，userId:" + userId + ";count:" + count);
    }

    @Override
    public R<List<Ch999UserServiceVO>> listExclusiveCustomerService(Integer userId, Integer count, Integer xtenant) {
        return R.error("Cloud 调用，获取 用户专属客服," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/listExclusiveCustomerService\"，userId:" + userId + ";count:" + count + ";xtenant:" + xtenant);
    }

    @Override
    public R<Integer> countNotEvaluatedOrder(Integer userId) {
        return R.error("Cloud 调用，获取 未评价订单数量," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/countNotEvaluatedOrder\"，userId:" + userId);
    }

    @Override
    public R<List<SaleOrderVO>> getSaleOrders(List<Integer> productIds, Double pay) {
        return R.error("Cloud 调用，获取 p30订金预定订单," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/getSaleOrders\"，productIds:" + StringUtils.join(productIds, ",") + ";pay:" + pay);
    }

    @Override
    public R<List<SaleOrderVO>> getSaleOrders(List<Integer> productIds, Double pay, String subDate) {
        return R.error("Cloud 调用，获取 p30订金预定订单," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/getSaleOrders/v2\"，productIds:" + StringUtils.join(productIds, ",") + ";pay:" + pay +";subDate:" + subDate);
    }

    @Override
    public R<List<SaleOrderKcGroupInfoVO>> getSaleOrderKcGroupInfo(WebOrderSaleGroupReq req) {
        return R.error("Cloud 调用，获取新机库存预设分组信息," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/getSaleOrderKcGroupInfo\"， basketIds:" + StringUtils.join(req.getBasketIds(), ",") + ";level:" + req.getLevel() + ";wxBind:" + req.getWxBind());
    }

    @Override
    public R<InventoryPresetVO> getInventoryPreset(Integer basketId) {
        return R.error("Cloud 调用，获取 库存预设," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/getInventoryPreset\"，basketId:" + basketId);
    }

    @Override
    public R<String> changeSubPpriceid(Integer userId, Integer basketId, Integer ppriceid, BigDecimal price) {
        return R.error("Cloud 调用，更换ppriceid," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/changeSubPpriceid\"，userId:" + userId + ";basketId:" + basketId + ";ppriceid:" + ppriceid + ";price:" + price);
    }

    @Override
    public R<List<WhetherToBuyReq.Res>> getWhetherToBuy(@RequestBody @Validated WhetherToBuyReq whetherToBuyReq) {
        return R.error("Cloud 调用，当前用户是否存在购买记录," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/getWhetherToBuy\"， xTenant:" + StringUtils.join(whetherToBuyReq.getXTenant(), ",") + ";userIds:" + whetherToBuyReq.getUserIds().toString());
    }

    @Override
    public R<List<Integer>> getMyClientNotComplete(MyClientReq myClientReq) {
        return R.error("Cloud 调用，筛选未产生过【已完成】状态的客户," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/getMyClientNotComplete\"， startTime:" + StringUtils.join(myClientReq.getBeginTime(), ";"));

    }

    @Override
    public R<List<Integer>> getMyClientPurchaseComplete(MyClientReq myClientReq) {
        return R.error("Cloud 调用，筛选有购买完成的客户," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/getMyClientPurchaseComplete\"， startTime:" + StringUtils.join(myClientReq.getBeginTime(), ";"));

    }

    @Override
    public R<List<Integer>> getMyClientIsComplete(MyClientReq myClientReq) {
        return R.error("Cloud 调用，筛选近X天【已完成】状态的客户," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/getMyClientIsComplete\"， startTime:" + StringUtils.join(myClientReq.getBeginTime(), ";"));

    }

    @Override
    public R<List<Integer>> getMyClientIsCompleteByCigarette(MyClientReq myClientReq) {
        return R.error("Cloud 调用，筛选【已完成】状态的订单中包含电子烟商品," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/getMyClientIsCompleteByCigarette\"， startTime:" + StringUtils.join(myClientReq.getBeginTime(), ";"));

    }

    @Override
    public R getOrderNotice(Integer userId, Integer xtenant,Integer size,Integer current){
        return R.error("Cloud 调用，获取待办事项（老路由）," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/api/notice/getOrderNotice\"， userId:" + StringUtils.join(userId, ";"));

    }

    @Override
    public R getOrderNoticeV2(Integer userId, Integer xtenant,Integer size,Integer current){
        return R.error("Cloud 调用，获取待办事项（新路由）," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/api/notice/getOrderNoticeV2\"， userId:" + StringUtils.join(userId, ";"));

    }

    @Override
    public R getOrderNoticeV3(Integer userId,Integer areaId, String orderTypes, Integer xtenant, Integer size, Integer current) {
        return R.error("Cloud 调用，获取待办事项（新路由）,/orderservice/api/notice/getOrderNoticeV3\"， 参数:" + StringUtils.join(userId,orderTypes,xtenant, ";"));
    }

    @Override
    public R<List<GoodProductStockVO>> getGoodProductStockByMkcList(List<Integer> mkcIdList) {
        return R.error("Cloud 调用，获取待办事项（新路由）," +
                "value = \"OREDERSERVICE\", path = \"/orderservice/InApi/webOrder/getGoodProductStockByMkcList\"， mkcIdList:" + StringUtils.join(mkcIdList, ";"));
    }

    @Override
    public R<Boolean> judgeGoodAccessories(JudgeGoodAccessoriesBO judgeGoodAccessoriesBO) {
        log.error("Cloud 调用判断是否为良品订单出错,传入参数："+ judgeGoodAccessoriesBO);
        return R.success(Boolean.FALSE);
    }


    @Override
    public R<ElectronicTicketShow> getElectronicTicket(@RequestBody SelectElectronicTicketVO selectElectronicTicketVO, @RequestHeader(required = false,name = "xtenant") Integer xtenant) {
        return R.error("Cloud 调用，电子小票查询失败," + "value = \"WEB\", path = web/electronicTicket/getElectronicTicket/v1");
    }

    @Override
    public R<Integer> getPointsBySubId(Integer subType, Integer subId) {
        return R.success(NumberConstant.ZERO);
    }

    @Override
    public R<String> updateTicket(SelectElectronicTicketVO selectElectronicTicketVO) {
        return R.error("Cloud 调用，电子小票修改失败,value = \"order\", path = /orderservice/InApi/webOrder/updateTicket/v1");
    }

    @Override
    public R<List<OrderDynamicsVO>> orderDynamicsV1(Integer orderId, Integer orderType) {
        return R.error("Cloud 调用，获取订单动态,value = \"order\", path = /orderservice/InApi/webOrder/subDynamics/v1");
    }

    @Override
    public R<Integer> getSubIdByBasketId(@RequestParam("basketId") Integer basketId) {
        return R.error("Cloud 调用，获取订单动态,value = \"order\", path = /orderservice/InApi/webOrder/getSubIdByBasketId/v1");
    }

    /**
     * 通过Type获取不同类别的订单信息
     *
     * @param subAppReqs
     * @return
     */
    @Override
    public R<ImOrderObjVo> getOrderByTypeAndUserIdV2(List<SubAppReq> subAppReqs) {
        return R.error("Cloud 调用，通过Type获取不同类别的订单信息,value = \"order\", path = /orderservice/InApi/webOrder/getOrderByTypeAndUserIdV2/v1");
    }

    /**
     * 查询门店赠品库存信息
     *
     * @param req
     * @return
     */
    @Override
    public R<List<GiftStockResVO>> getGiftStockByAreaIds(GiftStockReqVO req) {
        return R.error("Cloud 调用，查询赠品库存信息,value = \"order\", path = /orderservice/InApi/webOrder/getGiftStockByAreaIds/v1");
    }

    @Override
    public R<OrderInfoRes> getOrderByWxNo(String outTradeNo) {
        return R.error("Cloud 调用，支付单号获取订单信息,value = \"order\", path = /orderservice/InApi/webOrder/getOrderIdListForWx/v1");
    }

}
