package com.jiuji.oa.oacore.weborder;

import com.jiuji.oa.oacore.common.res.PageRes;
import com.jiuji.oa.oacore.web.vo.ElectronicTicketShow;
import com.jiuji.oa.oacore.web.vo.SelectElectronicTicketVO;
import com.jiuji.oa.oacore.web.vo.UpdateSubCommentVO;
import com.jiuji.oa.oacore.weborder.fallback.WebOrderCloudFallbackFactory;
import com.jiuji.oa.oacore.weborder.req.*;
import com.jiuji.oa.oacore.weborder.res.BargainVO;
import com.jiuji.oa.oacore.weborder.res.GiftStockResVO;
import com.jiuji.oa.oacore.weborder.res.ImOrderObjVo;
import com.jiuji.oa.oacore.weborder.res.OrderInfoRes;
import com.jiuji.oa.oacore.weborder.vo.*;
import com.jiuji.tc.common.vo.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 * @since 2020/2/28
 */
@FeignClient(value = "OREDERSERVICE", qualifier = "WebOrderCloud",
        path = "/orderservice/InApi/webOrder",url = "${orderservice_url:}",
        fallbackFactory = WebOrderCloudFallbackFactory.class)
public interface WebOrderCloud {


    /**
     * 获取良品退还次数
     * @param userId
     * @return
     */
    @GetMapping("/retreatCount/v1")
    R<Integer> retreatCount(@RequestParam("userId") Integer userId);


    /**
     * 获取枚举
     * @return
     */
    @GetMapping("getAssistantMenuEnum/v1")
    R<List<OrderAssistantMenuEnumVO>> getAssistantMenuEnum();

    /**
     * 大小件优品上架逻辑验证
     * @param queryBO
     * @return
     */
    @PostMapping("/queryImeiForBargain/v1")
    R<BargainVO> queryImeiForBargain (@RequestBody QueryBO queryBO );

    /**
     * 为订单添加备注
     * @param updateSubCommentVO
     * @return
     */
    @PostMapping("/updateSubComment/v1")
    R<String> updateSubComment(@RequestBody UpdateSubCommentVO updateSubCommentVO);

    @GetMapping("/purchaseCareAndIphone/v1")
    R<Set<Integer>> purchaseCareAndIphone(@RequestParam("userId") Integer userId);

    @GetMapping("/purchaseCareAndIphone/v2")
    R<Set<Integer>> purchaseCareAndIphoneV2(@RequestParam("userId") Integer userId);

    @PostMapping("list")
    R<PageRes<XinjiSubVO>> getOrderList(@RequestBody WebOrderQueryReq req);

    /**
     * 获取待支付订单信息
     * @param req
     * @return
     */
    @PostMapping("/getOrderPendingPayment/v1")
    R<PendingPaymentVO> getOrderPendingPayment(@RequestBody PendingPaymentReq req);

    /**
     * 获取待支付订单信息
     * @param req
     * @return
     */
    @PostMapping("/getOrderPendingPayment/v2")
    R<List<PendingPaymentVO>> getOrderPendingPaymentV2(@RequestBody PendingPaymentReq req);

    @PostMapping("listLiangpin")
    R<PageRes<LiangpinSubVO>> getLiangpinList(@RequestBody WebOrderQueryReq req);

    @PostMapping("listRecover")
    R<PageRes<RecoverSubVO>> getRecoverList(@RequestBody WebOrderQueryReq req);

    /**
     * @see com.jiuji.oa.oacore.weborder.WebOrderCloud#getAfterServiceRepairList(com.jiuji.oa.oacore.weborder.req.WebAfterQueryReq)
     * @param req
     * @return
     */
    @PostMapping("listAfterServiceRepair")
    @Deprecated
    R<PageRes<AfterServiceSubVO>> getAfterServiceRepairList(@RequestBody WebOrderQueryReq req);

    /**
     * 新接口
     * @param req
     * @return
     */
    @PostMapping("listAfterServiceRepair")
    R<PageRes<AfterServiceSubVO>> getAfterServiceRepairList(@RequestBody WebAfterQueryReq req);

    /**
     * 新接口
     * @param req
     * @return
     */
    @PostMapping("listAfterAndSmallServiceRepair")
    R<PageRes<AfterServiceSubVO>> listAfterAndSmallServiceRepair(@RequestBody WebAfterQueryReq req);

    @PostMapping("listReservation")
    R<PageRes<ReservationSubVO>> getReservationList(@RequestBody WebOrderQueryReq req);

    @PostMapping("count")
    R<OrderCountVO> getOrderCount(@RequestBody WebOrderCountReq req);

    @PostMapping("check")
    R<List<OrderStatusCheckVO>> checkNormal(@RequestBody WebOrderCheckReq req);

    @GetMapping("/listExclusiveCustomerServiceByUserId")
    R<List<Ch999UserServiceVO>> listExclusiveCustomerServiceByUserId(@RequestParam("userId") Integer userId,
                                                                     @RequestParam(value = "count", defaultValue = "4") Integer count);

    @GetMapping("/listExclusiveCustomerService")
    R<List<Ch999UserServiceVO>> listExclusiveCustomerService(@RequestParam("userId") Integer userId,
                                                             @RequestParam(value = "count", defaultValue = "4") Integer count,
                                                             @RequestParam(value = "xtenant", defaultValue = "0") Integer xtenant);

    @GetMapping("/countNotEvaluatedOrder")
    R<Integer> countNotEvaluatedOrder(@RequestParam("userId") Integer userId);

    /**
     * 查询p30订金预定订单
     */
    @GetMapping("/getSaleOrders")
    R<List<SaleOrderVO>> getSaleOrders(@RequestParam("productIds") List<Integer> productIds,
                                       @RequestParam(value = "pay", defaultValue = "0") Double pay);

    /**
     * 查询p30订金预定订单
     */
    @GetMapping("/getSaleOrders/v2")
    R<List<SaleOrderVO>> getSaleOrders(@RequestParam(value = "productIds", required = false) List<Integer> productIds,
                                       @RequestParam(value = "pay", defaultValue = "0", required = false) Double pay,
                                       @RequestParam(value = "subDate", required = false) String subDate);

    /**
     * 获取新机库存预设分组信息
     */
    @PostMapping("/getSaleOrderKcGroupInfo")
    R<List<SaleOrderKcGroupInfoVO>> getSaleOrderKcGroupInfo(@RequestBody WebOrderSaleGroupReq req);

    /**
     * 根据订单明细id获取库存预设
     *
     * @param basketId 订单明细id
     * @return 库存预设
     */
    @GetMapping("/getInventoryPreset")
    R<InventoryPresetVO> getInventoryPreset(@RequestParam("basketId") Integer basketId);

    /**
     * 更换ppriceid
     * C# oa999Services\lib\oaApi.changeSubPpriceid
     *
     * @param userId   客户编号
     * @param basketId 订单明细id
     * @param ppriceid ppriceid
     * @param price    卖价
     */
    @GetMapping("/changeSubPpriceid")
    R<String> changeSubPpriceid(@RequestParam("userId") Integer userId,
                                       @RequestParam("basketId") Integer basketId,
                                       @RequestParam("ppriceid") Integer ppriceid,
                                       @RequestParam("price") BigDecimal price);



    /**
     * 根据传入用户id和租户编号，品牌id，时间段，查询当前用户是否存在购买记录
     * @param whetherToBuyReq 用户id和租户编号，品牌id，时间段
     * @return 当前用户是否存在购买记录
     */
    @PostMapping("/getWhetherToBuy")
    R<List<WhetherToBuyReq.Res>> getWhetherToBuy(@RequestBody @Validated WhetherToBuyReq whetherToBuyReq);


    /**
     *  筛选近X天未产生过【已完成】状态的订单（30天、90天、半年、1年、2年）
     * @param myClientReq myClientReq
     * @return 客户编号
     */
    @PostMapping("/getMyClientNotComplete")
    R<List<Integer>> getMyClientNotComplete(@RequestBody @Validated MyClientReq myClientReq);

    /**
     *  筛选商品ID 47113，有购买完成的客户（近一年有购买完成该商品的客户）
     * @param myClientReq myClientReq
     * @return 客户编号
     */
    @PostMapping("/getMyClientPurchaseComplete")
    R<List<Integer>> getMyClientPurchaseComplete(@RequestBody @Validated MyClientReq myClientReq);

    /**
     *  筛选近X天【已完成】状态的订单
     * @param myClientReq myClientReq
     * @return 客户编号
     */
    @PostMapping("/getMyClientIsComplete")
    R<List<Integer>> getMyClientIsComplete(@RequestBody @Validated MyClientReq myClientReq);


    /**
     *  筛选近X天【已完成】状态的订单中包含电子烟商品
     * @param myClientReq myClientReq
     * @return 客户编号
     */
    @PostMapping("/getMyClientIsCompleteByCigarette")
    R<List<Integer>> getMyClientIsCompleteByCigarette(@RequestBody @Validated MyClientReq myClientReq);

    /**
     * 待办事项接口 获取老路由
     * @param userId userId
     * @param xtenant xtenant
     * @param size size
     * @param current current
     * @return
     */
    @GetMapping("/getOrderNotice")
    R getOrderNotice(@RequestParam("userId") Integer userId,
                                           @RequestParam("xtenant") Integer xtenant,
                                           @RequestParam(value = "size",required = false) Integer size,
                                           @RequestParam(value = "current",required = false) Integer current);

    /**
     * 待办事项接口 获取新路由
     * @param userId userId
     * @param xtenant xtenant
     * @param size size
     * @param current current
     * @return
     */
    @GetMapping("/getOrderNotice/V2")
    R getOrderNoticeV2(@RequestParam("userId") Integer userId,
                                           @RequestParam("xtenant") Integer xtenant,
                                           @RequestParam(value = "size",required = false) Integer size,
                                           @RequestParam(value = "current",required = false) Integer current);


    /**
     * 待办事项接口
     * @param userId
     * @see com.jiuji.oa.oacore.weborder.enums.OrderNoticeV3Enum
     * @param orderTypes
     * @param xtenant
     * @param size
     * @param current
     * @return
     */
    @GetMapping("/getOrderNotice/V3")
    R getOrderNoticeV3(@RequestParam(value = "userId",required = false) Integer userId,
                       @RequestParam(value = "areaId",required = false) Integer areaId,
                       @RequestParam(value = "orderTypes",required = false,defaultValue = "1,2,3,4,5,6,8") String orderTypes,
                       @RequestParam("xtenant") Integer xtenant,
                       @RequestParam(value = "size",required = false,defaultValue = "10") Integer size,
                       @RequestParam(value = "current",required = false, defaultValue = "1") Integer current);

    /**
     * 根据mkcIdList查询库存位置
     * @param mkcIdList mkcIdList
     * @return
     */
    @PostMapping("/getGoodProductStockByMkcList")
    R<List<GoodProductStockVO>> getGoodProductStockByMkcList(@RequestBody List<Integer> mkcIdList);


    /**
     * 判断是否为良品订单
     * @param judgeGoodAccessoriesBO
     * @return
     */
    @PostMapping("/judgeGoodAccessories")
    R<Boolean> judgeGoodAccessories(@RequestBody JudgeGoodAccessoriesBO judgeGoodAccessoriesBO);



    /**
     * 电子小票详情查询
     * @param selectElectronicTicketVO
     * @param xtenant
     * @return
     */
    @PostMapping("/getElectronicTicket/v1")
    R<ElectronicTicketShow> getElectronicTicket(@RequestBody SelectElectronicTicketVO selectElectronicTicketVO,@RequestHeader(required = false) Integer xtenant);


    /**
     * 订单积分查询
     * @param selectElectronicTicketVO
     * @param xtenant
     * @return
     */
    @GetMapping("/getPointsBySubId/v1")
    R<Integer> getPointsBySubId(@RequestParam("subType") Integer subType,@RequestParam("subId")Integer subId);


    /**
     * 电子小票确认
     * @param selectElectronicTicketVO
     * @return
     */
    @PostMapping("/updateTicket/v1")
    R<String> updateTicket(@RequestBody SelectElectronicTicketVO selectElectronicTicketVO);

    /**
     * 获取订单动态
     *
     * @param orderId 订单号
     * @param orderType 订单类型
     * @return
     */
    @GetMapping("/orderDynamics/v1")
    R<List<OrderDynamicsVO>> orderDynamicsV1(@RequestParam("orderId") Integer orderId,
                                             @RequestParam("orderType") Integer orderType);

    /**
     * 根据basketId查询subId
     *
     * @param basketId 订单商品明细id
     * @return subId，若未查询到订单，返回0
     */
    @GetMapping("/getSubIdByBasketId/v1")
    R<Integer> getSubIdByBasketId(@RequestParam("basketId") Integer basketId);

    /**
     * 通过Type获取不同类别的订单信息
     * @param subAppReqs
     * @return
     */
    @PostMapping(value = "/getOrderByTypeAndUserId/v2")
    R<ImOrderObjVo> getOrderByTypeAndUserIdV2(@RequestBody @Validated List<SubAppReq> subAppReqs);

    /**
     * 查询门店赠品库存信息
     *
     * @return
     */
    @PostMapping("/getGiftStockByAreaIds/v1")
    R<List<GiftStockResVO>> getGiftStockByAreaIds(@RequestBody GiftStockReqVO req);


    @GetMapping("/getOrderIdListForWx/v1")
    R<OrderInfoRes> getOrderByWxNo(@RequestParam("outTradeNo") String outTradeNo);
}
