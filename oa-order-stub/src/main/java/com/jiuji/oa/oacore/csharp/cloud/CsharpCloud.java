package com.jiuji.oa.oacore.csharp.cloud;

import com.jiuji.oa.oacore.csharp.cloud.fallback.CsharpCloudFallback;
import com.jiuji.oa.oacore.csharp.vo.req.*;
import com.jiuji.oa.oacore.csharp.vo.res.MemberShipCodeModelRes;
import com.jiuji.oa.oacore.csharp.vo.res.ProductMarksTreeRes;
import com.jiuji.oa.oacore.weborder.req.SmallPurchaseCheckOrderReq;
import com.jiuji.oa.oacore.weborder.req.SmallPurchaseOrderReq;
import com.jiuji.oa.oacore.weborder.req.SmallPurchaseSendOrderReq;
import com.jiuji.tc.common.vo.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * csharp服务调用封装
 * <AUTHOR>
 * @since 2023/11/27 13:31
 */
@FeignClient(name = "csharp-service",url = "${jiuji.sys.moa:}", fallback = CsharpCloudFallback.class)
public interface CsharpCloud {
    /**
     * 添加商品
     * @param addBasketForm
     * @return
     */
    @PostMapping(value = "/platapi_nc/csharp-service/oa/addOrder/addNewProducts", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R<Integer> addOrderNewProducts(AddBasketForm addBasketForm);

    /**
     * 删除商品
     * @param basketId
     * @return
     */
    @GetMapping(value = "/platapi_nc/csharp-service/oa/addOrder/delOrder")
    R delOrder(@RequestParam("basket_id") Integer basketId);

    /**
     * 更新订单金额
     * @param subId
     * @return
     */
    @GetMapping(value = "/platapi_nc/csharp-service/oa/addOrder/orderPrice")
    R orderPrice(@RequestParam("subId") Integer subId);

    /**
     * 良品更新订单金额
     * @param subId
     * @return
     */
    @GetMapping(value = "/platapi_nc/csharp-service/oa/StockOut/subCheckOp", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R stockOutSubCheckOp(@RequestParam("sub_id") Integer subId, @RequestParam("sub_check") Integer subCheck);


    /**
     * 大件批量入库
     * @param mkcInKuCunReq
     * @return
     */
    @GetMapping(value = "/platapi_nc/csharp-service/oa/ProductMkc/kucun_set_select", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R productMkcInKuCun(@RequestBody MkcInKuCunReq mkcInKuCunReq);

    /**
     * 大件入库
     * @param req
     * @return
     */
    @GetMapping(value = "/platapi_nc/csharp-service/oa/ProductMkc/mkcdhUpV2", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R mkcdhUpV2(@RequestBody MkcdhUpV2Req req);

    /**
     * 大小件退货单审核
     * @param suId
     * @return
     */
    @GetMapping(value = "/platapi_nc/csharp-service/oa/back/sub_check", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R backSubCheck(@RequestParam("sub_id") Integer suId);


    /**
     * 小件采购单生成
     * @param req
     * @return
     */
    @PostMapping(value = "/platapi_nc/csharp-service/oa/mproductKC/saveCaigouSub", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R<Integer> saveCaigouSub(@RequestBody SmallPurchaseOrderReq req);

    /**
     * 小件采购单审核
     * @param req
     * @return
     */
    @PostMapping(value = "/platapi_nc/csharp-service/oa/mproductKC/checkCaigouSub", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R checkCaigouSub(@RequestBody SmallPurchaseCheckOrderReq req);

    /**
     * 小件采购单发货
     * @param req
     * @return
     */
    @PostMapping(value = "/platapi_nc/csharp-service/oa/mproductKC/sendCaigouSub", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R sendCaigouSub(@RequestBody SmallPurchaseSendOrderReq req);

    @GetMapping(value = "/platapi_nc/csharp-service/ajaxapi/BaoZunAutoCompleteSub", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R baoZunAutoCompleteSub(@RequestParam(value = "transactionNumber", required = false) String transactionNumber);



    /**
     * 小件备货
     * @param req
     * @return
     */
    @PostMapping(value = "/platapi_nc/csharp-service/oa/productKC/peijianbeihuoAdd", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R peiJianBeiHuoAdd(@RequestBody PeiJianBeiHuoAddReq req);

    /**
     * 备货锁定
     * @param smallproBillId
     * @return
     */
    @GetMapping(value = "/platapi_nc/csharp-service/oa/commonApi/SmallProBeihuo", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R smallProBeihuo(@RequestParam(value = "smallproBillId") Integer smallproBillId);

    /**
     * 小件库存自动备货接口，同城调拨、自动提交未备货
     * @param req
     * @return
     */
    @GetMapping(value = "/platapi_nc/csharp-service/oa/commonApi/SmallProBeihuoAuto", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R smallProBeihuoAuto(@RequestBody SmallProBeihuoAutoReq req);

    /**
     * 备货锁定取消
     */
    @GetMapping(value = "/platapi_nc/csharp-service/oa/commonApi/SmallProBeihuoCancel", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R smallProBeihuoCancel(@RequestParam(value = "smallproBillId") Integer smallproBillId);

    /**
     * 验证识别码是否过期
     */
    @GetMapping(value = "/platapi_nc/csharp-service/oa/commonApi/CheckMemberShipCode", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R<MemberShipCodeModelRes> checkMemberShipCode(@RequestParam(value = "codeStr") String codeStr);

    /**
     * 查询商品统计标签树
     */
    @GetMapping(value = "/platapi_nc/csharp-service/oa/api/GetProductMarksTree", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R<List<ProductMarksTreeRes>> getProductMarksTree();

    /**
     * 提交样机(提交成功返回的是html页面,慎用)
     */
    @PostMapping(value = "/platapi_nc/csharp-service/oa/ProductMkc/mkdDel", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R productMkcMkdDel(@RequestBody MkcMkdDelReq req);

    /**
     * 解锁样机
     */
    @GetMapping(value = "/platapi_nc/csharp-service/oa/ProductMkc/unLockmouldFlag", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    R productMkcUnLockmouldFlag(@RequestParam(value = "id") Integer mkcId);



    /**
     * 计算退定金费用
     */
    @GetMapping(value = "/platapi_nc/csharp-service/commonAPi/CalculateSubTuiServicesFee", produces = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    R calculateSubTuiServicesFee(@RequestParam(value = "returnId") Integer returnId);
}
