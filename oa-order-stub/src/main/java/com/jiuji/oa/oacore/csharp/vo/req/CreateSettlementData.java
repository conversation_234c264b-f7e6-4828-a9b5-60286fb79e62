package com.jiuji.oa.oacore.csharp.vo.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreateSettlementData {
    /**
     * 到账时间
     */
    private String arrivalDate;
    /**
     * 渠道id
     */
    private Integer qudaoId;
    /**
     * 品牌id
     */
    private Integer brandId;
    /**
     * 固定值 RedHeadInvoice
     */
    private String cashWay;
    /**
     * 固定值 AreaCashBack
     */
    private String businessType;
    /**
     * 单据日期
     */
    private String settlementDate;
}
