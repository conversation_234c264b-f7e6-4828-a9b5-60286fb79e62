package com.jiuji.oa.oacore.csharp.cloud.fallback;

import cn.hutool.core.lang.Dict;
import com.alibaba.fastjson.JSON;
import com.jiuji.oa.oacore.csharp.cloud.CsharpOaWcfCloud;
import com.jiuji.oa.oacore.csharp.vo.req.*;
import com.jiuji.oa.oacore.csharp.vo.res.BankPaymentRes;
import com.jiuji.oa.oacore.csharp.vo.res.CheckBeiHuoJiajiFromWuLiuRes;
import com.jiuji.oa.oacore.csharp.vo.res.ModifyBeiHuoJiajiFromWuLiuRes;
import com.jiuji.oa.oacore.oaorder.fallback.FallbackParams;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/28 20:33
 */
@Component
@Slf4j
public class CsharpOaWcfCloudFallback implements CsharpOaWcfCloud {
    @Override
    public R<List<Dict>> generateMkcPurcharse(UserReq<GenerateMkcPurcharseReq> req) {
        return R.error("/platapi_nc/csharp-service/kcApi/GenerateMkcPurcharse 失败, "
                + FallbackParams.paramsToStringLog(JSON.toJSONString(req)));
    }

    @Override
    public R mkcdhUpV2(OaWcfMkcdhUpV2Req req) {
        return R.error("/platapi_nc/csharp-service/kcApi/mkcdhUpV2 失败, "
                + FallbackParams.paramsToStringLog(JSON.toJSONString(req)));
    }

    @Override
    public R<BankPaymentRes> bankPayment(Integer areaId, Integer subType) {
        return R.error("/platapi_nc/csharp-service/oa/addorder/BankPayment 失败, "
                + areaId+" "+subType);
    }

    /**
     * 备货核对
     *
     * @param req
     * @return
     */
    @Override
    public R stockingCheckSaveByDetail(List<StockingCheckDetailReq> req) {
        return R.error("/platapi_nc/csharp-service/oa/StockingCheck/StockingCheckSaveByDetail失败,"
                + FallbackParams.paramsToStringLog(JSON.toJSONString(req)));
    }

    /**
     * 检查并获取最近有货跑腿调货门店
     *
     * @param wuliuId
     * @return
     */
    @Override
    public R<CheckBeiHuoJiajiFromWuLiuRes> checkBeiHuoJiajiFromWuLiu(Integer wuliuId) {
        return R.error("/platapi_nc/csharp-service/oa/commonApi/CheckBeiHuoJiajiFromWuLiu失败,wuliuId=" + wuliuId);
    }

    /**
     * 匹配跑腿调拨门店并生成物流单
     *
     * @param wuliuId
     * @return
     */
    @Override
    public R<ModifyBeiHuoJiajiFromWuLiuRes> modifyBeiHuoJiajiFromWuLiu(Integer wuliuId) {
        return R.error("/platapi_nc/csharp-service/oa/commonApi/ModifyBeiHuoJiajiFromWuLiu失败,wuliuId=" + wuliuId);
    }

    /**
     * 生成结算单
     *
     * @param req
     * @return
     */
    @Override
    public R<String> batchCreateSettlement(BatchCreateSettlementReq req) {
        return R.error("/platapi_nc/csharp-service/ajaxApi/batchCreateSettlement 失败"
                + FallbackParams.paramsToStringLog(JSON.toJSONString(req)));
    }
}
