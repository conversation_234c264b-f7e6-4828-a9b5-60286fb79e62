--九机线上表数据量情况说明
--tousu表：有22861条记录，Evaluate：2911077，EvaluateScore：27485922
-- 投诉表增加租户字段
if not exists(select  * from syscolumns where id =object_id('tousu') and name='xtenant')
BEGIN
ALTER TABLE tousu ADD xtenant INT DEFAULT NULL;
EXEC sp_addextendedproperty 'MS_Description', N'租户ID','SCHEMA', N'dbo','TABLE', N'tousu','COLUMN', N'xtenant';
END

if not exists(select  * from syscolumns where id =object_id('tousu') and name='xtenant_name')
BEGIN
ALTER TABLE tousu ADD xtenant_name varchar(64) DEFAULT NULL;
EXEC sp_addextendedproperty 'MS_Description', N'租户名称','SCHEMA', N'dbo','TABLE', N'tousu','COLUMN', N'xtenant_name';
END

-- 投诉表增加数据来源字段
if not exists(select  * from syscolumns where id =object_id('tousu') and name='source')
BEGIN
ALTER TABLE tousu ADD source tinyint DEFAULT NULL;
EXEC sp_addextendedproperty 'MS_Description', N'数据来源（null = 九机，1=九讯云，2=九讯云Neo）','SCHEMA', N'dbo','TABLE', N'tousu','COLUMN', N'source';
END

-- 评价表增加租户字段
if not exists(select  * from syscolumns where id =object_id('Evaluate') and name='xtenant')
BEGIN
ALTER TABLE Evaluate ADD xtenant bigint DEFAULT NULL;
EXEC sp_addextendedproperty
'MS_Description', N'代表是哪个租户提交的（父级别租户）',
'SCHEMA', N'dbo',
'TABLE', N'Evaluate',
'COLUMN', N'xtenant';
END

-- 评价表增加租户名称字段
if not exists(select  * from syscolumns where id =object_id('Evaluate') and name='xtenant_name')
BEGIN
ALTER TABLE Evaluate ADD xtenant_name varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT NULL;
EXEC sp_addextendedproperty
'MS_Description', N'租户名称',
'SCHEMA', N'dbo',
'TABLE', N'Evaluate',
'COLUMN', N'xtenant_name';
END

-- 评价表增加数据来源字段
if not exists(select  * from syscolumns where id =object_id('Evaluate') and name='source')
BEGIN
ALTER TABLE Evaluate ADD source tinyint DEFAULT NULL;
EXEC sp_addextendedproperty
'MS_Description', N'数据来源（null = 九机，1=九讯云，2=九讯云Neo）',
'SCHEMA', N'dbo',
'TABLE', N'Evaluate',
'COLUMN', N'source';
END

-- 评价表增加输出员工姓名字段
if not exists(select  * from syscolumns where id =object_id('Evaluate') and name='employee_name')
BEGIN
ALTER TABLE Evaluate ADD employee_name varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT NULL;
EXEC sp_addextendedproperty
'MS_Description', N'输出员工姓名',
'SCHEMA', N'dbo',
'TABLE', N'Evaluate',
'COLUMN', N'employee_name';
END

-- 评价表增加输出员工手机号字段
if not exists(select  * from syscolumns where id =object_id('Evaluate') and name='employee_mobile')
BEGIN
ALTER TABLE Evaluate ADD employee_mobile varchar(255) COLLATE Chinese_PRC_CI_AS DEFAULT NULL;
EXEC sp_addextendedproperty
'MS_Description', N'输出员工手机号',
'SCHEMA', N'dbo',
'TABLE', N'Evaluate',
'COLUMN', N'employee_mobile';
END

-- 评价表增加改进建议字段
if not exists(select  * from syscolumns where id =object_id('Evaluate') and name='improve_suggest')
BEGIN
ALTER TABLE Evaluate ADD improve_suggest text COLLATE Chinese_PRC_CI_AS DEFAULT NULL;
EXEC sp_addextendedproperty
'MS_Description', N'改进建议',
'SCHEMA', N'dbo',
'TABLE', N'Evaluate',
'COLUMN', N'improve_suggest';
END

-- 评价表增加支撑建议字段
if not exists(select  * from syscolumns where id =object_id('Evaluate') and name='support_suggest')
BEGIN
ALTER TABLE Evaluate ADD support_suggest text COLLATE Chinese_PRC_CI_AS DEFAULT NULL;
EXEC sp_addextendedproperty
'MS_Description', N'支撑建议',
'SCHEMA', N'dbo',
'TABLE', N'Evaluate',
'COLUMN', N'support_suggest';
END

-- 评价表增加推送消息id字段
if not exists(select  * from syscolumns where id =object_id('Evaluate') and name='message_id')
BEGIN
ALTER TABLE Evaluate ADD message_id int DEFAULT NULL;
EXEC sp_addextendedproperty
'MS_Description', N'推送消息id',
'SCHEMA', N'dbo',
'TABLE', N'Evaluate',
'COLUMN', N'message_id';
END

-- 评价表增加响应速度分数字段
if not exists(select  * from syscolumns where id =object_id('EvaluateScore') and name='speed_score')
BEGIN
ALTER TABLE EvaluateScore ADD speed_score tinyint DEFAULT NULL;
EXEC sp_addextendedproperty
'MS_Description', N'响应速度分数',
'SCHEMA', N'dbo',
'TABLE', N'EvaluateScore',
'COLUMN', N'speed_score';
END