murl.host=https://m.9ji.com
moaurl.host=https://moa.9ji.com
inwcfurl.host=http://inwcf.dev.9ji.com
inwcfurl.wuliu.log=/webView/webApi.aspx?act=wuliuLog&wlCompany={0}&wlNum={1}
inwcfurl.send.goods=/oaApi.svc/rest/caigouSubSend
inwcfurl.send.batch.goods=/oaApi.svc/rest/caigouSubSendBatch
inwcfurl.notice.sms.byagentid=webview/webApi.aspx?act=SendTextMessageByagentid
inwcfurl.send.goods.split.order=/oaApi.svc/rest/caigouSubSendV2
#oaï¿½ï¿½Ï¢ï¿½ï¿½ï¿½ï¿½
inwcfurl.oa.msg=/ajax.ashx?act=oaMessagePush&content={0}&link={1}&ch999ids={2}&msgType={3}

url.order.detail=/order/editorder?SubID={0}
url.goods.order.detail=/mStockOut/editorder?SubID={0}
url.aqiyi.ordercallback=/cloudapi_nc/orderservice/api/iqiyi/rechargeOrderCallback?xservicename=oa-orderservice

#Õ¾ï¿½ï¿½ï¿½ï¿½
wwwurl.zn.msg=/oaapi/api.ashx?act=SendUserMsg

#oawcfurl.ch999.cn
oawcfurl.notice.weixin=/ajax.ashx?act=qyMessage&userids={0}&msg={1}
oawcfurl.host=http://oawcf2.ch999.cn

inwcfurl.submit.orderex=/oaApi.svc/rest/SubmitOrderEx

inwcfurl.sub.pay=/oaApi.svc/rest/subPay

inwcfurl.purchase.sub.send=/oaapi.svc/rest/PurchaseSubSend