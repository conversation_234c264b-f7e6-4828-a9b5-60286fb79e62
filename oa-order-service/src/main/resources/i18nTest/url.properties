# oa.9ji.com
oaurl.host=https://demo.oa.saas.ch999.cn
oaurl.order.stocking.detail=/addOrder/editOrder?SubID={0}
oaurl.order.logistics.detail=/addOrder/wuliu?wuliuid={0}
oaurl.user.detail=/member?actionName=fromid&key={0}
oaurl.order.detail=/addOrder/editOrder?SubID={0}
# moa.9ji.com
moaurl.host=https://demo.moa.saas.ch999.cn
moaurl.code.image=/static/{0}
# tool.ch999.cn
toolurl.host=http://tool.ch999.cn
toolurl.print.client=/ajax.aspx?Act=getc&area={0}
# push.ch999.cn
pushurl.host=http://push.ch999.cn
pushurl.do.print=:8000/push?cname=p{0}&content={1},{2},{3},{4},{5},{6},{7},{8},1
# inwcf.ch999.cn
inwcfurl.host=http://inwcf.dev.9ji.com
inwcfurl.notice.weixin=/ajax.ashx?act=qyMessage&userids={0}&msg={1}
inwcfurl.notice.sms=/webView/webApi.aspx?act=SendMessage
inwcfurl.notice.sms.byagentid=webview/webApi.aspx?act=SendTextMessageByagentid
inwcfurl.notice.email=/webView/webApi.aspx?act=SendOAEmail
inwcfurl.order.submit=/oaApi.svc/rest/submitorder
inwcfurl.pj.beihuo=/oaApi.svc/rest/PJbeihuo
inwcfurl.pj.beihuo.del=/oaApi.svc/rest/PJbeihuoDel
inwcfurl.del.order=/oaApi.svc/rest/delOrderByCh999User
inwcfurl.product.kc=/oaApi.svc/rest/product_kc
inwcfurl.send.goods=/oaApi.svc/rest/caigouSubSend
inwcfurl.send.batch.goods=/oaApi.svc/rest/caigouSubSendBatch
inwcfurl.send.goods.split.order=/oaApi.svc/rest/caigouSubSendV2
#oaï¿½ï¿½Ï¢ï¿½ï¿½ï¿½ï¿½
inwcfurl.oa.msg=/ajax.ashx?act=oaMessagePush&content={0}&link={1}&ch999ids={2}&msgType={3}

#oawcfurl.ch999.cn
oawcfurl.notice.weixin=/ajax.ashx?act=qyMessage&userids={0}&msg={1}
oawcfurl.host=http://oawcf2.ch999.cn

moaurl.integral=/ajaxapi/weikeJifenAdd?ch999_id={0,number,#}&fen={1,number,#}&comment={2}&title={3}

inwcfurl.submit.orderex=/oaApi.svc/rest/SubmitOrderEx

inwcfurl.sub.pay=/oaApi.svc/rest/subPay

inwcfurl.purchase.sub.send=/oaapi.svc/rest/PurchaseSubSend