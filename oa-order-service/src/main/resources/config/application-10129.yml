consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://weedfs.xn.saas.ch999.cn:5083
  select:
    url: https://img.jz0377.com/
  upload:
    url: http://weedfs.xn.saas.ch999.cn:9333
instance-zone: 10129
jiuji:
  sys:
    moa: https://moa.jz0377.com
    pc: https://oa.jz0377.com
    web: https://www.jz0377.com
    inWcf: http://inwcf.jz0377.com
    oaWcf: http://inwcf2.jz0377.com
    xtenant: 10129
  xtenant: 129000
  seata:
    enabled: false
    config:
      consul:
        address: xxx
        prefix: xxx
    registry:
      consul:
        address: xxx
        cluster: xxx
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10129:VXYADF83gp9P@***********:27017,***********:27017,***********:27017/ch999oa__10129
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10129
    password: FQN8lkuOoMwe
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_core__10129
  oa_nc:
    dbname: oa_nc__10129
    password: m2jcxEuCmgw7
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_nc__10129
  oa_log:
    dbname: oa_log__10129
    password: '9ysrJ64IrWjK'
    url: tidb.serv.hd.saas.ch999.cn:9383
    username: oa_log__10129
office:
  sys:
    xtenant: 10129
rabbitmq:
  master:
    password: kykjM
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10129
    vhost: oaAsync__10129
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: KjjMX
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oa__10129
    vhost: oa__10129
  oaAsync:
    password: kykjM
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10129
    vhost: oaAsync__10129
  printer:
    password: IqRAu
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: printer__10129
    vhost: printer__10129
redis:
  oa:
    host: ***********
    password: google99
    port: 6392
    url: google99@***********:6392
sms:
  send:
    email:
      url: http://sms.jz0377.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.jz0377.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10129
sqlserver:
  after_write:
    dbname: ch999oanew__10129
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: 'lb2E8fEHtFtL'
    port: 1433
    username: ch999oanew__10129
  ch999oanew:
    dbname: ch999oanew__10129
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: 'lb2E8fEHtFtL'
    port: 1433
    username: ch999oanew__10129
  ch999oanewHis:
    dbname: ch999oanew__10129
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: 'lb2E8fEHtFtL'
    port: 1433
    username: ch999oanew__10129
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10129
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: 'bZGPs1TRyxEw'
    port: 1433
    username: office__10129
  oanewWrite:
    dbname: ch999oanew__10129
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: 'lb2E8fEHtFtL'
    port: 1433
    username: ch999oanew__10129
  office:
    dbname: office__10129
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: 'bZGPs1TRyxEw'
    port: 1433
    username: office__10129
  officeWrite:
    dbname: office__10129
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: 'bZGPs1TRyxEw'
    port: 1433
    username: office__10129
  smallpro_write:
    dbname: ch999oanew__10129
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "lb2E8fEHtFtL"
    port: 1433
    username: ch999oanew__10129
  web999:
    dbname: web999__10129
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: XVfeJz2fhSJY
    port: 1433
    username: web999__10129
  ch999oanewReport:
    dbname: ch999oanew__10129
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "lb2E8fEHtFtL"
    port: 1433
    username: ch999oanew__10129
  ershou:
    dbname: ershou__10129
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "nFrOXM7Ypm5d"
    port: 1433
    username: ershou__10129
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.jz0377.com/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.jz0377.com/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
autoCalculateSalary:
  xtenant: 10129

# 亚丁保险对接地址url参数等
yading:
  # 同步员工信息地址
  sync-user-url: https://mapi.cdydkj.cn/jiuji/User/synchronizaMember
  # 注册保险的地址
  insurance-register-url: http://jiuji.cdydkj.cn/RightApply/addApply
  # 接口加签的签名key
  sign-key: 5f623e2d2e90cbd25d2b05a6a9a34faa


apollo:
  url: http://**************:8080
  file: application-order.yml
