# data1
sqlserver.data1.host=***************
sqlserver.data1.port=1433
## sqlserver:web99
sqlserver.web999.host=${sqlserver.data1.host}
sqlserver.web999.port=${sqlserver.data1.port}
sqlserver.web999.dbname=web999
sqlserver.web999.username=devops
sqlserver.web999.password=devops00
## sqlserver:office
sqlserver.officeWrite.host=${sqlserver.data1.host}
sqlserver.officeWrite.port=${sqlserver.data1.port}
sqlserver.officeWrite.dbname=office
sqlserver.officeWrite.username=devops
sqlserver.officeWrite.password=devops00
## sqlserver:ch999oanew
sqlserver.ch999oanew.host=${sqlserver.data1.host}
sqlserver.ch999oanew.port=${sqlserver.data1.port}
sqlserver.ch999oanew.dbname=ch999oanew
sqlserver.ch999oanew.username=devops
sqlserver.ch999oanew.password=devops00
## sqlserver:ch999oanewReport
sqlserver.ch999oanewReport.host=${sqlserver.data1.host}
sqlserver.ch999oanewReport.port=${sqlserver.data1.port}
sqlserver.ch999oanewReport.dbname=ch999oanew
sqlserver.ch999oanewReport.username=devops
sqlserver.ch999oanewReport.password=devops00
## sqlserver:smallpro_write
sqlserver.smallpro_write.host=${sqlserver.data1.host}
sqlserver.smallpro_write.port=${sqlserver.data1.port}
sqlserver.smallpro_write.dbname=ch999oanew
sqlserver.smallpro_write.username=devops
sqlserver.smallpro_write.password=devops00
## redisConfig
redis.oa.host=**************
redis.oa.port=6379
redis.oa.password=google99
redis.oa.url=${redis.oa.host}:${redis.oa.port}
## ÈÕÖ¾ÎÄ¼þÅäÖÃÂ·¾¶
logging.config.path=classpath:log/log4j2-dev.xml
## mongoDB
mongodb.url1=*****************************************************
#Íâ²¿Á´½ÓÎÄ¼þµØÖ·
url.source.path=i18nTest/url
#rabbitMq
rabbitmq.master.url=**************
rabbitmq.master.port=5672
rabbitmq.master.username=oa
rabbitmq.master.password=ch999
rabbitmq.master.publisher-confirm=true
rabbitmq.master.publisher-returns=true
rabbitmq.master.connection-timeout=20000
rabbitmq.master.vhost=oaAsync
#consul
consul.host=**************
consul.port=8500
spring.cloud.consul.discovery.instance-zone=10000
spring.cloud.consul.discovery.default-query-tag=zone=10000

sqlserver.datasource.max-pool-size=100
mysql.datasource.max-pool-size=100
