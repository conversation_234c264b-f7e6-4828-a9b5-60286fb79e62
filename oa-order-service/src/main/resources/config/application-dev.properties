sqlserver.data.host=sqlserver.dev.ch999.cn
sqlserver.data.port=1433

## sqlserver:ch999oanew
sqlserver.ch999oanew.host=${sqlserver.data.host}
sqlserver.ch999oanew.port=${sqlserver.data.port}
sqlserver.ch999oanew.dbname=ch999oanew
sqlserver.ch999oanew.username=devops
sqlserver.ch999oanew.password=devops00
## sqlserver:ch999oanew2
sqlserver.ch999oanew2.host=${sqlserver.data.host}
sqlserver.ch999oanew2.port=${sqlserver.data.port}
sqlserver.ch999oanew2.dbname=ch999oanew2
sqlserver.ch999oanew2.username=devops
sqlserver.ch999oanew2.password=devops00
sqlserver.ch999oanew2.is-enable=true
## sqlserver:ch999oanewReport
sqlserver.ch999oanewReport.host=${sqlserver.data.host}
sqlserver.ch999oanewReport.port=${sqlserver.data.port}
sqlserver.ch999oanewReport.dbname=ch999oanew
sqlserver.ch999oanewReport.username=devops
sqlserver.ch999oanewReport.password=devops00
## sqlserver:oanewWrite
sqlserver.oanewWrite.host=${sqlserver.data.host}
sqlserver.oanewWrite.port=${sqlserver.data.port}
sqlserver.oanewWrite.dbname=ch999oanew
sqlserver.oanewWrite.username=devops
sqlserver.oanewWrite.password=devops00

## sqlserver:ch999oanewHis
sqlserver.ch999oanewHis.host=sqlserver.dev.ch999.cn
sqlserver.ch999oanewHis.port=1433
sqlserver.ch999oanewHis.dbname=ch999oahis
sqlserver.ch999oanewHis.username=devops
sqlserver.ch999oanewHis.password=devops00
sqlserver.ch999oanewHis.is-enable=true

## sqlserver:office
sqlserver.office.host=${sqlserver.data.host}
sqlserver.office.port=${sqlserver.data.port}
sqlserver.office.dbname=office
sqlserver.office.username=devops
sqlserver.office.password=devops00

## sqlserver:office2
sqlserver.office2.host=${sqlserver.data.host}
sqlserver.office2.port=${sqlserver.data.port}
sqlserver.office2.dbname=office2
sqlserver.office2.username=devops
sqlserver.office2.password=devops00
sqlserver.office2.is-enable=true
## sqlserver:oanewHis
sqlserver.oanewHis.host=${sqlserver.data.host}
sqlserver.oanewHis.port=${sqlserver.data.port}
sqlserver.oanewHis.dbname=ch999oahis
sqlserver.oanewHis.username=devops
sqlserver.oanewHis.password=devops00
sqlserver.oanewHis.is-enable=true

## sqlserver:ershou
sqlserver.ershou.host=${sqlserver.data.host}
sqlserver.ershou.port=${sqlserver.data.port}
sqlserver.ershou.dbname=ershou
sqlserver.ershou.username=devops
sqlserver.ershou.password=devops00
## sqlserver:web99
sqlserver.web999.host=${sqlserver.data.host}
sqlserver.web999.port=${sqlserver.data.port}
sqlserver.web999.dbname=web999
sqlserver.web999.username=devops
sqlserver.web999.password=devops00
## sqlserver:web999_other
sqlserver.web999_other.host=${sqlserver.data.host}
sqlserver.web999_other.port=${sqlserver.data.port}
sqlserver.web999_other.dbname=web999_other
sqlserver.web999_other.username=devops
sqlserver.web999_other.password=devops00

## sqlserver:oanewWrite
sqlserver.officeWrite.host=${sqlserver.data.host}
sqlserver.officeWrite.port=${sqlserver.data.port}
sqlserver.officeWrite.dbname=office
sqlserver.officeWrite.username=devops
sqlserver.officeWrite.password=devops00

## sqlserver:smallpro_write
sqlserver.smallpro_write.host=${sqlserver.data.host}
sqlserver.smallpro_write.port=${sqlserver.data.port}
sqlserver.smallpro_write.dbname=ch999oanew
sqlserver.smallpro_write.username=devops
sqlserver.smallpro_write.password=devops00

## mysql
mysql.url=mysql.dev.ch999.cn:3306
## mysql:oa_nc
mysql.oa_nc.url=${mysql.url}
mysql.oa_nc.dbname=oa_nc
mysql.oa_nc.username=oa_nc
mysql.oa_nc.password=oa_nc2020#

## mysql:oa_log
mysql.oa_log.url=************:9383
mysql.oa_log.dbname=oa_log
mysql.oa_log.username=oa_log
mysql.oa_log.password=oa_log!@#

## mysql:oa_core
mysql.oa_core.url=${mysql.url}
mysql.oa_core.dbname=oa_core
mysql.oa_core.username=oa_core
mysql.oa_core.password=oa_core2020#

## StarRocks
starrocks.url=dwcluster.ch999.cn:19030
starrocks.dbname=ods_jiuji
starrocks.username=dev_operation
starrocks.password=osPIRH2dT47

## midl
redis.oa.host=redis.dev.ch999.cn
redis.oa.port=6379
redis.oa.password=google00
redis.oa.url=${redis.oa.password}@${redis.oa.host}:${redis.oa.port}

redis.oa2.clusterNode=************:7001,************:7002,************:7003,************:7001,************:7002,************:7003
redis.oa2.password=google00

## \u65E5\u5FD7
logging.config.path=classpath:log/log4j2-jiujitest.xml
## mongoDB
mongodb.url1=mongodb://ch999oa:<EMAIL>:27017/ch999oa
## image
image.upload.url=http://weedfs.dev.ch999.cn:9333
image.del.url=http://weedfs.dev.ch999.cn:5083
image.select.url=https://img.dev.9ji.com/
## spring.autoconfigure.exclude
autoconfigure.exclude=org.springframework.cloud.consul.serviceregistry.ConsulAutoServiceRegistrationAutoConfiguration,org.springframework.cloud.consul.serviceregistry.ConsulServiceRegistryAutoConfiguration

jiuji.weborder.enableHis=true

jiuji.seata.enabled=true
jiuji.seata.config.consul.address=************:8500
jiuji.seata.config.consul.prefix=seata-config/orderservice
jiuji.seata.registry.consul.address=************:8500
jiuji.seata.registry.consul.cluster=seata-jiuji

# rabbitMq-oaAsync
rabbitmq.master.url=rabbitmq.dev.ch999.cn
rabbitmq.master.port=5672
rabbitmq.master.username=oaAsync
rabbitmq.master.password=oaAsyncpwd
rabbitmq.master.vhost=oaAsync
rabbitmq.oa.url=rabbitmq.dev.ch999.cn
rabbitmq.oa.port=5672
rabbitmq.oa.username=oa
rabbitmq.oa.password=ch999
rabbitmq.oa.vhost=oa

consul.host=${spring.cloud.client.hostname}
consul.port=8500
spring.cloud.consul.discovery.instance-zone=9ji
instance-zone=9ji
active-zone=dev

sqlserver.datasource.max-pool-size=100
mysql.datasource.max-pool-size=100

sms.url=http://sms.dev.9ji.com/?test=
sms.send.email.url=http://sms.dev.9ji.com/email/email.aspx
sms.send.in.url=http://office/Handler/api.ashx

meituan.shangou.appId=6322
meituan.shangou.appSecret=8d5697553c53e7d1f26dff66dce21fec

jiuji.sys.moa=https://moa.dev.9ji.com
jiuji.sys.pc=https://oa.dev.9ji.com
jiuji.sys.web=https://www.dev.9ji.com
jiuji.sys.inWcf=http://inwcf.dev.9ji.com
jiuji.sys.oaWcf=http://oawcf.dev.9ji.com

autoCalculateSalary.xtenant=10000

# ÑÇ¶¡±£ÏÕ¶Ô½ÓµØÖ·url²ÎÊýµÈ
# Í¬²½Ô±¹¤ÐÅÏ¢µØÖ·
yading.sync-user-url: http://yading.viphfkj.com/jiuji/User/synchronizaMember
# ×¢²á±£ÏÕµÄµØÖ·
yading.insurance-register-url: http://train.9000ji.com/RightApply/addApply
# ½Ó¿Ú¼ÓÇ©µÄÇ©Ãûkey
yading.sign-key: 5f623e2d2e90cbd25d2b05a6a9a34faa


apollo.url=http://************:6000
apollo.file=application-order.yml

lmstfy.host=************
lmstfy.mult.first-lmstfy-client.token=01H9FPJXDY73P2YZC5FNJRM9YA
orderservice_baozuncloud_url=https://dss-api-oms2wms-uat.baozun.com
feign.logger.my.isFull: true

