consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://weedfs.xn.saas.ch999.cn:5083
  select:
    url: https://img.hx1993.com/
  upload:
    url: http://weedfs.xn.saas.ch999.cn:9333
instance-zone: 10094
jiuji:
  sys:
    moa: https://moa.hx1993.com
    pc: https://oa.hx1993.com
    web: https://www.hx1993.com
    xtenant: 10094
  xtenant: 94000
  seata:
    enabled: false
    config:
      consul:
        address: xxx
        prefix: xxx
    registry:
      consul:
        address: xxx
        cluster: xxx
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10094:WhuTEEAJEfkH@***********:27017,***********:27017,***********:27017/ch999oa__10094
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10094
    password: hCKcEaTbYAxZ
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_core__10094
  oa_nc:
    dbname: oa_nc__10094
    password: EDHIg2XNyPZD
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_nc__10094
office:
  sys:
    xtenant: 10094
rabbitmq:
  master:
    password: PkJAr
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10094
    vhost: oaAsync__10094
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: VAqcy
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oa__10094
    vhost: oa__10094
  oaAsync:
    password: PkJAr
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10094
    vhost: oaAsync__10094
  printer:
    password: SoVPs
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: printer__10094
    vhost: printer__10094
redis:
  oa:
    host: ***********
    password: google99
    port: 6390
    url: google99@***********:6390
sms:
  send:
    email:
      url: http://sms.hx1993.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.hx1993.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10094
sqlserver:
  after_write:
    dbname: ch999oanew__10094
    host: sqlserver.serv.hb.saas.ch999.cn
    password: '5Sone^7kxrf7'
    port: 1433
    username: ch999oanew__10094
  ch999oanew:
    dbname: ch999oanew__10094
    host: sqlserver.serv.hb.saas.ch999.cn
    password: '5Sone^7kxrf7'
    port: 1433
    username: ch999oanew__10094
  ch999oanewHis:
    dbname: ch999oanew__10094
    host: sqlserver.serv.hb.saas.ch999.cn
    password: '5Sone^7kxrf7'
    port: 1433
    username: ch999oanew__10094
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10094
    host: sqlserver.serv.hb.saas.ch999.cn
    password: 'huAQjQzreT7b'
    port: 1433
    username: office__10094
  oanewWrite:
    dbname: ch999oanew__10094
    host: sqlserver.serv.hb.saas.ch999.cn
    password: '5Sone^7kxrf7'
    port: 1433
    username: ch999oanew__10094
  office:
    dbname: office__10094
    host: sqlserver.serv.hb.saas.ch999.cn
    password: 'huAQjQzreT7b'
    port: 1433
    username: office__10094
  officeWrite:
    dbname: office__10094
    host: sqlserver.serv.hb.saas.ch999.cn
    password: 'huAQjQzreT7b'
    port: 1433
    username: office__10094
  smallpro_write:
    dbname: ch999oanew__10094
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "5Sone^7kxrf7"
    port: 1433
    username: ch999oanew__10094
  web999:
    dbname: web999__10094
    host: sqlserver.serv.hb.saas.ch999.cn
    password: hjmuKQfsAKpj
    port: 1433
    username: web999__10094
  ch999oanewReport:
    dbname: ch999oanew__10094
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "5Sone^7kxrf7"
    port: 1433
    username: ch999oanew__10094
  ershou:
    dbname: ershou__10094
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "tLTmv3AUskz1"
    port: 1433
    username: ershou__10094
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.hx1993.com/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.hx1993.com/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
autoCalculateSalary:
  xtenant: 10094
