<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.tousu.dao.TouSuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="AreaResultMap" type="com.jiuji.oa.oacore.tousu.po.AreaInfoModel">
        <id column="id" property="id"/>
        <result column="area" property="area"/>
        <result column="area_name" property="area_name"/>
        <result column="city" property="city"/>
        <result column="kind1" property="kind1"/>
        <result column="kind2" property="kind2"/>
        <result column="authorizeid" property="authorizeid"/>
        <result column="departId" property="departId"/>
        <result column="ispass" property="ispass"/>
        <result column="Province_name" property="province_name"/>
        <result column="city_name" property="city_name"/>
        <result column="rank" property="rank"/>
        <result column="dcAreaID" property="dcAreaID"/>
        <result column="dcArea" property="dcArea"/>
        <result column="userid" property="userid"/>
        <result column="kemu" property="kemu"/>
        <result column="cityid" property="cityid"/>
        <result column="level1" property="level1"/>
        <result column="isweb" property="isweb"/>
        <result column="isSend" property="issend"/>
        <result column="printName" property="printName"/>
        <result column="position" property="position"/>
        <result column="courier" property="courierStr"/>
        <result column="logo" property="logo"/>
        <result column="company_tel1" property="company_tel1"/>
        <result column="url" property="url"/>
        <result column="company_address" property="company_address"/>
        <result column="cityAreas" property="cityAreas"/>
        <result column="autoTransfer" property="autoTransfer"/>
        <result column="xtenant" property="xtenant"/>
        <result column="hours" property="hours"/>
        <result column="company_tel2" property="company_tel2"/>
        <result column="company_qq" property="company_qq"/>
        <result column="DisplayLevel" property="displayLevel"/>
        <result column="AreaType" property="areaType"/>
        <result column="methodology" property="methodology"/>
        <result column="deliveryBeginTime" property="deliveryBeginTime"/>
        <result column="deliveryEndTime" property="deliveryEndTime"/>
        <result column="areaVision" property="areaVision"/>
        <result column="areaTask" property="areaTask"/>
        <result column="transferLimit" property="transferLimit"/>
        <result column="smsChannel" property="smsChannel"/>
        <result column="smsChannelMarketing" property="smsChannelMarketing"/>
        <result column="mUrl" property="mUrl"/>
        <result column="webUrl" property="webUrl"/>
        <result column="hsUrl" property="hsUrl"/>
        <result column="pid" property="pid"/>
    </resultMap>
    <resultMap id="touSuProcess" type="com.jiuji.oa.oacore.tousu.res.TouSuProcess">
        <result column="id" property="id"/>
        <result column="tsID" property="tsID"/>
        <result column="opUser" property="opUser"/>
        <result column="dsc_" property="dsc_"/>
        <result column="intime" property="intime"/>
        <result column="isShow" property="show"/>
        <result column="cate" property="cate"/>
        <result column="cateContent" property="cateContent"/>
        <result column="attachFiles" property="attachFiles"/>
        <result column="ch999_id" property="ch999_id"/>
        <collection property="attachments" ofType="com.jiuji.oa.oacore.tousu.res.AttachmentsRes">
            <id column="id" property="id"/>
            <result column="filename" property="filename"/>
            <result column="filepath" property="filepath"/>
            <result column="fid" property="fid"/>
            <result column="Extension" property="extension"/>
        </collection>
    </resultMap>
    <resultMap id="TouSuResMap" type="com.jiuji.oa.oacore.tousu.res.TouSuModelRes">
        <id column="id" property="id"/>
        <result column="area" property="area"/>
        <result column="userid" property="userid"/>
        <result column="email" property="email"/>
        <result column="content_" property="content_"/>
        <result column="mobile" property="mobile"/>
        <result column="writer" property="writer"/>
        <result column="inuser" property="inuser"/>
        <result column="addTime" property="addTime"/>
        <result column="writeIp" property="writeIp"/>
        <result column="type_" property="type_"/>
        <result column="states_" property="states_"/>
        <result column="statsName" property="statsName"/>
        <result column="dsc" property="dsc"/>
        <result column="userName" property="userName"/>
        <result column="areaName" property="areaName"/>
        <result column="memberName" property="memberName"/>
        <result column="tousupic" property="tousupic"/>
        <result column="TouSuTypeIds" property="TouSuTypeIds"/>
        <result column="huanyuantimeout" property="huanyuantimeout"/>
        <result column="DealTimeout" property="DealTimeout"/>
        <result column="userclass" property="userclass"/>
        <result column="userclassName" property="userclassName"/>
        <result column="TouSuTypeIds" property="TouSuTypeIds"/>
        <collection property="ProcessLog" resultMap="touSuProcess" ofType="com.jiuji.oa.oacore.tousu.res.TouSuProcess">
        </collection>
        <collection property="TouSuTypeDes" ofType="String"/>
        <collection property="attachments" ofType="com.jiuji.oa.oacore.tousu.res.AttachmentsRes">
            <id column="id" property="id"/>
            <result column="filename" property="filename"/>
            <result column="filepath" property="filepath"/>
            <result column="fid" property="fid"/>
            <result column="Extension" property="extension"/>
        </collection>
        <collection property="touSuZeRenRens" ofType="com.jiuji.oa.oacore.tousu.res.TouSuZeRenRen">
            <id column="ch999_Id" property="ch999_Id"/>
            <result column="ch999_Name" property="ch999_Name"/>
            <result column="departId" property="departId"/>
            <result column="area1id" property="area1id"/>
            <result column="area" property="area"/>
            <result column="area_name" property="area_name"/>
            <result column="departName1" property="departName1"/>
            <result column="departName2" property="departName2"/>
        </collection>
    </resultMap>
    <resultMap id="TouSuZenRenRenMap" type="com.jiuji.oa.oacore.tousu.res.TouSuZeRenRen">
        <result column="ch999_Id" property="ch999_Id"/>
        <result column="ch999_Name" property="ch999_Name"/>
        <result column="departId" property="departId"/>
        <result column="area1id" property="area1id"/>
        <result column="area" property="area"/>
        <result column="area_name" property="area_name"/>
        <result column="departName1" property="departName1"/>
        <result column="departName2" property="departName2"/>
    </resultMap>
    <insert id="addTouSu">
        INSERT INTO dbo.tousu(
            userid ,email ,mobile ,content_ ,writer ,addTime ,writeIp ,type_ ,states_ ,userName ,
            Areaid,areaName,inuser,memberName,tousupic,tousuTypes,huanyuantimeout,dealtimeout)
        VALUES(#{touSuModel.userid},#{touSuModel.email},#{touSuModel.mobile},#{touSuModel.content_},#{touSuModel.writer},getdate(),#{touSuModel.writeIp},
                  #{touSuModel.type_},#{touSuModel.states_},#{touSuModel.userName},#{touSuModel.areaId},#{touSuModel.areaName},#{touSuModel.inuser},
        #{touSuModel.memberName},#{touSuModel.tousupic},#{touSuModel.TouSuTypeIds},#{touSuModel.huanyuantimeout},#{touSuModel.dealTimeout})
    </insert>
    <update id="updateScoreArea">
        update tousu set scoreArea = #{scoreArea} where id =#{id}
    </update>
    <update id="updateScoreDep">
        update tousu set scoreDep = #{scoreDep} where id = #{id}
    </update>
    <select id="getTouSuId" resultType="java.lang.Integer">
        SELECT id FROM dbo.tousu with(nolock) where userid=#{userId}
        <![CDATA[and states_!=3
        ]]>
    </select>
    <select id="getTouSuCount" resultType="java.lang.Integer">
        select count(1) from dbo.tousu with(nolock) where mobile=#{mobile} and content_=#{content_}
    </select>
    <select id="getUserClass" resultType="java.lang.Integer">
        SELECT userclass FROM dbo.BBSXP_Users with(nolock) WHERE ID=#{userId}
    </select>
    <select id="getAreaListByArea" resultType="com.jiuji.oa.oacore.tousu.po.AreaInfoModel">
        select area,area_name,id from dbo.areainfo with(nolock) where area = #{area}
    </select>
    <select id="getReciversByCondition" resultType="com.jiuji.oa.oacore.tousu.po.ReceiverCache">
        select
        case
        when u.area1id in (13, 16, 22)
        then cast(dbo.getBuMenCode(u.departId) as nvarchar(20))
        else cast(u.area1id as nvarchar(10))
        end as name,
        case
        when u.area1id in (13, 16, 22)
        then 2 else 1 end as type_,ch999_id,u.area1id,z.leve
        from dbo.ch999_user u with(nolock)
        inner join zhiwu z with(nolock) ON u.zhiwuid = z.id
        where u.iszaizhi = 1
        <![CDATA[and isnull(u.isshixi, 0) != 4 and u.islogin = 0]]>
        <if test="authorize != null and authorize>0">
            AND EXISTS(SELECT 1 FROM dbo.areainfo ai with(nolock) WHERE ai.id = u.area1id AND ai.authorizeid =
            #{authorize})
        </if>
        <if test="departIds!=null and departIds.size>0">
            and u.departId in
            <foreach collection="departIds" index="index" item="departId" separator="," open="(" close=")">
                #{departId}
            </foreach>
        </if>
        <if test="areaids!=null and areaids.size>0">
            and u.area1id in
            <foreach collection="areaids" index="index" item="areaid" separator="," open="(" close=")">
                #{areaid}
            </foreach>
        </if>
        <if test="orCon='ids'">
            <if test="ids!=null and ids.size>0">
                and u.zhiwuid in
                <foreach collection="ids" index="index" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
        </if>
        <if test="orCon='roleIds'">
            <if test="roleIds!=null and roleIds.size>0">
                and u.mainRole in
                <foreach collection="roleIds" index="index" item="roleId" separator="," open="(" close=")">
                    #{roleId}
                </foreach>
            </if>
        </if>
        <if test="orCon='both'">
            and (
            <if test="ids!=null and ids.size>0 and roleIds!=null and roleIds.size>0">
                u.zhiwuid in
                <foreach collection="ids" index="index" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
                or u.mainRole in
                <foreach collection="roleIds" index="index" item="roleId" separator="," open="(" close=")">
                    #{roleId}
                </foreach>
            </if>
            <if test="ids!=null and ids.size>0 ">
                <if test="roleIds==null or roleIds.size==0">
                    u.zhiwuid in
                    <foreach collection="ids" index="index" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="roleIds!=null and roleIds.size>0 ">
                <if test="ids==null or ids.size==0">
                    u.mainRole in
                    <foreach collection="roleIds" index="index" item="roleId" separator="," open="(" close=")">
                        #{roleId}
                    </foreach>
                </if>
            </if>
            )
        </if>
        <if test="ch999ids!=null and ch999ids.size>0">
            and u.ch999_id in
            <foreach collection="ch999ids" index="index" item="ch999id" separator="," open="(" close=")">
                #{ch999id}
            </foreach>
        </if>
    </select>
    <select id="loadAllAreaInfo" resultMap="AreaResultMap">
        select id,area,area_name,city,kind1,kind2,authorizeid,departId,ispass,Province_name,city_name,rank,dcAreaID,dcArea,userid,kemu,cityid,level1,
               isweb,isSend,printName,position,courier,concat('https://moa.9ji.com/static/24,37835b86a5e75b',logo),company_tel1,url,company_address,cityAreas,autoTransfer,xtenant,hours,company_tel2,company_qq,
               DisplayLevel,AreaType,methodology,deliveryBeginTime,deliveryEndTime,areaVision,areaTask,transferLimit,(case xtenant when 0 then 9 when 1 then 23 when 2 then 27 end) as smsChannel,
               (case xtenant when 0 then 18 when 1 then 24 when 2 then 27 end) as smsChannelMarketing,convert(int,(LEFT(convert(varchar(10),cityid),2))) as pid
        from dbo.areainfo with(nolock)
        where 1=1
        order by kind1,rank asc
    </select>
    <select id="loadAreaInfoById" resultMap="AreaResultMap">
        select id,area,area_name,city,kind1,kind2,authorizeid,departId,ispass,Province_name,city_name,rank,dcAreaID,dcArea,userid,kemu,cityid,level1,
               isweb,isSend,printName,position,courier,concat('https://moa.9ji.com/static/24,37835b86a5e75b',logo),company_tel1,url,company_address,cityAreas,autoTransfer,xtenant,hours,company_tel2,company_qq,
               DisplayLevel,AreaType,methodology,deliveryBeginTime,deliveryEndTime,areaVision,areaTask,transferLimit,(case xtenant when 0 then 9 when 1 then 23 when 2 then 27 end) as smsChannel,
               (case xtenant when 0 then 18 when 1 then 24 when 2 then 27 end) as smsChannelMarketing,convert(int,(LEFT(convert(varchar(10),cityid),2))) as pid
        from dbo.areainfo with(nolock)
        where id=${area1Id}
        order by kind1,rank asc
    </select>

    <select id="getUserIds" resultMap="touSuProcess">
        select t.id,t.tsID,t.opUser,t.dsc_ as dsc,t.intime,t.cate,t.attachFiles,t.isShow,t.isxinsheng ,u.ch999_id from ${officeName}.dbo.tsProcess t with(nolock) left join dbo.ch999_user u with(nolock) on
        t.opUser=u.ch999_name where t.isShow=1 and t.tsID in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getTouSuModelByUserIdAndId" resultMap="TouSuResMap">
         SELECT t.id,t.tsID,t.opUser,t.dsc_ as dsc,t.intime,t.cate,t.attachFiles,t.isShow,t.isxinsheng, u.userclass
         FROM ${officeName}.tousu t WITH(NOLOCK) LEFT JOIN dbo.BBSXP_Users u WITH(NOLOCK) ON u.ID = t.userid WHERE t.userid =${userid}  and t.id =${id}
    </select>
    <select id="getTouSuProcessBytsID" resultMap="touSuProcess">
         select t.*,u.ch999_id from ${officeName}.dbo.tsProcess t with(nolock) left join dbo.ch999_user u with(nolock) on t.opUser=u.ch999_name where t.isShow=1 and
          t.tsID=${tsID} order by intime
    </select>

    <select id="getTouSuPicByIds" resultType="com.jiuji.oa.oacore.tousu.res.AttachmentsRes">
        SELECT id, linkedID, type, filename, filepath, kind, fid, Extension, frame_path as framePath
        FROM dbo.attachments WITH(NOLOCK) WHERE id IN
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getTouSuZeRenRenById" resultMap="TouSuZenRenRenMap">
        SELECT tz.userId ch999_Id,tz.userName ch999_Name,tz.departId,tz.area1id,a.area,a.area_name,d.name departName2,d2.name departName1
                FROM ${officeName}..tousuZeRenRen tz WITH(NOLOCK)
                LEFT JOIN dbo.areainfo a WITH(NOLOCK) ON tz.area1id = a.id
                LEFT JOIN dbo.departInfo d WITH(NOLOCK) ON d.id = tz.departId
                LEFT JOIN dbo.departInfo d2 WITH(NOLOCK) ON d2.id = tz.departId
                WHERE tz.tousuId = ${tousuId}
                ORDER BY tz.tousuRank
    </select>
    <!--    <select id="listTouSuRes" resultMap="TouSuResMap">-->
    <!--        select * from ${officeName}.dbo.tousu WITH(NOLOCK) where userid=${userid} order by id desc ;-->
    <!--    </select>-->
    <select id="getProcessUser" resultType="com.jiuji.oa.oacore.tousu.res.ProcessUser">
        SELECT ISNULL(states_,1) states_,processUser FROM tousu WITH(NOLOCK) WHERE userid =${userid} and id=${id}
    </select>
    <select id="getMobile" resultType="java.lang.String">
        select mobile from getOfficeName().dbo.tousu with(nolock) where id =${tsID}
    </select>
    <select id="getUserById" resultType="com.jiuji.oa.oacore.tousu.res.User">
        SELECT t.mobile,u.xtenant,u.ID userid FROM ${officeName}.dbo.tousu t WITH(NOLOCK)
                    LEFT JOIN dbo.BBSXP_Users u WITH(NOLOCK) ON t.userid = u.ID
                    WHERE t.id = #{id}
                    ORDER BY u.xtenant
    </select>
    <select id="getCh999_id" resultType="java.lang.Integer">
        SELECT ch999_id FROM dbo.ch999_user WITH(NOLOCK) WHERE departId=1830 AND iszaizhi = 1
    </select>
    <select id="getDepartIdList" resultType="java.lang.String">
        select id from dbo.departInfo with(nolock)
        where isdel=0 and departType=3 and id=#{areaId}
    </select>
    <select id="getCh999_user" resultType="java.lang.Integer">
        SELECT a.IDnumber,a.ch999_id,a.ch999_name,a.default_areaid,a.departId,z.name as zhiwu,a.mobile,a.areaid,a.iszaizhi,a.area1id,a.pwd,a.nickname,a.WorkKeys,a.Email,b.url,a.Roles,z.leve,a.sphoto,a.sphotoId,a.zhiji,a.Szhiji,a.islogin,a.isshixi,a.indate,a.mainRole,a.scheduleId bkind,a.birsday,a.weixinopenid,a.NamePY,a.zhiwuid,a.graduation,a.graduation1,a.zhuanye,a.zhuanye1,a.EducationType,a.Education,a.EducationType1,a.mainStation ,a.qqhao,a.zhuanzhendate,a.usersex,a.enterpriseBind,a.zhengzhimianbao,a.jobCateId  FROM dbo.ch999_user a with(nolock) left join getOfficeName().dbo.appHeadimg b with(nolock) on a.ch999_id=b.ch999_id left join zhiwu z with(nolock) on a.zhiwuid=z.id where a.iszaizhi=1 and a.ch999_id>1
        <![CDATA[ and z.leve<=3
        ]]>
    </select>
    <select id="saveTouSuProcess" resultType="java.lang.Void">
        INSERT INTO getOfficeName().dbo.tsProcess( tsID ,opUser ,dsc_ ,intime ,isShow,attachFiles,noticeUser,cate,cateContent) values
        (#{tsId},#{opUser},#{dsc},getdate(),#{show},#{attachFiles},#{noticeUser},#{cate},#{cateContent})
    </select>
    <select id="getMyWx" resultType="com.jiuji.oa.oacore.tousu.bo.Weixin">
         select openid,wxid from WeixinUser with(nolock) where userid=#{userId}  and type=1 and kinds=1 and follow =1
    </select>

    <select id="getFeedbackDetailForUser"
            resultType="com.jiuji.oa.oacore.partner.feedback.vo.res.FeedbackDetailForUserRes">
        SELECT
        t.id,
        t.memberName,
        t.mobile,
        t.inuser,
        t.processUser,
        t.content_ as content,
        t.tousupic as attachIds,
        t.addTime,
        t.huanyuantime,
        t.huanyuantimeout,
        case
        when t.states_ = 1 then '未处理'
        when t.states_ = 2 then '已受理'
        when t.states_ = 3 then '已完成'
        when t.states_ = 4 then '待整改'
        when t.states_ = 5 then '已处理'
        when t.states_ = 6 then '已整改'
        when t.states_ = 7 then '已还原'
        when t.states_ = 8 then '已界定'
        when t.states_ = 9 then '待复核'
        when t.states_ = 10 then '仲裁处理'
        when t.states_ = 11 then '客诉完结'
        else '未知'
        end AS status
        FROM
            getOfficeName().dbo.tousu t WITH(NOLOCK)
        WHERE t.id =#{id}
    </select>
    <select id="selectTousuNotifyUserIds" resultType="java.lang.String">
        select Ch999ids from WXSmsReceiver with(nolock) where Classify='69' and isnull(IsDel,0)=0
    </select>
    <select id="getTousuCnt" resultType="java.lang.Integer">
        select count(1) from getOfficeName().dbo.tousu  WITH(NOLOCK) where xtenant=#{xtenant} and userid = #{userId}
    </select>

    <update id="updateComplaintStatus">
        update getOfficeName().dbo.tousu
        set isdel = case when isnull(isdel, 0) = 0 then 1 else 0
        end
        where id = #{id}
    </update>

    <select id="getTousuAreaInfos" resultType="com.jiuji.oa.oacore.tousu.bo.TousuAreaBO">
        select tousuId as tousuId ,areaId as areaId
        from (
        select tousuId, areaId,
        RANK() OVER (PARTITION BY tousuId ORDER BY id desc) rank
        from tousuArea ta with(nolock)
        where ta.tousuId in
        <foreach collection="tsIds" open="(" close=")" separator="," item="tsId">
            #{tsId}
        </foreach>
        ) r where r.rank = 1
    </select>

    <select id="getPartnerTouSuUnFinishCount" resultType="java.lang.Integer">
        SELECT COUNT(*) from tousu t with(nolock)
        where 1 = 1
        and t.xtenant = #{xtenant}
        and isnull(t.isdel,0)=0
        and t.mobile = #{mobile}
        and t.states_ not in (3)
    </select>

    <select id="getCommentPraise" resultType="com.jiuji.oa.oacore.tousu.bo.ZeRenRen">
        select t.id as tsId,isnull(t.content_,'')  as tsContent,zr.userId as staffId,zr.userName as name
        from tousu t
        inner join tousuZeRenRen zr with(nolock) on zr.tousuId  = t.id
        where t.evaluate_id = #{evaluateId}
    </select>

    <select id="getTsCollection" resultType="com.jiuji.oa.oacore.oaorder.po.SubCollection">
        select id, sub_id, ch999_id, dtime, kind
        from dbo.subCollection with(nolock)
        where sub_id = #{tsId}
        and kind = #{type}
        <if test="staffId != null">
            and ch999_id = #{staffId}
        </if>
        ;
    </select>

    <insert id="addTsCollection">
        INSERT INTO dbo.subCollection
            (sub_id, ch999_id, dtime, kind, ch999_name)
        VALUES (#{subCollection.tsId},
                #{subCollection.ch999Id},
                #{subCollection.dtime},
                #{subCollection.kind},
                #{subCollection.ch999Name});
    </insert>

    <delete id="delTsCollection">
        DELETE FROM dbo.subCollection
        WHERE sub_id = #{subCollection.subId}
        and ch999_id = #{subCollection.ch999Id}
        and kind = #{subCollection.kind};
    </delete>
</mapper>
