<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.oacore.tousu.dao.SmsConfigMapper">

    <!-- getAppInfo -->
    <select id="getAppInfo" resultType="com.jiuji.oa.afterservice.shouhou.vo.sms.AppInfoVo">
        SELECT top 1 app.id,app.value appId,secret.value secretKey,iif(app.xtenant = #{xtenant},-100,app.xtenant) xRank
        FROM sysConfig app with(nolock)
        left join sysConfig secret with(nolock) on secret.code = 241 and secret.xtenant = app.xtenant
        where app.code = 240 AND isnull(app.isdel,0)=0 and isnull(secret.isdel,0)=0
        order by xRank ASC
    </select>

    <!-- getNcSmsconfig -->
    <select id="getNcSmsconfig" resultType="com.jiuji.oa.afterservice.shouhou.vo.sms.AppInfoVo">
        SELECT app.id, app.appid appId, app.secret_key secretKey
        FROM nc_sms_config app
        WHERE app.xtenant = #{xtenant}
    </select>


</mapper>