<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.tousu.dao.TousuTimeoutMapper">
    <select id="queryTimeoutTotal" resultType="java.lang.Long">
        select count(1) from(
            <include refid="queryMainSql"/>
        ) t1
    </select>

    <select id="queryTimeoutRecords" resultType="com.jiuji.oa.oacore.tousu.vo.res.TousuTimeoutRes"
            parameterType="com.jiuji.oa.oacore.tousu.vo.req.TousuTimeoutReq">
        SELECT
            *
        FROM
            (
                SELECT
                    row_number ( ) OVER ( ORDER BY t1.tousuTimeTmp ASC ) AS rownumber,
                    t1.*
                FROM
                    (
                    <include refid="queryMainSql"/>
                    ) t1
            ) t2
        <if test="req.current != null and req.size != null">
            where rownumber BETWEEN (#{req.current}-1) * #{req.size} +1 and #{req.current}*#{req.size};
        </if>
    </select>


    <sql id="queryMainSql">

        <!--有跟进相关的筛选项的时候，受理、还原、完结超时都不查-->
        <if test="!((req.followUserIds != null and req.followUserIds.length > 0)
                    or (req.departIds != null and req.departIds.length > 0)
                        or (req.roleIds != null and req.roleIds.length > 0))">
            <!--没有超时类型的筛选项的时候，全查；有超时类型的筛选项的时候，查对应类型超时数据-->
            <if test="req.typeList == null or (req.typeList != null and  req.typeList.contains(@com.jiuji.oa.oacore.tousu.enums.TousuTimeoutTypeEnum@HANDLE_TIMEOUT.getCode))">
                <!--当前受理超时-->
                <include refid="query_current_handle_timeout"/>
                union all
            </if>

            <if test="req.typeList == null or (req.typeList != null and  req.typeList.contains(@com.jiuji.oa.oacore.tousu.enums.TousuTimeoutTypeEnum@REDUCTION_TIMEOUT.getCode))">
                <!--当前还原超时-->
                <include refid="query_current_reduction_timeout"/>
                union all
            </if>

            <if test="req.typeList == null or (req.typeList != null and  req.typeList.contains(@com.jiuji.oa.oacore.tousu.enums.TousuTimeoutTypeEnum@FINISH_TIMEOUT.getCode))">
                <!--当前完结超时-->
                <include refid="query_current_finish_timeout"/>
                union all
            </if>
        </if>

        <if test="req.typeList == null or (req.typeList != null and  req.typeList.contains(@com.jiuji.oa.oacore.tousu.enums.TousuTimeoutTypeEnum@FOLLOW_TIMEOUT.getCode))">
        <!--当前跟进超时-->
            <include refid="query_current_follow_timeout"/>
            union all
        </if>

        <!--历史超时记录-->
        <include refid="query_history_timeout_records"/>
    </sql>

    <sql id="query_history_timeout_records">
        SELECT
            CAST(CAST((DATEDIFF( second, tt.timeout, tt.handle_time ) ) / (60*60*24) AS INT) AS VARCHAR) + '天'
                + CAST(CAST((DATEDIFF( second, tt.timeout, tt.handle_time )) % 86400 / 3600 AS INT) AS VARCHAR) + '小时'
                + CAST(CAST((DATEDIFF( second, tt.timeout, tt.handle_time )) % 3600 / 60 AS INT) AS VARCHAR) + '分' AS overTime,
            tt.timeout as timeoutTmp,
            tt.tousu_id as tousuId,
            t.addTime as tousuTimeTmp,
            t.tag,
            tt.type,
            tt.status,
            u.ch999_name as followUser,
            d.name as depart,
            r.RoleName as role
        from
            ${officeName}.dbo.tousu_timeout tt with(nolock)
            LEFT JOIN ${officeName}.dbo.tousu t with(nolock) on tt.tousu_id = t.id
            LEFT JOIN ch999_user u with(nolock) on tt.follow_user_id =u.ch999_id
            LEFT JOIN departInfo d with(nolock) on u.depart_id = d.id
            LEFT JOIN RoleInfo r with(nolock) on u.mainRole = r.id
        where
            <!--是否合作伙伴投诉-->
            <choose>
                <when test="req.partner != null and req.partner">
                    ISNULL(t.type_,0) = 17
                    and tt.partner = 1
                </when>
                <otherwise>
                    ISNULL(t.type_,0) &lt;&gt; 17
                    and tt.partner = 0
                </otherwise>
            </choose>
            <include refid="where_time"/>
            <include refid="where_follow"/>
            <include refid="where_type"/>
    </sql>

    <sql id="query_current_follow_timeout">
        SELECT
            CAST(CAST((DATEDIFF( second, tn.LastTime, getdate( ) ) ) / (60*60*24) AS INT) AS VARCHAR) + '天'
                + CAST(CAST((DATEDIFF( second, tn.LastTime, getdate( ) )) % 86400 / 3600 AS INT) AS VARCHAR) + '小时'
                + CAST(CAST((DATEDIFF( second, tn.LastTime, getdate( ) )) % 3600 / 60 AS INT) AS VARCHAR) + '分' AS overTime,
            tn.LastTime as timeoutTmp,
            t.id as tousuId,
            t.addTime as tousuTimeTmp,
            t.tag,
            3 as type,
            0 as status,
            u.ch999_name as followUser,
            d.name as depart,
            r.RoleName as role
        FROM
            ${officeName}.dbo.TousuNotice tn with(nolock)
            LEFT JOIN ${officeName}.dbo.tousu_timeout tt with(nolock) on tn.tousuID = tt.tousu_id and tn.ToUserID = tt.follow_user_id and tn.id = tt.notice_id
            LEFT JOIN ${officeName}.dbo.tousu t with(nolock) on tn.tousuID = t.id
            LEFT JOIN ch999_user u with(nolock) on tn.touserid =u.ch999_id
            LEFT JOIN departInfo d with(nolock) on u.depart_id = d.id
            LEFT JOIN RoleInfo r with(nolock) on u.mainRole = r.id
        WHERE
            tn.isNotice = 1
            <!--是否合作伙伴投诉-->
            <choose>
                <when test="req.partner != null and req.partner">
                    and ISNULL(t.type_,0) = 17
                    and tt.partner = 1
                </when>
                <otherwise>
                    and ISNULL(t.type_,0) &lt;&gt; 17
                    and tt.partner = 0
                </otherwise>
            </choose>
            and (isFinish is null or isFinish &lt;&gt; 1)
            <!--当前超过了截至时间-->
            AND DATEDIFF( MINUTE, tn.LastTime, getdate( ) ) > 0
            <!--排除已经处理了的记录-->
            and tt.id is null
            and t.id is not null
            <include refid="where_time"/>
            <include refid="where_follow"/>
    </sql>

    <sql id="query_current_finish_timeout">
        SELECT
            CAST(CAST((DATEDIFF( second, t.addTime, getdate( ) ) - 3600 * 60) / (60*60*24) AS INT) AS VARCHAR) + '天'
                + CAST(CAST((DATEDIFF( second, t.addTime, getdate( ) ) - 3600 * 60) % 86400 / 3600 AS INT) AS VARCHAR) + '小时'
                + CAST(CAST((DATEDIFF( second, t.addTime, getdate( ) ) - 3600 * 60) % 3600 / 60 AS INT) AS VARCHAR) + '分' AS overTime,
            dateadd(MINUTE, 3600, t.addTime) as timeoutTmp,
            id as tousuId,
            addTime as tousuTimeTmp,
            tag,
            4 as type,
            0 as status,
            null as followUser,
            null as depart,
            null as role
        FROM
            ${officeName}.dbo.tousu t with(nolock)
        WHERE
            <!--已完成-->
            t.states_ &lt;&gt; 3
            AND DATEDIFF( MINUTE, t.addTime, getdate( ) ) > 3600
            <!--是否合作伙伴投诉-->
            <choose>
                <when test="req.partner != null and req.partner">
                    and ISNULL(t.type_,0) = 17
                </when>
                <otherwise>
                    and ISNULL(t.type_,0) &lt;&gt; 17
                </otherwise>
            </choose>
            <include refid="where_time"/>
    </sql>

    <!--查询时时还原超时数据-->
    <sql id="query_current_reduction_timeout">
        SELECT
            CAST(CAST((DATEDIFF( second, t.addTime, getdate( ) ) - 50 * 60) / (60*60*24) AS INT) AS VARCHAR) + '天'
                + CAST(CAST((DATEDIFF( second, t.addTime, getdate( ) ) - 50 * 60) % 86400 / 3600 AS INT) AS VARCHAR) + '小时'
                + CAST(CAST((DATEDIFF( second, t.addTime, getdate( ) ) - 50 * 60) % 3600 / 60 AS INT) AS VARCHAR) + '分' AS overTime,
            dateadd(MINUTE, 50, t.addTime) as timeoutTmp,
            id as tousuId,
            addTime as tousuTimeTmp,
            tag,
            2 as type,
            0 as status,
            null as followUser,
            null as depart,
            null as role
        FROM
            ${officeName}.dbo.tousu t with(nolock)
        WHERE
            <!--已受理-->
            t.states_ in (1,2)
            <!--是否合作伙伴投诉-->
            <choose>
                <when test="req.partner != null and req.partner">
                    and ISNULL(t.type_,0) = 17
                </when>
                <otherwise>
                    and ISNULL(t.type_,0) &lt;&gt; 17
                </otherwise>
            </choose>
            AND DATEDIFF( MINUTE, t.addTime, getdate( ) ) > 50
            <include refid="where_time"/>
    </sql>

    <!--查询时时受理超时数据-->
    <sql id="query_current_handle_timeout" >
        SELECT
            CAST(CAST((DATEDIFF( second, t.addTime, getdate( ) ) - 15 * 60) / (60*60*24) AS INT) AS VARCHAR) + '天'
                + CAST(CAST((DATEDIFF( second, t.addTime, getdate( ) ) - 15 * 60) % 86400 / 3600 AS INT) AS VARCHAR) + '小时'
                + CAST(CAST((DATEDIFF( second, t.addTime, getdate( ) ) - 15 * 60) % 3600 / 60 AS INT) AS VARCHAR) + '分' AS overTime,
               <!-- + CAST(CAST((DATEDIFF( second, t.addTime, getdate( ) ) - 15 * 60) % 60 AS INT) AS VARCHAR) + '秒' -->
            dateadd(MINUTE, 15, t.addTime) as timeoutTmp,
            id as tousuId,
            addTime as tousuTimeTmp,
            tag,
            1 as type,
            0 as status,
            null as followUser,
            null as depart,
            null as role
        FROM
            ${officeName}.dbo.tousu t with(nolock)
        WHERE
            <!--未处理-->
            t.states_ = 1
            <!--是否合作伙伴投诉-->
            <choose>
                <when test="req.partner != null and req.partner">
                    and ISNULL(t.type_,0) = 17
                </when>
                <otherwise>
                    and ISNULL(t.type_,0) &lt;&gt; 17
                </otherwise>
            </choose>
            -- 投诉产生15分钟之后受理，为受理超时
            <!--未处理-->
            AND DATEDIFF( MINUTE, t.addTime, getdate( ) ) > 15
            <include refid="where_time"/>
    </sql>

    <!--投诉时间-->
    <sql id="where_time">
        <if test="req.startTime != null">
           and t.addTime &gt;= #{req.startTime}
        </if>
        <if test="req.endTime != null">
            and t.addTime &lt;= #{req.endTime}
        </if>
    </sql>

    <sql id="where_follow">
        <!--跟进人-->
        <if test="req.followUserIds != null and req.followUserIds.length > 0">
            and tt.follow_user_id in
            <foreach collection="req.followUserIds" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!--跟进人所属部门-->
        <if test="req.departIds != null and req.departIds.length > 0">
            and d.id in
            <foreach collection="req.departIds" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!--跟进人主要角色-->
        <if test="req.roleIds != null and req.roleIds.length > 0">
            and r.id in
            <foreach collection="req.roleIds" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="where_type">
        <if test="req.type != null and req.type.length > 0">
            and tt.type in
            <foreach collection="req.type" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>


    <select id="queryPendList" resultType="com.jiuji.oa.oacore.tousu.bo.PendBO">
        SELECT
            n.id as noticeId,
            n.ToUserID AS userId,
            n.TousuID AS tousuId,
            n.LastTime AS lastTime,
            t.type_ as type
        FROM
            ${officeName}.dbo.TousuNotice n with(nolock)
            LEFT JOIN ${officeName}.dbo.tousu t with(nolock) on n.TousuID = t.id
        WHERE
            n.NoticeType = 1
            and (n.isFinish is null or n.isFinish &lt;&gt; 1)
            AND n.ToUserID = #{userId}
            <!--去除完成状态的投诉-->
            and t.states_ &lt;&gt; 3
            <!--未处理历史数据，所以限制时间-->
            and n.create_time >= '2022-04-21 00:00:00'
    </select>

    <select id="queryPendIsFinish" resultType="java.lang.Boolean">
        SELECT
            isFinish
        FROM
            ${officeName}.dbo.TousuNotice with(nolock)
        WHERE
            ToUserID = #{userId}
            and id =#{noticeId}
    </select>
</mapper>
