<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.tousu.dao.TouSuDepartMapper">

    <!-- 通用查询映射结果 -->

    <!--    <insert id="addTouSuDepart" useGeneratedKeys="true" keyProperty="id"  keyColumn="id" >-->
    <!--        INSERT INTO tousuDepart (departArea, departCode,tousuId,createTime,createUser,-->
    <!--        scoreArea,scoreDep,areaId,areaName,departAreaName,department,departmentCentCode,departmentCentName,-->
    <!--        type,cat,kinds,touSuRank,tousuTypes)-->
    <!--        VALUES (#{departArea}, #{departCode}, #{tousuId}, GETDATE(),-->
    <!--        #{createUser}, #{scoreArea}, #{scoreDep}, #{areaId}, #{areaName},-->
    <!--        #{departAreaName}, #{department}, #{departmentCentCode},-->
    <!--        #{departmentCentName}, #{type}, #{cat}, #{kinds},-->
    <!--         #{touSuRank}, #{tousuTypes});-->
    <!--    </insert>-->

    <update id="setTouSuDepart">
        update getOfficeName().dbo.tousuDepart
        set departArea= #{departArea},
            departcode = #{departCode},
            createTime=GETDATE(),
            createUser = #{createUser},
            scoreArea= #{scoreArea},
            scoreDep= #{scoreDep},
            areaId = #{areaId},
            areaName = #{areaName},
            cat=#{cat},
            kinds = #{kinds},
            touSuRank=#{touSuRank},
            tousuTypes =#{tousuTypes}
        where id = #{id}
    </update>


    <select id="getTouSuProcessBO" resultType="com.jiuji.oa.oacore.tousu.bo.TouSuProcessBO">
        select id,
               tsID,
               opUser,
               dsc_ as dsc,
               intime,
               isShow,
               attachFiles,
               noticeUser,
               cate,
               isxinsheng
        from getOfficeName().dbo.tsProcess WITH(NOLOCK)
        where tsId = #{id}
    </select>
    <select id="getMoneyById" resultType="com.jiuji.oa.oacore.tousu.vo.res.TouSuEndInfoRes">
        select BonusMoney, FineMoney
        from getOfficeName().dbo.tousu WITH(NOLOCK)
        where id = #{id}
    </select>
    <select id="getTouSuDepartByTsId" resultType="com.jiuji.oa.oacore.tousu.vo.res.TouSuDepartRes">
        SELECT id,
               departArea,
               depart_id,
               tousuId,
               createTime,
               createUser,
               scoreArea,
               scoreDep,
               areaId,
               type,
               tousuTypes,
               cat,
               kinds,
               touSuRank,
               tousuPoint
        FROM getOfficeName().dbo.tousuDepart WITH(NOLOCK)
        where tousuId = #{id}
    </select>
    <select id="getTouSUCategoryRelation" resultType="com.jiuji.oa.oacore.tousu.bo.TousuCategoryRelationBO">
        SELECT r.cateId,c.name,r.departId FROM getOfficeName().dbo.TousuCategoryRelation r WITH(NOLOCK)
        INNER JOIN getOfficeName().dbo.TousuCategory c WITH(NOLOCK) ON c.id = r.cateId
        WHERE r.tousuId = #{tsId}
        <if test="departIds!=null and departIds.size >0">
            and r.departId in
            <foreach collection="departIds" index="index" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getTouSUCategoryRelationByTsIds" resultType="com.jiuji.oa.oacore.tousu.bo.TousuCategoryRelationBO">
        SELECT r.tousuId,r.departId,r.cateId,c.name FROM getOfficeName().dbo.TousuCategoryRelation r WITH(NOLOCK)
        INNER JOIN getOfficeName().dbo.TousuCategory c WITH(NOLOCK) ON c.id = r.cateId
        WHERE r.tousuId in
            <foreach collection="list" index="index" item="tousuId" separator="," open="(" close=")">
                #{tousuId}
            </foreach>
    </select>
    <select id="getTouSuCategoryByIds" resultType="com.jiuji.oa.oacore.tousu.po.TousuCategory">
        select id, name, parentId, disOrder, createTime, createUser, lastUpdateTime, lastUpdateUser, tagId
        from getOfficeName().dbo.TousuCategory WITH(NOLOCK) where id in
        <foreach collection="ids" index="id" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
</mapper>
