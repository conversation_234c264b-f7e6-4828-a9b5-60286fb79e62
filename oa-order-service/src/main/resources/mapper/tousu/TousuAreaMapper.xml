<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.tousu.dao.TousuAreaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.tousu.po.TousuArea">
        <id column="id" property="id"/>
        <result column="tousuId" property="tousuId"/>
        <result column="areaId" property="areaId"/>
        <result column="createTime" property="createTime"/>
        <result column="createUser" property="createUser"/>
    </resultMap>

    <insert id="addNoticeTousu" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO TousuNotice (TousuID,ToUserName,ToUserID,NoticeType,NoticeContent,LastTime,isNotice) values  (#{notice.tousuID},#{notice.toUserName},#{notice.toUserId},#{notice.noticeType},
        #{notice.noticeContent},#{notice.lastTime},#{notice.isNotice})
    </insert>
    <insert id="addNewTouSu" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO dbo.tousu( userid ,email ,mobile ,content_ ,writer ,addTime ,writeIp ,type_ ,states_ ,userName ,AreaId,
        areaName,inuser,memberName,cat,types,kinds,tousuTypes,huanyuantimeout,dealtimeout)  VALUES
        (#{userid},#{email},#{mobile},#{content},#{writer},getdate(),#{writeIp},#{type},#{states},#{userName},
        #{AreaId},#{areaName},#{inuser},#{memberName},#{cat},#{types},#{kinds},#{touSuTypeIds},#{huanyuantimeout},#{dealTimeout})
    </insert>

    <insert id="insertTousuStatus" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO TousuProcessTime(tousuid,processTime)  VALUES(#{tousuId},#{processTime})
    </insert>
    <insert id="saveTouSuZenRenRen">
        INSERT INTO  dbo.tousuZeRenRen ( tousuId ,userId ,userName ,tousuRank,tousuPoint)
        VALUES (#{zeRenRen.tousuId}, #{zeRenRen.userId} ,#{zeRenRen.userName}, #{zeRenRen.tousuRank},#{zeRenRen.tousuPoint})
    </insert>

    <insert id="saveTouSuZenRenRenV2">
        INSERT INTO  dbo.tousuZeRenRen ( tousuId ,userId ,userName ,tousuRank,tousuPoint, tousu_lose_point, bonus_points)
        VALUES (#{zeRenRen.tousuId}, #{zeRenRen.userId} ,#{zeRenRen.userName}, #{zeRenRen.tousuRank},#{zeRenRen.tousuPoint},#{zeRenRen.tousuLosePoint}, #{zeRenRen.bonusPoints})
    </insert>

    <insert id="saveTousuAreaByTouSuId">
        INSERT INTO getOfficeName().dbo.tousuArea(tousuId,areaId,createTime,createUser,departId) VALUES(#{tousuId},#{areaId},GETDATE(),#{userName},#{departId})
    </insert>

    <insert id="saveTouSuCategory">
        INSERT INTO TousuCategory(name,parentId,disOrder,createTime,createUser,lastUpdateTime,lastUpdateUser)
        VALUES(#{name},#{parentId},#{disOrder},GETDATE(),#{userName},GETDATE(),#{userName})
    </insert>
    <update id="updateCustomerEndTime">
         update getOfficeName().dbo.tousu set CustomerEndTime=GETDATE(),CustomerEndUser=#{userName} where
         id = #{tousuId} and CustomerEndTime IS NULL
    </update>
    <update id="setTousuEndAndinvalid">
        update tousu set cat = 4,states_=3 where id = #{id}
    </update>

    <update id="updateTousuStatus">
        update tousu set states_ = #{status}
        <if test="status!=null and status != 0 and status == 3 ">
            ,finishTime=getdate()
        </if>
        <choose>
            <when test="status!=null and status != 0 and status == 5">
                ,dealTime = getdate()
            </when>
            <when test="status!=null and status != 0 and status == 7">
                ,huanyuantime=getdate()
            </when>
        </choose>
        where id=#{tousuId}
    </update>
    <update id="updateTousuProcessTime">
        UPDATE getOfficeName().dbo.TousuProcessTime SET
        <if test="status!=null and status != 0 and status == 2">
            sltime=GETDATE(), sluser=#{user}
        </if>
        <if test="status!=null and status != 0 and status == 7">
            hytime=GETDATE(), hyuser=#{user}
        </if>
        <if test="status!=null and status != 0 and status == 5">
            cltime=GETDATE(), cluser=#{user}
        </if>
        <if test="status!=null and status != 0 and status == 8">
            jdtime=GETDATE(), jduser=#{user}
        </if>
        <if test="status!=null and status != 0 and status == 9">
            fhtime=GETDATE(), fhuser=#{user}
        </if>
        <if test="status!=null and status != 0 and status == 4">
            zgtime=GETDATE(), zguser=#{user}
        </if>
        <if test="status!=null and status != 0 and status == 10">
            coozgtime=GETDATE(), coozguser=#{user}
        </if>
        <if test="status!=null and status != 0 and status == 6">
            yzgtime=GETDATE(), yzguser=#{user}
        </if>
        <if test="status!=null and status != 0 and status == 3">
            wctime=GETDATE(), wcguser=#{user}
        </if>
        WHERE id=#{tousuId}
    </update>
    <update id="insertCh99Jifen">
        INSERT INTO dbo.ch999_fen(ch999_id,yanyin,fen,inuser,fendate,jifenType,bumen_id)
        VALUES (#{param.ch999Id},#{param.yanyin},#{param.fen},#{param.inuser},GETDATE(),#{param.jifenType},#{param.bumenId})
    </update>

    <select id="queryCh999FenByUserIdAndTouSuId" resultType="int">
        SELECT
            count(1)
        FROM
            ch999_fen with(nolock)
        WHERE
            <!--投诉扣分-->
            jifenType = 3
            AND bumen_id = #{touSuId}
            AND ch999_id = #{userId}
    </select>

    <update id="setTousuAreaByTouSuId">
        UPDATE getOfficeName().dbo.tousuArea  set areaId = #{areaId},createTime = GETDATE(),createUser = #{userName}  where tousuId=#{tousuId}  and departId = #{departId}
    </update>

    <update id="updateStatusById">
        update    getOfficeName().dbo.tousuNotice set isNotice=1,isFinish=1   where tousuid = #{tousuId} and ToUserID = #{userId} and id = #{id};
    </update>
    <update id="updateNoticeTousuById">
         update    getOfficeName().dbo.tousuNotice set lastTime=#{time}  where TousuID = #{tsId} and ToUserID = #{userId};
    </update>

    <update id="updateTouSuCategory">
        UPDATE dbo.TousuCategory SET name = #{param.name}, parentId = #{param.parentId},
        isdel = #{param.isdel}, disOrder = #{param.disOrder}, lastUpdateTime = GETDATE(), lastUpdateUser = #{param.userName} WHERE id = #{param.id}
    </update>
    <update id="updateZeRenRenTousuPoints">
        UPDATE getOfficeName().dbo.tousuZeRenRen
        SET tousuPoint = #{zeRenRen.tousuPoint}, tousu_lose_point = #{zeRenRen.tousuLosePoint}, bonus_points = #{zeRenRen.bonusPoints}
        WHERE tousuId=#{zeRenRen.tousuId} AND userId = #{zeRenRen.userId} AND id = #{zeRenRen.id}
    </update>
    <delete id="updateZeRenRen">
        update    getOfficeName().dbo.tousuZeRenRen set  tousuRank= #{zeRenRen.tousuRank},tousuPoint = #{zeRenRen.tousuPoint} WHERE tousuId=#{zeRenRen.tousuId} and userId = #{zeRenRen.userId} and id =  #{zeRenRen.id}
    </delete>

    <delete id="deleteTousuAreaByTouSuId">
         DELETE from  getOfficeName().dbo.tousuArea where tousuId = #{tousuId} and areaId = #{areaId}
    </delete>
    <delete id="deleteTousuCategoryRelation">
        DELETE FROM TousuCategoryRelation WHERE tousuId = #{tousuId} and cateId = #{cateId}
        <if test="departId != null and departId != 0">
            and departId = #{departId}
        </if>
    </delete>
    <delete id="deleteZeRenRen">
         DELETE FROM getOfficeName().dbo.tousuZeRenRen where  tousuId = #{param.tousuId} and userId = #{param.userId} and id = #{param.id}
    </delete>
    <select id="selectByTouSuId" resultType="com.jiuji.oa.oacore.tousu.po.TousuArea">
        SELECT id,tousuId,areaId,createUser,createTime FROM dbo.tousuArea WITH(NOLOCK) WHERE tousuId = #{id}
    </select>

    <select id="getAllTouSuTags" resultType="com.jiuji.oa.oacore.tousu.po.TouSuTag">
        select Id,TagName,TouSuTypeId,TouSuType,isdel from dbo.TouSuTag with(nolock) WHERE ISNULL(isdel,0)=0
    </select>
    <select id="getTsIdsByDsc" resultType="java.lang.Integer">
        SELECT DISTINCT tsID FROM getOfficeName().dbo.tsProcess with(nolock) WHERE dsc_ LIKE CONCAT('%',#{processContent},'%')
    </select>
    <select id="getTousuIdByUserId" resultType="java.lang.Integer">
        SELECT DISTINCT tousuId FROM getOfficeName().dbo.tousuZeRenRen with(nolock) WHERE userId=#{zeRenRenId}
    </select>
    <select id="getTousuIdByUserName" resultType="java.lang.Integer">
          SELECT DISTINCT tousuId FROM getOfficeName().dbo.tousuZeRenRen with(nolock) WHERE username LIKE CONCAT('%',#{zeRenRenName},'%')
    </select>

    <select id="touSuResList" resultType="com.jiuji.oa.oacore.tousu.vo.res.TouSuModelRes">
        <if test="param.selfFlag == 1">
            <include refid="self_tousu"/>
        </if>

        SELECT DISTINCT(t.id) as tsId,t.AreaId as areaId, t.userid as userId, t.tag as tag, t.addTime,t.finishTime,t.processUser,users.userclass as
        userClass,users.xtenant as xtenant,(select count(1) from ${officeName}.dbo.tousu  WITH(nolock) where id = t.id
        and ((getdate() > t.dealTimeout and t.states_ &lt;&gt; 3) or (t.states_ = 3 and t.finishTime > t.dealTimeout))) as timeOut,
        ( SELECT COUNT (id)
        FROM ${officeName}.dbo.tousu WITH(nolock)
        WHERE userid = t.userid
            AND userid IS NOT NULL
            AND tag in (1, 2)
            AND userid &lt;&gt; 0) AS Tcount,
        dealTimeout, t.content_ AS content,t.states_ AS states,t.bonusMoney,t.fineMoney,t.tag, t.deal_user as dealUserIds, t.type_ as tsType
        FROM ${officeName}.dbo.tousu t WITH(nolock)
        <if test="param.selfFlag == 1">
           inner join #self_tousu st on st.id = t.id
        </if>
        LEFT JOIN ${officeName}.dbo.tousuDepart tt WITH(nolock) ON t.id = tt.tousuid
        LEFT JOIN ${officeName}.dbo.tsProcess ts WITH(nolock) ON t.id = ts.tsId
        LEFT JOIN ${officeName}.dbo.TousuCategoryRelation tr WITH(nolock) ON tr.departId = tt.id
        LEFT JOIN dbo.BBSXP_Users users WITH(nolock) ON t.userid = users.id
        WHERE
        isnull(t.source,0)=0
        <include refid="tousuBasicWhere"/>
        ORDER BY addtime desc
        OFFSET #{startIndex} ROWS FETCH NEXT #{pageSize} ROWS ONLY
    </select>
    <sql id="tousuBasicWhere">
        <!--是否超五星好评 -->
        <if test="param.overFiveFlag != null">
            <choose>
                <when test="param.overFiveFlag == 0">
                    and t.evaluate_id is null
                </when>
                <when test="param.overFiveFlag == 1">
                    and t.evaluate_id is not null
                </when>
            </choose>
        </if>
        <!--投诉客评跟进时效统计 字段明细 查询条件中不需要下面的条件-->
        <if test="param.searchFieldType == null or (param.searchFieldType != 1 and
        param.searchFieldType != 2 and param.searchFieldType != 3 and param.searchFieldType != 4)">
            and t.type_ &lt;&gt; 5
        </if>
        <!--投诉扣分查询-->
        <if test="param.onlyDeduct != null">
            <!--扣分-->
            <if test="param.onlyDeduct == 1">
                and (exists(select 1 from ${officeName}.dbo.tousuZeRenRen with(nolock) where tousuId = t.id and isnull(tousuPoint, 0) > 0) or
                exists(select 1 from ${officeName}.dbo.tousuDepart with(nolock) where tousuId = t.id and isnull(scoreArea, 0) > 0))
            </if>
        </if>
        <if test="param != null">
            <if test="param.optimize!= null and param.optimize != 0 and param.optimize ==1">
                and ts.cate = 3
            </if>
            <if test="param.userClass != null">
                and users.userclass = #{param.userClass}
            </if>
            <if test="param.xtenant != null">
                and users.xtenant = #{param.xtenant}
            </if>
            <if test="param.tag != null">
                and t.tag = #{param.tag}
            </if>
            <if test="param.tags != null and param.tags.size() > 0">
                and t.tag IN
                <foreach collection="param.tags" index="index" item="tag" open="(" close=")" separator=",">
                    #{tag}
                </foreach>
            </if>
            <if test="param.cat!= null and param.cat != 0">
                and tt.cat = #{param.cat}
            </if>
            <if test="param.catList!= null and param.catList.size() > 0">
                and tt.cat IN
                <foreach collection="param.catList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="param.kinds!= null and param.kinds != 0">
                and tt.kinds = #{param.kinds}
            </if>
            <if test="param.kindList!= null and param.kindList.size() > 0">
                and tt.kinds IN
                <foreach collection="param.kindList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="param.touSuRank!= null and param.touSuRank != 0">
                and tt.touSuRank = #{param.touSuRank}
            </if>
            <!--因为一条投诉可以对应多个责任归属部门，组合门店和部门筛选的时候晒不出来-->
            <if test="param.depart != null and param.depart.size >0">
                and EXISTS ( SELECT 1 FROM getOfficeName().dbo.tousuDepart with(nolock) where tousuid = t.id and type = 1 and depart_id in
                <foreach collection="param.depart" index="index" item="code" open="(" close=")" separator=",">
                    #{code}
                </foreach>
                )
            </if>
            <if test="param.AreaIds != null and param.AreaIds.size > 0">
                and tt.type = 0 and tt.areaId in
                <foreach collection="param.AreaIds" index="index" item="areaId" open="(" close=")" separator=",">
                    #{areaId}
                </foreach>
            </if>
            <if test="param.searchState != null and param.searchState != 0">
                <!--
                    <choose>
                        <when test="param.searchState == 2">
                            and t.states_ IN (2,4,5,6,7,8,9)
                    </when>
                    <otherwise>
                        and t.states_=#{param.searchState}
                    </otherwise>
                </choose>-->
                and t.states_ = #{param.searchState}
            </if>
            <if test="param.stateList != null and param.stateList.size() > 0">
                and t.states_ IN
                <foreach collection="param.stateList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="param.bonus != null and  param.bonus !=0 and param.bonus == 1">
                and isnull(t.bonusMoney,0) &lt;&gt; 0
            </if>
            <if test="param.fine != null and param.fine !=0  and param.fine == 1">
                and isnull(t.fineMoney,0) &lt;&gt; 0
            </if>
            <if test="param.overTime != null and  param.overTime !=0 and param.overTime == 1">
                and ((getdate() > t.dealTimeout and t.states_ &lt;&gt; 3) or (t.states_ = 3 and t.finishTime > t.dealTimeout))
            </if>
            <if test="param.inProgress != null and  param.inProgress !=0 and param.inProgress == 1">
                and t.states_ not in(1, 3)
            </if>
            <if test="param.customer != null">
                <choose>
                    <when test="param.customer == 1">
                        and t.CustomerEndTime IS NOT NULL
                    </when>
                    <when test="param.customer == 2">
                        and t.CustomerEndTime IS NULL
                    </when>
                </choose>
            </if>
            <!--高级-->
            <if test="param.dateType != null ">
                <choose>
                    <when test="param.dateType == 1">
                        and addTime between #{param.startTime} and #{param.endTime}
                    </when>
                    <when test="param.dateType == 2">
                        and finishTime between #{param.startTime} and #{param.endTime}
                    </when>
                </choose>
            </if>

            <if test="param.key != null and param.searchKind != null and param.searchKind != 0">
                <choose>
                    <when test="param.searchKind ==1">
                        and t.writer like CONCAT('%',#{param.key},'%')
                    </when>
                    <when test="param.searchKind ==2">
                        and t.userName like CONCAT('%',#{param.key},'%')
                    </when>
                    <when test="param.searchKind == 11 ">
                        and t.mobile like CONCAT('%',#{param.key},'%')
                    </when>
                    <when test="param.searchKind ==4">
                        and t.areaName = #{param.key}
                    </when>
                    <when test="param.searchKind ==5">
                        and t.content_ like CONCAT('%',#{param.key},'%')
                    </when>
                    <when test="param.searchKind ==7">
                        and t.userid =#{param.key}
                    </when>
                    <when test="param.searchKind ==8 and param.idsStr != null and param.idsStr != ''">
                        and t.id in (${param.idsStr})
                    </when>
                    <when test="param.searchKind ==6 or param.searchKind ==9 ">
                        <if test="param.ids != null and param.ids.size >0">
                            and t.id in
                            <foreach collection="param.ids" index="index" item="id" open="(" close=")" separator=",">
                                #{id}
                            </foreach>
                        </if>
                    </when>
                    <when test="param.searchKind ==10">
                        and t.processUser like CONCAT('%',#{param.key},'%')
                    </when>
                    <when test="param.searchKind ==12">
                        and EXISTS (SELECT 1 FROM dbo.BBSXP_Users u WITH(nolock) WHERE u.ID =userid AND u.mobile LIKE
                        CONCAT('%',#{param.key},'%'))
                    </when>
                    <when test="param.searchKind ==13">
                        and t.inuser like CONCAT('%',#{param.key},'%')
                    </when>

                </choose>
            </if>
            <if test="param.typical != null  and param.typical !=0 and param.typical == 1">
                and isnull(t.ArchiveCategory,0)=1
            </if>
            <if test="param.isXinSheng != null and  param.isXinSheng !=0 and param.isXinSheng == 1">
                and isnull(t.isxinsheng,0)=1
            </if>
            <if test="param.touSuTypeVal != null and param.touSuTypeVal.size > 0">
                and tr.cateid in
                <foreach collection="param.touSuTypeVal" index="index" separator="," open="(" close=")" item="cateId">
                    #{cateId}
                </foreach>
            </if>
            <if test="param.catId != null">
                and tr.cateid = #{param.catId}
            </if>
            <!--投诉客评跟进时效统计 字段明细 查询条件-->
            <if test="param.searchFieldType != null">
                <if test="param.searchFieldType == 1">
                    and t.processUser = #{param.key}
                </if>
                <if test="param.searchFieldType == 2">
                    and t.processUser = #{param.key}
                    and t.huanyuantime is not null
                </if>
                <if test="param.searchFieldType == 3">
                    and t.processUser = #{param.key}
                    and t.huanyuantimeout is not null
                    and isnull(t.huanyuantime,t.finishTime) > t.huanyuantimeout
                </if>
                <if test="param.searchFieldType == 4">
                    and t.processUser = #{param.key}
                    and t.DealTimeout is not null
                    and isnull(t.dealTime,t.finishTime) > t.DealTimeout
                </if>
            </if>
            <if test="!param.containDel">
                and (t.isdel is null or t.isdel =0)
            </if>
            <!--发放方式-->
            <if test="param.types != null and param.types.size() != 0">
                and EXISTS (
                SELECT 1
                FROM ${officeName}.dbo.complain_push_relation with(nolock)
                where complain_id = t.id
                and type in
                <foreach collection="param.types" index="index" item="type" open="(" close=")" separator=",">
                    #{type}
                </foreach>
                )
            </if>
            <!-- 是否在网站展示 -->
            <if test="param.showWeb != null">
                <choose>
                    <when test="param.showWeb == true">
                        AND ISNULL(t.show_web, 0) = 1
                    </when>
                    <otherwise>
                        AND ISNULL(t.show_web, 0) = 0
                    </otherwise>
                </choose>
            </if>
            <!-- 投诉来源 -->
            <if test="param.tsType != null and param.tsType.size() > 0">
                AND t.type_ in
                <foreach collection="param.tsType" separator="," item="type" open="(" close=")">
                    #{type}
                </foreach>
            </if>
            <if test="param.highRankFlag != 1">
                AND t.type_ not in (8,9)
            </if>
            <if test="param.followFlag != null and param.followFlag == true">
                AND exists (
                    select 1
                    from dbo.subCollection sc with(nolock)
                    where sc.ch999_id = #{param.currentStaffId} and sc.kind = 4
                    and sc.sub_id = t.id
                )
            </if>
        </if>
    </sql>


    <select id="getAreaIdsCount" resultType="com.jiuji.oa.oacore.tousu.po.TousuArea">
        SELECT t.AreaId,t.tousuId FROM tousuArea t WITH(nolock)
        LEFT JOIN tousu WITH(nolock) on tousu.id = t.tousuId
        WHERE ISNULL(tousu.isdel,0) = 0
        AND isnull(tousu.tag, 0) IN (1, 2)
        AND DATEDIFF(MONTH,tousu.addTime,GETDATE()) = 0 AND t.AreaId in
        <foreach collection="areaids" index="index" item="areaid" open="(" close=")" separator=",">
            #{areaid}
        </foreach>
    </select>

    <select id="getAreaIdsCountByDate" resultType="com.jiuji.oa.oacore.tousu.po.TousuArea">
        SELECT distinct t.AreaId,t.tousuId FROM tousuArea t WITH(nolock)
        LEFT JOIN tousu WITH(nolock) on tousu.id = t.tousuId
        WHERE ISNULL(tousu.isdel,0) = 0
        AND isnull(tousu.tag, 0) IN (1, 2)
        AND DATEDIFF(day,tousu.addTime,GETDATE()) &lt; #{dateNum} AND t.AreaId in
        <foreach collection="areaids" index="index" item="areaid" open="(" close=")" separator=",">
            #{areaid}
        </foreach>
    </select>
    <select id="getTsProcess" resultType="com.jiuji.oa.oacore.tousu.po.TsProcess">
        select id, tsID, opUser, dsc_ , intime, isShow, attachFiles, noticeUser, cate, isxinsheng,fake_log
        from tsProcess WITH(nolock) where isShow = 1 and tsID in
        <foreach collection="ids" index="index" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="getTouSuById" resultType="com.jiuji.oa.oacore.tousu.po.TouSuModel">
        select id, userid,isnull(isdel,0) as isdel, email, mobile,isnull(cat,0) as cat,tousuRank as touSuRank , (select  count(1) from getOfficeName().dbo.tousu  WITH(nolock) where  getdate() > t.dealTimeout and t.states_ &lt;&gt; 3 and id = t.id) as timeOut,
        tag,customerEndTime,isxinsheng,archiveCategory,processUser,content_ as content, writer, (Select CONVERT(varchar(100), addTime, 20)) as addTime, writeIp, type_ as type,
        states_ as states, kinds, userName, areaId, areaName,departArea,tousuTags as touSuTagIds,isnull(tousuTypes,'''') as touSuTypeIds,inuser,memberName,DealTimeout,
        departcode,bonusMoney,fineMoney,zhenggaiTime,zhenggaiPingshenTime,(SELECT COUNT(id) FROM getOfficeName().dbo.tousu with(nolock) WHERE userid=t.userid AND userid IS NOT NULL AND userid &lt;&gt; 0 AND ISNULL(tag, 0) IN (1,2)) AS Tcount,
         huanyuantime,huanyuantimeout,tousupic as AttachIds,isnull(scoreDep,0) as scoreDep,isnull(scoreArea,0) as scoreArea,supplier,source,xtenant_name as xtenantName,xtenant,show_web as showWeb,deal_user as deal_user
         from getOfficeName().dbo.tousu t WITH(nolock)
        where id =#{id}

    </select>
    <select id="getTsProcessByTsId" resultType="com.jiuji.oa.oacore.tousu.po.TsProcess">
        select tp.id,
               tp.tsID,
               tp.opUser,
               tp.dsc_ as dsc,
               tp.intime,
               tp.cate,
               tp.attachFiles,
               tp.isShow,
               tp.isxinsheng,
               tp.fake_log,
               tp.record_id as processId,
               tp.show_web as showWeb,
               cpr.id as recordId,
               cpr.is_check as checkFlag,
               cpr.money,
               tp.show_web as showWeb
        from getOfficeName().dbo.tsProcess tp WITH (nolock)
            left join getOfficeName().dbo.complain_push_relation cpr WITH (nolock) on cpr.process_id = tp.record_id and tp.cate = 6 and cpr.type = 2
        where tp.tsID = #{id}
        ORDER BY tp.intime
    </select>
    <select id="getTousuZeRenRen" resultType="com.jiuji.oa.oacore.tousu.po.TouSuZenRenRen">
        SELECT id,userName, tousuRank,tousuPoint,tousu_lose_point AS tousuLosePoint,userId,area1id,departCode,
               bonus_points bonusPoints
        FROM getOfficeName().dbo.tousuZeRenRen WITH(nolock)  WHERE tousuId=#{id}
    </select>
    <select id="getTousuZeRenRens" resultType="com.jiuji.oa.oacore.tousu.po.TouSuZenRenRen">
        SELECT id,userName, tousuRank,tousuPoint,tousu_lose_point AS tousuLosePoint,userId,area1id,departCode,tousuId
        FROM getOfficeName().dbo.tousuZeRenRen WITH(nolock)
        WHERE tousuId in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getTousuAreaByTouSuId" resultType="com.jiuji.oa.oacore.tousu.po.TousuArea">
        SELECT areaId,createUser FROM dbo.tousuArea WITH(NOLOCK) WHERE tousuId =#{id}
    </select>
    <select id="getTouSUCategoryRelation" resultType="com.jiuji.oa.oacore.tousu.bo.TousuCategoryRelationBO">
        SELECT r.cateId,c.name FROM dbo.TousuCategoryRelation r WITH(NOLOCK)
                INNER JOIN dbo.TousuCategory c WITH(NOLOCK) ON c.id = r.cateId
                WHERE r.tousuId = #{id}
    </select>
    <select id="getTousuZeRenRenByUserIds" resultType="com.jiuji.oa.oacore.tousu.po.TouSuZenRenRen">
        SELECT z.userId,t.id FROM tousuZeRenRen z WITH(NOLOCK)
        INNER JOIN tousu t WITH(NOLOCK) ON z.tousuId = t.id
        WHERE ISNULL(t.isdel, 0) = 0 AND t.addTime > '2018-11-12' AND DATEDIFF(DAY, t.addTime, GETDATE()) &lt; 180 AND
        z.userId IN
        <foreach collection="ids" index="index" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
    <select id="getTousuPunish" resultType="java.lang.Integer">
        select distinct sub_id  from dbo.punishSub s WITH(NOLOCK)  where s.tousuid like CONCAT('%',#{id},'%')
    </select>
    <select id="getAllTousuCategory" resultType="com.jiuji.oa.oacore.tousu.po.TousuCategory">
         select id, name,  parentId, isdel,  disOrder, createTime, createUser,   lastUpdateTime,   lastUpdateUser,   tagId
            from getOfficeName().dbo.TousuCategory WITH(NOLOCK) where isdel = 0
        <if test="kind != 0">
            and (kind=#{kind} or parentId=0)
        </if>
    </select>

    <!-- listAllTousuCategory -->
    <select id="listAllTousuCategory" resultType="com.jiuji.oa.oacore.tousu.po.TousuCategory">
        SELECT id, name,  parentId, isdel,  disOrder, createTime, createUser,   lastUpdateTime,   lastUpdateUser,   tagId
        FROM getOfficeName().dbo.TousuCategory WITH(NOLOCK)
        <where>
            isdel = 0
            <if test="kindList != null and kindList.size() >0">
                AND (kind IN
                <foreach collection="kindList" index="index" item="kind" separator="," open="(" close=")">
                    #{kind}
                </foreach>
                OR parentId = 0
                )
            </if>
        </where>
    </select>

    <select id="getTousuCategoryByIds" resultType="com.jiuji.oa.oacore.tousu.po.TousuCategory">
         select id, name,  parentId, isdel,  disOrder, createTime, createUser,   lastUpdateTime,   lastUpdateUser,   tagId
            from getOfficeName().dbo.TousuCategory WITH(NOLOCK) where isdel = 0 and id in
            <foreach collection="ids" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
    </select>

    <select id="getAllTousuCategoryWithNoKind" resultType="com.jiuji.oa.oacore.tousu.po.TousuCategory">
        select id, name,  parentId, isdel,  disOrder, createTime, createUser,   lastUpdateTime,   lastUpdateUser,   tagId
        from getOfficeName().dbo.TousuCategory WITH(NOLOCK) where isdel = 0
    </select>

    <select id="getTouSuNoticeByTsId" resultType="com.jiuji.oa.oacore.tousu.po.TousuNotice">
        SELECT ID, TousuID, ToUserName, ToUserID, NoticeType, NoticeContent, Create_time, LastTime
         FROM getOfficeName().dbo.TousuNotice  WITH(NOLOCK) WHERE TousuID=#{id}
         <if test="userId != null">
             and touserId = #{userId}
         </if>
          and noticeType = 1
    </select>
    <select id="getTousuProcessTimeByTsId" resultType="java.lang.Integer">
        SELECT ID FROM getOfficeName().dbo.TousuProcessTime  WITH(NOLOCK) WHERE tousuid=#{tousuId};
    </select>

    <select id="getCh999IdByMainRole" resultType="java.lang.String">
        SELECT   ch999_id  FROM  dbo.ch999_user  WITH(NOLOCK)  WHERE  mainRole=#{roleId}
    </select>

    <select id="getXtenant" resultType="java.lang.Integer">
        SELECT xtenant FROM dbo.BBSXP_Users  WITH(NOLOCK) WHERE id=#{userid}
    </select>
    <select id="getTouSuPicByIds" resultType="com.jiuji.oa.oacore.tousu.res.AttachmentsRes">
        SELECT id,linkedID,type,filename,filepath,kind,fid,Extension,frame_path as framePath
         FROM dbo.attachments WITH(NOLOCK) WHERE EXISTS(SELECT * FROM dbo.F_SPLIT(isnull(#{attIds},''),',') WHERE id IN (split_value))
    </select>
    <select id="getAreaIdCount" resultType="com.jiuji.oa.oacore.tousu.po.TouSuModel">
        SELECT t.areaId,t.id FROM tousu t WITH(nolock) WHERE ISNULL(t.isdel,0) = 0 AND isnull(t.tag, 0) IN (1,2) AND
        DATEDIFF(MONTH,t.addTime,GETDATE()) = 0 AND t.AreaId = #{areaid}
    </select>
    <select id="getTousuNoticeByTime" resultType="com.jiuji.oa.oacore.tousu.po.TousuNotice">
        select ID,TousuID,  ToUserName,  ToUserID,  NoticeType,  NoticeContent,create_time,
        LastTime from tousuNotice  WITH(NOLOCK) where lastTime between #{time1} and  #{time2} and isNotice = 0
    </select>

    <select id="getTsProcessByTsIdAndUser" resultType="com.jiuji.oa.oacore.tousu.po.TsProcess">
        select id,tsID,opUser,dsc_,intime,isShow,attachFiles,noticeUser,cate,cateContent,isxinsheng
         from tsProcess  WITH(NOLOCK) where tsId = #{tsId} and opUser = #{userName} and intime between #{time1} and  #{time2}
    </select>

    <select id="getFollowTimeoutCountByUserAndTouSuId" resultType="int">
        SELECT
            count(1)
        from
            tousu_timeout with(nolock)
        where
            type = 3
          and follow_user_id = #{userId}
          and tousu_id = #{tsId}
          and notice_id = #{noticeId}
    </select>

    <select id="getTouSuDepartsByTsIds" resultType="com.jiuji.oa.oacore.tousu.vo.res.TouSuDepartRes">
        select id, departArea, depart_id, tousuId, createTime, createUser,departCode,
        scoreArea, scoreDep, areaId, type, tousuTypes, areaName, cat, kinds, touSuRank
        from tousuDepart with(nolock) where 1 = 1 and
        <if test="tsIds != null and tsIds.size >0">
            tousuid in
            <foreach collection="tsIds" item="tsId" separator="," open="(" close=")" index="index">
                #{tsId}
            </foreach>
        </if>
        <if test="orderByCateFlag != null and orderByCateFlag == true">
            order by case cat when 1 then 1
            when 1 then 1
            when 3 then 2
            when 2 then 3
            else 4
            end
        </if>
    </select>

    <select id="getTousuZeRenRenByUserIdsAndTsId" resultType="com.jiuji.oa.oacore.tousu.po.TouSuZenRenRen">
        SELECT id, tousuId, userId, userName, tousuRank, tousuPoint, tousu_lose_point AS tousuLosePoint, bonus_points AS bonusPoints
        FROM getOfficeName().dbo.tousuZeRenRen z WITH(NOLOCK)
        WHERE z.id =#{id}
    </select>

    <select id="getTousuZeRenRenByTousuId" resultType="com.jiuji.oa.oacore.tousu.po.TouSuZenRenRen">
        SELECT id, tousuId, userId, userName, tousuRank, tousuPoint
        FROM getOfficeName().dbo.tousuZeRenRen z WITH(NOLOCK)
        WHERE z.tousuId =#{tousuId}
    </select>

    <select id="getTouSuCategoryByName" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM dbo.TousuCategory t WITH(NOLOCK) WHERE t.isdel = 0 AND t.name = #{name}
        <if test="id != null and id != 0">
            AND t.id &lt;&gt; #{id}
        </if>
    </select>
    <select id="getTouSuCategoryById" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM getOfficeName().dbo..TousuCategory t WITH(NOLOCK) WHERE t.isdel = 0 AND t.id = #{id}
    </select>
    <select id="getTsProcessByTsIdAndUserAndDsc" resultType="java.lang.Integer">
        select COUNT(1)   from getOfficeName().dbo.tsProcess  WITH(NOLOCK) where tsId = #{tsId} and opUser = #{userName} and dsc_ like '%跟进超时,超时时长%' and intime between #{createTime} and  #{lastTime}
    </select>

    <sql id="self_tousu">
        SELECT DISTINCT t.id into #self_tousu from (
            SELECT t.id
            from ${officeName}.dbo.tousu t WITH(nolock)
            where t.processUser = #{param.currentUserName}

            UNION ALL

            SELECT t.id
            from ${officeName}.dbo.TsProcess tp with(nolock)
            inner join ${officeName}.dbo.tousu t WITH(nolock) on t.id = tp.tsID
            where tp.opUser = #{param.currentUserName}

            UNION ALL

            SELECT t.id
            from ${officeName}.dbo.TousuNotice tn with(nolock)
            inner join ${officeName}.dbo.tousu t WITH(nolock) on t.id = tn.TousuID
            where tn.ToUserID = #{param.currentUserId}
        )t;
    </sql>
    <select id="getListCount" resultType="java.lang.Long">
        <if test="param.selfFlag == 1">
            <include refid="self_tousu"/>
        </if>

        SELECT COUNT(1)
        FROM (
        SELECT DISTINCT(t.id) as tsId, t.userid as userId, t.tag as tag, t.addTime,users.userclass as
        userClass,users.xtenant as xtenant,
        ( SELECT COUNT (id) FROM ${officeName}.dbo.tousu WITH(nolock) WHERE userid = t.userid AND userid IS NOT NULL AND
        userid &lt;&gt; 0) AS Tcount,
        t.content_ AS content,t.states_ AS states,t.bonusMoney,t.fineMoney
        FROM ${officeName}.dbo.tousu t WITH(nolock)
        <if test="param.selfFlag == 1">
        inner join #self_tousu st on st.id = t.id
        </if>
        LEFT JOIN ${officeName}.dbo.tousuDepart tt WITH(nolock) ON t.id = tt.tousuid
        LEFT JOIN ${officeName}.dbo.tsProcess ts WITH(nolock) ON t.id = ts.tsId
        LEFT JOIN ${officeName}.dbo.TousuCategoryRelation tr WITH(nolock) ON tr.departId = tt.id
        LEFT JOIN dbo.BBSXP_Users users WITH(nolock) ON t.userid = users.id
        WHERE isnull(t.source,0)=0
        <include refid="tousuBasicWhere"/>
        ) TOTAL;
    </select>
    <select id="getAllCoupleBack" resultType="com.jiuji.oa.oacore.tousu.vo.res.CoupleBackRes">
        select DISTINCT(t.id) as id, t.userid as userId, (Select CONVERT(varchar(100), t.addTime, 20)) as addTime,
        t.content_ as content,
        t.states_ as states,t.supplier,
        ( SELECT COUNT (id) FROM getOfficeName().dbo.tousu tt WITH(nolock) WHERE tt.userid = t.userid AND tt.userid IS NOT NULL
        AND
        tt.userid &lt;&gt; 0 and tt.type_ =5) as Tcount
        from getOfficeName().dbo.tousu t WITH(nolock)
        where t.isdel IS NULL and t.type_ = 5
        <include refid="CoupleBackWhere"/>
        ORDER BY addtime desc
        OFFSET #{startIndex} ROWS FETCH NEXT #{pageSize} ROWS ONLY
    </select>

    <select id="coupleBackListCount" resultType="java.lang.Integer">
        select count(*) from getOfficeName().dbo.tousu t WITH(nolock)
        where t.isdel IS NULL and t.type_ = 5
        <include refid="CoupleBackWhere"/>
    </select>
    <select id="getComplaintByDate" resultType="com.jiuji.oa.oacore.tousu.po.TouSuZenRenRen">
        SELECT a.id,a.tousuId,a.userId FROM getOfficeName().dbo.tousuZeRenRen as a WITH(nolock)
        inner join getOfficeName().dbo.tousu as b WITH(nolock) on a.tousuId=b.id
        <where>
            <if test="renRs !=null and renRs.size &gt; 0">
                a.userId in
                <foreach collection="renRs" index="index" item="ren" open="(" separator="," close=")">
                    #{ren.userId}
                </foreach>
            </if>
            and DATEDIFF(day,b.addTime,GETDATE()) &lt; 90
            and isnull(b.tag, 0) in (1, 2)
        </where>
    </select>
    <sql id="CoupleBackWhere">
        <if test="req!= null">
            <if test="req.keyWord != null and req.keyWord != 0">
                <choose>
                    <when test="req.keyWord == 1">
                        and t.supplier LIKE CONCAT('%',#{req.key},'%')
                    </when>
                    <when test="req.keyWord == 2">
                        and t.mobile = #{req.key}
                    </when>
                    <when test="req.keyWord ==3">
                        and t.content_
                        like CONCAT('%',#{req.key},'%')
                    </when>
                </choose>
            </if>
            <if test="req.states!=null and req.states!= 0 ">
                and t.states_ = #{req.states}
            </if>
            <if test="req.startTime!=null and req.startTime != null">
                and t.addTime between #{req.startTime} and #{req.endTime}
            </if>
            <if test="req.userId!=null">
                and t.userid =#{req.userId}
            </if>
        </if>
    </sql>

    <sql id="getFeedbackListWhere">
        <!--投诉原因-->
        <if test="param.touSuTypeVal!= null and param.touSuTypeVal.size() > 0">
            and EXISTS ( SELECT 1 FROM getOfficeName().dbo.TousuCategoryRelation WITH(nolock) where tousuId = t.id and cateId in
            <foreach collection="param.touSuTypeVal" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <!--反馈人ID-->
        <if test="param.userId!= null and param.userId != 0">
            and t.userid = #{param.userId}
        </if>
        <!--定性分类-->
        <if test="param.cat!= null and param.cat != 0">
            and tt.cat = #{param.cat}
        </if>
        <!--合作伙伴-->
        <if test="param.xtenant != null">
            and t.xtenant = #{param.xtenant}
        </if>
        <!--投诉等级-->
        <if test="param.touSuRank!= null and param.touSuRank != 0">
            and tt.touSuRank = #{param.touSuRank}
        </if>
        <!--反馈类型-->
        <if test="param.tags != null and param.tags.size() >0">
            and t.tag in
            <foreach collection="param.tags" item="tag" index="index" open="(" close=")" separator=",">
                #{tag}
            </foreach>
        </if>
        <!--处理状态-->
        <if test="param.searchState != null and param.searchState != 0">
            and t.states_ = #{param.searchState}
        </if>
        <!--进行中-->
        <if test="param.ckdIng != null and param.ckdIng != 0">
            <!--已受理、待整改、已处理、已整改、已还原、已界定、待复核、待仲裁-->
            and t.states_ IN (2,4,5,6,7,8,9,10)
        </if>
        <!--是否有奖励-->
        <if test="param.bonus != null and  param.bonus !=0 and param.bonus == 1">
            and isnull(t.bonusMoney,0) &lt;&gt; 0
        </if>
        <!--是否有罚款-->
        <if test="param.fine != null and param.fine !=0  and param.fine == 1">
            and isnull(t.fineMoney,0) &lt;&gt; 0
        </if>
        <!--是否有超时-->
        <if test="param.overTime != null and  param.overTime !=0 and param.overTime == 1">
            and ((getdate() > t.dealTimeout and t.states_ &lt;&gt; 3) or (t.states_ = 3 and t.finishTime > t.dealTimeout))
        </if>
        <!--客诉状态-->
        <if test="param.customer != null">
            <choose>
                <when test="param.customer == 1">
                    and t.CustomerEndTime IS NOT NULL
                </when>
                <when test="param.customer == 2">
                    and t.CustomerEndTime IS NULL
                </when>
            </choose>
        </if>
        <!--投诉时间/完结时间-->
        <if test="param.dateType != null ">
            <choose>
                <when test="param.dateType == 1 and param.startTime != null and param.endTime != null">
                    and t.addTime between #{param.startTime} and #{param.endTime}
                </when>
                <when test="param.dateType == 2 and param.startTime != null and param.endTime != null">
                    and t.finishTime between #{param.startTime} and #{param.endTime}
                </when>
            </choose>
        </if>
        <!--其他筛选条件-->
        <if test="param.key != null and param.searchKind != null and param.searchKind != 0">
            <choose>
                <!--反馈内容-->
                <when test="param.searchKind == 2">
                    and t.content_ like CONCAT('%',#{param.key},'%')
                </when>
                <!--反馈人-->
                <when test="param.searchKind == 3">
                    and t.memberName like CONCAT('%',#{param.key},'%')
                </when>
                <!--反馈ID,进程内容,责任人-->
                <when test="param.searchKind == 1 or param.searchKind == 4 or param.searchKind == 5 ">
                    <if test="param.ids != null and param.ids.size >0">
                        and t.id in
                        <foreach collection="param.ids" index="index" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    </if>
                </when>
                <!--跟进人-->
                <when test="param.searchKind == 6">
                    and t.processUser like CONCAT('%',#{param.key},'%')
                </when>
                <!--反馈人联系电话-->
                <when test="param.searchKind == 7">
                    and t.mobile LIKE
                    CONCAT('%',#{param.key},'%')
                </when>
            </choose>
        </if>
        <!-- 责任部门 -->
        <if test="param.departIds != null and param.departIds.size > 0">
            and tt.depart_id in
            <foreach collection="param.departIds" index="index" item="departId" separator="," open="(" close=")">
                #{departId}
            </foreach>
        </if>
    </sql>
    <select id="getFeedbackList" resultType="com.jiuji.oa.oacore.partner.feedback.vo.res.FeedbackRes">
        SELECT
        DISTINCT(t.id) as tsId,
        t.xtenant_name as xtenantName,
        t.addTime,
        t.content_ AS content,
        t.xtenant,
        STUFF((SELECT '|' + CAST(depart_id AS VARCHAR) + ',' + CAST(scoreDep AS VARCHAR)
                FROM office.dbo.tousuDepart with(nolock)
                WHERE tousuId = t.id
                FOR XML PATH('')), 1, 1, '') departs,
        case
        when t.source = 1 then '中大型'
        when t.source = 2 then 'NEO'
        else '九机'
        end AS sourceStr,
        case
        when t.states_ = 1 then '未处理'
        when t.states_ = 2 then '已受理'
        when t.states_ = 3 then '已完成'
        when t.states_ = 4 then '待整改'
        when t.states_ = 5 then '已处理'
        when t.states_ = 6 then '已整改'
        when t.states_ = 7 then '已还原'
        when t.states_ = 8 then '已界定'
        when t.states_ = 9 then '待复核'
        when t.states_ = 10 then '仲裁处理'
        when t.states_ = 11 then '客诉完结'
        else '未知'
        end AS states,
        t.tag
        FROM getOfficeName().dbo.tousu t WITH(nolock)
        LEFT JOIN getOfficeName().dbo.tousuDepart tt WITH(nolock) ON t.id = tt.tousuid

        WHERE isnull(t.isdel,0) = 0
        <include refid="sourceCondition"/>
        <include refid="getFeedbackListWhere"/>
        ORDER BY t.id desc
        OFFSET #{startIndex} ROWS FETCH NEXT #{pageSize} ROWS ONLY
    </select>

    <select id="getFeedbackListCount" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM (
        SELECT
        DISTINCT(t.id) as tsId
        FROM getOfficeName().dbo.tousu t WITH(nolock)
        LEFT JOIN getOfficeName().dbo.tousuDepart tt WITH(nolock) ON t.id = tt.tousuid
        WHERE isnull(t.isdel,0) = 0
        <include refid="sourceCondition"/>
        <include refid="getFeedbackListWhere"/>
        ) TOTAL
    </select>

    <sql id="sourceCondition">
        <choose>
            <when test="param.source!=null">
                and t.source=#{param.source}
            </when>
            <otherwise>
                and t.source in (1,2)
            </otherwise>
        </choose>
    </sql>

    <select id="getComplainReceiver" resultType="java.lang.String">
    SELECT TOP 1 opUser FROM tsProcess with(nolock) WHERE tsID = #{id} and dsc_ like '%*受理%' ORDER BY id DESC
    </select>

    <!-- countTousuPublic -->
    <select id="countTousuPublic" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM (
            SELECT DISTINCT(t.id) as tsId, t.userid as userId, isnull(t.new_tag, t.tag) as tousuKinds, t.addTime,
                        users.userclass as userClass, t.content_ AS content, t.states_ AS status
            FROM ${officeName}.dbo.tousu t WITH(nolock)
                LEFT JOIN BBSXP_Users users WITH(nolock) ON t.userid = users.id
            WHERE ISNULL(t.source, 0) = 0 AND ISNULL(t.show_web, 0) = 1
            <if test="param.xtenant != null">
                AND t.xtenant = #{param.xtenant}
            </if>
         ) TOTAL;
    </select>

    <!-- pageTousuPublic -->
    <select id="pageTousuPublic" resultType="com.jiuji.oa.oacore.tousu.res.TousuPublicRes">
        SELECT DISTINCT(t.id) as id, t.AreaId as shopId, t.userid as userId, t.processUser, a.area AS shopCode,
            a.area_name as shopName, users.userclass as userClass, t.tag as tag, t.addTime,
            t.content_ as content, t.states_ AS status, isnull(t.new_tag, t.tag) AS tousuKinds, t.tousupic as attachIds
        FROM ${officeName}.dbo.tousu t WITH(nolock)
            LEFT JOIN BBSXP_Users users WITH(nolock) ON t.userid = users.id
            LEFT JOIN areainfo a WITH(nolock) ON t.AreaId = a.id
        WHERE ISNULL(t.source, 0) = 0 AND ISNULL(t.show_web, 0) = 1
        <if test="param.xtenant != null">
            AND t.xtenant = #{param.xtenant}
        </if>
        ORDER BY t.addtime desc
        OFFSET #{startIndex} ROWS FETCH NEXT #{pageSize} ROWS ONLY
    </select>

    <!-- listTsProcessByShowWeb -->
    <select id="listTsProcessWithShowWebByTsIds" resultType="com.jiuji.oa.oacore.tousu.po.TsProcess">
        SELECT id, tsID, opUser, dsc_ , intime, isShow, attachFiles, noticeUser, cate, isxinsheng, fake_log, show_web
        FROM tsProcess WITH(nolock)
        WHERE show_web = 1 AND tsID in
        <foreach collection="tsIds" index="index" item="tsId" open="(" close=")" separator=",">
            #{tsId}
        </foreach>
    </select>

    <select id="getTousuDepartPoints" resultType="com.jiuji.oa.oacore.tousu.bo.TousuDepartPointsBO">
        select top 1
               b.id,
               b.bumen,
               b.totaljifen,
               b.jifens,
               b.jifendate,
               b.type_ type
        from bumenjifen b with(nolock)
        where b.fenType = 1
          and b.type_ = #{type}
          and b.bumen = #{bumen}
          and b.jifendate = #{month}
    </select>

    <update id="updateTousuDepartFen">
        update bumenjifen
        set jifens = #{param.jifens},
            totaljifen = #{param.totaljifen}
        where id = #{param.id}
    </update>

        <insert id="addTousuDepartFenLog">
        INSERT INTO dbo.bumenjifen_log (jfid, fen, inuser, dtime, comment)
        VALUES (#{param.id}, #{param.fen}, #{param.inuser}, #{param.dtime}, #{param.comment} )
    </insert>
</mapper>
