<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.tousu.dao.ComplainPushRelationMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.tousu.entity.ComplainPushRelation">
    <!--@mbg.generated-->
    <!--@Table complain_push_relation-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="complain_id" jdbcType="INTEGER" property="complainId" />
    <result column="money" jdbcType="DECIMAL" property="money" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="is_check" jdbcType="INTEGER" property="isCheck" />
    <result column="end_time" jdbcType="DATE" property="endTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, complain_id, money, [type], is_check, end_time, create_time, update_time
  </sql>

  <select id="getComplainRewardByComplainId" resultType="com.jiuji.oa.oacore.tousu.vo.res.ComplainReward">
    select cpr.complain_id complainId, sum(cpr.money) reward
    from complain_push_relation cpr with (nolock)
    where cpr.type = 2
      and cpr.complain_id in
    <foreach collection="complainIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    group by cpr.complain_id
  </select>
</mapper>