<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.tousu.dao.RecivesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="ReceiverMap" type="com.jiuji.oa.oacore.tousu.po.ReceiverCache">
        <result column="name" property="name" />
        <result column="type_" property="type_" />
        <result column="ch999_id" property="ch999_id" />
        <result column="area1id" property="areaid" />
        <result column="leve" property="level" />
    </resultMap>

    <select id="getReciverByZhiWu" resultMap="ReceiverMap">
        select * from (
            select row_number() over(partition by name order by leve asc, ch999_id asc) rn, name, type_, ch999_id,aa.area1id,aa.leve
            from (
                select
                       case when u.area1id in (13, 16, 22)
                           then cast(dbo.getBuMenCode(u.departId) as nvarchar(20))
                           else cast(u.area1id as nvarchar(10)) end as name,
                       case when u.area1id in (13, 16, 22)
                           then 2
                           else 1  end as type_,
                       ch999_id,ch999_name,z.leve,u.area1id
                from dbo.ch999_user u with(nolock) inner join zhiwu z with(nolock)
                    ON u.zhiwuid = z.id
                           AND z.id IN (#{ids})
                           and u.iszaizhi = 1
                           <![CDATA[ and u.isshixi  <> 4 ]]>
                           and u.islogin=0
                <if test="authorize>0">
                    AND EXISTS(
                    SELECT 1 FROM dbo.areainfo ai with(nolock)
                    WHERE ai.id = u.area1id AND ai.authorizeid = #{authorize})
                </if>
                ) aa
            ) aa
        where aa.rn = 1
    </select>

</mapper>
