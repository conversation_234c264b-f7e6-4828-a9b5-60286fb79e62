<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright © 2006 - 2020 九机网 All Rights Reserved
  ~
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.oacore.tousu.dao.AttachmentsMapper">

  <resultMap id="attachmentsMap" type="com.jiuji.oa.oacore.tousu.po.Attachments">
                  <id property="id" column="id"/>
                        <result property="linkedid" column="linkedID"/>
                        <result property="type" column="type"/>
                        <result property="filename" column="filename"/>
                        <result property="filepath" column="filepath"/>
                        <result property="kind" column="kind"/>
                        <result property="dtime" column="dtime"/>
                        <result property="userid" column="userid"/>
                        <result property="fid" column="fid"/>
                        <result property="extension" column="Extension"/>
                        <result property="receiveUser" column="receiveUser"/>
                        <result property="receiveDate" column="receiveDate"/>
                        <result property="filesize" column="fileSize"/>
            </resultMap>
    <select id="getAttachmentsByLinkId" resultType="com.jiuji.oa.oacore.tousu.po.Attachments">
        SELECT id, linkedID linkedid, [type], filename, filepath, kind, dtime, userid, fid, Extension, receiveUser, receiveDate, fileSize, kind1, isweb, frame_path
        FROM dbo.attachments with(nolock) where linkedID = #{linkId} and type = #{type}
    </select>

    <!-- listTousuAttachmentsByIds -->
    <select id="listTousuAttachmentsByIds" resultType="com.jiuji.oa.oacore.tousu.res.AttachmentsRes">
        SELECT id, linkedID linkedid, [type], filename, filepath, kind, fid, Extension, frame_path as framePath
        FROM dbo.attachments with(nolock)
        WHERE id IN
        <foreach collection="attachIds" index="index" item="attachId" open="(" separator="," close=")">
            #{attachId}
        </foreach>
    </select>

    <select id="getAttachmentsByLinkIds" resultType="com.jiuji.oa.oacore.tousu.po.Attachments">
        SELECT id, linkedID linkedid, filename, filepath, fid, Extension, receiveUser
        FROM dbo.attachments with(nolock) where linkedID in
        <foreach collection="linkIds" index="index" item="linkId" open="(" separator="," close=")">
            #{linkId}
        </foreach>
        and type = #{type}
    </select>
</mapper>
