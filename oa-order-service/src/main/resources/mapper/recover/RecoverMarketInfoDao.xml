<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.recover.dao.RecoverMarketInfoDao">

    <select id="recentSoldPid" resultType="java.lang.Integer">
        SELECT
            DISTINCT pp.productid
        from recover_marketInfo s with(nolock)
            INNER JOIN recover_marketSubInfo b with(nolock) ON s.sub_id = b.sub_id
            INNER JOIN productprice pp with(nolock) on b.ppriceid=pp.ppriceid
        WHERE 1 = 1
            AND ISNULL(s.saleType, 0) = 0
            AND s.sub_check = 3
            AND ISNULL(b.isdel, 0) = 0
            <if test="startDate!=null">
                AND tradeDate >= #{startDate}
            </if>
            <if test="endDate!=null">
                AND tradeDate &lt;= #{endDate}
            </if>
        <if test="pidList !=null and pidList.size>0">
            AND pp.productid IN
            <foreach collection="pidList" item="pid" separator="," open="(" close=")">
                #{pid}
            </foreach>
        </if>
    </select>

</mapper>
