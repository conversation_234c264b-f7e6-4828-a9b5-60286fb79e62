<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.recover.dao.WebRecoverMapper">

    <select id="selectRecoverToAreaByMkcId" resultType="com.jiuji.oa.oacore.recover.res.RecoverToAreaRes">
        SELECT top 1 k.id mkcId, a.areaid fromAreaId,a.toareaid toAreaId,a.sendtime sendTime,a.dtime dTime, k.mkc_check mkcCheck
            ,a.status
        FROM dbo.recover_mkc k WITH(NOLOCK)
        INNER JOIN dbo.recover_toarea a WITH(NOLOCK) ON k.id=a.mkc_id
        WHERE a.status != 3
        AND k.id = #{recoverToAreaMkcReq.mkcId}
    </select>
    <select id="selectRecoverToAreaBySubId" resultType="com.jiuji.oa.oacore.recover.res.RecoverToAreaRes">
        SELECT k.id mkcId,k.mkc_check mkcCheck,k.areaid,s.areaid,t.areaid fromAreaId,t.toareaid toAreaId,t.sendtime sendTime,t.dtime dTime
        FROM recover_marketSubInfo b WITH(NOLOCK)
        inner join recover_marketInfo s WITH(NOLOCK) on s.sub_id = b.sub_id
        inner join recover_mkc k WITH(NOLOCK) on k.to_basket_id = b.basket_id
        inner join recover_toarea t WITH(NOLOCK) on t.mkc_id = k.id and t.status != 3
        where b.sub_id = #{recoverToAreaSubIdReq.subId}
    </select>
    <select id="selectRecoverMkcByMkcId" resultType="com.jiuji.oa.oacore.recover.res.RecoverToAreaRes">
        SELECT k.id mkcId,a.areaid fromAreaId,isnull(a.toareaid, k.areaid) toAreaId,a.sendtime sendTime,a.dtime dTime, k.mkc_check mkcCheck
            ,a.status
        FROM dbo.recover_mkc k WITH(NOLOCK)
        left join dbo.recover_toarea a WITH(NOLOCK) ON k.id=a.mkc_id and a.status != 3
        WHERE k.id in
        <foreach collection="req.mkcIds" item="mkcId" open="(" separator="," close=")">
            #{mkcId}
        </foreach>
        order by k.id asc, a.id desc
    </select>

</mapper>
