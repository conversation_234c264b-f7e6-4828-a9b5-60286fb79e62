<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.oacore.recover.dao.UsedGoodsMapper">
    <select id="selectNewMpicsByMkcIds" resultType="com.jiuji.oa.oacore.recover.res.SalfGoodsNewMpicsRes">
        select sf.mkc_id mkcId,sf.newMpics from salfGoods sf with(nolock)
        <where>
            sf.mkc_id in
            <foreach collection="mkcIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="getRecyclePjtGoodsByGoodsIds"
            resultType="com.jiuji.oa.oacore.recover.res.SalfGoodsNewMpicsRes">
        select rpg.id mkcId,rpg.ppic newMpics from recycle_pjt_goods rpg with(nolock)
        <where>
            rpg.id in
            <foreach collection="goodsIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </where>
    </select>
</mapper>
