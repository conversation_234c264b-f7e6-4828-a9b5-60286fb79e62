<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.subRecommended.mapper.SubRecommendedMainConfigMapper">


    <select id="pageInfo" resultType="com.jiuji.oa.oacore.subRecommended.vo.res.RecommendedPageRes">
        SELECT main.id as mainConfigId,
        main.start_time,
        main.end_time,
        ISNULL(t.useCount,0) AS use_count,
        main.create_user,
        main.is_enabled,
        STUFF((SELECT ',' + CAST(a.area AS VARCHAR(10))
        FROM dbo.sub_recommended_main_area_config area with (nolock)
            left join dbo.areainfo a with (nolock ) on a.id=area.area_id
            WHERE area.fk_config_id = main.id and area.is_del=0
            FOR XML PATH('')), 1, 1, '') AS applyArea,
        STUFF((SELECT  distinct  ',' + CAST( info.product_name AS VARCHAR(100))+'('+CAST(info.product_id AS VARCHAR(10))+')'
            FROM dbo.sub_recommended_main_product_config product with (nolock)
            left join dbo.productinfo info with(nolock ) on info.product_id=product.product_id
            WHERE product.fk_config_id = main.id and product.is_del=0
            FOR XML PATH('')), 1, 1, '') AS mainProductName
        FROM dbo.sub_recommended_main_config main with (nolock)
            left join (SELECT count(b.sub_id) useCount,SUBSTRING(be.gourp_buy_key, 1, CASE WHEN CHARINDEX('|', be.gourp_buy_key) > 0 THEN CHARINDEX('|', be.gourp_buy_key) - 1 ELSE 0 END) as mainConfigId  from dbo.basket b
            inner join dbo.basket_extend be on be.basket_id = b.basket_id
            inner join dbo.sub s on b.sub_id = s.sub_id
            where b.[type] = 123 and s.sub_check =3 and isnull(b.isdel,0) = 0
            <if test="req.timeType!= null and req.timeType==2">
                 and s.tradeDate1 between #{req.startTime} and #{req.endTime}
            </if>
        group by  SUBSTRING(be.gourp_buy_key, 1, CASE WHEN CHARINDEX('|', be.gourp_buy_key) > 0 THEN CHARINDEX('|', be.gourp_buy_key) - 1 ELSE 0 END)  ) t on t.mainConfigId = main.id
        <where>
            1=1 and main.is_del=0
            <if test="req.timeType!= null and req.timeType==1">
                and (
                (#{req.startTime}  &lt;= main.end_time and #{req.endTime} >= main.start_time)
                or(#{req.startTime} >= main.start_time and #{req.startTime}  &lt;= main.end_time)
                or(#{req.endTime}>= main.start_time and #{req.endTime}  &lt;= main.end_time)
                )
            </if>
            <if test="req.timeType!= null and req.timeType==2">
                and t.useCount>0
            </if>
            <if test="req.applyAreaIdList != null and req.applyAreaIdList.size() > 0">
                and exists(select 1
                from dbo.sub_recommended_main_area_config area with(nolock)
                where area.area_id in
                <foreach collection="req.applyAreaIdList" item="areaId" open="(" close=")" separator=",">
                    #{areaId}
                </foreach>
                and area.fk_config_id = main.id and area.is_del=0)
            </if>
            <if test="req.selectKey!=null and req.selectKey!='' and req.selectType== 2">
                and exists(select 1
                from dbo.sub_recommended_main_product_config product with(nolock)
                where product.product_id = #{req.selectKey} and product.fk_config_id = main.id and product.is_del=0)
            </if>
            <if test="req.selectKey!=null and req.selectKey!='' and req.selectType== 1">
                and exists(select 1
                from dbo.sub_recommended_main_product_config product with(nolock) left join dbo.productinfo p with(nolock) on p.product_id = product.product_id
                where p.product_name like CONCAT('%',#{req.selectKey},'%') and product.fk_config_id = main.id and product.is_del=0)
            </if>
            <if test="req.selectKey!=null and req.selectKey!='' and req.selectType== 3">
                and main.create_user = #{req.selectKey}
            </if>
            <if test="req.state!=null and req.state==1">
                and  GETDATE() &lt; main.start_time
            </if>
            <if test="req.state!=null and req.state==2">
                and GETDATE() between main.start_time and main.end_time
            </if>
            <if test="req.state!=null and req.state==3">
                and GETDATE()  &gt; main.end_time
            </if>
            <if test="req.isEnabled!=null  ">
                and main.is_enabled = #{req.isEnabled}
            </if>

        </where>
     </select>
    <select id="selectRecommendedSelectOa" resultType="com.jiuji.oa.oacore.subRecommended.entity.SubRecommendedMainConfig">
        select  *
        from dbo.sub_recommended_main_config main with (nolock)
         left join dbo.sub_recommended_main_product_config product with (nolock) on product.fk_config_id = main.id and product.is_del=0
            left join dbo.productinfo info with(nolock ) on info.product_id=product.product_id
        where main.is_del = 0
          and main.is_enabled = 1
          and main.xtenant = #{xtenant}
          and GETDATE() between main.start_time and main.end_time
          and info.ppriceid=#{ppid}
        order by main.create_time desc

    </select>
    <select id="selectPersonnelType" resultType="java.lang.Integer">
        select case
                   when a.areaType = 2 and a.area_region_divide = 1 then 5
                   when (d.dataType in (3,5) and (a.areaType = 2 and ISNULL(a.area_region_divide, 2) = 2)) or r.RoleName = '分公司总经理'  then 4
                   when (d.dataType = 4 and (a.areaType = 2 and ISNULL(a.area_region_divide, 2) = 2)) or r.roleName = '城市经理' then 3
                   when (a.areaType = 1 and ISNULL(a.area_region_divide, 2) = 2) and (r.roleName in ('店长', '副店长', '主管','门店主管')) then 2
                   when a.areaType = 1 and ISNULL(a.area_region_divide, 2) = 2 then 1
                   else 1 end
        from ch999_user c with (nolock)
            left join areainfo a with (nolock) on c.area1id = a.id
            left join RoleInfo r with (nolock) on r.id = c.mainRole
            left join departInfo d with (nolock) on d.id = c.depart_id
        where isnull(d.isdel, 0) = 0
          and isnull(r.is_del, 0) = 0
          and c.ch999_id = #{userId}
    </select>
    <select id="selectComprehensiveStore" resultType="java.lang.Integer">
      select a.id from dbo.areainfo a with(nolock)  where a.attribute=1101 and ISNULL(a.ispass,0)=1 and a.id = #{areaId}
    </select>
    <select id="selectBasketInfo" resultType="com.jiuji.oa.oacore.subRecommended.vo.bo.RecommendedBasketInfo">
        select s.areaid as areaId,b.basket_id,p.ppriceid,p.product_id,a.xtenant
        from dbo.sub s with (nolock)
         left join dbo.basket b with (nolock) on b.sub_id = s.sub_id
            left join dbo.productinfo p with (nolock) on p.ppriceid = b.ppriceid
            left join dbo.areainfo a with(nolock ) on a.id=s.areaid
        where ISNULL(b.isdel,0)=0 and s.sub_id=#{subId}
    </select>
    <select id="selectInitializationData"
            resultType="com.jiuji.oa.oacore.subRecommended.vo.bo.InitializationDataBo">
        select config.id,
               config.create_user,
               config.xtenant        as configXtenant,
               config.create_area_id as configCreateAreaId,
               u.area1id,
               a.xtenant
        from dbo.sub_recommended_main_config config with (nolock)
         left join dbo.ch999_user u with (nolock) on u.ch999_name = config.create_user
            left join dbo.areainfo a with (nolock) on a.id = u.area1id
        where config.is_del = 0
    </select>
    <select id="selectViewProductStBoByPpid" resultType="com.jiuji.oa.oacore.subRecommended.vo.bo.ViewProductStBo">
        select ppriceid,productid,name,value from dbo.VIEW_PRODUCT_ST
        where ppriceid in
        <foreach collection="ppidList" item="ppid" open="(" close=")" separator=",">
            #{ppid}
        </foreach>
    </select>
</mapper>
