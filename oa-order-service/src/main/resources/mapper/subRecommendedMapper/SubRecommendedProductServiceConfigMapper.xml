<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.subRecommended.mapper.SubRecommendedProductServiceConfigMapper">


    <select id="selectCloseServiceBo" resultType="com.jiuji.oa.oacore.subRecommended.vo.bo.CloseServiceBo">
        select distinct service.ppid          as servicePpid,
               service.fk_package_id as packageId,
               product.ppid          as productPpid,
               p.cid                 as productCid,
               main.id               as mainConfigId,
               main.create_user      as userName,
               ur.ch999_id           as userId
        from dbo.sub_recommended_product_service_config service with (nolock)
         left join dbo.sub_recommended_package_config package with (nolock) on package.id = service.fk_package_id
            left join dbo.sub_recommended_main_config main with (nolock) on main.id = package.fk_config_id
            left join dbo.sub_recommended_product_config product with (nolock) on product.id = service.fk_product_id
            left join dbo.productinfo p with (nolock) on product.ppid = p.ppriceid
            left join dbo.ch999_user ur with (nolock) on main.create_user = ur.ch999_name
        <where>
            service.is_del = 0
            and product.is_del = 0
            and main.is_del = 0
            and main.is_enabled = 1
                <if test="servicePpidList != null and servicePpidList.size() > 0">
                    and service.ppid in
                    <foreach collection="servicePpidList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="productPpid != null">
                    and product.ppid = #{productPpid}
                </if>
        </where>
     </select>
</mapper>
