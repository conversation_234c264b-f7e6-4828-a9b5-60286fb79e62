<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.TaxPiaoUserVerifyMapper">
    <select id="getMaxPriceBySubId" resultType="com.jiuji.oa.oacore.oaorder.bo.tax.TaxSubjectBo">
        <choose>
            <when test="type == null || type == 0">
                <!--新机订单-->
                select top 1 b.sub_id subId,b.basket_id basketId,b.ppriceid ppriceid,isnull(b.price,0) price
                from dbo.basket b with(nolock)
                where b.sub_id = #{subId} and isnull(b.isdel,0) = 0
            </when>
            <when test="type == 1">
                <!--售后单-->
                select top 1 b.wxid subId,b.id basketId,b.ppriceid ppriceid,isnull(b.price,0) price
                from dbo.wxkcoutput b with(nolock)
                where b.ppriceid >0 and b.wxid = #{subId} and isnull(b.stats,0) &lt;&gt; 3
            </when>
            <when test="type == 2">
                <!--良品单-->
                select top 1 b.sub_id subId,b.basket_id basketId,b.ppriceid ppriceid,isnull(b.price,0) price
                from dbo.recover_marketSubInfo b with(nolock)
                where b.sub_id = #{subId} and isnull(b.isdel,0) = 0
            </when>
        </choose>
        order by price desc,basketId desc
    </select>
    <select id="listRPpid" resultType="com.jiuji.oa.oacore.oaorder.bo.tax.TaxSubjectBo">
        select b.ppriceid ppriceid
        from(
            <choose>
                <when test="type == null || type == 0">
                    <!--新机订单-->
                    select b.ppriceid ppriceid,b.basket_id basketId
                         ,row_number() over (partition by b.ppriceid order by b.basket_id desc) n
                         ,row_number() over (partition by b.sub_id order by b.price desc) mp
                    from dbo.basket b with(nolock)
                    where isnull(b.isdel,0) = 0
                    and b.ppriceid not in(select b2.ppriceid from basket b2 with(nolock) where b2.sub_id = #{subId})
                    and b.ismobile = #{productinfo.ismobile1}
                </when>
                <when test="type == 1">
                    <!--售后单-->
                    select b.ppriceid ppriceid,b.id basketId
                         ,row_number() over (partition by ppriceid order by b.id desc) n
                         ,row_number() over (partition by b.wxid order by b.price desc) mp
                    from dbo.wxkcoutput b with(nolock)
                    where b.ppriceid >0
                    and b.ppriceid not in(select b2.ppriceid from wxkcoutput b2 with(nolock) where b2.wxid = #{subId})
                </when>
                <when test="type == 2">
                    <!--良品单-->
                    select b.ppriceid ppriceid,b.basket_id basketId
                         ,row_number() over (partition by ppriceid order by b.basket_id desc) n
                         ,row_number() over (partition by b.sub_id order by b.price desc) mp
                    from dbo.recover_marketSubInfo b with(nolock)
                    where isnull(b.isdel,0) = 0
                    and b.ppriceid not in(select b2.ppriceid from recover_marketSubInfo b2 with(nolock) where b2.sub_id = #{subId})
                    and b.ismobile = #{productinfo.ismobile1}
                </when>
            </choose>
        ) b
        where b.n = 1 and b.mp = 1
        order by b.basketId desc
        offset #{offset} row fetch next 3 row only
    </select>
    <select id="getTaxPiaoId" resultType="java.lang.Integer">
        select tp.id from tax_piao tp with(nolock)
            where isnull(tp.type_,0) = #{userVerifyBo.subType}
              and tp.sub_id = #{userVerifyBo.subId} and tp.flag &lt;&gt; -1
    </select>
</mapper>