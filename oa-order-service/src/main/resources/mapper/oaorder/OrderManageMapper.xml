<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.OrderManageMapper">

    <sql id="sale_base">
        <choose>
            <when test="kind == 1 || kind == 3">
                AND b.seller = #{userName}
            </when>
            <when test="kind == 2">
                AND s.areaid = #{params.curAreaId}
            </when>
            <otherwise></otherwise>
        </choose>
        <choose>
            <when test="params.searchKind != null and params.searchKind == '订单电话号码'">
                AND s.sub_mobile = #{params.searchValue}
            </when>
            <when test="params.searchKind != null and params.searchKind == 'IMEI'">
                AND EXISTS (SELECT 1 FROM dbo.product_mkc mkc with(NOLOCK) WHERE mkc.basket_id = b.basket_id AND mkc.imei LIKE '%' + #{params.searchValue} + '%')
            </when>
            <when test="params.searchKind != null and params.searchKind == '订单号'">
                AND CAST(s.sub_id AS VARCHAR(50)) =  #{params.searchValue}
            </when>
            <otherwise></otherwise>
        </choose>
        <choose>
            <when test="(params.subDateStart == null or params.subDateStart == '') and (params.subDateEnd != null and params.subDateEnd != '')">
                AND s.sub_date &lt;= #{params.subDateEnd}
            </when>
            <when test="(params.subDateStart != null and params.subDateStart != '') and (params.subDateEnd == null or params.subDateEnd == '')">
                AND s.sub_date &gt;= #{params.subDateEnd}
            </when>
            <when test="(params.subDateStart != null and params.subDateStart != '') and (params.subDateEnd != null and params.subDateEnd != '')">
                AND s.sub_date BETWEEN #{params.subDateStart} AND #{params.subDateEnd}
            </when>
            <otherwise></otherwise>
        </choose>
        <if test="params.paisongStats != null and params.paisongStats != -1">
            AND EXISTS(SELECT 1 FROM dbo.SubAddress sa WITH(NOLOCK) WHERE sa.sub_id = s.sub_id AND
            ISNULL(sa.paisongState,0) = #{params.paisongStats})
        </if>
        <if test="params.kcStats !=null ">
            <choose>
                <when test="params.kcStats == -2">
                    AND NOT EXISTS (SELECT 1 FROM dbo.product_mkc mkc with(NOLOCK) WHERE mkc.basket_id =
                    b.basket_id)
                </when>
<!--                <when test="params.kcStats >= 0">-->
<!--                    AND EXISTS (SELECT 1 FROM dbo.product_mkc mkc with(NOLOCK) WHERE mkc.basket_id = b.basket_id AND-->
<!--                    mkc.kc_check = #{params.kcStats})-->
<!--                </when>-->
                <otherwise>
                    AND 1 = 1
                </otherwise>
            </choose>
        </if>
    </sql>
    <sql id="confirmed_stock">
        <choose>
            <when test="kind == 1">
                AND b.seller = #{params.sellerName}
            </when>
            <when test="kind == 2">
                AND s.areaid = #{params.curAreaId}
            </when>
            <otherwise></otherwise>
        </choose>
        <choose>
            <when test="params.searchKind != null and params.searchKind == '订单电话号码'">
                AND s.sub_mobile = #{params.searchValue}
            </when>
            <when test="params.searchKind != null and params.searchKind == '商品名称'">
                AND EXISTS (SELECT 1 FROM dbo.productinfo pf with(NOLOCK) WHERE pf.ppriceid = b.ppriceid AND pf.product_name LIKE '%' + #{params.searchValue} + '%')
            </when>
            <when test="params.searchKind != null and params.searchKind == 'IMEI'">
                AND EXISTS (SELECT 1 FROM dbo.product_mkc mkc with(NOLOCK) WHERE mkc.basket_id = b.basket_id AND mkc.imei LIKE '%' + #{params.searchValue} + '%')
            </when>
            <when test="params.searchKind != null and params.searchKind == '订单号'">
                AND CAST(s.sub_id AS VARCHAR(50)) =  #{params.searchValue}
            </when>
            <otherwise></otherwise>
        </choose>
        <choose>
            <when test="(params.subDateStart == null or params.subDateStart == '') and (params.subDateEnd != null and params.subDateEnd != '')">
                AND s.sub_date &lt;= #{params.subDateEnd}
            </when>
            <when test="(params.subDateStart != null and params.subDateStart != '') and (params.subDateEnd == null or params.subDateEnd == '')">
                AND s.sub_date &gt;= #{params.subDateEnd}
            </when>
            <when test="(params.subDateStart != null and params.subDateStart != '') and (params.subDateEnd != null and params.subDateEnd != '')">
                AND s.sub_date BETWEEN #{params.subDateStart} AND #{params.subDateEnd}
            </when>
            <otherwise></otherwise>
        </choose>
        <if test="params.paisongStats != null and params.paisongStats != -1">
            AND EXISTS(SELECT 1 FROM dbo.SubAddress sa WITH(NOLOCK) WHERE sa.sub_id = s.sub_id AND
            ISNULL(sa.paisongState,0) = #{params.paisongStats})
        </if>
    </sql>
    <sql id="good_product_base">
        <choose>
            <when test="kind == 1">
                AND rms.seller = #{userName}
            </when>
            <when test="kind == 2">
                AND rmi.areaid= #{params.curAreaId}
            </when>
            <otherwise></otherwise>
        </choose>
        <choose>
            <when test="params.searchKind != null and params.searchKind == '订单电话号码'">
                AND rmi.sub_mobile = #{params.searchValue}
            </when>
            <when test="params.searchKind != null and params.searchKind == '商品名称'">
                AND EXISTS (SELECT 1 FROM dbo.productinfo pf with(NOLOCK) WHERE pf.ppriceid = rms.ppriceid AND pf.product_name LIKE '%' + #{params.searchValue} + '%')
            </when>
            <when test="params.searchKind != null and params.searchKind == 'IMEI'">
                AND EXISTS (SELECT 1 FROM dbo.recover_mkc mkc with(NOLOCK) WHERE mkc.to_basket_id = rms.basket_id AND mkc.imei LIKE '%' + #{params.searchValue} + '%')
            </when>
            <when test="params.searchKind != null and params.searchKind == '订单号'">
                AND CAST(rmi.sub_id AS VARCHAR(50)) =  #{params.searchValue}
            </when>
            <otherwise></otherwise>
        </choose>
        <choose>
            <when test="(params.subDateStart == null or params.subDateStart == '') and (params.subDateEnd != null and params.subDateEnd != '')">
                AND rmi.sub_date &lt;= #{params.subDateEnd}
            </when>
            <when test="(params.subDateStart != null and params.subDateStart != '') and (params.subDateEnd == null or params.subDateEnd == '')">
                AND rmi.sub_date &gt;= #{params.subDateEnd}
            </when>
            <when test="(params.subDateStart != null and params.subDateStart != '') and (params.subDateEnd != null and params.subDateEnd != '')">
                AND rmi.sub_date BETWEEN #{params.subDateStart} AND #{params.subDateEnd}
            </when>
            <otherwise></otherwise>
        </choose>
        <if test="params.subCheck !=null ">
            AND rmi.sub_check = #{params.subCheck}
        </if>
    </sql>
    <sql id="recycle_base">
        <choose>
            <when test="kind == 1">
                AND (b.inuser = #{userName} or (s.sub_delivery=2 and b.checkUser=#{userName}))
            </when>
            <when test="kind == 2">
                AND  s.areaid = #{params.curAreaId}
            </when>
            <otherwise></otherwise>
        </choose>
        <choose>
            <when test="params.searchKind != null and params.searchKind == '订单电话号码'">
                AND s.sub_tel = #{params.searchValue}
            </when>
            <when test="params.searchKind != null and params.searchKind == '商品名称'">
                AND EXISTS (SELECT 1 FROM dbo.productinfo pf with(NOLOCK) WHERE pf.ppriceid = b.ppriceid AND pf.product_name LIKE '%'+#{params.searchValue}+'%')
            </when>
            <when test="params.searchKind != null and params.searchKind == 'IMEI'">
                AND b.imei  LIKE '%'+#{params.searchValue}+'%')
            </when>
            <when test="params.searchKind != null and params.searchKind == '订单号'">
                AND CAST(s.sub_id AS VARCHAR(50))  = #{params.searchValue}
            </when>
            <otherwise></otherwise>
        </choose>
        <choose>
            <when test="(params.subDateStart == null or params.subDateStart == '') and (params.subDateEnd != null and params.subDateEnd != '')">
                AND s.dtime &lt;= #{params.subDateEnd}
            </when>
            <when test="(params.subDateStart != null and params.subDateStart != '') and (params.subDateEnd == null or params.subDateEnd == '')">
                AND s.dtime &gt;= #{params.subDateEnd}
            </when>
            <when test="(params.subDateStart != null and params.subDateStart != '') and (params.subDateEnd != null and params.subDateEnd != '')">
                AND s.dtime BETWEEN #{params.subDateStart} AND #{params.subDateEnd}
            </when>
            <otherwise></otherwise>
        </choose>
        <choose>
            <when test="params.subCheck !=null ">
                AND s.sub_check =  #{params.subCheck}
            </when>
        </choose>
    </sql>
    <sql id="small_base">
        <choose>
            <when test="req.whetherQueryByCurArea == true">
                AND s.areaid = #{req.curAreaId}
            </when>
            <otherwise>
                AND s.Inuser=#{req.sellerName}
            </otherwise>
        </choose>
        <choose>
            <when test="(req.subDateStart == null or req.subDateStart == '') and (req.subDateEnd != null and req.subDateEnd != '')">
                AND s.Indate &lt;= #{req.subDateEnd}
            </when>
            <when test="(req.subDateStart != null and req.subDateStart != '') and (req.subDateEnd == null or req.subDateEnd == '')">
                AND s.Indate &gt;= #{req.subDateStart}
            </when>
            <when test="(req.subDateStart != null and req.subDateStart != '') and (req.subDateEnd != null and req.subDateEnd != '')">
                AND s.Indate BETWEEN #{req.subDateStart} AND #{req.subDateEnd}
            </when>
            <otherwise></otherwise>
        </choose>
        <choose>
            <when test="req.searchKind != null and req.searchKind == 'id'">
                AND s.id = #{req.searchValue}
            </when>
            <when test="req.searchKind != null and req.searchKind == 'name'">
                AND s.name like '%' + #{req.searchValue} + '%'
            </when>
            <when test="req.searchKind != null and req.searchKind == 'Mobile'">
                AND s.Mobile like '%' + #{req.searchValue} + '%'
            </when>
            <when test="req.searchKind != null and req.searchKind == 'Username'">
                AND s.Username like '%' + #{req.searchValue} + '%'
            </when>
            <when test="req.searchKind != null and req.searchKind == 'subId'">
                AND s.sub_id = #{req.searchValue}
            </when>
            <when test="req.searchKind != null and req.searchKind == 'userId'">
                AND s.userid = #{req.searchValue}
            </when>
            <otherwise></otherwise>
        </choose>
    </sql>
    <sql id="sale_after">
        <choose>
            <when test="req.whetherQueryByCurArea == true">
                AND EXISTS (SELECT 1 FROM dbo.ch999_user u WITH(nolock) WHERE t.inuser=u.ch999_name AND u.area1id=#{req.curAreaId} )
            </when>
            <otherwise>
                AND t.inuser = #{req.sellerName}
            </otherwise>
        </choose>
        <choose>
            <when test="req.searchKind != null and req.searchKind == '客户电话号码'">
                AND sh.mobile = #{req.searchValue}
            </when>
            <when test="req.searchKind != null and req.searchKind == '机型名称'">
                AND sh.name LIKE '%' + #{req.searchValue} + '%'
            </when>
            <when test="req.searchKind != null and req.searchKind == 'IMEI'">
                AND sh.imei LIKE '%' + #{req.searchValue} + '%'
            </when>
            <when test="req.searchKind != null and req.searchKind == '维修单号'">
                AND CAST(sh.id AS VARCHAR(50)) = #{req.searchValue}
            </when>
            <otherwise></otherwise>
        </choose>
        <choose>
            <when test="(req.subDateStart == null or req.subDateStart == '') and (req.subDateEnd != null and req.subDateEnd != '')">
                AND sh.modidate &lt;= #{req.subDateEnd}
            </when>
            <when test="(req.subDateStart != null and req.subDateStart != '') and (req.subDateEnd == null or req.subDateEnd == '')">
                AND sh.modidate &gt;= #{req.subDateStart}
            </when>
            <when test="(req.subDateStart != null and req.subDateStart != '') and (req.subDateEnd != null and req.subDateEnd != '')">
                AND sh.modidate BETWEEN #{req.subDateStart} AND #{req.subDateEnd}
            </when>
            <otherwise></otherwise>
        </choose>
        <if test="req.stats != null">
            AND sh.stats = #{req.stats}
        </if>
        <if test="req.quJi != null">
            AND ISNULL(sh.isquji,0) = #{req.whetherQuJi}
        </if>
    </sql>
    <sql id="dispatch_base">
        <if test="req.stats != null">
            AND s.stats = #{req.stats}
        </if>
        <choose>
            <when test="(req.subDateStart == null or req.subDateStart == '') and (req.subDateEnd != null and req.subDateEnd != '')">
                AND s.dtime &lt;= #{req.subDateEnd}
            </when>
            <when test="(req.subDateStart != null and req.subDateStart != '') and (req.subDateEnd == null or req.subDateEnd == '')">
                AND s.dtime &gt;= #{req.subDateStart}
            </when>
            <when test="(req.subDateStart != null and req.subDateStart != '') and (req.subDateEnd != null and req.subDateEnd != '')">
                AND s.dtime BETWEEN #{req.subDateStart} AND #{req.subDateEnd}
            </when>
            <otherwise></otherwise>
        </choose>
        <choose>
            <when test="req.searchKind != null and req.searchKind == '标题'">
                AND s.title LIKE '%' + #{req.searchValue} + '%'
            </when>
            <when test="req.searchKind != null and req.searchKind == '商品名称'">
                AND EXISTS (SELECT 1 FROM dbo.diaobo_basket b with(NOLOCK)
                LEFT JOIN dbo.productinfo pf with(nolock) ON pf.ppriceid = b.ppriceid
                WHERE b.sub_id = s.id AND pf.product_name LIKE '%' + #{req.searchValue} + '%')
            </when>
            <when test="req.searchKind != null and req.searchKind == 'PPID'">
                AND EXISTS (SELECT 1 FROM dbo.diaobo_basket b with(NOLOCK) WHERE b.sub_id = s.id AND b.ppriceid = #{req.searchValue})
            </when>
            <when test="req.searchKind != null and req.searchKind == '商品条码'">
                AND EXISTS (SELECT 1 FROM dbo.diaobo_basket b with(NOLOCK)
                LEFT JOIN dbo.productinfo pf with(nolock)  ON pf.ppriceid = b.ppriceid
                WHERE b.sub_id = s.id AND pf.barCode LIKE '%' + #{req.searchValue} + '%')
            </when>
            <when test="req.searchKind != null and req.searchKind == '调拨单号'">
                AND CAST(s.id AS VARCHAR(50)) = #{req.searchValue}
            </when>
            <otherwise></otherwise>
        </choose>
    </sql>
    <sql id="sale_after_appoint">
        <choose>
            <when test="req.whetherQueryByCurArea == true">
                AND yy.areaid = #{req.curAreaId}
            </when>
            <otherwise>
                AND ISNULL(yy.check_user, yy.enterUser) = #{req.sellerName}
            </otherwise>
        </choose>
        <choose>
            <when test="req.searchKind != null and req.searchKind == '客户电话号码'">
                AND yy.mobile = #{req.searchValue}
            </when>
            <when test="req.searchKind != null and req.searchKind == '机型名称'">
                AND yy.name LIKE '%' + #{req.searchValue} + '%'
            </when>
            <when test="req.searchKind != null and req.searchKind == 'IMEI'">
                AND yy.imei LIKE '%' + #{req.searchValue} + '%'
            </when>
            <when test="req.searchKind != null and req.searchKind == '预约单号'">
                AND CAST(yy.id AS VARCHAR(50)) = #{req.searchValue}
            </when>
            <when test="req.searchKind != null and req.searchKind == '维修单号'">
                AND yy.shouhou_id = #{req.searchValue}
            </when>
            <otherwise></otherwise>
        </choose>
        <choose>
            <when test="(req.subDateStart == null or req.subDateStart == '') and (req.subDateEnd != null and req.subDateEnd != '')">
                AND yy.dtime &lt;= #{req.subDateEnd}
            </when>
            <when test="(req.subDateStart != null and req.subDateStart != '') and (req.subDateEnd == null or req.subDateEnd == '')">
                AND yy.dtime &gt;= #{req.subDateStart}
            </when>
            <when test="(req.subDateStart != null and req.subDateStart != '') and (req.subDateEnd != null and req.subDateEnd != '')">
                AND yy.dtime BETWEEN #{req.subDateStart} AND #{req.subDateEnd}
            </when>
            <otherwise></otherwise>
        </choose>
        <if test="req.stats != null">
            AND yy.stats =#{req.stats}
        </if>
    </sql>

    <!-- smallOrderMap -->
    <resultMap id="SmallOrderMap" type="com.jiuji.oa.oacore.oaorder.bo.SmallOrderPageBO">
        <id column="id" property="subId"/>
        <result column="Stats" property="stats"/>
        <result column="areaid" property="areaId"/>
        <result column="Indate" property="subDate"/>
        <result column="Name" property="productName"/>
        <result column="Kind" property="kind"/>
        <result column="IsBaoxiu" property="baoXiu"/>
    </resultMap>

    <!-- DispatchOrderMap -->
    <resultMap id="DispatchOrderMap" type="com.jiuji.oa.oacore.oaorder.bo.DispatchOrderPageBO">
        <id column="id" property="subId"/>
        <result column="area" property="area"/>
        <result column="toarea" property="toArea"/>
        <result column="stats" property="stats"/>
        <result column="title" property="title"/>
        <result column="inuser" property="inUser"/>
        <result column="dtime" property="subDate"/>
        <result column="product_name" property="productName"/>
        <result column="product_color" property="productColor"/>
        <result column="lcount" property="lcount"/>
    </resultMap>

    <!-- SaleAfterOrderMap -->
    <resultMap id="SaleAfterOrderMap" type="com.jiuji.oa.oacore.oaorder.bo.SaleAfterOrderPageBO">
        <id column="id" property="subId"/>
        <result column="area" property="area"/>
        <result column="stats" property="stats"/>
        <result column="wxkind" property="wxKind"/>
        <result column="modidate" property="subDate"/>
        <result column="webtype2" property="webType"/>
        <result column="issoft" property="soft"/>
        <result column="name" property="productName"/>
        <result column="product_color" property="productColor"/>
        <result column="imei" property="imei"/>
        <result column="mobile" property="mobile"/>
    </resultMap>

    <!-- SaleAfterAppointOrderMap -->
    <resultMap id="SaleAfterAppointOrderMap" type="com.jiuji.oa.oacore.oaorder.bo.SaleAfterAppointOrderPageBO">
        <id column="id" property="subId"/>
        <result column="stats" property="stats"/>
        <result column="kind" property="kind"/>
        <result column="stype" property="serviceType"/>
        <result column="dtime" property="subDate"/>
        <result column="name" property="productName"/>
        <result column="color" property="productColor"/>
        <result column="imei" property="imei"/>
        <result column="area" property="area"/>
        <result column="mobile" property="mobile"/>
    </resultMap>

    <!-- PendingDeliveryOrderMap -->
    <resultMap id="PendingDeliveryOrderMap" type="com.jiuji.oa.oacore.oaorder.bo.PendingDeliveryOrderPageBO">
        <id column="sub_id" property="subId"/>
        <result column="sub_check" property="subCheck"/>
        <result column="delivery" property="delivery"/>
        <result column="sub_date" property="subDate"/>
        <result column="areaid" property="areaId"/>
        <result column="product_name" property="productName"/>
        <result column="product_color" property="productColor"/>
        <result column="price" property="price"/>
        <result column="basket_count" property="basketCount"/>
        <result column="area" property="area"/>
        <result column="subType" property="subType"/>
    </resultMap>

    <select id="getSaleProductInfo" resultType="com.jiuji.oa.oacore.oaorder.vo.res.OrderInfoVO" timeout="30000">
        SELECT a.*, pf.product_name, pf.product_color, b.price,pf.ppriceid,b.basket_id as basketId,b.ismobile, b.basket_count, c.area
        FROM (
                 SELECT
                     s.sub_id, s.sub_check, s.delivery, s.sub_date, s.areaid,s.expectTime,s.tradeDate1
                 FROM dbo.sub s WITH(NOLOCK)
                    INNER JOIN
                    (
                    SELECT distinct
                        <if test="params.subCheck !=null and params.subCheck == 3">
                            top 100
                        </if>
                        s.sub_id,s.tradeDate1
                    FROM dbo.sub s
                        <choose>
                            <when test="kind == 1 || kind == 3">
                                WITH(NOLOCK, index(PK_sub))
                            </when>
                            <otherwise>WITH(NOLOCK)</otherwise>
                        </choose>
                    INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
                    LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
                    <where>
                        <choose>
                            <when test="params.subCheck !=null">
                                AND s.sub_check = #{params.subCheck}
                                <if test="params.subCheck == 3">
                                    AND s.tradeDate1 >= DATEADD(MONTH, -3, GETDATE())
                                </if>
                            </when>
                            <otherwise>
                                and s.sub_check IN (0,1,2,6,5,7)
                            </otherwise>
                        </choose>
                        AND ISNULL(b.isdel,0) = 0
                        <if test="params.whetherHidePresale == true">
                            AND NOT EXISTS(SELECT 1 FROM basket bet with(nolock) inner join productinfo pfo with(nolock)
                            on pfo.ppriceid = bet.ppriceid where bet.sub_id = s.sub_id and pfo.saleStartTime &gt; GETDATE()
                            and ISNULL(bet.isdel,0) = 0)
                        </if>
                    <include refid="sale_base"></include>
                    </where>
                    <if test="params.subCheck !=null and params.subCheck == 3">
                        ORDER BY s.tradeDate1 DESC
                    </if>
                    ) t ON t.sub_id = s.sub_id
             ) a
            LEFT JOIN dbo.basket b WITH(NOLOCK) ON a.sub_id = b.sub_id
            <if test="kind == 1 and userName !=null" >
                AND b.seller = #{userName}
            </if>
            LEFT JOIN dbo.productinfo pf   WITH (NOLOCK) ON pf.ppriceid = b.ppriceid
            LEFT JOIN dbo.areainfo c  WITH (NOLOCK)  ON c.id = a.areaid
            <where>
                <if test="params.searchKind != null and params.searchKind == '商品名称'">
                    AND pf.product_name LIKE '%' + #{params.searchValue} + '%'
                </if>
                AND ISNULL(b.isdel,0) = 0
            </where>
    </select>

    <select id="getSearchProduct" resultType="com.jiuji.oa.oacore.oaorder.vo.res.OrderInfoVO">
        SELECT a.*, pf.product_name, pf.product_color,pf.ppriceid, b.price, b.basket_count,pf.ppriceid,b.basket_id as basketId,b.ismobile ,c.area
        FROM (
            SELECT s.sub_id, s.sub_check, s.delivery, s.sub_date, s.areaid
            FROM dbo.sub s WITH(NOLOCK)
            WHERE s.sub_id IN
            <foreach collection="idList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        ) a
        LEFT JOIN dbo.basket b WITH(NOLOCK) ON a.sub_id = b.sub_id
        LEFT JOIN dbo.productinfo pf   WITH (NOLOCK) ON pf.ppriceid = b.ppriceid
        LEFT JOIN dbo.areainfo c  WITH (NOLOCK)  ON c.id = a.areaid
    </select>

    <select id="getSaleProductGroup" resultType="com.jiuji.oa.oacore.oaorder.bo.SubNoticeNumber">

        SELECT s.sub_check kind,COUNT(DISTINCT s.sub_id) subNumber FROM dbo.sub s WITH(NOLOCK,index(PK_sub))
                INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
        <where>
            s.sub_check in(0, 1, 2, 5, 6) AND ISNULL(b.isdel,0) = 0
            <include refid="sale_base"></include>
            group by s.sub_check
        </where>
        UNION
        SELECT 3 kind,COUNT(DISTINCT s.sub_id) subNumber FROM dbo.sub s WITH(NOLOCK,index(PK_sub))
        INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
        <where>
            s.sub_check = 3 AND ISNULL(b.isdel,0) = 0 AND s.tradeDate1 >= DATEADD(MONTH, -3, GETDATE())
            <include refid="sale_base"></include>
        </where>

<!--        UNION-->
<!--        SELECT 7 kind,COUNT(DISTINCT s.sub_id) subNumber FROM dbo.sub s WITH(NOLOCK)-->
<!--                INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id-->
<!--        <where>-->
<!--            s.sub_check = 7 AND ISNULL(b.isdel,0) = 0-->
<!--            <include refid="sale_base"></include>-->
<!--        </where>-->
    </select>

    <select id="countSaleOrder" resultType="java.lang.Integer">
        SELECT count(distinct s.sub_id)
        FROM dbo.sub s
            <choose>
                <when test="kind == 1 || kind == 3">
                    WITH(NOLOCK, index(PK_sub))
                </when>
                <otherwise>
                    WITH(NOLOCK)
                </otherwise>
            </choose>

        INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON
        p.ppriceid = b.ppriceid
        <where>
            ISNULL(b.isdel,0) = 0
            <choose>
                <when test="params.subCheck !=null">
                    AND s.sub_check = #{params.subCheck}
                    <if test="params.subCheck == 3">
                        AND s.tradeDate1 >= DATEADD(MONTH, -3, GETDATE())
                    </if>
                </when>
                <otherwise>
                    AND s.sub_check in(0,1,2,6,5,7)
                </otherwise>
            </choose>
            <if test="params.whetherHidePresale == true">
                AND NOT EXISTS(SELECT 1 FROM basket bet with(nolock) inner join productinfo pfo with(nolock)
                on pfo.ppriceid = bet.ppriceid where bet.sub_id = s.sub_id and pfo.saleStartTime &gt; GETDATE()
                and ISNULL(bet.isdel,0) = 0)
            </if>
            <if test="params.searchKind != null and params.searchKind == '商品名称'">
                AND EXISTS (SELECT 1 FROM dbo.productinfo pf with(NOLOCK) WHERE pf.ppriceid = b.ppriceid AND pf.product_name LIKE '%' + #{params.searchValue} + '%')
            </if>
            <include refid="sale_base"></include>
        </where>
    </select>

    <select id="getConfirmedStock" resultType="com.jiuji.oa.oacore.oaorder.vo.res.OrderInfoVO">
        SELECT s.sub_id,s.sub_check,s.delivery,s.sub_date,s.areaid,pf.product_name,pf.product_color,b.price,b.basket_count,c.area
        FROM dbo.sub s WITH(NOLOCK)
		    LEFT JOIN dbo.basket b WITH(NOLOCK) ON s.sub_id = b.sub_id
            LEFT JOIN dbo.productinfo pf WITH(NOLOCK) ON pf.ppriceid = b.ppriceid
            LEFT JOIN dbo.areainfo c WITH(NOLOCK) ON c.id = s.areaid
        <where>
            s.sub_check = 1
            AND ISNULL(b.isdel,0) = 0
            <choose>
                <when test="params.kcStats == -2">
                    AND NOT EXISTS (SELECT 1 FROM dbo.product_mkc mkc with(NOLOCK) WHERE mkc.basket_id =
                    b.basket_id)
                </when>
                <when test="params.kcStats >= 0">
                    AND EXISTS (SELECT 1 FROM dbo.product_mkc mkc with(NOLOCK) WHERE mkc.basket_id = b.basket_id AND
                    mkc.kc_check = #{params.kcStats})
                </when>
                <otherwise>
                    AND 1 = 1
                </otherwise>
            </choose>
            <include refid="confirmed_stock"></include>
        </where>
    </select>

    <select id="getConfirmedStockGroup" resultType="com.jiuji.oa.oacore.oaorder.bo.SubNoticeNumber">
        SELECT -2 kind,COUNT(DISTINCT s.sub_id) subNumber FROM dbo.sub s WITH(NOLOCK)
            INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
        <where>
            s.sub_check IN (1)
            AND ISNULL(b.isdel,0) = 0
            AND NOT EXISTS (SELECT 1 FROM dbo.product_mkc mkc with(NOLOCK) WHERE mkc.basket_id = b.basket_id)
            <include refid="confirmed_stock"></include>
        </where>
        UNION
        SELECT 0 kind,COUNT(DISTINCT s.sub_id) subNumber FROM dbo.sub s WITH(NOLOCK)
        INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
        LEFT JOIN dbo.product_mkc mkc with(NOLOCK) ON mkc.basket_id = b.basket_id
        <where>
            s.sub_check IN (1)
            AND ISNULL(b.isdel,0) = 0
            AND mkc.kc_check= 0
            <include refid="confirmed_stock"></include>
        </where>
        UNION
        SELECT 1 kind,COUNT(DISTINCT s.sub_id) subNumber FROM dbo.sub s WITH(NOLOCK)
        INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
        LEFT JOIN dbo.product_mkc mkc with(NOLOCK) ON mkc.basket_id = b.basket_id
        <where>
            s.sub_check IN (1)
            AND ISNULL(b.isdel,0) = 0
            AND mkc.kc_check= 1
            <include refid="confirmed_stock"></include>
        </where>
        UNION
        SELECT 2 kind,COUNT(DISTINCT s.sub_id) subNumber FROM dbo.sub s WITH(NOLOCK)
        INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
        LEFT JOIN dbo.product_mkc mkc with(NOLOCK) ON mkc.basket_id = b.basket_id
        <where>
            s.sub_check IN (1)
            AND ISNULL(b.isdel,0) = 0
            AND mkc.kc_check= 2
            <include refid="confirmed_stock"></include>
        </where>
        UNION
        SELECT 3 kind,COUNT(DISTINCT s.sub_id) subNumber FROM dbo.sub s WITH(NOLOCK)
        INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
        LEFT JOIN dbo.product_mkc mkc with(NOLOCK) ON mkc.basket_id = b.basket_id
        <where>
            s.sub_check IN (1)
            AND ISNULL(b.isdel,0) = 0
            AND mkc.kc_check= 3
            <include refid="confirmed_stock"></include>
        </where>
        UNION
        SELECT 10 kind,COUNT(DISTINCT s.sub_id) subNumber FROM dbo.sub s WITH(NOLOCK)
        INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
        LEFT JOIN dbo.product_mkc mkc with(NOLOCK) ON mkc.basket_id = b.basket_id
        <where>
            s.sub_check IN (1)
            AND ISNULL(b.isdel,0) = 0
            AND mkc.kc_check= 10
            <include refid="confirmed_stock"></include>
        </where>
    </select>

    <select id="getGoodProductInfo" resultType="com.jiuji.oa.oacore.oaorder.vo.res.OrderInfoVO">
        SELECT a.*,pf.product_name,pf.product_color,b.price,b.basket_count,c.area,b.basket_id as basketId
        FROM
        (
        SELECT s.sub_id,s.sub_check,s.delivery,s.sub_date,s.areaid
        FROM dbo.recover_marketInfo s WITH(NOLOCK)
        INNER JOIN
        (
            SELECT rmi.sub_id
            FROM dbo.recover_marketInfo rmi WITH(NOLOCK)
            INNER JOIN dbo.recover_marketSubInfo rms WITH(NOLOCK) ON rms.sub_id = rmi.sub_id
            <where>
                rmi.sub_check IN (0,1,2,6,5,7)
                AND ISNULL(rms.isdel,0) = 0
                AND (ISNULL(rmi.saleType,0) = 0 OR rmi.sub_to in ('内部购买渠道','内部借用渠道') OR DATEDIFF(HOUR,rmi.sub_date,GETDATE())>= 72)
                <if test="params.kcStats !=null">
                    AND EXISTS(SELECT 1 FROM dbo.recover_mkc mkc with (NOLOCK) WHERE mkc.to_basket_id = rms.basket_id AND mkc.mkc_check = #{params.kcStats})
                </if>
                <include refid="good_product_base"/>
            </where>

            GROUP BY rmi.sub_id
            ) t ON t.sub_id = s.sub_id
        ) a
        LEFT JOIN dbo.recover_marketSubInfo b with(nolock) ON a.sub_id = b.sub_id
        LEFT JOIN dbo.productinfo pf with(nolock) ON pf.ppriceid = b.ppriceid
        LEFT JOIN dbo.areainfo c WITH(NOLOCK) ON c.id = a.areaid
        WHERE ISNULL(b.isdel,0) = 0
    </select>

    <select id="getGoodProductGroup" resultType="com.jiuji.oa.oacore.oaorder.bo.SubNoticeNumber">
        SELECT 0 kind,COUNT(DISTINCT rmi.sub_id) as subNumber FROM dbo.recover_marketInfo rmi WITH(NOLOCK)
        INNER JOIN dbo.recover_marketSubInfo rms WITH(NOLOCK) ON rms.sub_id = rmi.sub_id
        <where>
            rmi.sub_check = 0 AND ISNULL(rms.isdel,0) = 0
            AND (ISNULL(rmi.saleType,0) = 0 OR DATEDIFF(HOUR,rmi.sub_date,GETDATE())>= 72)
            <include refid="good_product_base"/>
        </where>

        UNION
        SELECT 1,COUNT(DISTINCT rmi.sub_id) FROM dbo.recover_marketInfo rmi WITH(NOLOCK)
        INNER JOIN dbo.recover_marketSubInfo rms WITH(NOLOCK) ON rms.sub_id = rmi.sub_id
        <where>
            rmi.sub_check = 1 AND ISNULL(rms.isdel,0) = 0
            AND (ISNULL(rmi.saleType,0) = 0 OR DATEDIFF(HOUR,rmi.sub_date,GETDATE())>= 72)
            <include refid="good_product_base"/>
        </where>
        UNION
        SELECT 2,COUNT(DISTINCT rmi.sub_id) FROM dbo.recover_marketInfo rmi WITH(NOLOCK)
        INNER JOIN dbo.recover_marketSubInfo rms WITH(NOLOCK) ON rms.sub_id = rmi.sub_id
        <where>
            rmi.sub_check = 2 AND ISNULL(rms.isdel,0) = 0
            AND (ISNULL(rmi.saleType,0) = 0 OR DATEDIFF(HOUR,rmi.sub_date,GETDATE())>= 72)
            <include refid="good_product_base"/>
        </where>
<!--        UNION-->
<!--        SELECT 3,COUNT(DISTINCT rmi.sub_id) FROM dbo.recover_marketInfo rmi WITH(NOLOCK)-->
<!--        INNER JOIN dbo.recover_marketSubInfo rms WITH(NOLOCK) ON rms.sub_id = rmi.sub_id-->
<!--        <where>-->
<!--            rmi.sub_check = 3 AND ISNULL(rms.isdel,0) = 0-->
<!--            AND (ISNULL(rmi.saleType,0) = 0 OR DATEDIFF(HOUR,rmi.sub_date,GETDATE())>= 72)-->
<!--            <include refid="good_product_base"/>-->
<!--        </where>-->
        UNION
        SELECT 6,COUNT(DISTINCT rmi.sub_id) FROM dbo.recover_marketInfo rmi WITH(NOLOCK)
        INNER JOIN dbo.recover_marketSubInfo rms WITH(NOLOCK) ON rms.sub_id = rmi.sub_id
        <where>
            rmi.sub_check = 6 AND ISNULL(rms.isdel,0) = 0
            AND (ISNULL(rmi.saleType,0) = 0 OR DATEDIFF(HOUR,rmi.sub_date,GETDATE())>= 72)
            <include refid="good_product_base"/>
        </where>
        UNION
        SELECT 5,COUNT(DISTINCT rmi.sub_id) FROM dbo.recover_marketInfo rmi WITH(NOLOCK)
        INNER JOIN dbo.recover_marketSubInfo rms WITH(NOLOCK) ON rms.sub_id = rmi.sub_id
        <where>
            rmi.sub_check = 5 AND ISNULL(rms.isdel,0) = 0
            AND (ISNULL(rmi.saleType,0) = 0 OR DATEDIFF(HOUR,rmi.sub_date,GETDATE())>= 72)
            <include refid="good_product_base"/>
        </where>
<!--        UNION-->
<!--        SELECT 7,COUNT(DISTINCT rmi.sub_id) FROM dbo.recover_marketInfo rmi WITH(NOLOCK)-->
<!--        INNER JOIN dbo.recover_marketSubInfo rms WITH(NOLOCK) ON rms.sub_id = rmi.sub_id-->
<!--        <where>-->
<!--            rmi.sub_check = 7 AND ISNULL(rms.isdel,0) = 0-->
<!--            AND (ISNULL(rmi.saleType,0) = 0 OR DATEDIFF(HOUR,rmi.sub_date,GETDATE())>= 72)-->
<!--            <include refid="good_product_base"/>-->
<!--        </where>-->
    </select>

    <select id="countGoodProductOrder" resultType="java.lang.Integer">
        SELECT count(distinct rmi.sub_id)
        FROM dbo.recover_marketInfo rmi WITH(NOLOCK)
        INNER JOIN dbo.recover_marketSubInfo rms WITH(NOLOCK) ON rms.sub_id = rmi.sub_id
        <where>
            AND ISNULL(rms.isdel,0) = 0
            AND rmi.sub_check IN (0,1,2,6,5,7)
            AND (ISNULL(rmi.saleType,0) = 0 OR DATEDIFF(HOUR,rmi.sub_date,GETDATE())>= 72)
            AND EXISTS(SELECT 1 FROM dbo.recover_mkc mkc with (NOLOCK) WHERE issalf = 1 and mkc.to_basket_id = rms.basket_id and  mkc.mkc_check = #{params.kcStats})
            <include refid="good_product_base"/>
        </where>
    </select>

    <select id="getRecycleInfo" resultType="com.jiuji.oa.oacore.oaorder.vo.res.OrderInfoVO">
        select s.sub_id,s.sub_check,s.dtime sub_date,a.area,pf.product_name,pf.product_color,b.price from dbo.recover_basket b with(nolock)
        inner join dbo.recover_sub s with(nolock) on s.sub_id = b.sub_id
        left join dbo.productinfo pf with(nolock)  ON pf.ppriceid = b.ppriceid
        LEFT JOIN dbo.areainfo a WITH(NOLOCK) ON a.id = s.areaid
        <where>
             ISNULL(b.isdel,0) = 0
            <include refid="recycle_base"/>
        </where>
    </select>

    <select id="getRecycleGroup" resultType="com.jiuji.oa.oacore.oaorder.bo.SubNoticeNumber">
        select 1 as kind,count(distinct b.sub_id) as subNumber,2 as rank from dbo.recover_basket b with(nolock)
        inner join dbo.recover_sub s with(nolock) on s.sub_id = b.sub_id
        <where>
            s.sub_check = 1 and ISNULL(b.isdel,0) = 0
            <include refid="recycle_base"/>
        </where>
        UNION
        select 2 as kind ,count(distinct b.sub_id)as subNumber,4 as rank from dbo.recover_basket b with(nolock)
        inner join dbo.recover_sub s with(nolock) on s.sub_id = b.sub_id
        <where>
            s.sub_check = 2 and ISNULL(b.isdel,0) = 0
            <include refid="recycle_base"/>
        </where>

        UNION
        select 5 as kind ,count(distinct b.sub_id)as subNumber,3 as rank from dbo.recover_basket b with(nolock)
        inner join dbo.recover_sub s with(nolock) on s.sub_id = b.sub_id
        <where>
            s.sub_check = 5 and ISNULL(b.isdel,0) = 0
            <include refid="recycle_base"/>
        </where>
        UNION
        select 0 as kind ,count(distinct b.sub_id)as subNumber,1 as rank from dbo.recover_basket b with(nolock)
        inner join dbo.recover_sub s with(nolock) on s.sub_id = b.sub_id
        <where>
            s.sub_check = 0 and ISNULL(b.isdel,0) = 0
            <include refid="recycle_base"/>
        </where>


    </select>

    <!-- listOrderPanel -->
    <select id="listOrderPanel" resultType="com.jiuji.oa.oacore.oaorder.vo.res.OrderPanelVO">
        SELECT 1 kind, COUNT(DISTINCT s.sub_id) subNumber FROM dbo.sub s WITH(NOLOCK)
        INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
        WHERE s.sub_check IN (0,1,2,6,5,7) AND ISNULL(b.isdel,0) = 0
        <choose>
            <when test="whetherQueryByCurArea == true">
                AND s.areaid= #{areaId}
            </when>
            <otherwise>
                AND b.seller = #{ch999name}
            </otherwise>
        </choose>
        UNION
        SELECT 2 AS kind, COUNT(DISTINCT rmi.sub_id) AS subNumber  FROM dbo.recover_marketInfo rmi WITH(NOLOCK)
        INNER JOIN dbo.recover_marketSubInfo rms WITH(NOLOCK) ON rms.sub_id = rmi.sub_id
        WHERE rmi.sub_check IN (0,1,2,6,5,7) AND ISNULL(rms.isdel,0) = 0
        <choose>
            <when test="whetherQueryByCurArea == true">
                AND rmi.areaid=#{areaId}
            </when>
            <otherwise>
                AND rms.seller = #{ch999name}
            </otherwise>
        </choose>
        AND (ISNULL(rmi.saleType,0) = 0 OR rmi.sub_to in ('内部购买渠道','内部借用渠道') OR DATEDIFF(HOUR,rmi.sub_date,GETDATE())>= 72)
        UNION
        SELECT 3 AS kind, COUNT(DISTINCT yy.id) AS subNumber FROM dbo.shouhou_yuyue yy WITH(NOLOCK)
        WHERE yy.stats IN (2,4) AND ISNULL(yy.isdel,0) = 0 AND ISNULL(yy.iszy,0) = 0
        <choose>
            <when test="whetherQueryByCurArea == true">
                AND yy.areaid=#{areaId}
            </when>
            <otherwise>
                AND ISNULL(yy.check_user,yy.enterUser) = #{ch999name}
            </otherwise>
        </choose>
        UNION
        SELECT 7 AS kind, count(1) AS subNumber
        FROM dbo.Smallpro s WITH(nolock)
        WHERE
        isnull(s.isdel, 0) = 0
        and ((s.Stats = 0 AND isnull(s.wxState, 0) = 0)
        or s.Stats = 3
        or (s.Stats = 0 and s.wxState = 1)
        or (s.Stats = 0 and s.wxState = 2))
        <choose>
            <when test="whetherQueryByCurArea == true">
                AND s.areaid=#{areaId}
            </when>
            <otherwise>
                AND s.Inuser=#{ch999name}
            </otherwise>
        </choose>
        UNION
        SELECT 8 AS kind, count(DISTINCT b.sub_id) AS subNumber FROM dbo.recover_basket b WITH(nolock)
        INNER JOIN dbo.recover_sub s WITH(nolock) ON s.sub_id = b.sub_id
        WHERE s.sub_check IN (1,2,5) AND ISNULL(b.isdel,0) = 0
        <choose>
            <when test="whetherQueryByCurArea == true">
                AND s.areaid=#{areaId}
            </when>
            <otherwise>
                AND b.inuser = #{ch999name}
            </otherwise>
        </choose>
        UNION
        select 8 as kind, count(distinct b.sub_id) AS subNumber
        from dbo.recover_basket b with (nolock)
        inner join dbo.recover_sub s with (nolock) on s.sub_id = b.sub_id
        inner join dbo.recover_sub_extend se with(nolock) on se.sub_id = b.sub_id
        where s.sub_check = 5 and s.sub_delivery = 2
        <choose>
            <when test="whetherQueryByCurArea == true">
                AND s.areaid=#{areaId}
            </when>
            <otherwise>
                AND b.checkUser = #{ch999name}
            </otherwise>
        </choose>
        and ISNULL(b.isdel, 0) = 0
        and b.checkUser != b.inuser
        and (s.express_recover_status =2 OR se.signature is not null)
        UNION
        SELECT 4 AS kind, count(1) AS subNumber FROM(
            SELECT distinct t.id
            FROM
            (
                SELECT sh.id,so.inuser,so.mark,so.timeoutdate,
                    row_number() over(partition by so.shouhouid,so.mark order by so.timeoutdate desc) rn
                FROM dbo.shouhou sh WITH(nolock)
                    INNER JOIN dbo.shouhou_other so WITH(nolock) ON sh.id = so.shouhouid
                WHERE isnull(sh.isquji,0) = 0
                    AND isnull(sh.xianshi,0)= 1
                    AND so.kind = 3
                    AND isnull(sh.iszy,0) = 0
                    AND NOT EXISTS ( SELECT 1 FROM dbo.shouhou_qudao sq WITH(nolock)
                                     WHERE sq.shouhouid = sh.id)
            ) t
            WHERE
                (
                (t.mark = 0 AND DATEDIFF(HOUR,t.timeoutdate,GETDATE()) &gt;= 72)
                OR
                (t.mark = 1 AND DATEDIFF(HOUR,t.timeoutdate,GETDATE()) &gt;= 24)
                )
                AND t.rn = 1
            <choose>
                <when test="whetherQueryByCurArea == true">
                    AND EXISTS (SELECT 1 FROM dbo.ch999_user u WITH(nolock) WHERE t.inuser=u.ch999_name AND u.area1id=#{areaId} )
                </when>
                <otherwise>
                    AND t.inuser = #{ch999name}
                </otherwise>
            </choose>
        )ss
    </select>

    <!-- listSmallOrder -->
    <select id="listSmallOrder" resultMap="SmallOrderMap">
        SELECT s.id, s.Stats, s.areaid, s.Indate, s.Name, s.Kind, s.IsBaoxiu
        FROM dbo.Smallpro s with(nolock)
       <where>
           isnull(s.isdel,0)=0
           <choose>
               <when test="req.stats != null and req.stats == 0">
                   AND ( s.Stats=0 AND isnull(s.wxState,0) = 0)
               </when>
               <when test="req.stats != null and req.stats == 3">
                   AND s.Stats=3
               </when>
               <when test="req.stats != null and req.stats == 5">
                   AND s.Stats=0 and s.wxState=1
               </when>
               <when test="req.stats != null and req.stats == 6">
                   AND s.Stats=0 and s.wxState=2
               </when>
               <otherwise>
                   AND (s.Stats=3 OR ( s.stats=0 AND isnull(s.wxState,0) = 0))
               </otherwise>
           </choose>
            <include refid="small_base"></include>
       </where>
    </select>

    <select id="getSmallOrderGroup" resultType="com.jiuji.oa.oacore.oaorder.bo.SubNoticeNumber">
        select 0 as kind,count(1) as subNumber from dbo.Smallpro s with(nolock)
        <where>
            s.Stats = 0 and isnull(wxState,0) = 0  and isnull(s.isdel,0) = 0
            <include refid="small_base"></include>
        </where>
        union
        select 3 as kind,count(1) as subNumber from dbo.Smallpro s with(nolock)
        <where>
            s.Stats=3  and isnull(s.isdel,0) = 0
            <include refid="small_base"></include>
        </where>
        union
        select 5 as kind,count(1) as subNumber from dbo.Smallpro s with(nolock)
        <where>
            s.Stats=0 and s.wxState=1   and isnull(s.isdel,0) = 0
            <include refid="small_base"></include>
        </where>
        union
        select 6 as kind,count(1) as subNumber from dbo.Smallpro s with(nolock)
        <where>
            s.Stats=0 and s.wxState=2 and isnull(s.isdel,0) = 0
            <include refid="small_base"></include>
        </where>
    </select>
    <!-- listDispatchOrder -->
    <select id="listDispatchOrder" resultMap="DispatchOrderMap">
        SELECT TOP 100 PERCENT s.id, a1.area, a2.area toarea, s.stats, s.title, s.inuser, s.dtime,
               pf.product_name, pf.product_color, b.lcount
        FROM
            (
                SELECT st.* FROM
                (
                    SELECT s.id FROM dbo.diaobo_sub s WITH(NOLOCK)
                    INNER JOIN dbo.ch999_user u with(nolock)  ON u.area1id = s.areaid
                    WHERE s.kinds = 'wx' AND s.stats IN (1,2,5) AND u.zhiwu = '店长' AND u.iszaizhi = 1 AND u.ch999_name = #{req.sellerName}
                    UNION
                    SELECT s.id FROM dbo.diaobo_sub s WITH(NOLOCK)
                    INNER JOIN dbo.ch999_user u with(nolock)  ON u.area1id = s.toareaid
                    WHERE s.kinds = 'wx' AND s.stats IN (3) AND u.zhiwu = '店长' AND u.iszaizhi = 1 AND u.ch999_name = #{req.sellerName}
                ) st
                ORDER BY st.id DESC
                OFFSET #{req.offset} ROWS FETCH NEXT #{req.pageSize} ROWS ONLY
            ) t
            INNER JOIN dbo.diaobo_sub s WITH(NOLOCK) ON t.id = s.id
            LEFT JOIN dbo.areainfo a1 with(nolock)  ON a1.id = s.areaid
            LEFT JOIN dbo.areainfo a2 with(nolock) ON a2.id = s.toareaid
            LEFT JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
            LEFT JOIN dbo.productinfo pf WITH(NOLOCK) ON pf.ppriceid = b.ppriceid
        WHERE s.kinds = 'wx'
        <include refid="dispatch_base"></include>
    </select>

    <!-- countDispatchOrder -->
    <select id="countDispatchOrder" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT s.id)
        FROM
            (
                SELECT s.id FROM dbo.diaobo_sub s WITH(NOLOCK)
                INNER JOIN dbo.ch999_user u with(nolock) ON u.area1id = s.areaid
                WHERE s.kinds = 'wx' AND s.stats IN (1,2,5) AND u.zhiwu = '店长' AND u.iszaizhi = 1 AND u.ch999_name = #{req.sellerName}
                UNION
                SELECT s.id FROM dbo.diaobo_sub s WITH(NOLOCK)
                INNER JOIN dbo.ch999_user u with(nolock) ON u.area1id = s.toareaid
                WHERE s.kinds = 'wx' AND s.stats IN (3) AND u.zhiwu = '店长' AND u.iszaizhi = 1 AND u.ch999_name = #{req.sellerName}
            ) t
            INNER JOIN dbo.diaobo_sub s WITH(NOLOCK) ON t.id = s.id
            LEFT JOIN dbo.areainfo a1 with(nolock) ON a1.id = s.areaid
            LEFT JOIN dbo.areainfo a2 with(nolock) ON a2.id = s.toareaid
            LEFT JOIN dbo.diaobo_basket b WITH(NOLOCK) ON s.id = b.sub_id
            LEFT JOIN dbo.productinfo pf WITH(NOLOCK) ON pf.ppriceid = b.ppriceid
        WHERE s.kinds = 'wx'
        <include refid="dispatch_base"></include>
    </select>

    <select id="getDispatchGroup" resultType="com.jiuji.oa.oacore.oaorder.bo.SubNoticeNumber">
        SELECT 1 as kind ,COUNT(DISTINCT s.id) as subNumber,1 as rank FROM dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.ch999_user u with(nolock) ON u.area1id = s.areaid
        <where>
            s.kinds = 'wx' AND s.stats IN (1) AND u.zhiwu = '店长' AND u.iszaizhi =1
            AND u.ch999_name = #{req.sellerName}
            <include refid="dispatch_base"></include>
        </where>
        UNION
        SELECT 2 as kind , COUNT(DISTINCT s.id) as subNumber,2 as rank FROM dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.ch999_user u with(nolock) ON u.area1id = s.areaid
        <where>
            s.kinds = 'wx' AND s.stats IN  (2) AND u.zhiwu = '店长' AND u.iszaizhi =1
            AND u.ch999_name = #{req.sellerName}
            <include refid="dispatch_base"></include>
        </where>
        UNION
        SELECT 6 as kind ,COUNT(DISTINCT s.id) as subNumber,3 as rank FROM dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.ch999_user u with(nolock) ON u.area1id = s.toareaid
        <where>
            s.kinds = 'wx' AND s.stats IN (6) AND u.zhiwu = '店长' AND u.iszaizhi = 1
            AND u.ch999_name = #{req.sellerName}
            <include refid="dispatch_base"></include>
        </where>
        UNION
        SELECT 5 as kind ,COUNT(DISTINCT s.id) as subNumber,4 as rank FROM dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.ch999_user u with(nolock) ON u.area1id = s.areaid
        <where>
            s.kinds = 'wx' AND s.stats IN  (5) AND u.zhiwu = '店长' AND u.iszaizhi =1
            AND u.ch999_name = #{req.sellerName}
            <include refid="dispatch_base"></include>
        </where>
        UNION
        SELECT 3 as kind ,COUNT(DISTINCT s.id) as subNumber,5 as rank FROM dbo.diaobo_sub s WITH(NOLOCK)
        INNER JOIN dbo.ch999_user u with(nolock) ON u.area1id = s.toareaid
        <where>
            s.kinds = 'wx' AND s.stats IN (3) AND u.zhiwu = '店长' AND u.iszaizhi = 1
            AND u.ch999_name = #{req.sellerName}
            <include refid="dispatch_base"></include>
        </where>
    </select>

    <!-- listSaleAfterOrder -->
    <select id="listSaleAfterOrder" resultMap="SaleAfterOrderMap">
        SELECT Distinct sh.id, a.area, sh.stats, sh.wxkind, sh.modidate, sh.webtype2, isnull(sh.issoft, 0) as issoft,
                sh.name, sh.product_color, sh.imei, sh.mobile
        FROM (
            SELECT sh.id, so.inuser, so.mark, so.timeoutdate,
                    ROW_NUMBER() OVER (PARTITION BY so.shouhouid,so.mark ORDER BY so.timeoutdate DESC) rn
            FROM dbo.shouhou sh WITH (NOLOCK)
                INNER JOIN dbo.shouhou_other so WITH (NOLOCK) ON sh.id = so.shouhouid
            WHERE ISNULL(sh.isquji, 0) = 0
                AND ISNULL(sh.xianshi, 0) = 1
                AND so.kind = 3
                AND ISNULL(sh.iszy, 0) = 0
                AND NOT EXISTS(SELECT 1 FROM dbo.shouhou_qudao sq with (nolock) WHERE sq.shouhouid = sh.id)
                AND so.timeoutdate &lt;= dateadd(hour, -24, getdate())
        ) t
            INNER JOIN dbo.shouhou sh WITH (NOLOCK) ON t.id = sh.id and t.rn = 1
            LEFT JOIN dbo.areainfo a WITH (NOLOCK) ON a.id = sh.areaid
        <where>
            (
                (t.mark = 0 AND DATEDIFF(HOUR,t.timeoutdate,GETDATE()) &gt;= 72)
                OR
                (t.mark = 1 AND DATEDIFF(HOUR,t.timeoutdate,GETDATE()) &gt;= 24)
            )
            <include refid="sale_after"></include>
        </where>
        ORDER BY sh.id DESC
        OFFSET #{req.offset} ROWS FETCH NEXT #{req.pageSize} ROWS ONLY
    </select>

    <!-- countSaleAfterOrder -->
    <select id="countSaleAfterOrder" resultType="java.lang.Long">
        SELECT count(distinct sh.id)
        FROM
        (
            SELECT sh.id, so.inuser, so.mark, so.timeoutdate,
                ROW_NUMBER() OVER(PARTITION BY so.shouhouid,so.mark ORDER BY so.timeoutdate DESC) rn
            FROM dbo.shouhou sh WITH(NOLOCK)
                INNER JOIN dbo.shouhou_other so WITH(NOLOCK) ON sh.id = so.shouhouid
            WHERE ISNULL(sh.isquji,0) = 0
                AND ISNULL(sh.xianshi,0)= 1
                AND so.kind = 3
                AND ISNULL(sh.iszy,0) = 0
            AND NOT EXISTS( SELECT 1 FROM dbo.shouhou_qudao sq with(nolock) WHERE sq.shouhouid = sh.id)
            AND so.timeoutdate &lt;= dateadd(hour,-24,getdate())
        ) t
            INNER JOIN dbo.shouhou sh WITH(NOLOCK) ON t.id = sh.id AND t.rn = 1
            LEFT JOIN dbo.areainfo a WITH(NOLOCK) ON a.id = sh.areaid
        WHERE
             (
                (t.mark = 0 AND DATEDIFF(HOUR,t.timeoutdate,GETDATE()) &gt;= 72)
                OR
                (t.mark = 1 AND DATEDIFF(HOUR,t.timeoutdate,GETDATE()) &gt;= 24)
            )
            <include refid="sale_after"></include>
    </select>

    <select id="getSaleAfterGroup" resultType="com.jiuji.oa.oacore.oaorder.bo.SubNoticeNumber">
        SELECT 0 as kind, count(distinct sh.id) as subNumber ,1 as rank
        FROM
        (
            SELECT sh.id, so.inuser, so.mark, so.timeoutdate,
                ROW_NUMBER() OVER(PARTITION BY so.shouhouid,so.mark ORDER BY so.timeoutdate DESC) rn
            FROM dbo.shouhou sh WITH(NOLOCK)
            INNER JOIN dbo.shouhou_other so WITH(NOLOCK) ON sh.id = so.shouhouid
            WHERE ISNULL(sh.isquji,0) = 0
                AND ISNULL(sh.xianshi,0)= 1
                AND so.kind = 3
                AND ISNULL(sh.iszy,0) = 0
                AND NOT EXISTS( SELECT 1 FROM dbo.shouhou_qudao sq with(nolock) WHERE sq.shouhouid = sh.id)
                AND so.timeoutdate &lt;= dateadd(hour,-24,getdate())
                AND sh.stats = 0
        ) t
            INNER JOIN dbo.shouhou sh WITH(NOLOCK) ON t.id = sh.id AND t.rn = 1
            LEFT JOIN dbo.areainfo a WITH(NOLOCK) ON a.id = sh.areaid
        WHERE
            (
            (t.mark = 0 AND DATEDIFF(HOUR,t.timeoutdate,GETDATE()) &gt;= 72)
            OR
            (t.mark = 1 AND DATEDIFF(HOUR,t.timeoutdate,GETDATE()) &gt;= 24)
            )
            <include refid="sale_after"></include>
    UNION
        SELECT 1 as kind, count(distinct sh.id) as subNumber,2 as rank
        FROM
        (
        SELECT sh.id, so.inuser, so.mark, so.timeoutdate,
        ROW_NUMBER() OVER(PARTITION BY so.shouhouid,so.mark ORDER BY so.timeoutdate DESC) rn
        FROM dbo.shouhou sh WITH(NOLOCK)
        INNER JOIN dbo.shouhou_other so WITH(NOLOCK) ON sh.id = so.shouhouid
        WHERE ISNULL(sh.isquji,0) = 0
        AND ISNULL(sh.xianshi,0)= 1
        AND so.kind = 3
        AND ISNULL(sh.iszy,0) = 0
        AND NOT EXISTS( SELECT 1 FROM dbo.shouhou_qudao sq with(nolock) WHERE sq.shouhouid = sh.id)
        AND so.timeoutdate &lt;= dateadd(hour,-24,getdate())
        AND sh.stats = 1
        ) t
        INNER JOIN dbo.shouhou sh WITH(NOLOCK) ON t.id = sh.id AND t.rn = 1
        LEFT JOIN dbo.areainfo a WITH(NOLOCK) ON a.id = sh.areaid
        WHERE
        (
        (t.mark = 0 AND DATEDIFF(HOUR,t.timeoutdate,GETDATE()) &gt;= 72)
        OR
        (t.mark = 1 AND DATEDIFF(HOUR,t.timeoutdate,GETDATE()) &gt;= 24)
        )
        <include refid="sale_after"></include>
     UNION
        SELECT 3 as kind, count(distinct sh.id) as subNumber,3 as rank
        FROM
        (
        SELECT sh.id, so.inuser, so.mark, so.timeoutdate,
        ROW_NUMBER() OVER(PARTITION BY so.shouhouid,so.mark ORDER BY so.timeoutdate DESC) rn
        FROM dbo.shouhou sh WITH(NOLOCK)
        INNER JOIN dbo.shouhou_other so WITH(NOLOCK) ON sh.id = so.shouhouid
        WHERE ISNULL(sh.isquji,0) = 0
        AND ISNULL(sh.xianshi,0)= 1
        AND so.kind = 3
        AND ISNULL(sh.iszy,0) = 0
        AND NOT EXISTS( SELECT 1 FROM dbo.shouhou_qudao sq with(nolock) WHERE sq.shouhouid = sh.id)
        AND so.timeoutdate &lt;= dateadd(hour,-24,getdate())
        AND sh.stats = 3
        ) t
        INNER JOIN dbo.shouhou sh WITH(NOLOCK) ON t.id = sh.id AND t.rn = 1
        LEFT JOIN dbo.areainfo a WITH(NOLOCK) ON a.id = sh.areaid
        WHERE
        (
        (t.mark = 0 AND DATEDIFF(HOUR,t.timeoutdate,GETDATE()) &gt;= 72)
        OR
        (t.mark = 1 AND DATEDIFF(HOUR,t.timeoutdate,GETDATE()) &gt;= 24)
        )
        <include refid="sale_after"></include>
    </select>

    <!-- listSaleAfterAppointOrder -->
    <select id="listSaleAfterAppointOrder" resultMap="SaleAfterAppointOrderMap">
        SELECT yy.id, a.area, yy.stats, yy.kind, yy.stype, yy.dtime, yy.name, yy.color, yy.imei, yy.mobile
        FROM dbo.shouhou_yuyue yy WITH(NOLOCK)
            LEFT JOIN dbo.areainfo a with(nolock) ON yy.areaid = a.id
        WHERE  ISNULL(yy.isdel,0) = 0
            AND ISNULL(yy.iszy,0) = 0
           <include refid="sale_after_appoint"></include>
    </select>

    <select id="getSaleAfterAppointGroup" resultType="com.jiuji.oa.oacore.oaorder.bo.SubNoticeNumber">
        SELECT 1 as kind,COUNT(DISTINCT yy.id) as subNumber,1 as rank FROM dbo.shouhou_yuyue yy WITH(NOLOCK)
        <where>
            yy.stats = 1  AND ISNULL(yy.isdel,0) = 0 AND ISNULL(yy.iszy,0) = 0
            <include refid="sale_after_appoint"/>
        </where>
        UNION
        SELECT 2 as kind,COUNT(DISTINCT yy.id) as subNumber,2 as rank FROM dbo.shouhou_yuyue yy WITH(NOLOCK)
        <where>
            yy.stats = 2  AND ISNULL(yy.isdel,0) = 0 AND ISNULL(yy.iszy,0) = 0
            <include refid="sale_after_appoint"/>
        </where>
        UNION
        SELECT 4 as kind,COUNT(DISTINCT yy.id) as subNumber,3 as rank FROM dbo.shouhou_yuyue yy WITH(NOLOCK)
        <where>
            yy.stats = 4  AND ISNULL(yy.isdel,0) = 0 AND ISNULL(yy.iszy,0) = 0
            <include refid="sale_after_appoint"/>
        </where>
        UNION
        SELECT 6 as kind,COUNT(DISTINCT yy.id) as subNumber,5 as rank FROM dbo.shouhou_yuyue yy WITH(NOLOCK)
        <where>
            yy.stats = 6  AND ISNULL(yy.isdel,0) = 0 AND ISNULL(yy.iszy,0) = 0
            <include refid="sale_after_appoint"/>
        </where>
        UNION
        SELECT 7 as kind,COUNT(DISTINCT yy.id) as subNumber,4 as rank FROM dbo.shouhou_yuyue yy WITH(NOLOCK)
        <where>
            yy.stats = 7  AND ISNULL(yy.isdel,0) = 0 AND ISNULL(yy.iszy,0) = 0
            <include refid="sale_after_appoint"/>
        </where>
        UNION
        SELECT 8 as kind,COUNT(DISTINCT yy.id) as subNumber,6 as rank FROM dbo.shouhou_yuyue yy WITH(NOLOCK)
        <where>
            yy.stats = 8  AND ISNULL(yy.isdel,0) = 0 AND ISNULL(yy.iszy,0) = 0
            <include refid="sale_after_appoint"/>
        </where>
    </select>

    <!-- listPendingDeliveryOrder -->
    <select id="listPendingDeliveryOrder" resultMap="PendingDeliveryOrderMap" >
        SELECT a.*,pf.product_name,pf.product_color,b.price,b.basket_count,c.area
        FROM
        (
            SELECT st.* FROM (
                SELECT s.sub_id,s.sub_check,s.delivery,s.sub_date,s.areaid, 1 as subType
                FROM dbo.sub s WITH(NOLOCK)
                INNER JOIN
                (
                    SELECT s.sub_id
                    FROM dbo.sub s WITH(NOLOCK)
                        INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
                    WHERE s.sub_check IN (0,1,5,7)
                        AND ISNULL(b.isdel,0) = 0
                        and s.delivery in (2,4,5)
                        and s.trader = #{req.sellerName}
                        and exists(select 1  from SubAddress s1 with(nolock)  where s1.sub_id =s.sub_id and  isnull(s1.wuliuNo,'') ='')
                        and (exists(SELECT 1  FROM dbo.basket_other o WITH(NOLOCK) WHERE o.basket_id=b.basket_id AND b.basket_count=o.lcount) or
                        exists (select 1  from  dbo.product_mkc k with(nolock) where  k.basket_id =b.basket_id))
                        <choose>
                            <when test="req.searchKind != null and req.searchKind == '订单电话号码'">
                                AND s.sub_mobile = #{req.searchValue}
                            </when>
                            <when test="req.searchKind != null and req.searchKind == '商品名称'">
                                AND EXISTS (SELECT 1 FROM dbo.productinfo pf with(NOLOCK) WHERE pf.ppriceid = b.ppriceid AND pf.product_name LIKE '%' + #{req.searchValue} + '%')
                            </when>
                            <when test="req.searchKind != null and req.searchKind == 'IMEI'">
                                AND EXISTS (SELECT 1 FROM dbo.product_mkc mkc with(NOLOCK) WHERE mkc.basket_id = b.basket_id AND mkc.imei LIKE '%' + #{req.searchValue} + '%')
                            </when>
                            <when test="req.searchKind != null and req.searchKind == '订单号'">
                                AND CAST(s.sub_id AS VARCHAR(50)) =  #{req.searchValue}
                            </when>
                            <otherwise></otherwise>
                        </choose>
                        <choose>
                            <when test="(req.subDateStart == null or req.subDateStart == '') and (req.subDateEnd != null and req.subDateEnd != '')">
                                AND s.sub_date &lt;= #{req.subDateEnd}
                            </when>
                            <when test="(req.subDateStart != null and req.subDateStart != '') and (req.subDateEnd == null or req.subDateEnd == '')">
                                AND s.sub_date &gt;= #{req.subDateStart}
                            </when>
                            <when test="(req.subDateStart != null and req.subDateStart != '') and (req.subDateEnd != null and req.subDateEnd != '')">
                                AND s.sub_date BETWEEN #{req.subDateStart} AND #{req.subDateEnd}
                            </when>
                            <otherwise></otherwise>
                        </choose>
                        <if test="req.whetherHideMaskOrder == true">
                            AND b.ppriceid NOT IN
                            <foreach collection="req.maskPpids" item="ppid" open="(" close=")" separator=",">
                                #{ppid}
                            </foreach>
                        </if>
                        GROUP BY s.sub_id
                ) t ON t.sub_id = s.sub_id
                UNION
                SELECT s.sub_id,s.sub_check,s.delivery,s.sub_date,s.areaid,2 as subType
                FROM dbo.recover_marketInfo s WITH(NOLOCK)
                INNER JOIN
                (
                    SELECT rmi.sub_id
                    FROM dbo.recover_marketInfo rmi WITH(NOLOCK)
                        INNER JOIN dbo.recover_marketSubInfo rms WITH(NOLOCK) ON rms.sub_id = rmi.sub_id
                        LEFT join dbo.recover_mkc k with(nolock)  on  rms.basket_id = k.to_basket_id
                    WHERE rmi.sub_check IN (0,1,5,7) AND ISNULL(rms.isdel,0) = 0
                        and (ISNULL(rmi.saleType,0) = 0 OR DATEDIFF(HOUR,rmi.sub_date,GETDATE())>= 72)
                        and exists(select 1  from RecoverSubAddress s1 with(nolock)
                                        left join(select danhaobind, paijianren from dbo.wuliu  with(nolock) where isnull(stats,0) &lt;&gt; 5 and wutype = 9 ) w on w.danhaobind = s1.sub_id
                                    where s1.sub_id =rmi.sub_id and isnull(s1.psuser,w.paijianren)=#{req.sellerName}  and  isnull(s1.wuliuNo,'') ='')
                        and rmi.delivery in (2,4,5)
                        and k.mkc_check in (3)
                    <choose>
                        <when test="req.searchKind != null and req.searchKind == '订单电话号码'">
                            AND rmi.sub_mobile = #{req.searchValue}
                        </when>
                        <when test="req.searchKind != null and req.searchKind == 'IMEI'">
                            AND EXISTS (SELECT 1 FROM dbo.productinfo pf with(NOLOCK) WHERE pf.ppriceid = rms.ppriceid AND pf.product_name LIKE '%' + #{req.searchValue} + '%')
                        </when>
                        <when test="req.searchKind != null and req.searchKind == '商品名称'">
                            AND EXISTS (SELECT 1 FROM dbo.product_mkc mkc with(NOLOCK) WHERE mkc.basket_id = rms.basket_id AND mkc.imei LIKE '%' + #{req.searchValue} + '%')
                        </when>
                        <when test="req.searchKind != null and req.searchKind == '订单号'">
                            AND CAST(rmi.sub_id AS VARCHAR(50)) = #{req.searchValue}
                        </when>
                        <otherwise></otherwise>
                    </choose>
                    <choose>
                        <when test="(req.subDateStart == null or req.subDateStart == '') and (req.subDateEnd != null and req.subDateEnd != '')">
                            AND rmi.sub_date &lt;= #{req.subDateEnd}
                        </when>
                        <when test="(req.subDateStart != null and req.subDateStart != '') and (req.subDateEnd == null or req.subDateEnd == '')">
                            AND rmi.sub_date &gt;= #{req.subDateStart}
                        </when>
                        <when test="(req.subDateStart != null and req.subDateStart != '') and (req.subDateEnd != null and req.subDateEnd != '')">
                            AND rmi.sub_date BETWEEN #{req.subDateStart} AND #{req.subDateEnd}
                        </when>
                        <otherwise></otherwise>
                    </choose>
                    GROUP BY rmi.sub_id
                ) t ON t.sub_id = s.sub_id
            ) st
            ORDER BY st.sub_id
            OFFSET #{req.offset} ROWS FETCH NEXT #{req.pageSize} ROWS ONLY
        ) a
        LEFT JOIN dbo.basket b WITH(NOLOCK) ON a.sub_id = b.sub_id
        LEFT JOIN dbo.productinfo pf WITH(NOLOCK) ON pf.ppriceid = b.ppriceid
        LEFT JOIN dbo.areainfo c WITH(NOLOCK) ON c.id = a.areaid
        WHERE ISNULL(b.isdel,0) = 0
    </select>

    <!-- countPendingDeliveryOrder -->
    <select id="countPendingDeliveryOrder" resultType="java.lang.Long" >
        SELECT COUNT(DISTINCT a.sub_id)
        FROM
        (
            SELECT s.sub_id,s.sub_check,s.delivery,s.sub_date,s.areaid, 1 as subType
            FROM dbo.sub s WITH(NOLOCK)
                LEFT JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id
            WHERE s.sub_check IN (0,1,5,7)
                AND ISNULL(b.isdel,0) = 0
                and s.delivery in (2,4,5)
                and s.trader = #{req.sellerName}
                and exists(select 1  from SubAddress s1 with(nolock)  where s1.sub_id =s.sub_id and  isnull(s1.wuliuNo,'') ='')
                and (exists(SELECT 1  FROM dbo.basket_other o WITH(NOLOCK) WHERE o.basket_id=b.basket_id AND b.basket_count=o.lcount) or
                exists (select 1  from  dbo.product_mkc k with(nolock) where  k.basket_id =b.basket_id))
                <choose>
                    <when test="req.searchKind != null and req.searchKind == '订单电话号码'">
                        AND s.sub_mobile = #{req.searchValue}
                    </when>
                    <when test="req.searchKind != null and req.searchKind == '商品名称'">
                        AND EXISTS (SELECT 1 FROM dbo.productinfo pf with(NOLOCK) WHERE pf.ppriceid = b.ppriceid AND pf.product_name LIKE '%' + #{req.searchValue} + '%')
                    </when>
                    <when test="req.searchKind != null and req.searchKind == 'IMEI'">
                        AND EXISTS (SELECT 1 FROM dbo.product_mkc mkc with(NOLOCK) WHERE mkc.basket_id = b.basket_id AND mkc.imei LIKE '%' + #{req.searchValue} + '%')
                    </when>
                    <when test="req.searchKind != null and req.searchKind == '订单号'">
                        AND CAST(s.sub_id AS VARCHAR(50)) =  #{req.searchValue}
                    </when>
                    <otherwise></otherwise>
                </choose>
                <choose>
                    <when test="(req.subDateStart == null or req.subDateStart == '') and (req.subDateEnd != null and req.subDateEnd != '')">
                        AND s.sub_date &lt;= #{req.subDateEnd}
                    </when>
                    <when test="(req.subDateStart != null and req.subDateStart != '') and (req.subDateEnd == null or req.subDateEnd == '')">
                        AND s.sub_date &gt;= #{req.subDateStart}
                    </when>
                    <when test="(req.subDateStart != null and req.subDateStart != '') and (req.subDateEnd != null and req.subDateEnd != '')">
                        AND s.sub_date BETWEEN #{req.subDateStart} AND #{req.subDateEnd}
                    </when>
                    <otherwise></otherwise>
                </choose>
                <if test="req.whetherHideMaskOrder == true">
                    AND b.ppriceid NOT IN
                    <foreach collection="req.maskPpids" item="ppid" open="(" close=")" separator=",">
                        #{ppid}
                    </foreach>
                </if>
            UNION
            SELECT s.sub_id,s.sub_check,s.delivery,s.sub_date,s.areaid,2 as subType
            FROM dbo.recover_marketInfo s WITH(NOLOCK)
                LEFT JOIN dbo.recover_marketSubInfo rms WITH(NOLOCK) ON rms.sub_id = s.sub_id
                LEFT JOIN dbo.recover_mkc k with(nolock)  on  rms.basket_id = k.to_basket_id
            WHERE s.sub_check IN (0,1,5,7)
                AND ISNULL(rms.isdel,0) = 0
                and (ISNULL(s.saleType,0) = 0 OR DATEDIFF(HOUR,s.sub_date,GETDATE())>= 72)
                and exists(select 1  from RecoverSubAddress s1 with(nolock)
                                left join(select danhaobind, paijianren from dbo.wuliu  with(nolock) where isnull(stats,0) &lt;&gt; 5 and wutype = 9 ) w on w.danhaobind = s1.sub_id
                            where s1.sub_id =s.sub_id and isnull(s1.psuser,w.paijianren)=#{req.sellerName}  and  isnull(s1.wuliuNo,'') ='')
                and s.delivery in (2,4,5)
                and k.mkc_check in (3)
                <choose>
                    <when test="req.searchKind != null and req.searchKind == '订单电话号码'">
                        AND s.sub_mobile = #{req.searchValue}
                    </when>
                    <when test="req.searchKind != null and req.searchKind == 'IMEI'">
                        AND EXISTS (SELECT 1 FROM dbo.productinfo pf with(NOLOCK) WHERE pf.ppriceid = rms.ppriceid AND pf.product_name LIKE '%' + #{req.searchValue} + '%')
                    </when>
                    <when test="req.searchKind != null and req.searchKind == '商品名称'">
                        AND EXISTS (SELECT 1 FROM dbo.product_mkc mkc with(NOLOCK) WHERE mkc.basket_id = rms.basket_id AND mkc.imei LIKE '%' + #{req.searchValue} + '%')
                    </when>
                    <when test="req.searchKind != null and req.searchKind == '订单号'">
                        AND CAST(s.sub_id AS VARCHAR(50)) = #{req.searchValue}
                    </when>
                    <otherwise></otherwise>
                </choose>
                <choose>
                    <when test="(req.subDateStart == null or req.subDateStart == '') and (req.subDateEnd != null and req.subDateEnd != '')">
                        AND s.sub_date &lt;= #{req.subDateEnd}
                    </when>
                    <when test="(req.subDateStart != null and req.subDateStart != '') and (req.subDateEnd == null or req.subDateEnd == '')">
                        AND s.sub_date &gt;= #{req.subDateStart}
                    </when>
                    <when test="(req.subDateStart != null and req.subDateStart != '') and (req.subDateEnd != null and req.subDateEnd != '')">
                        AND s.sub_date BETWEEN #{req.subDateStart} AND #{req.subDateEnd}
                    </when>
                    <otherwise></otherwise>
                </choose>
        ) a
    </select>
    <select id="getGoodStockGroup" resultType="com.jiuji.oa.oacore.oaorder.bo.SubNoticeNumber">
<!--        SELECT 0 kind,COUNT(DISTINCT s.sub_id) subNumber FROM dbo.recover_marketInfo s WITH(NOLOCK)-->
<!--        INNER JOIN dbo.recover_marketSubInfo b WITH(NOLOCK) ON b.sub_id = s.sub_id-->
<!--        LEFT JOIN dbo.recover_mkc mkc with(NOLOCK) ON mkc.to_basket_id = b.basket_id-->
<!--        <where>-->
<!--            s.sub_check IN (1)-->
<!--            AND ISNULL(b.isdel,0) = 0-->
<!--            AND mkc.mkc_check= 0 AND mkc.issalf =1-->
<!--            <include refid="good_stock"></include>-->
<!--        </where>-->
<!--        UNION-->
<!--        SELECT 1 kind,COUNT(DISTINCT s.sub_id) subNumber FROM dbo.recover_marketInfo s WITH(NOLOCK)-->
<!--        INNER JOIN dbo.recover_marketSubInfo b WITH(NOLOCK) ON b.sub_id = s.sub_id-->
<!--        LEFT JOIN dbo.recover_mkc mkc with(NOLOCK) ON mkc.to_basket_id = b.basket_id-->
<!--        <where>-->
<!--            s.sub_check IN (1)-->
<!--            AND ISNULL(b.isdel,0) = 0-->
<!--            AND mkc.mkc_check= 1 AND mkc.issalf =1-->
<!--            <include refid="good_stock"></include>-->
<!--        </where>-->
<!--        UNION-->
<!--        SELECT 2 kind,COUNT(DISTINCT s.sub_id) subNumber FROM dbo.recover_marketInfo s WITH(NOLOCK)-->
<!--        INNER JOIN dbo.recover_marketSubInfo b WITH(NOLOCK) ON b.sub_id = s.sub_id-->
<!--        LEFT JOIN dbo.recover_mkc mkc with(NOLOCK) ON mkc.to_basket_id = b.basket_id-->
<!--        <where>-->
<!--            s.sub_check IN (1)-->
<!--            AND ISNULL(b.isdel,0) = 0-->
<!--            AND mkc.mkc_check= 2 AND mkc.issalf =1-->
<!--            <include refid="good_stock"></include>-->
<!--        </where>-->
<!--        UNION-->
        SELECT 3 kind,COUNT(DISTINCT s.sub_id) subNumber FROM dbo.recover_marketInfo s WITH(NOLOCK)
        INNER JOIN dbo.recover_marketSubInfo b WITH(NOLOCK) ON b.sub_id = s.sub_id
        LEFT JOIN dbo.recover_mkc mkc with(NOLOCK) ON mkc.to_basket_id = b.basket_id
        <where>
            s.sub_check IN (1)
            AND ISNULL(b.isdel,0) = 0
            AND mkc.mkc_check= 3 AND mkc.issalf =1
            <include refid="good_stock"></include>
        </where>
        UNION
        SELECT 10 kind,COUNT(DISTINCT s.sub_id) subNumber FROM dbo.recover_marketInfo s WITH(NOLOCK)
        INNER JOIN dbo.recover_marketSubInfo b WITH(NOLOCK) ON b.sub_id = s.sub_id
        LEFT JOIN dbo.recover_mkc mkc with(NOLOCK) ON mkc.to_basket_id = b.basket_id
        <where>
            s.sub_check IN (1)
            AND ISNULL(b.isdel,0) = 0
            AND mkc.mkc_check= 10 AND mkc.issalf =1
            <include refid="good_stock"></include>
        </where>
    </select>
    <select id="selectSmallStockState" resultType="com.jiuji.oa.oacore.oaorder.vo.res.SelectSmallStockState">
        select p.cid,
               b.basket_id as basketId,
               b.ppriceid,
               b.basket_count as basketCount,
               o.lcount  as beiCount,
               db.lcount as dbCount,
               cg.lcount as cgCount,
               c.lcount     bCount
        from dbo.basket b with (nolock)
         left join dbo.basket_other o with (nolock) on o.basket_id = b.basket_id
            left join dbo.productinfo p with (nolock) on p.ppriceid = b.ppriceid
            left join(select db.basket_id, sum(db.lcount) as lcount
            from dbo.diaobo_basket db with (nolock)
            left join dbo.diaobo_sub ds with (nolock) on db.sub_id = ds.id
            where ds.stats in (1, 2, 5, 3)
            and db.basket_type = 0 and db.basket_id in
            <foreach collection="basketIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            group by db.basket_id) db on db.basket_id = b.basket_id
            left join(select db.basket_id, sum(db.lcount) as lcount
            from dbo.caigou_basket db with (nolock)
            left join dbo.caigou_sub ds with (nolock) on db.sub_id = ds.id
            where ds.stats in (0, 1, 2)
            and db.basket_type = 0 and db.basket_id in
            <foreach collection="basketIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            group by db.basket_id) cg on cg.basket_id = b.basket_id
            left join (select lcount, basket_id
            from caigouinfo with (nolock)
            where isnull([type], 0) = 1
            and basket_id in
            <foreach collection="basketIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
                ) c on c.basket_id = b.basket_id
        where b.basket_id in
        <foreach collection="basketIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectStockState" resultType="com.jiuji.oa.oacore.oaorder.vo.res.StockState">
        select b.basket_id, b.ppriceid, pm.kc_check as stockStateValue
        from basket b with (nolock)
         left join product_mkc pm with (nolock) on b.basket_id = pm.basket_id
        where b.basket_id in
        <foreach collection="basketIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>


    <select id="selectStockStateRecover" resultType="com.jiuji.oa.oacore.oaorder.vo.res.StockState">
        select to_basket_id as basketId,mkc_check as stockStateValue,ppriceid from recover_mkc WITH (NOLOCK)
        where to_basket_id in
        <foreach collection="basketIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getOrderProductList" resultType="com.jiuji.oa.oacore.oaorder.vo.res.OrderProductList">
        SELECT s.sub_id,b.basket_id,b.ismobile
        FROM dbo.sub s WITH (NOLOCK)
         INNER JOIN dbo.basket b WITH (NOLOCK)
        ON b.sub_id = s.sub_id
        <where>
            s.sub_check = 1 AND ISNULL(b.isdel,0) = 0
            <include refid="sale_base"></include>
        </where>
    </select>
    <select id="getOrderTipBySubIds" resultType="com.jiuji.oa.oacore.oaorder.bo.OrderTipBO">
        select sub_id,tip from dbo.sub_tip_info with(nolock)
        where sub_type=1 and isnull(is_del,0)=0
        and sub_id in
        <foreach collection="subIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="listBasketRank" resultType="com.jiuji.oa.oacore.oaorder.vo.res.OrderInfoVO">
        select basket_id basketId,order_number basketRank from dbo.sub_pro_distribution_rank with(nolock)
        where isnull(isdel,0)=0 and basket_id in
        <foreach collection="basketIds" open="(" close=")" separator="," item="basketId">
            #{basketId}
        </foreach>
    </select>
    <select id="getSaleAfterAppointJiuJiGroup" resultType="com.jiuji.oa.oacore.oaorder.bo.SubNoticeNumber">
        SELECT 1 as kind,COUNT(DISTINCT yy.id) as subNumber,1 as rank FROM dbo.shouhou_yuyue yy WITH(NOLOCK)
        <where>
            yy.stats = 1  AND ISNULL(yy.isdel,0) = 0 AND ISNULL(yy.iszy,0) = 0
            <include refid="sale_after_appoint"/>
        </where>
        UNION
        SELECT 4 as kind,COUNT(DISTINCT yy.id) as subNumber,3 as rank FROM dbo.shouhou_yuyue yy WITH(NOLOCK)
        <where>
            yy.stats = 4  AND ISNULL(yy.isdel,0) = 0 AND ISNULL(yy.iszy,0) = 0
            <include refid="sale_after_appoint"/>
        </where>
    </select>

    <sql id="good_stock">
        <choose>
            <when test="kind == 1">
                AND b.seller = #{params.sellerName}
            </when>
            <when test="kind == 2">
                AND s.areaid = #{params.curAreaId}
            </when>
            <otherwise></otherwise>
        </choose>
        <choose>
            <when test="params.searchKind != null and params.searchKind == '订单电话号码'">
                AND s.sub_mobile = #{params.searchValue}
            </when>
            <when test="params.searchKind != null and params.searchKind == '商品名称'">
                AND EXISTS (SELECT 1 FROM dbo.productinfo pf with(NOLOCK) WHERE pf.ppriceid = b.ppriceid AND pf.product_name LIKE '%' + #{params.searchValue} + '%')
            </when>
            <when test="params.searchKind != null and params.searchKind == 'IMEI'">
                AND EXISTS (SELECT 1 FROM dbo.recover_mkc mkc with(NOLOCK) WHERE mkc.to_basket_id = b.basket_id AND mkc.imei LIKE '%' + #{params.searchValue} + '%')
            </when>
            <when test="params.searchKind != null and params.searchKind == '订单号'">
                AND CAST(s.sub_id AS VARCHAR(50)) =  #{params.searchValue}
            </when>
            <otherwise></otherwise>
        </choose>
        <choose>
            <when test="(params.subDateStart == null or params.subDateStart == '') and (params.subDateEnd != null and params.subDateEnd != '')">
                AND s.sub_date &lt;= #{params.subDateEnd}
            </when>
            <when test="(params.subDateStart != null and params.subDateStart != '') and (params.subDateEnd == null or params.subDateEnd == '')">
                AND s.sub_date &gt;= #{params.subDateEnd}
            </when>
            <when test="(params.subDateStart != null and params.subDateStart != '') and (params.subDateEnd != null and params.subDateEnd != '')">
                AND s.sub_date BETWEEN #{params.subDateStart} AND #{params.subDateEnd}
            </when>
            <otherwise></otherwise>
        </choose>
        <if test="params.paisongStats != null and params.paisongStats != -1">
            AND EXISTS(SELECT 1 FROM dbo.RecoverSubAddress sa WITH(NOLOCK) WHERE sa.sub_id = s.sub_id AND
            ISNULL(sa.paisongState,0) = #{params.paisongStats})
        </if>
    </sql>

</mapper>
