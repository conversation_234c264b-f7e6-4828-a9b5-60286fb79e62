<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.DebtSubMapper">

    <select id="getDebtSub" resultType="com.jiuji.oa.oacore.oaorder.bo.DebtSubBO">
        SELECT DISTINCT s.sub_id ,b.seller as seller ,0 as type,s.areaId from sub s with(nolock)
        left join basket b with(nolock) on b.sub_id = s.sub_id
        where seller != '网络' and s.sub_check = 6 and ISNULL(b.isdel,0) = 0
        order by sub_id DESC
    </select>

    <select id="getDebtRecoverSub" resultType="com.jiuji.oa.oacore.oaorder.bo.DebtSubBO">
        SELECT DISTINCT s.sub_id ,b.seller as seller ,1 as type,s.areaId from recover_marketInfo s with(nolock)
        left join recover_marketSubInfo b with(nolock) on b.sub_id = s.sub_id
        where seller != '网络' and s.sub_check = 6 and ISNULL(b.isdel,0) = 0
        order by sub_id DESC
    </select>
</mapper>
