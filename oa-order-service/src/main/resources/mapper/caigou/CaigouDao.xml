<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.caigou.dao.CaigouDao">

    <select id="getCaigouAvgPrice" resultType="com.jiuji.oa.oacore.caigou.res.CaigouAvgPriceVO">
            SELECT CAST
        ( MAX ( inprice ) AS NUMERIC ( 9, 2 ) ) AS maxinprice,
        CAST ( MIN ( inprice ) AS NUMERIC ( 9, 2 ) ) AS mininprice,
        CAST (
        CASE

                WHEN SUM ( lcount ) > 0 THEN
                SUM ( lcount * inprice ) / SUM ( lcount ) ELSE AVG ( inprice )
            END AS NUMERIC ( 9, 2 )
        ) AS avgPrice,
        SUM ( lcount ) AS lcount,
        SUM ( leftCount ) AS lcountx,
        ppriceid as ppid
    FROM
        product_kc k with(nolock)
    WHERE
        EXISTS (
        SELECT
            1
        FROM
            dbo.caigou_basket b1 with(nolock)
            LEFT JOIN dbo.caigou_sub s1 with(nolock) ON b1.sub_id = s1.id
        WHERE
            s1.stats = 3
            AND datediff( DAY, s1.ruku_dtime, #{date} ) = 0
            AND k.ppriceid= b1.ppriceid
            AND s1.subKind = 0
        )
    GROUP BY
        ppriceid
    </select>

    <select id="countDiaoboAwaitOld" resultType="com.jiuji.oa.oacore.caigou.vo.DiaoboAwaitCountVo">
        select count(1) total, min(allotTime) earlyTime from(
        <!--大件-->
        select 1 type, m.[dtime] allotTime, m.id mkcId
        from mkc_toarea m with(nolock)
        left join product_mkc k with(nolock) on m.mkc_id=k.id
        where k.id is not null and k.kc_check &lt;&gt; 4
        <!--订购的订单-->
        and k.basket_id is not null
        <!--0 未发出 1 准备发货-->
        and m.stats in(0,1)
        and k.kc_check = 10
        <!--发货门店id-->
        and m.areaid = #{areaId}
        union all

        <!--小件-->
        select 2 type, s.check_dtime allotTime, s.id mkcId
        from diaobo_sub s with(nolock)
        left join diaobo_basket dbb with(nolock)  on s.id=dbb.sub_id
        left join basket b with(nolock) on b.basket_id = dbb.basket_id and dbb.basket_type = 0
        where 1 = 1 and ISNULL(b.type,0) &lt;&gt; 22
        and s.kinds in('pj')
        and dbb.basket_id is not null
        <!--2 已审核 5 准备发货-->
        and s.stats in (2,5)
        and s.areaid = #{areaId}
        union all

        <!--维修配件-->
        select 3 type, s.check_dtime allotTime, s.id mkcId
        from diaobo_sub s with(nolock)
        left join diaobo_basket dbb with(nolock) on s.id=dbb.sub_id
        where 1 = 1
        and s.kinds in('wx')
        and dbb.basket_id is not null
        <!--2 已审核 5 准备发货-->
        and s.stats in (2,5)
        and s.areaid = #{areaId}
        union all

        <!--良品-->
        select 4 type, m.[dtime] allotTime, k.id mkcId
        FROM recover_toarea m with(nolock)
        LEFT join recover_mkc k with(nolock) on m.mkc_id = k.id
        WHERE k.id is not NULL
        <!--良品库存-->
        and ISNULL(k.issalf,0) =1
        <!--订购的订单-->
        and k.from_basket_id is not null
        <!--0 未发出 1 准备发货-->
        and m.status in (0,1)
        <!--发货门店id-->
        and m.areaid = #{areaId}

        union all

        <!--小件优品-->
        select 5 type, s.check_dtime allotTime, s.id mkcId
        from diaobo_sub s with(nolock)
        left join diaobo_basket dbb with(nolock) on s.id=dbb.sub_id
        left join basket b with(nolock) on b.basket_id = dbb.basket_id and dbb.basket_type = 0
        where 1 = 1 and ISNULL(b.type,0) = 22
        and s.kinds in('pj')
        and dbb.basket_id is not null
        <!--2 已审核 5 准备发货-->
        and s.stats in (2,5)
        and s.areaid = #{areaId}
        )as db
        where (db.type in(4,5) or not EXISTS (
        SELECT 1 from product_stock_out pso with (nolock) where order_status not in (1,6) and is_delete = 0  and db.mkcId = pso.related_id
        ))
    </select>

    <select id="countDiaoboAwait" resultType="com.jiuji.oa.oacore.caigou.vo.DiaoboAwaitCountVo">
        select count(1) total, min(allotTime) earlyTime from(
        <!--大件-->
        select 1 type, m.[dtime] allotTime, m.id mkcId
        from mkc_toarea m with(nolock)
        left join product_mkc k with(nolock) on m.mkc_id=k.id
        where k.id is not null and k.kc_check &lt;&gt; 4
        <!--订购的订单-->
        and k.basket_id is not null
        <!--0 未发出 1 准备发货-->
        and m.stats in(0,1)
        and k.kc_check = 10
        <!--发货门店id-->
        and m.areaid = #{areaId}
        union all

        <!--小件-->
        select 2 type, s.check_dtime allotTime, s.id mkcId
        from diaobo_sub s with(nolock)
        left join diaobo_basket dbb with(nolock)  on s.id=dbb.sub_id
        left join basket b with(nolock) on b.basket_id = dbb.basket_id and dbb.basket_type = 0
        where 1 = 1 and ISNULL(b.type,0) &lt;&gt; 22 and s.diaoboType not in (2,4)
        and s.kinds in('pj')
        and dbb.basket_id is not null
        <!--2 已审核 5 准备发货-->
        and s.stats in (2,5)
        and s.areaid = #{areaId}
        union all

        <!--维修配件-->
        select 3 type, s.check_dtime allotTime, s.id mkcId
        from diaobo_sub s with(nolock)
        left join diaobo_basket dbb with(nolock) on s.id=dbb.sub_id
        where 1 = 1
        and s.kinds in('wx')
        and dbb.basket_id is not null
        <!--2 已审核 5 准备发货-->
        and s.stats in (2,5)
        and s.areaid = #{areaId}
        union all

        <!--良品-->
        select 4 type, m.[dtime] allotTime, k.id mkcId
        FROM recover_toarea m with(nolock)
        LEFT join recover_mkc k with(nolock) on m.mkc_id = k.id
        WHERE k.id is not NULL
        <!--良品库存-->
        and ISNULL(k.issalf,0) =1
        <!--订购的订单-->
        and k.from_basket_id is not null
        <!--0 未发出 1 准备发货-->
        and m.status in (0,1)
        <!--发货门店id-->
        and m.areaid = #{areaId}

        union all

        <!--小件优品-->
        select 5 type, s.check_dtime allotTime, s.id mkcId
        from diaobo_sub s with(nolock)
        left join diaobo_basket dbb with(nolock) on s.id=dbb.sub_id
        left join basket b with(nolock) on b.basket_id = dbb.basket_id and dbb.basket_type = 0
        where 1 = 1 and ISNULL(b.type,0) = 22
        and s.kinds in('pj')
        and dbb.basket_id is not null
        <!--2 已审核 5 准备发货-->
        and s.stats in (2,5)
        and s.areaid = #{areaId}
        union

        <!--小件优品-->
        select 5 type, s.check_dtime allotTime, s.id mkcId
        from diaobo_sub s with(nolock)
        left join diaobo_basket dbb with(nolock) on s.id=dbb.sub_id
        left join basket b with(nolock) on b.basket_id = dbb.basket_id and dbb.basket_type = 0
        where 1 = 1 and s.diaoboType in (2,4)
        and s.kinds in('pj')
        and dbb.basket_id is not null
        <!--2 已审核 5 准备发货-->
        and s.stats in (2,5)
        and s.areaid = #{areaId}
        )as db
        where (db.type in(4,5) or not EXISTS (
        SELECT 1 from product_stock_out pso with (nolock) where order_status not in (1,6) and is_delete = 0  and db.mkcId = pso.related_id
        ))
    </select>

    <select id="diaoboAwaitList" resultType="com.jiuji.oa.oacore.caigou.vo.DiaoboAwaitRes">
        select * from(
            <!--大件-->
            select  '机器编号' idDesc,k.orderid showId,m.[dtime] allotTime,1 type,(select ai.area from areainfo ai with(nolock) where ai.id = m.toareaid ) areaIn,(select ai.area from areainfo ai with(nolock) where ai.id = m.areaid ) areaOut, m.id mkcId
            from mkc_toarea m with(nolock)
            left join product_mkc k with(nolock) on m.mkc_id=k.id
            where k.id is not null and k.kc_check &lt;&gt; 4
            <!--订购的订单-->
            and k.basket_id is not null
            <!--0 未发出 1 准备发货-->
            and m.stats in(0,1)
            and k.kc_check = 10
            <!--发货门店id-->
            and m.areaid = #{diaoboAwaitReq.areaId}
            union all

            <!--小件-->
            select '调拨单号' idDesc,CAST(s.id AS VARCHAR(10)) showId, s.check_dtime allotTime, 2 type, (select ai.area from areainfo ai with(nolock) where ai.id = s.toareaid ) areaIn,(select ai.area from areainfo ai with(nolock) where ai.id = s.areaid ) areaOut, s.id mkcId
            from diaobo_sub s with(nolock)
            left join diaobo_basket dbb with(nolock)  on s.id=dbb.sub_id
            left join basket b with(nolock) on b.basket_id = dbb.basket_id and dbb.basket_type = 0
            where 1 = 1 and ISNULL(b.type,0) &lt;&gt; 22 and s.diaoboType not in (2,4)
            and s.kinds in('pj')
            and dbb.basket_id is not null
            <!--2 已审核 5 准备发货-->
            and s.stats in (2,5)
            and s.areaid = #{diaoboAwaitReq.areaId}
            union all

            <!--维修配件-->
            select '调拨单号' idDesc,CAST(s.id AS VARCHAR(10)) showId, s.check_dtime allotTime, 3 type, (select ai.area from areainfo ai with(nolock) where ai.id = s.toareaid ) areaIn,(select ai.area from areainfo ai with(nolock) where ai.id = s.areaid ) areaOut, s.id mkcId
            from diaobo_sub s with(nolock)
            left join diaobo_basket dbb with(nolock) on s.id=dbb.sub_id
            where 1 = 1
            and s.kinds in('wx')
            and dbb.basket_id is not null
            <!--2 已审核 5 准备发货-->
            and s.stats in (2,5)
            and s.areaid = #{diaoboAwaitReq.areaId}
            union all

            <!--良品-->
            select '机器编号' idDesc,k.orderid showId,m.[dtime] allotTime,4 type, (select ai.area from areainfo ai with(nolock) where ai.id = m.toareaid ) areaIn,(select ai.area from areainfo ai with(nolock) where ai.id = m.areaid ) areaOut, k.id mkcId
            FROM recover_toarea m with(nolock)
            LEFT join recover_mkc k with(nolock) on m.mkc_id = k.id
            WHERE k.id is not NULL
            <!--良品库存-->
            and ISNULL(k.issalf,0) =1
            <!--订购的订单-->
            and k.from_basket_id is not null
            <!--0 未发出 1 准备发货-->
            and m.status in (0,1)
            <!--发货门店id-->
            and m.areaid = #{diaoboAwaitReq.areaId}

            union all

            <!--小件优品-->
            select '调拨单号' idDesc,CAST(s.id AS VARCHAR(10)) showId, s.check_dtime allotTime, 5 type,
            (select ai.area from areainfo ai with(nolock) where ai.id = s.toareaid ) areaIn,(select ai.area from areainfo ai with(nolock) where ai.id = s.areaid ) areaOut, s.id mkcId
            from diaobo_sub s with(nolock)
            left join diaobo_basket dbb with(nolock) on s.id=dbb.sub_id
            left join basket b with(nolock) on b.basket_id = dbb.basket_id and dbb.basket_type = 0
            where 1 = 1 and ISNULL(b.type,0) = 22
            and s.kinds in('pj')
            and dbb.basket_id is not null
            <!--2 已审核 5 准备发货-->
            and s.stats in (2,5)
            and s.areaid = #{diaoboAwaitReq.areaId}
        union

        <!--小件优品-->
        select '调拨单号' idDesc,CAST(s.id AS VARCHAR(10)) showId, s.check_dtime allotTime, 5 type,
        (select ai.area from areainfo ai with(nolock) where ai.id = s.toareaid ) areaIn,(select ai.area from areainfo ai with(nolock) where ai.id = s.areaid ) areaOut, s.id mkcId
        from diaobo_sub s with(nolock)
        left join diaobo_basket dbb with(nolock) on s.id=dbb.sub_id
        left join basket b with(nolock) on b.basket_id = dbb.basket_id and dbb.basket_type = 0
        where 1 = 1 and s.diaoboType in (2,4)
        and s.kinds in('pj')
        and dbb.basket_id is not null
        <!--2 已审核 5 准备发货-->
        and s.stats in (2,5)
        and s.areaid = #{diaoboAwaitReq.areaId}
        )as db
        where (db.type in(4,5) or not EXISTS (
        SELECT 1 from product_stock_out pso with (nolock) where order_status not in (1,6) and is_delete = 0  and db.mkcId = pso.related_id
        ))
        ORDER BY allotTime
        offset #{startRows} rows fetch next #{size} rows only
    </select>


    <select id="diaoboAwaitListOld" resultType="com.jiuji.oa.oacore.caigou.vo.DiaoboAwaitRes">
        select * from(
        <!--大件-->
        select  '机器编号' idDesc,k.orderid showId,m.[dtime] allotTime,1 type,(select ai.area from areainfo ai with(nolock) where ai.id = m.toareaid ) areaIn,(select ai.area from areainfo ai with(nolock) where ai.id = m.areaid ) areaOut, m.id mkcId
        from mkc_toarea m with(nolock)
        left join product_mkc k with(nolock) on m.mkc_id=k.id
        where k.id is not null and k.kc_check &lt;&gt; 4
        <!--订购的订单-->
        and k.basket_id is not null
        <!--0 未发出 1 准备发货-->
        and m.stats in(0,1)
        and k.kc_check = 10
        <!--发货门店id-->
        and m.areaid = #{diaoboAwaitReq.areaId}
        union all

        <!--小件-->
        select '调拨单号' idDesc,CAST(s.id AS VARCHAR(10)) showId, s.check_dtime allotTime, 2 type, (select ai.area from areainfo ai with(nolock) where ai.id = s.toareaid ) areaIn,(select ai.area from areainfo ai with(nolock) where ai.id = s.areaid ) areaOut, s.id mkcId
        from diaobo_sub s with(nolock)
        left join diaobo_basket dbb with(nolock)  on s.id=dbb.sub_id
        left join basket b with(nolock) on b.basket_id = dbb.basket_id and dbb.basket_type = 0
        where 1 = 1 and ISNULL(b.type,0) &lt;&gt; 22
        and s.kinds in('pj')
        and dbb.basket_id is not null
        <!--2 已审核 5 准备发货-->
        and s.stats in (2,5)
        and s.areaid = #{diaoboAwaitReq.areaId}
        union all

        <!--维修配件-->
        select '调拨单号' idDesc,CAST(s.id AS VARCHAR(10)) showId, s.check_dtime allotTime, 3 type, (select ai.area from areainfo ai with(nolock) where ai.id = s.toareaid ) areaIn,(select ai.area from areainfo ai with(nolock) where ai.id = s.areaid ) areaOut, s.id mkcId
        from diaobo_sub s with(nolock)
        left join diaobo_basket dbb with(nolock) on s.id=dbb.sub_id
        where 1 = 1
        and s.kinds in('wx')
        and dbb.basket_id is not null
        <!--2 已审核 5 准备发货-->
        and s.stats in (2,5)
        and s.areaid = #{diaoboAwaitReq.areaId}
        union all

        <!--良品-->
        select '机器编号' idDesc,k.orderid showId,m.[dtime] allotTime,4 type, (select ai.area from areainfo ai with(nolock) where ai.id = m.toareaid ) areaIn,(select ai.area from areainfo ai with(nolock) where ai.id = m.areaid ) areaOut, k.id mkcId
        FROM recover_toarea m with(nolock)
        LEFT join recover_mkc k with(nolock) on m.mkc_id = k.id
        WHERE k.id is not NULL
        <!--良品库存-->
        and ISNULL(k.issalf,0) =1
        <!--订购的订单-->
        and k.from_basket_id is not null
        <!--0 未发出 1 准备发货-->
        and m.status in (0,1)
        <!--发货门店id-->
        and m.areaid = #{diaoboAwaitReq.areaId}

        union all

        <!--小件优品-->
        select '调拨单号' idDesc,CAST(s.id AS VARCHAR(10)) showId, s.check_dtime allotTime, 5 type, (select ai.area from areainfo ai with(nolock) where ai.id = s.toareaid ) areaIn,(select ai.area from areainfo ai with(nolock) where ai.id = s.areaid ) areaOut, s.id mkcId
        from diaobo_sub s with(nolock)
        left join diaobo_basket dbb with(nolock) on s.id=dbb.sub_id
        left join basket b with(nolock) on b.basket_id = dbb.basket_id and dbb.basket_type = 0
        where 1 = 1 and ISNULL(b.type,0) = 22
        and s.kinds in('pj')
        and dbb.basket_id is not null
        <!--2 已审核 5 准备发货-->
        and s.stats in (2,5)
        and s.areaid = #{diaoboAwaitReq.areaId}
        )as db
        where (db.type in(4,5) or not EXISTS (
        SELECT 1 from product_stock_out pso with (nolock) where order_status not in (1,6) and is_delete = 0  and db.mkcId = pso.related_id
        ))
        ORDER BY allotTime
        offset #{startRows} rows fetch next #{size} rows only
    </select>
</mapper>
