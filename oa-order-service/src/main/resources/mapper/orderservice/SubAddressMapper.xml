<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.SubAddressMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.SubAddress">
        <id column="id" property="id" />
        <result column="sub_id" property="subId" />
        <result column="Address" property="Address" />
        <result column="cityid" property="cityid" />
        <result column="psuser" property="psuser" />
        <result column="wuliucompany" property="wuliucompany" />
        <result column="wuliuNo" property="wuliuNo" />
        <result column="paisongdtime" property="paisongdtime" />
        <result column="paisongState" property="paisongState" />
        <result column="issms_send" property="issmsSend" />
        <result column="waitTime" property="waitTime" />
        <result column="sendTime" property="sendTime" />
        <result column="expectTime" property="expectTime" />
        <result column="isXianHuo" property="isXianHuo" />
        <result column="userDate" property="userDate" />
        <result column="userTime" property="userTime" />
        <result column="isSpecial" property="isSpecial" />
        <result column="TuotouType" property="TuotouType" />
        <result column="ch999userid" property="ch999userid" />
        <result column="paisongAreaId" property="paisongAreaId" />
    </resultMap>
    <update id="updateWuliuCompanyAndWuliuNo">
        update dbo.SubAddress set wuliucompany = #{wuliuCom},wuliuNo = #{wuliuNo} where sub_id = #{subId}
    </update>

    <select id="getReceiverInfo" resultType="com.jiuji.oa.oacore.oaorder.vo.res.OutboundOrderRes$ReceiverInfo">
        SELECT s.tradeDate,s.sub_to as receiverName,s.sub_mobile as receiverPhone,s.sub_check as status,sa.Address as receiverAddress from sub s with(nolock)
        left join SubAddress sa with(nolock) on sa.sub_id = s.sub_id
                                             where s.sub_id = #{subId}
    </select>

</mapper>
