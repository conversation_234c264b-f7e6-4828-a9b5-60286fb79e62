<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.ApplyPrizeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.ApplyPrize">
        <id column="id" property="id" />
        <result column="applyId" property="applyId" />
        <result column="ch999_id" property="ch999Id" />
        <result column="ch999_name" property="ch999Name" />
        <result column="PrizeM" property="PrizeM" />
        <result column="area" property="area" />
        <result column="AreaId" property="AreaId" />
        <result column="addTime" property="addTime" />
        <result column="scoreDate" property="scoreDate" />
    </resultMap>
    <select id="getCh999UerId" resultType="java.lang.Integer">
    SELECT ap.ch999_id FROM dbo.ApplyPrize ap WITH(NOLOCK) INNER JOIN T_ApplyInfo t WITH(NOLOCK) ON ap.ApplyId = t.Id INNER JOIN T_ApprovalLog l
    WITH(NOLOCK) ON t.ApplyId = l.ApplyId  WHERE l.LogId =#{subId}

    </select>

</mapper>
