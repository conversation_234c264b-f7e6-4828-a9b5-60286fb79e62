<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.ReturnSubMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.ReturnSub">
        <id column="id" property="id" />
        <result column="states" property="states" />
        <result column="checkState" property="checkState" />
        <result column="finishTime" property="finishTime" />
        <result column="delTime" property="delTime" />
        <result column="title" property="title" />
        <result column="dtime" property="dtime" />
        <result column="type_" property="type" />
        <result column="piqianhao" property="piqianhao" />
        <result column="insourceid" property="insourceid" />
        <result column="area" property="area" />
        <result column="pjType" property="pjType" />
        <result column="realPrice" property="realPrice" />
        <result column="isSend" property="send" />
        <result column="mainsubid" property="mainsubid" />
        <result column="pzid" property="pzid" />
        <result column="outTime" property="outTime" />
        <result column="areaid" property="areaid" />
        <result column="inuser" property="inuser" />
    </resultMap>

    <resultMap id="ReturnSubResMap" type="com.jiuji.oa.oacore.oaorder.res.ReturnSubRes">
        <id column="id" property="id" />
        <result column="dtime" property="dtime" />
        <result column="shippingAddress" property="shippingAddress" />
        <result column="stats" property="stats" />
        <result column="received" property="received" />
        <result column="areaid" property="areaid" />
        <result column="expressName" property="expressName" />
        <result column="expressNum" property="expressNum" />
        <collection property="returnBaskets" ofType="com.jiuji.oa.oacore.oaorder.res.ReturnBasketRes">
            <id column="detail_id" property="id" />
            <result column="lcount" property="lcount" />
            <result column="price" property="price" />
            <result column="sub_id" property="subId" />
            <result column="ppriceid" property="ppriceid" />
            <result column="barCode" property="barCode" />
            <result column="productName" property="productName" />
        </collection>
    </resultMap>

    <sql id="Base_Column_List" >
        sub.id, sub.dtime, sub.area, sub.states stats, sub.areaid, sub.wuliuid, sub.isReceive received
    </sql>

    <sql id="whereSql" >
        <if test="subReq.startTime != null">
            and sub.dtime >= #{subReq.startTime}
        </if>
        <if test="subReq.endTime != null">
            and sub.dtime &lt;= #{subReq.endTime}
        </if>
        <if test="subReq.stats != null">
            and sub.states = #{subReq.stats}
        </if>
        <if test="subReq.type != null">
            and sub.type_ = #{subReq.type}
        </if>
    </sql>

    <select id="countReturnSub" resultType="integer">
        select count(*) from return_sub sub with(nolock)
        left join Ok3w_qudao qudao with(nolock) on sub.insourceid = qudao.id
        where qudao.userid = #{subReq.userid} and sub.states != 4
        <include refid="whereSql"/>
    </select>

    <select id="listReturnSub" resultMap="ReturnSubResMap">
        select a.*, d.company_address shippingAddress, b.id detail_id, b.lcount, b.price, b.sub_id, b.ppriceid, e.barCode,
        e.product_name productName, f.com expressName, f.nu expressNum
        from
        (select <include refid="Base_Column_List"/>
        from return_sub sub with(nolock) LEFT JOIN Ok3w_qudao c with(nolock) ON sub.insourceid = c.id
        WHERE c.userid = #{subReq.userid} and sub.states != 4
        <include refid="whereSql"/> ORDER BY id offset #{startRows} rows fetch next #{size} rows only
        ) a
        left join return_basket b with(nolock) on a.id = b.sub_id
        left join areainfo d with(nolock) on a.areaid = d.id
        left join productinfo e with(nolock) on b.ppriceid = e.ppriceid
        left join wuliu f with(nolock) on a.wuliuid = f.id
    </select>

</mapper>
