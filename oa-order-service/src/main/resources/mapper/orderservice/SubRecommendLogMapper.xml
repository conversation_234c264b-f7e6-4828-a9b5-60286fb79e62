<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.SubRecommendLogMapper">

    <!-- batchInsert -->
    <insert id="batchInsert">
        INSERT INTO sub_recommend_log
        (sub_id, main_product_basket_id, main_product_ppid, parts_product_ppid,
         parts_product_type, operator_staff, operator_ch999_id, create_time, update_time)
        VALUES
        <foreach collection="logs" item="item" separator=",">
            (#{item.subId}, #{item.mainProductBasketId}, #{item.mainProductPpid}, #{item.partsProductPpid},
            #{item.partsProductType}, #{item.operatorStaff}, #{item.operatorCh999Id}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

</mapper>
