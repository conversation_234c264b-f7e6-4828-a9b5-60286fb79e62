<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.CaigouSubMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.CaigouSub">
        <id column="id" property="id" />
        <result column="inuser" property="inuser" />
        <result column="dtime" property="dtime" />
        <result column="kinds" property="kinds" />
        <result column="area" property="area" />
        <result column="title" property="title" />
        <result column="insourceid" property="insourceid" />
        <result column="stats" property="stats" />
        <result column="pay1" property="pay1" />
        <result column="pay2" property="pay2" />
        <result column="pay1_dtime" property="pay1Dtime" />
        <result column="pay2_dtime" property="pay2Dtime" />
        <result column="comment" property="comment" />
        <result column="ruku_dtime" property="rukuDtime" />
        <result column="areaid" property="areaid" />
        <result column="zaituDtime" property="zaituDtime" />
        <result column="pid" property="pid" />
        <result column="CertifNumber" property="certifNumber" />
        <result column="piqianhao" property="piqianhao" />
        <result column="inPzid" property="inPzid" />
        <result column="expressName" property="expressName" />
        <result column="expressNum" property="expressNum" />
    </resultMap>

    <resultMap id="CaigouSubResMap" type="com.jiuji.oa.oacore.oaorder.res.CaigouSubRes">
        <id column="id" property="id" />
        <result column="dtime" property="dtime" />
        <result column="shippingAddress" property="shippingAddress" />
        <result column="amountPaid" property="amountPaid" />
        <result column="stats" property="stats" />
        <result column="areaid" property="areaid" />
        <result column="expressName" property="expressName" />
        <result column="expressNum" property="expressNum" />
        <collection property="caigouBaskets" ofType="com.jiuji.oa.oacore.oaorder.res.CaigouBasketRes">
            <id column="detail_id" property="id" />
            <result column="lcount" property="lcount" />
            <result column="inprice" property="inprice" />
            <result column="sub_id" property="subId" />
            <result column="ppriceid" property="ppriceid" />
            <result column="productName" property="productName" />
            <result column="product_color" property="productColor" />
        </collection>
    </resultMap>

    <sql id="Base_Column_List" >
        sub.id, sub.dtime, sub.area, sub.stats, sub.areaid, sub.expressName, sub.expressNum,
        (isnull(sub.pay1, 0) + isnull(sub.pay2, 0)) as amountPaid
    </sql>

    <sql id="wherePurchases" >
        <if test="subReq.startTime != null">
            and sub.dtime >= #{subReq.startTime}
        </if>
        <if test="subReq.endTime != null">
            and sub.dtime &lt;= #{subReq.endTime}
        </if>
        <if test="subReq.stats != null">
            and sub.stats = #{subReq.stats}
        </if>
        and sub.stats in(1,2,3)
    </sql>

    <sql id="wherePurchasesV2" >
        <if test="subReq.startTime != null">
            and cc.dtime >= #{subReq.startTime}
        </if>
        <if test="subReq.endTime != null">
            and cc.dtime &lt;= #{subReq.endTime}
        </if>
        <if test="subReq.stats != null">
            <choose>
                <when test="subReq.stats == 1">
                    and sub.stats in (1,2)
                </when>
                <when test="subReq.stats == 2">
                    and sub.stats in (3)
                </when>
            </choose>
        </if>
        <if test="subReq.payStats != null">
            <choose>
                <when test="subReq.payStats == 1">
                    and isnull(sub.payedPrice, 0)  > 0
                </when>
                <when test="subReq.payStats == 2">
                    and isnull(sub.payedPrice, 0)  = 0
                </when>
            </choose>
        </if>
        <if test="subReq.searchValue != null and subReq.searchValue != ''">
            <choose>
                <when test="subReq.searchOptions == 1">
                    AND sub.id = #{subReq.searchValue}
                </when>
                <when test="subReq.searchOptions == 2">
                    AND exists(select 1 from caigou_basket b with(nolock)
                    left join productinfo e with(nolock) on b.ppriceid = e.ppriceid
                    where sub.id = b.sub_id and e.product_name LIKE concat('%',#{subReq.searchValue},'%')
                    )
                </when>
                <when test="subReq.searchOptions == 3">
                    AND exists(select 1 from caigou_basket b with(nolock)
                    left join productBarcode e with(nolock) on b.ppriceid = e.ppriceid
                    where sub.id = b.sub_id and e.barCode LIKE concat('%',#{subReq.searchValue},'%')
                    )
                </when>
            </choose>
        </if>
        and sub.stats in(1,2,3)
        and sub.consignee is not null
        and sub.consignee_mobile is not null
        and sub.consignee_address is not null
        and cc.dtime >= DATEADD(yyyy,-1,GETDATE())
    </sql>
    <select id="countPurchaseSub" resultType="integer">
        select count(*) from caigou_sub sub with(nolock)
        left join Ok3w_qudao qudao with(nolock) on sub.insourceid = qudao.id
        where qudao.userid = #{subReq.userid}
        <include refid="wherePurchases"/>
    </select>

    <select id="countPurchaseSubV2" resultType="integer">
        select count(*) from caigou_sub sub with(nolock)
        left join Ok3w_qudao qudao with(nolock) on sub.insourceid = qudao.id
        left join (
        select c.cg_id,c.dtime,row_number() over(partition by c.cg_id order by c.dtime desc) as rank_ from dbo.caigouSub_comment c with(nolock)
        left join dbo.caigou_sub sub with(nolock) on sub.id=c.cg_id
        LEFT JOIN Ok3w_qudao o with(nolock) ON sub.insourceid = o.id
        where c.comment='[已审核] 操作' and o.userid = #{subReq.userid}
        )cc on cc.cg_id = sub.id and cc.rank_=1
        where qudao.userid = #{subReq.userid}
        <include refid="wherePurchasesV2"/>
    </select>

    <select id="listPurchaseSub" resultMap="CaigouSubResMap">
        select a.*, d.company_address shippingAddress, b.id detail_id, lcount, inprice, sub_id, b.ppriceid, e.product_name productName,
        e.product_color
        from
        (select <include refid="Base_Column_List"/>
            from caigou_sub sub with(nolock) LEFT JOIN Ok3w_qudao c with(nolock) ON sub.insourceid = c.id
            WHERE c.userid = #{subReq.userid} <include refid="wherePurchases"/> ORDER BY id offset #{startRows} rows fetch next #{size} rows only
        ) a
        left join caigou_basket b with(nolock) on a.id = b.sub_id
        left join areainfo d with(nolock) on a.areaid = d.id
        left join productinfo e with(nolock) on b.ppriceid = e.ppriceid
        order by a.id
    </select>

    <select id="listPurchaseSubV2" resultType="com.jiuji.oa.oacore.oaorder.res.CaigouSubResV2">
        select sub.id,sub.title,
               case when sub.stats in (1,2) then 1 when sub.stats in (3) then 2 else 0 end stats,
               isnull(sub.payedPrice,0) as amountPaid,
               sub.inuser, isnull(cc.dtime,sub.dtime) dtime,
               t.lcount,t.inprice as totalPrice,
               t.leftcount,
               t.sendcount,
               t.lcount-t.sendcount as toSendcount
        from caigou_sub sub with(nolock)
        LEFT JOIN Ok3w_qudao c with(nolock)
        ON sub.insourceid = c.id
            left join (select sum(lcount) lcount, sum(leftcount) leftcount,isnull(sum(sendcount),0) sendcount,
            sum(inprice*lcount) inprice, sub_id from caigou_basket b with(nolock) group by sub_id) t on sub.id = t.sub_id
        left join (
        select c.cg_id,c.dtime,row_number() over(partition by c.cg_id order by c.dtime desc) as rank_ from dbo.caigouSub_comment c with(nolock)
        left join dbo.caigou_sub sub with(nolock) on sub.id=c.cg_id
        LEFT JOIN Ok3w_qudao o with(nolock) ON sub.insourceid = o.id
        where c.comment='[已审核] 操作' and o.userid = #{subReq.userid}
        )cc on cc.cg_id = sub.id and cc.rank_=1
        WHERE c.userid = #{subReq.userid} <include refid="wherePurchasesV2"/>
        ORDER BY cc.dtime desc offset #{startRows} rows fetch next #{size} rows only
    </select>

    <select id="listPurchaseSubForDji" resultType="com.jiuji.oa.oacore.brand.dji.vo.PurchaseInfoVo">
        select b.sub_id purchaseID,isnull(k.imeidate,k.dtime) purchaseDate,'03' purchaseType,o.company_jc supplierName,k.insourceid2
        supplierCode,p.product_name+' '+isnull(p.product_color,'') prodName,
        p.barCode prodBarcode,k.imei SN,p.ppriceid [SKUID],'台' prodUnit,1 prodQuantity,k.inbeihuoprice
        prodPrice,k.inbeihuoprice [prodAmount],a.area [stockType]
        from dbo.product_mkc k WITH(nolock)
        left join dbo.productinfo p WITH(nolock) on p.ppriceid=k.ppriceid
        left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
        left join dbo.areainfo a WITH(nolock) on a.id=k.origareaid
        inner join dbo.mkcCaiGouBasket b WITH(nolock) on b.mkc_id=k.id
        where
        <if test="areaIds!=null and areaIds.size>0">
            a.id in
            <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                #{areaId}
            </foreach>
            and
        </if>
        o.id in (4815,4816) and p.brandID=1146 and b.sub_id is not null and k.imeidate  between #{startTime} and #{endTime}
        union all
        select b.sub_id purchaseID,s.finishTime purchaseDate,'04' purchaseType,o.company_jc supplierName,k.insourceid2
        supplierCode,p.product_name+' '+isnull(p.product_color,'') prodName,
        p.barCode prodBarcode,k.imei SN,p.ppriceid [SKUID],'台' prodUnit,1 prodQuantity,k.inbeihuoprice
        prodPrice,k.inbeihuoprice [prodAmount],a.area [stockType]
        from return_basket b WITH(nolock)
        inner join dbo.return_sub s WITH(nolock) on b.sub_id=s.id
        left join dbo.Ok3w_qudao o WITH(nolock) on o.id=s.insourceid
        left join dbo.product_mkc k WITH(nolock) on k.id=b.ppriceid
        left join dbo.productinfo p WITH(nolock) on k.ppriceid=p.ppriceid
        left join dbo.areainfo a WITH(nolock) on a.id=k.origareaid
        where
        <if test="areaIds!=null and areaIds.size>0">
            a.id in
            <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                #{areaId}
            </foreach>
            and
        </if>
        o.id in (4815,4816) and s.states=3 and s.type_=3 and b.sub_id is not null and p.brandID=1146 and s.finishTime between #{startTime} and #{endTime}
    </select>
    <select id="getPurchaseSubByIdV2" resultType="com.jiuji.oa.oacore.oaorder.res.CaigouSubDetailResV2">
        select sub.id,
               case when sub.stats in (1,2) then 1 when sub.stats in (3) then 2 else 0 end stats,
               sub.inuser,
               sub.dtime,
               sub.areaid,
               sub.consignee as name,
               sub.consignee_mobile as mobile,
               sub.consignee_address as shippingAddress,
               sub.expressName,
               sub.expressNum,
               c.company_jc as companyJc
        from caigou_sub sub with(nolock)
        LEFT JOIN Ok3w_qudao c with(nolock) ON sub.insourceid = c.id
        left join areainfo d with(nolock) on d.id = sub.areaid
        where sub.id = #{subId}
    </select>
    <select id="getPurchaseSubBasketV2" resultType="com.jiuji.oa.oacore.oaorder.res.CaigouBasketResV2">
        select b.id,
               p.ppriceid as skuId,
               p.barCode,
               p.product_name as productName,
               p.product_color as productColor,
               b.lcount,
               b.leftCount,
               isnull(b.sendcount,0) sendcount,
               isnull(b.lcount,0) - isnull(b.sendcount,0) toSendcount,
               b.inputcount,
               b.inprice,
               b.inprice * b.lcount as totalPrice,
               b.basket_id as basketId
        from caigou_basket b with(nolock)
        left join productinfo p with(nolock) on b.ppriceid = p.ppriceid
        where b.sub_id = #{subId}
    </select>
    <select id="getPurchaseSubLogV2" resultType="com.jiuji.oa.oacore.oaorder.res.CaigouSubLogResV2">
        select id,
               cg_id as cgId,
               inuser,
               dtime,
               showType,
               comment
        from caigouSub_comment with(nolock)
        where showType = 1
        and cg_id = #{subId}
    </select>
    <select id="getPurchaseSubBasketTotal" resultType="com.jiuji.oa.oacore.oaorder.res.CaigouBasketResV2">
        select sum(t.lcount) lcount,
               sum(t.leftCount) leftCount,
               sum(t.sendcount) sendcount,
               sum(t.toSendcount) toSendcount,
               sum(t.inputcount) inputcount,
               sum(t.totalPrice) totalPrice
        from (
                 select b.lcount,
                        b.leftCount,
                        isnull(b.sendcount,0) sendcount,
                        isnull(b.lcount,0) - isnull(b.sendcount,0) toSendcount,
                        b.inputcount,
                        b.inprice * b.lcount as totalPrice
                 from caigou_basket b with(nolock)
                 where b.sub_id = #{subId}
             ) t
    </select>
    <select id="getBarcodeByPpids" resultType="com.jiuji.oa.oacore.oaorder.res.CaigouBarcodeRes">
        select pb.ppriceid ppid,pb.barCode barCode,pb.isDefault from productBarcode pb with(nolock)
        where pb.ppriceid in
        <foreach collection="ppidList" item="ppid" separator="," close=")" open="(">
            #{ppid}
        </foreach>
        order by pb.isDefault desc
    </select>
    <select id="exportPurchaseSubV2" resultType="com.jiuji.oa.oacore.oaorder.res.CaigouSubExportResV2">
        select sub.id,
        sub.title,
        sub.stats,
        case when sub.stats in (1,2) then '进行中' when sub.stats in (3) then '已完成' else '' end statsName,
        sub.inuser,
        sub.consignee as consignee,
        sub.consignee_mobile as consigneeMobile,
        sub.consignee_address as consigneeAddress,
        isnull(cc.dtime,sub.dtime) dtime,
        p.ppriceid,
        p.barCode,
        p.product_name as productName,
        p.product_color as productColor,
        b.lcount,
        b.inprice,
        b.leftcount,
        isnull(b.inputcount,0) inputCount,
        isnull(b.sendcount,0) sendCount,
        isnull(b.lcount,0)-isnull(b.sendcount,0) as toSendcount
        from caigou_sub sub with(nolock)
        LEFT JOIN Ok3w_qudao c with(nolock)
        ON sub.insourceid = c.id
        LEFT JOIN caigou_basket b with(nolock) on sub.id = b.sub_id
        left join productinfo p with(nolock) on b.ppriceid = p.ppriceid
        left join (
        select c.cg_id,c.dtime,row_number() over(partition by c.cg_id order by c.dtime desc) as rank_ from dbo.caigouSub_comment c with(nolock)
        left join dbo.caigou_sub sub with(nolock) on sub.id=c.cg_id
        LEFT JOIN Ok3w_qudao o with(nolock) ON sub.insourceid = o.id
        where c.comment='[已审核] 操作' and o.userid = #{subReq.userid}
        )cc on cc.cg_id = sub.id and cc.rank_=1
        WHERE c.userid = #{subReq.userid} <include refid="wherePurchasesV2"/>
        ORDER BY cc.dtime desc offset 0 rows fetch next 10000 rows only
    </select>

</mapper>
