<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.SubMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.Sub">
        <id column="sub_id" property="subId"/>
        <result column="sub_date" property="subDate"/>
        <result column="sub_check" property="subCheck"/>
        <result column="sub_to" property="subTo"/>
        <result column="sub_tel" property="subTel"/>
        <result column="sub_pay" property="subPay"/>
        <result column="comment" property="comment"/>
        <result column="Inuser" property="Inuser"/>
        <result column="sub_mobile" property="subMobile"/>
        <result column="printxcount" property="printxcount"/>
        <result column="area" property="area"/>
        <result column="zitidianID" property="zitidianID"/>
        <result column="userid" property="userid"/>
        <result column="online_pay" property="onlinePay"/>
        <result column="Marketingid" property="Marketingid"/>
        <result column="ispiao" property="ispiao"/>
        <result column="subtype" property="subtype"/>
        <result column="delivery" property="delivery"/>
        <result column="yingfuM" property="yingfuM"/>
        <result column="yifuM" property="yifuM"/>
        <result column="feeM" property="feeM"/>
        <result column="youhui1M" property="youhui1M"/>
        <result column="shouxuM" property="shouxuM"/>
        <result column="jidianM" property="jidianM"/>
        <result column="tradeDate" property="tradeDate"/>
        <result column="tradeDate1" property="tradeDate1"/>
        <result column="dingjing" property="dingjing"/>
        <result column="subPID" property="subPID"/>
        <result column="trader" property="trader"/>
        <result column="fankuan" property="fankuan"/>
        <result column="islock" property="islock"/>
        <result column="areaid" property="areaid"/>
        <result column="coinM" property="coinM"/>
        <result column="expectTime" property="expectTime"/>
        <result column="returnDate" property="returnDate"/>
    </resultMap>
    <update id="updateSubComment">
        update sub set comment = '取货码：'+ #{spickupGoodsCode} + ',' + comment where sub_id in
        <foreach collection="subIds" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
    </update>

    <select id="listUnfinishedNetworkSub" resultType="com.jiuji.oa.oacore.oaorder.bo.NetOrderInfoBO">
        SELECT
        s.sub_id AS subId,
        s.delivery,
        s.trader,
        s.sub_date AS subDate,
        s.subtype AS subType,
        s.sub_check AS subCheck,
        s.yingfuM AS price,
        b.basket_id AS basketId,
        b.basket_date AS basketDate,
        b.ppriceid AS ppriceId,
        b.seller,
        b.type AS basketType,
        b.basket_count AS basketCount,
        pi.product_name AS productName,
        pi.product_color AS productColor,
        pi.ismobile1 AS isMobile,
        pi.cid
        FROM
        (
        SELECT
        sub_id,
        delivery,
        trader,
        sub_date,
        subtype,
        sub_check,
        islock,
        yingfuM
        FROM
        sub with(nolock)
        WHERE
        subtype IN ( 2,5,6,11,18,19,22,21,24,25,26,27,28,29,32,38)
        AND sub_date >= '2019-01-01'
        AND sub_check in (0,1)
        AND areaid = #{areaId}
        AND ((delivery IN (1,2) AND sub_pay IN (1,2,10)) OR (delivery in (4,5,6) AND yifuM >= yingfuM))
        ) s
        LEFT JOIN basket b with(nolock) ON b.sub_id = s.sub_id
        LEFT JOIN productinfo pi with(nolock) ON b.ppriceid = pi.ppriceid
        WHERE
        (pi.cid <![CDATA[<>]]> 370 AND pi.cid <![CDATA[<>]]> 164 or b.type = 15)
        <!--去掉多余的限制 保持跟count 的一致-->
        <!--AND (b.type <![CDATA[<>]]> 7 OR (b.type=7 and s.islock=4))-->
        AND ISNULL(b.isdel,0)=0
        <if test="subCheck==1">
            AND b.seller <![CDATA[<>]]> '网络' and DATEDIFF(d,isnull(s.sub_date,GETDATE()),GETDATE()) = 0
        </if>
        <if test="subCheck==0">
            AND b.seller = '网络'
        </if>
    </select>
    <!--         AND pi.ppriceid not in(87788,88051,87998,87997,87443,87458,88147,88185,87874,88255,88289,88361)-->

    <select id="listUnfinishedNetworkSubGoodProduct" resultType="com.jiuji.oa.oacore.oaorder.bo.NetOrderInfoBO">
        SELECT
        s.sub_id AS subId,
        s.delivery,
        s.trader,
        s.sub_date AS subDate,
        s.subtype AS subType,
        s.sub_check AS subCheck,
        s.yingfuM AS price,
        b.basket_id AS basketId,
        b.basket_date AS basketDate,
        b.ppriceid AS ppriceId,
        b.seller,
        b.type AS basketType,
        b.basket_count AS basketCount,
        pi.product_name AS productName,
        pi.product_color AS productColor,
        pi.ismobile1 AS isMobile,
        pi.cid
        FROM
        (
        SELECT
        sub_id,
        delivery,
        trader,
        sub_date,
        subtype,
        sub_check,
        yingfuM
        FROM
        recover_marketInfo with(nolock)
        WHERE
        subtype IN ( 2, 5, 6 ,11,18,19,22)
        AND sub_date &gt;= '2019-01-01'
        AND sub_check in (0,1)
        AND areaid = #{areaId}
        AND (yifuM &gt; 0
             <!-- OR sub_pay IN (2,10)-->
             or delivery IN (1,2,4)
            )
        <!-- 与count的条件同步 -->
        <!--AND delivery IN (1,2,4)-->
        ) s
        LEFT JOIN recover_marketSubInfo b with(nolock) ON b.sub_id = s.sub_id
        LEFT JOIN productinfo pi with(nolock) ON b.ppriceid = pi.ppriceid
        <where>
            <!--pi.cid <![CDATA[<>]]> 370 AND pi.cid <![CDATA[<>]]> 164 -->
            AND ISNULL(b.isdel,0)=0
            <if test="subCheck==1">
                AND b.seller <![CDATA[<>]]> '网络' and DATEDIFF(d,isnull(s.sub_date,GETDATE()),GETDATE()) = 0
            </if>
            <if test="subCheck==0">
                AND b.seller = '网络'
            </if>
        </where>
    </select>
    <!--         AND pi.ppriceid not in(87788,88051,87998,87997,87443,87458,88147,88185,87874,88255,88289,88361)-->
    <sql id="netWorkCondition">
        and not(
        <!-- 恒定过滤ppid-->
        b.ppriceid in (75774,75775,60175)
        <!-- 口罩ppid-->
        or (b.ppriceid in (87788,88051,87998,87997,87443,87458,88147,88185,87874,88255,88289,88361,89317,89304,88361,89305) and s.delivery in(1,3,6))
        )
        <if test="isOverTime">
            <!-- 是否超期-->

            and DATEDIFF(mi,isnull(b.basket_date,GETDATE()),GETDATE())>iif(isnull(s.delivery,0) = 1 and s.yingfuM = 1,24*60,iif(isnull(s.delivery,0)=2,5,30))
        </if>
    </sql>

    <select id="getNetworkSubOrderCountV2" resultType="java.lang.Integer">
            SELECT b.sub_id,b.ppriceid,b.basket_date,b.type into #basket_${timeStamp} FROM basket b with(nolock)
            inner join sub s with(nolock) on b.sub_id = s.sub_id
            where b.seller = '网络' AND ISNULL(b.isdel,0)=0  and b.basket_date >= GETDATE()-90
            and s.subtype IN ( 2, 5, 6 ,11,18,19,22,21,24,25,26,27,28,29,32,38) AND s.sub_date >= GETDATE()-90 AND s.sub_check in (0,1) AND s.areaid = #{areaId}
            ;

            SELECT   b.sub_id,b.basket_date  into #recover_basket_${timeStamp} FROM recover_marketSubInfo b with(nolock)
            inner join recover_marketInfo s with(nolock) on b.sub_id = s.sub_id
            where  b.seller = '网络' AND ISNULL(b.isdel,0)=0  and b.basket_date >= GETDATE()-90
            and s.subtype IN ( 2, 5, 6 ,11,18,19,22) AND s.sub_date >= GETDATE()-90 AND s.sub_check in (0,1) AND s.areaid = #{areaId}
            ;

            SELECT sum(t.orderCount) from
            (select COUNT(1) orderCount FROM sub s with(nolock)
            WHERE s.subtype IN ( 2, 5, 6 ,11,18,19,22,21,24,25,26,27,28,29,32,38) AND s.sub_date >= GETDATE()-90
              AND s.sub_check in (0,1) AND s.areaid = #{areaId} AND s.delivery IN (1,2) AND s.sub_pay IN (1,2,10)
            AND EXISTS (select 1 from   #basket_${timeStamp} b with(nolock) where b.sub_id =s.sub_id
            <if test="isOverTime">
                and DATEDIFF(mi,isnull(b.basket_date,GETDATE()),GETDATE())>iif(isnull(s.delivery,0) = 1 and s.yingfuM = 1,24*60,iif(isnull(s.delivery,0)=2,5,30))
            </if>
            )
            AND NOT EXISTS(
            SELECT 1 FROM #basket_${timeStamp} b with(nolock) inner join productinfo p with(nolock) on p.ppriceid = b.ppriceid where b.sub_id = s.sub_id
                and p.cid in (370,164) and b.type &lt;&gt; 15
            )
            AND NOT EXISTS(
            SELECT 1 FROM #basket_${timeStamp} b with(nolock)
            where b.sub_id = s.sub_id  and b.ppriceid in (75774,75775,60175,87788,88051,87998,87997,87443,87458,88147,88185,87874,88255,88289,88361,89317,89304,88361,89305)
                and b.type &lt;&gt; 15
            )
            union all
            SELECT COUNT(1) orderCount FROM sub s with(nolock)
            WHERE s.subtype IN ( 2, 5, 6 ,11,18,19,22, 21,24,25,26,27,28,29,32,38) AND s.sub_date >= GETDATE()-90
              AND s.sub_check in (0,1) AND s.areaid = #{areaId} AND s.delivery in (4,5,6) AND yifuM >= yingfuM
            AND EXISTS (select 1 from   #basket_${timeStamp} b with(nolock) where b.sub_id =s.sub_id
            <if test="isOverTime">
                and DATEDIFF(mi,isnull(b.basket_date,GETDATE()),GETDATE())>iif(isnull(s.delivery,0) = 1 and s.yingfuM = 1,24*60,iif(isnull(s.delivery,0)=2,5,30))
            </if>
            )
            AND NOT EXISTS(
            SELECT 1 FROM #basket_${timeStamp} b with(nolock) inner join productinfo p with(nolock) on p.ppriceid = b.ppriceid where b.sub_id = s.sub_id and p.cid in (370,164)
            )
            AND NOT EXISTS(
            SELECT 1 FROM #basket_${timeStamp} b with(nolock)
            where b.sub_id = s.sub_id  and b.ppriceid in (75774,75775,60175,87788,88051,87998,87997,87443,87458,88147,88185,87874,88255,88289,88361,89317,89304,88361,89305)
            )
            union all
            SELECT COUNT(1) orderCount FROM recover_marketInfo s with(nolock)
            WHERE s.subtype IN ( 2, 5, 6 ,11,18,19,22) AND s.sub_date >= GETDATE()-90 AND s.sub_check in (0,1) AND s.areaid = #{areaId} AND s.yifuM > 0
            AND EXISTS ( select 1 FROM #recover_basket_${timeStamp} b with(nolock) where b.sub_id = s.sub_id
            <if test="isOverTime">
                and DATEDIFF(mi,isnull(b.basket_date,GETDATE()),GETDATE())>iif(isnull(s.delivery,0) = 1 and s.yingfuM = 1,24*60,iif(isnull(s.delivery,0)=2,5,30))
            </if> )
            union all
            SELECT COUNT(1) orderCount FROM recover_marketInfo s with(nolock)
            WHERE s.subtype IN ( 2, 5, 6 ,11,18,19,22) AND s.sub_date >= GETDATE()-90 AND s.sub_check in (0,1) AND s.areaid = #{areaId} AND s.yifuM = 0 AND s.delivery IN (1,2,4)
            AND EXISTS (
            SELECT 1 FROM #recover_basket_${timeStamp} b with(nolock)
            where b.sub_id = s.sub_id
            <if test="isOverTime">
                and DATEDIFF(mi,isnull(b.basket_date,GETDATE()),GETDATE())>iif(isnull(s.delivery,0) = 1 and s.yingfuM = 1,24*60,iif(isnull(s.delivery,0)=2,5,30))
            </if>
            )
            ) t
    </select>


    <select id="getNetworkSubOrderCount" resultType="java.lang.Integer">
        SELECT sum(t.orderCount) from
        (
        SELECT COUNT(1) orderCount FROM sub s with(nolock)
        WHERE s.subtype IN ( 2, 5, 6 ,11) AND s.sub_date >= GETDATE()-90 AND s.sub_check in (0,1) AND s.areaid = #{areaId} AND s.delivery IN (1,2) AND s.sub_pay IN (1,2,10)
        AND EXISTS (
        SELECT 1 FROM basket b with(nolock)
        where b.sub_id = s.sub_id AND b.seller = '网络' AND ISNULL(b.isdel,0)=0
        <if test="isOverTime">
            and DATEDIFF(mi,isnull(b.basket_date,GETDATE()),GETDATE())>iif(isnull(s.delivery,0) = 1 and s.yingfuM = 1,24*60,iif(isnull(s.delivery,0)=2,5,30))
        </if>
        )
        AND NOT EXISTS(
        SELECT 1 FROM basket b with(nolock)
        inner join productinfo p with(nolock) on p.ppriceid = b.ppriceid
        where b.sub_id = s.sub_id AND b.seller = '网络' AND ISNULL(b.isdel,0) = 0 and p.cid in (370,164)
        )
        AND NOT EXISTS(
        SELECT 1 FROM basket b with(nolock)
        where b.sub_id = s.sub_id AND b.seller = '网络' AND ISNULL(b.isdel,0) = 0 and b.ppriceid in
        (75774,75775,60175,87788,88051,87998,87997,87443,87458,88147,88185,87874,88255,88289,88361,89317,89304,88361,89305)
        )
        union all
        SELECT COUNT(1) orderCount FROM sub s with(nolock)
        WHERE s.subtype IN ( 2, 5, 6 ,11) AND s.sub_date >= GETDATE()-90 AND s.sub_check in (0,1) AND s.areaid = #{areaId} AND s.delivery=4 AND s.yifuM > 0
        AND EXISTS (
        SELECT 1 FROM basket b with(nolock)
        where b.sub_id = s.sub_id AND b.seller = '网络' AND ISNULL(b.isdel,0)=0
        <if test="isOverTime">
            and DATEDIFF(mi,isnull(b.basket_date,GETDATE()),GETDATE())>iif(isnull(s.delivery,0) = 1 and s.yingfuM = 1,24*60,iif(isnull(s.delivery,0)=2,5,30))
        </if>
        )
        AND NOT EXISTS(
        SELECT 1 FROM basket b with(nolock)
        inner join productinfo p with(nolock) on p.ppriceid = b.ppriceid
        where b.sub_id = s.sub_id AND b.seller = '网络' AND ISNULL(b.isdel,0) = 0 and p.cid in (370,164)
        )
        AND NOT EXISTS(
        SELECT 1 FROM basket b with(nolock)
        where b.sub_id = s.sub_id AND b.seller = '网络' AND ISNULL(b.isdel,0) = 0 and b.ppriceid in
        (75774,75775,60175,87788,88051,87998,87997,87443,87458,88147,88185,87874,88255,88289,88361,89317,89304,88361,89305)
        )
        union all
        SELECT COUNT(1) orderCount FROM recover_marketInfo s with(nolock)
        WHERE s.subtype IN ( 2, 5, 6 ,11) AND s.sub_date >= GETDATE()-90 AND s.sub_check in (0,1) AND s.areaid = #{areaId} AND s.yifuM > 0
        AND EXISTS (
        SELECT 1 FROM recover_marketSubInfo b with(nolock)
        where b.sub_id = s.sub_id AND b.seller = '网络' AND ISNULL(b.isdel,0)=0
        <if test="isOverTime">
            and DATEDIFF(mi,isnull(b.basket_date,GETDATE()),GETDATE())>iif(isnull(s.delivery,0) = 1 and s.yingfuM = 1,24*60,iif(isnull(s.delivery,0)=2,5,30))
        </if>
        )
        union all
        SELECT COUNT(1) orderCount FROM recover_marketInfo s with(nolock)
        WHERE s.subtype IN ( 2, 5, 6 ,11) AND s.sub_date >= GETDATE()-90 AND s.sub_check in (0,1) AND s.areaid = #{areaId} AND s.yifuM = 0 AND s.delivery IN (1,2,4)
        AND EXISTS (
        SELECT 1 FROM recover_marketSubInfo b with(nolock)
        where b.sub_id = s.sub_id AND b.seller = '网络' AND ISNULL(b.isdel,0)=0
        <if test="isOverTime">
            and DATEDIFF(mi,isnull(b.basket_date,GETDATE()),GETDATE())>iif(isnull(s.delivery,0) = 1 and s.yingfuM = 1,24*60,iif(isnull(s.delivery,0)=2,5,30))
        </if>
        )
        )t
    </select>
    <!--         AND pi.ppriceid not in(87788,88051,87998,87997,87443,87458,88147,88185,87874,88255,88289,88361)-->

    <select id="isSendArea" resultType="Map">
        SELECT
        id AS areaId,
        area AS areaName,
        area_name AS areaRealName
        FROM
        view_areainfo with(nolock)
        WHERE
        ispass = 1
        AND isweb = 1
        AND id =#{areaId}
    </select>

    <select id="getXianXiaAllUserId" resultType="java.lang.Integer">
        SELECT TOP 1 u.ch999_id FROM dbo.ch999_user AS u  with(nolock)
        LEFT JOIN dbo.sub AS s with(nolock) ON s.areaid = u.area1id
        LEFT JOIN dbo.zhiwu z with(nolock) ON u.zhiwuid = z.id
        WHERE z.name IN ('店长','副店长') AND u.iszaizhi = 1 AND isnull(u.isshixi, 0) != 4
        AND s.sub_id =#{subId} ORDER BY z.leve ASC, u.ch999_id ASC
    </select>

    <select id="getXianXiaAllUsername" resultType="java.lang.String">
        SELECT TOP 1 u.ch999_name FROM dbo.ch999_user AS u  with(nolock)
        LEFT JOIN dbo.sub AS s with(nolock) ON s.areaid = u.area1id
        LEFT JOIN dbo.zhiwu z with(nolock) ON u.zhiwuid = z.id
        WHERE z.name IN ('店长','副店长') AND u.iszaizhi = 1 AND isnull(u.isshixi, 0) != 4
        AND s.sub_id =#{subId} ORDER BY z.leve ASC, u.ch999_id ASC
    </select>

    <select id="getTrader" resultType="java.lang.String">
        SELECT s.trader FROM dbo.sub s with(nolock) WHERE s.sub_id = #{subId} and not exists (select 1 from dbo.ch999_user u
        with(nolock) where s.trader=u.ch999_name and (left(u.depart_id,6)=100705 or u.area1id != 22 ) )
    </select>
    <select id="getOrderUserComplete" parameterType="com.jiuji.oa.oacore.oaorder.req.CheckOrderUserReq"
            resultType="com.jiuji.oa.oacore.oaorder.bo.CheckOrderUserBO">
        SELECT DISTINCT
        p.product_id,
        s.userid
        FROM
        sub s with(nolock)
        LEFT JOIN basket b with(nolock) ON s.sub_id= b.sub_id
        LEFT JOIN productinfo p with(nolock) ON b.ppriceid= p.ppriceid
        WHERE
        p.product_id in
        <foreach collection="req.productIds" index="index" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
        AND s.sub_check IN ( 1, 2, 3 )
        AND s.sub_date BETWEEN #{req.startTime}
        AND #{req.endTime}
        AND ISNULL( b.isdel, 0 ) = 0 UNION
        SELECT DISTINCT
        p.product_id,
        s.userid
        FROM
        recover_marketInfo s with(nolock)
        LEFT JOIN recover_marketSubInfo b with(nolock) ON s.sub_id= b.sub_id
        LEFT JOIN productinfo p with(nolock) ON b.ppriceid= p.ppriceid
        WHERE
        p.product_id in
        <foreach collection="req.productIds" index="index" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
        AND s.sub_check IN ( 1, 2, 3 )
        AND s.sub_date BETWEEN #{req.startTime}
        AND #{req.endTime}
        AND ISNULL( b.isdel, 0 ) =0
    </select>
    <select id="getIsRemind" resultType="java.lang.Integer">
        select count(1) from dbo.RushInfo with(nolock) where sub_id=${subId} and comment=#{comment} and dealTime is null and datediff(day,dtime,getdate())=0
    </select>
    <select id="getInfo" resultType="java.util.HashMap">
        SELECT TOP 1 b.UserName,dbo.basket.seller from dbo.BBSXP_Users b with(nolock)
								LEFT join sub s with(nolock) on b.id=s.userid
								LEFT JOIN basket with(nolock) ON basket.sub_id = s.sub_id
								 where s.sub_id=${subId}
    </select>
    <insert id="addRemind">
         insert into RushInfo(sub_id,comment,mobile)  values(${subId},#{comment},#{mobile})
    </insert>
    <select id="getUserIds" resultType="java.lang.Integer">
        select ch999_id from ch999_user with(nolock) where ch999_id in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and iszaizhi = 1 and isnull(islogin,0)=0 and exists(select 1 from sub with(nolock) where areaid = '16' and sub_id = #{subId})
        and exists(select 1 from dbo.basket b with(nolock) where exists( select * from dbo.product_mkc k with(nolock) where
        b.basket_id=k.basket_id and k.kc_check in (2,3)) and b.sub_id= ${subId})
        union
        select ch999_id from sub with(nolock)
        inner join subAddress addr with(nolock) on addr.sub_id = sub.sub_id
        inner join ch999_user with(nolock) on ch999_user.ch999_name = addr.psuser and ch999_user.iszaizhi = 1 and
        isnull(ch999_user.islogin,0)=0
        where sub.areaid = '16' and addr.psuser is not null and sub.sub_id = #{subId}
        and exists(select 1 from dbo.basket b with(nolock) where exists( select * from dbo.product_mkc k with(nolock) where
        b.basket_id=k.basket_id and k.kc_check in (2,3)) and b.sub_id= ${subId})
    </select>
    <select id="getSubCollentUser" resultType="com.jiuji.oa.oacore.oaorder.po.SubCollection">
        select id,sub_id,ch999_id,dtime,kind from dbo.subCollection with(nolock) where sub_id in
        <foreach collection="subIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and kind = ${type};
    </select>
    <select id="getSubTrader" resultType="java.lang.String">
        SELECT TOP 1 s.trader FROM sub AS s with(nolock) WHERE s.sub_id=#{subId}
    </select>

    <select id="getAreaId" resultType="java.lang.Integer">
        SELECT areaid FROM sub with(nolock) WHERE sub_id=#{subId}
    </select>
    <select id="getSubData" resultType="com.jiuji.oa.oacore.oaorder.bo.SubDataBO">
        SELECT
        s.sub_id subId,
        sub_to subTo,
        sub_date subDate,
        sub_check subCheck,
        delivery,
        zitidianID,
        sub_pay subPay,
        yingfuM,
        yifuM,
        feeM,
        youhui1M,
        jidianM,
        tradeDate,
        addr.userDate,
        addr.userTime,
        tradeDate1
        FROM
        dbo.sub s with(nolock)
        LEFT JOIN dbo.SubAddress addr with(nolock) ON s.sub_id= addr.sub_id
        WHERE
        s.sub_check!= 4
        <if test="smallShop == true">
            and s.zitidianID=#{zitidianId}
        </if>
        <if test="smallShop == false">
            and s.userid=#{userId}
        </if>
        AND NOT EXISTS (
        SELECT
        1
        FROM
        dbo.sub_delCollect c WITH(nolock)
        WHERE
        s.sub_id = c.sub_id
        AND c.subType = #{code}
        )
        <if test="subIds !=null and subIds.size>0">
            and s.sub_id in
            <foreach collection="subIds" index="index" item="subId" open="(" separator="," close=")">
                #{subId}
            </foreach>
        </if>
        <if test="type==1">
            and s.sub_check in (0,1)
        </if>
        <if test="type==2">
            and (s.sub_check in (2,6) or (s.sub_check=1 and s.yifuM=s.yingfuM))
        </if>
        <if test="type==5">
            and s.sub_check=3
        </if>
        <if test="type==6">
            and s.sub_check in (0,1,6) and s.yifuM <![CDATA[<>]]> s.yingfuM
        </if>
        <if test="isweixiu=='true'">
            and not exists
            ( select 1  from   dbo.basket b with(nolock) left join dbo.productinfo p with(nolock) on b.ppriceid = p.ppriceid  where  s.sub_id = b.sub_id and isnull(b.isdel, 0) = 0 and p.cid in
            ( 3, 58, 52, 7, 62, 33, 61, 67, 221, 297, 220, 98, 164, 165, 23, 50, 53, 68, 70, 31, 24, 25, 26, 27, 54, 28, 30, 29, 159,239, 69, 4 )
            and not exists
            ( select 1    from   dbo.basket c with(nolock)  left join dbo.productinfo p with(nolock) on c.ppriceid = p.ppriceid    where  s.sub_id = c.sub_id   and isnull(c.isdel, 0) = 0
              and p.cid not in
              ( 3, 58,52,7, 62, 33, 61,67, 221, 297,220,98,164,165, 23, 50, 53,68, 70, 31, 24,25, 26, 27, 54,28, 30, 29, 159,69, 4 ) ) )
        </if>
        <if test="recover == true">
            and exists(SELECT sub_id as recoverSubId FROM recover_sub rs with(nolock) WHERE sub_ido = s.sub_id AND rs.sub_pay = 3 AND rs.userid = #{userId})
        </if>
        <choose>
            <when test="xtenant == 0">
                and not exists(select 1 from dbo.areainfo a with(nolock) where s.areaid=a.id and a.printName<![CDATA[<>]]>'九机网')
            </when>
            <otherwise>
                and exists(select 1 from dbo.areainfo a with(nolock) where s.areaid=a.id and a.xtenant= #{xtenant})
            </otherwise>
        </choose>
    </select>
    <select id="getEvaluateScore" resultType="com.jiuji.oa.oacore.oaorder.res.EvaluateScoreBO">
        SELECT  id,sub_id FROM ${officeName}.dbo.EvaluateScore es with(nolock)  WHERE
         sub_id in
        <foreach collection="subIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
         AND es.EvaluateId IS NOT NULL
    </select>
    <select id="getTaoCanData" resultType="com.jiuji.oa.oacore.weborder.res.BasketSearchRes">
        SELECT
            basket_id basketId,
            isnull( PlanId, '' ) PlanId,
            Mobile,
            PackageType,
            isptype,
            ContractPeroid
        FROM
            taocan with(nolock)
        WHERE
            basket_id IN
            <foreach collection="asList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>
    <select id="getHaoMaData" resultType="com.jiuji.oa.oacore.weborder.res.BasketSearchRes">
        SELECT MAX
            ( id ) AS id,
            basket_id
        FROM
            haoma with(nolock)
        WHERE
            basket_id IN
            <foreach collection="asList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        GROUP BY
            basket_id
    </select>
    <select id="getSubIds" resultType="java.lang.Integer">
        SELECT sub_id FROM sub  with(nolock) where  sub_check in(1,2,3,5) and userid = #{userid} and sub_date between #{startTime} and #{endTime}
    </select>
    <select id="getRecoverIds" resultType="java.lang.Integer">
        SELECT sub_id FROM recover_marketInfo  with(nolock) where  sub_check in(1,2,3,5) and userid = #{userid} and sub_date between #{startTime} and #{endTime}
    </select>

    <select id="listDjiSalesInfo" resultType="com.jiuji.oa.oacore.brand.dji.vo.SalesInfoVo">
        select s.sub_id subID,s.tradeDate1 subDate,'销售出库单' subType,a.area storeCode,a.area_name storeName,p.ppriceid SKUID,
               p.barCode prodBarcode,p.product_name prodName,k.imei SN,'台' prodUnit,1 prodQuantity,
               b.price2 prodPrice,b.price2 prodAmount,a1.area stockType
        from dbo.basket b WITH(nolock)
        left join dbo.sub s WITH(nolock) on b.sub_id=s.sub_id
        left join dbo.productinfo p WITH(nolock) on b.ppriceid=p.ppriceid
        left join dbo.product_mkc k WITH(nolock) on k.basket_id=b.basket_id
        left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
        left join dbo.areainfo a WITH(nolock) on a.id=s.areaid
        left join dbo.areainfo a1 WITH(nolock) on a1.id=k.origareaid
        where
        <if test="areaIds!=null and areaIds.size>0">
            a1.id in
            <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                #{areaId}
            </foreach>
            and
        </if>
        o.id in (4815,4816) and isnull(b.isdel,0)=0 and s.sub_check in (3,9) and s.tradeDate1 between #{startTime} and #{endTime}
        and p.brandID=1146
        union
        select s.sub_id subID,s.returnDate subDate,'退款单' subType,a.area storeCode,a.area_name storeName,p.ppriceid SKUID,
               p.barCode prodBarcode,p.product_name prodName,k.imei SN,'台' prodUnit,1 prodQuantity,
               b.return_price prodPrice,b.return_price prodAmount,a1.area stockType
        from dbo.basket b WITH(nolock)
        left join dbo.sub s WITH(nolock) on b.sub_id=s.sub_id
        left join dbo.productinfo p WITH(nolock) on b.ppriceid=p.ppriceid
        left join dbo.product_mkc k WITH(nolock) on k.basket_id=b.basket_id
        left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
        left join dbo.areainfo a WITH(nolock) on a.id=s.areaid
        left join dbo.areainfo a1 WITH(nolock) on a1.id=k.origareaid
        where
        <if test="areaIds!=null and areaIds.size>0">
            a1.id in
            <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                #{areaId}
            </foreach>
            and
        </if>
        o.id in (4815,4816) and isnull(b.isdel,0)=0 and s.sub_check in (9) and s.returnDate between #{startTime} and #{endTime}
        and p.brandID=1146
        <if test="djAreaIds!=null and djAreaIds.size>0">
        union
        select s.sub_id subID,s.tradeDate1 subDate,'分销' subType,convert(varchar(20),bu.id) storeCode,bu.UserName storeName,p.ppriceid SKUID,
        p.barCode prodBarcode,p.product_name prodName,k.imei SN,'台' prodUnit,1 prodQuantity,
        b.price2 prodPrice,b.price2 prodAmount,a1.area stockType
        from dbo.basket b WITH(nolock)
        left join dbo.sub s WITH(nolock) on b.sub_id=s.sub_id
        left join dbo.productinfo p WITH(nolock) on b.ppriceid=p.ppriceid
        left join dbo.product_mkc k WITH(nolock) on k.basket_id=b.basket_id
        left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
        left join dbo.areainfo a1 WITH(nolock) on a1.id=k.origareaid
        left join dbo.BBSXP_Users bu  WITH(nolock) on bu.id=s.userid
        where
            a1.id in
            <foreach collection="djAreaIds" item="areaId" separator="," close=")" open="(">
                #{areaId}
            </foreach>
            and isnull(b.isdel,0)=0 and s.sub_check in (3,9) and s.tradeDate1 between #{startTime} and #{endTime}
        and p.brandID=1146 and o.id in (4815,4816)
        </if>
    </select>

    <select id="listXiaomiSalesInfo" resultType="com.jiuji.oa.oacore.brand.vo.SalesVO">
        SELECT
        s.sub_id subId,
        b.ppriceid sku_id,
        p.product_name+ ' ' + isnull( p.product_color, '' ) productName,
        1 productQuantity,
        k.imei,
        isnull(k.imeidate,k.inbeihuodate) stockDate,
        a.id areaId,
        a.area_name areaName,
        s.userid memberId,
        b.price ,
        s.tradeDate finishDate,
        '已完成' subStats,
        u.userName,
        s.sub_mobile subMobile,
        isnull(address.Address,company_address) address,
        b.price2,
        b.basket_count as count
        FROM
        dbo.basket b with(nolock)
        LEFT JOIN dbo.sub s with(nolock) ON b.sub_id= s.sub_id
        LEFT JOIN dbo.areainfo a with(nolock) ON a.id= s.areaid
        LEFT JOIN dbo.productinfo p with(nolock) ON b.ppriceid= p.ppriceid
        LEFT JOIN dbo.product_mkc k with(nolock) ON k.basket_id= b.basket_id
        LEFT JOIN BBSXP_Users u with(nolock) on s.userid = u.id
        LEFT JOIN SubAddress address with(nolock) on s.sub_id = address.sub_id
        WHERE
        isnull( b.isdel, 0 ) = 0
        AND s.sub_check in (3,9)
        AND a.ispass = 1
        AND a.kind2 in (0,1,2)
        AND s.tradeDate1 BETWEEN #{startTime}
        AND #{endTime}
        AND p.cid IN
        <foreach collection="cidList" separator="," open="(" close=")" item="cid" index="index">
            #{cid}
        </foreach>
        and p.brandID in
        <foreach collection="brandIdList" index="index" item="brandId" open="(" close=")" separator=",">
            #{brandId}
        </foreach>
        <if test="cityIds!=null and cityIds.size>0">
            and left(a.cityid,2) in
            <foreach collection="cityIds" open="(" close=")" separator="," item="cityId">
                #{cityId}
            </foreach>
        </if>
        UNION ALL
        SELECT
        s.sub_id subId,
        b.ppriceid skuId,
        p.product_name+ ' ' + isnull( p.product_color, '' ) productName,
        1 productQuantity,
        k.imei,
        isnull(k.imeidate,k.inbeihuodate) stockDate,
        a.id areaId,
        a.area_name areaName,
        s.userid memberId,
        b.price ,
        s.returnDate finishDate,
        '退款' subStats,
        u.userName,
        s.sub_mobile subMobile,
        isnull(address.Address,company_address) address,
        b.price2,
        b.basket_count as count
        FROM
        dbo.basket b with(nolock)
        LEFT JOIN dbo.sub s with(nolock) ON b.sub_id= s.sub_id
        LEFT JOIN dbo.areainfo a with(nolock) ON a.id= s.areaid
        LEFT JOIN dbo.productinfo p with(nolock) ON b.ppriceid= p.ppriceid
        LEFT JOIN dbo.product_mkc k with(nolock) ON k.basket_id= b.basket_id
        LEFT JOIN BBSXP_Users u with(nolock) on s.userid = u.id
        LEFT JOIN SubAddress address with(nolock) on s.sub_id = address.sub_id
        WHERE
        isnull( b.isdel, 0 ) = 0
        AND s.sub_check= 9
        AND a.ispass = 1
        AND a.kind2 in (0,1,2)
        AND s.returnDate BETWEEN #{startTime}
        AND #{endTime}
        AND p.cid IN
        <foreach collection="cidList" separator="," open="(" close=")" item="cid" index="index">
            #{cid}
        </foreach>
        and p.brandID in
        <foreach collection="brandIdList" index="index" item="brandId" open="(" close=")" separator=",">
            #{brandId}
        </foreach>
        <if test="cityIds!=null and cityIds.size>0">
            and left(a.cityid,2) in
            <foreach collection="cityIds" open="(" close=")" separator="," item="cityId">
                #{cityId}
            </foreach>
        </if>
    </select>
    <select id="xiaomiSalesProportion" resultType="java.math.BigDecimal">
        select cast(sum(case when
        p.brandID in
        <foreach collection="brandIdList" index="index" item="brandId" open="(" close=")" separator=",">
            #{brandId}
        </foreach>
        then 1.0 else 0.0 end)/count(1)*100 as decimal(5,2))
        from basket b with(nolock) left join product_mkc k with(nolock) on b.basket_id=k.basket_id
        left join productinfo p with(nolock) on b.ppriceid=p.ppriceid
        left join sub s with(nolock) on s.sub_id=b.sub_id
        left join dbo.areainfo a with(nolock) on a.id=s.areaid
        where 1=1 and s.sub_check in (3,9) and isnull(b.isdel,0)=0
        and s.tradeDate1 between  #{startTime} and #{endTime} and b.ismobile=1
        AND a.ispass = 1
        AND a.kind2 in (0,1,2)
        AND p.cid IN
        <foreach collection="cidList" separator="," open="(" close=")" item="cid" index="index">
            #{cid}
        </foreach>
        <if test="cityIds!=null and cityIds.size>0">
            and left(a.cityid,2) in
            <foreach collection="cityIds" open="(" close=")" separator="," item="cityId">
                #{cityId}
            </foreach>
        </if>
    </select>

    <select id="existsProcessingOrderInfo" resultType="java.lang.Integer">
        select count(1) from dbo.sub with(nolock) where sub_check in (0,1,2,3,5,6,9) and userid = #{userId}
    </select>

    <select id="getBasketIdsBySubId" resultType="java.lang.Integer">
        select b.basket_id from basket b with(nolock)
        inner join sub s with(nolock) on s.sub_id = b.sub_id
        where b.sub_id = #{subId}
    </select>

    <select id="getAfterSaleQuantity" resultType="java.lang.Integer">
        SELECT count(1) FROM dbo.shouhou s with(nolock)
        WHERE s.userid != 76783
        AND s.baoxiu !=2
        AND isnull(s.xianshi,0) = 1
        and s.basket_id in
        <foreach item="basketId" collection="basketIdsBySubId" separator="," open="(" close=")" index="">
            #{basketId}
        </foreach>
        and not exists (select 1 from dbo.sub_delCollect c with(nolock) where s.id = c.sub_id and c.subType=4)
    </select>

    <select id="getSmallProQuantity" resultType="java.lang.Integer">
        SELECT count(1) FROM dbo.Smallpro s with(nolock)
        where s.sub_id = #{subId} and isnull(s.isdel, 0)=0 and s.userid != 76783
        and not exists (select 1 from dbo.sub_delCollect c with(nolock) where s.id = c.sub_id and c.subType=5)
    </select>
    <sql id="getTaxSubInfoSql">
        <choose>
            <when test="type == null || type == 0">
                <!--新机订单-->
                select s.sub_check as checkState, s.sub_id subId,s.userid userId,s.tradeDate1 tradeDate,s.areaid areaId,a.area area,isnull(a.company_tel1,a.company_tel2) areaTel,0 type
                from dbo.sub s with(nolock)
                    left join dbo.areainfo a with(nolock) on a.id=s.areaid
                where  s.sub_id = #{subId}
                  <!--and a.xtenant = #{xtenant}-->
            </when>
            <when test="type == 1">
                <!--售后单-->
                select s.isquji as checkState , s.id as subId,s.userid userId,s.offtime tradeDate,s.areaid areaId,a.area area,isnull(a.company_tel1,a.company_tel2) areaTel,1 type
                from dbo.shouhou s with(nolock)
                left join dbo.areainfo a with(nolock) on a.id = s.areaid
                where s.id = #{subId}
                <!--and a.xtenant = #{xtenant}-->
            </when>
            <when test="type == 2">
                <!--良品单-->
                select s.sub_check as checkState, s.sub_id as subId,s.userid userId,s.tradeDate1 tradeDate,s.areaid areaId,a.area area,isnull(a.company_tel1,a.company_tel2) areaTel,2 type
                from dbo.recover_marketInfo s with(nolock)
                left join dbo.areainfo a with(nolock) on a.id=s.areaid
                where s.sub_id = #{subId}
                <!--and a.xtenant = #{xtenant}-->
            </when>
        </choose>
    </sql>
    <select id="getTaxSubInfo" resultType="com.jiuji.oa.oacore.weborder.res.TaxSubInfoRes">
        <include refid="getTaxSubInfoSql"/>
    </select>
    <select id="getTaxSubInfoHis" resultType="com.jiuji.oa.oacore.weborder.res.TaxSubInfoRes">
        <include refid="getTaxSubInfoSql"/>
    </select>

    <!-- countOrdersByAreaAttr -->
    <select id="countOrdersByAreaAttr" resultType="map">
        SELECT a.attribute attr, COUNT(1) orderCount
        FROM sub s with(nolock)
            LEFT JOIN basket b with(nolock) on b.sub_id = s.sub_id
            LEFT JOIN dbo.areainfo a with(nolock) on s.areaid = a.id
        WHERE s.sub_check IN (1,2,3,5,6,7) AND isnull(b.isdel, 0) = 0
          AND b.basket_date BETWEEN #{startTime} AND #{endTime}
        GROUP BY a.attribute
    </select>
    <select id="getNetworkYuyueCountCount" resultType="java.lang.Integer">
        select COUNT(1) orderCount FROM shouhou_yuyue s with(nolock)
        WHERE isnull(s.isdel,0) = 0 and s.from_source IN (1,2,3) AND s.dtime >= GETDATE()-90 AND s.stats = 1 AND s.areaid = #{areaId}
        <if test="isOverTime">
            <!-- 超过30分钟就算超时 -->
            and DATEDIFF(mi,isnull(s.dtime,GETDATE()),GETDATE()) > 30
        </if>

    </select>
    <select id="listUnfinishedNetworkYuYue" resultType="com.jiuji.oa.oacore.oaorder.bo.NetOrderInfoBO">
        SELECT
            s.id AS subId,
            s.stype delivery,
            s.inuser trader,
            s.dtime AS subDate,
            s.from_source AS subType,
            s.stats AS subCheck,
            b.price1 AS price,
            s.basket_id AS basketId,
            s.dtime AS basketDate,
            s.ppriceid AS ppriceId,
            b.seller as seller,
            b.type AS basketType,
            1 AS basketCount,
            pi.product_name AS productName,
            pi.product_color AS productColor,
            isnull(s.ismobile,0) AS isMobile,
            pi.cid as cid
        FROM shouhou_yuyue s with(nolock)
        left join basket b with(nolock) on s.basket_id >0 and b.basket_id = s.basket_id
        LEFT JOIN productinfo pi with(nolock) ON s.ppriceid > 0 and s.ppriceid = pi.ppriceid
        where isnull(s.isdel,0) = 0 and s.from_source IN (1,2,3) AND s.dtime >= GETDATE()-90  AND s.areaid = #{areaId}
            <choose>
                <when test="subCheck==1">AND s.stats &lt;&gt; 1 and DATEDIFF(d,isnull(s.dtime,GETDATE()),GETDATE()) = 0</when>
                <otherwise>AND s.stats = 1</otherwise>
            </choose>
    </select>
    <select id="selectSubByBasketId" resultType="com.jiuji.oa.oacore.oaorder.po.Sub">
        select *
        from dbo.sub s with (nolock)
         left join dbo.basket b with (nolock) on b.sub_id = s.sub_id
        where basket_id=#{basketId}
    </select>

    <select id="listUnfinishedNetworkRecycle" resultType="com.jiuji.oa.oacore.oaorder.bo.NetOrderInfoBO">
        select
        s.sub_id as subId,
        s.sub_delivery as recycleSubDelivery,
        s.inuser as trader,
        s.dtime as subDate,
        #{subCheck} as subCheck,
        rb.price as price,
        rb.id as basketId,
        rb.intime as basketDate,
        rb.ppriceid as ppriceId,
        s.inuser as seller,
        1 as basketCount,
        p.product_name as productName,
        p.product_color as productColor,
        p.ismobile1 as isMobile,
        p.cid as cid
        from recover_sub s WITH (NOLOCK)
        inner join recover_basket rb WITH (NOLOCK) on s.sub_id = rb.sub_id
        inner join productinfo_huishou p WITH (NOLOCK) on rb.ppriceid = p.ppriceid
        where ISNULL(s.isnetsub, 0) = 1
        and ISNULL(rb.isdel,0) = 0
        and s.areaid = #{areaId}
        <choose>
            <when test="subCheck==1">
                AND s.sub_check &lt;&gt; 0 and DATEDIFF(d,isnull(s.dtime,GETDATE()),GETDATE()) =0
            </when>
            <otherwise>AND s.sub_check = 0</otherwise>
        </choose>
    </select>

    <select id="getDevicesByUser" resultType="com.jiuji.oa.oacore.server.entity.ServiceRecord">
        select sr.id,sr.areaid,sr.tradedate,sr.imei,sr.basket_idBind,sr.ServiceType,isnull(sr.price,0) as price
        ,isnull(sr.feiyong,0) as feiyong,sr.startTime,sr.endTime,sr.ppriceid,sr.servicesTypeBindId
        ,isnull(sr.classification,1) as classification,sr.discountBasketId,sr.server_shouhou_id serverShouhouId
        from ServiceRecord sr with(nolock)
        where isnull(sr.isdel,0)=0 and sr.ServiceType = 115 and sr.discountBasketId is NULL
        and sr.startTime &lt; GETDATE() and sr.endTime > GETDATE()
        and NOT EXISTS (SELECT 1 FROM ServiceRecord s2 where s2.imei = sr.imei and s2.ServiceType = 104 and feiyong > 0)
        and sr.imei in
        <foreach collection="imeiList" item="imei" open="(" close=")" separator=",">
            #{imei}
        </foreach>
    </select>

    <select id="getSubById" resultType="com.jiuji.oa.oacore.oaorder.res.DeviceRes">
        SELECT
            b.ppriceid AS ppriceId,
            k.id AS mkcId,
            s.tradedate AS tradeDate,
            k.imei AS imei,
            s.userid AS userId,
            s.areaid AS mkcAreaId,
            s.sub_mobile AS subMobile,
            b.sub_id AS subId,
            b.basket_id AS basketId,
            ISNULL(b.type, 0) AS basketType,
            s.subtype AS subType,
            b.price AS price,
            b.basket_date AS basketDate,
            s.tradeDate1 AS transactionDate,
            s.sub_date AS subDate,
            s.sub_check
        FROM product_mkc k WITH (NOLOCK)
        LEFT JOIN basket b WITH (NOLOCK) ON k.basket_id = b.basket_id
        LEFT JOIN sub s WITH (NOLOCK) ON b.sub_id = s.sub_id
        WHERE
            ismobile = 1
            AND ISNULL(b.isdel, 0) = 0
            AND s.sub_check = 3
            <if test="subMobile != null and subMobile != ''">
                AND s.sub_mobile = #{subMobile}
            </if>
            <if test="imei != null and imei != ''">
                AND k.imei = #{imei}
            </if>
    </select>

    <select id="getGiftByBasketIds" resultType="com.jiuji.oa.oacore.oaorder.res.DeviceRes$Gift">
        SELECT
            b.basket_id AS basketId,
            b.price1 AS price,
            b.ppriceid AS ppriceId,
            b.giftid,
            b.basket_count AS quantity
        FROM
            dbo.basket b WITH (NOLOCK)
        WHERE ISNULL(b.isdel, 0) = 0
        AND ISNULL(b.giftid, 0) &lt;> 0
        AND b.price = 0
        AND b.giftid IN
        <foreach collection="bindBasketIds" item="basketId" open="(" close=")" separator=",">
            #{basketId}
        </foreach>
    </select>

    <select id="getBasketById" resultType="com.jiuji.oa.oacore.oaorder.po.Basket">
        SELECT
         *
        FROM
        dbo.basket b WITH (NOLOCK)
        WHERE b.basket_id = #{basketId}
    </select>

    <select id="getNationalSubsidy" resultType="java.lang.Integer">
        select sub_id FROM subFlagRecord sr WITH(NOLOCK)
        where sub_id in
          <foreach collection="subIdList" item="subId" open="(" close=")" separator=",">
            #{subId}
          </foreach>
          and sr.flagType = 7
    </select>
</mapper>
