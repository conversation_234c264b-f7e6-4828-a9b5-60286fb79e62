<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.MsoftMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.Msoft">
        <id column="id" property="id" />
        <result column="problem" property="problem" />
        <result column="inuser" property="inuser" />
        <result column="imei" property="imei" />
        <result column="tradedate" property="tradedate" />
        <result column="modidate" property="modidate" />
        <result column="area" property="area" />
        <result column="userid" property="userid" />
        <result column="ppriceid" property="ppriceid" />
        <result column="buyarea" property="buyarea" />
        <result column="isticheng" property="isticheng" />
        <result column="pingjia" property="pingjia" />
        <result column="isfan" property="isfan" />
        <result column="areaid" property="areaid" />
        <result column="buyareaid" property="buyareaid" />
        <result column="imeifid" property="imeifid" />
        <result column="sub_id" property="subId" />
        <result column="evaluateid" property="evaluateid" />
        <result column="ishuishou" property="ishuishou" />
        <result column="mobileServeiceType" property="mobileServeiceType" />
        <result column="question_type" property="questionType" />
        <result column="fidlist" property="fidlist" />
        <result column="isxianchang" property="isxianchang" />
        <result column="username" property="username" />
        <result column="usermobile" property="usermobile" />
        <result column="expectdate" property="expectdate" />
    </resultMap>

    <select id="getMsoftCountBySubId" resultType="java.lang.Integer">
        SELECT count(*) FROM msoft with(nolock) WHERE imei in (
        SELECT k.imei FROM basket b WITH(nolock)
        LEFT JOIN product_mkc k WITH(nolock) ON k.basket_id = b.basket_id
        WHERE sub_id = #{subId}
        AND ISNULL(ismobile,0) = 1) AND  DATEDIFF(DAY,modidate,tradedate) = 0
    </select>

    <select id="getMsoftInuser" resultType="java.lang.String">
        SELECT TOP 1 inuser FROM msoft with(nolock) WHERE imei in (
        SELECT k.imei FROM basket b WITH(nolock)
        LEFT JOIN product_mkc k WITH(nolock) ON k.basket_id = b.basket_id
        WHERE sub_id = #{subId}
        AND ISNULL(ismobile,0) = 1 and isnull(b.isdel,0)=0 )
        ORDER BY id desc
    </select>
    <select id="getInUser" resultType="java.lang.String">
        SELECT TOP 1 inuser FROM msoft with(nolock) WHERE imei in (
                        SELECT k.imei FROM basket b WITH(nolock)
                        LEFT JOIN product_mkc k WITH(nolock) ON k.basket_id = b.basket_id
                        WHERE sub_id = #{subId}
                        AND ISNULL(ismobile,0) = 1 and isnull(b.isdel,0)=0 )
                        ORDER BY id desc
    </select>
    <select id="getInUserV2" resultType="java.lang.String">
        SELECT inuser FROM dbo.msoft with(nolock) WHERE id=#{subId}
    </select>
</mapper>
