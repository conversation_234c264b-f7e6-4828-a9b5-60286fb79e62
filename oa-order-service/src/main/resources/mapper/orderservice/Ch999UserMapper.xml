<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.Ch999UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.Ch999User">
        <id column="ch999_id" property="ch999Id"/>
        <result column="ch999_name" property="ch999Name"/>
        <result column="pwd" property="pwd"/>
        <result column="userGroup" property="userGroup"/>
        <result column="work" property="work"/>
        <result column="usersex" property="usersex"/>
        <result column="money1" property="money1"/>
        <result column="address" property="address"/>
        <result column="postcode" property="postcode"/>
        <result column="hometel" property="hometel"/>
        <result column="jtname" property="jtname"/>
        <result column="homeaddress" property="homeaddress"/>
        <result column="mobile" property="mobile"/>
        <result column="bankname" property="bankname"/>
        <result column="bankcard" property="bankcard"/>
        <result column="iszaizhi" property="iszaizhi"/>
        <result column="money2" property="money2"/>
        <result column="lockzl" property="lockzl"/>
        <result column="indate" property="indate"/>
        <result column="IDnumber" property="IDnumber"/>
        <result column="nickname" property="nickname"/>
        <result column="area1" property="area1"/>
        <result column="area" property="area"/>
        <result column="duanhao" property="duanhao"/>
        <result column="bankzhangfu" property="bankzhangfu"/>
        <result column="sphoto" property="sphoto"/>
        <result column="bphoto" property="bphoto"/>
        <result column="IDcard" property="IDcard"/>
        <result column="fenjihao" property="fenjihao"/>
        <result column="WorkKeys" property="workKeys"/>
        <result column="islogin" property="islogin"/>
        <result column="s_date" property="sDate"/>
        <result column="e_date" property="eDate"/>
        <result column="zhiwu" property="zhiwu"/>
        <result column="bumeng2" property="bumeng2"/>
        <result column="bumeng3id" property="bumeng3id"/>
        <result column="offtime" property="offtime"/>
        <result column="Education" property="education"/>
        <result column="graduation" property="graduation"/>
        <result column="default_index" property="defaultIndex"/>
        <result column="wuxiangdate" property="wuxiangdate"/>
        <result column="wuxiang" property="wuxiang"/>
        <result column="baoxianM" property="baoxianM"/>
        <result column="payway" property="payway"/>
        <result column="daifaM" property="daifaM"/>
        <result column="bankname1" property="bankname1"/>
        <result column="bankcard1" property="bankcard1"/>
        <result column="bankzhangfu1" property="bankzhangfu1"/>
        <result column="workhtdate" property="workhtdate"/>
        <result column="pwd2" property="pwd2"/>
        <result column="leve1" property="leve1"/>
        <result column="leve2" property="leve2"/>
        <result column="islirun" property="islirun"/>
        <result column="default_area" property="defaultArea"/>
        <result column="butieM" property="butieM"/>
        <result column="baoxianM1" property="baoxianM1"/>
        <result column="birsday" property="birsday"/>
        <result column="zhuanzhendate" property="zhuanzhendate"/>
        <result column="cmbdata" property="cmbdata"/>
        <result column="j_xishu" property="jXishu"/>
        <result column="onlinedtime" property="onlinedtime"/>
        <result column="onlinekind" property="onlinekind"/>
        <result column="weixinopenid" property="weixinopenid"/>
        <result column="weixindtime" property="weixindtime"/>
        <result column="EduLevel" property="eduLevel"/>
        <result column="isjifen" property="isjifen"/>
        <result column="zhengzhimianbao" property="zhengzhimianbao"/>
        <result column="xuewei" property="xuewei"/>
        <result column="zhuanye" property="zhuanye"/>
        <result column="jiguan" property="jiguan"/>
        <result column="isshixi" property="isshixi"/>
        <result column="cityid" property="cityid"/>
        <result column="dcityid" property="dcityid"/>
        <result column="Roles" property="roles"/>
        <result column="depart_id" property="departId"/>
        <result column="appidentifier" property="appidentifier"/>
        <result column="scheduleId" property="scheduleId"/>
        <result column="pwdram" property="pwdram"/>
        <result column="email" property="email"/>
        <result column="EmailAddress" property="emailAddress"/>
        <result column="isapplogout" property="isapplogout"/>
        <result column="default_areaid" property="defaultAreaid"/>
        <result column="areaid" property="areaid"/>
        <result column="area1id" property="area1id"/>
        <result column="salt" property="salt"/>
        <result column="mainRole" property="mainRole"/>
        <result column="zhiji" property="zhiji"/>
        <result column="sphotoId" property="sphotoId"/>
        <result column="bphotoId" property="bphotoId"/>
        <result column="IDcardid" property="IDcardid"/>
        <result column="qqhao" property="qqhao"/>
        <result column="nation" property="nation"/>
        <result column="Szhiji" property="szhiji"/>
        <result column="healthCondition" property="healthCondition"/>
        <result column="height" property="height"/>
        <result column="EntryChannel" property="entryChannel"/>
        <result column="weight" property="weight"/>
        <result column="isMarry" property="isMarry"/>
        <result column="FingerPrint" property="fingerPrint"/>
        <result column="registerNatur" property="registerNatur"/>
        <result column="homeInsure" property="homeInsure"/>
        <result column="totalPoint" property="totalPoint"/>
        <result column="point" property="point"/>
        <result column="lastLoginTime" property="lastLoginTime"/>
        <result column="QuitTag" property="quitTag"/>
        <result column="zhuanshiyondate" property="zhuanshiyondate"/>
        <result column="wuxianCompany" property="wuxianCompany"/>
        <result column="dressSize" property="dressSize"/>
        <result column="mainStation" property="mainStation"/>
        <result column="homeaddrqu" property="homeaddrqu"/>
        <result column="homeaddrdoor" property="homeaddrdoor"/>
        <result column="homeaddrrom" property="homeaddrrom"/>
        <result column="adddate" property="adddate"/>
        <result column="NamePY" property="namePY"/>
        <result column="ganweiM" property="ganweiM"/>
        <result column="isWxBind" property="isWxBind"/>
        <result column="zhiwuid" property="zhiwuid"/>
        <result column="EducationType" property="educationType"/>
        <result column="EducationType1" property="educationType1"/>
        <result column="graduation1" property="graduation1"/>
        <result column="zhuanye1" property="zhuanye1"/>
        <result column="zwxishu" property="zwxishu"/>
        <result column="zjxishu" property="zjxishu"/>
        <result column="pubZhanhu" property="pubZhanhu"/>
        <result column="pubBankCard" property="pubBankCard"/>
        <result column="pubBankName" property="pubBankName"/>
        <result column="ch999Type" property="ch999Type"/>
        <result column="zwSalary" property="zwSalary"/>
        <result column="childredStationedAllowance" property="childredStationedAllowance"/>
        <result column="FilialAllowance" property="filialAllowance"/>
        <result column="stayOutsiceAllowance" property="stayOutsiceAllowance"/>
        <result column="oilAllowance" property="oilAllowance"/>
        <result column="settleAllowance" property="settleAllowance"/>
        <result column="stationAllowance" property="stationAllowance"/>
        <result column="carAllowance" property="carAllowance"/>
        <result column="enterpriseBind" property="enterpriseBind"/>
        <result column="isPromise" property="isPromise"/>
        <result column="IDStime" property="IDStime"/>
        <result column="IDEtime" property="IDEtime"/>
        <result column="jsxishu" property="jsxishu"/>
        <result column="salary" property="salary"/>
        <result column="expatriateDate" property="expatriateDate"/>
        <result column="expatriateXi" property="expatriateXi"/>
        <result column="laborRelations" property="laborRelations"/>
    </resultMap>

    <select id="getCh999Users" resultType="com.jiuji.oa.oacore.oaorder.po.Ch999User">
        SELECT a.IDnumber,a.ch999_id,a.ch999_name,a.default_areaid,a.depart_id,z.name as zhiwu,a.mobile,a.areaid,
        a.iszaizhi,a.area1id,a.pwd,a.nickname,a.WorkKeys,a.Email,b.url,a.Roles,z.leve,a.sphoto,a.sphotoId,a.zhiji,
        a.Szhiji,a.islogin,a.isshixi,a.indate,a.mainRole,a.scheduleId bkind,a.birsday,a.weixinopenid,a.NamePY,
        a.zhiwuid,a.graduation,a.graduation1,a.zhuanye,a.zhuanye1,a.EducationType,a.Education,a.EducationType1,
        a.mainStation ,a.qqhao,a.zhuanzhendate,a.usersex,a.enterpriseBind,a.zhengzhimianbao
        FROM dbo.ch999_user a with(nolock)
        left join ${officeName}.dbo.appHeadimg b with(nolock) on a.ch999_id=b.ch999_id
        left join zhiwu z with(nolock) on a.zhiwuid=z.id
        where a.iszaizhi=1 and a.ch999_id>1
    </select>
    <select id="getCh999UerId" resultType="java.lang.Integer">
        SELECT TOP 1 u.ch999_id FROM dbo.ch999_user AS u  with(nolock)
        LEFT JOIN dbo.sub AS s with(nolock) ON s.areaid = u.area1id
        LEFT JOIN dbo.zhiwu z with(nolock) ON u.zhiwuid = z.id
        WHERE z.name IN ('店长','副店长') AND u.iszaizhi = 1
        AND isnull(u.isshixi, 0) != 4 AND s.sub_id = #{subId} ORDER BY z.leve ASC, u.ch999_id ASC
    </select>
    <select id="getUerId" resultType="java.lang.Integer">
        SELECT TOP 1 u.ch999_id FROM dbo.ch999_user AS u  with(nolock)
        LEFT JOIN dbo.shouhou AS s with(nolock) ON s.areaid = u.area1id
        LEFT JOIN dbo.zhiwu z with(nolock) ON u.zhiwuid = z.id
        WHERE u.iszaizhi = 1 AND CHARINDEX('17',u.roles)>0 AND isnull(u.isshixi, 0) != 4 AND s.id = #{subId}
        ORDER BY z.leve DESC, u.ch999_id ASC
    </select>
    <select id="getXtenantOfSalary" resultType="com.jiuji.oa.oacore.salary.bo.dto.SalaryBalanceInfoDto">
        SELECT A.xtenant as xtenant,A.id as areaId,M.mainRole,M.zhiwuid as zhiWu, M.isshixi as isShiXi, M.mainStation
        FROM dbo.ch999_user U WITH (NOLOCK)
         LEFT JOIN (select ch999_id,area1id,mainRole,zhiwuid,isshixi,mainStation
                    from dbo.ch999_month_user with (nolock)
                    where CONVERT(VARCHAR(6), dtime, 112) = #{month}
                      and ch999_id = #{ch999Id}) M ON M.ch999_id = U.ch999_id
            LEFT JOIN areainfo A WITH (NOLOCK) ON A.ID = ISNULL(M.area1id, U.area1id)
        WHERE U.ch999_id = #{ch999Id}
    </select>
    <select id="getCh999UserBasicBO" resultType="com.jiuji.oa.oacore.oaorder.bo.Ch999UserBasicBO">
        select a.ch999_id   as ch999Id,
               a.ch999_name as ch999Name,
               a.mobile     as mobile,
               a.area       as area,
               a.areaid     as areaId,
               a.departCode as departCode,
               a.area1      as area1,
               a.area1id    as area1id,
               a.zhiwu      as zhiwu,
               a.zhiwuid    as zhiwuId,
               a.mainStation as mainStation,
               a.mainRole   as mainRole,
               a.roles      as roles,
               a.indate     As inDate,
               a.isshixi    as isShiXi,
               a.zwSalary   as zwSalary,
               a.zhiji,
               a.Szhiji     as sZhiji,
               a.depart_id     departId,
               a.zhuanzhendate
        from ch999_user a with (nolock)
        where a.ch999_id = #{ch999Id}
    </select>

    <select id="getCh999UsersById" resultType="com.jiuji.oa.oacore.oaorder.po.Ch999User">
        select a.ch999_id   as ch999Id,
               a.ch999_name as ch999Name,
               a.iszaizhi,
               a.mobile,
              a.area1id as area1id
        from ch999_user a with (nolock)
        where
        a.ch999_id in
        <foreach collection="ids" index="index" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="getUserCountByAreaId" resultType="java.lang.Integer">
        select count(1)
        from ch999_user with (nolock)
        where area1id=#{areaId} and iszaizhi=1
    </select>
    <select id="getCh999UsersByAreaId" resultType="com.jiuji.oa.oacore.oaorder.po.Ch999User">
        select top 1 u.ch999_id,
               u.ch999_name,
               u.mobile,
               u.area1id
        from dbo.ch999_user u with (nolock)
        left join dbo.zhiwu z with (nolock)
        on u.zhiwuid = z.id
        where u.iszaizhi = 1
          and isnull(u.islogin, 0) != 1
          and isnull(u.isshixi, 0) != 4
          and u.area1id = #{areaId}
        order by z.leve,u.ch999_id
    </select>
    <select id="listCh999UsersByAreaId" resultType="com.jiuji.oa.oacore.oaorder.po.Ch999User">
        select u.ch999_id,
                u.ch999_name,
               u.mobile,
               u.area1id
        from dbo.ch999_user u with (nolock)
        left join dbo.zhiwu z with (nolock)
        on u.zhiwuid = z.id
        where u.iszaizhi = 1
          and isnull(u.islogin, 0) != 1
          and isnull(u.isshixi, 0) != 4
          and u.area1id = #{areaId}
        order by z.leve,u.ch999_id
    </select>
    <select id="getComplaintEvaluateShortLink" resultType="java.lang.String">
        select dbo.encrypt(#{tsId})
    </select>
    <select id="getWxOpenIdByUserId" resultType="java.lang.String">
        select openid
        from weixinuser WITH(nolock)
        where kinds = 1 and userid = #{userId} and wxid = #{wxId}
    </select>
    <select id="selectStoreManagerAndManager" resultType="java.lang.Integer">
        select u.ch999_id
        from ch999_user u with (nolock)
         left join dbo.zhiwu z with (nolock) on u.zhiwuid = z.id
        where u.iszaizhi = 1
          and z.name in ('店长', '主管','门店主管')
          and u.area1id = (select area1id
            from ch999_user with (nolock)
            where ch999_id = #{userId}
          and iszaizhi = 1)

    </select>
    <select id="selectPwdByCh999Id" resultType="java.lang.String">
        select pwd from dbo.ch999_user with (nolock) where ch999_id=#{userId}
    </select>

    <select id="getStaffSimpleMessage" resultType="com.jiuji.oa.oacore.tousu.bo.StaffPraiseBO">
        select cu.ch999_id staffId,cu.ch999_name staffName,cu.zhiwu ,img.url  staffHeadImg,
        CONCAT( a.Province_name ,' ', a.city_name ,' ', a.area_name) areaName,cu.iszaizhi
        from ch999_user cu with(nolock)
        left join ${officeDbName}..appHeadimg img with(nolock) on img.ch999_id =  cu.ch999_id
        left join areainfo a with(nolock) on a.id = cu.area1id
        where cu.ch999_id in <foreach collection="staffIdList" item="staffId" open="(" close=")" separator=",">#{staffId}</foreach>
    </select>


    <sql id="basePraiseRecordBaseTable">
        with #praiseRecord as(
        select
        1 as rewardType,
        cf.ch999_id staffId,
        cf.bumen_id userId,
        bu.UserName userName,
        bu.headImg ,
        cf.fendate rewardTime,
        cf.fen * 50 rewardAmount
        from
        ch999_fen cf with(nolock)
        left join BBSXP_Users bu with(nolock) on bu.ID = cf.bumen_id
        where
        cf.jifenType in (200, 201, 202, 203)
        and isnull(cf.fen,0) > 0
        and cf.fendate is not null
        and cf.ch999_id = #{staffId}

        union ALL

        select
        2 as rewardType,
        e.RelateCh999Id staffId,
        e.userid userId,
        bu.UserName userName,
        bu.headImg ,
        e.udate rewardTime,
        e.uPrices rewardAmount
        from
        ${officeDbName}..EvaluateScore e with(nolock)
        left join BBSXP_Users bu with(nolock) on bu.ID = e.userid
        where
        isnull(e.uPrices,0) > 0
        and e.udate is not null
        and e.RelateCh999Id = #{staffId}
        )

    </sql>

    <select id="countStaffPraiseRecord" resultType="java.lang.Integer">
        <include refid="basePraiseRecordBaseTable"/>
        select count(*) from #praiseRecord
        <where>
            <if test="rewardType != null">
                and rewardType = #{rewardType}
            </if>
        </where>
    </select>

    <select id="countStaffCashReward" resultType="java.lang.Integer">
        select count(*)
        from ${officeDbName}..EvaluateScore e with(nolock)
        left join BBSXP_Users bu with(nolock) on bu.ID = e.userid
        where
            isnull(e.uPrices,0) > 0
          and e.udate is not null
          and e.RelateCh999Id = #{staffId}
        and e.EvaluateId = #{evaluateId}
    </select>

    <select id="getStaffPraiseRecord" resultType="com.jiuji.oa.oacore.tousu.bo.StaffPraiseBO">
        <include refid="basePraiseRecordBaseTable"/>

        select * from #praiseRecord
        order by rewardTime desc
        <if test="startRows != null and #{size} != null">
            offset #{startRows} rows fetch next #{size} rows only
        </if>
    </select>
</mapper>
