<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.CallCenterServiceEvalRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.CallCenterServiceEvalRecord">
        <id column="id" property="id" />
        <result column="dtime" property="dtime" />
        <result column="ch999_id" property="ch999Id" />
        <result column="addtime" property="addtime" />
        <result column="scored" property="scored" />
        <result column="userid" property="userid" />
        <result column="phone" property="phone" />
        <result column="inviter_ch999id" property="inviterCh999id" />
        <result column="ch999_name" property="ch999Name" />
        <result column="uniqueid" property="uniqueid" />
    </resultMap>
    <select id="getCh99Id" resultType="java.lang.String">
        select top 1 ch999_id from CallCenterServiceEvalRecord with(nolock) where id = #{subId}
    </select>

</mapper>
