<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.EvaluateTagMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.EvaluateTag">
        <id column="Id" property="Id" />
        <result column="Name" property="Name" />
        <result column="TagType" property="TagType" />
        <result column="EvaluateType" property="EvaluateType" />
        <result column="StarLevel" property="StarLevel" />
        <result column="ClickCount" property="ClickCount" />
        <result column="Rank" property="Rank" />
        <result column="IsValid" property="IsValid" />
        <result column="Job" property="Job" />
        <result column="RelateKpi" property="RelateKpi" />
        <result column="Group" property="Group" />
        <result column="KpiVolatility" property="KpiVolatility" />
        <result column="TagLimit" property="TagLimit" />
        <result column="section" property="section" />
        <result column="kind" property="kind" />
    </resultMap>
    <update id="updateE">
        UPDATE EvaluateTag SET ClickCount= IsNull(ClickCount, 0)+1 WHERE Id=#{id}
    </update>

    <insert id="saveBatchNoId" parameterType="com.jiuji.oa.oacore.oaorder.po.EvaluateTagRecord">
        INSERT INTO EvaluateTagRecord (TagId,EvaluateId,RelateCh999Id,IsOrderTag)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.TagId},#{item.EvaluateId},#{item.RelateCh999Id},#{item.IsOrderTag})
        </foreach>
    </insert>
</mapper>
