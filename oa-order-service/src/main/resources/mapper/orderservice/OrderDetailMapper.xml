<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.OrderDetailMapper">


    <select id="getSubSeparateConfig" resultType="com.jiuji.oa.oacore.oaorder.res.SubSeparateConfigBO">
      select * from dbo.subSeparateConfig with(nolock)
    </select>
    <select id="getNewSub" resultType="java.lang.Integer">
        select count(1) from dbo.sub with(nolock) where sub_id = #{subId}
    </select>
    <select id="getLiangPingSub" resultType="java.lang.Integer">
        select count(1) from dbo.recover_marketInfo with(nolock) where sub_id = #{subId}
    </select>
    <select id="getshouHouYuYueSub" resultType="java.lang.Integer">
        select count(1) from dbo.shouhou_yuyue with(nolock) where id = #{subId}
    </select>
    <select id="getshouHouSub" resultType="java.lang.Integer">
        select count(1) from dbo.shouhou with(nolock) where id = #{subId}
    </select>
    <select id="getRecoverSub" resultType="java.lang.Integer">
        select count(1) from dbo.recover_sub with(nolock) where sub_id = #{subId}
    </select>
    <select id="getSmallProSub" resultType="java.lang.Integer">
        select count(1) from dbo.Smallpro with(nolock) where id = #{subId}
    </select>
    <select id="getXTenantByUserId" resultType="java.lang.Integer">
        SELECT
            u.xtenant
        FROM
            dbo.sub s with(nolock)
            INNER JOIN dbo.BBSXP_Users u with(nolock) ON s.userid = u.ID
        WHERE
            s.sub_id = #{subId}
    </select>
    <select id="getSubMessage" parameterType="com.jiuji.oa.oacore.oaorder.req.OrderDetailReq"
            resultType="com.jiuji.oa.oacore.oaorder.res.SubBO">
        SELECT
        a.sub_id subId,
        a.sub_pay subPay,
        a.sub_check subCheck,
        a.sub_date subDate,
        a.sub_to subTo,
        a.sub_tel subTel,
        a.sub_mobile subMobile,
        a.zitidianID ziTiDianID,
        a.delivery,
        a.areaid areaId,
        a.comment,
        yingfuM yIngFuM,
        yifuM yiFuM,
        feeM,
        youhui1M youHui1M,
        jidianM jiDianM,
        tradeDate,
        tradeDate1,
        a.subtype subType,
        a.islock isLockNum,
        a.coinM,
        a.expectTime,
        z.shopType,
        a.dingjing dingJIng,
        addr.expectTime autoDelTime,
        aa.area
        FROM
        sub a with(nolock)
        LEFT JOIN dbo.zitidian z with(nolock) ON z.id = a.zitidianID
        LEFT JOIN dbo.SubAddress addr with(nolock) ON addr.sub_id= a.sub_id
        LEFT JOIN areaInfo aa with(nolock) on aa.id = a.areaid
        WHERE
        1 =1
        <choose>
            <when test="req.ziTiId != null and req.ziTiId != ''">
                and a.zitidianid=#{req.ziTiId} and a.sub_id=#{req.subId}
            </when>
            <otherwise>
                and a.userid=#{req.userId} and a.sub_id=#{req.subId}
            </otherwise>
        </choose>
        <choose>
            <when test="req.xTenant ==0">
                and not exists(select 1 from dbo.areainfo ar with(nolock) where a.areaid=ar.id and ar.printName
                <![CDATA[<>]]>'九机网')
            </when>
            <when test="req.xTenant ==2">
                and exists(select 1 from dbo.areainfo ar with(nolock) where a.areaid=ar.id and ar.printName='华为授权')
            </when>
            <otherwise>
                and exists(select 1 from dbo.areainfo ar with(nolock) where a.areaid=ar.id and
                ar.xtenant=#{req.xTenant})
            </otherwise>
        </choose>
    </select>
    <select id="getTaxPiAo" resultType="com.jiuji.oa.oacore.oaorder.res.TaxPiAoBO">
        SELECT TOP
            1 p.fileid  fileId,
            t.id AS piAoId,
            t.name,
            t.customType,
            t.kind,
            t.flag
        FROM
            dbo.tax_piao t with(nolock)
            LEFT JOIN dbo.electronPiao p with(nolock) ON t.id= p.piaoid
        WHERE
            t.flag IN ( 0, 1, 2, 3, 4 )
            AND isnull( t.type_, 0 ) = 0
            AND EXISTS ( SELECT 1 FROM dbo.piaoProductInfo f with(nolock) WHERE t.id = f.piaoid AND f.sub_id = #{subId})
    </select>
    <select id="getTaxPiAos" resultType="com.jiuji.oa.oacore.oaorder.res.TaxPiAoBO">
        select f.sub_id as subId, t.id as piaoid, t.flag
        from
        dbo.tax_piao t with(nolock)
        left join dbo.piaoProductInfo f with(nolock) on
        t.id = f.piaoid
        left join dbo.electronPiao p with(nolock) on
        t.id = p.piaoid
        where
        t.flag in (0, 1, 2, 3, 4, 8,6,7)
        and isnull(t.tax_service_type,0) &lt;&gt; 3
        and isnull(t.type_, 0) = #{type}
        and f.sub_id in
        <foreach collection="subIds" index="index" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
        order by t.id desc
    </select>
    <select id="getHaveComment" resultType="java.lang.Object">
        SELECT TOP
            1 id
        FROM
            EvaluateScore es with(nolock)
        WHERE
            sub_id = #{subId}
            AND es.EvaluateId IS NOT NULL
    </select>
    <select id="getSubAddress" resultType="com.jiuji.oa.oacore.oaorder.res.SubAddressBO">
         SELECT
            cityid,
            Address,
            wuliucompany,
            wuliuNo,
            userDate,
            userTime,
            isSpecial,
            expectTime,
            paisongState,
            paisongdtime
        FROM
            dbo.SubAddress with(nolock)
        WHERE
            sub_id =#{subId}
    </select>
    <select id="getAllArea" resultType="com.jiuji.oa.oacore.oaorder.res.AreaListBO">
        SELECT
        name,
        rank,
        code,
        parent_code parentCode,
        isnull( isReal, 0 ) isReal,
        level_ level,
        name1
        FROM
        AreaList with(nolock)
        WHERE
        name <![CDATA[<>]]> '全国'
        AND level_ <![CDATA[<]]>=4
        and isnull( isReal, 0 )=0
    </select>
    <select id="getAutoDeltime" resultType="java.time.LocalDateTime">
         SELECT
            expectTime
        FROM
            dbo.SubAddress with(nolock)
        WHERE
            sub_id =#{subId}
    </select>
    <select id="getZiTiDian" resultType="com.jiuji.oa.oacore.oaorder.res.ZiTiDianBO">
        SELECT
            cityid,
            name,
            address,
            hours,
            tel1,
            tel2,
            nickContact contractName,
            zhoubian,
            comment
        FROM
            zitidian with(nolock)
        WHERE
            id=#{ziTiId}
    </select>
    <select id="getareaInfoById" resultType="com.jiuji.oa.oacore.oaorder.res.AreaInfoBO">
        SELECT
            area,
            company_tel1 companyTel1,
            company_address companyAddress,
            area_name areaName,
            hours,
            url
        FROM
            areainfo with(nolock)
        WHERE
            isweb = 1
            AND ispass = 1
            AND kind2 IN ( 0, 1 )
            AND id =#{areaId}
    </select>
    <select id="getSdate" resultType="com.jiuji.oa.oacore.oaorder.res.ArrivalTimeBO">
        SELECT
            sDate,
            sTime,
            type_ type
        FROM
            dbo.sub_other with(nolock)
        WHERE
            type_ IN ( 3, 4 )
            AND sub_id =#{subId}
    </select>
    <select id="getNaHuoId" resultType="java.lang.String">
        SELECT TOP
            1 orderidd
        FROM
            nahuoduilie n with(nolock)
        WHERE
            EXISTS ( SELECT 1 FROM basket b with(nolock) WHERE b.basket_id= n.basket_id AND b.sub_id=#{subId} )
        ORDER BY
            isna
    </select>
    <select id="getRecoverSubId" resultType="java.lang.Integer">
        SELECT top 1
            sub_id
        FROM
            dbo.recover_sub with(nolock)
        WHERE
            sub_pay = 3
            AND sub_ido =#{subId}
            AND sub_check NOT IN ( 3, 4 )
    </select>
    <select id="getRecoverDetail" resultType="com.jiuji.oa.oacore.oaorder.res.ReCoverSubBO">
        SELECT
            s.sub_id,
            s.sub_to subTo,
            s.sub_tel subMobile,
            s.sub_pay subPay,
            s.sub_address subAdds,
            s.sub_delivery subDelivery,
            s.sub_bank subBank,
            s.bank_user bankUser,
            s.bank_num bankNum,
            s.sub_check subCheck,
            s.dtime dTimeTemp,
            s.comment comment,
            s.pay_time payTime,
            s.kuaididan kuAiDiDan,
            s.areaid areaId,
            b.save_money,
            b.erdu,
            b.userclass
        FROM
            recover_sub s with(nolock)
            LEFT JOIN BBSXP_Users b with(nolock) ON s.userid= b.ID
        WHERE
            sub_id =#{subId}
            AND userid =#{userId}
    </select>
    <select id="getCompanyAdress" resultType="java.lang.String">
        SELECT
            company_address
        FROM
            dbo.areainfo with(nolock)
        WHERE
            id =#{areaId}
    </select>
    <select id="getRecoverBaskets" resultType="com.jiuji.oa.oacore.oaorder.res.RecoverBasketBO">
        SELECT
            b.id,
            b.comment,
            b.costPrice,
            b.[count] count,
            b.ppriceid,
            b.price product_price,
            b.imei,
            b.isdel,
            b.check_ checkFlag,
            p.product_name productName,
            p.product_color productColor
        FROM
            dbo.recover_basket b with(nolock)
            LEFT JOIN dbo.productinfo p with(nolock) ON b.ppriceid= p.ppriceid
        WHERE
            b.sub_id=#{subId}
            AND isnull( b.isdel, 0 ) = 0
    </select>
    <select id="getEvaluateScore" resultType="com.jiuji.oa.oacore.oaorder.res.EvaluateScoreBO">
        SELECT
        e.Id,
        e.RelateCh999Id,
        e.Job,
        e.uPrices,
        u.area1id,
        s.sub_check subCheck,
        s.sub_id subId
        FROM
        ${officeName}..EvaluateScore e with(nolock)
        LEFT JOIN dbo.ch999_user u with(nolock) ON e.RelateCh999Id= u.ch999_id
        LEFT JOIN dbo.sub s with(nolock) ON s.sub_id= e.sub_id
        WHERE 1=1
         and e.sub_id in
        <foreach collection="subIds" index="index" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
        AND e.type_ IN ( 1, 2, 6 )
        AND e.RelateCh999Id<![CDATA[<>]]>0
    </select>
    <select id="getAllOrderUser" resultType="com.jiuji.oa.oacore.oaorder.res.OrderUserBO">
        SELECT
            a.ch999_id ch999Id,
            a.ch999_name ch999Name,
            b.url
        FROM
            dbo.ch999_user a with(nolock)
            LEFT JOIN ${officeName}.dbo.appHeadimg b with(nolock) ON a.ch999_id= b.ch999_id
            LEFT JOIN zhiwu z with(nolock) ON a.zhiwuid= z.id
        WHERE
            a.iszaizhi= 1
            AND a.ch999_id> 1
    </select>
    <select id="getAttachmentsBySubId" resultType="com.jiuji.oa.oacore.oaorder.res.OrderAttachmentsBO">
        SELECT
            filename,
            fid,
            Extension,
            dtime
        FROM
            attachments with(nolock)
        WHERE
            type = 10
            AND isnull( kind, 0 ) = 0
            AND linkedID = #{subId}
    </select>
    <select id="getJiFenBySubId" resultType="java.math.BigDecimal">
         SELECT top 1
            points
        FROM
            dbo.jifen with(nolock)
        WHERE
            sub_number = #{subId}
            AND isinput = 1
            AND kinds =1
    </select>
    <select id="getBasketSearch" resultType="com.jiuji.oa.oacore.oaorder.res.BasketSearchBO">
        SELECT
        A.basket_id basketId,
        A.basket_count basketCount,
        A.price,
        A.price1,
        isnull( giftid, 0 ) giftId,
        A.ppriceid pPriceId,
        B.product_name productName,
        B.product_color productColor,
        B.bpic productImg,
        isnull( A.type, 0 ) type,
        A.ismobile ismMobile,
        A.product_peizhi peiZhi,
        B.supportService
        FROM
        basket A with(nolock)
        INNER JOIN dbo.sub s with(nolock) ON s.sub_id = A.sub_id
        INNER JOIN productinfo B with(nolock) ON A.ppriceid = B.ppriceid
        WHERE
        A.sub_id = #{subId}
        AND s.sub_check <![CDATA[<>]]> 4
        AND isnull( a.isdel, 0 ) = 0 UNION ALL
        SELECT
        A.basket_id basketId,
        A.basket_count basketCount,
        A.price ,
        A.price1,
        isnull( giftid, 0 ) giftId,
        A.ppriceid pPriceId,
        B.product_name productName,
        B.product_color productColor,
        B.bpic productImg,
        isnull( A.type, 0 ) type,
        A.ismobile ismMobile,
        A.product_peizhi peiZhi,
        B.supportService
        FROM
        basket A with(nolock)
        INNER JOIN dbo.sub s with(nolock) ON s.sub_id = A.sub_id
        INNER JOIN productinfo B with(nolock) ON A.ppriceid = B.ppriceid
        WHERE
        A.sub_id = #{subId}
        AND s.sub_check = 4
    </select>
    <select id="getBskPeiJian" resultType="com.jiuji.oa.oacore.oaorder.res.BskPeiJianBO">
         SELECT
            p.cid,
            b.basket_id,
            b.ppriceid,
            b.basket_count,
            o.lcount AS beiCount,
            db.lcount AS dbCount,
            cg.lcount AS cgCount,
            c.lcount bCount
        FROM
            dbo.basket b with(nolock)
            LEFT JOIN dbo.basket_other o with(nolock) ON o.basket_id = b.basket_id
            LEFT JOIN dbo.productinfo p with(nolock) ON p.ppriceid = b.ppriceid
            LEFT JOIN (
            SELECT
                db.basket_id,
                SUM ( db.lcount ) AS lcount
            FROM
                dbo.diaobo_basket db with(nolock)
                LEFT JOIN dbo.diaobo_sub ds with(nolock) ON db.sub_id = ds.id
            WHERE
                ds.stats IN ( 1, 2, 5, 3 )
                and db.basket_type = 0 AND db.basket_id IN
        <foreach collection="basketIds" index="index" item="basketId" open="(" separator="," close=")">
            #{basketId}
        </foreach>
            GROUP BY
                db.basket_id
            ) db ON db.basket_id = b.basket_id
            LEFT JOIN (
            SELECT
                db.basket_id,
                SUM ( db.lcount ) AS lcount
            FROM
                dbo.caigou_basket db with(nolock)
                LEFT JOIN dbo.caigou_sub ds with(nolock) ON db.sub_id = ds.id
            WHERE
                ds.stats IN ( 0, 1, 2 )
                and db.basket_type = 0 AND db.basket_id IN
        <foreach collection="basketIds" index="index" item="basketId" open="(" separator="," close=")">
            #{basketId}
        </foreach>
            GROUP BY
                db.basket_id
            ) cg ON cg.basket_id = b.basket_id
            LEFT JOIN (
            SELECT
                lcount,
                basket_id
            FROM
                caigouinfo with(nolock)
            WHERE
                isnull( [type], 0 ) = 1
                AND basket_id IN
        <foreach collection="basketIds" index="index" item="basketId" open="(" separator="," close=")">
            #{basketId}
        </foreach>
            ) c ON c.basket_id= b.basket_id
        WHERE
        b.basket_id IN
        <foreach collection="basketIds" index="index" item="basketId" open="(" separator="," close=")">
            #{basketId}
        </foreach>
    </select>
    <select id="getNegativeStockOutPpid" resultType="com.jiuji.oa.oacore.oaorder.res.NegativeStockOutBO">
        SELECT
            id,
            dsc,
            name,
        VALUE,
            code,
            rank,
            xtenant,
            authId,
            areaids,
            kemu,
            fzhsType
        FROM
            dbo.sysConfig with(nolock)
        WHERE
            isnull( isdel, 0 ) = 0
            AND code = #{code}
        <if test="xTenant != null">
            and xtenant= #{xTenant}
        </if>
    </select>
    <select id="getHaoMaId" resultType="java.lang.Integer">
        SELECT top 1
            id
        FROM
            haoma with(nolock)
        WHERE
            basket_id = #{basketId}
    </select>
    <select id="getMkcDetail" resultType="com.jiuji.oa.oacore.oaorder.res.ProductMkcBO">
        SELECT
            id,
            kc_check kcCheck,
            inbeihuo
        FROM
            product_mkc with(nolock)
        WHERE
            basket_id = #{basketId}
            AND kc_check NOT IN ( 4 )
    </select>
    <select id="getMkcArea" resultType="com.jiuji.oa.oacore.oaorder.res.MkcAreaBO">
        SELECT TOP
            1 dtime,
            areaid,
            toareaid
        FROM
            dbo.mkc_toarea with(nolock)
        WHERE
            mkc_id = 1
            AND toareaid = 2
            AND recivedtime IS NULL
        ORDER BY
            dtime DESC
    </select>
    <select id="getBasketImei" resultType="com.jiuji.oa.oacore.oaorder.res.BasketImeiBO">
        SELECT
            b.basket_id basketId,
            m.imei
        FROM
            dbo.basket b with(nolock)
            LEFT JOIN dbo.product_mkc m with(nolock) ON b.basket_id= m.basket_id
        WHERE
            isnull( b.isdel, 0 ) = 0
            AND b.ismobile= 1
            AND m.imei IS NOT NULL
            AND b.sub_id= #{subId}
    </select>
    <select id="getTaocan" resultType="com.jiuji.oa.oacore.oaorder.res.TaocanBO">
        SELECT
            basket_id basketId,
            isnull( PlanId, '' ) PlanId,
            Mobile,
            PackageType,
            isptype,
            ContractPeroid
        FROM
            taocan with(nolock)
        WHERE
            basket_id IN
        <foreach collection="ids" index="index" item="basket" open="(" separator="," close=")">
            #{basket}
        </foreach>
    </select>
    <select id="getBigBeiHuo" resultType="java.lang.Integer">
        SELECT
        1
        FROM
        (
        SELECT
        b.basket_count,
        isnull( k.COUNT_, 0 ) c,
        b.basket_id
        FROM
        dbo.basket b with(nolock)
        LEFT JOIN (
        SELECT
        basket_id,
        COUNT ( 1 ) COUNT_
        FROM
        product_mkc k with(nolock)
        WHERE
        ISNULL( ischu1, 0 ) = 0
        AND kc_check IN ( 2, 3 )
        GROUP BY
        basket_id
        ) k ON b.basket_id= k.basket_id
        WHERE
        isnull( b.isdel, 0 ) = 0
        AND b.ismobile= 1
        AND b.sub_id=#{subId}
        ) aa
        WHERE
        aa.basket_count<![CDATA[<>]]>c
    </select>
    <select id="getSmall" resultType="java.lang.Integer">
        SELECT
        1
        FROM
        (
        SELECT
        b.basket_count,
        isnull( k.lcount, 0 ) c,
        b.basket_id
        FROM
        dbo.basket b with(nolock)
        LEFT JOIN sub s with(nolock) ON b.sub_id= s.sub_id
        LEFT JOIN product_kc k with(nolock) ON k.ppriceid= b.ppriceid
        AND s.areaid= k.areaid
        LEFT JOIN productprice pp with(nolock) ON pp.ppriceid = b.ppriceid
        WHERE
        isnull( b.isdel, 0 ) = 0
        AND b.ismobile= 0
        AND b.sub_id=#{subId}
        AND pp.productid NOT IN ( 21055 )
        ) aa
        WHERE
        aa.basket_count<![CDATA[<>]]> c
    </select>
    <select id="getCountBasket" resultType="java.lang.Integer">
        SELECT COUNT
            ( 1 ) COUNT
        FROM
            dbo.nahuoduilie with(nolock)
        WHERE
            basket_id IN
        <foreach collection="baskets" index="index" item="basket" open="(" separator="," close=")">
            #{basket}
        </foreach>
    </select>
    <select id="getRecoverSubs" resultType="java.lang.Integer">
        SELECT
            sub_id
        FROM
            dbo.recover_sub with(nolock)
        WHERE
            sub_ido =#{subId}
            AND sub_check = 3
    </select>
    <select id="getSubMessageSecond" resultType="com.jiuji.oa.oacore.oaorder.res.SubBO">
        SELECT
        a.sub_id subId,
        a.sub_pay subPay,
        a.sub_check subCheck,
        a.sub_date subDate,
        a.sub_to subTo,
        a.sub_tel subTel,
        a.sub_mobile subMobile,
        a.zitidianID ziTiDianID,
        a.delivery,
        a.areaid areaId,
        a.comment,
        yingfuM yIngFuM,
        yifuM yiFuM,
        feeM,
        youhui1M youHui1M,
        jidianM jiDianM,
        tradeDate,
        tradeDate1,
        a.subtype subType,
        a.islock isLockNum,
        a.coinM,
        a.expectTime,
        z.shopType,
        a.dingjing dingJIng,
        addr.expectTime autoDelTime,
        aa.area
        FROM
        sub a with(nolock)
        LEFT JOIN dbo.zitidian z with(nolock) ON z.id = a.zitidianID
        LEFT JOIN dbo.SubAddress addr with(nolock) ON addr.sub_id= a.sub_id
        LEFT JOIN areaInfo aa with(nolock) on aa.id = a.areaid
        WHERE
        1 =1
        <choose>
            <when test="req.ziTiId != null and req.ziTiId != ''">
                and a.zitidianid=#{req.ziTiId} and a.sub_id=#{req.subId}
            </when>
            <otherwise>
                 and a.sub_id=#{req.subId}
            </otherwise>
        </choose>
        <choose>
            <when test="req.xTenant ==0">
                and not exists(select 1 from dbo.areainfo ar with(nolock) where a.areaid=ar.id and ar.printName
                <![CDATA[<>]]>'九机网')
            </when>
            <when test="req.xTenant ==2">
                and exists(select 1 from dbo.areainfo ar with(nolock) where a.areaid=ar.id and ar.printName='华为授权')
            </when>
            <otherwise>
                and exists(select 1 from dbo.areainfo ar with(nolock) where a.areaid=ar.id and
                ar.xtenant=#{req.xTenant})
            </otherwise>
        </choose>
    </select>
    <select id="queryHalfCount" resultType="com.jiuji.oa.oacore.oaorder.res.HalfCountBO">
        SELECT
            b.basket_id item1,
            SUM ( isnull( r.buyCount, 0 ) ) AS item2,
            b.basket_count item3
        FROM
            dbo.basket b with(nolock)
            LEFT JOIN dbo.bskHalfBuyRecord r with(nolock) ON r.frombasket_id = b.basket_id
            AND r.isdel = 0
        WHERE
            b.basket_id IN
        <foreach collection="collect2" index="index" item="basket" open="(" separator="," close=")">
            #{basket}
        </foreach>
        GROUP BY
            b.basket_id,
            b.basket_count
    </select>
    <select id="getWuLiu" resultType="com.jiuji.oa.oacore.oaorder.res.WuLiuBO">
        SELECT
        id,
        com,
        nu
        FROM
        dbo.wuliu with(nolock)
        WHERE
        stats NOT IN ( 4, 5 )
        AND isnull( nu, '' ) <![CDATA[<>]]> ''
        AND isnull( com, '' ) <![CDATA[<>]]> ''
        AND id IN
        <foreach collection="collect" index="index" item="wuliuId" open="(" separator="," close=")">
            #{wuliuId}
        </foreach>
    </select>
    <select id="getTestSub" resultType="com.jiuji.oa.oacore.oaorder.res.SubBO">
    select top 50 s.userid,s.sub_id from sub  s with(nolock)
						inner join recover_sub  r with(nolock) on s.sub_id=r.sub_ido and s.userid=r.userid
						where
						r.sub_pay = 3
            AND r.sub_check NOT IN ( 3, 4 )
    </select>
</mapper>
