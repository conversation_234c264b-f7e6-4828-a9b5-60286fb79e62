<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.WuliuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.Wuliu">
        <id column="id" property="id"/>
        <result column="sname" property="sname"/>
        <result column="smobile" property="smobile"/>
        <result column="saddress" property="saddress"/>
        <result column="sarea" property="sarea"/>
        <result column="scityid" property="scityid"/>
        <result column="rname" property="rname"/>
        <result column="rmobile" property="rmobile"/>
        <result column="raddress" property="raddress"/>
        <result column="rarea" property="rarea"/>
        <result column="rcityid" property="rcityid"/>
        <result column="area" property="area"/>
        <result column="dtime" property="dtime"/>
        <result column="ctime" property="ctime"/>
        <result column="price" property="price"/>
        <result column="inprice" property="inprice"/>
        <result column="shoujianren" property="shoujianren"/>
        <result column="paijianren" property="paijianren"/>
        <result column="stats" property="stats"/>
        <result column="danhaobind" property="danhaobind"/>
        <result column="wutype" property="wutype"/>
        <result column="comment" property="comment"/>
        <result column="com" property="com"/>
        <result column="nu" property="nu"/>
        <result column="weight" property="weight"/>
        <result column="inuser" property="inuser"/>
        <result column="linktype" property="linktype"/>
        <result column="sendtime" property="sendtime"/>
        <result column="areaid" property="areaid"/>
        <result column="sareaid" property="sareaid"/>
        <result column="rareaid" property="rareaid"/>
        <result column="wCateId" property="wCateId"/>
    </resultMap>

    <select id="createTempSubTable">

    </select>

    <select id="getOnSiteInstallStatisticList"
            resultType="com.jiuji.oa.oacore.oaorder.vo.res.OnSiteInstallStatisticRes">
        <!--零时表-->
        select s.trader,s.delivery,b.basket_count,s.areaid,b.ismobile,b.sub_id,b.ppriceid into #tmp from dbo.basket b
        with(nolock)
        left join dbo.sub s with(nolock) on b.sub_id=s.sub_id
        where isnull(b.isdel,0)=0 and s.sub_check=3
        <if test="param != null">
            <if test="param.startTime!=null and param.startTime!='' and param.endTime!=null and param.endTime!=''">
                and s.tradeDate1 between #{param.startTime} and #{param.endTime}
            </if>
            <if test="param.areaIds != null and param.areaIds.size>0">
                and s.areaid in
                <foreach collection="param.areaIds" index="index" item="areaId" open="(" close=")" separator=",">
                    #{areaId"}
                </foreach>
            </if>
        </if>
        ;
        <!--零时表-->
        select aa.*,a.depart_id as departId ,a.area from (
        select aa.areaId,aa.trader as userName,sum(sjpeisong) djpsNum,sum(pjpeisong) xjpsNum,sum(shangmenweixiu)
        smwxNum,sum(shangmenqujian) smqjNum,sum(shangmenjishu) smazNum,sum(shangmenhuishou) smhsNum
        ,SUM(weixiuyuyue) wxyyNum
        FROM (
        <!--大件配送-->
        select b.areaid,b.trader,sum(basket_count) sjpeisong,0 pjpeisong,0 shangmenweixiu,0 shangmenqujian,0
        shangmenjishu,0 shangmenhuishou,0 weixiuyuyue from #tmp b with(nolock) where b.ismobile=1 and delivery=2 group by
        trader,b.areaid
        union
        <!--小件配送-->
        select b.areaid,b.trader,0 sjpeisong,count(distinct b.sub_id) pjpeisong,0 shangmenweixiu,0 shangmenqujian,0
        shangmenjishu,0 shangmenhuishou,0 weixiuyuyue from #tmp b with(nolock) where b.ismobile=0 and delivery=2
        and not exists(select 1 from #tmp t with(nolock) where t.sub_id=b.sub_id and t.ismobile=1 )
        group by trader,b.areaid
        union
        <!--上门维修-->
        select isnull(s.toareaid,s.areaid) areaid,s.weixiuren,0 sjpeisong,0 pjpeisong,count(1) shangmenweixiu,0
        shangmenqujian,0 shangmenjishu,0 shangmenhuishou,0 weixiuyuyue from shouhou s with(nolock) WHERE
        ISNULL(s.isquji,0) = 1 AND ISNULL(s.issoft,0) =0 AND s.STATS = 1 AND (costprice &gt; 0 OR feiyong &gt; 0)
        <if test="param != null and param.startTime!=null and param.startTime!='' and param.endTime!=null and param.endTime!=''">
            AND s.offtime between #{param.startTime} and #{param.endTime}
        </if>
        AND ISNULL(s.xianshi,0) = 1 AND ISNULL(s.wxkind,0) IN(5,6) AND ISNULL(s.webtype2,0) = 4 GROUP by
        isnull(s.toareaid,s.areaid),s.weixiuren
        union
        <!--上门取件-->
        select s.areaid,w.shoujianren,0 sjpeisong,0 pjpeisong,0 shangmenweixiu,count(1) shangmenqujian,0 shangmenjishu,0
        shangmenhuishou,0 weixiuyuyue from shouhou_yuyue s with(nolock) LEFT JOIN wuliu w with(nolock) ON s.id =
        w.danhaobind WHERE ISNULL(s.stype,0) = 2 and w.linktype =8 and ISNULL(shoujianren,'') != '' and
        ISNULL(isdel,0) =0
        <if test="param != null and param.startTime!=null and param.startTime!='' and param.endTime!=null and param.endTime!=''">
            and s.dtime between #{param.startTime} and #{param.endTime}
        </if>
        group by s.areaid,w.shoujianren
        union
        <!--上门技术-->
        select b.areaid,b.trader,0 sjpeisong,0 pjpeisong,0 shangmenweixiu,0 shangmenqujian,sum(basket_count)
        shangmenjishu,0 shangmenhuishou,0 weixiuyuyue from #tmp b with(nolock) where exists(select 1 from dbo.productinfo p
        with(nolock) where p.ppriceid=b.ppriceid and p.productid=30108 ) group by b.areaid,b.trader
        union
        <!--上门回收-->
        select b.areaid,b.recover_ch999name,0 sjpeisong,0 pjpeisong,0 shangmenweixiu,0 shangmenqujian,0
        shangmenjishu,count(1) shangmenhuishou,0 weixiuyuyue from dbo.recover_sub b with(nolock) where b.sub_check=3
        <if test="param != null and param.startTime!=null and param.startTime!='' and param.endTime!=null and param.endTime!=''">
            and b.pay_time between #{param.startTime} and #{param.endTime}
        </if>
        and b.sub_delivery=3 group by b.areaid,b.recover_ch999name
        UNION
        <!--维修预约量-->
        select s.areaid,isnull(s.enteruser,'未知') AS trade,0 sjpeisong,0 pjpeisong,0 shangmenweixiu,0 shangmenqujian,0
        shangmenjishu,0 shangmenhuishou,count(1) weixiuyuyue from shouhou_yuyue s with(nolock) WHERE ISNULL(s.isdel,0)
        =0 and kind = 1
        <if test="param != null and param.startTime!=null and param.startTime!='' and param.endTime!=null and param.endTime!=''">
            and s.dtime between #{param.startTime} and #{param.endTime}
        </if>
        group by s.areaid,s.enteruser
        ) aa where exists(select 1 from dbo.ch999_user u with(nolock) where u.ch999_name=aa.trader) group by
        aa.trader,aa.areaid) aa
        left join areainfo a with(nolock) on aa.areaid=a.id where 1=1
        <if test="param != null">
            <if test="param.areaIds != null and param.areaIds.size>0">
                and a.id in
                <foreach collection="param.areaIds" index="index" item="areaId" open="(" close=")" separator=",">
                    #{areaId}
                </foreach>
            </if>
            <if test="param.areaLevel != null">
                and a.leve=#{param.areaLevel}
            </if>
            <if test="param.kind != null and param.kind != 0">
                and a.kind1=#{param.kind}
            </if>
        </if>
    </select>

    <select id="updateShoujianren">
        UPDATE wuliu SET shoujianren = #{shoujianren} WHERE danhaobind = #{danhaobind} and linktype = #{linkType}
    </select>

    <select id="getDeliveryman" resultType="java.lang.String">
        SELECT top 1 w.paijianren FROM dbo.sub AS s with(nolock) JOIN dbo.wuliu AS w with(nolock) ON s.sub_id =
        w.danhaobind WHERE delivery IN(2, 5) AND isnull(w.linkType, 0)= 0 AND s.sub_id = #{subId}
    </select>
    <select id="getPaiJianRen" resultType="java.lang.String">
        SELECT top 1 w.paijianren FROM dbo.sub AS s with(nolock) JOIN dbo.wuliu AS w with(nolock) ON s.sub_id = w.danhaobind
         WHERE delivery IN(2, 5) AND isnull(w.linkType, 0)= 0 AND s.sub_id = #{subId}
    </select>

</mapper>
