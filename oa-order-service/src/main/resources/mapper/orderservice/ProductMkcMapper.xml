<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.ProductMkcMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.ProductMkc">
        <id column="id" property="id"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="orderid" property="orderid"/>
        <result column="inbeihuo" property="inbeihuo"/>
        <result column="dtime" property="dtime"/>
        <result column="inbeihuodate" property="inbeihuodate"/>
        <result column="inbeihuoprice" property="inbeihuoprice"/>
        <result column="imeidate" property="imeidate"/>
        <result column="imei" property="imei"/>
        <result column="inuser" property="inuser"/>
        <result column="imeiuser" property="imeiuser"/>
        <result column="kc_check" property="kcCheck"/>
        <result column="area" property="area"/>
        <result column="frarea" property="frarea"/>
        <result column="basket_id" property="basketId"/>
        <result column="inprice" property="inprice"/>
        <result column="origarea" property="origarea"/>
        <result column="caigoulock" property="caigoulock"/>
        <result column="pandian" property="pandian"/>
        <result column="pandiandate" property="pandiandate"/>
        <result column="cangkuid" property="cangkuid"/>
        <result column="rank" property="rank"/>
        <result column="insourceid" property="insourceid"/>
        <result column="insourceid2" property="insourceid2"/>
        <result column="ischu1" property="ischu1"/>
        <result column="useWay" property="useWay"/>
        <result column="takeBatch" property="takeBatch"/>
        <result column="beihuoUser" property="beihuoUser"/>
        <result column="areaid" property="areaid"/>
        <result column="origareaid" property="origareaid"/>
        <result column="frareaid" property="frareaid"/>
        <result column="pandianuser" property="pandianuser"/>
        <result column="lockDtime" property="lockDtime"/>
        <result column="pzid" property="pzid"/>
        <result column="isTax" property="isTax"/>
        <result column="fanli" property="fanli"/>
        <result column="protectPrice" property="protectPrice"/>
        <result column="modifyPrice" property="modifyPrice"/>
        <result column="staticPrice" property="staticPrice"/>
        <result column="fanlilock" property="fanlilock"/>
        <result column="fanlilockDtime" property="fanlilockDtime"/>
        <result column="fanlilockPzid" property="fanlilockPzid"/>
        <result column="rewardFlag" property="rewardFlag"/>
        <result column="mouldFlag" property="mouldFlag"/>
        <result column="transferPrice" property="transferPrice"/>
        <result column="pandianException" property="pandianException"/>
        <result column="inPzid" property="inPzid"/>
    </resultMap>
    <select id="listInventoryInfoForDji" resultType="com.jiuji.oa.oacore.brand.dji.vo.InventoryInfoVo">
        SELECT CAST (getdate() AS DATE) stockDate,p.product_name+' '+isnull(p.product_color,'') prodName,p.ppriceid
        SKUID,
        '台' prodUnit,k.imei SN,p.barCode prodBarcode,1 stockQuantity,a.area [stockType]
        FROM dbo.product_mkc k WITH(nolock)
        left join dbo.Ok3w_qudao o WITH(nolock) on o.id=k.insourceid2
        LEFT JOIN dbo.productinfo p WITH(nolock) ON k.ppriceid=p.ppriceid
        LEFT JOIN dbo.areainfo a WITH(nolock) ON a.id=k.origareaid
        WHERE
        <if test="areaIds!=null and areaIds.size>0">
            a.id in
            <foreach collection="areaIds" item="areaId" separator="," close=")" open="(">
                #{areaId}
            </foreach>
            and
        </if>
        k.kc_check IN (2,3,10,8,14,6) AND k.imei IS NOT NULL AND p.brandID=1146 and o.id in (4815,4816)
    </select>

    <select id="listInventoryForXiaomi" resultType="com.jiuji.oa.oacore.brand.vo.InventoryVO">
        SELECT
        k.imei,
        k.ppriceid sku_id,
        p.product_name+ ' ' + isnull( p.product_color, '' ) productName,
        k.areaid areaId,
        a.area_name areaName,
        isnull( k.imeidate, k.inbeihuodate ) stockDate,
        case when isnull(k.mouldFlag,0)=1 then '是' else '否' end modelMachine
        FROM
        dbo.product_mkc k with(nolock)
        LEFT JOIN dbo.productinfo p with(nolock) ON k.ppriceid= p.ppriceid
        LEFT JOIN dbo.areainfo a with(nolock) ON a.id= k.areaid
        WHERE
        k.kc_check IN ( 2, 3, 10 )
        AND k.imei IS NOT NULL
        AND a.ispass = 1
        AND a.kind2 in (0,1,2)
        AND p.cid IN
        <foreach collection="cidList" separator="," open="(" close=")" item="cid" index="index">
            #{cid}
        </foreach>
        and p.brandID in
        <foreach collection="brandIdList" index="index" item="brandId" open="(" close=")" separator=",">
            #{brandId}
        </foreach>
        <if test="cityIds!=null and cityIds.size>0">
            and left(a.cityid,2) in
            <foreach collection="cityIds" open="(" close=")" separator="," item="cityId">
                #{cityId}
            </foreach>
        </if>

    </select>
    <select id="getSecretByCode" resultType="java.lang.String">
        select secret from dbo.secretCodeConfig with(nolock) where code=#{code}
    </select>
    <select id="selectDisplayProductInfo" resultType="com.jiuji.oa.oacore.weborder.res.BargainSmallVO">
          select id as mainTainID,ppriceid,areaId,sellPrice,count_ as count,curAreaId,kc_check,isFlaw from dbo.displayProductInfo with(nolock)  where id=#{id} and stats_=1
    </select>


</mapper>
