<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.BrandMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.Brand">
        <id column="id" property="id" />
        <result column="name" property="name" />
    </resultMap>

    <select id="getBrandsByCid" resultType="com.jiuji.oa.oacore.oaorder.vo.res.BrandRes">
           select b.* from  brand b with(nolock) LEFT JOIN brandCategory bc with(nolock) on b.id=bc.brandID
           where 1=1
           <if test="cids != null and cids.size>0">
               and bc.categoryID in
               <foreach collection="cids" index="index" item="cid" open="(" separator="," close=")">
                   #{cid}
               </foreach>
           </if>
    </select>

    <select id="getCidAndBrands" resultType="com.jiuji.oa.oacore.oaorder.vo.res.BrandRes">
        select b.* ,bc.categoryID as cid
        from  brand b with(nolock)
        LEFT JOIN  brandCategory bc with(nolock) on b.id=bc.brandID
        where 1=1
        <if test="cids != null and cids.size>0">
            and bc.categoryID in
            <foreach collection="cids" index="index" item="cid" open="(" separator="," close=")">
                #{cid}
            </foreach>
        </if>
    </select>

    <select id="getByIds" resultType="com.jiuji.oa.oacore.promocode.bo.CommonSwitchBO">
        select id,name from dbo.brand with(nolock) where id in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getBrandIds" resultType="java.lang.Integer">
        select ID from dbo.brand with(nolock) where id in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>
