<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.TousuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.Tousu">
        <id column="id" property="id" />
        <result column="userid" property="userid" />
        <result column="email" property="email" />
        <result column="mobile" property="mobile" />
        <result column="content_" property="content" />
        <result column="writer" property="writer" />
        <result column="addTime" property="addTime" />
        <result column="writeIp" property="writeIp" />
        <result column="type_" property="type" />
        <result column="states_" property="states" />
        <result column="userName" property="userName" />
        <result column="area" property="area" />
        <result column="areaName" property="areaName" />
        <result column="inuser" property="inuser" />
        <result column="memberName" property="memberName" />
        <result column="finishTime" property="finishTime" />
        <result column="cat" property="cat" />
        <result column="isdel" property="isdel" />
        <result column="types" property="types" />
        <result column="kinds" property="kinds" />
        <result column="departId" property="departId" />
        <result column="oldDeapartId" property="oldDeapartId" />
        <result column="BonusMoney" property="bonusMoney" />
        <result column="FineMoney" property="fineMoney" />
        <result column="AreaId" property="areaId" />
        <result column="tousupic" property="tousupic" />
        <result column="tousupictmp" property="tousupictmp" />
        <result column="isup" property="isup" />
        <result column="tousuTypes" property="tousuTypes" />
        <result column="tousuTags" property="tousuTags" />
        <result column="departArea" property="departArea" />
        <result column="tousuRank" property="tousuRank" />
        <result column="dealTime" property="dealTime" />
        <result column="zhenggaiTime" property="zhenggaiTime" />
        <result column="zhenggaiPingshenTime" property="zhenggaiPingshenTime" />
        <result column="scoreArea" property="scoreArea" />
        <result column="scoreDep" property="scoreDep" />
        <result column="processUser" property="processUser" />
        <result column="huanyuantime" property="huanyuantime" />
        <result column="huanyuantimeout" property="huanyuantimeout" />
        <result column="DealTimeout" property="dealTimeout" />
        <result column="chuliren" property="chuliren" />
        <result column="chulirendate" property="chulirendate" />
        <result column="stoptimeuser" property="stoptimeuser" />
        <result column="stoptimedate" property="stoptimedate" />
        <result column="questionIsPush" property="questionIsPush" />
        <result column="ArchiveCategory" property="archiveCategory" />
        <result column="isxinsheng" property="isxinsheng" />
        <result column="xinshengrank" property="xinshengrank" />
        <result column="xs_comment_count" property="xsCommentCount" />
        <result column="xs_view_count" property="xsViewCount" />
        <result column="xs_praise_count" property="xsPraiseCount" />
        <result column="CustomerEndTime" property="customerEndTime" />
        <result column="CustomerEndUser" property="customerEndUser" />
    </resultMap>
    <select id="getProcessUser" resultType="java.lang.String">
        SELECT processUser FROM with(nolock) tousu WHERE id=#{subId};
    </select>

</mapper>
