<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.LiangPinOrderMapper">

    <select id="getLiangPinOrderList" resultType="com.jiuji.oa.oacore.oaorder.vo.res.LiangPinOrderRes">
        SELECT
        s.trader,s.isfahuo,rs.paisongdtime,rs.paisongState,S.areaid,rsub.areaid reareaid,S.sub_id,P.product_name+'
        '+ISNULL(P.product_color,'')
        productName,B.basket_count,B.price,S.yingfuM,S.yifuM,B.basket_date,B.ppriceid,B.basket_id,<PERSON><PERSON>ismobile,S.sub_check,b.isdel,b.seller,k.imei,k.intime,
        k.mkc_check,isnull(k.id,b.mkc_id2)
        id,k.orderid,k.comment,k.comment1,k.checkUpUser,k.remark,B.ksRemark,rm.sub_to,rm.saleType,k.from_basket_id,k.inprice,k.mkcid,
        rb.inuser recoverUser,rb.checkUser recoverCheckUser,rb.sub_id recoverSubID,k.addprice
        giftInprice,s.youhui1M,s.jidianM
        ,k.isAuction,rb.price - rb.price1 addPrice,rb.addCodePrice,k.diffopt,ISNULL(rb.checkprice,rb.price) - rb.price
        diffPrice,s.tradeDate1
        FROM dbo.recover_marketSubInfo B with(nolock)
        LEFT JOIN recover_marketInfo S with(nolock) ON B.sub_id=S.sub_id
        LEFT JOIN dbo.productinfo P with(nolock) ON B.ppriceid=P.ppriceid
        left join dbo.recover_mkc k with(nolock) on k.to_basket_id=B.basket_id
        left join recover_basket rb with(nolock) on rb.id=k.from_basket_id
        LEFT JOIN dbo.recover_sub rsub WITH(NOLOCK) ON rsub.sub_id=rb.sub_id
        left join RecoverSubAddress rs with(nolock) on s.sub_id = rs.sub_id
        LEFT JOIN dbo.recover_marketInfo rm with(nolock) ON rm.sub_id=s.sub_id
        where 1=1
        <include refid="getLiangPinOrderWhereSql"/>
    </select>

    <select id="getLiangPinOrderListByStatistics" resultType="com.jiuji.oa.oacore.oaorder.vo.res.LiangPinOrderSumRes">
        SELECT SUM(B.basket_count * B.price) totalPrice,
               SUM(B.basket_count) totalQuantity,
               count(distinct s.sub_id) totalOrder,
               count(rs.sub_id),
               sum(k.inprice) inprice
        FROM dbo.recover_marketSubInfo B with(nolock)
        LEFT JOIN recover_marketInfo S with (nolock) ON B.sub_id=S.sub_id
        LEFT JOIN dbo.productinfo P with (nolock) ON B.ppriceid=P.ppriceid
        left join dbo.recover_mkc k with (nolock) on k.to_basket_id=B.basket_id
        left join RecoverSubAddress rs with (nolock) on s.sub_id = rs.sub_id
        left join recover_basket rb with(nolock) on rb.id=k.from_basket_id
        LEFT JOIN dbo.recover_sub rsub WITH(NOLOCK) ON rsub.sub_id=rb.sub_id
        where 1=1
        <include refid="getLiangPinOrderWhereSql"/>
    </select>


    <sql id="getLiangPinOrderWhereSql">
        <!--地区判断-->
        <if test="req.areaIds != null and req.areaIds.size() > 0">
            and S.areaid in
            <foreach item="areaId" collection="req.areaIds" index="index" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>

        <!--判断门店类型-->
        <if test="req.areaKind != null">
            <choose>
                <when test="req.areaKind != 1 or (req.xtenant != null and 1000 > req.xtenant)">
                    and exists (select 1 from dbo.areainfo a with(nolock)
                    where S.areaid=a.id and a.kind1 = #{req.areaKind} and a.authorizeid = #{req.authorizeId})
                </when>
                <when test="req.areaKind != 0">
                    and exists(select 1 from areainfo a with(nolock) where a.id=S.areaid and a.kind1 = #{req.areaKind})
                </when>
            </choose>
        </if>
        <if test="req.subPayList != null and req.subPayList.size() > 0">
            AND rsub.sub_pay IN
            <foreach collection="req.subPayList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.brandIdList != null and req.brandIdList.size() > 0">
            and p.brandID in
            <foreach collection="req.brandIdList" item="brandId" open="(" separator="," close=")">
                #{brandId}
            </foreach>
        </if>
        <!--客户类别-->
        <if test="req.customerType != null and req.customerType != 0 ">
            <choose>
                <when test="req.customerType == 1">
                    AND s.saleType= 0
                </when>
                <when test="req.customerType == 2">
                    AND s.saleType= 1
                </when>
                <when test="req.customerType == 3">
                    AND s.sub_to = '回收机退回渠道'
                </when>
                <when test="req.customerType == 4">
                    AND EXISTS(SELECT 1 FROM dbo.recover_paimai with(nolock) WHERE sub_id=S.sub_id)
                </when>
            </choose>
        </if>

        <!--商品分类cid-->
        <if test="req.productCid != null and req.productCid != ''">
            and exists( select 1 from f_category_children(#{req.productCid}) f where f.id=p.cid )
        </if>

        <!--订单状态-->
        <if test="req.subCheck != null">
            <choose>
                <when test="req.subCheck == 10">
                    and S.sub_check NOT IN (3,4,8,9) and isnull(b.isdel,0) != 1
                </when>
                <when test="req.subCheck == 11">
                    and S.sub_check = 0
                </when>
                <when test="req.subCheck != 10 and req.subCheck != 11">
                    and S.sub_check= #{req.subCheck}
                    <!--!已删除-->
                    <if test="req.subCheck != 4">
                        and isnull(b.isdel,0) != 1
                    </if>
                </when>
            </choose>
        </if>

        <!--查询条件构造-->
        <if test="req.key !=null and req.key !='' and req.limintClint != null and req.limintClint != 0">
            <choose>
                <!--               电话号码 -->
                <when test="req.limintClint == 2">
                    and ((s.sub_mobile like concat('%',#{req.key},'%')) or (s.sub_tel like concat('%',#{req.key},'%')))
                </when>
                <!--                商品名称-->
                <when test="req.limintClint == 3">
                    and p.product_name like concat('%',#{req.key},'%')
                </when>
                <!--                mkcId编号-->
                <when test="req.limintClint == 4">
                    AND k.id = #{req.key}
                </when>
                <!--                orderid-->
                <when test="req.limintClint == 5">
                    AND k.orderid = #{req.key}
                </when>
                <!--                串号-->
                <when test="req.limintClint == 6">
                    AND k.imei like concat('%',#{req.key},'%')
                </when>
                <!--                商品ID-->
                <when test="req.limintClint == 7">
                    AND P.productid = #{req.key}
                </when>
                <!--                skuId-->
                <when test="req.limintClint == 8">
                    AND B.ppriceid = #{req.key}
                </when>
                <!--                订单备注-->
                <when test="req.limintClint == 9">
                    AND S.comment like concat('%',#{req.key},'%')
                </when>
                <!--                交易人-->
                <when test="req.limintClint == 10">
                    AND S.trader like concat('%',#{req.key},'%')
                </when>
                <!--                销售人-->
                <when test="req.limintClint == 11">
                    AND B.seller like concat('%',#{req.key},'%')
                </when>

            </choose>
        </if>

        <!--时间类别-->
        <if test="req.startTime != null and req.endTime != null">
            <choose>
                <!--                订机时间-->
                <when test="req.timeType == 1">
                    and basket_date between #{req.startTime} and #{req.endTime}
                </when>
                <!--               出库时间 -->
                <when test="req.timeType == 2">
                    and s.tradedate between #{req.startTime} and #{req.endTime}
                </when>
                <!--                交易时间-->
                <when test="req.timeType == 3">
                    and s.tradedate1 between #{req.startTime} and #{req.endTime}
                </when>
            </choose>
        </if>

        <!--大小件-->
        <if test="req.sizePiece != null">
            <choose>
                <!--                大件-->
                <when test="req.sizePiece == 1">
                    and p.ismobile1= 1
                </when>
                <!--               小件 -->
                <when test="req.sizePiece == 2">
                    and p.ismobile1= 0
                </when>
            </choose>
        </if>

        <!--配送方式-->
        <if test="req.distributionType != null and req.distributionType != 0">
            and s.delivery= #{req.distributionType}
        </if>
    </sql>

    <select id="getRecoverId" resultType="java.lang.Integer">
        SELECT sub_id FROM recover_marketInfo with(nolock) where sub_id= #{key}
    </select>
</mapper>
