<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.EvaluateQuestionAnswerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.EvaluateQuestionAnswer">
        <id column="id" property="id" />
        <result column="evaluateid" property="evaluateid" />
        <result column="ch999id" property="ch999id" />
        <result column="questionid" property="questionid" />
        <result column="answer" property="answer" />
        <result column="dtime" property="dtime" />
    </resultMap>
    <update id="updateQuestionUser">
        UPDATE office..EvaluateQuestionUser SET kind=0,[enable]=0 WHERE ch999id=#{id}
    </update>
    <select id="cancelQuestAnswer" resultType="com.jiuji.oa.oacore.oaorder.po.EvaluateQuestionAnswer">
        SELECT TOP 10 qa.id,qa.answer FROM office.dbo.EvaluateQuestionAnswer qa with(nolock) INNER JOIN office.dbo.EvaluateQuestionUser qu with(nolock) ON qa.ch999id=qu.ch999id
        WHERE qu.ch999id = #{id} AND qu.enable=1 AND qa.dtime &lt;= qu.dtime ORDER BY qa.id ASC
    </select>

</mapper>
