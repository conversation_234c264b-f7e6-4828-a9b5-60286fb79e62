<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.OrderDailySaleReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.OrderDailySaleReport">
        <id column="id" property="id" />
        <result column="tdate" property="tdate" />
        <result column="ppid" property="ppid" />
        <result column="pid" property="pid" />
        <result column="product_name" property="productName" />
        <result column="product_color" property="productColor" />
        <result column="brand_id" property="brandId" />
        <result column="brand_name" property="brandName" />
        <result column="early_num" property="earlyNum" />
        <result column="early_amount" property="earlyAmount" />
        <result column="purchase_amount" property="purchaseAmount" />
        <result column="purchase_num" property="purchaseNum" />
        <result column="return_amount" property="returnAmount" />
        <result column="return_num" property="returnNum" />
        <result column="loss_num" property="lossNum" />
        <result column="loss_amount" property="lossAmount" />
        <result column="retail_num" property="retailNum" />
        <result column="retail_amount" property="retailAmount" />
        <result column="retail_cost" property="retailCost" />
        <result column="retail_gross_profit" property="retailGrossProfit" />
        <result column="distribut_num" property="distributNum" />
        <result column="distribut_amount" property="distributAmount" />
        <result column="distribut_cost" property="distributCost" />
        <result column="distribut_gross_profit" property="distributGrossProfit" />
        <result column="sale_total_num" property="saleTotalNum" />
        <result column="sale_total_amount" property="saleTotalAmount" />
        <result column="sale_total_cost" property="saleTotalCost" />
        <result column="sale_total_gross_profit" property="saleTotalGrossProfit" />
        <result column="period_stock_num" property="periodStockNum" />
        <result column="period_stock_amount" property="periodStockAmount" />
        <result column="supplier" property="supplier" />
        <result column="supplier_id" property="supplierId" />
        <result column="cname" property="cname" />
        <result column="cid" property="cid" />
        <result column="refund_amount" property="refundAmount" />
        <result column="refund_num" property="refundNum" />
        <result column="repair_num" property="repairNum" />
        <result column="repair_amount" property="repairAmount" />
        <result column="repair_cost" property="repairCost" />
        <result column="repair_gross_profit" property="repairGrossProfit" />
        <result column="ggc_num" property="ggcNum" />
        <result column="ggc_amount" property="ggcAmount" />
        <result column="promo_code_amount" property="promoCodeAmount" />
        <result column="giveaway_amount" property="giveawayAmount" />
        <result column="wxcspfzp_num" property="wxcspfzpNum" />
        <result column="wxcspfzp_cost" property="wxcspfzpCost" />
        <result column="exchange_num" property="exchangeNum" />
        <result column="exchange_cost" property="exchangeCost" />
        <result column="promo_code_return_amount" property="promoCodeReturnAmount" />
        <result column="integral_return_amount" property="integralReturnAmount" />
        <result column="giveaway_return_amount" property="giveawayReturnAmount" />
        <result column="sale_return_amount" property="saleReturnAmount" />
        <result column="create_tiem" property="createTiem" />
    </resultMap>

</mapper>
