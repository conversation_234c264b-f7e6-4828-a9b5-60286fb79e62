<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.BasketMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.Basket">
        <id column="basket_id" property="basketId"/>
        <result column="basket_count" property="basketCount"/>
        <result column="basket_date" property="basketDate"/>
        <result column="product_peizhi" property="productPeizhi"/>
        <result column="seller" property="seller"/>
        <result column="ismobile" property="ismobile"/>
        <result column="price" property="price"/>
        <result column="sub_id" property="subId"/>
        <result column="price1" property="price1"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="inprice" property="inprice"/>
        <result column="giftid" property="giftid"/>
        <result column="type" property="type"/>
        <result column="isdel" property="isdel"/>
        <result column="ischu" property="ischu"/>
        <result column="price2" property="price2"/>
        <result column="iskc" property="iskc"/>
        <result column="isOnShop" property="isOnShop"/>
    </resultMap>

    <select id="getUserBySubId" resultType="java.lang.String">
        SELECT TOP 1 b.seller FROM basket AS b with(nolock) WHERE b.sub_id = #{subId} AND ISNULL(b.isdel,0) = 0
        order BY b.ismobile desc,b.basket_date ASC
    </select>

    <select id="getJishuUsername" resultType="java.lang.String">
        SELECT TOP 1  s.inuser  FROM dbo.basket b with(nolock) JOIN dbo.product_mkc m with(nolock)
        on b.basket_id = m.basket_id JOIN msoft s with(nolock)
        ON s.imei = m.imei WHERE b.ismobile = 1 AND b.sub_id = #{subId}
    </select>

    <select id="getXianshangKefuUsername" resultType="java.lang.String">
        SELECT TOP 1 b.seller FROM basket AS b with(nolock) WHERE b.sub_id= #{subId} ORDER BY b.ismobile desc
    </select>
    <select id="getSeller" resultType="java.lang.String">
        SELECT TOP 1 b.seller FROM basket AS b with(nolock) WHERE b.sub_id=#{subId} AND ISNULL(b.isdel,0) = 0 order BY b.ismobile desc,b.basket_date ASC
    </select>
    <select id="getInuser" resultType="java.lang.String">
        SELECT TOP 1  s.inuser  FROM dbo.basket b with(nolock) JOIN dbo.product_mkc m with(nolock) on b.basket_id = m.basket_id JOIN msoft s with(nolock)
        ON s.imei = m.imei WHERE b.ismobile = 1 AND b.sub_id = #{subId}
    </select>
    <select id="getSellerV2" resultType="java.lang.String">
        SELECT TOP 1 b.seller FROM basket AS b with(nolock) WHERE b.sub_id=#{subId}  ORDER BY b.ismobile desc
    </select>
    <select id="getBasketDataBySubIds"
            resultType="com.jiuji.oa.oacore.weborder.res.BasketSearchRes">
        SELECT
            A.basket_id basketId,
            A.sub_id subId,
            A.basket_count basketCount,
            A.price,
            A.price1 ,
            isnull( giftid, 0 ) giftid,
            A.ppriceid ,
            B.product_name productName,
            B.product_color productColor,
            B.bpic productImg,
            isnull( A.type, 0 ) type,
            A.ismobile,
            A.product_peizhi peizhi
        FROM
            basket A with(nolock),
            productinfo B with(nolock)
        WHERE
            A.sub_id IN
            <foreach collection="subIdList" index="index" item="evaluateType" open="(" separator="," close=")">
                #{evaluateType}
            </foreach>
            AND A.ppriceid = B.ppriceid
            AND isnull( a.isdel, 0 ) =0
            <if test="isweixiu == 'true'">
                and B.cid not in(3,58,52,7,62,33,61,67,221,297,220,98,164,165,23,50,53,68,70,31,24,25,26,27,54,28,30,29,159,239,69,4)
            </if>
    </select>

    <!-- listBasketByOrderRecommend -->
    <select id="listBasketByOrderRecommend" resultType="com.jiuji.oa.oacore.cloud.bo.OrderRecommendBO$BasketInRecommend">
        SELECT a.id areaId, a.area areaCode, a.area_name areaName, b.basket_id basketId, b.ppriceid,
               b.basket_count basketCount, b.ismobile mobile,
               s.userid userId, u.xtenant userXtenant
        FROM dbo.basket b with(nolock)
            LEFT JOIN dbo.sub s with(nolock) ON s.sub_id = b.sub_id
            LEFT JOIN dbo.areainfo a with(nolock) ON s.areaid = a.id
            LEFT JOIN dbo.BBSXP_Users u with(nolock) ON u.ID = s.userid
        WHERE s.sub_id = #{subId}
            AND ISNULL(b.isdel, 0) = 0
    </select>

    <select id="listOutboundsByOrderNo" resultType="com.jiuji.oa.oacore.oaorder.vo.res.OutboundOrderRes$OutboundOrderProduct">
        SELECT b.ppriceid ppriceid,b.price price,b.basket_count basketCount,b.price * b.basket_count totalPrice from basket b with(nolock) where b.sub_id = #{subId} AND ISNULL(b.isdel, 0) = 0
    </select>

    <select id="listOutboundsByOrderNoV2" resultType="com.jiuji.oa.oacore.oaorder.vo.res.OutboundOrderRes$OutboundOrderProduct">
        select b.basket_id ,b.ppriceid ppriceid,b.price price,b.basket_count basketCount,b.price * b.basket_count totalPrice,p.barCode,k.number as locationNo from sub s with(nolock )
            left join basket b with(nolock) on  b.sub_id = s.sub_id and  ISNULL(b.isdel, 0) = 0
            left join productBarcode p with(nolock ) on p.ppriceid=b.ppriceid and p.isDefault = 'true'
            left join product_kc k with(nolock ) on k.areaid=s.areaid and b.ppriceid=k.ppriceid
            where s.sub_id = #{subId}
    </select>

</mapper>
