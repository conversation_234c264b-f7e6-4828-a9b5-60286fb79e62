<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.TApprovallogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.TApprovallog">
        <id column="LogId" property="logId" />
        <result column="RecordTime" property="recordTime" />
        <result column="ApplyId" property="applyId" />
        <result column="Status" property="status" />
        <result column="Comment" property="comment" />
        <result column="Checker" property="checker" />
        <result column="CheckName" property="checkName" />
        <result column="NextChecker" property="nextChecker" />
        <result column="NextCheckerName" property="nextCheckerName" />
        <result column="LogType" property="logType" />
    </resultMap>

    <select id="getApplyInUser" resultType="java.lang.Integer">
        SELECT TOP 1 t.InUser
        FROM T_ApprovalLog l WITH(NOLOCK)
        INNER JOIN  T_ApplyInfo t WITH(NOLOCK) ON t.ApplyId = l.ApplyId
        WHERE l.LogId = #{id}
    </select>

    <select id="getDeveloperUserId" resultType="java.lang.Integer">
        SELECT TOP 1 ap.ch999_id FROM dbo.ApplyPrize ap WITH(NOLOCK) INNER JOIN T_ApplyInfo t WITH(NOLOCK) ON
        ap.ApplyId = t.Id INNER JOIN T_ApprovalLog l WITH(NOLOCK) ON t.ApplyId = l.ApplyId WHERE l.LogId =
        #{id}
    </select>

    <select id="selectById" resultType="com.jiuji.oa.oacore.oaorder.po.TApprovallog">
        select * from T_ApprovalLog with(nolock) where LogId = #{id}
    </select>
    <select id="getCh999UerId" resultType="java.lang.Integer">
        SELECT l.Checker FROM T_ApprovalLog l WITH(NOLOCK) WHERE l.LogId =#{subId}
    </select>
</mapper>
