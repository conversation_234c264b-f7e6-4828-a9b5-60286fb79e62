<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.Ch999EvaluateInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.Ch999EvaluateInfo">
        <id column="ch999_id" property="ch999Id" />
        <result column="EvaluatePoint" property="EvaluatePoint" />
        <result column="EvaluateCount" property="EvaluateCount" />
        <result column="mobileCount" property="mobileCount" />
        <result column="priceCount" property="priceCount" />
        <result column="pricePrice" property="pricePrice" />
        <result column="type_" property="type" />
    </resultMap>
    <update id="updateByIdAndType">
         update Ch999EvaluateInfo set EvaluatePoint+=#{EvaluatePoint},EvaluateCount+=#{EvaluateCount},
         mobileCount+=#{mobileCount},priceCount+=#{priceCount},pricePrice+=#{pricePrice}
           where ch999_id=#{ch999_id} and type_=#{type}
    </update>
    <select id="getById" resultType="java.lang.String">
        select ch999_id from Ch999EvaluateInfo with(nolock) where ch999_id=#{ch999_id} and type_=#{type}
    </select>

</mapper>
