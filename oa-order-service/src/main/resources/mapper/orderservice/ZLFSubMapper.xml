<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.ZLFSubServiceMapper">

    <select id="querySubBizData" resultType="com.jiuji.oa.oacore.oaorder.vo.res.SubBizData">

        select 1 sort,sum(lcount) counts,isnull(sum(lcount*inprice),0) prices,'小件库存金额' kindsName,isnull(d.name,'总部') departName  from dbo.product_kc k with(nolock)
        left join areainfo a with(nolock) on a.id=k.areaid
        left join dbo.departInfo d with(nolock) on departId=d.id
        where lcount>0 group by isnull(d.name,'总部')
        union
        select 2 sort,sum(b.lcount) counts,isnull(sum(b.lcount*b.inprice),0) allPrices,'小件调拨在途',isnull(d.name,'总部') from dbo.diaobo_basket b with(nolock)
        left join dbo.diaobo_sub s with(nolock) on b.sub_id=s.id
        left join dbo.product_kc k with(nolock) on (k.areaid=s.areaid and k.ppriceid=b.ppriceid)
        left join areainfo a with(nolock) on a.id=s.areaid
        left join dbo.departInfo d with(nolock) on departId=d.id
        where s.stats in (2,3,5) group by isnull(d.name,'总部')
        union
        --订单欠款、已出库
        select 3 sort,sum(b.basket_count) counts,isnull(sum(b.basket_count*b.inprice),0) inprices,'小件订单欠款、已出库',isnull(d.name,'总部') from
        dbo.basket b with(nolock) left join dbo.sub s with(nolock) on b.sub_id=s.sub_id
        left join areainfo a with(nolock) on a.id=s.areaid
        left join dbo.departInfo d with(nolock) on departId=d.id
        where isnull(b.isdel,0)=0 and s.sub_check in (1,2,6) and isnull(b.ischu,0)=1 and b.ismobile=0 group by  isnull(d.name,'总部')
        union
        --报损、退货 已审核未办理
        select 4 sort,sum(b.lcount) lcounts,isnull(sum(b.price1*b.lcount),0) inprices,'小件报损、退货 已审核未办理 退货中',isnull(d.name,'总部') from dbo.return_basket b with(nolock)
        left join dbo.return_sub s with(nolock) on b.sub_id=s.id
        left join areainfo a with(nolock) on a.id=s.areaid
        left join dbo.departInfo d with(nolock) on departId=d.id
        where s.states=2 and s.type_ in (1,2,5,6,7,8)  group by isnull(d.name,'总部')
        union
        --小件旧件未转现
        SELECT 5 sort,count(1),isnull(sum(isnull(sf.inprice,b.inprice)),0),'小件旧件未转现',isnull(d.name,'总部') from shouhou_fanchang sf with(nolock)
        left join smallpro s with(nolock) on sf.smallproid=s.id
        left JOIN dbo.basket b with(nolock) ON sf.basket_id = b.basket_id left JOIN dbo.productinfo p with(nolock)
        on p.ppriceid = sf.ppid1
        left join areainfo a with(nolock) on a.id=s.areaid
        left join dbo.departInfo d with(nolock) on departId=d.id
        where isnull(sf.rstats,0) in (0,1)
        group by isnull(d.name,'总部')
        union
        --维修单已出配件 未交易完成
        select 6 sort,count(1),isnull(sum(w.inprice),0),'小件维修单已出库',isnull(d.name,'总部') from dbo.wxkcoutput w with(nolock)
        left join dbo.shouhou s with(nolock) on w.wxid=s.id
        left join areainfo a with(nolock) on a.id=w.areaid
        left join dbo.departInfo d with(nolock) on departId=d.id
        where isnull(s.isquji,0)=0 and isnull(w.islockkc,0)=0 and w.ppriceid &lt;&gt; 0 and isnull(w.stats,0) &lt;&gt; 3 group by isnull(d.name,'总部')

        union
        --大件
        select 7 sort,count(1) counts,isnull(sum(inbeihuoprice),0) prices,'大件库存',isnull(d.name,'总部') from dbo.product_mkc k with(nolock)
        left join areainfo a with(nolock) on a.id=k.areaid
        left join dbo.departInfo d with(nolock) on departId=d.id
        where kc_check in(2,3) and imei is not null group by isnull(d.name,'总部')
        union
        select 8 sort,count(1) counts,isnull(sum(inbeihuoprice),0) prices,'大件在途',isnull(d.name,'总部') from dbo.product_mkc k with(nolock)
        left join areainfo a with(nolock) on a.id=k.areaid
        left join dbo.departInfo d with(nolock) on departId=d.id
        where kc_check=10 and imei is not null group by isnull(d.name,'总部')
        union
        ----订单欠款、已出库
        select 9 sort,count(1) counts,isnull(sum(k.inbeihuoprice),0) inprices,'大件欠款',isnull(d.name,'总部') from dbo.product_mkc k with(nolock)
        left join areainfo a with(nolock) on a.id=k.areaid
        left join dbo.departInfo d with(nolock) on departId=d.id
        where kc_check=8  group by isnull(d.name,'总部')
        union
        --报损、退货 已审核为办理
        select 10 sort,count(1) counts,isnull(sum(k.inbeihuoprice),0) inprices,'大件 退货中',isnull(d.name,'总部') from dbo.product_mkc k with(nolock)
        left join areainfo a with(nolock) on a.id=k.areaid
        left join dbo.departInfo d with(nolock) on departId=d.id
        where kc_check=14 and k.inPzid is not null group by isnull(d.name,'总部')
        union
        select 11 sort,count(1) counts,isnull(sum(k.inbeihuoprice),0) inprices,'大件售后',isnull(d.name,'总部') from dbo.product_mkc k with(nolock)
        left join areainfo a with(nolock) on a.id=k.areaid
        left join dbo.departInfo d with(nolock) on departId=d.id
        where kc_check=6 group by isnull(d.name,'总部')
        union
        --回收库存
        select 12 sort,count(1) counts,isnull(sum(k.inprice),0) inprices,'回收库存',isnull(d.name,'总部') from
        dbo.recover_mkc k with(nolock)
        left join areainfo a with(nolock) on a.id=k.areaid
        left join dbo.departInfo d with(nolock) on departId=d.id
        where mkc_check in (3,10) and exists(select 1 from dbo.recover_basket b with(nolock) left join dbo.recover_sub s with(nolock) on b.sub_id=s.sub_id where b.id=k.from_basket_id and s.sub_check=3 ) group by isnull(d.name,'总部')
        union
        select 13 sort,count(1) counts,0-sum(kcount*price) inprices,'旧件返厂',isnull(d.name,'总部') from dbo.shouhou_huishou k with(nolock)
        left join areainfo a with(nolock) on a.id=k.areaid
        left join dbo.departInfo d with(nolock) on departId=d.id
        where hsjj_saletype=1 AND ISNULL(issale,0)=1 group by isnull(d.name,'总部')
    </select>


    <select id="getQueryTimeList" resultType="com.jiuji.oa.oacore.oaorder.vo.res.QueryTimeRes">

        select id,dtime from tmp_data with(nolock)
        where 1=1
        and contentNew is not null
        <if test="startTime != null and startTime != ''">
            and dtime between #{startTime} and #{endTime}
        </if>
        order by id desc

    </select>

    <select id="queryHistoryDataById" resultType="com.jiuji.oa.oacore.oaorder.bo.SubHistoryDataBo">

        select content,contentNew from tmp_data with(nolock) where id=#{id}

    </select>


    <select id="genPZ" statementType="CALLABLE">
        EXEC dbo.doSubSellcost1
	</select>

    <select id="queryBranchCompanys" resultType="java.lang.String">
      select isnull(d.name,'总部') departName  from dbo.product_kc k with(nolock) left join areainfo a with(nolock) on a.id=k.areaid
      left join dbo.departInfo d with(nolock) on departId=d.id
      where lcount>0 group by isnull(d.name,'总部')
    </select>

</mapper>