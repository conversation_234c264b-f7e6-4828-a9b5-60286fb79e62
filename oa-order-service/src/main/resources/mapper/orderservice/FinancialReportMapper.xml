<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.FinancialServiceMapper">

   <select id="queryFinancialData"  resultType="com.jiuji.oa.oacore.oaorder.vo.res.SubFinData">
       select isnull(sum(a.jie_amounts)-sum(a.dai_amounts),0) yuE,a.kemu keMu from dbo.cert_pz_record a with(nolock)
                left join dbo.cert_pz b with(nolock)
                on a.pzid=b.id
                where left(a.kemu,4)=1405
                group by a.kemu

   </select>

</mapper>