<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.RecoverSubMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.RecoverSub">
        <id column="sub_id" property="subId" />
        <result column="sub_to" property="subTo" />
        <result column="sub_tel" property="subTel" />
        <result column="sub_pay" property="subPay" />
        <result column="sub_address" property="subAddress" />
        <result column="sub_delivery" property="subDelivery" />
        <result column="sub_bank" property="subBank" />
        <result column="bank_user" property="bankUser" />
        <result column="bank_num" property="bankNum" />
        <result column="sub_check" property="subCheck" />
        <result column="cityid" property="cityid" />
        <result column="userid" property="userid" />
        <result column="area" property="area" />
        <result column="inuser" property="inuser" />
        <result column="dtime" property="dtime" />
        <result column="comment" property="comment" />
        <result column="ruku_time" property="rukuTime" />
        <result column="pay_time" property="payTime" />
        <result column="printCount" property="printCount" />
        <result column="pzid" property="pzid" />
        <result column="kuaididan" property="kuaididan" />
        <result column="sub_ido" property="subIdo" />
        <result column="tui_way" property="tuiWay" />
        <result column="smtime" property="smtime" />
        <result column="areaid" property="areaid" />
        <result column="isnetsub" property="isnetsub" />
        <result column="userCard" property="userCard" />
        <result column="reUserid" property="reUserid" />
        <result column="reUserName" property="reUserName" />
        <result column="reareaid" property="reareaid" />
        <result column="rehsUserName" property="rehsUserName" />
        <result column="rehsUserMobile" property="rehsUserMobile" />
        <result column="reZjyId" property="reZjyId" />
        <result column="isleshou" property="isleshou" />
        <result column="payopenid" property="payopenid" />
        <result column="externalCode" property="externalCode" />
        <result column="leshouRemark" property="leshouRemark" />
        <result column="kuaidigongsi" property="kuaidigongsi" />
        <result column="recover_ch999name" property="recoverCh999name" />
        <result column="recover_datetime" property="recoverDatetime" />
        <result column="recover_confirm" property="recoverConfirm" />
        <result column="recover_subType" property="recoverSubtype" />
        <result column="recover_confirmCh999Id" property="recoverConfirmch999id" />
        <result column="zitidianID" property="zitidianID" />
        <result column="kdTime" property="kdTime" />
        <result column="subIdoType" property="subIdoType" />
        <result column="kdtype" property="kdtype" />
        <result column="payUser" property="payUser" />
        <result column="islock" property="islock" />
        <result column="couponPrice" property="couponPrice" />
        <result column="payopenidKind" property="payopenidKind" />
    </resultMap>
    <select id="getInUser" resultType="java.lang.String">
         SELECT inuser FROM dbo.recover_sub with(nolock) WHERE sub_id=#{subId}
    </select>
    <select id="getRecoverDataByUserId"
            resultType="com.jiuji.oa.oacore.weborder.res.RecoverSubRes">
        SELECT
        s.sub_id subId,
        s.sub_to subTo,
        s.sub_tel subMobile,
        s.sub_pay subPay,
        s.sub_address subAddress,
        s.sub_delivery subDelivery,
        s.sub_bank subBank,
        s.bank_user bankUser,
        s.bank_num banNum,
        s.sub_check subCheck,
        s.areaid areaId,
        s.cityid cityid,
        s.dtime,
        s.comment,
        b.UserName,
        b.save_money,
        b.erdu,
        b.userclass
        FROM
        recover_sub s with(nolock)
        LEFT JOIN BBSXP_Users b with(nolock) ON s.userid= b.ID
        WHERE
        s.sub_check<![CDATA[<>]]> 4
        AND s.sub_pay = 3
        AND s.userid= #{userId}
        AND sub_ido = #{subId}
    </select>

</mapper>
