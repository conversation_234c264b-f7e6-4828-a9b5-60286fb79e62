<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.QudaoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.Qudao">
        <id column="id" property="id"/>
        <result column="enter_type" property="enterType"/>
        <result column="kinds" property="kinds"/>
        <result column="company" property="company"/>
        <result column="username" property="username"/>
        <result column="tel" property="tel"/>
        <result column="fax" property="fax"/>
        <result column="mobile" property="mobile"/>
        <result column="QQ" property="qq"/>
        <result column="wangwang" property="wangwang"/>
        <result column="address" property="address"/>
        <result column="Area" property="area"/>
        <result column="comment" property="comment"/>
        <result column="adddate" property="adddate"/>
        <result column="inuser" property="inuser"/>
        <result column="ispass" property="pass"/>
        <result column="email" property="email"/>
        <result column="insourceid" property="insourceid"/>
        <result column="company_jc" property="companyJc"/>
        <result column="g_zh1" property="gzh1"/>
        <result column="g_hm1" property="ghm1"/>
        <result column="g_khh1" property="gkhh1"/>
        <result column="g_zh2" property="gzh2"/>
        <result column="g_hm2" property="ghm2"/>
        <result column="g_khh2" property="gkhh2"/>
        <result column="s_zh1" property="szh1"/>
        <result column="s_hm1" property="shm1"/>
        <result column="s_khh1" property="skhh1"/>
        <result column="s_zh2" property="szh2"/>
        <result column="s_hm2" property="shm2"/>
        <result column="s_khh2" property="skhh2"/>
        <result column="cw_fzr" property="cwFzr"/>
        <result column="cw_lxfs" property="cwLxfs"/>
        <result column="s_zh3" property="szh3"/>
        <result column="s_hm3" property="shm3"/>
        <result column="s_khh3" property="skhh3"/>
        <result column="s_zh4" property="szh4"/>
        <result column="s_hm4" property="shm4"/>
        <result column="s_khh4" property="skhh4"/>
        <result column="s_zh5" property="szh5"/>
        <result column="s_hm5" property="shm5"/>
        <result column="s_khh5" property="skhh5"/>
        <result column="s_zh6" property="szh6"/>
        <result column="s_hm6" property="shm6"/>
        <result column="s_khh6" property="skhh6"/>
        <result column="s_zh7" property="szh7"/>
        <result column="s_hm7" property="shm7"/>
        <result column="s_khh7" property="skhh7"/>
        <result column="s_zh8" property="szh8"/>
        <result column="s_hm8" property="shm8"/>
        <result column="s_khh8" property="skhh8"/>
        <result column="kfp" property="kfp"/>
        <result column="authorizeid" property="authorizeid"/>
        <result column="signStartTime" property="signStartTime"/>
        <result column="signEndTime" property="signEndTime"/>
        <result column="cityid" property="cityid"/>
        <result column="pid" property="pid"/>
        <result column="zid" property="zid"/>
        <result column="did" property="did"/>
        <result column="CompanyNature" property="companyNature"/>
        <result column="QuDaoNature" property="quDaoNature"/>
        <result column="QuDaoLevel" property="quDaoLevel"/>
        <result column="PayType" property="payType"/>
        <result column="RegisteredCapital" property="registeredCapital"/>
        <result column="LegalRepresent" property="legalRepresent"/>
        <result column="LegalMobile" property="legalMobile"/>
        <result column="WeiXin" property="weiXin"/>
        <result column="shouhouContacts" property="shouhouContacts"/>
        <result column="after_address" property="afterAddress"/>
        <result column="after_cityId" property="afterCityId"/>
        <result column="shouhouMobile" property="shouhouMobile"/>
        <result column="yajin" property="yajin"/>
        <result column="SameKindQuDao" property="sameKindQuDao"/>
        <result column="LoanAmount_M" property="loanamountM"/>
        <result column="LoanAmount" property="loanAmount"/>
        <result column="CaiWuCheckUser" property="caiWuCheckUser"/>
        <result column="CaiWuCheckTime" property="caiWuCheckTime"/>
        <result column="ShenJiCheckUser" property="shenJiCheckUser"/>
        <result column="ShenJiCheckTime" property="shenJiCheckTime"/>
        <result column="PanDianUser" property="panDianUser"/>
        <result column="PanDianTime" property="panDianTime"/>
        <result column="kemu" property="kemu"/>
        <result column="charger" property="charger"/>
        <result column="username_1" property="username1"/>
        <result column="tel_1" property="tel1"/>
        <result column="username_2" property="username2"/>
        <result column="tel_2" property="tel2"/>
        <result column="comment1" property="comment1"/>
        <result column="seltPurchase" property="seltPurchase"/>
        <result column="CooperationId" property="cooperationId"/>
        <result column="CustomCode" property="customCode"/>
        <result column="userid" property="userid"/>
        <result column="ChannelType" property="channelType"/>
        <result column="ChannelScale" property="channelScale"/>
        <result column="DepositHasReceipt" property="depositHasReceipt"/>
        <result column="cids" property="cids"/>
        <result column="brandid" property="brandid"/>
        <result column="Receiver" property="receiver"/>
        <result column="ShippingAddress" property="shippingAddress"/>
        <result column="bindAreaId" property="bindAreaId"/>
        <result column="isAuction" property="auction"/>
        <result column="g_city1" property="gcity1"/>
        <result column="g_is_same_city1" property="gissamecity1"/>
        <result column="g_bank_number1" property="gbanknumber1"/>
        <result column="g_city2" property="gcity2"/>
        <result column="g_is_same_city2" property="gissamecity2"/>
        <result column="g_bank_number2" property="gbanknumber2"/>
    </resultMap>
    <select id="isQuDao" resultType="java.lang.String">
        select k.company_jc
        from dbo.Ok3w_qudao k with(nolock) inner join  dbo.BBSXP_Users u
        with (nolock) on k.tel=u.mobile or k.tel_1=u.mobile or k.tel_2=u.mobile
        where
            k.ispass=1
          and k.kinds in (0
            , 1
            , 3)
          and u.xtenant=0
          and u.id =#{userId}
    </select>
    <select id="getTel" resultType="java.lang.String">
        SELECT s.tel
        FROM dbo.caigou_sub c WITH(NOLOCK), dbo.Ok3w_qudao q WITH (NOLOCK),
            dbo.qudaocontactss s WITH (NOLOCK)
        WHERE c.insourceid=q.id
          and q.id=s.qudaoID
          and c.stats=3
          and len(s.tel)=11
          and s.tel like '1%'
          and ruku_dtime between (SELECT DATEADD(month
            , DATEDIFF(month
            , -1
            , getdate()) - 1
            , 0))
          and (SELECT DATEADD(ss
            , -1
            , DATEADD(month
            , DATEDIFF(month
            , 0
            , getdate()) + 1
            , 0)) as monthLastDay)
    </select>
    <select id="getIsCooperationAccessory" resultType="java.lang.Integer">
        select id
        from dbo.channel_kind_link
        <where>channel_id = #{param}
        and channel_state=1
        and kind=0
        </where>
    </select>

    <select id="getIsCooperationMobile" resultType="java.lang.Integer">
        select id
        from dbo.channel_kind_link
        <where> channel_id = #{param}
         and channel_state=1
         and kind=3
        </where>
    </select>

</mapper>
