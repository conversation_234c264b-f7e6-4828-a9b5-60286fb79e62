<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.SmallproMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.Smallpro">
        <id column="id" property="id" />
        <result column="Name" property="name" />
        <result column="Area" property="area" />
        <result column="userid" property="userid" />
        <result column="sub_id" property="subId" />
        <result column="Buydate" property="buydate" />
        <result column="Outward" property="outward" />
        <result column="IsBaoxiu" property="isBaoxiu" />
        <result column="Groupid" property="groupid" />
        <result column="Inuser" property="inuser" />
        <result column="Indate" property="indate" />
        <result column="Kind" property="kind" />
        <result column="Stats" property="stats" />
        <result column="Username" property="username" />
        <result column="Mobile" property="mobile" />
        <result column="Problem" property="problem" />
        <result column="Comment" property="comment" />
        <result column="toarea" property="toarea" />
        <result column="istoarea" property="istoarea" />
        <result column="yuyueCheck" property="yuyueCheck" />
        <result column="yuyueid" property="yuyueid" />
        <result column="areaid" property="areaid" />
        <result column="toareaid" property="toareaid" />
        <result column="oldid" property="oldid" />
        <result column="codeMsg" property="codeMsg" />
        <result column="owenStats" property="owenStats" />
        <result column="isshouyinglock" property="isshouyinglock" />
        <result column="feiyong" property="feiyong" />
        <result column="costprice" property="costprice" />
        <result column="shouyinuser" property="shouyinuser" />
        <result column="shouyindate" property="shouyindate" />
        <result column="istui" property="istui" />
        <result column="qujiandate" property="qujiandate" />
        <result column="wxqudao" property="wxqudao" />
        <result column="wxState" property="wxState" />
        <result column="wxuser" property="wxuser" />
        <result column="isCheckmsg" property="isCheckmsg" />
        <result column="isdel" property="isdel" />
        <result column="isth" property="isth" />
        <result column="tongzhitime" property="tongzhitime" />
        <result column="wuliuid" property="wuliuid" />
        <result column="fcWuliuId" property="fcWuliuId" />
        <result column="intertime" property="intertime" />
        <result column="toareatime" property="toareatime" />
        <result column="config" property="config" />
        <result column="dataRelease" property="dataRelease" />
        <result column="imei" property="imei" />
        <result column="fid" property="fid" />
        <result column="hhSubId" property="hhSubId" />
        <result column="changePpriceid" property="changePpriceid" />
        <result column="ServiceType" property="serviceType" />
    </resultMap>
    <select id="getInUser" resultType="java.lang.String">
        select Inuser from dbo.Smallpro with(nolock) where id=#{subId}
    </select>

</mapper>
