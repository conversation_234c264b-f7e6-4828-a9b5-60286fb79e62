<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.CibLogsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.CibLogs">
        <id column="id" property="id" />
        <result column="SRVRTID" property="srvrtid" />
        <result column="TRNTYPE" property="trntype" />
        <result column="TRNCODE" property="trncode" />
        <result column="DTACCT" property="dtacct" />
        <result column="TRNAMT" property="trnamt" />
        <result column="BALAMT" property="balamt" />
        <result column="CURRENCY" property="currency" />
        <result column="MEMO" property="memo" />
        <result column="CORRELATE_ACCTID" property="correlateAcctid" />
        <result column="CORRELATE_NAME" property="correlateName" />
        <result column="CHEQUENUM" property="chequenum" />
        <result column="BILLTYPE" property="billtype" />
        <result column="BILLNUMBER" property="billnumber" />
        <result column="CORRELATE_BANKNAME" property="correlateBankname" />
        <result column="CORRELATE_BANKCODE" property="correlateBankcode" />
        <result column="BUSINESSTYPE" property="businesstype" />
        <result column="ATTACHINFO" property="attachinfo" />
        <result column="ATTACHTYPE" property="attachtype" />
    </resultMap>

    <select id="count" resultType="java.lang.Integer">
        select count(*) from cibLogs with(nolock) where 1=1
        <if test="serverid != null and serverid != ''">
            and SRVRTID = #{serverid}
        </if>
        <if test="trncode != null and trncode != ''">
            and TRNCODE = #{trncode}
        </if>
        <if test="attachinfo != null and attachinfo != ''">
            and ATTACHINFO = #{attachinfo}
        </if>
        <if test="trnamt != null">
            and TRNAMT = #{trnamt}
        </if>
    </select>

</mapper>
