<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.CategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.Category">
        <id column="ID" property="id" />
        <result column="ParentID" property="ParentID" />
        <result column="Name" property="Name" />
        <result column="Level" property="Level" />
        <result column="Child" property="Child" />
        <result column="Rank" property="Rank" />
        <result column="Path" property="Path" />
        <result column="Display" property="Display" />
        <result column="IsMobile" property="IsMobile" />
    </resultMap>
    <select id="getByIds" resultType="com.jiuji.oa.oacore.promocode.bo.CommonSwitchBO">
        select id,Name from dbo.category WITH(NOLOCK) where id in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getCategoryd" resultType="java.lang.Integer">
        select ID from dbo.category WITH(NOLOCK) where id in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="listCategoryByIds" resultType="com.jiuji.oa.oacore.oaorder.po.Category">
        WITH TEMP AS
        (
        SELECT * FROM category with(nolock) WHERE ID in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and Display = 1
        UNION ALL
        SELECT t.* FROM TEMP,category t WHERE TEMP.ID=t.ParentID and t.Display = 1
        )
        SELECT * FROM TEMP;
    </select>
    <select id="selectCategoryChildrenByCid" resultType="java.lang.Integer">
        select id from dbo.f_category_children (#{cid})
    </select>
    <select id="getCaseOrFilmCid" resultType="java.lang.String">
        SELECT sc.value FROM  dbo.sysConfig sc where code in (100012,100013) and xtenant = 0 and isdel  = 0
    </select>

</mapper>
