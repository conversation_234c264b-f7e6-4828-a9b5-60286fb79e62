<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.OrderRecommendMapper">

    <!-- listKcByPpidAndArea -->
    <select id="listKcByPpidAndArea" resultType="com.jiuji.oa.oacore.cloud.bo.OrderRecommendBO$KcInRecommend">
        SELECT pk.ppriceid, pk.leftCount kcCount
        FROM product_kc pk WITH(NOLOCK)
        WHERE pk.areaid = #{areaId}
        <if test="ppids != null and ppids.size() > 0">
            AND pk.ppriceid IN
            <foreach collection="ppids" index="index" item="ppid" open="(" separator="," close=")">
                #{ppid}
            </foreach>
        </if>

    </select>

    <!-- listMkcByPpidAndArea -->
    <select id="listMkcByPpidAndArea" resultType="com.jiuji.oa.oacore.cloud.bo.OrderRecommendBO$KcInRecommend">
        SELECT pm.ppriceid, count(1) kcCount
        FROM product_mkc pm WITH(NOLOCK)
        WHERE pm.areaid = #{areaId}
          AND isnull(pm.mouldFlag, 0) = 0
          AND basket_id IS NULL
          AND NOT EXISTS (SELECT 1 FROM xc_mkc xm with(nolock) WHERE xm.mkc_id = pm.id)
            <if test="ppids != null and ppids.size() > 0">
                AND pm.ppriceid IN
                <foreach collection="ppids" index="index" item="ppid" open="(" separator="," close=")">
                    #{ppid}
                </foreach>
            </if>
        GROUP BY pm.ppriceid
    </select>

</mapper>
