<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.ShouhouMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.Shouhou">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="peizhi" property="peizhi" />
        <result column="problem" property="problem" />
        <result column="comment" property="comment" />
        <result column="username" property="username" />
        <result column="mobile" property="mobile" />
        <result column="tel" property="tel" />
        <result column="stats" property="stats" />
        <result column="baoxiu" property="baoxiu" />
        <result column="inuser" property="inuser" />
        <result column="imei" property="imei" />
        <result column="xianshi" property="xianshi" />
        <result column="contentcsdate" property="contentcsdate" />
        <result column="tradedate" property="tradedate" />
        <result column="modidate" property="modidate" />
        <result column="feiyong" property="feiyong" />
        <result column="costprice" property="costprice" />
        <result column="weixiuren" property="weixiuren" />
        <result column="dyjid" property="dyjid" />
        <result column="offtime" property="offtime" />
        <result column="area" property="area" />
        <result column="shouyinglock" property="shouyinglock" />
        <result column="shouyingdate" property="shouyingdate" />
        <result column="shouyinguser" property="shouyinguser" />
        <result column="userid" property="userid" />
        <result column="kinds" property="kinds" />
        <result column="isticheng" property="isticheng" />
        <result column="waiguan" property="waiguan" />
        <result column="result_dtime" property="resultDtime" />
        <result column="issoft" property="issoft" />
        <result column="modidtime" property="modidtime" />
        <result column="product_id" property="productId" />
        <result column="product_color" property="productColor" />
        <result column="buyarea" property="buyarea" />
        <result column="pandian" property="pandian" />
        <result column="pandiandate" property="pandiandate" />
        <result column="toarea" property="toarea" />
        <result column="istui" property="istui" />
        <result column="pandianinuser" property="pandianinuser" />
        <result column="ppriceid" property="ppriceid" />
        <result column="mkc_id" property="mkcId" />
        <result column="isquick" property="isquick" />
        <result column="wcount" property="wcount" />
        <result column="weixiuzuid" property="weixiuzuid" />
        <result column="weixiuzuid_jl" property="weixiuzuidJl" />
        <result column="isweixiu" property="isweixiu" />
        <result column="weixiudtime" property="weixiudtime" />
        <result column="weixiu_startdtime" property="weixiuStartdtime" />
        <result column="orderid" property="orderid" />
        <result column="isquji" property="isquji" />
        <result column="isfan" property="isfan" />
        <result column="pingjia" property="pingjia" />
        <result column="pingjia1" property="pingjia1" />
        <result column="sub_id" property="subId" />
        <result column="webtype1" property="webtype1" />
        <result column="webtype2" property="webtype2" />
        <result column="webstats" property="webstats" />
        <result column="ServiceType" property="ServiceType" />
        <result column="basket_id" property="basketId" />
        <result column="ishuishou" property="ishuishou" />
        <result column="yuyueid" property="yuyueid" />
        <result column="huiprint" property="huiprint" />
        <result column="weixiurentime" property="weixiurentime" />
        <result column="reweixiuren" property="reweixiuren" />
        <result column="sxname" property="sxname" />
        <result column="sxmobile" property="sxmobile" />
        <result column="sxsex" property="sxsex" />
        <result column="sxuserid" property="sxuserid" />
        <result column="lockpwd" property="lockpwd" />
        <result column="testuser" property="testuser" />
        <result column="wxkind" property="wxkind" />
        <result column="wxConfig" property="wxConfig" />
        <result column="noticetime" property="noticetime" />
        <result column="testtime" property="testtime" />
        <result column="deviceid" property="deviceid" />
        <result column="devicepwd" property="devicepwd" />
        <result column="youhuima" property="youhuima" />
        <result column="yuyueCheck" property="yuyueCheck" />
        <result column="isXcMkc" property="isXcMkc" />
        <result column="isXcMkcInfo" property="isXcMkcInfo" />
        <result column="wxTestTime" property="wxTestTime" />
        <result column="wxTestInfo" property="wxTestInfo" />
        <result column="RepairLevel" property="RepairLevel" />
        <result column="areaid" property="areaid" />
        <result column="toareaid" property="toareaid" />
        <result column="buyareaid" property="buyareaid" />
        <result column="wxTestStats" property="wxTestStats" />
        <result column="gjUser" property="gjUser" />
        <result column="ProcessConfirmStats" property="ProcessConfirmStats" />
        <result column="oldshouhouid" property="oldshouhouid" />
        <result column="isBakData" property="isBakData" />
        <result column="isjbanwxqq" property="isjbanwxqq" />
        <result column="yuyueCheckuser" property="yuyueCheckuser" />
        <result column="qujitongzhitime" property="qujitongzhitime" />
        <result column="daojishi" property="daojishi" />
        <result column="codeMsg" property="codeMsg" />
        <result column="result_user" property="resultUser" />
        <result column="smstime" property="smstime" />
        <result column="teltime" property="teltime" />
        <result column="EarnestMoneySubid" property="EarnestMoneySubid" />
        <result column="serversOutUser" property="serversOutUser" />
        <result column="serversOutDtime" property="serversOutDtime" />
        <result column="youhuifeiyong" property="youhuifeiyong" />
        <result column="truename" property="truename" />
        <result column="iszy" property="iszy" />
        <result column="wxAreaid" property="wxAreaid" />
        <result column="imeifid" property="imeifid" />
        <result column="yifum" property="yifum" />
        <result column="kuaixiuFlag" property="kuaixiuFlag" />
        <result column="kuaixiuSendTime" property="kuaixiuSendTime" />
        <result column="iszp" property="iszp" />
        <result column="wuliyou" property="wuliyou" />
        <result column="mobileServeiceType" property="mobileServeiceType" />
        <result column="lppeizhi" property="lppeizhi" />
        <result column="fromshouhouid" property="fromshouhouid" />
        <result column="ServiceCostprice" property="ServiceCostprice" />
        <result column="question_type" property="questionType" />
        <result column="pzid" property="pzid" />
    </resultMap>

    <select id="getShouhouListByShouhouIds" resultType="com.jiuji.oa.oacore.oaorder.bo.BasketProductBO">
               SELECT
            s.id AS subId,
            s.name AS productName,
            s.product_color AS productColor,
            s.ppriceid AS ppid,
            s.webtype2 AS wxWay
        FROM
            shouhou s with(nolock)
        WHERE 1=1
        <if test="shouhouIds != null and shouhouIds.size>0">
            and s.id IN
            <foreach collection="shouhouIds" index="index" item="shouhouId" open="(" separator="," close=")">
                #{shouhouId}
            </foreach>
        </if>
    </select>

    <select id="getShouHouAllUserId" resultType="java.lang.Integer">
        SELECT TOP 1 u.ch999_id FROM dbo.ch999_user AS u  with(nolock)
        LEFT JOIN dbo.shouhou AS s with(nolock) ON s.areaid = u.area1id
        LEFT JOIN dbo.zhiwu z with(nolock) ON u.zhiwuid = z.id
        WHERE u.iszaizhi = 1 AND CHARINDEX('17',u.roles)&gt;0 AND isnull(u.isshixi, 0) != 4 AND s.id = #{subId}
        ORDER BY z.leve DESC, u.ch999_id ASC
    </select>

    <select id="getShouHouAllUsername" resultType="java.lang.String">
        SELECT TOP 1 u.ch999_name FROM dbo.ch999_user AS u  with(nolock)
        LEFT JOIN dbo.shouhou AS s with(nolock) ON s.areaid = u.area1id
        LEFT JOIN dbo.zhiwu z with(nolock) ON u.zhiwuid = z.id
        WHERE u.iszaizhi = 1 AND CHARINDEX('17',u.roles)&gt;0 AND isnull(u.isshixi, 0) != 4 AND s.id = #{subId}
        ORDER BY z.leve DESC, u.ch999_id ASC
    </select>
    <select id="getInUser" resultType="java.lang.String">
        SELECT TOP 1 sh.inuser FROM dbo.shouhou AS sh with(nolock) WHERE sh.id = #{subId}
    </select>

</mapper>
