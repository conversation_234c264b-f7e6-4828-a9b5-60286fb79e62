<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.DoorVisitRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.DoorVisitRecord">
        <id column="id" property="id" />
        <result column="dtime" property="dtime" />
        <result column="customName" property="customName" />
        <result column="insuer" property="insuer" />
        <result column="mobile" property="mobile" />
        <result column="visitTime" property="visitTime" />
        <result column="visitCode" property="visitCode" />
        <result column="issend" property="issend" />
        <result column="ch999_id" property="ch999Id" />
        <result column="printTime" property="printTime" />
        <result column="printUser" property="printUser" />
        <result column="printCount" property="printCount" />
        <result column="remark" property="remark" />
        <result column="cardid" property="cardid" />
        <result column="msg" property="msg" />
        <result column="departId" property="departId" />
        <result column="isAddEvaluate" property="isAddEvaluate" />
        <result column="userid" property="userid" />
        <result column="areaid" property="areaid" />
        <result column="state_" property="state" />
        <result column="arrivalTime" property="arrivalTime" />
        <result column="isStateChange" property="isStateChange" />
        <result column="visitType" property="visitType" />
    </resultMap>
    <select id="getCh999UerId" resultType="java.lang.Integer">
        SELECT d.ch999_id FROM dbo.doorVisitRecord d WITH(NOLOCK) WHERE d.id =#{subId}
    </select>

</mapper>
