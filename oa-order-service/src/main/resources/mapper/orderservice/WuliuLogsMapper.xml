<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.WuliuLogsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.WuliuLogs">
        <id column="id" property="id" />
        <result column="wuliuid" property="wuliuid" />
        <result column="dtime" property="dtime" />
        <result column="inuser" property="inuser" />
        <result column="msg" property="msg" />
    </resultMap>

    <select id="getSmallproByWuliuFlag" resultType="java.lang.Integer">
<!--        当查询结果为0时，该物流单中的小件单都完成了-->
        SELECT count(*) FROM Smallpro sm WITH(nolock) WHERE sm.wuliuid = #{logisticsId} AND isnull(sm.istoarea,0) != 0
    </select>
</mapper>
