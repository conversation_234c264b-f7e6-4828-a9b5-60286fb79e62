<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.BbsxpUsersMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.BbsxpUsers">
        <id column="ID" property="id"/>
        <result column="UserName" property="userName"/>
        <result column="UserPass" property="userPass"/>
        <result column="mobile" property="mobile"/>
        <result column="tel" property="tel"/>
        <result column="UserMail" property="userMail"/>
        <result column="UserSex" property="userSex"/>
        <result column="UserLastIP" property="userLastIP"/>
        <result column="UserRegTime" property="userRegTime"/>
        <result column="UserLandTime" property="userLandTime"/>
        <result column="UserBuyTime" property="userBuyTime"/>
        <result column="realname" property="realname"/>
        <result column="userclass" property="userclass"/>
        <result column="totalpoint" property="totalpoint"/>
        <result column="points" property="points"/>
        <result column="chengzhangzhi" property="chengzhangzhi"/>
        <result column="blacklist" property="blacklist"/>
        <result column="comment" property="comment"/>
        <result column="inuser" property="inuser"/>
        <result column="istemp" property="istemp"/>
        <result column="area" property="area"/>
        <result column="kinds" property="kinds"/>
        <result column="erdu" property="erdu"/>
        <result column="save_money" property="saveMoney"/>
        <result column="hezuo" property="hezuo"/>
        <result column="hezuo_name" property="hezuoName"/>
        <result column="lastarea" property="lastarea"/>
        <result column="birthday" property="birthday"/>
        <result column="areaid" property="areaid"/>
        <result column="LastAreaId" property="lastAreaId"/>
        <result column="salt" property="salt"/>
        <result column="AgeGroup" property="ageGroup"/>
        <result column="HasChild" property="hasChild"/>
        <result column="ChildSex" property="childSex"/>
        <result column="ChildAgeGroup" property="childAgeGroup"/>
        <result column="uCoin" property="uCoin"/>
        <result column="LastLoginClient" property="lastLoginClient"/>
        <result column="regClient" property="regClient"/>
        <result column="cityid" property="cityid"/>
        <result column="isValidate" property="isValidate"/>
        <result column="EffectM" property="effectM"/>
        <result column="payPwd" property="payPwd"/>
        <result column="saltPay" property="saltPay"/>
        <result column="UserBuyTime1" property="userBuyTime1"/>
        <result column="LastAreaId1" property="lastAreaId1"/>
        <result column="frozenMoney" property="frozenMoney"/>
        <result column="xtenant" property="xtenant"/>
        <result column="specialType" property="specialType"/>
        <result column="iaMarry" property="iaMarry"/>
        <result column="occupation" property="occupation"/>
        <result column="hasCar" property="hasCar"/>
        <result column="isBigCustomer" property="isBigCustomer"/>
        <result column="userNature" property="userNature"/>
        <result column="visitTime" property="visitTime"/>
        <result column="hasNoticedWay" property="hasNoticedWay"/>
        <result column="blackListType" property="blackListType"/>
        <result column="headImg" property="headImg"/>
        <result column="wxHeadImg" property="wxHeadImg"/>
        <result column="firstAppLoginTime" property="firstAppLoginTime"/>
        <result column="bindGroupId" property="bindGroupId"/>
        <result column="UserLastLandTime" property="userLastLandTime"/>
        <result column="UserAppLandtime" property="userAppLandtime"/>
    </resultMap>
    <update id="updateBBSXPUsers">
        UPDATE dbo.BBSXP_Users SET totalpoint=totalpoint+#{jifen},points=points+#{jifen},
        chengzhangzhi=isnull(chengzhangzhi,0)+#{price} WHERE id=#{userid}
    </update>
    <update id="updateBBSXPUser">
        UPDATE dbo.BBSXP_Users SET totalpoint=totalpoint-#{jifen},points=points-#{jifen},
        chengzhangzhi=isnull(chengzhangzhi,0)-#{price} WHERE id=#{userid}
    </update>


    <select id="queryUserExternal" parameterType="com.jiuji.oa.oacore.oaorder.req.UserExternalReq"
            resultType="com.jiuji.oa.oacore.oaorder.res.UserExternalVO">
         SELECT
        u.realname as realName,
        u.mobile,
        u.xtenant,
        u.UserRegTime as regTime,
        ISNULL(u.LastAreaId, u.areaid) AS areaId,
        a.area AS areaCode,
        a.area_name AS areaName,
        c.proName
        <include refid="queryUserExternalWhere"></include>
    </select>

    <sql id="queryUserExternalWhere">
        FROM BBSXP_Users u WITH(NOLOCK)
        LEFT JOIN areainfo a WITH(NOLOCK) ON a.id = ISNULL(u.LastAreaId, u.areaid)
        LEFT JOIN ClientCollInfo c WITH(NOLOCK) ON c.userid = u.ID
        AND c.collId = (SELECT MAX(collId) FROM ClientCollInfo WITH(NOLOCK) WHERE userid = u.ID)
        WHERE 1=1
        <if test="req.regTimeStart != null and req.regTimeStart != ''  and req.regTimeEnd != null and req.regTimeStart != ''">
            and u.UserRegTime BETWEEN #{req.regTimeStart} and #{req.regTimeEnd}
        </if>
        <if test="req.mobileList!=null and req.mobileList.size() != 0">
            and u.mobile in
            <foreach collection="req.mobileList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>
    <select id="getSubInfo" resultType="com.jiuji.oa.oacore.oaorder.res.UserSubInfo">
        <!-- 获取每个用户最近的一笔订单-->
        WITH LatestPurchases AS (
        SELECT
        u.mobile,
        u.UserName,
        s.userid,
        s.sub_id,
        s.tradeDate1,
        ROW_NUMBER() OVER (PARTITION BY u.mobile ORDER BY s.tradeDate1 DESC) AS rn
        FROM sub s WITH(NOLOCK)
        LEFT JOIN BBSXP_Users u WITH(NOLOCK) ON s.userid = u.ID
        WHERE s.sub_check = 3
        AND s.tradeDate1 IS NOT NULL
        AND s.userid IS NOT NULL
        AND u.mobile IS NOT NULL
        AND u.mobile IN
        <foreach collection="mobileList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ),
        <!-- 获取每个订单中最贵的商品（可能为空）-->
        TopItems AS (
        SELECT
        b.sub_id,
        b.price,
        b.seller,
        ROW_NUMBER() OVER (PARTITION BY b.sub_id ORDER BY b.price DESC) AS item_rank
        FROM basket b WITH(NOLOCK)
        WHERE isnull(b.isdel, 0) = 0
        AND b.sub_id IN (SELECT sub_id FROM LatestPurchases WHERE rn = 1)
        )
        <!-- 最终结果：即使用户没有basket记录也会显示-->
        SELECT
        lp.mobile ,
        lp.UserName,
        lp.userid,
        lp.tradeDate1 AS lastUserBuyTime,
        ti.price,
        ti.seller AS saleUserName,
        lp.sub_id
        FROM LatestPurchases lp
        LEFT JOIN TopItems ti ON lp.sub_id = ti.sub_id AND ti.item_rank = 1
        <!-- 只取每个用户最近的一笔订单-->
        WHERE lp.rn = 1
        ORDER BY lp.tradeDate1 DESC
    </select>

<!--    <select id="queryUserExternalTotal" parameterType="com.jiuji.oa.oacore.oaorder.req.UserExternalReq"-->
<!--            resultType="java.lang.Integer">-->
<!--        SELECT-->
<!--        count(*)-->
<!--        <include refid="queryUserExternalWhere"></include>-->
<!--    </select>-->
    <select id="getCallServiceUser" resultType="com.jiuji.oa.oacore.oaorder.res.UserBO">
        SELECT u.userclass,ISNULL(u.realname,u.UserName) AS userName,16 areaid, id userid, 0 sub_send
        FROM dbo.BBSXP_Users u WITH(NOLOCK) WHERE id= #{userId}
    </select>

    <select id="getLineUser" resultType="com.jiuji.oa.oacore.oaorder.res.UserBO">
        SELECT top 1 s.sub_id sub_id,u.userclass,isnull(u.hezuo_name,u.UserName) as userName,s.areaid,s.delivery sub_send,s.userid
        FROM dbo.sub s WITH(NOLOCK) LEFT JOIN dbo.BBSXP_Users u WITH(NOLOCK) on s.userid = u.ID
        WHERE  s.sub_id = #{subId}
    </select>

    <select id="getShouhouUser" resultType="com.jiuji.oa.oacore.oaorder.res.UserBO">
        SELECT top 1 sh.id sub_id,u.userclass,isnull(u.realname,u.UserName) as userName,sh.areaid,sh.userid
        FROM dbo.shouhou sh WITH(NOLOCK)
        LEFT JOIN dbo.BBSXP_Users u WITH(NOLOCK) ON sh.userid=u.ID
        WHERE sh.issoft=0 and sh.isquji=1 and sh.xianshi=1 and userid != 76783 AND sh.id = #{subId}
    </select>

    <select id="getSoftUser" resultType="com.jiuji.oa.oacore.oaorder.res.UserBO">
        SELECT top 1 m.id AS sub_id,u.userclass,ISNULL(u.realname,u.UserName) AS userName,m.areaid,m.userid, 0 sub_send
        FROM dbo.msoft m WITH(NOLOCK) LEFT JOIN dbo.BBSXP_Users u WITH(NOLOCK) ON m.userid=u.ID WHERE m.id = #{subId}
    </select>

    <select id="getLiangpinUser" resultType="com.jiuji.oa.oacore.oaorder.res.UserBO">
        SELECT top 1 s.sub_id as sub_id,u.userclass,isnull(u.hezuo_name,u.UserName) as userName,s.areaid,s.delivery sub_send,s.userid
        FROM dbo.recover_marketInfo s WITH(NOLOCK) LEFT JOIN dbo.BBSXP_Users u WITH(NOLOCK) on s.userid = u.ID
        WHERE  s.sub_id = #{subId}
    </select>

    <select id="getInterviewUser" resultType="com.jiuji.oa.oacore.oaorder.res.UserBO">
        SELECT top 1 d.id sub_id,userid,u.userclass,isnull(u.hezuo_name,u.UserName) as userName,d.areaid
        FROM dbo.doorVisitRecord d WITH(NOLOCK)
        LEFT JOIN dbo.BBSXP_Users u WITH(NOLOCK) ON d.userid = u.ID
        WHERE d.id = #{subId} and d.printTime is not null and datediff(day,d.printTime,getdate()) &lt;= 2
    </select>

    <select id="getOnlineServicesUserId" resultType="java.lang.Integer">
        select FromUserID from dbo.MsgCenterQueue with(nolock) where ID = #{subId}
    </select>

    <select id="getOnlineServicesUser" resultType="com.jiuji.oa.oacore.oaorder.res.UserBO">
        SELECT u.ID userid,u.userclass,isnull(u.hezuo_name,u.UserName) as userName,u.areaid
        FROM dbo.BBSXP_Users u WITH(NOLOCK) WHERE u.ID = #{userId}
    </select>

    <select id="getAfterSaleUser" resultType="com.jiuji.oa.oacore.oaorder.res.UserBO">
        SELECT top 1 s.id sub_id,u.userclass,isnull(u.hezuo_name,u.UserName) as userName,s.areaid,s.userid
        FROM dbo.Smallpro s WITH(NOLOCK)
        LEFT JOIN dbo.BBSXP_Users u WITH(NOLOCK) on s.userid = u.ID
        WHERE s.id = #{subId}
    </select>

    <select id="getRecycleUser" resultType="com.jiuji.oa.oacore.oaorder.res.UserBO">
        SELECT top 1 s.sub_id as sub_id,u.userclass,isnull(u.hezuo_name,u.UserName) as userName,s.areaid,s.userid
        FROM dbo.recover_sub s WITH(NOLOCK)
        LEFT JOIN dbo.BBSXP_Users u WITH(NOLOCK) on s.userid = u.ID
        WHERE s.sub_id = #{subId}
    </select>

    <select id="getComplaintUser" resultType="com.jiuji.oa.oacore.oaorder.res.UserBO">
        SELECT s.id as sub_id,u.userclass,isnull(u.hezuo_name,u.UserName) as userName,isnull(s.areaid,22) areaid,s.userid
        FROM ${officeName}.dbo.tousu s WITH(NOLOCK)
        LEFT JOIN dbo.BBSXP_Users u WITH(NOLOCK) on s.userid = u.ID
        WHERE s.id = #{subId}
    </select>

    <select id="lineIsCanEvaluate" resultType="java.lang.Integer">
        select count(*) from dbo.ch999_user WITH(NOLOCK)
        where iszaizhi=1 and mobile in (select sub_mobile from dbo.sub WITH(NOLOCK) where sub_id = #{subId})
    </select>

    <select id="shouhouIsCanEvaluate" resultType="java.lang.Integer">
        SELECT count(*) FROM dbo.ch999_user u WITH(NOLOCK) WHERE iszaizhi=1 AND EXISTS
        (
            SELECT 1 FROM dbo.shouhou s WITH(NOLOCK)
            INNER JOIN dbo.BBSXP_Users u1 WITH(NOLOCK) ON s.userid = u1.ID
            WHERE s.id = #{subId} AND u1.mobile = u.mobile
        )
    </select>

    <select id="liangpingIsCanEvaluate" resultType="java.lang.Integer">
        SELECT count(*) FROM dbo.ch999_user WITH(NOLOCK) WHERE iszaizhi=1 and mobile IN
        (
            SELECT u.mobile FROM dbo.recover_marketInfo s WITH(NOLOCK) INNER JOIN dbo.BBSXP_Users u WITH(NOLOCK) ON s.userid=u.ID
            WHERE s.saleType=0 AND s.sub_id = #{subId}
        )
    </select>

    <select id="afterSaleIsCanEvaluate" resultType="java.lang.Integer">
        SELECT count(*) FROM dbo.ch999_user u WITH(NOLOCK)
        WHERE u.iszaizhi = 1 AND EXISTS
        (
            SELECT 1 FROM dbo.Smallpro s WITH(NOLOCK)
            INNER JOIN dbo.BBSXP_Users u1 WITH(NOLOCK) ON s.userid = u1.ID
            WHERE s.id = #{subId} AND (s.Mobile = u.mobile OR u1.mobile = u.mobile)
        )
    </select>

    <select id="recycleIsCanEvaluate" resultType="java.lang.Integer">
        SELECT count(*) FROM dbo.ch999_user u WITH(NOLOCK) WHERE iszaizhi=1 AND EXISTS
        (
            SELECT 1 FROM dbo.recover_sub s WITH(NOLOCK)
            INNER JOIN dbo.BBSXP_Users u1 WITH(NOLOCK) ON s.userid=u1.ID
            WHERE s.sub_id = #{subId} AND u1.mobile = u.mobile
        )
    </select>

    <select id="complaintIsCanEvaluate" resultType="java.lang.Integer">
        SELECT count(*) FROM dbo.ch999_user u WITH(NOLOCK) WHERE iszaizhi=1 AND EXISTS
        (
            SELECT 1 FROM ${officeName}.dbo.tousu s WITH(NOLOCK)
            INNER JOIN dbo.BBSXP_Users u1 WITH(NOLOCK) ON s.userid=u1.ID
            WHERE s.id = #{subId} AND u1.mobile = u.mobile
        )
    </select>

    <select id="getByUserId" resultType="java.lang.Integer">
        select top 1 ID from dbo.BBSXP_Users WITH(NOLOCK) where ID=#{userId} and isnull(chengzhangzhi,0)=0
    </select>
    <select id="getDianZhang" resultType="java.lang.String">
        SELECT TOP 1 u.ch999_name FROM dbo.ch999_user u with(nolock) left join dbo.zhiwu z with(nolock)
        on u.zhiwuid=z.id WHERE u.islogin=0 AND u.iszaizhi=1
        AND (z.name='店长' OR z.name='副店长') AND u.area1id=@areaid ORDER BY z.leve
    </select>
    <select id="getUserClassName" resultType="java.lang.Integer">
        SELECT userclass FROM dbo.BBSXP_Users with(nolock) WHERE 1=1
        <if test="type != null and type != 0">
            <choose>
                <when test="type == 1">
                    and mobile=#{userId}
                </when>
                <when test="type == 2">
                    and id=#{userId}
                </when>
            </choose>
        </if>
    </select>
    <select id="getUserName" resultType="java.lang.String">
        SELECT UserName FROM dbo.BBSXP_Users with(nolock)
        <where>
            <choose>
                <when test="mobile != null and mobile != 0">
                    mobile=#{mobile}
                </when>

            </choose>
        </where>
    </select>
    <select id="getUserNameByUserId" resultType="java.lang.String">
         SELECT UserName FROM dbo.BBSXP_Users with(nolock) WHERE id=#{userId}
    </select>
    <select id="getUserNameByMobile" resultType="java.lang.String">
        SELECT UserName FROM dbo.BBSXP_Users with(nolock) WHERE mobile=#{mobile}
    </select>
    <select id="getUserListByUserIds" resultType="com.jiuji.oa.oacore.oaorder.res.UserBO">
        select ID userid, isnull(chengzhangzhi, 0) growth
        FROM dbo.BBSXP_Users with(nolock)
        where userclass = 6
        and ID in
        (
            ${userIds}
        )
    </select>

    <select id="getUserTypesByUserIds" resultType="com.jiuji.oa.oacore.oaorder.res.UserBO">
        SELECT ID userid,
        CASE WHEN userTypes <![CDATA[&]]> 1 = 1 THEN '(1),' ELSE ''
        END +
        CASE WHEN userTypes <![CDATA[&]]> 2 = 2 THEN '(2),' ELSE ''
        END +
        CASE WHEN userTypes <![CDATA[&]]> 4 = 4 THEN '(4),' ELSE ''
        END +
        CASE WHEN userTypes <![CDATA[&]]> 8 = 8 THEN '(8),' ELSE ''
        END +
        CASE WHEN userTypes <![CDATA[&]]> 16 = 16 THEN '(16),' ELSE ''
        END AS userTypesStr
        FROM dbo.BBSXP_Users WITH(nolock)
        WHERE ID IN (${userIds})
    </select>
    
    <select id="getLabelListByUserIds" resultType="com.jiuji.oa.oacore.oaorder.res.UserBO">
        select user_id
        from bbsxp_users_blacklist with(nolock)
        where is_del = 0
        and status = 0
        and isnull(end_time, dateadd(day, 1, getdate())) > getdate()
        and user_id IN (${userIds})
    </select>

    <select id="getUserIdByMobile" resultType="java.lang.Integer">
        select top 1 id from BBSXP_Users with(nolock) where (mobile = #{mobile} or tel= #{mobile})
    </select>
    <select id="selectCancellationMember" resultType="java.lang.Integer">
        select ID from dbo.bbsxp_users_deleted with(nolock) where ID = #{userId}
    </select>
</mapper>
