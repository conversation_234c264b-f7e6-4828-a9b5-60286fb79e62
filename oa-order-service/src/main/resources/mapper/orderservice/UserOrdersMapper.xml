<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.UserOrdersMapper">
    <resultMap id="HuiShouSubMapper" type="com.jiuji.oa.oacore.oaorder.bo.HuishouSub">
        <result column="areaid" property="areaid"/>
        <result column="sub_id" property="sub_id"/>
        <result column="sub_address" property="subAddress"/>
        <result column="sub_delivery" property="delivery"/>
        <result column="sub_check" property="sub_check"/>
        <result column="dtime" property="addtime"/>
        <result column="pay_time" property="finishtime"/>
        <result column="area" property="areaCode"/>
        <result column="company_address" property="areaAddress"/>
        <result column="company_tel1" property="areaTel"/>
        <result column="area_name" property="areaName"/>
        <result column="ispj" property="ispj"/>
        <result column="couponPrice" property="couponPrice"/>
    </resultMap>
    <resultMap id="LiangPinSubMapper" type="com.jiuji.oa.oacore.oaorder.bo.LiangpinSub">
        <result column="yingfuM" property="yinfum"/>
        <result column="yifuM" property="yifuM"/>
        <result column="areaid" property="areaid"/>
        <result column="delivery" property="delivery"/>
        <result column="paijianren" property="sender"/>
        <result column="sub_check" property="sub_check"/>
        <result column="sub_id" property="sub_id"/>
        <result column="sub_date" property="addtime"/>
        <result column="tradeDate1" property="finishtime"/>
        <result column="ispj" property="ispj"/>
        <result column="paisongState" property="paisongState"/>
        <result column="sub_ido" property="recoverSubid"/>
        <result column="psStatsName" property="psStatsName"/>
    </resultMap>
    <resultMap id="SubHSProMapper" type="com.jiuji.oa.oacore.oaorder.bo.ProductInfoForOrders">
        <result column="ppriceid" property="ppriceid"/>
        <result column="basketid" property="baset_id"/>
        <result column="ismobile1" property="ismobile"/>
        <result column="product_name" property="product_name"/>
        <result column="product_color" property="product_color"/>
        <result column="price" property="product_price"/>
        <result column="imageUrl" property="product_url"/>
        <result column="sub_id" property="sub_id"/>
    </resultMap>
    <resultMap id="SubLPProMapper" type="com.jiuji.oa.oacore.oaorder.bo.ProductInfoForOrders">
        <result column="ppriceid" property="ppriceid"/>
        <result column="basketid" property="baset_id"/>
        <result column="ismobile1" property="ismobile"/>
        <result column="product_name" property="product_name"/>
        <result column="product_color" property="product_color"/>
        <result column="price" property="product_price"/>
        <result column="imageUrl" property="product_url"/>
        <result column="sub_id" property="sub_id"/>
        <result column="imei" property="imei"/>
        <result column="kcaeraid" property="kcaeraid"/>
        <result column="areaid" property="areaid"/>
        <result column="mkc_check" property="mkc_check"/>
    </resultMap>
    <resultMap id="XiaoJianMapper" type="com.jiuji.oa.oacore.oaorder.bo.XiaojianSub">
        <result column="id" property="id"/>
        <result column="Kind" property="kind"/>
        <result column="Problem" property="problem"/>
        <result column="Outward" property="outward"/>
        <result column="Stats" property="stats"/>
        <result column="IsBaoxiu" property="baoXiuFlag"/>
        <result column="Feiyong" property="feiYong"/>
        <result column="Costprice" property="costPrice"/>
        <result column="YuyueId" property="yuYueId"/>
        <result column="Mobile" property="mobile"/>
        <result column="UserName" property="userName"/>
        <result column="InDate" property="inDate"/>
        <result column="Buydate" property="buyDate"/>
        <result column="sub_id" property="subId"/>
        <result column="troubleDesc" property="troubleDesc"/>
        <result column="ispj" property="pjFlag"/>
    </resultMap>
    <resultMap id="XiaoJianProMapper" type="com.jiuji.oa.oacore.oaorder.bo.XiaojianProduct">
        <result column="smallproID" property="xjid"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="product_name" property="product_name"/>
        <result column="product_color" property="product_color"/>
        <result column="price" property="product_price"/>
        <result column="count" property="product_count"/>
        <result column="imageUrl" property="product_url"/>
        <result column="cid" property="cid"/>
    </resultMap>

    <resultMap id="ShouHouSubMapper" type="com.jiuji.oa.oacore.oaorder.bo.ShouhouSub">
        <result column="sub_id" property="sub_id"/>
        <result column="stats" property="stats"/>
        <result column="modidate" property="addtime"/>
        <result column="offtime" property="finishtime"/>
        <result column="ispj" property="pjFlag"/>
        <result column="isquji" property="qujiFlag"/>
        <result column="shouyinglock" property="shouyinglock"/>
        <result column="kind" property="kind"/>
        <result column="stype" property="stype"/>
        <result column="areaid" property="areaid"/>
        <result column="problem" property="troubleDesc"/>
        <result column="yuyuePPids" property="yuyuePPids"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="product_color" property="product_color"/>
        <result column="product_name" property="product_name"/>
        <result column="feiyong" property="feiyong"/>
        <result column="imageUrl" property="imageUrl"/>
        <result column="imei" property="imei"/>
        <result column="issoft" property="isSoft"/>
    </resultMap>

    <resultMap id="ShouHouYuYueMapper" type="com.jiuji.oa.oacore.oaorder.bo.ShouhouYuyueSub">
        <result column="sub_id" property="sub_id"/>
        <result column="stats" property="stats"/>
        <result column="dtime" property="addtime"/>
        <result column="kind" property="kind"/>
        <result column="stype" property="stype"/>
        <result column="areaid" property="areaid"/>
        <result column="problem" property="troubleDesc"/>
        <result column="yuyuePPids" property="yuyuePPids"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="product_color" property="product_color"/>
        <result column="product_name" property="product_name"/>
        <result column="imageUrl" property="product_url"/>
    </resultMap>

    <select id="huiShouSubList" resultMap="HuiShouSubMapper">
        SELECT s.areaid,s.sub_id,s.sub_to,s.sub_tel,s.sub_pay,s.sub_address,s.sub_delivery,s.sub_check,s.userid,inuser,s.dtime,s.pay_time,
        a.area,a.company_address,a.company_tel1,a.area_name,(case when e.id is null then 0 else 1 end) as ispj,s.couponPrice
        FROM dbo.recover_sub s with(nolock)
        LEFT JOIN dbo.areainfo a with(nolock) ON s.areaid=a.id
        left join office.dbo.evaluate e with(nolock) on (e.subid = s.sub_id and e.EvaluateType = 15)
        WHERE s.userid=#{userid}
        <![CDATA[ AND DATEDIFF(MONTH,s.dtime,GETDATE()) <= 24 ]]>
        and s.sub_check NOT IN(4,9)
        and not exists (select 1 from dbo.sub_delCollect c with(nolock) where s.sub_id = c.sub_id and c.subType=#{subType})
        <choose>
            <when test="xtenant == 0">
                and EXISTS(select 1 from dbo.areainfo ar with(nolock) where s.areaid=ar.id and ar.printName='九机网' AND ar.xtenant =#{xtenant})
            </when>
            <when test="xtenant == 2">
                and EXISTS(select 1 from dbo.areainfo ar with(nolock) where s.areaid=ar.id and ar.printName='华为授权' )
            </when>
            <otherwise>
                and EXISTS(select 1 from dbo.areainfo ar with(nolock) where s.areaid=ar.id and ar.xtenant=#{xtenant})
            </otherwise>
        </choose>
    </select>
    <select id="subHSProList" resultMap="SubHSProMapper">
        SELECT p.productid,p.ppriceid,p.product_name,p.product_color,b.price,p.ismobile1,b.id basketid, concat('http://img2.ch999img.com/pic/product/440x440/',p.bpic) AS imageUrl,b.sub_id
        FROM dbo.recover_basket b with(nolock)
        INNER JOIN dbo.recover_sub s with(nolock) ON s.sub_id = b.sub_id
        INNER JOIN dbo.productinfo p with(nolock) ON b.ppriceid=p.ppriceid
        WHERE ISNULL(b.isdel,0)=0
        <![CDATA[ AND s.sub_check <> 4]]>
        and s.sub_id in
        <foreach item="subIds" collection="list" separator="," open="(" close=")" index="">
            #{subIds}
        </foreach>
        UNION ALL
        SELECT p.productid,p.ppriceid,p.product_name,p.product_color,b.price,p.ismobile1,b.id basketid, concat('http://img2.ch999img.com/pic/product/440x440/',p.bpic) AS imageUrl,b.sub_id
        FROM dbo.recover_basket b with(nolock)
        INNER JOIN dbo.recover_sub s with(nolock) ON s.sub_id = b.sub_id
        INNER JOIN dbo.productinfo p with(nolock) ON b.ppriceid=p.ppriceid
        WHERE s.sub_check = 4  and s.sub_id in
        <foreach item="subIds" collection="list" separator="," open="(" close=")" index="">
            #{subIds}
        </foreach>
    </select>
    <select id="liangPinSubList" resultMap="LiangPinSubMapper">
        SELECT (case when e.id is null then 0 else 1 end) as ispj,rm.sub_date,rm.tradeDate1,rm.areaid,rm.sub_id,rm.sub_check,rm.delivery,rm.sub_id,rm.yingfuM,rm.yifuM,w.paijianren,
        addr.paisongState,addr.Address,addr.cityid,ss.sub_id as sub_ido,case addr.paisongState when 2 then '已送出' when 4 then '已送达' else '未送出' end as psStatsName
        FROM dbo.recover_marketInfo rm with(nolock)
        LEFT JOIN dbo.wuliu w with(nolock) ON (rm.sub_id = w.danhaobind  and w.wutype= 9)
        left join office.dbo.evaluate e with(nolock) on (e.subid = rm.sub_id and e.EvaluateType = 9)
        left join dbo.RecoverSubAddress addr with(nolock)  on addr.sub_id = rm.sub_id
        left join recover_sub ss with(nolock) on ss.sub_ido = rm.sub_id AND ss.subIdoType = 2
        WHERE 1=1 AND rm.userid = #{userid}
        <![CDATA[ AND DATEDIFF(MONTH,rm.sub_date,GETDATE())<=24]]>
        <if test="orderList == true">
            <![CDATA[ AND (rm.sub_check <> 4 OR DATEDIFF(DAY,rm.sub_date,GETDATE())<=30)]]>
        </if>
        <choose>
            <when test="xtenant == 0">
                and EXISTS(select 1 from dbo.areainfo ar with(nolock) where rm.areaid=ar.id and ar.printName='九机网' AND ar.xtenant =#{xtenant})
            </when>
            <when test="xtenant == 2">
                and EXISTS(select 1 from dbo.areainfo ar with(nolock) where rm.areaid=ar.id and ar.printName='华为授权')
            </when>
            <otherwise>
                and EXISTS(select 1 from dbo.areainfo ar with(nolock) where rm.areaid=ar.id and ar.xtenant=#{xtenant})
            </otherwise>
        </choose>
        and not exists (select 1 from dbo.sub_delCollect c with(nolock) where rm.sub_id = c.sub_id and c.subType=#{subType})
    </select>
    <select id="subLPProList" resultMap="SubLPProMapper">
        SELECT k.imei,k.mkc_check,ISNULL(p2.product_name,p.product_name) product_name,ISNULL(p2.product_color,p.product_color) product_color, rms.*,k.id AS mkcid,k.areaid AS kcaeraid,rm.areaid,
        concat('http://img2.ch999img.com/pic/product/440x440/',p.bpic) AS imageUrl,rm.delivery
        FROM dbo.recover_marketSubInfo rms with(nolock)
        LEFT JOIN recover_mkc k with(nolock) ON rms.basket_id = k.to_basket_id
        LEFT JOIN dbo.recover_marketInfo rm with(nolock) ON rm.sub_id = rms.sub_id
        LEFT JOIN dbo.productinfo p with(nolock) ON p.ppriceid = rms.ppriceid
        LEFT join productinfo p2 WITH(NOLOCK) on k.ppriceid = p2.ppriceid
        WHERE ISNULL(rms.isdel,0) = 0 AND rms.giftid is NULL
        AND rms.sub_id in
        <foreach item="subIds" collection="list" separator="," open="(" close=")" index="">
            #{subIds}
        </foreach>
        UNION ALL
        SELECT k.imei,k.mkc_check,ISNULL(p2.product_name,p.product_name) product_name,ISNULL(p2.product_color,p.product_color) product_color, rms.*,k.id AS mkcid,k.areaid AS kcaeraid,rm.areaid,
        concat('http://img2.ch999img.com/pic/product/440x440/',p.bpic) AS imageUrl,rm.delivery
        FROM dbo.recover_marketSubInfo rms with(nolock)
        LEFT JOIN recover_mkc k with(nolock) ON rms.basket_id = k.to_basket_id
        LEFT JOIN dbo.recover_marketInfo rm with(nolock) ON rm.sub_id = rms.sub_id
        LEFT JOIN dbo.productinfo p with(nolock) ON p.ppriceid = rms.ppriceid
        LEFT join productinfo p2 WITH(NOLOCK) on k.ppriceid = p2.ppriceid
        WHERE rm.sub_check = 4 AND rms.giftid is NULL AND rms.sub_id in
        <foreach item="subIds" collection="list" separator="," open="(" close=")" index="">
            #{subIds}
        </foreach>
    </select>
    <select id="xiaoJianSubList" resultMap="XiaoJianMapper">
        SELECT s.id,Kind,Problem,Outward,Stats,IsBaoxiu,Feiyong,Costprice,YuyueId,s.Mobile,UserName,InDate,
        Buydate,sub_id,problem as troubleDesc,(case when e.id is null then 0 else 1 end) as ispj
        FROM dbo.Smallpro s with(nolock)
        left join office.dbo.evaluate e with(nolock) on (e.subid = s.id and e.EvaluateType IN (13,14))
        WHERE s.userid = #{userid}
        <![CDATA[ AND DATEDIFF(MONTH,s.Indate,GETDATE()) <= 24 ]]>
        and not exists (select 1 from dbo.sub_delCollect c with(nolock) where s.id = c.sub_id and c.subType=5)
        and ISNULL(s.isdel,0)=0  AND s.Stats!=2
        <if test="orderList == true">
            and ISNULL(s.isdel,0)=0  AND s.Stats!=2
        </if>
        <if test="xtenant == 0">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock) where s.areaid=ar.id and ar.printName='九机网' AND ar.xtenant=0)
        </if>
        <if test="xtenant != 0">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock) where s.areaid=ar.id and ar.xtenant=#{xtenant})
        </if>
    </select>

    <select id="xiaoJianProductList" resultMap="XiaoJianProMapper">
        SELECT sb.smallproID, p.ppriceid,p.product_name,p.product_color,concat('http://img2.ch999img.com/pic/product/440x440/',p.bpic) AS imageUrl,sb.count,b.price,b.basket_id,p.cid
        FROM dbo.SmallproBill sb with(nolock) INNER JOIN dbo.Smallpro s with(nolock) ON s.id=sb.smallproID LEFT JOIN dbo.productinfo p with(nolock) ON sb.ppriceid=p.ppriceid
        LEFT JOIN dbo.basket b with(nolock) ON b.basket_id=sb.basket_id
        WHERE s.userid=#{userid}
        and not exists (select 1 from dbo.sub_delCollect c with(nolock) where s.id = c.sub_id and c.subType=5)
        and ISNULL(s.isdel,0)=0  AND s.Stats!=2
        <if test="orderList == true">
            and ISNULL(s.isdel,0)=0  AND s.Stats!=2
        </if>
        <if test="xtenant == 0">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock) where s.areaid=ar.id and ar.printName='九机网' AND ar.xtenant=0)
        </if>
        <if test="xtenant != 0">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock) where s.areaid=ar.id and ar.xtenant=#{xtenant})
        </if>
    </select>

    <select id="shouhouSubList" resultMap="ShouHouSubMapper">
        SELECT  (case when e.id is null then 0 else 1 end) as ispj,s.shouyinglock,isnull(isquji,0) isquji,s.offtime,s.modidate,s.id AS sub_id,s.imei
             ,s.stats,s.ppriceid,s.name AS product_name,s.product_color,s.feiyong,s.userid,concat('http://img2.ch999img.com/pic/product/440x440/',p.bpic) AS imageUrl,sy.yuyuePPids,sy.kind,sy.stype,s.areaid,s.problem
        FROM shouhou s with(nolock)
        LEFT JOIN dbo.shouhou_yuyue sy WITH(NOLOCK) ON s.yuyueid = sy.id
            LEFT JOIN dbo.productinfo p with(nolock) ON p.ppriceid = s.ppriceid
            left join office.dbo.evaluate e with(nolock) on (e.subid = s.id and e.EvaluateType = 3)
        WHERE 1=1 and ISNULL(s.xianshi,0) = 1 and isnull(s.issoft,0) = 0
          AND s.userid = #{userId}
        <![CDATA[ AND DATEDIFF(MONTH,s.modidate,GETDATE())<=24 ]]>
        ORDER BY s.id desc
    </select>

    <select id="shouHouYuYueList" resultMap="ShouHouYuYueMapper">
        select sy.dtime,sy.id AS sub_id,CASE WHEN sy.isdel = 1 and <![CDATA[ sy.stats <> 5 ]]> THEN 10 ELSE sy.stats END stats,CASE WHEN sy.ppriceid > 0 THEN sy.ppriceid ELSE sh.ppriceid END ppriceid,ISNULL(ISNULL(p.product_name,p2.product_name),sy.name) product_name,ISNULL(ISNULL(p.product_color,p2.product_color),sy.color) product_color,concat('http://img2.ch999img.com/pic/product/440x440/',ISNULL(p.bpic,p2.bpic)) AS imageUrl,
        sy.kind,sy.stype,sy.areaid,sh.problem,sy.yuyuePPids,s.issoft
        FROM shouhou_yuyue sy with(nolock)
        LEFT JOIN shouhou sh WITH(nolock) ON sh.id = sy.shouhou_id
        LEFT JOIN dbo.productinfo p with(nolock) ON p.ppriceid = sy.ppriceid
        LEFT JOIN dbo.productinfo p2 with(nolock) ON p2.ppriceid = sh.ppriceid
        WHERE 1=1
        AND ISNULL(sy.isdel,0) =0  and sy.stats IN(0,1,2,4,6,7)
        <if test="orderList == true">
            and sy.stats IN(0,1,2,4,6,7,3)
        </if>
        <if test="xtenant == 0">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock) where sy.areaid=ar.id and ar.printName='九机网' AND ar.xtenant = #{xtenant})
        </if>
        <if test="xtenant == 2">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock) where sy.areaid=ar.id and ar.printName='华为授权')
        </if>
        <if test="xtenant != 0 and xtenant != 2">
            and EXISTS(select 1 from dbo.areainfo ar with(nolock) where sy.areaid=ar.id and ar.xtenant=#{xtenant})
        </if>
        and not exists (select 1 from dbo.sub_delCollect c with(nolock) where sy.id = c.sub_id and c.subType=3 )
        and sy.userid = #{userid}
        <![CDATA[AND DATEDIFF(MONTH,sy.dtime,GETDATE())<=24 ]]>
        ORDER BY sy.id desc
    </select>

    <select id="shouhouSubBySoftList" resultMap="ShouHouSubMapper">
        SELECT  (case when e.id is null then 0 else 1 end) as ispj,s.shouyinglock,isnull(isquji,0) isquji,s.offtime,s.modidate,s.id AS sub_id,s.imei
        ,s.stats,s.ppriceid,s.name AS product_name,s.product_color,s.feiyong,s.userid,concat('http://img2.ch999img.com/pic/product/440x440/',p.bpic) AS imageUrl,sy.yuyuePPids,sy.kind,sy.stype,s.areaid,s.problem,s.issoft
        FROM shouhou s with(nolock)
        LEFT JOIN dbo.shouhou_yuyue sy WITH(NOLOCK) ON s.yuyueid = sy.id
        LEFT JOIN dbo.productinfo p with(nolock) ON p.ppriceid = s.ppriceid
        left join office.dbo.evaluate e with(nolock) on (e.subid = s.id and e.EvaluateType = 3)
        WHERE 1=1 and ISNULL(s.xianshi,0) = 1 and isnull(s.issoft,0) = 1
        AND s.userid = #{userId}
        <![CDATA[ AND DATEDIFF(MONTH,s.modidate,GETDATE())<=24 ]]>
        ORDER BY s.id desc
    </select>
    <select id="listBasketTimeCount" resultType="com.jiuji.oa.oacore.weborder.res.BasketTimeCountRes">
        select  <choose>
                    <when test="queryType == 2">p.productid productId,</when>
                    <otherwise>b.ppriceid ppid,</otherwise>
                </choose>
                SUM(b.basket_count) basketCount
        from productinfo p with(nolock)
        <!--用左连接,是为了查询的商品要返回个数量,没有的时候返回0-->
        left join sub s with(nolock) on s.userid = #{userId} and s.sub_check not in (4,8,9)
            and EXISTS(select 1 from dbo.areainfo ai with(nolock) where s.areaid=ai.id and ai.xtenant=#{xtenant})
        left join basket b with(nolock) on b.sub_id = s.sub_id and b.ppriceid = p.ppriceid and isnull(b.isdel,0)=0
        where
            <foreach collection="basketReqList" item="basketReq" open="(" separator="or" close=")">
                (
                <choose>
                    <when test="queryType == 2">p.productid = #{basketReq.productId}</when>
                    <otherwise>b.ppriceid = #{basketReq.ppid}</otherwise>
                </choose>

                and s.sub_date between #{basketReq.startTime} and #{basketReq.endTime}
                )
            </foreach>
        group by
        <choose>
            <when test="queryType == 2">p.productid</when>
            <otherwise>b.ppriceid</otherwise>
        </choose>

    </select>
</mapper>
