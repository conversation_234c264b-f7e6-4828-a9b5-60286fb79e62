<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.RecoverBasketMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.RecoverBasket">
        <id column="id" property="id"/>
        <result column="sub_id" property="subId"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="costPrice" property="costPrice"/>
        <result column="price" property="price"/>
        <result column="comment" property="comment"/>
        <result column="count" property="count"/>
        <result column="imei" property="imei"/>
        <result column="isdel" property="isdel"/>
        <result column="check_" property="check"/>
        <result column="intime" property="intime"/>
        <result column="inuser" property="inuser"/>
        <result column="checkUser" property="checkUser"/>
        <result column="takeUser" property="takeUser"/>
        <result column="price1" property="price1"/>
        <result column="lockInfo" property="lockInfo"/>
        <result column="mkcid" property="mkcid"/>
        <result column="checkprice" property="checkprice"/>
        <result column="checkTime" property="checkTime"/>
        <result column="addCodePrice" property="addCodePrice"/>
        <result column="iszuji" property="iszuji"/>
        <result column="ismobile" property="ismobile"/>
        <result column="isSalfFlag" property="isSalfFlag"/>
        <result column="eveResultId" property="eveResultId"/>
        <result column="evaMd5" property="evaMd5"/>
        <result column="goodsId" property="goodsId"/>
        <result column="checkResultId" property="checkResultId"/>
        <result column="checkResultMd5" property="checkResultMd5"/>
    </resultMap>

    <select id="getRecoverRecords" resultType="com.jiuji.oa.oacore.oaorder.res.RecoverRecordRes">
        SELECT * INTO #pinfo FROM productinfo p WITH (NOLOCK) WHERE p.product_id IN
        <foreach collection="pids" index="index" item="pid" open="(" close=")" separator=",">
            #{pid}
        </foreach>

        SELECT p.product_name as productName,p.product_color as productColor,b.price,s.pay_time as payTime,s.sub_tel as
        subTel,u.UserName as userName,s.userid as userId
        FROM recover_basket b with(nolock)
        inner JOIN recover_sub s with(nolock) ON b.sub_id = s.sub_id
        inner JOIN dbo.areainfo a WITH(NOLOCK) ON a.id = s.areaid
        LEFT JOIN dbo.BBSXP_Users u with(nolock) ON u.ID=s.userid
        inner JOIN #pinfo p with(nolock) ON b.ppriceid = p.ppriceid1
        WHERE s.sub_check = 3 AND ISNULL(b.isdel,0) = 0 AND b.price > 0
        <choose>
            <when test="xtenant != null">
                AND a.xtenant = #{xtenant}
            </when>
            <otherwise>
                AND a.xtenant = 0
            </otherwise>
        </choose>
        ORDER BY b.id DESC
        OFFSET 0 ROWS FETCH NEXT #{queryCount} ROWS ONLY

        <!-- 删除临时表-->
        DROP TABLE #pinfo
    </select>

    <select id="getRecoverPrice" resultType="com.jiuji.oa.oacore.oaorder.res.RecoverPriceRes">
        SELECT AVG(rb.price) avgPrice,MAX(rb.price) maxPrice,MIN(rb.price) minPrice
        FROM dbo.recover_mkc k WITH(NOLOCK)
        INNER JOIN dbo.recover_basket b WITH(NOLOCK) ON b.id = k.from_basket_id
        INNER JOIN dbo.recover_marketSubInfo rb WITH(NOLOCK) ON rb.basket_id = k.to_basket_id
        WHERE k.mkc_check = 5 AND ISNULL(rb.isdel,0) = 0 AND b.price > 0
        <if test="md5Code != null and md5Code != ''">
            AND ISNULL(b.checkResultMd5,b.evaMd5) = #{md5Code}
        </if>
    </select>

    <select id="getRecoverProduct" resultType="com.jiuji.oa.oacore.oaorder.bo.BasketProductBO">
        SELECT
        t. ppid,
        t.productName,
        t.productColor,
        t.subId,
        t.eveResultId,
        t.tradeWay,
        t.price,
        t.orderTime
        FROM
        (
        SELECT
        b.ppriceid as ppid,
        p.product_name as productName,
        p.product_color as productColor,
        b.sub_id as subId,
        b.eveResultId,
        b.price,
        s.sub_delivery as tradeWay,
        isnull(s.pay_time,s.ruku_time) as orderTime,
        ROW_NUMBER () OVER ( PARTITION BY b.sub_id ORDER BY b.price DESC, b.id ) rn
        FROM
        recover_basket b with(nolock)
        LEFT JOIN productinfo p with(nolock) ON b.ppriceid = p.ppriceid
        LEFT JOIN recover_sub s with(nolock) ON s.sub_id = b.sub_id
        WHERE 1=1
        <if test="subIds != null and subIds.size>0">
            and b.sub_id IN
            <foreach collection="subIds" index="index" item="subId" open="(" separator="," close=")">
                #{subId}
            </foreach>
        </if>
        AND ISNULL( b.isdel, 0 ) = 0
        ) t
        WHERE
        t.rn = 1
    </select>
    <select id="getBasketDta" resultType="com.jiuji.oa.oacore.weborder.res.RecoverBasketRes">
        SELECT
            b.id,
            b.comment,
            b.costprice,
            b.count,
            b.ppriceid,
            b.price productPrice,
            b.imei imei,
            b.isdel isdel,
            b.check_ check_,
            b.sub_id subId,
            b.ismobile,
            p.product_name productName,
            p.product_color productColor
        FROM
            dbo.recover_basket b with(nolock)
            LEFT JOIN dbo.productinfo p with(nolock) ON b.ppriceid= p.ppriceid
        WHERE
        b.sub_id in
        <foreach collection="subIds" index="index" item="subId" open="(" separator="," close=")">
            #{subId}
        </foreach>
            AND isnull( b.isdel, 0 ) = 0
    </select>
    <select id="getBindingRecoverId" resultType="com.jiuji.oa.oacore.weborder.vo.LpBindRes">
        SELECT rmi.sub_id lpSubId,rmi.newSubId newSubId from recover_marketInfo rmi with(nolock) where rmi.newSubId = #{subId}
    </select>

</mapper>
