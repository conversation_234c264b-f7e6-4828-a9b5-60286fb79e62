<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.EvaluateScoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.EvaluateScore">
        <id column="Id" property="Id" />
        <result column="EvaluateId" property="EvaluateId" />
        <result column="RelateCh999Id" property="RelateCh999Id" />
        <result column="Score" property="Score" />
        <result column="score2" property="score2" />
        <result column="Job" property="Job" />
        <result column="sub_id" property="subId" />
        <result column="type_" property="type" />
        <result column="userid" property="userid" />
        <result column="sub_check" property="subCheck" />
        <result column="mobileCount" property="mobileCount" />
        <result column="uPrices" property="uPrices" />
        <result column="udate" property="udate" />
        <result column="dtime" property="dtime" />
        <result column="areaid" property="areaid" />
        <result column="EvaluateTagComment" property="EvaluateTagComment" />
        <result column="EvaluateTagScore" property="EvaluateTagScore" />
        <result column="wuxiao" property="wuxiao" />
        <result column="isAppeal" property="isAppeal" />
        <result column="kpXishu" property="kpXishu" />
        <result column="kpXishuType" property="kpXishuType" />
        <result column="wuxiao2" property="wuxiao2" />
        <result column="appealStatus1" property="appealStatus1" />
        <result column="appealStatus2" property="appealStatus2" />
    </resultMap>
    <update id="updateEvaluateScore">
        update b set b.Score=0,b.wuxiao=0 from dbo.EvaluateScore a
        left join dbo.EvaluateScore b
        on (a.sub_id=b.sub_id and a.type_=b.type_  and a.Id != b.id)
        where a.Score is not null and b.Score is null
        and a.sub_id is not null
        and a.sub_id=#{subId} and a.type_=#{type}
    </update>
    <update id="updateEvaluate" parameterType="com.jiuji.oa.oacore.oaorder.po.EvaluateScore">
        UPDATE dbo.EvaluateScore SET EvaluateId=#{EvaluateId},Score=#{Score},wuxiao=#{wuxiao},kpXishu=#{kpXishu},
        kpXishuType=#{kpXishuType},score2=#{score2},wuxiao2=#{wuxiao2}
        WHERE job=#{job} AND sub_id=#{subId} AND type_= #{type} AND EvaluateId is null
    </update>
    <update id="updateEvaluateByCh999Id">
        UPDATE dbo.EvaluateScore SET EvaluateId=#{EvaluateId},Score=#{Score},wuxiao=#{wuxiao},kpXishu=#{kpXishu},
        kpXishuType=#{kpXishuType},score2=#{score2},wuxiao2=#{wuxiao2}
        WHERE job=#{job} AND sub_id=#{subId} AND type_= #{type} AND EvaluateId is null
        and RelateCh999Id=#{ch999Id}
    </update>
    <delete id="removeEvaluateScore">
        DELETE FROM EvaluateScore WHERE EvaluateId = #{id} AND EXISTS
                    (
	                    SELECT 1 FROM
	                    (
		                    SELECT id,Job,RelateCh999Id,ROW_NUMBER() OVER(PARTITION BY Job,RelateCh999Id ORDER BY Id) rn FROM EvaluateScore es1
		                    WHERE es1.EvaluateId =  #{id}
	                    ) t
	                    WHERE t.rn &gt; 1 AND id = EvaluateScore.id
                    )
    </delete>

    <select id="getEvaluateScoreByTypeAndSubId" resultMap="BaseResultMap">
        SELECT * FROM ${officeName}.dbo.EvaluateScore with(nolock) WHERE type_ = #{type} AND sub_id = #{subId}
    </select>
    <select id="getEvaluateScoreId" resultType="java.lang.Integer">
        select top 1 id from dbo.EvaluateScore with(nolock) where sub_id=#{subId} and type_=#{type} and EvaluateId is null and Score is null
    </select>
    <select id="getRelateCh999Id" resultType="com.jiuji.oa.oacore.oaorder.po.EvaluateScore">
       select RelateCh999Id,Id from dbo.EvaluateScore with(nolock) WHERE job=#{job} AND sub_id=#{subId} AND type_= #{type} AND EvaluateId is null
    </select>
    <select id="getRelateCh999IdById" resultType="com.jiuji.oa.oacore.oaorder.po.EvaluateScore">
        select RelateCh999Id,Id from dbo.EvaluateScore with(nolock)
        WHERE job=#{job} AND sub_id=#{subId} AND type_= #{type} AND EvaluateId is null
        and RelateCh999Id=#{ch999Id}
    </select>
    <select id="evaluateCountQuery" resultType="com.jiuji.oa.oacore.oaorder.bo.EvaluateUserBO">
        select f.*,u.area1id from (
        SELECT * FROM (SELECT t.*,ROW_NUMBER() OVER(PARTITION BY t.areaid ORDER BY haoping DESC) as R FROM (
	    SELECT RelateCh999Id,es.areaid,COUNT(1) AS pingjia,
		SUM(CASE WHEN ((type_=3 AND es.Score>=3) OR (es.Score!=3 AND es.Score>3)) THEN 1 ELSE 0 END) AS haoping,
        SUM(ISNULL(es.Score,0)) AS totalScore
		FROM dbo.EvaluateScore es with(nolock) INNER JOIN dbo.Evaluate e with(nolock) ON es.EvaluateId=e.Id
	    where ISNULL(es.Score,0)>0 AND ISNULL(es.wuxiao,0)=0 AND e.dtime BETWEEN #{date1} AND #{date2} AND ISNULL(e.isInvalid,0) = 0
	    GROUP BY RelateCh999Id,es.areaid) t) g WHERE RelateCh999Id IN
        <foreach collection="ch999Id" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        )f left join dbo.ch999_user u with(nolock) on f.RelateCh999Id = u.ch999_id
    </select>
    <insert id="batchSave">
        insert into EvaluateScore (Job,RelateCh999Id,userid,areaid,type_,dtime,sub_id) values
        <foreach collection="scoreList" item="item" index="index" separator=",">
            (#{item.Job}, #{item.RelateCh999Id}, #{item.userid}, #{item.areaid}, #{item.type}, #{item.dtime}, #{item.subId})
        </foreach>
    </insert>
</mapper>
