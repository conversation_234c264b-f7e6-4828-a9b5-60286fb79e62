<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.RecoverMarketsubinfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.RecoverMarketsubinfo">
        <id column="basket_id" property="basketId" />
        <result column="basket_count" property="basketCount" />
        <result column="basket_date" property="basketDate" />
        <result column="seller" property="seller" />
        <result column="ismobile" property="ismobile" />
        <result column="price" property="price" />
        <result column="sub_id" property="subId" />
        <result column="price1" property="price1" />
        <result column="ppriceid" property="ppriceid" />
        <result column="inprice" property="inprice" />
        <result column="giftid" property="giftid" />
        <result column="type" property="type" />
        <result column="isdel" property="isdel" />
        <result column="ischu" property="ischu" />
        <result column="price2" property="price2" />
        <result column="ishm" property="ishm" />
        <result column="ksRemark" property="ksRemark" />
        <result column="ksfid" property="ksfid" />
        <result column="imei2" property="imei2" />
        <result column="isOnShop" property="isOnShop" />
        <result column="mkc_id2" property="mkcId2" />
        <result column="auction_fee" property="auctionFee" />
    </resultMap>

    <select id="getOnlineSericesUsername" resultType="java.lang.String">
        SELECT TOP 1 b.seller FROM dbo.recover_marketSubInfo b with(nolock)
        WHERE b.sub_id = #{subId} AND ISNULL(b.isdel,0) = 0 ORDER BY b.price DESC
    </select>
</mapper>
