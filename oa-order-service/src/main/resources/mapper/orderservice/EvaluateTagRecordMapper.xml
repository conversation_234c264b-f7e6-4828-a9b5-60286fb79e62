<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.EvaluateTagRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.EvaluateTagRecord">
        <id column="Id" property="Id" />
        <result column="TagId" property="TagId" />
        <result column="EvaluateId" property="EvaluateId" />
        <result column="RelateCh999Id" property="RelateCh999Id" />
        <result column="IsOrderTag" property="IsOrderTag" />
    </resultMap>
    <select id="exists" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM EvaluateTagRecord r WITH(NOLOCK)
        INNER JOIN EvaluateTag t WITH(NOLOCK) ON r.TagId = t.Id
        WHERE r.EvaluateId = #{evaluateId} AND t.TagType = 1 AND r.RelateCh999Id = #{ch999Id}
        AND r.IsOrderTag = 0 AND isnull(t.kind,'') != '专业度'
    </select>

</mapper>
