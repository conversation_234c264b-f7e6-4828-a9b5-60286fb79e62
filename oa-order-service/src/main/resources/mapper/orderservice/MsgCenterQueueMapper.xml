<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.MsgCenterQueueMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.MsgCenterQueue">
        <id column="ID" property="id" />
        <result column="FromUserID" property="fromUserID" />
        <result column="ToUserID" property="toUserID" />
        <result column="QueueType" property="queueType" />
        <result column="EndDate" property="endDate" />
        <result column="IsMsg" property="isMsg" />
        <result column="Status_" property="status" />
        <result column="Version_" property="version" />
        <result column="CreateDate" property="createDate" />
        <result column="AccessedDate" property="accessedDate" />
        <result column="AccessedCount" property="accessedCount" />
        <result column="xtenant" property="xtenant" />
    </resultMap>

    <select id="getToUserID" resultType="java.lang.Integer">
        select top 1 ToUserID
        from dbo.MsgCenterQueue with(nolock)
        where FromUserID = #{fromUserId} and QueueType = 0 and id &lt; #{subId} and isnull(ToUserID, 0) != 0 and Status_ = 2
        order by id desc
    </select>
    <select id="getMsgCenterQueueMapper" resultType="com.jiuji.oa.oacore.oaorder.po.MsgCenterQueue">
        select ToUserID,FromUserID from dbo.MsgCenterQueue with(nolock) where ID=#{subId}
    </select>
</mapper>
