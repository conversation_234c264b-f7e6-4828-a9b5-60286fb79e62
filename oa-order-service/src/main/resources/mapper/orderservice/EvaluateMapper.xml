<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.EvaluateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.oaorder.po.Evaluate">
        <id column="Id" property="Id" />
        <result column="UserId" property="UserId" />
        <result column="Mobile" property="Mobile" />
        <result column="StarLevel" property="StarLevel" />
        <result column="Content" property="Content" />
        <result column="EvaluateType" property="EvaluateType" />
        <result column="EvaluateStatus" property="EvaluateStatus" />
        <result column="AreaId" property="AreaId" />
        <result column="depart_id" property="departId" />
        <result column="dtime" property="dtime" />
        <result column="SubId" property="SubId" />
        <result column="Process" property="Process" />
        <result column="EvaluateClass" property="EvaluateClass" />
        <result column="EvaluateTag" property="EvaluateTag" />
        <result column="IsAnon" property="IsAnon" />
        <result column="AvgStarLevel" property="AvgStarLevel" />
        <result column="CommendedScore" property="CommendedScore" />
        <result column="commendedRemark" property="commendedRemark" />
        <result column="processUser" property="processUser" />
        <result column="SourceFrom" property="SourceFrom" />
        <result column="EvaluateTagComment" property="EvaluateTagComment" />
        <result column="EvaluateTagScore" property="EvaluateTagScore" />
        <result column="dianzhang" property="dianzhang" />
        <result column="huanyuantime" property="huanyuantime" />
        <result column="huanyuantimeout" property="huanyuantimeout" />
        <result column="isInvalid" property="isInvalid" />
        <result column="isxinsheng" property="isxinsheng" />
        <result column="xinshengrank" property="xinshengrank" />
        <result column="xs_comment_count" property="xsCommentCount" />
        <result column="xs_view_count" property="xsViewCount" />
        <result column="xs_praise_count" property="xsPraiseCount" />
        <result column="isShow" property="isShow" />
        <result column="versionNum" property="versionNum" />
        <result column="isGuide" property="isGuide" />
    </resultMap>

    <resultMap id="EvaluateListVO" type="com.jiuji.oa.oacore.oaorder.res.EvaluateListVO">
        <id column="Id" property="id" />
        <result column="StarLevel" property="starLevel" />
        <result column="Content" property="content" />
        <result column="EvaluateType" property="evaluateType" />
        <result column="EvaluateStatus" property="evaluateStatus" />
        <result column="AreaId" property="areaId" />
        <result column="dtime" property="dTime" />
        <result column="SubId" property="subId" />
        <result column="Process" property="process" />
        <result column="EvaluateTag" property="evaluateTag" />
        <result column="IsAnon" property="isAnon" />
        <result column="AvgStarLevel" property="avgStarLevel" />
        <result column="CommendedScore" property="commendedScore" />
        <result column="SourceFrom" property="sourceFrom" />
        <result column="dianzhang" property="dianzhang" />
        <result column="isGuide" property="isGuide" />
        <result column="userclass" property="userClass" />
        <result column="depart_id" property="departId" />
        <result column="mobile" property="mobile" />
        <result column="AreaScore" property="areaScore" />
        <result column="AreaRemark" property="areaRemark" />
        <result column="is_illegal" property="isIllegal" />
        <result column="isShow" property="isShow" />
        <result column="standby_id" property="standbyId"/>
        <result column="standby_score" property="standbyScore"/>
        <result column="favourableText" property="favourableText"/>
        <result column="UserId" property="userId"/>
    </resultMap>

    <sql id="evaluateQuery">

    </sql>
    <update id="updateEvaluate">
        update dbo.Evaluate set Process = ISNULL(Process,'') + @Process where Id = @id
    </update>


    <select id="geteEaluateTotal" resultType="java.lang.Integer">
        SELECT
        count(t1.Id)
        from ( select e.Id,
        e.dtime,
        e.commendedRemark,
        e.AreaId as areaId,
        e.UserId as userId,
        e.SubId as subId FROM
        EvaluateScore es WITH (NOLOCK)
        INNER JOIN Evaluate e WITH (NOLOCK) ON es.EvaluateId = e.Id
        WHERE 1=1
        <if test="type != null and type != 0">
            and   e.EvaluateType = #{type}
        </if>
        AND es.Score >= 4
        AND LEN( e.commendedRemark ) >= 10
        GROUP BY
        e.Id,
        e.dtime,
        e.commendedRemark,
        e.AreaId,
        e.UserId,
        e.SubId
        )t1
    </select>

    <select id="getEvaluatePages" resultType="com.jiuji.oa.oacore.oaorder.res.EvaluateInfoRes">
        SELECT
        e.Id,
        e.dtime,
        -- e.commendedRemark,
        <!--取值换成员工评价，但是为了少量改动代码，返回字段就不改 -->
        e.Content as commendedRemark,
        MAX (es.Score) score,
        e.AreaId as areaId,
        e.UserId as userId,
        e.SubId as subId
        FROM
        EvaluateScore es WITH (NOLOCK)
        INNER JOIN Evaluate e WITH (NOLOCK) ON es.EvaluateId = e.Id
        left join areainfo a WITH (NOLOCK) on a.id=es.areaid
        WHERE 1=1
        <if test="type != null and type != 0">
        <choose>
            <when test="type == 3 and isJiuji == true ">
                AND ( e.EvaluateType in (-1) or (e.EvaluateType = 3 AND e.standby_id is null) )
            </when>
            <when test="type == 3 and isJiuji == false ">
                AND (e.EvaluateType = 3 AND e.standby_id is null)
            </when>
            <otherwise>
                and e.EvaluateType = #{type}
            </otherwise>
        </choose>

        </if>
        <if test="authId != null and authId != 0">
            and a.authorizeid = #{authId}
        </if>
        AND es.Score >= 4
        -- AND LEN( e.commendedRemark ) >= 10
        AND LEN( e.Content ) >= 10
        and e.isShow=1
        -- 九机查询近三月的数据
        <if test="isJiuji == true">
            AND e.dtime <![CDATA[>=]]> DATEADD(MONTH, -3, GETDATE())
        </if>
        GROUP BY
        e.Id,
        e.dtime,
        e.Content,
        e.AreaId,
        e.UserId,
        e.SubId
        ORDER BY
        MAX (es.Score) DESC,
        e.dtime DESC
        <if test="isJiuji == false">
        OFFSET #{startRow} ROWS
        FETCH NEXT #{size} ROWS ONLY
        </if>
    </select>

    <select id="getEvaluateBySubIdAndType" resultType="com.jiuji.oa.oacore.oaorder.po.Evaluate">
        SELECT top 1 id,content,commendedRemark,CommendedScore,ISNULL(versionNum,2) versionNum FROM dbo.Evaluate WITH(NOLOCK)
        WHERE SubId = #{subId}
        <if test="evaluateType != null">
            and EvaluateType = #{evaluateType}
        </if>
    </select>

    <select id="getEvaluateBySubIdAndTypes" resultType="com.jiuji.oa.oacore.oaorder.po.Evaluate">
        SELECT top 1 id,content,commendedRemark,CommendedScore,ISNULL(versionNum,2) versionNum FROM dbo.Evaluate WITH(NOLOCK)
        WHERE SubId = #{subId}
        <if test="evaluateTypes != null and evaluateTypes.size>0">
            and EvaluateType in
            <foreach collection="evaluateTypes" index="index" item="evaluateType" open="(" separator="," close=")">
                #{evaluateType}
            </foreach>
        </if>
    </select>

    <select id="getCallService" resultType="java.util.Map">
        select r.scored,e.id,r.userid,e.content,e.commendedRemark,ISNULL(e.versionNum,2) versionNum
        from CallCenterServiceEvalRecord r WITH(NOLOCK) LEFT JOIN Evaluate e WITH(NOLOCK) ON r.id=e.SubId
        AND e.EvaluateType = #{evaluateType} WHERE r.id = #{subId}
    </select>

    <select id="getAllTagsInfo" resultType="com.jiuji.oa.oacore.oaorder.res.TagsInfoBO">
        SELECT etr.RelateCh999Id as relateCh999Id, etr.tagid as tagId, et.Name as name,et.job,et.kind FROM EvaluateTagRecord etr with(nolock)
        LEFT JOIN EvaluateTag et with(nolock) ON et.id = etr.TagId
        WHERE EvaluateId = #{evaluateId} AND ISNULL(etr.IsOrderTag,0) = 0
    </select>

    <select id="getPjQuestions" resultType="com.jiuji.oa.oacore.oaorder.res.PjQuestionBO">
        SELECT q.id,q.content as question,a.answer,a.ch999id FROM EvaluateQuestionAnswer a with(nolock) INNER JOIN EvaluateQuestion q with(nolock) ON
        a.questionid=q.id WHERE a.evaluateid = #{evaluateId} ORDER BY a.id ASC
    </select>

    <select id="getQuestionUsers" resultType="java.util.Map">
        SELECT kind,ch999id FROM ${officeName}.dbo.EvaluateQuestionUser a with(nolock)
        WHERE [enable]=1 AND DATEDIFF(DAY, dtime,GETDATE()) &lt;= 30 AND kind > 0
        <if test="ch999Ids != null and ch999Ids.size>0">
            and ch999id in
            <foreach collection="ch999Ids" index="index" item="ch999Id" open="(" separator="," close=")">
                #{ch999Id}
            </foreach>
        </if>
    </select>

    <select id="getRecommendationWebsiteTagsByOrder" resultType="com.jiuji.oa.oacore.oaorder.res.PjTagsBO">
        SELECT etr.tagid as tagId, et.Name as tagName
        FROM EvaluateTagRecord etr with(nolock)
        LEFT JOIN EvaluateTag et with(nolock) ON
        et.id = etr.TagId WHERE
        EvaluateId =(select top 1 id from ${officeName}.dbo.evaluate with(nolock) where
        evaluate.subid = #{subId}
        and evaluate.EvalUateType = #{evaluateType})
        AND ISNULL(etr.IsOrderTag, 0)= 1
    </select>
    <select id="isExistV" resultType="java.lang.Integer">
        SELECT count(*) FROM dbo.sub s WITH(NOLOCK) WHERE s.sub_id = #{subId} AND DATEDIFF(day,s.tradedate1,GETDATE()) &gt;= 15
    </select>
    <select id="existsV" resultType="java.lang.Integer">
         SELECT 1 FROM dbo.ch999_user u WITH(NOLOCK)
                        WHERE u.iszaizhi = 1 AND EXISTS
                        (
	                        SELECT 1 FROM dbo.sub s WITH(NOLOCK) WHERE s.sub_id = #{subId} AND s.sub_mobile = u.mobile
                        )
    </select>
    <select id="existsS" resultType="java.lang.Integer">
         SELECT 1 FROM dbo.ch999_user u WITH(NOLOCK)
                        WHERE u.iszaizhi = 1 AND EXISTS
                        (
                            SELECT 1 FROM dbo.shouhou sh WITH(NOLOCK)
                            INNER JOIN dbo.BBSXP_Users u1 WITH(NOLOCK) ON sh.userid = u1.ID
                            WHERE sh.id = #{subId}  AND (u1.mobile = u.mobile OR sh.mobile = u.mobile)
                        )
    </select>
    <select id="isExistL" resultType="java.lang.Integer">
        SELECT 1 FROM recover_marketInfo WITH(NOLOCK) WHERE sub_id = #{subId} AND DATEDIFF(day,tradedate1,GETDATE()) &gt;= 15
    </select>
    <select id="isExistsL" resultType="java.lang.Integer">
        SELECT 1 FROM recover_marketInfo WITH(NOLOCK) WHERE sub_id = #{subId}  AND DATEDIFF(MINUTE,tradeDate1,GETDATE()) &lt; 30
    </select>
    <select id="existsL" resultType="java.lang.Integer">
         SELECT 1 FROM dbo.ch999_user u WITH(NOLOCK)
                        WHERE u.iszaizhi = 1 AND EXISTS
                        (
	                        SELECT 1 FROM dbo.recover_marketInfo r WITH(NOLOCK)
	                        INNER JOIN dbo.BBSXP_Users u1 WITH(NOLOCK) ON r.userid = u1.ID
	                        WHERE r.sub_id = #{subId}  AND (r.sub_mobile = u.mobile OR u1.mobile = u.mobile)
                        )
    </select>
    <select id="isExistsR" resultType="java.lang.Integer">
        SELECT 1 FROM dbo.msoft m WITH(NOLOCK) INNER JOIN dbo.BBSXP_Users u WITH(NOLOCK) ON u.ID = m.userid INNER JOIN
        dbo.ch999_user cu WITH(NOLOCK) ON cu.mobile = u.mobile WHERE m.id = @SubId AND cu.iszaizhi = 1 AND ISNULL(u.mobile,'') != ''
    </select>
    <select id="isExistKX" resultType="java.lang.Integer">
        SELECT 1 FROM dbo.Smallpro WITH(NOLOCK) WHERE id = #{subId} AND DATEDIFF(day,qujiandate,GETDATE()) &gt;= 15
    </select>
    <select id="isExistsKX" resultType="java.lang.Integer">
        SELECT 1 FROM dbo.ch999_user u WITH(NOLOCK)
                        WHERE u.iszaizhi = 1 AND EXISTS
                        (
	                        SELECT 1 FROM dbo.Smallpro s WITH(NOLOCK)
	                        INNER JOIN dbo.BBSXP_Users u1 WITH(NOLOCK) ON s.userid = u1.ID
	                        WHERE s.id = #{subId}  AND (s.Mobile = u.mobile OR u1.mobile = u.mobile)
                        )
    </select>
    <select id="isExistH" resultType="java.lang.Integer">
        SELECT 1 FROM dbo.recover_sub WITH(NOLOCK) WHERE sub_id = #{subId} AND DATEDIFF(day,pay_time,GETDATE()) &gt;= 15
    </select>
    <select id="isExistsH" resultType="java.lang.Integer">
        SELECT 1 FROM dbo.recover_sub WITH(NOLOCK) WHERE sub_id = #{subId} AND (DATEDIFF(MINUTE,pay_time,GETDATE()) &lt; 30 OR sub_check != 3)
    </select>
    <select id="existsH" resultType="java.lang.Integer">
         SELECT 1 FROM dbo.ch999_user u WITH(NOLOCK)
                        WHERE u.iszaizhi = 1 AND EXISTS
                        (
	                        SELECT 1 FROM dbo.recover_sub s WITH(NOLOCK)
	                        INNER JOIN dbo.BBSXP_Users u1 WITH(NOLOCK) ON s.userid = u1.ID
	                        WHERE s.sub_id = #{subId} AND u1.mobile = u.mobile
                        )
    </select>
    <select id="existsZ1" resultType="java.lang.Integer">
        SELECT 1 FROM dbo.BBSXP_Users u WITH(NOLOCK)
        INNER JOIN dbo.ch999_user cu WITH(NOLOCK) ON u.mobile = cu.mobile
        WHERE cu.iszaizhi = 1 AND u.ID = #{userId} AND ISNULL(u.mobile,'') != ''
    </select>
    <select id="getEvaluateData" resultType="com.jiuji.oa.oacore.oaorder.po.Evaluate">
        SELECT top 1 Id,CommendedScore FROM dbo.Evaluate WITH(NOLOCK) WHERE SubId = #{subId} and EvaluateType = #{evaluateType}
    </select>
    <select id="getEvaluateTags" resultType="com.jiuji.oa.oacore.oaorder.po.EvaluateTag">
        SELECT Id, TagType, RelateKpi,KpiVolatility,Job FROM dbo.EvaluateTag WITH(NOLOCK) WHERE Id in
        <foreach collection="tagIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getId" resultType="java.lang.Integer">
        SELECT Id FROM dbo.EvaluateScore WITH(NOLOCK) WHERE job=#{jobId} AND sub_id=#{subId} AND type_ in (1,2) AND EvaluateId is null
    </select>
    <select id="getID" resultType="java.lang.Integer">
        SELECT Id FROM dbo.EvaluateScore WITH(NOLOCK) WHERE jjob=#{jobId} AND sub_id=#{subId} AND type_= #{type} AND
        EvaluateId is null and RelateCh999Id=#{ch999Id}
    </select>
    <select id="getSingle" resultType="java.lang.Integer">
        select top 1 e.Id from Evaluate e with(nolock) where
        e.Id=#{evaluateId} and exists(select 1 from tousu t with(nolock) where e.UserId=t.userid)
    </select>
    <select id="getUObj" resultType="java.lang.Integer">
        select top 1 e.Id from Evaluate e with(nolock) where e.Id=#{evaluateId}
        and exists(select 1 from dbo.BBSXP_Users u with(nolock) where e.UserId=u.ID and u.userclass in (5,6))
    </select>
    <select id="getNObj" resultType="java.lang.Integer">
        select top 1 b.basket_id from dbo.basket b with(nolock) where b.sub_id=#{subId} and isnull(b.isdel,0)=0 and isnull(b.type,0)=86
    </select>

    <select id="getEvaluateStatisticsList" resultMap="EvaluateListVO">
        SELECT
        e.Id,
        e.AreaId,
        e.dianzhang,
        e.SubId,
        e.dtime,
        e.Content,
        e.depart_id,
        e.EvaluateStatus,
        e.EvaluateType,
        e.CommendedScore,
        e.StarLevel,
        e.Process,
        e.IsAnon,
        e.EvaluateTag,
        e.AvgStarLevel,
        CASE e.SourceFrom
        WHEN 2 THEN 10
        WHEN 3 THEN 10
        ELSE e.SourceFrom
        END as SourceFrom,
        e.isGuide,
        e.AreaScore,
        e.AreaRemark,
        u.userclass,
        e.isSolvingProblem,
        u.mobile,
        e.is_illegal,
        e.isShow
        FROM
        ${officeName}.dbo.Evaluate e WITH(NOLOCK)
        left join areainfo a WITH(NOLOCK) on e.AreaId = a.id
        left join BBSXP_Users u WITH(NOLOCK) on e.UserId = u.ID
        WHERE
        e.id in
        <foreach item="evaluateId" collection="evaluateIds" index="index" open="(" separator="," close=")">
            #{evaluateId}
        </foreach>
    </select>

    <select id="getEvaluateListTotal" resultType="java.lang.Long">
        select count(1) from(
        <include refid="getEvaluateListMainSql"/>
        ) ttt
    </select>

    <select id="getEvaluateList" resultMap="EvaluateListVO">
        select * from(
        <include refid="getEvaluateListMainSql"/>
        ) ttt
        <if test="req.current != null and req.size != null">
            where rownumber BETWEEN (#{req.current}-1) * #{req.size} +1 and #{req.current}*#{req.size};
        </if>
    </select>

    <sql id="getEvaluateListMainSql">
        SELECT
        row_number ( ) OVER ( ORDER BY dtime DESC ) AS rownumber,
        tt.*
        FROM
        (
        SELECT
        *
        FROM
        (


        SELECT
        ROW_NUMBER() over(PARTITION BY e.id ORDER BY e.id desc) as rn,
        e.Id,
        e.AreaId,
        e.dianzhang,
        e.SubId,
        e.dtime,
        e.Content,
        e.depart_id,
        e.EvaluateStatus,
        CASE WHEN e.EvaluateType = 3 and e.standby_id is not null THEN 22 ELSE e.EvaluateType END as EvaluateType,
        e.CommendedScore,
        e.StarLevel,
        e.Process,
        e.IsAnon,
        (CASE WHEN (ISNULL(e.EvaluateTag, 0) IN (7, 10)) THEN e.EvaluateTag
            WHEN (ISNULL(es.wuxiao, 0) = 1) THEN 6
            ELSE 0
            END) EvaluateTag,
        e.AvgStarLevel,
        CASE e.SourceFrom
        WHEN 2 THEN 10
        WHEN 3 THEN 10
        ELSE e.SourceFrom
        END as SourceFrom,
        e.isGuide,
        e.AreaScore,
        e.AreaRemark,
        u.userclass,
        e.UserId,
        e.isSolvingProblem,
        u.mobile,
        e.is_illegal,
        e.isShow,
        e.standby_id,
        e.standby_score,
        (CASE WHEN ISNULL(e.is_favourable, 0) = 0 THEN '否'
            ELSE '是'
        END) as favourableText,
        (CASE WHEN ISNULL(e.over_five_flg, 0) = 1 THEN '是' ELSE '否' END) as overFiveFlagText,
        (CASE WHEN EXISTS (
            SELECT 1
            FROM ch999_fen cf WITH(NOLOCK)
            WHERE cf.kinds = e.Id AND cf.jifenType = 200 AND ISNULL(cf.fen, 0) > 0 AND cf.fendate IS NOT NULL
        ) THEN '是' ELSE '否'END) as pointsFlagText,
        (CASE WHEN isnull(eS.uPrices,0) > 0 THEN '是' ELSE '否' END) as redPacketFlagText,
        e.processUser
        <if test="req.exportData==true">
            ,edp.depart_id as evaluateDepartId
        </if>
        FROM
        ${officeName}.dbo.Evaluate e WITH(NOLOCK)
        left join areainfo a WITH(NOLOCK) on e.AreaId = a.id
        left join BBSXP_Users u WITH(NOLOCK) on e.UserId = u.ID
        LEFT JOIN ${officeName}.dbo.EvaluateScore es WITH ( NOLOCK ) ON es.EvaluateId = e.Id
        LEFT JOIN ch999_fen f WITH(NOLOCK) on es.Id = f.bumen_id and es.RelateCh999Id = f.ch999_id
        LEFT JOIN ${officeName}.dbo.evaluate_award_record ear WITH ( NOLOCK ) ON ear.evaluate_score_id = es.Id and ear.del_flag = 0
        <if test="req.exportData==true">
            LEFT JOIN ${officeName}.dbo.EvaluateDepart edp WITH(NOLOCK) on e.id=edp.EvaluateId
        </if>
        WHERE
        <!-- 数据来源（null = 九机，1=九讯云，2=九讯云Neo） -->
        ISNULL(e.source,0) = 0
        <if test="req.evaluateDepartIds!=null and req.evaluateDepartIds.size()>0">
            AND EXISTS(SELECT 1 FROM ${officeName}.dbo.EvaluateDepart r WITH(NOLOCK)
            WHERE r.EvaluateId = e.Id and r.depart_id in
            <foreach collection="req.evaluateDepartIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="req.punishFlag != null and req.punishFlag == true">
            AND EXISTS(
            select 1
            from office.dbo.Evaluate ev
            cross apply F_SPLIT(ev.punish_sub_ids, ',') f
            inner join punishSub s WITH (nolock) ON s.sub_id = f.split_value
            inner join dbo.punishSubDetail sp WITH(NOLOCK) on sp.sub_id = s.sub_id
            where ev.id = e.id
            and isnull(sp.is_del,0) = 0
            and sp.stats_ not in (5,8)
            and isnull(sp.fine,0) > 0
            and sp.leve = 1
            and s.type_ = 3
            -- and CHARINDEX(CONCAT(ev.id,''), ',' + s.tousuid + ',' ) > 0
            )
        </if>
        <if test="req.evaluateCateIds!=null and req.evaluateCateIds.size()>0">
            AND EXISTS(SELECT 1 FROM ${officeName}.dbo.EvaluateDepart r WITH(NOLOCK)
            LEFT JOIN ${officeName}.dbo.EvaluateCategoryRelation ecr WITH(NOLOCK) on r.id=ecr.departId
            WHERE r.EvaluateId = e.Id and ecr.cateId in
            <foreach collection="req.evaluateCateIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="(req.areaScores != null and req.areaScores.size() > 0) and !(req.scores != null and req.scores.size() > 0)">
            AND e.AreaScore in
            <foreach item="item" collection="req.areaScores" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--授权隔离 九机的加盟店不需要授权隔离-->
        <choose>
            <when test="req.isJiuji != null and req.isJiuji == true">
                <if test="authorizeId != null ">
                    AND a.authorizeid = (CASE WHEN isnull(a.kind1, 0) != 2 THEN #{authorizeId} ELSE a.authorizeid END)
                </if>
            </when>
            <otherwise>
                <if test="authorizeId != null ">
                    and a.authorizeid = #{authorizeId}
                </if>
            </otherwise>
        </choose>
        <!-- 税务模式 -->
        <if test="req.taxModel != null and req.taxModel == true">
            AND a.taxpayer = 1
            <if test="req.taxCompanyIdList != null and req.taxCompanyIdList.size() > 0">
                AND ISNULL(a.main_company, 0) IN
                <foreach collection="req.taxCompanyIdList" item="taxCompanyId" open="(" close=")" separator=",">
                    #{taxCompanyId}
                </foreach>
            </if>
        </if>
        <!--是否违规-->
        <if test="req.isIllegal != null ">
            AND e.is_illegal = #{req.isIllegal}
        </if>
        <!--是否网站显示-->
        <if test="req.isShow != null ">
            AND e.isShow = #{req.isShow}
        </if>
        <if test="(req.areaScores != null and req.areaScores.size() > 0) and !(req.scores != null and req.scores.size() > 0)">
            AND e.AreaScore in
            <foreach item="item" collection="req.areaScores" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.isInvalid != null ">
            AND e.isInvalid = #{req.isInvalid}
        </if>

        <!--手机端 绩效排行 评价列表
            门店低评：
            列表显示条件：
            单条评价中，所有门店分数存在1-4星，且1-4星未被全部无效。
                若单条评价中门店评价1-4星数据全部被无效则不显示在列表上。
                若单条评价中门店评价均为0星或5星数据则不显示在列表上。
                若单条评价中门店评价1-4星数据已被无效，且剩下的为0星或5星数据则不显示在列表上。-->
        <if test="req.containAllAreaInvalid != null and !req.containAllAreaInvalid">
            and e.areaScore <![CDATA[>]]> 0
            and e.areaScore <![CDATA[<]]> 5
            AND (e.areaAppealStatus is null or e.areaAppealStatus <![CDATA[<>]]> 3)
        </if>

        <!--手机端 绩效排行 评价列表
            员工低评：
            列表显示条件：
            单条评价中，所有员工分数存在1-4星，且1-4星未被全部无效。
             若单条评价中员工评价1-4星数据全部被无效则不显示在列表上。
             若单条评价中员工评价均为0星或5星数据则不显示在列表上。
             若单条评价中员工评价1-4星数据已被无效，且剩下的为0星或5星数据则不显示在列表上。
            (其它涉及wuxiao的筛选条件生效，下列规则就不生效)-->
        <if test="req.containAllEmployeeInvalid != null and !req.containAllEmployeeInvalid and
                     (!req.evaluateTagType.contains(@<EMAIL>)) and
                         !(req.isLowEval != null and req.isLowEval == '1' ) and
                             !req.evaluateTagType.contains(-1) and
                                 req.wuxiao == null">
            AND (es.Score <![CDATA[>]]> 0 and es.Score <![CDATA[<]]> 5 and es.wuxiao = 0)
        </if>

        <if test="req.xtenant != null and req.xtenant != @<EMAIL> ">
            AND a.xtenant = #{req.xtenant}
        </if>
        <if test="req.departIds != null and req.departIds.size() > 0">
            AND e.depart_id in
            <foreach item="departId" collection="req.departIds" index="index" open="(" separator="," close=")">
                #{departId}
            </foreach>
        </if>
        <if test="req.evaluateSourceFromList != null and req.evaluateSourceFromList.size() > 0">
            AND e.SourceFrom in
            <foreach item="evaluateSourceFrom" collection="req.evaluateSourceFromList" index="index" open="(" separator="," close=")">
                #{evaluateSourceFrom}
            </foreach>
        </if>
        <if test="req.areaIds != null and req.areaIds.size() > 0">
            AND e.AreaId in
            <foreach item="areaId" collection="req.areaIds" index="index" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        <if test="req.evaluateStatusList != null and req.evaluateStatusList.size() > 0">
            AND e.EvaluateStatus in
            <foreach item="evaluateStatus" collection="req.evaluateStatusList" index="index" open="(" separator="," close=")">
                #{evaluateStatus}
            </foreach>
        </if>

        <if test="req.evaluateTagType != null and req.evaluateTagType.size > 0">
            AND (
            <foreach item="item" collection="req.evaluateTagType" index="index" open="(" separator=" OR " close=")">
                <choose>
                    <when test="item == -1">
                        (ISNULL(e.isInvalid, 0) = 0 AND ISNULL(es.wuxiao,0) = 0)
                    </when>
                    <when test="item == 0">
                        (ISNULL(e.EvaluateTag, 0) NOT IN (6, 7, 10) AND ISNULL(es.wuxiao,0) = 0)
                    </when>
                    <when test="item == 6">
                        (ISNULL(es.wuxiao, 0) = 1)
                    </when>
                    <otherwise>
                        (ISNULL(e.EvaluateTag, 0) = #{item})
                    </otherwise>
                </choose>
            </foreach>
            )
        </if>

        <!--
        <if test="req.evaluateTagType != null and req.evaluateTagType != -1
             and req.evaluateTagType != @<EMAIL>
             and req.evaluateTagType != @<EMAIL>
             and req.evaluateTagType != @<EMAIL>
             and req.evaluateTagType != -1 and req.evaluateTagType != -2">
            AND e.EvaluateTag = #{req.evaluateTagType}
        </if>
        -->
        <!--有效：非 失效/城市经理审核/申诉中的评价为有效评价-->
        <!--
        <if test="req.evaluateTagType != null and req.evaluateTagType == @<EMAIL>">
            AND (e.EvaluateTag not in (6,7,10) or e.EvaluateTag is null)
        </if>
        -->
        <choose>
            <when test="req.isJiuji != null and req.isJiuji == true">
                <choose>
                    <when test="req.evaluateTypeList != null and req.evaluateTypeList.size > 0">
                        AND ( e.EvaluateType in
                        <foreach item="evaluateType" collection="req.evaluateTypeList" index="index" open="(" separator="," close=")">
                            <choose>
                                <when test="evaluateType != @<EMAIL>
                                  and evaluateType != @<EMAIL>
                                  and evaluateType != @<EMAIL>">
                                    #{evaluateType}
                                </when>
                                <otherwise>
                                    -1
                                </otherwise>
                            </choose>
                        </foreach>
                        <if test="req.evaluateTypeList.contains(@<EMAIL>)">
                            or (e.EvaluateType = 3 AND e.standby_id is not null)
                        </if>
                        <if test="req.evaluateTypeList.contains(@<EMAIL>)">
                            or (e.EvaluateType = 3 AND e.standby_id is null)
                        </if>
                        <if test="req.evaluateTypeList.contains(@<EMAIL>)">
                            or (e.EvaluateType = 6 OR es.Job = 13)
                        </if>
                        )
                    </when>
                    <otherwise>
                        AND e.EvaluateType != 10 AND e.EvaluateType != 11
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                <choose>
                    <when test="req.evaluateType != null">
                        <choose>
                            <when test="req.evaluateType == @<EMAIL>">
                                AND e.EvaluateType = 3 AND e.standby_id is not null
                            </when>
                            <when test="req.evaluateType == @<EMAIL>">
                                AND e.EvaluateType = 3 AND e.standby_id is null
                            </when>
                            <when test="req.evaluateType != @<EMAIL>
                                or req.evaluateType != @<EMAIL>">
                                AND e.EvaluateType = #{req.evaluateType}
                            </when>
                            <otherwise>
                                AND (e.EvaluateType = 6 OR es.Job = 13)
                            </otherwise>
                        </choose>
                    </when>
                    <otherwise>
                        AND e.EvaluateType != 10 AND e.EvaluateType != 11
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
        <if test="req.isGuide != null ">
            <if test="req.isGuide == 0 or req.isGuide == 1">
                AND e.isGuide = #{req.isGuide}
            </if>
            <if test="req.isGuide == 2">
                AND e.isGuide is null
            </if>
        </if>
        <if test="exQuery.exAreaIds != null and exQuery.exAreaIds.size() > 0">
            AND e.AreaId in
            <foreach item="areaId" collection="exQuery.exAreaIds" index="index" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        <if test="req.evaluateTagIds != null">
            AND EXISTS(SELECT 1 FROM ${officeName}.dbo.EvaluateTagRecord r WITH(NOLOCK)
            WHERE r.EvaluateId = e.Id AND r.TagId = #{req.evaluateTagIds})
        </if>
        <if test="req.searchKey != null and exQuery.trimSearchValue != null and exQuery.trimSearchValue.length() > 0">
            <choose>
                <when test="req.searchKey == 1 and exQuery.isIntSearchValue">
                    AND e.SubId = #{exQuery.intSearchValue}
                </when>
                <when test="req.searchKey == 2">
                    AND u.mobile = #{exQuery.trimSearchValue}
                </when>
                <when test="req.searchKey == 3">
                    AND e.Content LIKE CONCAT('%',#{exQuery.trimSearchValue},'%')
                </when>
                <when test="req.searchKey == 4">
                    AND EXISTS(SELECT 1 FROM ${officeName}.dbo.EvaluateTagRecord r WITH(NOLOCK)
                    INNER JOIN ${officeName}.dbo.EvaluateTag t WITH(NOLOCK) ON r.TagId = t.Id
                    WHERE r.EvaluateId = e.Id AND t.Name LIKE CONCAT('%',#{exQuery.trimSearchValue},'%'))
                </when>
                <when test="req.searchKey == 5 and exQuery.isIntSearchValue">
                    AND e.Id = #{exQuery.intSearchValue}
                </when>
                <when test="req.searchKey == 6">
                    <choose>
                        <when test="exQuery.isIntSearchValue">
                            AND EXISTS(SELECT 1 FROM dbo.ch999_user u with(nolock) WHERE u.ch999_id = #{exQuery.intSearchValue} AND u.ch999_name = e.processUser)
                        </when>
                        <otherwise>
                            AND e.processUser like CONCAT('%',#{exQuery.trimSearchValue},'%')
                        </otherwise>
                    </choose>
                </when>
                <when test="req.searchKey == 8 and exQuery.isIntSearchValue">
                    AND e.UserId = #{exQuery.intSearchValue}
                </when>
                <when test="req.searchKey == 9">
                    <if test="exQuery.ppids != null and exQuery.ppids.size() > 0">
                        AND e.EvaluateType IN (1,2) AND EXISTS(
                        SELECT 1 FROM dbo.basket b WITH(NOLOCK)
                        INNER JOIN dbo.sub s WITH(NOLOCK) ON b.sub_id = s.sub_id
                        WHERE s.sub_id = e.SubId AND ISNULL(b.isdel,0) = 0 AND b.ppriceid IN
                        <foreach item="ppid" collection="exQuery.ppids" index="index" open="(" separator="," close=")">
                            #{ppid}
                        </foreach>
                        )
                    </if>
                </when>
                <when test="req.searchKey == 10">
                    <if test="exQuery.ppids != null and exQuery.ppids.size() > 0">
                        AND e.EvaluateType IN (1,2) AND EXISTS(
                        SELECT 1 FROM dbo.basket b WITH(NOLOCK)
                        INNER JOIN dbo.sub s WITH(NOLOCK) ON b.sub_id = s.sub_id
                        INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
                        WHERE s.sub_id = e.SubId AND ISNULL(b.isdel,0) = 0 AND p.productid IN
                        <foreach item="ppid" collection="exQuery.ppids" index="index" open="(" separator="," close=")">
                            #{ppid}
                        </foreach>
                        )
                    </if>
                </when>
                <when test="req.searchKey == 11">
                    AND e.EvaluateType IN (1,2) AND EXISTS(
                    SELECT 1 FROM dbo.basket b WITH(NOLOCK)
                    INNER JOIN dbo.sub s WITH(NOLOCK) ON b.sub_id = s.sub_id
                    INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
                    WHERE s.sub_id = e.SubId AND ISNULL(b.isdel,0) = 0 AND p.product_name LIKE CONCAT('%',#{exQuery.trimSearchValue},'%')
                    )
                </when>
            </choose>
        </if>
        <if test="req.evaluateIdStr != null and req.evaluateIdStr != ''">
            AND e.Id in (${req.evaluateIdStr})
        </if>
        <!-- 员工建议字数 -->
        <if test="req.personSuggestionLength != null and req.personSuggestionLength >= 0">
            AND DATALENGTH(ISNULL(e.Content,'')) <![CDATA[>=]]> #{req.personSuggestionLength}
        </if>
        <!-- 员工建议关键字 -->
        <if test="req.personSuggestion != null and req.personSuggestion != '' ">
            AND e.Content like '%${req.personSuggestion}%'
        </if>
        <!-- 门店建议字数 -->
        <if test="req.areaSuggestionLength != null and req.areaSuggestionLength >= 0">
            AND DATALENGTH(ISNULL(e.AreaRemark,'')) <![CDATA[>=]]> #{req.areaSuggestionLength}
        </if>
        <!-- 门店建议关键字 -->
        <if test="req.areaSuggestion != null and req.areaSuggestion != '' ">
            AND e.AreaRemark like '%${req.areaSuggestion}%'
        </if>
        <if test="req.isXinsheng != null and req.isXinsheng">
            AND e.isxinsheng = 1
        </if>
        <if test="req.commendScores != null and req.commendScores.size() > 0">
            AND e.CommendedScore IN
            <foreach item="commendScore" collection="req.commendScores" index="index" open="(" separator="," close=")">
                #{commendScore}
            </foreach>
        </if>
        <if test="req.userClassList != null and req.userClassList.size() > 0">
            AND u.userclass in
            <foreach item="userClass" collection="req.userClassList" index="index" open="(" separator="," close=")">
                #{userClass}
            </foreach>
        </if>
        <!-- 商品相关 -->
        <if test="(req.cIds != null and req.cIds.size() > 0) or (req.brandIds != null and req.brandIds.size() > 0)">
            AND e.EvaluateType IN (1,2) AND EXISTS(
            SELECT 1 FROM dbo.basket b WITH(NOLOCK)
            INNER JOIN dbo.sub s WITH(NOLOCK) ON b.sub_id = s.sub_id
            INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
            WHERE s.sub_id = e.SubId AND ISNULL(b.isdel,0) = 0
            <if test="req.cIds != null and req.cIds.size() > 0">
                AND p.cid IN
                <foreach item="cid" collection="req.cIds" index="index" open="(" separator="," close=")">
                    #{cid}
                </foreach>
            </if>
            <if test="req.brandIds != null and req.brandIds.size() > 0">
                AND p.brandID IN
                <foreach item="brandId" collection="req.brandIds" index="index" open="(" separator="," close=")">
                    #{brandId}
                </foreach>
            </if>
            )
        </if>

        <!--
        <if test="req.evaluateTagType != null and (
                  req.evaluateTagType != @<EMAIL> and req.evaluateTagType != @<EMAIL>
        )">
            AND e.EvaluateTag = #{req.evaluateTagType}
        </if>
        -->

        <choose>

            <!-- 单独处理下score or areaScore-->
            <when test="(req.scores != null and req.scores.size() > 0) and (req.areaScores != null and req.areaScores.size() > 0) and (req.andOr != null and req.andOr == 1)">
                AND (
                e.AreaScore in
                <foreach item="item" collection="req.areaScores" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR (
                es.Score IN
                <foreach item="score" collection="req.scores" index="index" open="(" separator="," close=")">
                    #{score}
                </foreach>
                )
                )
            </when>

            <!-- 人员评价相关 -->
            <when test="((req.isLowEval != null and req.isLowEval == '1') or (req.evaluateTagType != null and (
                   req.evaluateTagType.contains(@<EMAIL>) or req.evaluateTagType.contains(@<EMAIL>)
                  )) or (req.wuxiao != null)
                 or (req.searchKey != null and req.searchKey == 7 and exQuery.trimSearchValue != null and exQuery.trimSearchValue.length() > 0)
                 or (req.scores != null and req.scores.size() > 0) or (req.professionScores != null and req.professionScores.size() > 0) or (req.job != null and req.job > 0)
                 or (req.evaluateJob != null and req.evaluateJob.size() > 0) or (req.kpXishu != null and req.kpXishu > 0) or (req.staffName != null and req.staffName.length() > 0)
                 or (req.shangbang != null and req.shangbang == 1))
                 and req.containAllAreaInvalid
                ">
                <choose>
                    <when test="(req.scores != null and req.scores.size() > 0) or (req.evaluateJob != null and req.evaluateJob.size() > 0)
                                or (req.searchKey != null and req.searchKey == 7 and exQuery.trimSearchValue != null and exQuery.trimSearchValue.length() > 0)
                                or (req.evaluateTagType != null)">
                        and
                    </when>
                    <otherwise>
                        <!-- 如果不包含员工筛选项，就把只评价门店的数据带出来 -->
                        AND (
                        (es.EvaluateId is null)
                        or (
                    </otherwise>
                </choose>
                (es.EvaluateId is not null
                <if test="req.isLowEval != null and req.isLowEval == '1' ">
                    AND ((isnull(es.wuxiao,0) = 0 AND isnull(es.Score,0) > 0 AND isnull(es.Score,0) <![CDATA[<]]> 5) OR (isnull(es.wuxiao2,0) = 0
                    AND isnull(es.score2,0) <![CDATA[>]]> 0 AND isnull(es.score2,0) <![CDATA[<]]> 5))
                </if>
                <if test="req.wuxiao != null">
                    AND (isnull(es.wuxiao,0) = #{req.wuxiao} or isnull(es.wuxiao2,0) = #{req.wuxiao})
                </if>
                <if test="req.searchKey != null and req.searchKey == 7 and exQuery.trimSearchValue != null and exQuery.trimSearchValue.length() > 0">
                    AND es.RelateCh999Id = #{exQuery.ch999Id}
                </if>

                AND (1 = 1
                <if test="(req.scores != null and req.scores.size() > 0) and (req.areaScores != null and req.areaScores.size() > 0) and (req.andOr != null and req.andOr == 0)">
                    AND es.Score IN
                    <foreach item="score" collection="req.scores" index="index" open="(" separator="," close=")">
                        #{score}
                    </foreach>
                    AND e.AreaScore in
                    <foreach item="item" collection="req.areaScores" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="(req.scores != null and req.scores.size() > 0) and !(req.areaScores != null and req.areaScores.size() > 0)">
                    AND es.Score IN
                    <foreach item="score" collection="req.scores" index="index" open="(" separator="," close=")">
                        #{score}
                    </foreach>
                </if>
                )

                <if test="req.job != null and req.job > 0">
                    AND es.Job = #{req.job}
                </if>
                <if test="req.evaluateJob != null and req.evaluateJob.size() > 0">
                    AND es.Job IN
                    <foreach item="job" collection="req.evaluateJob" index="index" open="(" separator="," close=")">
                        #{job}
                    </foreach>
                </if>
                <if test="req.kpXishu != null and req.kpXishu > 0">
                    AND es.kpXishu = #{req.kpXishu}
                </if>
                <if test="req.staffName != null and req.staffName.length() > 0">
                    AND es.RelateCh999Id = #{exQuery.staffCh999Id}
                </if>
                <if test="req.shangbang != null and req.shangbang == 1">
                    AND isnull(es.xinshengJob,0)=1
                </if>
                )
                <if test="(req.scores == null or req.scores.size() lt 1) and (req.evaluateJob == null or req.evaluateJob.size() lt 1)
                           and (req.searchKey == null or req.searchKey != 7 or exQuery.trimSearchValue == null or exQuery.trimSearchValue.length() lt 1)
                           and (req.evaluateTagType == null) ">
                    ))
                </if>
            </when>

        </choose>

        <!--投诉客评跟进时效统计 字段明细 查询条件-->
        <if test="req.searchFieldType != null">
            <if test="req.searchFieldType == 5">
                AND e.processUser = #{exQuery.trimSearchValue}
            </if>
            <if test="req.searchFieldType == 6">
                AND e.processUser = #{exQuery.trimSearchValue}
                AND e.huanyuantime > e.huanyuantimeout
            </if>
        </if>
        <!-- 是否好评 -->
        <if test="req.favourable != null">
            <choose>
                <when test="req.favourable == true">
                    AND ISNULL(e.is_favourable, 0) = 1
                </when>
                <otherwise>
                    AND ISNULL(e.is_favourable, 0) = 0
                </otherwise>
            </choose>
        </if>
        <!-- 时间范围 -->
        <if test="req.timeType != null">
            <if test="req.timeType == 1">
                <if test="req.startTime != null ">
                    AND es.dtime <![CDATA[>=]]> #{req.startTime}
                </if>
                <if test="req.endTime != null ">
                    AND es.dtime <![CDATA[<=]]> #{req.endTime}
                </if>
            </if>
            <if test="req.timeType == 2">
                <if test="req.startTime != null ">
                    AND e.dtime <![CDATA[>=]]> #{req.startTime}
                </if>
                <if test="req.endTime != null ">
                    AND e.dtime <![CDATA[<=]]> #{req.endTime}
                </if>
            </if>
        </if>
        <if test="req.overFiveFlag != null and req.overFiveFlag == true">
            AND ISNULL(e.over_five_flg, 0) = 1
        </if>
        <if test="req.overFiveFlag != null and req.overFiveFlag == false">
            AND ISNULL(e.over_five_flg, 0) <![CDATA[ <> ]]>  1
        </if>
        <if test="req.pointsFlag != null and req.pointsFlag == true">
            AND exists (
                select 1
                from ch999_fen cf WITH(NOLOCK)
                where cf.kinds = e.Id AND cf.jifenType = 200 AND isnull(cf.fen, 0) > 0 AND cf.fendate IS NOT NULL
            )
        </if>
        <if test="req.pointsFlag != null and req.pointsFlag == false">
            AND not exists (
                select 1
                from ch999_fen cf WITH(NOLOCK)
                where cf.kinds = e.Id AND cf.jifenType = 200 AND isnull(cf.fen, 0) > 0 AND cf.fendate IS NOT NULL
            )
        </if>
        <if test="req.redPacketFlag != null and req.redPacketFlag == true">
            AND isnull(es.uPrices,0) > 0
        </if>
        <if test="req.redPacketFlag != null and req.redPacketFlag == false">
            AND isnull(es.uPrices,0) = 0
        </if>
        <if test="req.awardApplyStatus != null and req.awardApplyStatus.size() > 0">
            AND ( 1!=1
                <if test="req.awardApplyStatus.contains(1)">
                    or (ear.award_type = 1 and ear.approval_status in (1, 3))
                </if>
                <if test="req.awardApplyStatus.contains(2)">
                    or (ear.award_type = 2 and ear.approval_status in (1, 3))
                </if>
            )
        </if>
        <if test="req.moaLinkRewardFlag != null and req.moaLinkRewardFlag != 0">
            <choose>
                <when test="req.moaLinkRewardFlag == 1 or req.moaLinkRewardFlag == 2">
                    and (isnull(e.is_favourable, 0) = 1 or (ear.award_type = 2 and ear.approval_status = 4))
                    and f.fen is not null
                    and (f.jifenType = 21 or f.jifenType = 22)
                    <choose>
                        <when test="req.moaLinkRewardFlag == 1">
                            and u.userclass not in (5, 6)
                        </when>
                        <when test="req.moaLinkRewardFlag == 2">
                            and u.userclass in (5, 6)
                        </when>
                    </choose>
                </when>
                <when test="req.moaLinkRewardFlag == 3">
                    and f.fen is not null and f.jifenType = 11
                </when>
                <when test="req.moaLinkRewardFlag == 4">
                    and ear.award_type = 1 and ear.approval_status = 4
                    and f.fen is not null and f.jifenType = 23
                </when>
            </choose>
        </if>
        <if test="req.countrySupplement != null">
            AND isnull(e.is_intro_goods, 0) = #{req.countrySupplement}
        </if>
        <if test="req.serviceItemList != null and req.serviceItemList.size() > 0">
            AND exists (select 1
            from ${officeName}.dbo.Evaluate innerEva WITH(NOLOCK)
            cross apply F_SPLIT(innerEva.service_item, ',') f
            where innerEva.Id = e.Id
            and f.split_value in
            <foreach item="serviceItem" collection="req.serviceItemList" index="index" open="(" separator="," close=")">
                #{serviceItem}
            </foreach>
            )
        </if>

        ) t
        WHERE
        rn = 1
        ) tt
    </sql>
    <select id="getEvaluateJobScoreByEvaluateIds" resultType="com.jiuji.oa.oacore.oaorder.res.EvaluateJobScoreVO">
        SELECT e.Id as id, e.EvaluateId as evaluateId,e.Job as job,e.Score as score,u.ch999_id as userId,u.ch999_name as userName,e.score2,
        (case when e.xinshengJob=2 then 1 else 0 end) as isXinsheng,f.fen,f.inuser as inUser, f.yanyin as remark, f.kinds, f.fendate as fenDate
        FROM ${officeName}.dbo.EvaluateScore e WITH(NOLOCK)
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON e.RelateCh999Id = u.ch999_id
        LEFT JOIN dbo.ch999_fen f WITH(NOLOCK) ON f.bumen_id = e.Id AND f.jifenType = 11
        WHERE e.EvaluateId IN
        <foreach item="evaluateId" collection="evaluateIds" index="index" open="(" separator="," close=")">
            #{evaluateId}
        </foreach>
        AND e.Job IS NOT NULL
    </select>

    <select id="getEvaluateJobScoreByEvaluateIdStr" resultType="com.jiuji.oa.oacore.oaorder.res.EvaluateJobScoreVO">
        SELECT e.Id as id, e.EvaluateId as evaluateId,e.Job as job,e.Score as score,u.ch999_id as userId,u.ch999_name as userName,e.score2,
               e.wuxiao as invalid,
        (case when e.xinshengJob=2 then 1 else 0 end) as isXinsheng,f.fen,f.inuser as inUser, f.yanyin as remark, f.kinds, f.fendate as fenDate
        FROM ${officeName}.dbo.EvaluateScore e WITH(NOLOCK)
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON e.RelateCh999Id = u.ch999_id
        LEFT JOIN dbo.ch999_fen f WITH(NOLOCK) ON f.bumen_id = e.Id AND f.jifenType = 11
        WHERE e.EvaluateId IN
        (
            ${evaluateIdStr}
            )
        AND e.Job IS NOT NULL
    </select>

    <select id="getEvaluateJobScoreByEvaluateId"
            resultType="com.jiuji.oa.oacore.oaorder.res.EvaluateJobScoreVO">
        SELECT e.Id as id, e.EvaluateId as evaluateId,e.Job as job,e.Score as score,u.ch999_id as userId,u.ch999_name as userName,e.score2,
        (case when e.xinshengJob=2 then 1 else 0 end) as isXinsheng,f.fen,f.inuser as inUser, f.yanyin as remark, f.kinds, f.fendate as fenDate
        FROM ${officeName}.dbo.EvaluateScore e WITH(NOLOCK)
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_id = e.RelateCh999Id
        LEFT JOIN dbo.ch999_fen f WITH(NOLOCK) ON f.bumen_id = e.Id AND f.jifenType = 11
        WHERE e.EvaluateId = #{evaluateId}
    </select>
    <select id="getEvaluateJobScoreByScoreIds" resultType="com.jiuji.oa.oacore.oaorder.res.EvaluateJobScoreVO">
        SELECT e.Id as id, e.EvaluateId as evaluateId,e.Job as job,e.Score as score,u.ch999_id as userId,u.ch999_name as userName,e.score2,
        (case when e.xinshengJob=2 then 1 else 0 end) as isXinsheng,f.fen,f.inuser as inUser, f.yanyin as remark, f.kinds, f.fendate as fenDate
        FROM ${officeName}.dbo.EvaluateScore e WITH(NOLOCK)
        LEFT JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_id = e.RelateCh999Id
        LEFT JOIN dbo.ch999_fen f WITH(NOLOCK) ON f.bumen_id = e.Id AND f.jifenType = 11
        WHERE e.Id IN
        <foreach item="scoreId" collection="scoreIds" index="index" open="(" separator="," close=")">
            #{scoreId}
        </foreach>
        AND e.EvaluateId IS NOT NULL
    </select>

    <resultMap id="EvaluateTarget" type="com.jiuji.oa.oacore.oaorder.res.EvaluateTagRecordNameVO">
        <result column="EvaluateId" property="evaluateId" />
        <result column="Name" property="name" />
        <result column="TagType" property="tagType" />
        <result column="job" property="job" />
        <result column="IsOrderTag" property="isOrderTag" />
        <result column="scoreKind" property="scoreKind" />
        <result column="EvaluateType" property="evaluateType" />
    </resultMap>

    <select id="getEvaluateTargetList" resultMap="EvaluateTarget">
        SELECT r.EvaluateId,t.Name,t.TagType,t.EvaluateType,t.job,IsOrderTag,(case when t.kind='专业度' then 2 else 1 end) as scoreKind FROM dbo.EvaluateTagRecord r WITH(NOLOCK)
        INNER JOIN dbo.EvaluateTag t WITH(NOLOCK) ON t.Id = r.TagId
        WHERE r.EvaluateId IN
        <foreach item="evaluateId" collection="evaluateIds" index="index" open="(" separator="," close=")">
            #{evaluateId}
        </foreach>
    </select>

    <select id="getEvaluateTargetListByEvaluateIdStr" resultMap="EvaluateTarget">
        SELECT r.EvaluateId,t.Name,t.TagType,t.EvaluateType,t.job,IsOrderTag,(case when t.kind='专业度' then 2 else 1 end) as scoreKind FROM dbo.EvaluateTagRecord r WITH(NOLOCK)
        INNER JOIN dbo.EvaluateTag t WITH(NOLOCK) ON t.Id = r.TagId
        WHERE r.EvaluateId IN
        (
            ${evaluateIdStr}
        )
    </select>

    <select id="getEvaluateNonFiveStarDeductByEvaluateIdStr" resultType="com.jiuji.oa.oacore.oaorder.bo.EvaluateNonFiveStarDeductBO">
        select ABS(sum(fen)) fen, bumen_id id
        from ch999_fen with(nolock)
        where jifenType in (2, 6)
        and bumen_id in
        (
            ${evaluateScoreIdStr}
        )
        group by bumen_id
    </select>

    <select id="getEvaluateRedPackList" resultType="com.jiuji.oa.oacore.oaorder.res.EvaluateRedPackVO">
        SELECT e.EvaluateId as evaluateId,ee.EvaluateType as evaluateType,d2.name as dName2,d1.name as dName1,a.area,e.Job,
        u.ch999_id as userId,u.ch999_name as userName,f.fen,f.inuser as inUser,f.yanyin as remark,f.kinds,ee.dtime as dTime FROM dbo.ch999_fen f WITH(NOLOCK)
        INNER JOIN ${officeName}.dbo.EvaluateScore e WITH(NOLOCK) ON f.bumen_id = e.Id
        INNER JOIN ${officeName}.dbo.Evaluate ee WITH(NOLOCK) ON ee.Id = e.EvaluateId
        INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_id = e.RelateCh999Id
        LEFT JOIN dbo.areainfo a WITH(NOLOCK) ON a.id = u.area1id
        LEFT JOIN dbo.departInfo d1 WITH(NOLOCK) ON d1.id = u.depart_id AND ISNULL(d1.isdel,0) = 0
        LEFT JOIN dbo.departInfo d2 WITH(NOLOCK) ON d2.id = u.depart_id  AND ISNULL(d2.isdel,0) = 0
        WHERE f.jifenType = 11 and ee.dtime between #{req.startTime} and #{req.endTime} ORDER BY f.id DESC
    </select>
    <select id="getBasketsOfNew" resultType="java.lang.Integer">
        select basket_id
        from dbo.basket WITH(NOLOCK)
        where sub_id = #{subId}
        and isdel IS NULL
    </select>

    <select id="getUserInfoById" resultType="java.lang.String">
        SELECT ch999_name FROM ch999_user with(nolock) where ch999_id = #{userId}
    </select>


    <select id="selectDepartInfo" resultType="com.jiuji.oa.oacore.oaorder.vo.res.EvaluateEndInfo$EvaluateDepartInfo">
        SELECT id,
               departArea,
               depart_id,
               EvaluateId,
               createTime,
               createUser
        FROM getOfficeName().dbo.EvaluateDepart WITH(NOLOCK)
        where EvaluateId = #{id}
    </select>

    <select id="departInfos" resultType="com.jiuji.oa.oacore.oaorder.po.EvaluateDepart">
        SELECT id,
        EvaluateId,
        depart_id
        FROM getOfficeName().dbo.EvaluateDepart WITH(NOLOCK)
        where EvaluateId in
        (
            ${ids}
        )
    </select>

    <select id="cateInfos" resultType="com.jiuji.oa.oacore.oaorder.po.EvaluateCategoryRelation">
        SELECT EvaluateId,
        t2.name as cateName
        FROM getOfficeName().dbo.EvaluateCategoryRelation t1 WITH(NOLOCK)
        LEFT JOIN getOfficeName().dbo.TousuCategory t2 WITH(NOLOCK) on t1.cateId=t2.id
        where t1.departId in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <!-- getShouhouEvaluate -->
    <select id="getShouhouEvaluate" resultType="java.lang.String">
        SELECT CONCAT(COUNT(1), ',', COUNT(IIF(e.Score IS NOT NULL AND e.Score = 5, 1, NULL)))
        FROM office..EvaluateScore e WITH (NOLOCK)
            LEFT JOIN office..Evaluate oe WITH (NOLOCK)
                ON e.EvaluateId = oe.Id AND ISNULL(oe.isInvalid, 0) = 0 AND ISNULL(oe.EvaluateTag, 0) != 6
        WHERE e.Job IN (13, 7, 8)
          AND ISNULL(e.sub_check, 0) != 4
          AND ISNULL(e.wuxiao, 0) = 0
          AND ISNULL(e.appealStatus1, 0) != 3
    </select>

    <!-- getCumulativeService -->
    <select id="getCumulativeService" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM shouhou h with (nolock)
         LEFT JOIN shouhou_tuihuan t with (nolock) ON ISNULL(isdel, 0) = 0 AND tuihuan_kind IN (1, 2, 3, 4, 5) and t.shouhou_id = h.id
         LEFT JOIN (SELECT DISTINCT shouhou_id FROM shouhou_huishou with (nolock)) AS hs ON hs.shouhou_id = h.id
         LEFT JOIN BBSXP_Users u with (nolock) ON u.id = h.userid
        WHERE 1 = 1
          and h.isquji = 1
          and h.xianshi = 1
          and h.stats = 1
    </select>
    
    <select id="getEvaluateReward" resultType="com.jiuji.oa.oacore.oaorder.bo.EvaluateRewardBO">
        SELECT
               r.evaluateId,
               SUM(r.rewardReaPacket) rewardReaPacket,
               SUM(r.rewardPoints) rewardPoints
        FROM (
            SELECT
                es.EvaluateId evaluateId,
                SUM(CASE WHEN isnull(es.uPrices,0) > 0 THEN es.uPrices END) AS rewardReaPacket,
                0.0 rewardPoints
            FROM ${officeName}.dbo.EvaluateScore es WITH(NOLOCK)
            WHERE es.EvaluateId IN (${evaluateIdStr})
            GROUP BY es.EvaluateId
            UNION ALL
            SELECT
                e.Id evaluateId,
                0 AS rewardReaPacket,
                SUM(CASE WHEN cf.jifenType = 200 AND cf.fendate IS NOT NULL AND isnull(cf.fen, 0) > 0 THEN cf.fen * 50 END) AS rewardPoints
            FROM ${officeName}.dbo.Evaluate e WITH(NOLOCK)
            LEFT JOIN ch999_fen cf WITH(NOLOCK) ON cf.kinds = e.Id
            WHERE e.Id IN (${evaluateIdStr})
            GROUP BY e.Id
        ) r
        GROUP BY r.evaluateId
    </select>

    <select id="getOrderInfoWithinHours" resultType="com.jiuji.oa.oacore.oaorder.bo.EvaluateOrderBO">
        <!-- 新机 -->
        select s.sub_id, s.tradeDate1 tradeDate, 'V' subType
        from sub s with(nolock)
        where s.sub_check = 3
        and s.tradeDate1 <![CDATA[ > ]]> #{beforeTime}
        and s.tradeDate1 <![CDATA[ < ]]> #{afterTime}
        and s.userid = #{userId}

        union all

        <!-- 良品 -->
        select rm.sub_id, rm.tradeDate1 tradeDate, 'L' subType
        from recover_marketInfo rm with(nolock)
        where rm.sub_check = 3
        and rm.tradeDate1 <![CDATA[ > ]]> #{beforeTime}
        and rm.tradeDate1 <![CDATA[ < ]]> #{afterTime}
        and rm.saleType = 0
        and rm.userid = #{userId}

        union all

        <!-- 回收 -->
        select rs.sub_id, rs.pay_time tradeDate, 'H' subType
        from recover_sub rs with(nolock)
        where rs.sub_check = 3
        and rs.pay_time <![CDATA[ > ]]> #{beforeTime}
        and rs.pay_time <![CDATA[ < ]]> #{afterTime}
        and rs.userid = #{userId}

        union all

        <!-- 售后 -->
        select sh.id, sh.offtime tradeDate, 'S' subType
        from shouhou sh with(nolock)
        where sh.isquji = 1
        and sh.xianshi = 1
        and sh.offtime <![CDATA[ > ]]> #{beforeTime}
        and sh.offtime <![CDATA[ < ]]> #{afterTime}
        and sh.userid = #{userId}

        union all

        <!-- 小件售后 -->
        SELECT sp.id, sp.qujiandate tradeDate,
               case when p.cid in (${finalTieMoCidList}) then 'KT' else 'KX'
               end as subType
        from Smallpro sp with(nolock)
        left join SmallproBill sb with(nolock) on sb.smallproID = sp.id
        left join dbo.productinfo p with(nolock) on p.ppriceid = sb.ppriceid
        where isnull(sp.isdel, 0) = 0
        and sp.stats = 1
        and sp.qujiandate <![CDATA[ > ]]> #{beforeTime}
        and sp.qujiandate <![CDATA[ < ]]> #{afterTime}
        and sp.userid = #{userId}
    </select>
    
    <select id="getNotEvaluateOrderInfo" resultType="com.jiuji.oa.oacore.oaorder.bo.EvaluateOrderBO">
        select distinct es.sub_id subId, es.type_ evaluateType
        from EvaluateScore es with(nolock)
        where es.EvaluateId is null
        and es.sub_id in
        <foreach collection="subIds" item="subId" open="(" close=")" separator=",">
            #{subId}
        </foreach>
    </select>

    <select id="getEvaluateOrderInfoMain" resultType="com.jiuji.oa.oacore.oaorder.bo.EvaluateOrderBO">
        select distinct e.SubId subId, e.EvaluateType evaluateType
        from Evaluate e with(nolock)
        where e.SubId in
        <foreach collection="subIds" item="subId" open="(" close=")" separator=",">
            #{subId}
        </foreach>
    </select>

    <select id="checkPhoneForCh999User" resultType="java.lang.String">
        SELECT u.mobile FROM dbo.ch999_user u WITH(NOLOCK)
        WHERE u.iszaizhi = 1 and len(u.mobile) > 0
        AND EXISTS (
            SELECT 1 FROM dbo.BBSXP_Users bu WITH(NOLOCK)
            WHERE bu.ID = #{userId} AND bu.mobile = u.mobile
        )
    </select>
</mapper>
