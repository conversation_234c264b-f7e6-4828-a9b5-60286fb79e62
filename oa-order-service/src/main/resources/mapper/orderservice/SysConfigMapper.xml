<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.oaorder.dao.SysConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.orginfo.sysconfig.vo.SysConfigVo">
        <id column="id" property="id"/>
        <result column="dsc" property="dsc"/>
        <result column="name" property="name"/>
        <result column="value" property="value"/>
        <result column="code" property="code"/>
        <result column="xtenant" property="xtenant"/>
        <result column="rank" property="rank"/>
        <result column="authId" property="authId"/>
        <result column="areaids" property="areaids"/>
        <result column="kemu" property="kemu"/>
        <result column="fzhstype" property="fzhstype"/>
    </resultMap>
    <select id="getListByAuthorizeId" resultMap="BaseResultMap">
        select id,dsc,name,value,code,xtenant,rank,authId,areaids,kemu,fzhstype from sysConfig with(nolock) where (authId=#{authId} or authId = 0)  and code='37'
    </select>

    <select id="getPayWay" resultMap="BaseResultMap">
        select id,
               dsc,
               name,
               value,
               code,
               xtenant,
               rank,
               authId,
               areaids,
               kemu,
               fzhstype
        from sysConfig with(nolock)
        where code in (36, 37)
          and isnull(isdel, 0) = 0
    </select>

    <select id="getAuthorizeList" resultType="com.jiuji.oa.oacore.sys.bo.AuthModel">
        select * from authorize with(nolock) order by rank
    </select>
    <select id="getListByCodeAndXtenant" resultType="com.jiuji.oa.orginfo.sysconfig.vo.SysConfigVo">
        select id,dsc,name,value,code,xtenant,rank,authId,areaids,kemu,fzhstype from sysConfig with(nolock)  where  code=#{code} AND isnull(isdel, 0) = 0
    </select>
    <select id="getListByCoode" resultType="com.jiuji.oa.orginfo.sysconfig.vo.SysConfigVo">
        select id,dsc,name,value,code,xtenant,rank,authId,areaids,kemu,fzhstype from sysConfig with(nolock)  where  code=#{code} AND isnull(isdel, 0) = 0
    </select>

    <!-- updateValueByCode -->
    <update id="updateValueByCode">
        UPDATE sysConfig
        SET value = #{value}
        WHERE code = #{code} AND isnull(isdel, 0) = 0
    </update>
</mapper>
