<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.oacore.yearcardtransfer.mapper.YearPackageTransferMapper">

    <sql id="yearPackageDetail">
        SELECT
            t.id,
            t.transfer_code transferCode,
            t.sender_id AS senderId,
            isnull(su.hezuo_name, su.UserName) AS senderUserName,
            t.receiver_id AS receiverId,
            isnull(ru.hezuo_name, ru.UserName) AS receiverUserName,
            ru.mobile receiverMobile,
            t.status,
            t.start_time as startTime,
            t.end_time endTime,
            tc.basket_idBind bindBasketId
        FROM year_package_transfer t with(nolock)
                 LEFT JOIN BBSXP_Users su with(nolock) ON t.sender_id = su.id
                 LEFT JOIN BBSXP_Users ru with(nolock) ON t.receiver_id = ru.id
                 LEFT JOIN tiemoCard tc with(nolock) ON t.origin_card_id = tc.id
    </sql>

    <!-- 新增查询 -->
    <select id="selectTransferDetailByCode" resultType="com.jiuji.oa.oacore.yearcardtransfer.dto.YearPackageTransferDetailDto">
        <include refid="yearPackageDetail"></include>
        where t.transfer_code = #{transferCode}
    </select>
    <select id="listTransferDetail"
            resultType="com.jiuji.oa.oacore.yearcardtransfer.dto.YearPackageTransferDetailDto">
        <include refid="yearPackageDetail"></include>
        where tc.basket_idBind in
            <foreach collection="bindBasketIds" item="item" open="(" separator="," close=")">
                = #{item}
            </foreach>
            and t.receiver_id = #{userId}
        order by t.create_time desc
    </select>

</mapper>
