<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.oacore.sys.dao.SqlServerColumnMapper">
    <sql id="columnNameCOMMASql">
        <!--复制数据的时候动态处理列名-->
        <foreach collection="columnNames" separator="," item="columnName">
            <if test="colValMap.get(columnName) == null">
                ${columnName}
            </if>
        </foreach>
        <if test="!colValMap.isEmpty()">
            ,<foreach collection="colValMap" item="value" index="key" separator=",">
            <choose>
                <when test="isValue">#{value}</when>
                <otherwise>${key}</otherwise>
            </choose>

        </foreach>
        </if>
    </sql>
    <sql id="typeColumnCnd">
        <if test="mTableInfoEnum.typeColumnName != null and types != null and !types.isEmpty()">
            and ${mTableInfoEnum.typeColumnName} in
            <foreach collection="types" open="(" separator="," close=")" item="type">
                #{type}
            </foreach>
        </if>
    </sql>
    <select id="selectMax" resultType="java.lang.Long">
        select max(${mTableInfoEnum.mmColumnName}) from ${mTableInfoEnum.code} with(nolock)
        <where>
            <include refid="typeColumnCnd"></include>
            <if test="mTableInfoEnum.creatTimeColumnName != null">
                and ${mTableInfoEnum.creatTimeColumnName} = (
                select max(ctc.${mTableInfoEnum.creatTimeColumnName})
                from ${mTableInfoEnum.code} ctc with(nolock)
                <where>
                    <include refid="typeColumnCnd"></include>
                </where>
                )
            </if>
        </where>
    </select>
    <select id="selectId" resultType="java.lang.Object">
        select top 1 ${mTableInfoEnum.mmColumnName} from ${mTableInfoEnum.code} with(nolock)
        <where>
            <if test="mTableInfoEnum.typeColumnName != null and types != null and !types.isEmpty()">
                and ${mTableInfoEnum.typeColumnName} in
                <foreach collection="types" open="(" separator="," close=")" item="type">
                    #{type}
                </foreach>
            </if>
            and ${mTableInfoEnum.mmColumnName} = #{tableId}
        </where>
    </select>
</mapper>
