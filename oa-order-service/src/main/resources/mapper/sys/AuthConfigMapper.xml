<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.sys.dao.AuthConfigMapper">

    <select id="getAreaBelongsDcHqD1AreaId" resultType="com.jiuji.oa.oacore.sys.bo.AreaBelongsDcHqD1AreaId">
        SELECT top 1 a.id as areaId,au.D1AreaId as d1AreaId,au.dcAreaId,au.hqAreaId,au.h1AreaId from authorize au with(nolock)
        LEFT JOIN areainfo a with(nolock) on au.id = a.authorizeid
        where a.id = #{currentAreaId}
    </select>

</mapper>
