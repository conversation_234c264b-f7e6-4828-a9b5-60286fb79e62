<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.order.mapper.IAreainfoMapper">

    <select id="getSharedInventoryArea" resultType="java.lang.String">
        SELECT areaids
        from area_inventory_share_config aisc with(nolock)
        where isnull(aisc.is_del, 0) = 0 and isnull(aisc.share_type,0)=0 and #{areaId} in (SELECT * from dbo.F_SPLIT(isnull(aisc.areaids, ''), ','))
    </select>
    <select id="conversionCityId" resultType="java.lang.Integer">
        SELECT TOP 1 cityid
        from dbo.AREAWEB W WITH(NOLOCK)
        WHERE
        charindex(CONVERT(varchar(6), #{cityId}),
        case WHEN (w.kc_dids is null or Replace(w.kc_dids, ' ', '') = '')
        THEN CONVERT(varchar(6), w.cityid)
        ELSE w.kc_dids END) > 0
        order by ispass desc
    </select>
    <select id="getRankAreaId" resultType="java.lang.Integer">
        select areaid from dbo.ch999Ranks a with(nolock ) where ch999_id = #{userId}
    </select>
    <select id="getSmsChannelById" resultType="com.jiuji.oa.oacore.tousu.bo.SmsChannelBo">
        select id as areaId,vCodeChannel,marketChannel from areainfo with(nolock) where id = #{areaId};
    </select>

    <select id="getAreainfoComment" resultType="java.lang.Integer">
        SELECT TOP 1 ai_id FROM dbo.areainfo_comment with(nolock)
        WHERE ai_id = #{areaId} AND dtime > #{time}
        AND comment like CONCAT('%',#{comment},'%')
    </select>
</mapper>
