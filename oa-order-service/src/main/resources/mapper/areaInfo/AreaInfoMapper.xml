<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.tousu.dao.AreaInfoMapper">

    <select id="getIdByArea" resultType="java.lang.Integer">
        SELECT id,area FROM areaInfo with(nolock) where area = #{area}
    </select>
    <select id="getDcAreaOld" resultType="com.jiuji.oa.oacore.tousu.bo.AreaIdAndNameVO">
        select id as value ,area as label from areainfo with(nolock) where ispass='true' and area like '%dc%'
    </select>
    <select id="getDcAreaNew" resultType="com.jiuji.oa.oacore.tousu.bo.AreaIdAndNameVO">
        select id as value ,area as label from areainfo with(nolock)  where ispass='true' and areaDcType=1
    </select>
    <select id="getAreaXtenant" resultType="java.lang.Integer">
        SELECT DISTINCT xtenant from areainfo a2 with(nolock) order by xtenant
    </select>


    <select id="getStoreDepartByAreaIds" resultType="com.jiuji.oa.oacore.other.bo.StoreHierarchyDTO">
        SELECT
        id as areaId,
        depart_id as departId
        FROM
        areainfo with(nolock)
        WHERE
        id in
        <foreach collection="areaIds" item="areaId" open="(" separator="," close=")">
            #{areaId}
        </foreach>
    </select>

</mapper>
