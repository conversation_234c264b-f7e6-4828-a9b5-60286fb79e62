<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.samsung.dao.SamsungMapper">

    <select id="selectFenqiInfo" resultType="com.jiuji.oa.oacore.samsung.dto.FenQiSubDto">
        select
            top 1 payWay,
            orderPrice,
            fenQiNum,
            orderNum
        from
            dbo.alipayInfo with(nolock)
        where
            fenQiNum>0
          and isnull(isFenQiFree,0)= 1
          and (isnull(is_return,0)= 1 or isnull(is_notify,0)= 1 )
          and payType = 1
          and orderNum = #{orderNum}
        order by fenQiNum desc
    </select>

    <select id="selectTelNo" resultType="java.lang.String">
        SELECT
            top 1 d.telNo
        FROM
            recover_activity_detail d with(nolock)
        where
          isnull(d.isComplete,0) = 1
          and d.newSubId = #{newSubId}
    </select>
    <select id="selectOrderInfo" resultType="com.jiuji.oa.oacore.samsung.dto.OrderInfoDto">
        select s.sub_id subId,
               s.tradeDate1 tradeDate,
               pm.imei,
               isnull(d.telNo,s.sub_mobile) subMobile,
               b.price,
               p.product_name productName,
               p.product_color productColor,
               a.area_name shopName,
               ra.code shopId
        from sub s with(nolock)
        left join basket b with(nolock) on s.sub_id = b.sub_id
        left join product_mkc pm with(nolock) on b.basket_id = pm.basket_id
        left join productinfo p with(nolock) on pm.ppriceid = p.ppriceid
        left join recover_activity_detail d with(nolock) on isnull(d.isComplete,0) = 1 and d.newSubId =  s.sub_id
        left join areainfo a with(nolock) on s.areaid = a.id
        left join recover_activityArea ra with(nolock) on a.id = ra.areaId
        where p.brandID in (2)
          and p.ismobile1 = 1
          and ra.id is not null
          and s.sub_id = #{subId}
    </select>
    <select id="selectSamsungSubId" resultType="java.lang.Integer">
        select top 1000 isnull(ss.sub_id,s.sub_id) as subId
        from sub s with(nolock)
        left join basket b with(nolock) on b.sub_id = s.sub_id
        left join sub ss with(nolock) on s.subPID = ss.sub_id and s.returnDate > ss.tradeDate1
        left join product_mkc pm with(nolock) on b.basket_id = pm.basket_id
        left join productinfo p with(nolock) on pm.ppriceid = p.ppriceid
        left join samsung_instalment_data_log sidl with(nolock) on s.sub_id = sidl.sub_id and sidl.is_success = 1
        where s.sub_check in (3)
          and p.brandID in (2)
          and p.cid in (select * from f_category_children(2))
          and sidl.id is null
          and s.tradeDate1 > DATEADD(day,#{days},GETDATE())
        order by s.sub_id desc
    </select>
</mapper>
