<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.weborder.dao.OrderAssistantMapper">

    <select id="getOrderContext" resultType="com.jiuji.oa.oacore.weborder.dto.OrderContextDTO">
        select
            top 1
            s.sub_id subId,
            s.sub_check subCheck,
            case when cu.ch999_id is null then 0 else 1 end isStaffSub,
            case when rs.sub_ido is null then 0 else 1 end isRecoverSub,
            case when st.sub_id is null then 0 else 1 end isTuihuan,
            case when b.sub_id is null then 0 else 1 end isBargain,
            (select top 1 1 from dbo.tax_piao t with(nolock)
            left join dbo.electronPiao p with(nolock) on t.id=p.piaoid
            where t.flag in (0,1,2,3,4,8,6,7)
            and isnull(t.type_, 0)= 0
            and exists (select 1 from dbo.piaoProductInfo f with(nolock) where t.id = f.piaoid and f.sub_id = s.sub_id)) isTaxpiao,
            s.subtype
        from sub s with(nolock)
        left join basket b WITH(NOLOCK) on b.sub_id = s.sub_id and b.[type] = 22
        left join ch999_user cu WITH(NOLOCK) on ISNULL(cu.mobile,'') <![CDATA[ <> ]]> '' AND cu.mobile = s.sub_mobile and cu.iszaizhi = 1
        left join recover_sub rs WITH(NOLOCK) on rs.subIdoType = 1 and rs.sub_ido = s.sub_id and rs.sub_check <![CDATA[ <> ]]> 4
        left join shouhou_tuihuan st WITH(NOLOCK) on st.sub_id = s.sub_id and st.tuihuan_kind = 6 and isnull(st.isdel,0) = 0 and check3 is NULL
        where s.sub_id = #{req.orderId}
    </select>
    <select id="getXinjiOrderProductInfo" resultType="com.jiuji.oa.oacore.weborder.dto.OrderProductInfoDTO" timeout="10000">
        select
            s.sub_id subId,
            s.sub_check,
            b.basket_id basketId,
            p.productid productId,
            p.ppriceid,
            p.ismobile1 ismobile,
            case when br.basket_id is null then 0 else 1 end isBasketBindRecord,
            case when p.cid in (select f.id from f_category_children(662) f) then 1 else 0 end isCid662,
            p.cid
        from sub s with(nolock)
        left join basket b with(nolock) on s.sub_id = b.sub_id
        left join productinfo p with(nolock) on b.ppriceid = p.ppriceid
        left join basketBindRecord br with(nolock) on b.basket_id = br.basket_id and p.cid in (select f.id from f_category_children(63) f)
        where s.sub_id = #{subId}
    </select>
    <select id="getRepairOrderContext" resultType="com.jiuji.oa.oacore.weborder.dto.RepairOrderContextDTO">
        SELECT top 1
            s.id subId,
            isnull(s.ServiceType,0) serviceType,
            case when cu.ch999_id is null then 0 else 1 end isStaffSub,
            isnull(s.isquji,0) isquji,
            case when st.tuihuan_kind in (1,2,3,4) then 1 else 0 end istuihuan,
            case when st.tuihuan_kind in (5) then 1 else 0 end istuifei,
            case when st.tuihuan_kind in (11) then 1 else 0 end istuiding
        from shouhou s with(nolock)
        left join BBSXP_Users bu with(nolock) on bu.ID = s.userid
        left join ch999_user cu with(nolock) on (cu.mobile=bu.mobile or cu.mobile=s.mobile) and cu.iszaizhi = 1
        left join shouhou_tuihuan st with(nolock) on st.shouhou_id = s.id and st.tuihuan_kind in(1,2,3,4,5,11) and isnull(st.isdel,0) = 0 and st.check3 is null
        where s.xianshi = 1
        and s.id = #{req.orderId}
    </select>
    <select id="getRepairSmallOrderContext"
            resultType="com.jiuji.oa.oacore.weborder.dto.RepairSmallOrderContextDTO">
        select top 1
               s.id subId,
               sub.subtype as subType,
               s.codeMsg codeMsg,
               s.Kind kind,
               s.Stats stats,
               st.smallproid stSmallproId,
               case when sf.smallproid is null then 0 else 1 end ishandle,
               case when st.smallproid is not null and st.tuihuan_kind in(9) and st.check1dtime is null then 1 else 0 end ishuan,
               case when st.smallproid is not null and st.tuihuan_kind in(7) and st.check3 is null then 1 else 0 end istui,
               st.tuihuan_kind tuihuanKind,
               (select top 1 1 from SmallproBill sb with(nolock)
            left join productinfo p with(nolock) on p.ppriceid = sb.ppriceid
        where sb.smallproID=s.id and p.cid in(select f.id from f_category_children(662) f)) isCid662
        from Smallpro s with(nolock)
        left join sub sub  with(nolock) on sub.sub_id = s.sub_id
        left join shouhou_fanchang sf with(nolock) on sf.smallproid=s.id and isnull(sf.rstats,0)=0
        left join shouhou_tuihuan st with(nolock) on st.smallproid = s.id and isnull(st.isdel,0) = 0
        where isnull(s.isdel,0) = 0
        and s.id = #{req.orderId}
    </select>
    <select id="getRecoverOrderContext" resultType="com.jiuji.oa.oacore.weborder.dto.OrderContextDTO">
        select
            top 1
            s.sub_id subId,
            s.sub_check subCheck
        FROM dbo.recover_sub s WITH (nolock)
        where s.sub_id = #{req.orderId}
    </select>
    <select id="getSecondhandOrderContext" resultType="com.jiuji.oa.oacore.weborder.dto.OrderContextDTO">
        select
            top 1
            s.sub_id subId,
            s.sub_check subCheck,
            case when cu.ch999_id is null then 0 else 1 end isStaffSub,
            case when rs.sub_ido is null then 0 else 1 end isRecoverSub,
            case when st.sub_id is null then 0 else 1 end isTuihuan,
            (select top 1 1 from dbo.tax_piao t with(nolock)
            left join dbo.electronPiao p with(nolock) on t.id=p.piaoid
        where t.flag in (0,1,2,3,4,8,6,7)
          and isnull(t.type_, 0)= 2
          and exists (select 1 from dbo.piaoProductInfo f with(nolock) where t.id = f.piaoid and f.sub_id = s.sub_id)) isTaxpiao
        FROM
            dbo.recover_marketInfo s WITH (nolock)
        left join ch999_user cu WITH(NOLOCK) on ISNULL(cu.mobile,'') <![CDATA[ <> ]]> '' AND cu.mobile = s.sub_mobile and cu.iszaizhi = 1
        left join recover_sub rs WITH(NOLOCK) on rs.subIdoType = 2 and rs.sub_ido = s.sub_id and rs.sub_check <![CDATA[ <> ]]> 4
        left join shouhou_tuihuan st WITH(NOLOCK) on st.sub_id = s.sub_id and st.tuihuan_kind = 8 and isnull(st.isdel,0) = 0 and check3 is NULL
        where s.sub_id = #{req.orderId}
    </select>
</mapper>
