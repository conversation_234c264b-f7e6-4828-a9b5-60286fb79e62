<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.oacore.weborder.dao.MyPhoneMapper">
    <sql id="listMyPhoneCondition">
        <choose>
            <when test="xTenant == 0">
                and not exists(select 1 from dbo.areainfo a WITH(NOLOCK) where s.areaid=a.id and a.printName &lt;&gt; '九机网')
            </when>
            <otherwise>
                and exists(select 1 from dbo.areainfo a WITH(NOLOCK) where s.areaid=a.id and a.xtenant=#{xTenant})
            </otherwise>
        </choose>
        <if test="keyword != null and keyword != ''">
            AND (k.imei = #{keyword} or p.product_name like CONCAT('%',#{keyword},'%'))
        </if>

        <foreach collection="notConditions" item="notCondition">
            <if test="notCondition.subType == ${subType}">
                <if test="notCondition.brandId != null">
                    and p.brandID &lt;&gt; ${notCondition.brandId}
                </if>
            </if>
        </foreach>


    </sql>
    <sql id="subMyphoneSelect">
        select ${selectColumn1}
        FROM dbo.sub s WITH(NOLOCK)
        INNER JOIN dbo.basket b WITH(NOLOCK) ON b.sub_id = s.sub_id AND b.ismobile = 1 AND ISNULL(b.isdel,0) = 0
        INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
        LEFT JOIN dbo.product_mkc k WITH(NOLOCK) ON k.basket_id = b.basket_id
        LEFT JOIN dbo.subFlagRecord f with (nolock ) ON f.sub_id = s.sub_id
        WHERE s.sub_check = 3 AND s.userid = #{userId}
        <include refid="listMyPhoneCondition"><property name="subType" value="1"/></include>
    </sql>
    <sql id="recoverMyphoneSelect">
        SELECT ${selectColumn2}
        FROM dbo.recover_marketInfo s WITH(NOLOCK)
                 INNER JOIN dbo.recover_marketSubInfo b WITH(NOLOCK) ON s.sub_id = b.sub_id AND ISNULL(b.isdel,0) = 0 AND b.ismobile = 1
                 INNER JOIN dbo.recover_mkc k WITH(NOLOCK) ON b.basket_id = k.to_basket_id
                 left join  dbo.recover_mkc k1 WITH(NOLOCK) on k1.imei = k.imei and k1.id &lt;&gt; k.id AND k1.intime > s.tradeDate1
                 INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = k.ppriceid
        WHERE ISNULL(s.saleType,0) = 0 AND s.sub_check = 3 AND s.userid = #{userId}
        AND k1.id is null
        <include refid="listMyPhoneCondition"><property name="subType" value="2"/></include>
    </sql>
    <sql id="extendMyphoneSelect">
        select ${selectColumn3}
        from sub s with(nolock)
        inner join basket b with(nolock) on b.sub_id = s.sub_id
        inner join dbo.basket_extend be with(nolock) on be.basket_id = b.basket_id and be.union_pro_ppid is not null
        inner join ServiceRecord k with(nolock) on k.basket_id = b.basket_id and isnull(k.basket_idBind,0)=0 and isnull(k.bargain_basket_id_bind,0)=0
        INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = be.union_pro_ppid
        where s.userid = #{userId} and s.sub_check = 3
        <include refid="listMyPhoneCondition"><property name="subType" value="-1"/></include>
    </sql>
    <sql id="listMyPhoneFun">
        select * from #sub_myphone
        UNION ALL
        select * from #recover_myphone
        UNION ALL
        select * from #extend_myphone
    </sql>
    <select id="listMyPhone" resultType="com.jiuji.oa.oacore.weborder.res.MyPhoneResVo$MyPhoneResDetail">
        <bind name="selectColumn1" value="'distinct s.sub_id,b.basket_id,s.sub_to,s.sub_date,s.delivery,s.sub_pay,b.price,s.tradeDate,s.tradeDate1
        ,p.product_name,p.product_color,p.ppriceid,k.imei,p.bpic,p.productid,1 subKinds ,f.flagType into #sub_myphone'"/>
        <bind name="selectColumn2" value="'distinct s.sub_id,b.basket_id,s.sub_to,s.sub_date,s.delivery,s.sub_pay,b.price,s.tradeDate,s.tradeDate1
        ,p.product_name,p.product_color,p.ppriceid,k.imei,p.bpic,p.productid,2 subKinds ,0 flagType into #recover_myphone'"/>
        <bind name="selectColumn3" value="'distinct s.sub_id,null basket_id,s.sub_to,s.sub_date,s.delivery,s.sub_pay,p.memberprice price,s.tradeDate,s.tradeDate1
        ,p.product_name,p.product_color,p.ppriceid,k.imei,p.bpic,p.productid,3 subKinds ,0 flagType into #extend_myphone'"/>
        <include refid="subMyphoneSelect"></include>;
        <include refid="recoverMyphoneSelect"></include>;
        <include refid="extendMyphoneSelect"></include>;
        select distinct * from (<include refid="listMyPhoneFun"></include>) a
        ORDER BY a.sub_date DESC
        OFFSET #{offset} ROW FETCH NEXT #{pageSize} rows only
        DROP TABLE #sub_myphone,#recover_myphone,#extend_myphone
    </select>
    <select id="countMyPhone" resultType="java.lang.Integer">
        <choose>
            <when test="type == 1">
                <bind name="selectColumn1" value="'count(distinct k.id) c'"/>
                <include refid="subMyphoneSelect"></include>
            </when>
            <when test="type == 2">
                <bind name="selectColumn2" value="'count(distinct k.id) c'"/>
                <include refid="recoverMyphoneSelect"></include>
            </when>
            <when test="type == 3">
                <bind name="selectColumn3" value="'count(distinct b.basket_id) c'"/>
                <include refid="extendMyphoneSelect"></include>
            </when>
        </choose>
    </select>
</mapper>
