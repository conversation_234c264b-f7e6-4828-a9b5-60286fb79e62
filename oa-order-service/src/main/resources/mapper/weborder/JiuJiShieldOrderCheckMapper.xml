<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.oacore.weborder.dao.JiuJiShieldOrderCheckMapper">
    <resultMap type="com.jiuji.oa.oacore.weborder.JiuJiShieldCheckDetailVo" id="jiuJiShieldCheckDetailVo">
        <!-- 关联对象 -->
        <id property="id" column="id"/>
        <result property="flagType" column="flagType"/>
        <result property="subId" column="sub_id"/>
        <result property="dtime" column="dtime"/>
        <result property="status" column="status"/>
        <result property="checkUser" column="check_user"/>
        <result property="checkTime" column="check_time"/>
        <result property="checkComment" column="check_comment"/>
        <association property="protectProductInfo" javaType="com.jiuji.oa.oacore.weborder.JiuJiShieldCheckDetailVo$ProtectProductInfoVo">
            <!-- 关联绑定商品信息 -->
            <id column="union_pro_imei" property="imei"/>
            <result column="union_pro_ppid" property="ppriceid"/>
        </association>
        <association property="basketInfo" javaType="com.jiuji.oa.oacore.weborder.JiuJiShieldCheckDetailVo$BasketInfoVo">
            <!-- 关联商品信息 -->
            <id property="basketId" column="basket_id"/>
            <result property="ppriceid" column="basket_ppriceid"/>
        </association>
    </resultMap>
    <select id="getCheckDetail" resultMap="jiuJiShieldCheckDetailVo">
        SELECT sfr.id, sfr.sub_id, sfr.flagType, sfr.status, sfr.dtime, sfr.check_user, sfr.check_time, sfr.check_comment,
               be.union_pro_imei,be.union_pro_ppid,b.basket_id,b.ppriceid basket_ppriceid
        FROM dbo.subFlagRecord sfr with(nolock)
         inner join basket b with(nolock) on b.sub_id = sfr.sub_id and b.[type] = 107
         inner join dbo.basket_extend be with(nolock) on be.basket_id = b.basket_id
        where sfr.flagType = 3 and exists(select 1 from dbo.sub s with(nolock) where s.sub_id = sfr.sub_id and s.userid = #{userId}) and sfr.sub_id = #{subId}
    </select>
</mapper>