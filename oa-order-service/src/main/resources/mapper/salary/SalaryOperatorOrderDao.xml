<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.salary.dao.SalaryOperatorOrderDao">

    <select id="listOperatorOrder" resultType="com.jiuji.oa.salary.OperatorOrder">
        select 0                         isReturnOrder,
               'operator'                orderKind,
               '4-1'                     orderType,
               n.order_type orderVType,
               n.order_id                subId,
               n.aid areaid,
               1                         userType,
               n.sale_count 'count', n.pay_amt saleAmount,
               oo.predict_amt            estimatedCommission,
               oo.actual_amt             actualCommission,
               oo.point_amt              agencyPoints,
               oo.settlement_amt         settlementAmount,
               oo.bounty_amt          as bounty,
               oo.predict_ability_amt as preAbility,
               oo.deduction_amt       as deductionMoney,
               oo.order_status        as status,
               oo.refund_time         as returnTime,
               n.pid                     productId,
               n.bid                     brandId,
               n.ppid                    ppId,
               n.cid                     cid,
               n.is_big                  salaryProductType,
               n.finish_time          AS tradeDate,
               0                         trueSalaryLossRelation
        from dwd_base_jiuji.dwd_b_operator_order oo
                 left join dwd_pre_jiuji.dwd_p_new_order n on n.id = new_id
                 left join dws_jiuji.dim_ch999_user cu on cu.ch999_name = n.sell_staff
        where 1 = 1
          and cu.ch999_id = #{ch999Id}
          and n.finish_time BETWEEN #{startDate} AND #{endDate}
          AND oo.order_status in (1, 2)
          and oo.is_del = 0
          and (oo.order_status = 1 or oo.refund_time > n.finish_time)

        union all

        select 0                         isReturnOrder,
               'operator'                orderKind,
               '4-1'                     orderType,
               n.order_type orderVType,
               n.order_id                subId,
               n.aid areaid,
               2                         userType,
               n.sale_count 'count', n.pay_amt saleAmount,
               oo.predict_amt            estimatedCommission,
               oo.actual_amt             actualCommission,
               oo.point_amt              agencyPoints,
               oo.settlement_amt         settlementAmount,
               oo.bounty_amt          as bounty,
               oo.predict_ability_amt as preAbility,
               oo.deduction_amt       as deductionMoney,
               oo.order_status        as status,
               oo.refund_time         as returnTime,
               n.pid                     productId,
               n.bid                     brandId,
               n.ppid                    ppId,
               n.cid                     cid,
               n.is_big                  salaryProductType,
               n.finish_time          AS tradeDate,
               0                         trueSalaryLossRelation
        from dwd_base_jiuji.dwd_b_operator_order oo
                 left join dwd_pre_jiuji.dwd_p_new_order n on n.id = new_id
                 left join dws_jiuji.dim_ch999_user cu on cu.ch999_name = n.trade_staff
        where 1 = 1
          and cu.ch999_id = #{ch999Id}
          and n.finish_time BETWEEN #{startDate} AND #{endDate}
          AND oo.order_status in (1, 2)
          and oo.is_del = 0
          and (oo.order_status = 1 or oo.refund_time > n.finish_time)

        union all

        select 1                         isReturnOrder,
               'operator'                orderKind,
               '4-1'                     orderType,
               n.order_type orderVType,
               n.order_id                subId,
               n.aid areaid,
               1                         userType,
               n.sale_count 'count', n.pay_amt saleAmount,
               oo.predict_amt            estimatedCommission,
               oo.actual_amt             actualCommission,
               oo.point_amt              agencyPoints,
               oo.settlement_amt         settlementAmount,
               oo.bounty_amt          as bounty,
               oo.predict_ability_amt as preAbility,
               oo.deduction_amt       as deductionMoney,
               oo.order_status        as status,
               oo.refund_time         as returnTime,
               n.pid                     productId,
               n.bid                     brandId,
               n.ppid                    ppId,
               n.cid                     cid,
               n.is_big                  salaryProductType,
               n.finish_time          AS tradeDate,
               0                         trueSalaryLossRelation
        from dwd_base_jiuji.dwd_b_operator_order oo
                 left join dwd_pre_jiuji.dwd_p_new_order n on n.id = new_id
                 left join dws_jiuji.dim_ch999_user cu on cu.ch999_name = n.sell_staff
        where 1 = 1
          and cu.ch999_id = #{ch999Id}
          and oo.refund_time BETWEEN #{startDate} AND #{endDate}
          and oo.is_del = 0
          and n.finish_time > '1900-01-01 00:00:00'
          and oo.refund_time > n.finish_time

        union all

        select 1                         isReturnOrder,
               'operator'                orderKind,
               '4-1'                     orderType,
               n.order_type orderVType,
               n.order_id                subId,
               n.aid areaid,
               2                         userType,
               n.sale_count 'count', n.pay_amt saleAmount,
               oo.predict_amt            estimatedCommission,
               oo.actual_amt             actualCommission,
               oo.point_amt              agencyPoints,
               oo.settlement_amt         settlementAmount,
               oo.bounty_amt          as bounty,
               oo.predict_ability_amt as preAbility,
               oo.deduction_amt       as deductionMoney,
               oo.order_status        as status,
               oo.refund_time         as returnTime,
               n.pid                     productId,
               n.bid                     brandId,
               n.ppid                    ppId,
               n.cid                     cid,
               n.is_big                  salaryProductType,
               n.finish_time          AS tradeDate,
               0                         trueSalaryLossRelation
        from dwd_base_jiuji.dwd_b_operator_order oo
                 left join dwd_pre_jiuji.dwd_p_new_order n on n.id = new_id
                 left join dws_jiuji.dim_ch999_user cu on cu.ch999_name = n.trade_staff
        where 1 = 1
          and cu.ch999_id = #{ch999Id}
          and oo.refund_time BETWEEN #{startDate} AND #{endDate}
          and oo.is_del = 0
          and n.finish_time > '1900-01-01 00:00:00'
          and oo.refund_time > n.finish_time
    </select>

    <select id="listOperatorOrderOperator" parameterType="com.jiuji.oa.salary.CalculateOperatorReq"
            resultType="com.jiuji.oa.salary.OperatorOrder">
        SELECT
        t.isReturnOrder,
        t.orderKind,
        t.orderType,
        t.orderVType,
        t.brandId,
        t.salaryProductType,
        t.subId,
        t.userType,
        t.COUNT,
        t.saleAmount,
        t.estimatedCommission,
        t.actualCommission,
        t.agencyPoints,
        t.settlementAmount,
        t.bounty,
        t.preAbility,
        t.deductionMoney,
        t.status,
        t.returnTime,
        t.productId,
        t.ppId,
        t.cid,
        t.tradeDate,
        t.areaid AS areaId,
        0 checkSalaryLossRelation,
        0 trueSalaryLossRelation
        FROM(

        select
        0 isReturnOrder,
        'operator' orderKind,
        '4-1' orderType,
        n.order_type orderVType,
        n.order_id subId,
        1 userType,
        n.sale_count 'count',
        n.pay_amt saleAmount,
        oo.predict_amt estimatedCommission,
        oo.actual_amt actualCommission,
        oo.point_amt agencyPoints,
        oo.settlement_amt settlementAmount,
        oo.bounty_amt as bounty,
        oo.predict_ability_amt as preAbility,
        oo.deduction_amt as deductionMoney,
        oo.order_status as status,
        oo.refund_time as returnTime,
        n.pid productId,
        n.bid brandId,
        n.ppid ppId,
        n.cid cid,
        n.is_big salaryProductType,
        n.finish_time AS tradeDate,
        n.aid areaid
        from dwd_base_jiuji.dwd_b_operator_order oo
        left join dwd_pre_jiuji.dwd_p_new_order n on n.id = new_id
        WHERE n.finish_time BETWEEN #{calculateOperatorReq.startDate} AND #{calculateOperatorReq.endDate}
        AND oo.order_status in (1, 2)
        and oo.is_del = 0
        and (oo.order_status = 1 or oo.refund_time > n.finish_time)
        AND n.aid IN
        <foreach collection="calculateOperatorReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>

        union all

        select
        0 isReturnOrder,
        'operator' orderKind,
        '4-1' orderType,
        n.order_type orderVType,
        n.order_id subId,
        1 userType,
        n.sale_count 'count',
        n.pay_amt saleAmount,
        oo.predict_amt estimatedCommission,
        oo.actual_amt actualCommission,
        oo.point_amt agencyPoints,
        oo.settlement_amt settlementAmount,
        oo.bounty_amt as bounty,
        oo.predict_ability_amt as preAbility,
        oo.deduction_amt as deductionMoney,
        oo.order_status as status,
        oo.refund_time as returnTime,
        n.pid productId,
        n.bid brandId,
        n.ppid ppId,
        n.cid cid,
        n.is_big salaryProductType,
        n.finish_time AS tradeDate,
        n.aid areaid
        from dwd_base_jiuji.dwd_b_operator_order oo
        left join dwd_pre_jiuji.dwd_p_new_order n on n.id = new_id
        WHERE oo.refund_time BETWEEN #{calculateOperatorReq.startDate} AND #{calculateOperatorReq.endDate}
        and oo.is_del = 0
        and n.finish_time > '1900-01-01 00:00:00'
        and oo.refund_time > n.finish_time
        AND n.aid IN
        <foreach collection="calculateOperatorReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        ) t

        WHERE 1=1
        <if test="calculateOperatorReq.orderType!=null">
            AND t.orderType = #{calculateOperatorReq.orderType}
        </if>
        <if test="calculateOperatorReq.cid!=null and calculateOperatorReq.cid.size>0">
            AND t.cid IN
            <foreach collection="calculateOperatorReq.cid" item="cid" separator="," open="(" close=")">
                #{cid}
            </foreach>
        </if>
        <if test="calculateOperatorReq.productId!=null and calculateOperatorReq.productId.size>0">
            AND t.productId IN
            <foreach collection="calculateOperatorReq.productId" item="productId" separator="," open="(" close=")">
                #{productId}
            </foreach>
        </if>
        <if test="calculateOperatorReq.ppId!=null and calculateOperatorReq.ppId.size>0">
            AND t.ppId IN
            <foreach collection="calculateOperatorReq.ppId" item="ppId" separator="," open="(" close=")">
                #{ppId}
            </foreach>
        </if>
    </select>
</mapper>
