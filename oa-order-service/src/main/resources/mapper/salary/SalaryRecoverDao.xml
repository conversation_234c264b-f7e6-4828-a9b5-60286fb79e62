<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.salary.dao.SalaryRecoverDao">

    <sql id="return_order_from_2-1_2-2">
        FROM dbo.recover_sub s WITH(NOLOCK)
        INNER JOIN dbo.recover_basket b WITH(NOLOCK) ON s.sub_id = b.sub_id
        LEFT JOIN dbo.recover_basket_extend rbe with(nolock) on rbe.recover_basket_id = b.id
        INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
        INNER JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.from_basket_id = b.id
        LEFT JOIN dbo.mkc_dellogs md WITH (nolock) ON md.mkc_id = k.id and md.kinds = 'h4'
        LEFT JOIN dbo.recover_marketSubInfo rb with(nolock) ON k.to_basket_id = rb.basket_id
        LEFT JOIN dbo.recover_marketInfo rs with(nolock) ON rb.sub_id = rs.sub_id
    </sql>

    <sql id="return_order_refund_from_2-1_2-2">
        FROM dbo.recover_sub s WITH(NOLOCK)
        INNER JOIN dbo.recover_basket b WITH(NOLOCK) ON s.sub_id = b.sub_id
        LEFT JOIN dbo.recover_basket_extend rbe with(nolock) on rbe.recover_basket_id = b.id
        INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
        INNER JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.from_basket_id = b.id
        LEFT JOIN dbo.mkc_dellogs md WITH (nolock) ON md.mkc_id = k.id and md.kinds = 'h4'
        LEFT JOIN dbo.recover_marketSubInfo rb with(nolock) ON k.to_basket_id = rb.basket_id
        LEFT JOIN dbo.recover_marketInfo rs with(nolock) ON rb.sub_id = rs.sub_id
    </sql>

    <sql id="return_order_from_2-1_2-3">
        FROM dbo.recover_marketInfo rs with(nolock)
        LEFT JOIN dbo.recover_marketSubInfo rb with(nolock) ON rs.sub_id = rb.sub_id
        INNER JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.to_basket_id = rb.basket_id
        LEFT JOIN dbo.mkc_dellogs md WITH (nolock) ON md.mkc_id = k.id and md.kinds = 'h4'
        INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = k.ppriceid
        INNER JOIN dbo.recover_basket b WITH(NOLOCK) ON k.from_basket_id = b.id
        LEFT JOIN dbo.recover_basket_extend rbe with(nolock) on rbe.recover_basket_id = b.id
        INNER JOIN dbo.recover_sub s WITH(NOLOCK) ON s.sub_id = b.sub_id
    </sql>

    <sql id="return_order_select_2-1_2-2">
        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType,
        CASE WHEN s.kinds = 1 THEN '2-2' ELSE '2-1' END orderType,
        <choose>
            <when test="isJiuJi">
                CASE WHEN s.kinds = 1 THEN NULL ELSE (CASE WHEN k.first_on_salf_time is not null THEN 1 ELSE 0 END) END goodProductDisplay,
            </when>
            <otherwise>
                CASE WHEN isnull(rs.saleType,0)=0 then 1 else 0 end goodProductDisplay,
            </otherwise>
        </choose>
        CASE WHEN md.check2=1 and md.kinds = 'h4' then 1 else 2 end recTransNew,
        b.replacement_insurance_price replacementInsurancePrice,
        isnull(k.ppriceid,p.ppriceid) ppId,
        CASE WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=0 THEN 1
        WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=1 THEN 2
        WHEN  isnull(s.recover_subType,0)=1 THEN 3
        WHEN  isnull(s.recover_subType,0)=2 THEN 4
        END orderVType,
        s.isnetsub orderVTypeNetsub,
        s.recover_subType orderVTypeSubType,
        p.productid productId,
        b.ismobile salaryProductType,
        <choose>
            <when test="isJiuJi">
                <include refid="returnPrice"/> saleAmount,
                <include refid="returnPrice"/> returnAmount,
            </when>
            <otherwise>
                b.price saleAmount,
                b.price returnAmount,
            </otherwise>
        </choose>
        s.sub_id subId,
        s.areaid areaId,
        k.id mkcId,
        s.pay_time tradeDate,
        null trueProfit,
        null trueSalaryLossRelation,
        CASE WHEN isnull(rbe.pic_status, 0)=1 then 1 else 2 end hasUpCheckImg,
    </sql>

    <sql id="returnPrice">
        case
        when isnull(b.price, 0) - isnull(b.price1, 0) - isnull(b.addCodePrice, 0) - isnull(b.equity_addCodePrice, 0) - isnull(b.sale_out_lp_add_price, 0) >= 0
        then
        isnull(b.price1, 0) + isnull(b.addCodePrice, 0) + isnull(b.sale_out_lp_add_price, 0) + isnull(b.equity_addCodePrice, 0)
        else isnull(b.price, 0)
        end
    </sql>

    <sql id="return_order_select_2-1_2-2_2-3">
        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType,
        '2-3' orderType,
        <choose>
            <when test="isJiuJi">
                CASE WHEN s.kinds = 1 THEN NULL ELSE (CASE WHEN k.first_on_salf_time is not null THEN 1 ELSE 0 END) END goodProductDisplay,
            </when>
            <otherwise>
                CASE WHEN isnull(rs.saleType,0)=0 then 1 else 0 end goodProductDisplay,
            </otherwise>
        </choose>
        CASE WHEN md.check2=1 and md.kinds = 'h4' then 1 else 2 end recTransNew,
        b.replacement_insurance_price replacementInsurancePrice,
        isnull(k.ppriceid,p.ppriceid) ppId,
        CASE WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=0 THEN 1
        WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=1 THEN 2
        WHEN  isnull(s.recover_subType,0)=1 THEN 3
        WHEN  isnull(s.recover_subType,0)=2 THEN 4
        END orderVType,
        s.isnetsub orderVTypeNetsub,
        s.recover_subType orderVTypeSubType,
        p.productid productId,
        b.ismobile salaryProductType,
        <choose>
            <when test="isJiuJi">
                <include refid="returnPrice"/> saleAmount,
                <include refid="returnPrice"/> returnAmount,
            </when>
            <otherwise>
                b.price saleAmount,
                b.price returnAmount,
            </otherwise>
        </choose>
        s.sub_id subId,
        s.areaid areaId,
        k.id mkcId,
    </sql>

    <sql id="return_order_select_special_2-1">
        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        '2-1' orderType,
        <choose>
            <when test="isJiuJi">
                CASE WHEN s.kinds = 1 THEN NULL ELSE (CASE WHEN k.first_on_salf_time is not null THEN 1 ELSE 0 END) END goodProductDisplay,
            </when>
            <otherwise>
                CASE WHEN isnull(rs.saleType,0)=0 then 1 else 0 end goodProductDisplay,
            </otherwise>
        </choose>
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        s.isnetsub orderVTypeNetsub,
        s.recover_subType orderVTypeSubType,
        p.productid productId ,
        b.ismobile salaryProductType,
        ISNULL(rb.price2,rb.price) saleAmount,
        b.price returnAmount,
        s.sub_id subId,
        s.areaid areaId,
        K.id mkcId,
        rs.tradeDate1 tradeDate ,
    </sql>

    <sql id="return_order_select_2-1_2-3">
        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        '2-3' orderType,
        <choose>
            <when test="isJiuJi">
                CASE WHEN s.kinds = 1 THEN NULL ELSE (CASE WHEN k.first_on_salf_time is not null THEN 1 ELSE 0 END) END goodProductDisplay,
            </when>
            <otherwise>
                CASE WHEN isnull(rs.saleType,0)=0 then 1 else 0 end goodProductDisplay,
            </otherwise>
        </choose>
        CASE WHEN md.check2=1 and md.kinds = 'h4' then 1 else 2 end recTransNew,
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        CASE WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=0 THEN 1
        WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=1 THEN 2
        WHEN  isnull(s.recover_subType,0)=1 THEN 3
        WHEN  isnull(s.recover_subType,0)=2 THEN 4
        END orderVType,
        s.isnetsub orderVTypeNetsub,
        s.recover_subType orderVTypeSubType,
        p.productid productId ,
        b.ismobile salaryProductType,

        <choose>
            <when test="isJiuJi">
                <include refid="returnPrice"/> saleAmount,
                <include refid="returnPrice"/> returnAmount,
            </when>
            <otherwise>
                ISNULL(rb.price2,rb.price) saleAmount,
                b.price returnAmount,
            </otherwise>
        </choose>
        s.sub_id subId,
        s.areaid areaId,
        K.id mkcId,
        <choose>
            <when test="isJiuJi">
                (CASE WHEN (k.first_on_salf_time is null and isnull(k.second_costprice,0)=0) or isnull(ishouhou,0)=1 then rs.tradeDate1 else k.first_on_salf_time end) tradeDate,
            </when>
            <otherwise>
                rs.tradeDate1 tradeDate,
            </otherwise>
        </choose>
    </sql>

    <sql id="return_order_profit_2-3">
        <choose>
            <when test="isJiuJi">
                isnull(rb.price2, rb.price) - k.inprice trueProfit,
                IIF((isnull(rb.price2, rb.price) - k.inprice) > 0, 0,1) trueSalaryLossRelation,
            </when>
            <otherwise>
                ISNULL( rb.price2, rb.price ) - ISNULL( k.addprice, 0 ) - ISNULL(k.inprice,0) - ISNULL(rb.auction_fee,0) as trueProfit,
                IIF((ISNULL(rb.price2, rb.price) - ISNULL(k.addprice, 0) - ISNULL(k.inprice, 0) - ISNULL(rb.auction_fee, 0)) > 0, 0, 1) trueSalaryLossRelation,
            </otherwise>
        </choose>
    </sql>

    <sql id="return_order_profit_refund_2-3">
        <choose>
            <when test="isJiuJi">
                (isnull(rb.price2 ,rb.price ) - k.inprice) as trueProfit,
                IIF((isnull(rb.price2, rb.price) - k.inprice) > 0, 0, 1) trueSalaryLossRelation,
            </when>
            <otherwise>
                (isnull(convert( decimal(18,4),  rb.return_price),isnull( rb.price2, rb.price )) - ISNULL( k.addprice,  0) - k.inprice) as trueProfit,
                IIF((isnull(convert(decimal(18, 4), rb.return_price), isnull(rb.price2, rb.price)) - ISNULL(k.addprice, 0) - k.inprice) > 0, 0, 1) trueSalaryLossRelation,
            </otherwise>
        </choose>
    </sql>

    <sql id="recover_marketInfo_user_type_2-3">
        <include refid="return_order_select_2-1_2-3"></include>
        <include refid="return_order_profit_2-3"></include>
        null hasUpCheckImg,
        1 userType,
        NUll as salaryType,
        NULL refundDate,
        0 refunded
        <include refid="return_order_from_2-1_2-3"></include>
        INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.inuser
        WHERE rs.sub_check in (3,9)
        AND ISNULL(rb.isdel,0) = 0
        AND ISNULL(rs.sub_to,'') &lt;&gt; '回收机退回渠道'
        AND u.ch999_id = #{ch999Id}
        AND rs.tradeDate1 BETWEEN #{startDate} AND #{endDate}

        UNION ALL

        <include refid="return_order_select_2-1_2-3"></include>
        <include refid="return_order_profit_refund_2-3"></include>
        null hasUpCheckImg,
        1 userType,
        NUll as salaryType,
        rs.returnDate refundDate,
        1 refunded
        <include refid="return_order_from_2-1_2-3"></include>
        INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.inuser
        WHERE rs.sub_check = 9
        AND ISNULL(rb.isdel,0) = 0
        AND rs.returnDate BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}

        UNION ALL

        <!-- 检测人的良品和转售分开拉取， 转售按转售时间拉，良品（k.first_on_salf_time is not null）按结算时间，不管有没有卖出（按回收拉） -->
        <include refid="return_order_select_2-1_2-3"></include>
        <include refid="return_order_profit_2-3"></include>
        null hasUpCheckImg,
        2 userType,
        NUll as salaryType,
        NULL refundDate,
        0 refunded
        <include refid="return_order_from_2-1_2-3"></include>
        INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.checkUser
        WHERE rs.sub_check in (3,9)
        AND ISNULL(rb.isdel,0) = 0
        AND ISNULL(rs.sub_to,'') &lt;&gt; '回收机退回渠道'
        AND u.ch999_id = #{ch999Id}
        <if test="isJiuJi">
            AND k.first_on_salf_time is null and isnull(k.second_costprice,0)=0 and isnull(ishouhou,0)=0
        </if>
        AND rs.tradeDate1 BETWEEN #{startDate} AND #{endDate}

        <if test="isJiuJi">
            <!-- 九机特殊的查询,上架过并且结算时间在当月不管有没有卖出要在回收毛利规则里面给检测人提成-->
            UNION ALL
            <include refid="return_order_select_2-1_2-2_2-3"></include>
            k.first_on_salf_time tradeDate,
            null trueProfit,
            null trueSalaryLossRelation,
            CASE WHEN isnull(rbe.pic_status, 0)=1 then 1 else 2 end hasUpCheckImg,
            2 userType,
            NUll as salaryType,
            null refundDate,
            0 refunded
            <include refid="return_order_from_2-1_2-2"></include>
            INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.checkUser
            WHERE s.sub_check = 3
            AND ISNULL(b.isdel,0) = 0
            AND ISNULL(k.ishouhou,0) = 0
            AND k.first_on_salf_time is not null
            AND k.first_on_salf_time BETWEEN #{startDate} AND #{endDate}
            AND u.ch999_id = #{ch999Id}

            UNION ALL

            <include refid="return_order_select_2-1_2-2_2-3"></include>
            rs.tradeDate1 tradeDate,
            null trueProfit,
            null trueSalaryLossRelation,
            CASE WHEN isnull(rbe.pic_status, 0)=1 then 1 else 2 end hasUpCheckImg,
            2 userType,
            NUll as salaryType,
            CASE WHEN ISNULL(rs.saleType,0) = 1 AND ISNULL(rs.sub_to,'') = '回收机退回渠道' THEN rs.tradeDate1 ELSE NULL END refundDate,
            1 refunded
            <include refid="return_order_refund_from_2-1_2-2"></include>
            INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.checkUser
            WHERE s.sub_check = 3
            AND ISNULL(b.isdel,0) = 0
            AND ISNULL(k.ishouhou,0) = 0
            AND k.first_on_salf_time is not null
            AND rs.sub_check = 3
            AND ISNULL(rs.saleType,0) = 1
            AND ISNULL(rs.sub_to,'') = '回收机退回渠道'
            AND rs.tradeDate1 BETWEEN #{startDate} AND #{endDate}
            AND u.ch999_id =#{ch999Id}
        </if>

        UNION ALL

        <include refid="return_order_select_2-1_2-3"></include>
        <include refid="return_order_profit_refund_2-3"></include>
        null hasUpCheckImg,
        2 userType,
        NUll as salaryType,
        rs.returnDate refundDate,
        1 refunded
        <include refid="return_order_from_2-1_2-3"></include>
        INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.checkUser
        WHERE rs.sub_check = 9
        AND ISNULL(rb.isdel,0) = 0
        AND rs.returnDate BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}
        <if test="isJiuJi">
            and k.first_on_salf_time is null and isnull(k.second_costprice,0)=0 and isnull(ishouhou,0)=0
        </if>

        UNION ALL

        <include refid="return_order_select_2-1_2-3"></include>
        <include refid="return_order_profit_2-3"></include>
        CASE WHEN isnull(rbe.pic_status, 0)=1 then 1 else 2 end hasUpCheckImg,
        5 userType,
        NUll as salaryType,
        NULL refundDate,
        0 refunded
        <include refid="return_order_from_2-1_2-3"></include>
        INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.shop_check_user
        WHERE rs.sub_check in (3,9)
        AND ISNULL(rb.isdel,0) = 0
        AND ISNULL(rs.sub_to,'') &lt;&gt; '回收机退回渠道'
        AND u.ch999_id = #{ch999Id}
        AND rs.tradeDate1 BETWEEN #{startDate} AND #{endDate}

        UNION ALL

        <include refid="return_order_select_2-1_2-3"></include>
        <include refid="return_order_profit_refund_2-3"></include>
        CASE WHEN isnull(rbe.pic_status, 0)=1 then 1 else 2 end hasUpCheckImg,
        5 userType,
        NUll as salaryType,
        rs.returnDate refundDate,
        1 refunded
        <include refid="return_order_from_2-1_2-3"></include>
        INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.shop_check_user
        WHERE rs.sub_check = 9
        AND ISNULL(rb.isdel,0) = 0
        AND rs.returnDate BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}

        UNION ALL

        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        '2-3' orderType,
        0 goodProductDisplay,
        CASE WHEN md.check2=1 and md.kinds = 'h4' then 1 else 2 end recTransNew,
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        CASE WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=0 THEN 1
        WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=1 THEN 2
        WHEN  isnull(s.recover_subType,0)=1 THEN 3
        WHEN  isnull(s.recover_subType,0)=2 THEN 4
        END orderVType,
        s.isnetsub orderVTypeNetsub,
        s.recover_subType orderVTypeSubType,
        p.productid productId ,
        b.ismobile salaryProductType,
        <choose>
            <when test="isJiuJi">
                <include refid="returnPrice"/> saleAmount,
                <include refid="returnPrice"/> returnAmount,
            </when>
            <otherwise>
                md.price1 saleAmount,
                b.price returnAmount,
            </otherwise>
        </choose>
        s.sub_id subId,
        s.areaid areaId,
        k.id mkcId,
        md.check2dtime tradeDate ,
        (md.price2 - md.price1) as trueProfit,
        CASE WHEN (md.price2 - md.price1) &lt; 0 THEN 1 ELSE 0 END trueSalaryLossRelation ,
        null hasUpCheckImg,
        1 userType,
        NUll as salaryType,
        NULL refundDate,
        0 refunded
        FROM dbo.mkc_dellogs md WITH ( nolock )
        INNER JOIN dbo.recover_mkc k WITH ( nolock ) ON md.mkc_id = k.id
        INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = k.ppriceid
        INNER JOIN dbo.recover_basket b WITH ( nolock ) ON b.id = k.from_basket_id
        INNER JOIN dbo.recover_sub s WITH ( nolock ) ON s.sub_id = b.sub_id
        INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.inuser
        where isnull(md.check2,0) = 1 and md.check2dtime BETWEEN #{startDate} AND #{endDate}
        and md.kinds  = 'h4'
        <if test="isJiuJi">
            and k.first_on_salf_time is null
        </if>
        AND u.ch999_id = #{ch999Id}

        UNION ALL

        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        '2-3' orderType,
        0 goodProductDisplay,
        CASE WHEN md.check2=1 and md.kinds = 'h4' then 1 else 2 end recTransNew,
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        CASE WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=0 THEN 1
        WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=1 THEN 2
        WHEN  isnull(s.recover_subType,0)=1 THEN 3
        WHEN  isnull(s.recover_subType,0)=2 THEN 4
        END orderVType,
        s.isnetsub orderVTypeNetsub,
        s.recover_subType orderVTypeSubType,
        p.productid productId ,
        b.ismobile salaryProductType,
        <choose>
            <when test="isJiuJi">
                <include refid="returnPrice"/> saleAmount,
                <include refid="returnPrice"/> returnAmount,
            </when>
            <otherwise>
                md.price1 saleAmount,
                b.price returnAmount,
            </otherwise>
        </choose>
        s.sub_id subId,
        s.areaid areaId,
        k.id mkcId,
        md.check2dtime tradeDate ,
        (md.price2 - md.price1) as trueProfit,
        CASE WHEN (md.price2 - md.price1) &lt; 0 THEN 1 ELSE 0 END trueSalaryLossRelation ,
        null hasUpCheckImg,
        2 userType,
        NUll as salaryType,
        NULL refundDate,
        0 refunded
        FROM dbo.mkc_dellogs md WITH ( nolock )
        INNER JOIN dbo.recover_mkc k WITH ( nolock ) ON md.mkc_id = k.id
        INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = k.ppriceid
        INNER JOIN dbo.recover_basket b WITH ( nolock ) ON b.id = k.from_basket_id
        INNER JOIN dbo.recover_sub s WITH ( nolock ) ON s.sub_id = b.sub_id
        INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.checkUser
        where isnull(md.check2,0) = 1 and md.check2dtime BETWEEN #{startDate} AND #{endDate}
        and md.kinds  = 'h4'
        <if test="isJiuJi">
            and k.first_on_salf_time is null
        </if>
        AND u.ch999_id = #{ch999Id}

        UNION ALL

        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        '2-3' orderType,
        0 goodProductDisplay,
        CASE WHEN md.check2=1 and md.kinds = 'h4' then 1 else 2 end recTransNew,
        b.replacement_insurance_price replacementInsurancePrice,
        p.ppriceid ppId,
        CASE WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=0 THEN 1
        WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=1 THEN 2
        WHEN  isnull(s.recover_subType,0)=1 THEN 3
        WHEN  isnull(s.recover_subType,0)=2 THEN 4
        END orderVType,
        s.isnetsub orderVTypeNetsub,
        s.recover_subType orderVTypeSubType,
        p.productid productId ,
        b.ismobile salaryProductType,
        <choose>
            <when test="isJiuJi">
                <include refid="returnPrice"/> saleAmount,
                <include refid="returnPrice"/> returnAmount,
            </when>
            <otherwise>
                md.price1 saleAmount,
                b.price returnAmount,
            </otherwise>
        </choose>
        s.sub_id subId,
        s.areaid areaId,
        k.id mkcId,
        md.check2dtime tradeDate ,
        (md.price2 - md.price1) as trueProfit,
        CASE WHEN (md.price2 - md.price1) &lt; 0 THEN 1 ELSE 0 END trueSalaryLossRelation ,
        CASE WHEN isnull(rbe.pic_status, 0)=1 then 1 else 2 end hasUpCheckImg,
        5 userType,
        NUll as salaryType,
        NULL refundDate,
        0 refunded
        FROM dbo.mkc_dellogs md WITH ( nolock )
        INNER JOIN dbo.recover_mkc k WITH ( nolock ) ON md.mkc_id = k.id
        INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = k.ppriceid
        INNER JOIN dbo.recover_basket b WITH ( nolock ) ON b.id = k.from_basket_id
        LEFT JOIN dbo.recover_basket_extend rbe with(nolock) on rbe.recover_basket_id = b.id
        INNER JOIN dbo.recover_sub s WITH ( nolock ) ON s.sub_id = b.sub_id
        INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.shop_check_user
        where isnull(md.check2,0) = 1 and md.check2dtime BETWEEN #{startDate} AND #{endDate}
        and md.kinds  = 'h4'
        <if test="isJiuJi">
            and k.first_on_salf_time is null
        </if>
        AND u.ch999_id = #{ch999Id}
    </sql>

    <select id="listReturnOrder" resultType="com.jiuji.oa.oacore.salary.entity.vo.RetrunOrderEx">
        <include refid="return_order_select_2-1_2-2"></include>
        1 userType,
        3 as salaryType,
        null refundDate,
        0 refunded
        <include refid="return_order_from_2-1_2-2"></include>
        INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.inuser
        WHERE s.sub_check = 3
        AND ISNULL(b.isdel,0) = 0
        AND ISNULL(k.ishouhou,0) = 0
        AND isnull(s.pay_time, s.ruku_time) BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id =#{ch999Id}

        UNION ALL

        <include refid="return_order_select_2-1_2-2"></include>
        1 userType,
        3 as salaryType,
        CASE WHEN ISNULL(rs.saleType,0) = 1 AND ISNULL(rs.sub_to,'') = '回收机退回渠道' THEN rs.tradeDate1 ELSE NULL END refundDate,
        1 refunded
        <include refid="return_order_refund_from_2-1_2-2"></include>
        INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.inuser
        WHERE s.sub_check = 3
        AND ISNULL(b.isdel,0) = 0
        AND ISNULL(k.ishouhou,0) = 0
        AND rs.sub_check = 3
        AND ISNULL(rs.saleType,0) = 1
        AND ISNULL(rs.sub_to,'') = '回收机退回渠道'
        AND rs.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}

        UNION ALL

        <include refid="return_order_select_2-1_2-2"></include>
        2 userType,
        3 as salaryType,
        null refundDate,
        0 refunded
        <include refid="return_order_from_2-1_2-2"></include>
        INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.checkUser
        WHERE s.sub_check = 3
        AND ISNULL(b.isdel,0) = 0
        AND ISNULL(k.ishouhou,0) = 0
        AND isnull(s.pay_time, s.ruku_time) BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id = #{ch999Id}

        UNION ALL

        <include refid="return_order_select_2-1_2-2"></include>
        2 userType,
        3 as salaryType,
        CASE WHEN ISNULL(rs.saleType,0) = 1 AND ISNULL(rs.sub_to,'') = '回收机退回渠道' THEN rs.tradeDate1 ELSE NULL END refundDate,
        1 refunded
        <include refid="return_order_refund_from_2-1_2-2"></include>
        INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.checkUser
        WHERE s.sub_check = 3
        AND ISNULL(b.isdel,0) = 0
        AND ISNULL(k.ishouhou,0) = 0
        AND rs.sub_check = 3
        AND ISNULL(rs.saleType,0) = 1
        AND ISNULL(rs.sub_to,'') = '回收机退回渠道'
        AND rs.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id =#{ch999Id}

        UNION ALL

        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType,
        CASE WHEN s.kinds = 1 THEN '2-2' ELSE '2-1' END orderType,
        <choose>
            <when test="isJiuJi">
                CASE WHEN s.kinds = 1 THEN NULL ELSE (CASE WHEN k.first_on_salf_time is not null THEN 1 ELSE 0 END) END goodProductDisplay,
            </when>
            <otherwise>
                CASE WHEN isnull(rs.saleType,0)=0 then 1 else 0 end goodProductDisplay,
            </otherwise>
        </choose>
        CASE WHEN md.check2=1 and md.kinds = 'h4' then 1 else 2 end recTransNew,
        b.replacement_insurance_price replacementInsurancePrice,
        isnull(k.ppriceid,p.ppriceid) ppId,
        CASE WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=0 THEN 1
        WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=1 THEN 2
        WHEN  isnull(s.recover_subType,0)=1 THEN 3
        WHEN  isnull(s.recover_subType,0)=2 THEN 4
        END orderVType,
        s.isnetsub orderVTypeNetsub,
        s.recover_subType orderVTypeSubType,
        p.productid productId,
        b.ismobile salaryProductType,
        <choose>
            <when test="isJiuJi">
                <include refid="returnPrice"/> saleAmount,
                <include refid="returnPrice"/> returnAmount,
            </when>
            <otherwise>
                b.price saleAmount,
                b.price returnAmount,
            </otherwise>
        </choose>
        s.sub_id subId,
        s.areaid areaId,
        k.id mkcId,
        rta.sendtime tradeDate,
        null trueProfit,
        null trueSalaryLossRelation,
        CASE WHEN isnull(rbe.pic_status, 0)=1 then 1 else 2 end hasUpCheckImg,
        5 userType,
        3 as salaryType,
        null refundDate ,
        0 refunded
        <include refid="return_order_from_2-1_2-2"></include>
        outer apply (select top 1 sendtime from dbo.recover_toarea ra with(nolock) where ra.mkc_id=k.id) rta
        INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.shop_check_user
        WHERE s.sub_check = 3
        AND ISNULL(b.isdel,0) = 0
        AND ISNULL(k.ishouhou,0) = 0
        AND rta.sendtime BETWEEN #{startDate} AND #{endDate}
        AND u.ch999_id =#{ch999Id}


        <if test="isJiuJi">
            union all

            <include refid="return_order_select_2-1_2-2"></include>
            13 userType,
            2 as salaryType,
            null refundDate,
            0 refunded
            <include refid="return_order_from_2-1_2-2"></include>
            INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = s.recover_ch999name
            WHERE s.sub_check = 3
            AND ISNULL(b.isdel,0) = 0
            AND ISNULL(k.ishouhou,0) = 0
            AND isnull(s.pay_time, s.ruku_time) BETWEEN #{startDate} AND #{endDate}
            AND u.ch999_id = #{ch999Id}

            UNION ALL

            <include refid="return_order_select_2-1_2-2"></include>
            13 userType,
            2 as salaryType,
            CASE WHEN ISNULL(rs.saleType,0) = 1 AND ISNULL(rs.sub_to,'') = '回收机退回渠道' THEN rs.tradeDate1 ELSE NULL END refundDate,
            1 refunded
            <include refid="return_order_refund_from_2-1_2-2"></include>
            INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = s.recover_ch999name
            WHERE s.sub_check = 3
            AND ISNULL(b.isdel,0) = 0
            AND ISNULL(k.ishouhou,0) = 0
            AND rs.sub_check = 3
            AND ISNULL(rs.saleType,0) = 1
            AND ISNULL(rs.sub_to,'') = '回收机退回渠道'
            AND rs.tradeDate1 BETWEEN #{startDate} AND #{endDate}
            AND u.ch999_id =#{ch999Id}

            union all

            <include refid="return_order_select_2-1_2-2"></include>
            13 userType,
            1 as salaryType,
            null refundDate,
            0 refunded
            FROM dbo.recover_sub s WITH(NOLOCK)
            INNER JOIN dbo.recover_basket b WITH(NOLOCK) ON s.sub_id = b.sub_id
            LEFT JOIN dbo.recover_sub_extend se with(nolock) on se.sub_id = b.sub_id
            INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
            LEFT JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.from_basket_id = b.id
            LEFT JOIN dbo.mkc_dellogs md WITH (nolock) ON md.mkc_id = k.id and md.kinds = 'h4'
            LEFT JOIN dbo.recover_basket_extend rbe with(nolock) on rbe.recover_basket_id = b.id
            INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = s.recover_ch999name
            WHERE s.sub_check in(3,4) and s.sub_delivery = 3 and isnull(se.take_product_code_status,0)=1
            AND s.dtime BETWEEN #{startDate} AND #{endDate}
            AND u.ch999_id =#{ch999Id}

            <!--
                        union all

                                    SELECT
                                    b.id basketId,
                                    p.brandID brandId,
                                    p.cid,
                                    -1 COUNT,
                                    s.sub_delivery orderDelivery,
                                    'recycle' orderKind,
                                    s.sub_pay orderPayType ,
                                    '2-1' orderType,
                                    <choose>
                                        <when test="isJiuJi">
                                            CASE WHEN s.kinds = 1 THEN NULL ELSE (CASE WHEN k.first_on_salf_time is not null THEN 1 ELSE 0 END) END goodProductDisplay,
                                        </when>
                                        <otherwise>
                                            CASE WHEN isnull(rs.saleType,0)=0 then 1 else 0 end goodProductDisplay,
                                        </otherwise>
                                    </choose>
                                    b.replacement_insurance_price replacementInsurancePrice,
                                    k.ppriceid ppId,
                                    CASE WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=0 THEN 1
                                    WHEN isnull(s.isnetsub,0) =0 AND isnull(s.recover_subType,0)=1 THEN 2
                                    WHEN  isnull(s.recover_subType,0)=1 THEN 3
                                    WHEN  isnull(s.recover_subType,0)=2 THEN 4
                                    END orderVType,
                                    s.isnetsub orderVTypeNetsub,
                                    s.recover_subType orderVTypeSubType,
                                    p.productid productId ,
                                    b.ismobile salaryProductType,
                                    -b.price saleAmount,
                                    -b.price returnAmount,
                                    s.sub_id subId,
                                    K.id mkcId,
                                    rs.tradeDate1 tradeDate ,
                                    null trueProfit,
                                    null trueSalaryLossRelation,
                                    null hasUpCheckImg,
                                    1 userType,
                                    2 as salaryType,
                                    NULL refundDate,
                                    NULL refunded
                                    FROM dbo.recover_marketInfo rs with(nolock)
                                    INNER JOIN dbo.recover_marketSubInfo rb with(nolock) ON rs.sub_id = rb.sub_id
                                    INNER JOIN dbo.recover_mkc k WITH(NOLOCK) ON k.to_basket_id = rb.basket_id
                                    INNER JOIN dbo.recover_basket b WITH(NOLOCK) ON k.from_basket_id = b.id
                                    INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = b.ppriceid
                                    INNER JOIN dbo.recover_sub s WITH(NOLOCK) ON s.sub_id = b.sub_id
                                    INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON u.ch999_name = b.inuser
                                    WHERE rs.sub_check in (3,9)
                                    AND ISNULL(k.ishouhou,0) = 0
                                    AND ISNULL(rb.isdel,0) = 0
                                    AND ISNULL(rs.sub_to,'') &lt;&gt; '回收机退回渠道'
                                    AND isnull(s.pay_time, s.ruku_time)>'2023-08-15 00:00:00'
                                    AND rs.tradeDate1 BETWEEN #{startDate} AND #{endDate}
                                    AND u.ch999_id = #{ch999Id}
                                    and
                                    <choose>
                                        <when test="isJiuJi">
                                            (isnull(rb.price2, rb.price) - k.inprice)
                                        </when>
                                        <otherwise>
                                            (ISNULL( rb.price2, rb.price ) - ISNULL( k.addprice, 0 ) - ISNULL(k.inprice,0) - ISNULL(rb.auction_fee,0))
                                        </otherwise>
                                    </choose>
                                    &lt;-100 -->
        </if>

        union all

        <include refid="recover_marketInfo_user_type_2-3"></include>
    </select>

    <select id="getRecoverDiffUserRateNewDto" resultType="com.jiuji.oa.salary.RecoverDiffUserRateNewDto">

        select 1 userType,ch999_id,
        CASE
        WHEN t1.回收量 > 0 THEN CAST(ROUND(t1.回收差异量 * 100.0 / t1.回收量, 2) AS DECIMAL(18, 2))
        ELSE 0 END returnDifferenceRateUser,
        CASE
        WHEN t1.回收总量 > 0 THEN CAST(ROUND(t1.回收差异量 * 100.0 / t1.回收总量, 2) AS DECIMAL(18, 2))
        ELSE 0 END returnTotalDifferenceRateUser
        from(

        select ch999_id,
        SUM(CASE WHEN t.kind = 1 THEN 1 ELSE 0 END) 回收量,
        SUM(CASE WHEN t.kind = 2 and ISNULL(diffopt, '') != '' then 1 else 0 end) 回收差异量,
        SUM(CASE WHEN t.kind = 2 THEN 1 ELSE 0 END) 回收总量
        from (
        select *
        from (
        select 1 kind,
        ch999_id,
        mkcId,
        diffopt,
        dtime,
        RANK() OVER (PARTITION BY mkcId ORDER BY dtime asc) rank
        from (
        SELECT u.ch999_id, m.id as mkcId, mrs.diffopt, mrs.create_time as dtime
        FROM dbo.recover_mkc m WITH (nolock)
        INNER JOIN dbo.recover_basket b WITH (nolock) ON b.id = m.from_basket_id
        INNER JOIN dbo.ch999_user u WITH (NOLOCK) ON u.ch999_name = b.inuser
        INNER JOIN dbo.recover_sub s WITH (nolock) ON s.sub_id = b.sub_id
        INNER JOIN dbo.recover_mkc_first_shelf mrs WITH (nolock) on mrs.mkc_id = m.id
        WHERE isnull(s.sub_check, 0) = 3
        and isnull(b.isdel, 0) = 0
        and isnull(m.ishouhou, 0) = 0
        and ISNULL(b.price, 0) >= 21
        and mrs.create_time BETWEEN #{startDate} AND #{endDate}
        and u.ch999_id=#{ch999Id}
        <if test="isJiuJi">
            and s.pay_time >= '2023-04-01'
        </if>

        union ALL

        SELECT u.ch999_id,
        m.id                          as mkcId,
        case
        when b.checkResultId is not null then m.diffopt
        else rbe.shop_diffopt end as diffopt,
        rmi.tradeDate1                as dtime
        FROM dbo.recover_mkc m WITH (nolock)
        INNER JOIN dbo.recover_basket b WITH (nolock) ON b.id = m.from_basket_id
        INNER JOIN dbo.ch999_user u WITH (NOLOCK) ON u.ch999_name = b.inuser
        INNER JOIN dbo.recover_sub s WITH (nolock) ON s.sub_id = b.sub_id
        INNER join dbo.recover_marketSubInfo rms WITH (nolock) on rms.basket_id = m.to_basket_id
        INNER join dbo.recover_marketInfo rmi WITH (nolock) on rmi.sub_id = rms.sub_id
        LEFT JOIN dbo.recover_mkc_first_shelf mrs WITH (nolock) on mrs.mkc_id = m.id
        LEFT join dbo.recover_basket_extend rbe WITH (nolock) on rbe.recover_basket_id = b.id
        WHERE isnull(s.sub_check, 0) = 3
        and isnull(b.isdel, 0) = 0
        and isnull(m.ishouhou, 0) = 0
        and isnull(rmi.saleType, 0) = 1
        and mrs.mkc_id is null
        and (rmi.sub_to not in ('回收机退回渠道', '拍靓机', '拍机堂', '拍机堂_POP') and rmi.inuser != '拍机堂')
        and isnull(b.checkResultId, b.shop_check_result_id) is not null
        and ISNULL(b.price, 0) >= 21
        and rmi.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        and u.ch999_id=#{ch999Id}
        <if test="isJiuJi">
            and s.pay_time >= '2023-04-01'
        </if>
        ) a1
        ) a2
        where a2.rank = 1

        union all

        select *
        from (
        select 2 kind,
        ch999_id,
        mkcId,
        diffopt,
        dtime,
        RANK() OVER (PARTITION BY mkcId ORDER BY dtime asc) rank
        from (
        SELECT u.ch999_id, m.id as mkcId, mrs.diffopt, mrs.create_time as dtime
        FROM dbo.recover_mkc m WITH (nolock)
        INNER JOIN dbo.recover_basket b WITH (nolock) ON b.id = m.from_basket_id
        INNER JOIN dbo.ch999_user u WITH (NOLOCK) ON u.ch999_name = b.inuser
        INNER JOIN dbo.recover_sub s WITH (nolock) ON s.sub_id = b.sub_id
        INNER JOIN dbo.recover_mkc_first_shelf mrs WITH (nolock) on mrs.mkc_id = m.id
        WHERE isnull(s.sub_check, 0) = 3
        and isnull(b.isdel, 0) = 0
        and isnull(m.ishouhou, 0) = 0
        and mrs.create_time BETWEEN #{startDate} AND #{endDate}
        and u.ch999_id=#{ch999Id}
        <if test="isJiuJi">
            and s.pay_time >= '2023-04-01'
        </if>

        union ALL

        SELECT u.ch999_id,
        m.id                          as mkcId,
        case
        when b.checkResultId is not null then m.diffopt
        else rbe.shop_diffopt end as diffopt,
        rmi.tradeDate1                as dtime
        FROM dbo.recover_mkc m WITH (nolock)
        INNER JOIN dbo.recover_basket b WITH (nolock) ON b.id = m.from_basket_id
        INNER JOIN dbo.ch999_user u WITH (NOLOCK) ON u.ch999_name = b.inuser
        INNER JOIN dbo.recover_sub s WITH (nolock) ON s.sub_id = b.sub_id
        INNER join dbo.recover_marketSubInfo rms WITH (nolock) on rms.basket_id = m.to_basket_id
        INNER join dbo.recover_marketInfo rmi WITH (nolock) on rmi.sub_id = rms.sub_id
        LEFT JOIN dbo.recover_mkc_first_shelf mrs WITH (nolock) on mrs.mkc_id = m.id
        LEFT join dbo.recover_basket_extend rbe WITH (nolock) on rbe.recover_basket_id = b.id
        WHERE isnull(s.sub_check, 0) = 3
        and isnull(b.isdel, 0) = 0
        and isnull(m.ishouhou, 0) = 0
        and isnull(rmi.saleType, 0) = 1
        and mrs.mkc_id is null
        and (rmi.sub_to not in ('回收机退回渠道', '拍靓机', '拍机堂', '拍机堂_POP') and rmi.inuser != '拍机堂')
        and isnull(b.checkResultId, b.shop_check_result_id) is not null
        and rmi.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        and u.ch999_id=#{ch999Id}
        <if test="isJiuJi">
            and s.pay_time >= '2023-04-01'
        </if>
        ) a1
        ) a2
        where a2.rank = 1

        ) t
        GROUP by ch999_id ) t1

        union all

        select 2 userType,ch999_id,
        CASE
        WHEN t1.回收量 > 0 THEN CAST(ROUND(t1.回收差异量 * 100.0 / t1.回收量, 2) AS DECIMAL(18, 2))
        ELSE 0 END returnDifferenceRateUser,
        CASE
        WHEN t1.回收总量 > 0 THEN CAST(ROUND(t1.回收差异量 * 100.0 / t1.回收总量, 2) AS DECIMAL(18, 2))
        ELSE 0 END returnTotalDifferenceRateUser
        from(
        select ch999_id,
        SUM(CASE WHEN t.kind = 1 THEN 1 ELSE 0 END) 回收量,
        SUM(CASE WHEN t.kind = 2 and ISNULL(diffopt, '') != '' then 1 else 0 end) 回收差异量,
        SUM(CASE WHEN t.kind = 2 THEN 1 ELSE 0 END) 回收总量
        from (
        select *
        from (
        select 1 kind,
        ch999_id,
        mkcId,
        diffopt,
        dtime,
        RANK() OVER (PARTITION BY mkcId ORDER BY dtime asc) rank
        from (
        SELECT u.ch999_id, m.id as mkcId, mrs.diffopt, mrs.create_time as dtime
        FROM dbo.recover_mkc m WITH (nolock)
        INNER JOIN dbo.recover_basket b WITH (nolock) ON b.id = m.from_basket_id
        INNER JOIN dbo.ch999_user u WITH (NOLOCK) ON u.ch999_name = b.checkUser
        INNER JOIN dbo.recover_sub s WITH (nolock) ON s.sub_id = b.sub_id
        INNER JOIN dbo.recover_mkc_first_shelf mrs WITH (nolock) on mrs.mkc_id = m.id
        WHERE isnull(s.sub_check, 0) = 3
        and isnull(b.isdel, 0) = 0
        and isnull(m.ishouhou, 0) = 0
        and ISNULL(b.price, 0) >= 21
        and mrs.create_time BETWEEN #{startDate} AND #{endDate}
        and u.ch999_id=#{ch999Id}
        <if test="isJiuJi">
            and s.pay_time >= '2023-04-01'
        </if>

        union ALL

        SELECT u.ch999_id,
        m.id                          as mkcId,
        case
        when b.checkResultId is not null then m.diffopt
        else rbe.shop_diffopt end as diffopt,
        rmi.tradeDate1                as dtime
        FROM dbo.recover_mkc m WITH (nolock)
        INNER JOIN dbo.recover_basket b WITH (nolock) ON b.id = m.from_basket_id
        INNER JOIN dbo.ch999_user u WITH (NOLOCK) ON u.ch999_name = b.checkUser
        INNER JOIN dbo.recover_sub s WITH (nolock) ON s.sub_id = b.sub_id
        INNER join dbo.recover_marketSubInfo rms WITH (nolock) on rms.basket_id = m.to_basket_id
        INNER join dbo.recover_marketInfo rmi WITH (nolock) on rmi.sub_id = rms.sub_id
        LEFT JOIN dbo.recover_mkc_first_shelf mrs WITH (nolock) on mrs.mkc_id = m.id
        LEFT join dbo.recover_basket_extend rbe WITH (nolock) on rbe.recover_basket_id = b.id
        WHERE isnull(s.sub_check, 0) = 3
        and isnull(b.isdel, 0) = 0
        and isnull(m.ishouhou, 0) = 0
        and isnull(rmi.saleType, 0) = 1
        and mrs.mkc_id is null
        and (rmi.sub_to not in ('回收机退回渠道', '拍靓机', '拍机堂', '拍机堂_POP') and rmi.inuser != '拍机堂')
        and isnull(b.checkResultId, b.shop_check_result_id) is not null
        and ISNULL(b.price, 0) >= 21
        and rmi.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        and u.ch999_id=#{ch999Id}
        <if test="isJiuJi">
            and s.pay_time >= '2023-04-01'
        </if>
        ) a1
        ) a2
        where a2.rank = 1

        union all

        select *
        from (
        select 2 kind,
        ch999_id,
        mkcId,
        diffopt,
        dtime,
        RANK() OVER (PARTITION BY mkcId ORDER BY dtime asc) rank
        from (
        SELECT u.ch999_id, m.id as mkcId, mrs.diffopt, mrs.create_time as dtime
        FROM dbo.recover_mkc m WITH (nolock)
        INNER JOIN dbo.recover_basket b WITH (nolock) ON b.id = m.from_basket_id
        INNER JOIN dbo.ch999_user u WITH (NOLOCK) ON u.ch999_name = b.checkUser
        INNER JOIN dbo.recover_sub s WITH (nolock) ON s.sub_id = b.sub_id
        INNER JOIN dbo.recover_mkc_first_shelf mrs WITH (nolock) on mrs.mkc_id = m.id
        WHERE isnull(s.sub_check, 0) = 3
        and isnull(b.isdel, 0) = 0
        and isnull(m.ishouhou, 0) = 0
        and mrs.create_time BETWEEN #{startDate} AND #{endDate}
        and u.ch999_id=#{ch999Id}
        <if test="isJiuJi">
            and s.pay_time >= '2023-04-01'
        </if>

        union ALL

        SELECT u.ch999_id,
        m.id                          as mkcId,
        case
        when b.checkResultId is not null then m.diffopt
        else rbe.shop_diffopt end as diffopt,
        rmi.tradeDate1                as dtime
        FROM dbo.recover_mkc m WITH (nolock)
        INNER JOIN dbo.recover_basket b WITH (nolock) ON b.id = m.from_basket_id
        INNER JOIN dbo.ch999_user u WITH (NOLOCK) ON u.ch999_name = b.checkUser
        INNER JOIN dbo.recover_sub s WITH (nolock) ON s.sub_id = b.sub_id
        INNER join dbo.recover_marketSubInfo rms WITH (nolock) on rms.basket_id = m.to_basket_id
        INNER join dbo.recover_marketInfo rmi WITH (nolock) on rmi.sub_id = rms.sub_id
        LEFT JOIN dbo.recover_mkc_first_shelf mrs WITH (nolock) on mrs.mkc_id = m.id
        LEFT join dbo.recover_basket_extend rbe WITH (nolock) on rbe.recover_basket_id = b.id
        WHERE isnull(s.sub_check, 0) = 3
        and isnull(b.isdel, 0) = 0
        and isnull(m.ishouhou, 0) = 0
        and isnull(rmi.saleType, 0) = 1
        and mrs.mkc_id is null
        and (rmi.sub_to not in ('回收机退回渠道', '拍靓机', '拍机堂', '拍机堂_POP') and rmi.inuser != '拍机堂')
        and isnull(b.checkResultId, b.shop_check_result_id) is not null
        and rmi.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        and u.ch999_id=#{ch999Id}
        <if test="isJiuJi">
            and s.pay_time >= '2023-04-01'
        </if>
        ) a1
        ) a2
        where a2.rank = 1

        ) t
        GROUP by ch999_id
        ) t1
    </select>

    <select id="getRecoverDiffAreaRateNewDto" resultType="com.jiuji.oa.salary.RecoverDiffAreaRateNewDto">
        select areaid as areaId,
        CASE
        WHEN 回收量 > 0 THEN CAST(ROUND(回收差异量 * 100.0 / 回收量, 2) AS DECIMAL(18, 2))
        ELSE 0 END returnDifferenceRateArea,
        CASE
        WHEN 回收总量 > 0 THEN CAST(ROUND(回收差异量 * 100.0 / 回收总量, 2) AS DECIMAL(18, 2))
        ELSE 0 END returnTotalDifferenceRateArea
        from (

        select areaid,
        SUM(CASE WHEN kind = 1 THEN 1 ELSE 0 END) 回收量,
        SUM(CASE WHEN kind = 2 and ISNULL(diffopt, '') != '' then 1 else 0 end) 回收差异量,
        SUM(CASE WHEN kind = 2 THEN 1 ELSE 0 END) 回收总量
        from (
        select *
        from (
        select 1 kind,
        areaid,
        mkcId,
        diffopt,
        dtime,
        RANK() OVER (PARTITION BY mkcId ORDER BY dtime asc) rank
        from (
        SELECT s.areaid, m.id as mkcId, mrs.diffopt, mrs.create_time as dtime
        FROM dbo.recover_mkc m WITH (nolock)
        INNER JOIN dbo.recover_basket b WITH (nolock) ON b.id = m.from_basket_id
        INNER JOIN dbo.recover_sub s WITH (nolock) ON s.sub_id = b.sub_id
        INNER JOIN dbo.recover_mkc_first_shelf mrs WITH (nolock) on mrs.mkc_id = m.id
        WHERE isnull(s.sub_check, 0) = 3
        and isnull(b.isdel, 0) = 0
        and isnull(m.ishouhou, 0) = 0
        and ISNULL(b.price, 0) >= 21
        and mrs.create_time BETWEEN #{startDate} AND #{endDate}
        <if test="isJiuJi">
            and s.pay_time >= '2023-04-01'
        </if>

        union ALL

        SELECT s.areaid,
        m.id                          as mkcId,
        case
        when b.checkResultId is not null then m.diffopt
        else rbe.shop_diffopt end as diffopt,
        rmi.tradeDate1                as dtime
        FROM dbo.recover_mkc m WITH (nolock)
        INNER JOIN dbo.recover_basket b WITH (nolock) ON b.id = m.from_basket_id
        INNER JOIN dbo.recover_sub s WITH (nolock) ON s.sub_id = b.sub_id
        INNER join dbo.recover_marketSubInfo rms WITH (nolock) on rms.basket_id = m.to_basket_id
        INNER join dbo.recover_marketInfo rmi WITH (nolock) on rmi.sub_id = rms.sub_id
        LEFT JOIN dbo.recover_mkc_first_shelf mrs WITH (nolock) on mrs.mkc_id = m.id
        LEFT join dbo.recover_basket_extend rbe WITH (nolock) on rbe.recover_basket_id = b.id
        WHERE isnull(s.sub_check, 0) = 3
        and isnull(b.isdel, 0) = 0
        and isnull(m.ishouhou, 0) = 0
        and isnull(rmi.saleType, 0) = 1
        and mrs.mkc_id is null
        and (rmi.sub_to not in ('回收机退回渠道', '拍靓机', '拍机堂', '拍机堂_POP') and rmi.inuser != '拍机堂')
        and isnull(b.checkResultId, b.shop_check_result_id) is not null
        and ISNULL(b.price, 0) >= 21
        and rmi.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        <if test="isJiuJi">
            and s.pay_time >= '2023-04-01'
        </if>
        ) a1
        ) a2
        where a2.rank = 1

        union all

        select *
        from (
        select 2 kind,
        areaid,
        mkcId,
        diffopt,
        dtime,
        RANK() OVER (PARTITION BY mkcId ORDER BY dtime asc) rank
        from (
        SELECT s.areaid, m.id as mkcId, mrs.diffopt, mrs.create_time as dtime
        FROM dbo.recover_mkc m WITH (nolock)
        INNER JOIN dbo.recover_basket b WITH (nolock) ON b.id = m.from_basket_id
        INNER JOIN dbo.ch999_user u WITH (NOLOCK) ON u.ch999_name = b.inuser
        INNER JOIN dbo.recover_sub s WITH (nolock) ON s.sub_id = b.sub_id
        INNER JOIN dbo.recover_mkc_first_shelf mrs WITH (nolock) on mrs.mkc_id = m.id
        WHERE isnull(s.sub_check, 0) = 3
        and isnull(b.isdel, 0) = 0
        and isnull(m.ishouhou, 0) = 0
        and mrs.create_time BETWEEN #{startDate} AND #{endDate}
        <if test="isJiuJi">
            and s.pay_time >= '2023-04-01'
        </if>

        union ALL

        SELECT s.areaid,
        m.id                          as mkcId,
        case
        when b.checkResultId is not null then m.diffopt
        else rbe.shop_diffopt end as diffopt,
        rmi.tradeDate1                as dtime
        FROM dbo.recover_mkc m WITH (nolock)
        INNER JOIN dbo.recover_basket b WITH (nolock) ON b.id = m.from_basket_id
        INNER JOIN dbo.recover_sub s WITH (nolock) ON s.sub_id = b.sub_id
        INNER join dbo.recover_marketSubInfo rms WITH (nolock) on rms.basket_id = m.to_basket_id
        INNER join dbo.recover_marketInfo rmi WITH (nolock) on rmi.sub_id = rms.sub_id
        LEFT JOIN dbo.recover_mkc_first_shelf mrs WITH (nolock) on mrs.mkc_id = m.id
        LEFT join dbo.recover_basket_extend rbe WITH (nolock) on rbe.recover_basket_id = b.id
        WHERE isnull(s.sub_check, 0) = 3
        and isnull(b.isdel, 0) = 0
        and isnull(m.ishouhou, 0) = 0
        and isnull(rmi.saleType, 0) = 1
        and mrs.mkc_id is null
        and (rmi.sub_to not in ('回收机退回渠道', '拍靓机', '拍机堂', '拍机堂_POP') and rmi.inuser != '拍机堂')
        and isnull(b.checkResultId, b.shop_check_result_id) is not null
        and rmi.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        <if test="isJiuJi">
            and s.pay_time >= '2023-04-01'
        </if>
        ) a1
        ) a2
        where a2.rank = 1
        ) t
        GROUP by areaid ) t1

    </select>

    <select id="getRecoverDiffNewDto" resultType="com.jiuji.oa.salary.RecoverDiffNewDto">
        select *
        from (
        select sub_id,
        mkcId,
        CASE WHEN ISNULL(diffopt, '') != '' then 1 else 0 end difference,
        RANK() OVER (PARTITION BY mkcId ORDER BY dtime asc) rank
        from (
        SELECT s.sub_id, m.id as mkcId, mrs.diffopt, mrs.create_time as dtime
        FROM dbo.recover_mkc m WITH (nolock)
        INNER JOIN dbo.recover_basket b WITH (nolock) ON b.id = m.from_basket_id
        INNER JOIN dbo.recover_sub s WITH (nolock) ON s.sub_id = b.sub_id
        INNER JOIN dbo.recover_mkc_first_shelf mrs WITH (nolock) on mrs.mkc_id = m.id
        WHERE isnull(s.sub_check, 0) = 3
        and isnull(b.isdel, 0) = 0
        and isnull(m.ishouhou, 0) = 0
        <if test="isJiuJi">
            and s.pay_time >= '2023-04-01'
        </if>
        and s.sub_id in
        <foreach collection="subIds" item="subId" separator="," open="(" close=")">
            #{subId}
        </foreach>

        union ALL

        SELECT s.sub_id,m.id as mkcId,
        case when b.checkResultId is not null then m.diffopt else rbe.shop_diffopt end as diffopt,rmi.tradeDate1 as dtime
        FROM dbo.recover_mkc m WITH (nolock)
        INNER JOIN dbo.recover_basket b WITH (nolock) ON b.id = m.from_basket_id
        INNER JOIN dbo.recover_sub s WITH (nolock) ON s.sub_id = b.sub_id
        INNER join dbo.recover_marketSubInfo rms WITH (nolock)
        on rms.basket_id = m.to_basket_id
        INNER join dbo.recover_marketInfo rmi WITH (nolock) on rmi.sub_id = rms.sub_id
        LEFT JOIN dbo.recover_mkc_first_shelf mrs WITH (nolock) on mrs.mkc_id = m.id
        LEFT join dbo.recover_basket_extend rbe WITH (nolock)
        on rbe.recover_basket_id = b.id
        WHERE isnull(s.sub_check, 0) = 3
        and isnull(b.isdel, 0) = 0
        and isnull(m.ishouhou, 0) = 0
        and isnull(rmi.saleType, 0) = 1
        and mrs.mkc_id is null
        and (rmi.sub_to not in ('回收机退回渠道', '拍靓机', '拍机堂', '拍机堂_POP') and rmi.inuser != '拍机堂')
        and isnull(b.checkResultId, b.shop_check_result_id) is not null
        <if test="isJiuJi">
            and s.pay_time >= '2023-04-01'
        </if>
        and s.sub_id in
        <foreach collection="subIds" item="subId" separator="," open="(" close=")">
            #{subId}
        </foreach>
        ) a1
        ) a2
        where a2.rank = 1
    </select>

    <select id="listReturnOrderProfit" resultType="com.jiuji.oa.salary.ReturnOrder">
        SELECT
        t.basketId,
        t.brandID brandId,
        t.cid,
        t.COUNT,
        t.orderDelivery,
        t.orderKind,
        t.orderPayType,
        t.orderType,
        t.goodProductDisplay,
        t.ppId,
        t.orderVType,
        t.productId,
        t.salaryProductType,
        t.saleAmount,
        t.returnAmount,
        t.subId,
        t.mkcId,
        t.tradeDate,
        t.trueProfit,
        t.trueSalaryLossRelation,
        0 checkSalaryLossRelation,
        t.userType,
        t.refundDate,
        t.refunded,
        t.recTransNew,
        t.areaid AS areaId
        FROM
        (
        <include refid="listReturnOrderProfit2-3"></include>
        <include refid="return_order_profit_2-3"></include>
        s.areaid,
        CASE WHEN md.check2=1 and md.kinds = 'h4' then 1 else 2 end recTransNew,
        NULL refundDate,
        0 refunded
        <include refid="return_order_from_2-1_2-3"></include>
        WHERE
        rs.sub_check in (3,9)
        AND ISNULL(rb.isdel, 0 ) = 0
        <choose>
            <when test="isJiuJi">
                AND (CASE WHEN (k.first_on_salf_time is null and isnull(k.second_costprice,0)=0) or isnull(ishouhou,0)=1 then rs.tradeDate1 else k.first_on_salf_time end)
            </when>
            <otherwise>
                AND rs.tradeDate1
            </otherwise>
        </choose>
        BETWEEN #{calculateReturnReq.startDate} AND #{calculateReturnReq.endDate}

        AND s.areaid IN
        <foreach collection="calculateReturnReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        UNION ALL
        <include refid="listReturnOrderProfit2-3"></include>
        <include refid="return_order_profit_refund_2-3"></include>
        rs.areaid,
        CASE WHEN md.check2=1 and md.kinds = 'h4' then 1 else 2 end recTransNew,
        rs.returnDate refundDate,
        1 refunded
        <include refid="return_order_from_2-1_2-3"></include>
        WHERE
        rs.sub_check = 9
        AND ISNULL(rb.isdel, 0 ) = 0
        AND rs.tradeDate1 BETWEEN #{calculateReturnReq.startDate} AND #{calculateReturnReq.endDate}
        AND s.areaid IN
        <foreach collection="calculateReturnReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        <if test="isJiuJi">
            and k.first_on_salf_time is null and isnull(k.second_costprice,0)=0 and isnull(ishouhou,0)=0
        </if>

        UNION ALL

        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType ,
        '2-3' orderType,
        0 goodProductDisplay,
        p.ppriceid ppId,
        b.replacement_insurance_price replacementInsurancePrice,
        CASE WHEN isnull( s.isnetsub, 0 ) = 0 AND isnull( s.recover_subType, 0 ) = 0 THEN 1
        WHEN isnull( s.isnetsub, 0 ) = 0 AND isnull( s.recover_subType, 0 ) = 1 THEN 2
        WHEN isnull( s.recover_subType, 0 ) = 1 THEN 3 WHEN isnull( s.recover_subType, 0 ) = 2 THEN 4
        END orderVType,
        p.productid productId,
        b.ismobile salaryProductType,
        <choose>
            <when test="isJiuJi">
                <include refid="returnPrice"/> saleAmount,
                <include refid="returnPrice"/> returnAmount,
            </when>
            <otherwise>
                md.price1 saleAmount,
                b.price returnAmount,
            </otherwise>
        </choose>
        s.sub_id subId,
        k.id mkcId,
        md.check2dtime tradeDate,
        NULL userType,
        (md.price2 - md.price1) as trueProfit,
        CASE WHEN (md.price2 - md.price1) &lt; 0 THEN 1 ELSE 0 END trueSalaryLossRelation,
        s.areaid,
        CASE WHEN md.check2=1 and md.kinds = 'h4' then 1 else 2 end recTransNew,
        NULL refundDate,
        0 refunded
        FROM dbo.mkc_dellogs md WITH ( nolock )
        INNER JOIN dbo.recover_mkc k WITH ( nolock ) ON md.mkc_id = k.id
        INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = k.ppriceid
        INNER JOIN dbo.recover_basket b WITH ( nolock ) ON b.id = k.from_basket_id
        INNER JOIN dbo.recover_sub s WITH ( nolock ) ON s.sub_id = b.sub_id
        where isnull(md.check2,0) = 1 and md.check2dtime BETWEEN #{calculateReturnReq.startDate} AND #{calculateReturnReq.endDate}
        and md.kinds  = 'h4'
        <if test="isJiuJi">
            and k.first_on_salf_time is null
        </if>
        AND s.areaid IN
        <foreach collection="calculateReturnReq.areaId" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>

        ) t
        WHERE 1 = 1
        <if test="calculateReturnReq.orderType!=null">
            AND t.orderType = #{calculateReturnReq.orderType}
        </if>
        <if test="calculateReturnReq.cid!=null and calculateReturnReq.cid.size>0">
            AND t.cid IN
            <foreach collection="calculateReturnReq.cid" item="cid" separator="," open="(" close=")">
                #{cid}
            </foreach>
        </if>
        <if test="calculateReturnReq.productId!=null and calculateReturnReq.productId.size>0">
            AND t.productId IN
            <foreach collection="calculateReturnReq.productId" item="productId" separator="," open="(" close=")">
                #{productId}
            </foreach>
        </if>
        <if test="calculateReturnReq.ppId!=null and calculateReturnReq.ppId.size>0">
            AND t.ppId IN
            <foreach collection="calculateReturnReq.ppId" item="ppId" separator="," open="(" close=")">
                #{ppId}
            </foreach>
        </if>
        <if test="calculateReturnReq.orderVType!=null and calculateReturnReq.orderVType.size>0">
            AND t.orderVType IN
            <foreach collection="calculateReturnReq.orderVType" item="orderVType" separator="," open="(" close=")">
                #{orderVType}
            </foreach>
        </if>
        <if test="calculateReturnReq.orderPayType!=null and calculateReturnReq.orderPayType.size>0">
            AND t.orderPayType IN
            <foreach collection="calculateReturnReq.orderPayType" item="orderPayTypeId" separator="," open="(" close=")">
                #{orderPayTypeId}
            </foreach>
        </if>
        <if test="calculateReturnReq.orderDelivery!=null and calculateReturnReq.orderDelivery.size>0">
            AND t.orderDelivery IN
            <foreach collection="calculateReturnReq.orderDelivery" item="orderDelivery" separator="," open="(" close=")">
                #{orderDelivery}
            </foreach>
        </if>
    </select>

    <sql id="listReturnOrderProfit2-3">
        SELECT
        b.id basketId,
        p.brandID brandId,
        p.cid,
        1 COUNT,
        s.sub_delivery orderDelivery,
        'recycle' orderKind,
        s.sub_pay orderPayType,
        '2-3' orderType,
        <choose>
            <when test="isJiuJi">
                CASE WHEN s.kinds = 1 THEN NULL ELSE (CASE WHEN k.first_on_salf_time is not null THEN 1 ELSE 0 END) END goodProductDisplay,
            </when>
            <otherwise>
                CASE WHEN isnull(rs.saleType,0)=0 then 1 else 0 end goodProductDisplay,
            </otherwise>
        </choose>
        p.ppriceid ppId,
        b.replacement_insurance_price replacementInsurancePrice,
        CASE
        WHEN isnull( s.isnetsub, 0 ) = 0
        AND isnull( s.recover_subType, 0 ) = 0 THEN
        1
        WHEN isnull( s.isnetsub, 0 ) = 0
        AND isnull( s.recover_subType, 0 ) = 1 THEN
        2
        WHEN isnull( s.recover_subType, 0 ) = 1 THEN
        3
        WHEN isnull( s.recover_subType, 0 ) = 2 THEN
        4
        END orderVType,
        p.productid productId,
        rb.ismobile salaryProductType,
        <choose>
            <when test="isJiuJi">
                <include refid="returnPrice"/> saleAmount,
                <include refid="returnPrice"/> returnAmount,
            </when>
            <otherwise>
                ISNULL( rb.price2, rb.price ) saleAmount,
                b.price returnAmount,
            </otherwise>
        </choose>
        rs.sub_id subId,
        k.mkcId,
        <choose>
            <when test="isJiuJi">
                (CASE WHEN (k.first_on_salf_time is null and isnull(k.second_costprice,0)=0) or isnull(ishouhou,0)=1 then rs.tradeDate1 else k.first_on_salf_time end) tradeDate,
            </when>
            <otherwise>
                rs.tradeDate1 tradeDate,
            </otherwise>
        </choose>
        NULL userType,
    </sql>

    <select id="listReturnOrderSettlementProfit" resultType="com.jiuji.oa.oacore.salary.bo.dto.ReturnSettlementProfitDto">
        select sub_id,
        basketId,
        sum(recoverSettleCount) as settlementCount,
        sum(profile)            as settlementProfit
        from (
        -- 良品
        SELECT b.sub_id,
        b.id                              basketId,
        m.second_costprice - m.inprice as profile,
        1                              as recoverSettleCount
        from dbo.recover_mkc m WITH (nolock)
        inner join dbo.recover_basket b WITH (nolock) on b.id = m.from_basket_id
        inner join dbo.recover_sub s WITH(NOLOCK) ON s.sub_id = b.sub_id
        INNER JOIN dbo.ch999_user u WITH (NOLOCK) ON u.ch999_name = b.checkUser
        where m.first_on_salf_time BETWEEN #{startDate} AND #{endDate}
        and isnull(m.second_costprice, 0) > 0
        and isnull(m.ishouhou, 0) = 0
        <if test="ch999Id!=null">
            and u.ch999_id = #{ch999Id}
        </if>
        <if test="areaIdList!=null and areaIdList.size>0">
            and s.areaid in
            <foreach collection="areaIdList" item="areaId" separator="," open="(" close=")">
                #{areaId}
            </foreach>
        </if>

        UNION ALL

        -- 上良品后赎回
        SELECT b.sub_id,
        b.id                                     basketId,
        -1 * (m.second_costprice - m.inprice) as profile,
        -1                                    as recoverSettleCount
        FROM dbo.recover_mkc m WITH (nolock)
        INNER JOIN dbo.recover_basket b WITH (nolock) ON b.id = m.from_basket_id
        inner join dbo.recover_sub s WITH(NOLOCK) ON s.sub_id = b.sub_id
        INNER join dbo.recover_marketSubInfo rms WITH (nolock) on rms.basket_id = m.to_basket_id
        INNER join dbo.recover_marketInfo rmi WITH (nolock) on rmi.sub_id = rms.sub_id
        INNER JOIN dbo.ch999_user u WITH (NOLOCK) ON u.ch999_name = b.checkUser
        WHERE isnull(rmi.sub_check, 0) in (3, 9)
        and isnull(b.isdel, 0) = 0
        and m.first_on_salf_time is not null
        and isnull(m.second_costprice, 0) > 0
        and rmi.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        and rmi.sub_to = '回收机退回渠道'
        <if test="ch999Id!=null">
            and u.ch999_id = #{ch999Id}
        </if>
        <if test="areaIdList!=null and areaIdList.size>0">
            and s.areaid in
            <foreach collection="areaIdList" item="areaId" separator="," open="(" close=")">
                #{areaId}
            </foreach>
        </if>

        UNION ALL

        -- 转售（opt（售价-成本-服务费）、pop（售价-成本）、普通转售（售价-成本））
        SELECT b.sub_id,
        b.id                                                        basketId,
        case
        when rmi.sub_to = '拍机堂' then (isnull(rms.price2, rms.price) - isnull(rms.auction_fee, 0) -
        m.inprice)
        else (isnull(rms.price2, rms.price) - m.inprice) end as profile,
        1                                                        as recoverSettleCount
        FROM dbo.recover_mkc m WITH (nolock)
        INNER JOIN dbo.recover_basket b WITH (nolock) ON b.id = m.from_basket_id
        inner join dbo.recover_sub s WITH(NOLOCK) ON s.sub_id = b.sub_id
        INNER join dbo.recover_marketSubInfo rms WITH (nolock) on rms.basket_id = m.to_basket_id
        INNER join dbo.recover_marketInfo rmi WITH (nolock) on rmi.sub_id = rms.sub_id
        INNER JOIN dbo.ch999_user u WITH (NOLOCK) ON u.ch999_name = b.checkUser
        WHERE isnull(rmi.sub_check, 0) in (3, 9)
        and isnull(b.isdel, 0) = 0
        and m.first_on_salf_time is null
        and isnull(m.second_costprice, 0)=0
        and rmi.tradeDate1 BETWEEN #{startDate} AND #{endDate}
        and rmi.sub_to != '回收机退回渠道'
        <if test="ch999Id!=null">
            and u.ch999_id = #{ch999Id}
        </if>
        <if test="areaIdList!=null and areaIdList.size>0">
            and s.areaid in
            <foreach collection="areaIdList" item="areaId" separator="," open="(" close=")">
                #{areaId}
            </foreach>
        </if>

        UNION ALL

        -- 退款（opt、pop）
        SELECT b.sub_id,
        b.id                basketId,
        -1 * (isnull(convert(decimal(18, 4), rms.return_price), isnull(rms.price2, rms.price)) -
        m.inprice) as profile,
        -1               as recoverSettleCount
        FROM dbo.recover_mkc m WITH (nolock)
        INNER JOIN dbo.recover_basket b WITH (nolock) ON b.id = m.from_basket_id
        inner join dbo.recover_sub s WITH(NOLOCK) ON s.sub_id = b.sub_id
        INNER join dbo.recover_marketSubInfo rms WITH (nolock) on rms.basket_id = m.to_basket_id
        INNER join dbo.recover_marketInfo rmi WITH (nolock) on rmi.sub_id = rms.sub_id
        INNER JOIN dbo.ch999_user u WITH (NOLOCK) ON u.ch999_name = b.checkUser
        WHERE isnull(rmi.sub_check, 0) in (9)
        and isnull(b.isdel, 0) = 0
        and m.first_on_salf_time is null
        and isnull(m.second_costprice, 0)=0
        and rmi.returnDate BETWEEN #{startDate} AND #{endDate}
        <if test="ch999Id!=null">
            and u.ch999_id = #{ch999Id}
        </if>
        <if test="areaIdList!=null and areaIdList.size>0">
            and s.areaid in
            <foreach collection="areaIdList" item="areaId" separator="," open="(" close=")">
                #{areaId}
            </foreach>
        </if>

        UNION ALL

        -- pop退差
        SELECT b.sub_id,
        b.id basketId,
        -1 * isnull(r.amount, 0),
        0 as recoverSettleCount
        from dbo.pjt_pop_refund r WITH (nolock)
        INNER JOIN dbo.pjt_pop_goods g WITH (nolock) on g.id = r.pjt_pop_goods_id
        INNER JOIN dbo.recover_mkc m WITH (nolock) on m.id = g.mkcid
        INNER JOIN dbo.recover_basket b WITH (nolock) ON b.id = m.from_basket_id
        inner join dbo.recover_sub s WITH(NOLOCK) ON s.sub_id = b.sub_id
        INNER JOIN dbo.ch999_user u WITH (NOLOCK) ON u.ch999_name = b.checkUser
        WHERE r.refund_type = 1
        and r.status = 8
        and r.update_time BETWEEN #{startDate} AND #{endDate}
        and isnull(b.isdel, 0) = 0
        and m.first_on_salf_time is null
        and isnull(m.second_costprice, 0)=0
        <if test="ch999Id!=null">
            and u.ch999_id = #{ch999Id}
        </if>
        <if test="areaIdList!=null and areaIdList.size>0">
            and s.areaid in
            <foreach collection="areaIdList" item="areaId" separator="," open="(" close=")">
                #{areaId}
            </foreach>
        </if>

        UNION ALL

        -- 转新机
        SELECT b.sub_id,
        b.id                     basketId,
        md.price2 - md.price1 as profile,
        1                     as recoverSettleCount
        FROM dbo.mkc_dellogs md WITH (nolock)
        INNER JOIN dbo.recover_mkc k WITH (nolock) ON md.mkc_id = k.id
        INNER JOIN dbo.recover_basket b WITH (nolock) ON b.id = k.from_basket_id
        inner join dbo.recover_sub s WITH(NOLOCK) ON s.sub_id = b.sub_id
        INNER JOIN dbo.ch999_user u WITH (NOLOCK) ON u.ch999_name = b.checkUser
        where isnull(md.check2, 0) = 1
        and md.check2dtime BETWEEN #{startDate} AND #{endDate}
        and md.kinds = 'h4'
        and k.first_on_salf_time is null
        and isnull(k.second_costprice, 0)=0
        <if test="ch999Id!=null">
            and u.ch999_id = #{ch999Id}
        </if>
        <if test="areaIdList!=null and areaIdList.size>0">
            and s.areaid in
            <foreach collection="areaIdList" item="areaId" separator="," open="(" close=")">
                #{areaId}
            </foreach>
        </if>
        ) t
        group by sub_id, basketId
    </select>

    <select id="getAreaPopReSale" resultType="com.jiuji.oa.oacore.salary.bo.dto.AreaPopReSaleDto">
        select pjtPopSaleCount.areaId,CASE
                   WHEN pjtPopSaleCount > 0 THEN CAST(ROUND(pjtPopRefundCount * 100.0 / pjtPopSaleCount, 2) AS DECIMAL(18, 2))
                   ELSE 100 end areaPopReSaleRate
        from (
                 select count(t.mkcId) as pjtPopRefundCount, t.areaId
                 from (
                          select distinct k.id as mkcId, rs.areaid
                          from dbo.pjt_pop_refund ppr with (nolock)
                           inner join dbo.pjt_pop_goods g with (nolock) on g.id = ppr.pjt_pop_goods_id
                              inner join dbo.recover_mkc k with (nolock) on g.mkcid = k.id
                              inner join dbo.recover_basket rb with (nolock) on k.from_basket_id = rb.id
                              inner join dbo.recover_sub rs with (nolock) on rs.sub_id = rb.sub_id
                          where ((ppr.status in(5,8,10)) or (ppr.status = 7 and ppr.finish_time is not null))
                            and ppr.exempt = 0
                            and rb.shop_check_result_id is not null
                            and isnull(k.second_costprice ,0) = 0
                            and isnull(rb.is_increment,0) = 0
                            and ppr.is_appeal_success = 0
                            and ISNULL( k.ishouhou, 0 ) = 0
                            and ppr.finish_time BETWEEN #{startDate} AND #{endDate}
                      ) t
                 group by t.areaId
             ) pjtPopRefundCount
                 join
             (
                 select count(t.mkcId) as pjtPopSaleCount, t.areaid
                 from (
                          select distinct k.id as mkcId, rs.areaid
                          from dbo.recover_marketSubInfo rmsi with (nolock)
                           inner join dbo.recover_marketInfo rmi with (nolock) on rmsi.sub_id = rmi.sub_id
                              inner join dbo.recover_mkc k with (nolock) on rmsi.basket_id = k.to_basket_id
                              inner join dbo.recover_basket rb with (nolock) on k.from_basket_id = rb.id
                              inner join dbo.recover_sub rs with (nolock) on rs.sub_id = rb.sub_id
                              inner join dbo.pjt_pop_goods g with (nolock) on g.mkcid = k.id and g.del_flag = 0
                          where g.status = 6
                            and ISNULL( k.ishouhou, 0 ) = 0
                            and rmi.sub_check in(3,9)
                            and rb.shop_check_result_id is not null
                            and isnull(k.second_costprice ,0) = 0
                            and isnull(rb.is_increment,0) = 0
                            and rmi.tradeDate1 BETWEEN #{startDate} AND #{endDate}
                      ) t
                 group by t.areaId
             ) pjtPopSaleCount ON pjtPopSaleCount.areaId = pjtPopRefundCount.areaId;
    </select>
    <select id="getCaiWuApplySubDtoList" resultType="com.jiuji.oa.oacore.salary.bo.dto.CaiWuApplySubDto">
        select type_ as type, sub_id as subIdStr
        from dbo.caiwuApply with (nolock)
        where zhichu_stats = 5
          and payway = '自扣'
          and type_ in(0,1)
          and account_time between #{startDate} AND #{endDate}
    </select>
</mapper>
