<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.salary.dao.SalaryResDao">

    <insert id="batchSaveSalaryRes">
        INSERT INTO SalaryConfigLogRes (Ch999_ID, SalaryMonth, Amount, CardinalNumber,
        SalaryCategoryName,
        SalaryKinds, SalaryMode, SalaryName, SalaryPath, DTime,
        salaryCategoryId, salaryChildCategoryId, salaryChildCategoryName,
        salaryId, salaryKindsId, salaryModeId)
        values
        <foreach collection="salaryConfigLogRes" separator="," item="item" open="(" close=")">
            #{ch999Id},#{item.salaryMonth},#{item.amount},#{item.cardinalNumber},#{item.salaryCategoryName},
            #{item.salaryKinds},#{item.salaryMode},#{item.salaryName},#{item.salaryPath},GETDATE(),#{item.salaryCategoryId},
            #{item.salaryChildCategoryId},#{item.salaryChildCategoryName},#{item.salaryId},#{item.salaryKindsId},#{item.salaryModeId}
        </foreach>
    </insert>

    <insert id="saveSalaryRes" useGeneratedKeys="true" keyProperty="item.salaryConfigLogResId" keyColumn="ID">
        INSERT INTO SalaryConfigLogRes (Ch999_ID, SalaryMonth, Amount, CardinalNumber,
                                        SalaryCategoryName, orderKind,
                                        SalaryKinds, SalaryMode, SalaryName, SalaryPath, DTime,
                                        salaryCategoryId, salaryChildCategoryId, salaryChildCategoryName,
                                        salaryId, salaryKindsId, salaryModeId, daipei_id)
        values (#{ch999Id}, #{salaryMonth}, #{item.amount}, #{item.cardinalNumber}, #{item.salaryCategoryName},
                #{item.orderKind},
                #{item.salaryKinds}, #{item.salaryMode}, #{item.salaryName}, #{item.salaryPath}, GETDATE(),
                #{item.salaryCategoryId},
                #{item.salaryChildCategoryId}, #{item.salaryChildCategoryName}, #{item.salaryId}, #{item.salaryKindsId},
                #{item.salaryModeId}, #{item.daiPeiId})
    </insert>


    <insert id="saveSalaryResByType" useGeneratedKeys="true" keyProperty="item.salaryConfigLogResId" keyColumn="ID">
        INSERT INTO SalaryConfigLogRes (Ch999_ID, SalaryMonth, Amount, CardinalNumber,
                                        SalaryCategoryName,
                                        SalaryKinds, SalaryMode, SalaryName, SalaryPath, DTime,
                                        salaryCategoryId, salaryChildCategoryId, salaryChildCategoryName,
                                        salaryId, salaryKindsId, salaryModeId,salaryType)
        values
            (#{ch999Id},#{salaryMonth},#{item.amount},#{item.cardinalNumber},#{item.salaryCategoryName},
             #{item.salaryKinds},#{item.salaryMode},#{item.salaryName},#{item.salaryPath},GETDATE(),#{item.salaryCategoryId},
             #{item.salaryChildCategoryId},#{item.salaryChildCategoryName},#{item.salaryId},#{item.salaryKindsId},#{item.salaryModeId},#{salaryType})
    </insert>

    <insert id="batchSaveSalaryResDetails">
        INSERT INTO SalaryConfigLogResDetails (SalaryConfigLogResID, CardinalNumber, PPID,
        ReturnDate,
        SubId, basket_id,TradeDate, DTime)
        VALUES
        <foreach collection="businessDataResList" separator="," item="item">
            (#{salaryConfigLogResID}, #{item.cardinalNumber}, #{item.ppId}, #{item.returnDate,jdbcType=DATE}, #{item.subId},
             #{item.basketId},
            #{item.tradeDate,jdbcType=DATE}, GETDATE())
        </foreach>
    </insert>
    <update id="updateOrderKind">
        <foreach collection="list" separator=";" item="item">
            update SalaryConfigLogRes set orderKind=#{item.orderKind} where salaryId=#{item.salaryId}
        </foreach>
    </update>

    <delete id="removeByCh999IdAndMonth">
        delete from SalaryConfigLogRes where Ch999_ID=#{ch999Id} and SalaryMonth=#{salaryMonth}
    </delete>

    <delete id="removeByCh999IdAndMonthAndType">
        delete from SalaryConfigLogRes where Ch999_ID=#{ch999Id} and SalaryMonth=#{salaryMonth} and salaryType=#{salaryType}
    </delete>

    <delete id="removeSalaryDetails">
        delete from SalaryConfigLogResDetails where SalaryConfigLogResID in
        <foreach collection="salaryConfigLogResIds" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
    </delete>

    <select id="getIdsByCh999IdAndMonth" resultType="java.lang.Integer">
        select distinct id from SalaryConfigLogRes with(nolock) where Ch999_ID=#{ch999Id} and SalaryMonth=#{salaryMonth}
    </select>
    <select id="getAllSalaryId" resultType="java.lang.Long">
        select distinct salaryId from SalaryConfigLogRes with(nolock)
    </select>

    <select id="getDaiPeiInfoDtoList" resultType="com.jiuji.oa.oacore.salary.bo.dto.DaiPeiInfoDto">
        select dt.id as daiPeiId,
               u.ch999_id,
               u.ch999_name,
               u.iszaizhi as isZaiZhi,
               isnull(m.isshixi,u.isshixi) isshixi,
               ISNULL(m.zhuanzhendate, u.zhuanzhendate) as zhuanzhenDate,
               ISNULL(m.indate, u.indate) as inDate,
               u.offtime,
               ISNULL(m.zhiwuid, u.zhiwuid) as zhiWuId,
               datediff(day,ISNULL(m.indate,u.indate), #{startDate}) inDateDiffStart,
               datediff(day,ISNULL(m.indate,u.indate), #{endDate}) inDateDiffEnd
        from (select ndaipeiCh999id, id, row_number() over (partition by ndaipeiCh999id order by id desc) as rn
              from office.dbo.daipei d with(nolock)
              where ISNULL(d.type_, 1) in (1,3)
                and dataType = 0
                and ISNULL(isdel, 0) = 0
                and daipeiCh999id = #{ch999Id}) dt
                 inner join dbo.ch999_user u with(nolock) on u.ch999_id = dt.ndaipeiCh999id  and u.iszaizhi=1
            left join (select ch999_id, zhuanzhendate, indate, zhiwuid, isshixi
            from dbo.ch999_month_user with(nolock)
            where CONVERT(varchar(6), dtime, 112) = #{date}) m on m.ch999_id = u.ch999_id
        where dt.rn = 1 and format(ISNULL(u.offtime, '20000101'),'yyyyMM')  != #{date}
    </select>
    <select id="hasDaiPeiSalary" resultType="java.lang.Boolean">
        WITH SplitDepartPath AS (
            SELECT d.id,
                   value AS departNumber
            FROM areainfo a WITH (NOLOCK)
            INNER JOIN departInfo d WITH (NOLOCK) ON a.depart_id = d.id
            CROSS APPLY STRING_SPLIT(d.departPath, ',') AS split
        WHERE a.id = #{areaId}
            ),
            DepartIDs AS (
        SELECT id
        FROM departInfo WITH (NOLOCK)
        WHERE isnull(isdel, 0) = 0 AND name IN ('K区', 'A区', 'G区', 'M区', 'C区', 'D区', 'E区', 'F区', 'H区')
            )
        SELECT CASE
                   WHEN EXISTS (
                       SELECT 1
                       FROM SplitDepartPath sdp
                                INNER JOIN DepartIDs di ON sdp.departNumber = CAST(di.id AS VARCHAR)
                   ) THEN 1
                   ELSE 0
                   END AS IsIncluded;
    </select>
</mapper>
