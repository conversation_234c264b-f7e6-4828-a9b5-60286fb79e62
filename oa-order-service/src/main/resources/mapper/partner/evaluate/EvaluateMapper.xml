<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.partner.evaluate.mapper.PartnerEvaluateMapper">

    <insert id="insertEvaluate" parameterType="com.jiuji.oa.oacore.partner.evaluate.entity.Evaluate" useGeneratedKeys="true" keyProperty="id" keyColumn="Id">
        INSERT INTO
            Evaluate
            (
	           UserId,
	           StarLevel,
	           EvaluateType,
	           EvaluateStatus,
	           dtime,
	           AvgStarLevel,
	           xtenant,
	           xtenant_name,
	           source,
	           employee_name,
	           employee_mobile,
	           improve_suggest,
	           support_suggest,
	           message_id
            )
        VALUES
	        (
	        #{req.userId},#{req.starLevel},#{req.evaluateType},#{req.evaluateStatus},#{req.dtime},#{req.avgStarLevel},#{req.xtenant},#{req.xtenantName},
	        #{req.source},#{req.employeeName},#{req.employeeMobile},#{req.improveSuggest},#{req.supportSuggest},#{req.messageId}
	        )
    </insert>

	<sql id="sourceCondition">
		<choose>
			<when test="req.source!=null">
				and e.source=#{req.source}
			</when>
			<otherwise>
				and e.source in (1,2)
			</otherwise>
		</choose>
	</sql>

	<sql id="business_sql">
		from office..Evaluate e with(nolock)
		LEFT JOIN office..EvaluateScore es with(nolock) on e.id =es.EvaluateId
		LEFT JOIN ch999_user us with(nolock) on es.RelateCh999Id = us.ch999_id

		where

		e.xtenant is not null
		<include refid="sourceCondition"/>
		<!--合作伙伴-->
		<if test="req.xtenant != null">
			and e.xtenant = #{req.xtenant}
		</if>
		<!--评价时间-->
		<if test="req.startTime != null and req.startTime != ''">
			AND e.dtime &gt; #{req.startTime}
		</if>
		<if test="req.endTime != null and req.endTime != ''">
			AND e.dtime &lt; #{req.endTime}
		</if>
		<!--运营经理-->
		<if test="req.relateCh999Id != null">
			AND es.RelateCh999Id = #{req.relateCh999Id}
		</if>
		<!--服务分-->
		<if test="req.score != null">
			AND es.score = #{req.score}
		</if>
		<!--专业分-->
		<if test="req.score2 != null">
			AND es.Score2 = #{req.score2}
		</if>
		<!--响应速度-->
		<if test="req.speedScore != null">
			AND es.speed_score = #{req.speedScore}
		</if>
		<!--评价状态-->
		<if test="req.evaluateStatus != null and req.evaluateStatus.size()!=0">
   			AND e.EvaluateStatus in
			<foreach collection="req.evaluateStatus" item="item" separator="," open="(" close=")" index="">
				#{item}
			</foreach>
		</if>

	</sql>

	<select id="queryEvaluateList" parameterType="com.jiuji.oa.oacore.partner.evaluate.vo.req.QueryEvaluateListReq" resultType="com.jiuji.oa.oacore.partner.evaluate.vo.res.EvaluateListRes">
		select * from
		(
		select *, ROW_NUMBER() OVER(order by Id desc) row from
		(
		SELECT
		e.Id as id,
		e.xtenant_name as xtenantName,
		e.dtime as dtime,
		e.AvgStarLevel as avgStarLevel,
		e.improve_suggest as improveSuggest,
		e.support_suggest as supportSuggest,
		e.EvaluateStatus as evaluateStatusCode,
		e.ProcessUser as processUser,
		e.xtenant as xtenant,
		us.ch999_name as relateCh999Name,
		es.Score as score,
		es.Score2 as score2,
		es.speed_score as speedScore

		<include refid="business_sql"/>

		) t1
		) t2
		where t2.Row between #{req.startRow} and #{req.endRow}
    </select>

	<select id="selectRowCount" resultType="int" parameterType="com.jiuji.oa.oacore.partner.evaluate.vo.req.QueryEvaluateListReq">
		SELECT count(1) as totalCount
		<include refid="business_sql"/>
	</select>

	<select id="queryEvaluateDetail" resultType="com.jiuji.oa.oacore.partner.evaluate.vo.res.EvaluateDetailRes">
		 SELECT
            e.Id AS id,
			e.dtime AS dtime,
			e.employee_name as employeeName,
			e.xtenant_name AS xtenantName,
			e.xtenant AS xtenant,
			e.employee_mobile as employeeMobile,
			e.EvaluateStatus AS evaluateStatusCode,
			e.ProcessUser as processUser,
			e.Process as process,
		  	us.ch999_name AS relateCh999Name,
		  	img.url as relateCh999PicUri,
			us.ch999_id as ch999Id,
			us.zhiwu as zhiwu,
			us.zhiji as zhiji_id,
			us.Szhiji as szhiji_id,
			es.Score AS score,
			es.Score2 AS score2,
			es.speed_score AS speedScore,
			e.improve_suggest AS improveSuggest,
			e.source AS source,
			e.support_suggest AS supportSuggest
		FROM
			office..Evaluate e with(nolock)
			LEFT JOIN office..EvaluateScore es with(nolock) ON e.id = es.EvaluateId
			LEFT JOIN ch999_user us with(nolock) ON es.RelateCh999Id= us.ch999_id
			LEFT JOIN office..appHeadimg img with(nolock) ON es.RelateCh999Id= img.ch999_id
		WHERE
		  	e.xtenant is not null
		  	and e.id = #{id}
	</select>
</mapper>
