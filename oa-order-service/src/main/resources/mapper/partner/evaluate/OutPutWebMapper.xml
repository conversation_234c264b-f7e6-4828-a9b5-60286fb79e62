<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.partner.feedback.mapper.OutPutWebMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.partner.feedback.entity.OutPutWeb">
    <!--@mbg.generated-->
    <!--@Table OutPutWeb-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="web" jdbcType="VARCHAR" property="web" />
    <result column="webName" jdbcType="VARCHAR" property="webName" />
    <result column="isDelete" jdbcType="INTEGER" property="isdelete" />
    <result column="rank" jdbcType="INTEGER" property="rank" />
    <result column="moaUrl" jdbcType="VARCHAR" property="moaurl" />
    <result column="namePY" jdbcType="VARCHAR" property="namepy" />
    <result column="showKind" jdbcType="INTEGER" property="showkind" />
    <result column="webKind" jdbcType="INTEGER" property="webkind" />
    <result column="tokenUrl" jdbcType="VARCHAR" property="tokenurl" />
    <result column="nameSpaceId" jdbcType="VARCHAR" property="nameSpaceId" />
    <result column="xtencen" jdbcType="INTEGER" property="xtencen" />
    <result column="pcWeb" jdbcType="VARCHAR" property="pcweb" />
    <result column="xtenant" jdbcType="INTEGER" property="xtenant" />
    <result column="isSystemDocking" jdbcType="INTEGER" property="issystemdocking" />
    <result column="userId" jdbcType="INTEGER" property="userid" />
    <result column="areaConfig" jdbcType="VARCHAR" property="areaconfig" />
    <result column="companyName" jdbcType="VARCHAR" property="companyname" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, web, webName, isDelete, [rank], moaUrl, namePY, showKind, webKind, tokenUrl, 
    nameSpaceId, xtencen, pcWeb, xtenant, isSystemDocking, userId, areaConfig, companyName
  </sql>

  <select id="selectOutPutWebOne" resultMap="BaseResultMap">
    SELECT
    top 1
    webName,xtenant
    FROM OutPutWeb with(nolock)
    WHERE isDelete = 0
    AND xtenant = #{xTenant};
  </select>
  <select id="selectNeoOne" resultMap="BaseResultMap">
    SELECT
    top 1
    webName,nameSpaceId
    FROM OutPutWeb with(nolock)
    WHERE isDelete = 0
    AND nameSpaceId = #{xTenant};
</select>
</mapper>