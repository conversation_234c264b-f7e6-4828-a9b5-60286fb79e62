<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorActivityConfigurationMapper">


    <select id="pageInfo" resultType="com.jiuji.oa.oacore.operator.vo.res.ConfigurationPageRes">
        select config.id,
               cate.Name          as categoryName,
               b.name          as brandName,
               p.product_id    as productId,
               p.ppriceid      as ppid,
               p.product_name  as productName,
               p.product_color as productColor,
               q.company_jc+'('+CAST(q.id AS VARCHAR(10))+')' as channelName,
               q.id as channelId,
               config.activity_version as activityVersion,
               config.create_time as createTime,
               config.create_user as createUser
        from dbo.operator_activity_configuration config with (nolock)
         left join dbo.productinfo p with (nolock) on p.ppriceid = config.ppid
            left join dbo.category cate with (nolock) on p.cid = cate.ID
            left join dbo.brand b with (nolock) on p.brandID = b.id
            left join dbo.Ok3w_qudao q with(nolock ) on q.id=config.channel_id
        <where> config.is_del = 0
            <if test="req.activityVersionList != null and req.activityVersionList.size() > 0">
                and config.activity_version in
                <foreach collection="req.activityVersionList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.cidList != null and req.cidList.size() > 0">
                and p.cid in
                <foreach collection="req.cidList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.brandIdList != null and req.brandIdList.size() > 0">
                and p.brandID in
                <foreach collection="req.brandIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.channelIdList != null and req.channelIdList.size() > 0">
                and config.channel_id in
                <foreach collection="req.channelIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.selectType != null and req.selectValue != null and req.selectValue != ''">
                <if test="req.selectType == 1">
                    and config.ppid = #{req.selectValue}
                </if>
                <if test="req.selectType == 2">
                    and p.product_id = #{req.selectValue}
                </if>
                <if test="req.selectType == 3">
                    and p.product_name like CONCAT('%',#{req.selectValue},'%')
                </if>
            </if>
        </where>
    </select>
    <select id="selectActivityConfigurationByChannelIdAndPpId" resultType="com.jiuji.oa.oacore.operator.po.OperatorActivityConfiguration">
        select *,CONVERT(varchar(32),HASHBYTES('MD5',CAST(ppid as varchar(10)) + CAST(channel_id as varchar(10))),2) as selectKey
        from dbo.operator_activity_configuration config with (nolock)
        where config.is_del = 0
        <if test="ids != null and ids.size() > 0">
            and CONVERT(varchar(32),HASHBYTES('MD5',CAST(ppid as varchar(10)) + CAST(channel_id as varchar(10))),2) in
            <foreach collection="ids" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

    </select>
    <select id="selectChannelIds" resultType="java.lang.Integer">
        select channel_id from dbo.channel_kind_link with(nolock ) where channel_state=1 and kind=3 and channel_id in
        <foreach collection="channelIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>