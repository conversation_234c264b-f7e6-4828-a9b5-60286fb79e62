<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorBusinessCommissionRuleLogMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.operator.po.OperatorBusinessCommissionRuleLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="businessCommissionId" column="business_commission_id" jdbcType="BIGINT"/>
            <result property="comment" column="comment" jdbcType="VARCHAR"/>
            <result property="inUser" column="in_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="BIT"/>
            <result property="operatorBusinessCommissionRuleLogRv" column="operator_business_commission_rule_log_rv" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,business_commission_id,comment,
        in_user,create_time,update_time,
        is_del,operator_business_commission_rule_log_rv
    </sql>
</mapper>
