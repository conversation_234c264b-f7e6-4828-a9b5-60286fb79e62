<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorBusinessCommissionRuleMapper">

    <select id="selectByPage" resultType="com.jiuji.oa.oacore.operator.vo.res.OperatorCommissionRes">
        select
            id,
            name,
            applicable_fields,
            area_level,
            start_time,
            end_time,
            key_business_field,
            key_business_commission,
            key_business_word,
            non_key_business_commission,
            non_key_business_word,
            in_user,
            status,
            create_time,
            update_time
        from operator_business_commission_rule obcr with(nolock)
        <where>
            <if test="req.statusList != null and req.statusList.size > 0">
                and obcr.status in
                <foreach collection="req.statusList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.areaLevelList != null and req.areaLevelList.size > 0">
                and obcr.id in (
                select o.id from operator_business_commission_rule o with(nolock) outer apply dbo.F_SPLIT(o.area_level,',') f
                where f.split_value in
                <foreach collection="req.areaLevelList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="req.applicableFieldList != null and req.applicableFieldList.size > 0">
                and obcr.id in (
                    select o.id from operator_business_commission_rule o with(nolock) outer apply dbo.F_SPLIT(o.applicable_fields,',') f
                    where f.split_value in
                    <foreach collection="req.applicableFieldList" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                )
            </if>
            <if test="req.searchKey != null and req.searchKey != ''">
                <if test="req.searchKeyWord != null and req.searchKeyWord != ''">
                    <if test="req.searchKey == 1">
                        and obcr.name like CONCAT('%', #{req.searchKeyWord}, '%')
                    </if>
                    <if test="req.searchKey == 2">
                        and obcr.id = #{req.searchKeyWord}
                    </if>
                    <if test="req.searchKey == 3">
                        and obcr.in_user like CONCAT('%', #{req.searchKeyWord}, '%')
                    </if>
                </if>
            </if>
        </where>
    </select>
</mapper>

