<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorBusinessFieldRelationMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.operator.po.OperatorBusinessFieldRelation">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="field_id" jdbcType="INTEGER" property="fieldId" />
    <result column="configId" jdbcType="INTEGER" property="configId" />
    <result column="is_show" jdbcType="BIT" property="isShow" />
    <result column="is_require" jdbcType="BIT" property="isRequire" />
    <result column="is_can_edit" jdbcType="BIT" property="isCanEdit" />
    <result column="default_value" jdbcType="VARCHAR" property="defaultValue" />
    <result column="ranks" jdbcType="INTEGER" property="ranks" />
    <result column="last_update_user" jdbcType="VARCHAR" property="lastUpdateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
  </resultMap>

  <resultMap id="BaseResultMapRes" type="com.jiuji.oa.oacore.operator.vo.res.BasketFieldRelationRes">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="field_id" jdbcType="INTEGER" property="fieldId" />
    <result column="configId" jdbcType="INTEGER" property="configId" />
    <result column="is_show" jdbcType="BIT" property="isShow" />
    <result column="is_require" jdbcType="BIT" property="isRequire" />
    <result column="is_can_edit" jdbcType="BIT" property="isCanEdit" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="field_label" jdbcType="VARCHAR" property="fieldLabel" />
    <result column="permission_value" jdbcType="VARCHAR" property="permissionValue" />
    <result column="default_value" jdbcType="VARCHAR" property="defaultValue" />
    <result column="kinds" jdbcType="INTEGER" property="kinds" />
    <result column="ranks" jdbcType="INTEGER" property="ranks" />
    <result column="is_public" jdbcType="BIT" property="isPublic" />
  </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, field_id, configId, is_show, is_require, is_can_edit,default_value, ranks, last_update_user,
    create_time, update_time, is_del
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into operator_business_field_relation
    (field_id, configId, is_show, is_require, is_can_edit, default_value, ranks, last_update_user,
      create_time, update_time, is_del)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.fieldId,jdbcType=INTEGER}, #{item.configId,jdbcType=INTEGER}, #{item.isShow,jdbcType=BIT},
        #{item.isRequire,jdbcType=BIT}, #{item.isCanEdit,jdbcType=BIT}, #{item.defaultValue,jdbcType=VARCHAR},
        #{item.ranks,jdbcType=INTEGER}, #{item.lastUpdateUser,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDel,jdbcType=BIT}
        )
    </foreach>
  </insert>

  <select id="selectByBusinessConfigIds" resultType="com.jiuji.oa.oacore.operator.bo.OperatorBusinessFieldBo">
      select
          obf.id,
          obf.field_name fieldName,
          obfr.configId,
          obf.field_label fieldLabel,
          obf.permission_value permissionValue,
          obfr.default_value defaultValue
      FROM operator_business_field_relation obfr with(nolock)
      LEFT JOIN dbo.operator_business_field obf WITH(NOLOCK) ON obfr.field_id=obf.id
      where isnull(obf.is_del,0)=0 and isnull(obfr.is_del,0)=0 and obfr.configId in
      <foreach collection="ids" item="configId" open="(" close=")" separator=",">
        #{configId}
      </foreach>
  </select>

  <select id="getBasketFieldRelationByConfigId" resultMap="BaseResultMapRes">
    select
    obfr.id,
    obfr.field_id,
    obfr.configId,
    obfr.is_show,
    obfr.is_require,
    obfr.is_can_edit,
    obfr.ranks,
    obfr.default_value ,
    obf.field_label,
    obf.field_name,
    obf.kinds,
    obf.is_public,
    obf.permission_value
    FROM operator_business_field_relation obfr with(nolock)
    LEFT JOIN dbo.operator_business_field obf WITH(NOLOCK) ON obfr.field_id=obf.id
    where obfr.configId = #{configId} and isnull(obfr.is_del,0)=0 and isnull(obf.is_del,0)=0
  </select>

  <select id="selectByPage" resultMap="BaseResultMapRes">
    select
    obfr.id,
    obfr.field_id,
    obfr.configId,
    obfr.is_show,
    obfr.is_require,
    obfr.is_can_edit,
    obfr.ranks,
    obfr.default_value ,
    obf.field_label,
    obf.field_name,
    obf.kinds,
    obf.is_public,
    obf.permission_value
    FROM operator_business_field_relation obfr with(nolock)
    LEFT JOIN dbo.operator_business_field obf WITH(NOLOCK) ON obfr.field_id=obf.id
    where obfr.configId = #{configId} and isnull(obfr.is_del,0)=0 and isnull(obf.is_del,0)=0
  </select>

  <update id="batchUpdateRank">
    UPDATE operator_business_field_relation
    <set>
      <trim prefix="ranks=case" suffix=" end,">
        <foreach collection="req" item="list">
          <if test="list.rank != null and list.rank != 0">
            when field_id = #{list.id} then #{list.rank}
          </if>
        </foreach>
      </trim>
      update_time = GETDATE(),last_update_user=#{userName}
    </set>
    WHERE field_id in
    <foreach collection="req" item="list" open="(" close=")" separator=",">
      #{list.id}
    </foreach>
  </update>

  <update id="batchUpdateSynchronous">
    UPDATE operator_business_field_relation
    <set>
      <trim prefix="is_show=case" suffix=" else is_show end,">
        <foreach collection="data" item="list">
          <if test="list.isShow != null">
            when field_id = #{list.fieldId} then #{list.isShow}
          </if>
        </foreach>
      </trim>

      <trim prefix="is_require=case" suffix=" else is_require end,">
        <foreach collection="data" item="list">
          <if test="list.isRequire != null">
            when field_id = #{list.fieldId} then #{list.isRequire}
          </if>
        </foreach>
      </trim>

      <trim prefix="is_can_edit=case" suffix=" else is_can_edit end,">
        <foreach collection="data" item="list">
          <if test="list.isCanEdit != null">
            when field_id = #{list.fieldId} then #{list.isCanEdit}
          </if>
        </foreach>
      </trim>
      update_time = GETDATE(),last_update_user=#{userName}
    </set>
    WHERE configId in
    <foreach collection="configIds" item="list" open="(" close=")" separator=",">
      #{list}
    </foreach>
  </update>

  <insert id="batchSaveByFieldId" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into operator_business_field_relation
    (field_id, configId, is_show, is_require, is_can_edit, default_value, ranks, last_update_user,
    create_time, update_time, is_del)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.fieldId,jdbcType=INTEGER}, #{item.configId,jdbcType=INTEGER}, #{item.isShow,jdbcType=BIT},
      #{item.isRequire,jdbcType=BIT}, #{item.isCanEdit,jdbcType=BIT}, #{item.defaultValue,jdbcType=VARCHAR},
      #{item.ranks,jdbcType=INTEGER}, #{item.lastUpdateUser,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
      #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDel,jdbcType=BIT})
    </foreach>
  </insert>

  <select id="getBasketFieldRelationByConfigIdAndId" resultMap="BaseResultMapRes">
    select TOP 1
    obfr.id,
    obfr.field_id,
    obfr.configId,
    obfr.is_show,
    obfr.is_require,
    obfr.is_can_edit,
    obfr.ranks,
    obfr.default_value ,
    obf.field_label,
    obf.kinds,
    obf.field_name,
    obf.permission_value
    FROM operator_business_field_relation obfr with(nolock)
    LEFT JOIN dbo.operator_business_field obf WITH(NOLOCK) ON obfr.field_id=obf.id
    where obfr.configId = #{configId} and obfr.field_id = #{fieldId} and isnull(obfr.is_del,0)=0 and isnull(obf.is_del,0)=0
  </select>
</mapper>