<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorAddSubRecordMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.operator.po.OperatorAddSubRecord">
    <!--@mbg.generated-->
    <!--@Table OperatorAddSubRecord-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="areaId" jdbcType="INTEGER" property="areaid" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="ppids" jdbcType="VARCHAR" property="ppids" />
    <result column="account" jdbcType="VARCHAR" property="account" />
    <result column="staffID" jdbcType="INTEGER" property="staffid" />
    <result column="isDel" jdbcType="BIT" property="isdel" />
    <result column="check_status" jdbcType="TINYINT" property="checkStatus" />
    <result column="execute_time" jdbcType="TIMESTAMP" property="executeTime" />
    <result column="eroMsg" jdbcType="VARCHAR" property="eromsg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_userId" jdbcType="INTEGER" property="createUserid" />
    <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="handle_userId" jdbcType="BIGINT" property="handleUserid" />
    <result column="sub_id" jdbcType="BIGINT" property="subId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, areaId, mobile, ppids, account, staffID, isDel, check_status, execute_time, eroMsg, 
    create_time, create_userId, handle_time, handle_userId, sub_id
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into OperatorAddSubRecord
    (areaId, mobile, ppids, account, staffID, isDel, check_status, execute_time, eroMsg, 
      create_time, create_userId, handle_time, handle_userId, sub_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.areaid,jdbcType=INTEGER}, #{item.mobile,jdbcType=VARCHAR}, #{item.ppids,jdbcType=VARCHAR}, 
        #{item.account,jdbcType=VARCHAR}, #{item.staffid,jdbcType=INTEGER}, #{item.isdel,jdbcType=BIT}, 
        #{item.checkStatus,jdbcType=TINYINT}, #{item.executeTime,jdbcType=TIMESTAMP}, #{item.eromsg,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.createUserid,jdbcType=INTEGER}, #{item.handleTime,jdbcType=TIMESTAMP}, 
        #{item.handleUserid,jdbcType=BIGINT}, #{item.subId,jdbcType=BIGINT})
    </foreach>
  </insert>
</mapper>