<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorBankAccountMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.operator.po.OperatorBankAccount">
    <!--@mbg.generated-->
    <!--@Table operator_bank_account-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="bank_num" jdbcType="VARCHAR" property="bankNum" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_account_name" jdbcType="VARCHAR" property="bankAccountName" />
    <result column="bank_all_name" jdbcType="VARCHAR" property="bankAllName" />
    <result column="userid" jdbcType="BIGINT" property="userid" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="creat_user" jdbcType="VARCHAR" property="creatUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator_bank_account_rv" jdbcType="TIMESTAMP" property="operatorBankAccountRv" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, mobile, bank_num, bank_name, bank_account_name, bank_all_name, userid, is_del, 
    creat_user, create_time, update_time, operator_bank_account_rv
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into operator_bank_account
    (mobile, bank_num, bank_name, bank_account_name, bank_all_name, userid, is_del, creat_user, 
      create_time, update_time, operator_bank_account_rv)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.mobile,jdbcType=VARCHAR}, #{item.bankNum,jdbcType=VARCHAR}, #{item.bankName,jdbcType=VARCHAR}, 
        #{item.bankAccountName,jdbcType=VARCHAR}, #{item.bankAllName,jdbcType=VARCHAR}, 
        #{item.userid,jdbcType=BIGINT}, #{item.isDel,jdbcType=BIT}, #{item.creatUser,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.operatorBankAccountRv,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>