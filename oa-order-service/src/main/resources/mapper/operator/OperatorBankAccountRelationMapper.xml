<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorBankAccountRelationMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.operator.po.OperatorBankAccountRelation">
    <!--@mbg.generated-->
    <!--@Table operator_bank_account_relation-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="bank_account_id" jdbcType="INTEGER" property="bankAccountId" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="creat_user" jdbcType="VARCHAR" property="creatUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator_bank_account_relation_rv" jdbcType="TIMESTAMP" property="operatorBankAccountRelationRv" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, bank_account_id, account_id, start_time, end_time, is_del, creat_user, create_time, 
    update_time, operator_bank_account_relation_rv
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into operator_bank_account_relation
    (bank_account_id, account_id, start_time, end_time, is_del, creat_user, create_time, 
      update_time, operator_bank_account_relation_rv)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bankAccountId,jdbcType=INTEGER}, #{item.accountId,jdbcType=INTEGER}, #{item.startTime,jdbcType=TIMESTAMP}, 
        #{item.endTime,jdbcType=TIMESTAMP}, #{item.isDel,jdbcType=BIT}, #{item.creatUser,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.operatorBankAccountRelationRv,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>

  <insert id="insertByAccount" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    INSERT INTO dbo.operator_bank_account_relation
    (bank_account_id,account_id,start_time,end_time,is_del,creat_user,create_time)
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.bankAccountId},#{item.accountId},#{item.startTime}, #{item.endTime}, 0,#{item.userName},GETDATE()
      )
    </foreach>
  </insert>

  <update id="updateByAccount">
    UPDATE operator_bank_account_relation
    <set>
      <trim prefix="bank_account_id=case" suffix=" else bank_account_id end,">
        <foreach collection="list" item="data">
          <if test="data.bankAccountId != null">
            when id = #{data.id} then #{data.bankAccountId}
          </if>
        </foreach>
      </trim>

      <trim prefix="account_id=case" suffix=" else account_id end,">
        <foreach collection="list" item="data">
          <if test="data.accountId != null and data.accountId != 0">
            when id = #{data.id} then #{data.accountId}
          </if>
        </foreach>
      </trim>

      <trim prefix="start_time=case" suffix=" else start_time end,">
        <foreach collection="list" item="data">
          <if test="data.startTime != null">
            when id = #{data.id} then #{data.startTime}
          </if>
        </foreach>
      </trim>

      <trim prefix="end_time=case" suffix=" else end_time end,">
        <foreach collection="list" item="data">
          <if test="data.endTime != null">
            when id = #{data.id} then #{data.endTime}
          </if>
        </foreach>
      </trim>
      update_time = GETDATE()
    </set>
    WHERE id in
    <foreach collection="list" item="data" open="(" close=")" separator=",">
      #{data.id}
    </foreach>

  </update>
</mapper>