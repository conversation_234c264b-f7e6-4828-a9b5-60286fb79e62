<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorBusinessFieldMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.operator.po.OperatorBusinessField">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="is_public" jdbcType="BIT" property="isPublic" />
    <result column="field_label" jdbcType="VARCHAR" property="fieldLabel" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="kinds" jdbcType="TINYINT" property="kinds" />
    <result column="is_show" jdbcType="BIT" property="isShow" />
    <result column="is_require" jdbcType="BIT" property="isRequire" />
    <result column="is_can_edit" jdbcType="BIT" property="isCanEdit" />
    <result column="permission_value" jdbcType="VARCHAR" property="permissionValue" />
    <result column="ranks" jdbcType="INTEGER" property="ranks" />
    <result column="last_update_user" jdbcType="VARCHAR" property="lastUpdateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, is_public, field_label, field_name, kinds, is_show, is_require, is_can_edit, permission_value,
    ranks, last_update_user, create_time, update_time, is_del
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into operator_business_field
    (is_public, field_label, field_name, kinds, is_show, is_require, is_can_edit,permission_value, ranks,
      last_update_user, create_time, update_time, is_del)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.isPublic,jdbcType=BIT}, #{item.fieldLabel,jdbcType=VARCHAR}, #{item.fieldName,jdbcType=VARCHAR}, 
        #{item.kinds,jdbcType=TINYINT}, #{item.isShow,jdbcType=BIT}, #{item.isRequire,jdbcType=BIT}, 
        #{item.isCanEdit,jdbcType=BIT}, #{item.ranks,jdbcType=INTEGER}, #{item.lastUpdateUser,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDel,jdbcType=BIT}, 
        #{item.operatorBusinessFieldRv,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <select id="selectByPage" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/> from operator_business_field with(nolock)
  </select>

  <select id="getMaxRanks" resultType="java.lang.Integer">
    select Max(ranks) from operator_business_field with(nolock)
  </select>
  <select id="allList" resultType="com.jiuji.oa.oacore.operator.po.OperatorBusinessField">
    select id,field_label from operator_business_field with(nolock)
  </select>

  <update id="batchUpdateRank">
    UPDATE operator_business_field
    <set>
      <trim prefix="ranks=case" suffix=" end,">
        <foreach collection="req" item="list">
          <if test="list.rank != null and list.rank != 0">
            when id = #{list.id} then #{list.rank}
          </if>
        </foreach>
      </trim>
      update_time = GETDATE(),last_update_user=#{userName}
    </set>
    WHERE id in
    <foreach collection="req" item="list" open="(" close=")" separator=",">
      #{list.id}
    </foreach>
  </update>

  <update id="enableOrDisable">
    UPDATE operator_business_field SET is_del = #{req.isDel} WHERE id = #{req.id}
  </update>

  <update id="enableOrDisableRelation">
    UPDATE operator_business_field_relation SET is_del = #{req.isDel} WHERE field_id = #{req.id}
  </update>
</mapper>