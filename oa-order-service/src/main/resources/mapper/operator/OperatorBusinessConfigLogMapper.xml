<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorBusinessConfigLogMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.operator.po.OperatorBusinessConfigLog">
    <!--@mbg.generated-->
    <!--@Table operator_business_config_log-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="config_id" jdbcType="INTEGER" property="configId" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="create_user_id" jdbcType="INTEGER" property="createUserId" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, config_id, create_user_id, create_user, create_time, is_del, [comment]
  </sql>
  <insert id="batchInsert">
    insert into operator_business_config_log
    (config_id, comment, create_user_id, create_user, create_time, is_del)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.configId}, #{item.comment}, #{item.createUserId},
        #{item.createUser}, #{item.createTime}, #{item.isDel})
    </foreach>
  </insert>

  <select id="selectByPage" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/> from operator_business_config_log with(nolock) where 1=1
    <if test="configId != null and configId !=0 ">
      and config_id = #{configId}
    </if>
    </select>
</mapper>