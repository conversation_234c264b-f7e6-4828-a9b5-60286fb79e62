<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorStatisticConfigMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.operator.po.OperatorStatisticConfig">
    <!--@mbg.generated-->
    <!--@Table OperatorStatisticConfig-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="rank" jdbcType="INTEGER" property="rank" />
    <result column="inuser" jdbcType="INTEGER" property="inUser" />
    <result column="dtime" jdbcType="TIMESTAMP" property="dateTime" />
    <result column="isdel" jdbcType="INTEGER" property="isDel" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, [name], [rank], inuser, dtime, isdel
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into OperatorStatisticConfig
    ([name], [rank], inuser, dtime, isdel)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.rank,jdbcType=INTEGER}, #{item.inuser,jdbcType=INTEGER}, 
        #{item.dateTime,jdbcType=TIMESTAMP}, #{item.isdel,jdbcType=INTEGER})
    </foreach>
  </insert>

  <select id="getOperatorStatistic" resultMap="BaseResultMap">
    select  <include refid="Base_Column_List"/> from OperatorStatisticConfig with(nolock)
    </select>
  <select id="getAllOperatorStatistic" resultType="com.jiuji.oa.oacore.operator.po.OperatorStatisticConfig">
    select
      id,
      isnull(parent_id,0) parentId,
      name,
      [rank],
      inuser,
      dtime,
      isdel
    from
      OperatorStatisticConfig with(nolock)
  </select>
</mapper>