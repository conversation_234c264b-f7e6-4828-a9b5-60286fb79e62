<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorBusinessChangeLogMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.operator.po.OperatorBusinessChangeLog">
    <!--@mbg.generated-->
    <!--@Table operator_business_change_log-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="change_range" jdbcType="INTEGER" property="changeRange" />
    <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator_business_change_log_rv" jdbcType="TIMESTAMP" property="operatorBusinessChangeLogRv" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, business_id, staff_id, change_range, begin_time, end_time, is_del, create_time, 
    update_time, operator_business_change_log_rv
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into operator_business_change_log
    (business_id, staff_id, change_range, begin_time, end_time, is_del, create_time, 
      update_time, operator_business_change_log_rv)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.businessId,jdbcType=INTEGER}, #{item.staffId,jdbcType=INTEGER}, #{item.changeRange,jdbcType=INTEGER}, 
        #{item.beginTime,jdbcType=TIMESTAMP}, #{item.endTime,jdbcType=TIMESTAMP}, #{item.isDel,jdbcType=BIT}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.operatorBusinessChangeLogRv,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>