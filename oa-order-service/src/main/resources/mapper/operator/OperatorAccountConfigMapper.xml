<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorAccountConfigMapper">

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into OperatorAccountConfig
        (channel,account,subject,[comment],areaids,category,inuser,dtime,isdel,[rank],staffId,parent_id,save_money,channel_code,channel_mark,channel_name,
         not_deduction_predeposit,is_main)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.channel,jdbcType=INTEGER}, #{item.account,jdbcType=VARCHAR}, #{item.subject,jdbcType=INTEGER},
            #{item.comment,jdbcType=VARCHAR}, #{item.areaIds,jdbcType=VARCHAR}, #{item.category,jdbcType=INTEGER},
            #{item.inUser,jdbcType=INTEGER}, #{item.dateTime,jdbcType=TIMESTAMP}, #{item.isDel,jdbcType=INTEGER},
            #{item.rank,jdbcType=INTEGER}, #{item.staffId,jdbcType=INTEGER},#{item.parentId},#{item.saveMoney},#{item.channelCode},#{item.channelMark},#{item.channelName},
            #{item.notDeductionPredeposit,jdbcType=BIT},#{item.isMainJob,jdbcType=BIT})
        </foreach>
    </insert>

    <select id="getAccountInitData" resultType="com.jiuji.oa.oacore.operator.vo.res.BusinessConfigAccountInitRes">
        SELECT
        a.id,
        a.channel,
        q.company channelName,
        a.account,
        a.subject,
        a.comment,
        a.areaids areaIds,
        a.category,
        c.name categoryName,
        a.inuser inUser,
        u.ch999_name ch999Name,
        a.dtime dateTime,
        a.rank,
        a.staffId,
        a.is_main isMainJob,
        a.parent_id parentId,
        u2.ch999_name staffName,
        a.save_money saveMoney,
        a.channel_code channelCode,
        a.channel_mark channelMark,
        a.channel_name quDaoName,
        a.isdel isDel,
        isnull(a.not_deduction_predeposit,0) notDeductionPredeposit
        FROM
        OperatorAccountConfig a WITH ( nolock )
        LEFT JOIN Ok3w_qudao q WITH ( nolock ) ON q.id = a.channel
        LEFT JOIN category c WITH ( nolock ) ON c.id = a.category
        LEFT JOIN ch999_user u WITH ( nolock ) ON a.inuser = u.ch999_id
        LEFT JOIN ch999_user u2 WITH ( nolock ) ON a.staffid = u2.ch999_id
        WHERE 1 = 1
        <include refid="accountInitQuery"/>
    </select>

    <sql id="accountInitQuery">
        <if test="req.key != null and req.key != ''">
            <choose>
                <when test="req.account == 1">
                    and a.account like concat('%',#{req.key},'%')
                </when>
                <when test="req.account == 2">
                    and q.company like concat('%',#{req.key},'%')
                </when>
                <when test="req.account == 3">
                    AND a.staffId = #{req.key}
                </when>
                <otherwise>
                    AND u2.ch999_name like concat('%',#{req.key},'%')
                </otherwise>
            </choose>
        </if>
        <if test="req.zaiZhi != null and req.zaiZhi != 0">
            <choose>
                <when test="req.zaiZhi == 1">
                    AND isnull(u2.iszaizhi,0) = 1
                </when>
                <when test="req.zaiZhi == 2">
                    AND isnull(u2.iszaizhi,0) = 0
                </when>
            </choose>
        </if>
        <if test="req.areaId != null and req.areaId != ''">
            and (exists (
            select 1 from(
            select f.split_value from OperatorAccountConfig sub with(nolock) cross apply dbo.F_SPLIT(sub.areaids,',') f
            where sub.id = a.id intersect select * from F_SPLIT(isnull(#{req.areaId},''),',')
            ) a ) or a.areaids is null)
        </if>
        <choose>
            <when test="req.status == 1">
                and isnull(a.isdel,0) = 0
            </when>
            <when test="req.status == 2">
                and isnull(a.isdel,0) = 1
            </when>
        </choose>
        <if test="req.startBalance !=null and req.startBalance != ''">
            AND ISNULL(a.save_money, 0) >= #{req.startBalance}
        </if>
        <if test="req.endBalance != null and req.endBalance != ''">
            AND ISNULL(a.save_money, 0) &lt;= #{req.endBalance}
        </if>
        <if test="req.isMainJob != null and req.isMainJob == true ">
            and isnull(a.is_main,0) = 1
        </if>
        <if test="req.isMainJob != null and req.isMainJob == false ">
            and isnull(a.is_main,0) = 0
        </if>
    </sql>
    <select id="getAccountConfigByStaffId" resultType="java.lang.String">
        SELECT TOP 1 account FROM dbo.OperatorAccountConfig WITH ( nolock ) WHERE isnull(isdel,0)=0 AND staffId = #{staffId}
    </select>

    <insert id="addBusinessConfig" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into OperatorAccountConfig (channel,account,subject,comment,areaids,category,rank,inuser,dtime,staffId,channel_code,channel_mark,channel_name,isdel, not_deduction_predeposit, parent_id, is_main)
        values
        (#{req.channel},#{req.account},#{req.subject},#{req.comment},#{req.areaIds},#{req.category},#{req.rank},#{req.inUser},#{req.dateTime}
        ,#{req.staffId},#{req.channelCode},#{req.channelMark},#{req.quDaoName},0, #{req.notDeductionPredeposit,jdbcType=BIT}, #{req.parentId},#{req.isMainJob})
    </insert>

    <update id="updateBusinessConfig">
        update OperatorAccountConfig set
        channel= #{req.channel},
        account = #{req.account},
        subject = #{req.subject},
        comment = #{req.comment},
        areaids = #{req.areaIds},
        category = #{req.category},
        rank = #{req.rank},
        parent_id = #{req.parentId},
        staffId = #{req.staffId},
        is_main = #{req.isMainJob},
        channel_code = #{req.channelCode},
        channel_mark = #{req.channelMark},
        channel_name = #{req.quDaoName},
        not_deduction_predeposit = #{req.notDeductionPredeposit,jdbcType=BIT}
        where id = #{req.id}
    </update>

    <select id="getAccountConfigByStaffIdAndId" resultType="java.lang.String">
        SELECT TOP 1 c.account FROM dbo.OperatorAccountConfig c with(nolock) WHERE c.id != #{id} and isnull(c.isdel,0)=0 AND c.account = #{account}
    </select>

    <select id="selectCountByAccount" resultType="java.lang.Integer">
        select COUNT (1) from dbo.OperatorAccountConfig with(nolock) where account=#{account} and isnull(isdel,0)=0
    </select>

    <select id="getMainJobNumber" resultType="com.jiuji.oa.oacore.operator.vo.res.MainJobNumberRes">
        select id parentId,account
        from dbo.OperatorAccountConfig with(nolock)
        where isnull(isdel, 0)=0 and isnull(parent_id, 0) = 0
    </select>

    <update id="recoveryById">
        update OperatorAccountConfig set isdel = 0 where id = #{id}
    </update>

    <select id="selectCountByAccountList" resultType="java.lang.String">
        select account from dbo.OperatorAccountConfig with(nolock) where isnull(isdel,0)=0
        AND account IN
        <foreach item="accounts" collection="accountList" open="(" separator="," close=")">
            #{accounts}
        </foreach>
    </select>

    <select id="getAccountConfigByStaffIdList" resultType="java.lang.String">
        SELECT account FROM dbo.OperatorAccountConfig WITH ( nolock ) WHERE isnull(isdel,0)=0 AND staffId IN
        <foreach item="staffIds" collection="staffIdList" open="(" separator="," close=")">
            #{staffIds}
        </foreach>
    </select>

    <select id="getBasketByAccountAndId" resultType="java.lang.Integer">
        SELECT count(1) FROM dbo.OperatorBasket WITH(NOLOCK)
        WHERE account_id = #{id}
    </select>

    <select id="getKemuAssociation" resultType="com.jiuji.oa.oacore.operator.vo.res.KemuAssociationRes$Kemu">
        SELECT c.code, c.name, a.ztid, a.id authorize, c.auxiliary parentcode
        FROM dbo.aa_account_code c WITH(NOLOCK)
            INNER JOIN dbo.authorize a
        WITH (NOLOCK)
        ON a.ztid = c.account_set_id
        WHERE c.is_last_stage = 1 and a.id = #{authorizeid} and isnull(c.auxiliary,0) = 0
        ORDER BY c.code
    </select>

    <select id="selectCountById" resultType="java.lang.String">
        SELECT c.account FROM dbo.OperatorAccountConfig c WITH(NOLOCK) WHERE c.id = #{id}
    </select>

    <select id="getRepeatAccount" resultType="com.jiuji.oa.oacore.operator.po.OperatorAccountConfig">
        SELECT
            id,channel,account,subject,comment,
            areaids as areaIds,category,rank,inuser as inUser,
            dtime as dateTime,staffId,isdel as isDel,
            parent_id as parentId,is_main as isMainJob,save_money as saveMoney,
            channel_code as channelCode,channel_mark as channelMark,channel_name as channelName
        FROM OperatorAccountConfig WITH(nolock)
        WHERE account IN ( SELECT account FROM OperatorAccountConfig WITH(nolock) WHERE ISNULL(isdel, 0) = 0  GROUP BY account HAVING COUNT ( account ) > 1)
    </select>

    <select id="getRepeatBasketByAccountId"
            resultType="com.jiuji.oa.oacore.operator.vo.res.RepeatAccountRes$RepeatBasketRes">
        SELECT id,account,account_id as accountId FROM  OperatorBasket b WITH(nolock)
        WHERE ISNULL(isdel, 0) = 0 and  b.account_id in
        <foreach item="accountId" collection="accountIds" open="(" separator="," close=")">
            #{accountId}
        </foreach>
    </select>

    <select id="listPackage" resultType="com.jiuji.oa.oacore.operator.vo.res.ListPackageRes">
        SELECT
            op.Id as id,
            op.Phone as phone,
            op.package_name as operatorPackage,
            op.broadband_user as isbroadband,
            op.handle_time as handletime,
            op.service_effective_time as startTime,
            op.service_end_time as endTime,
            op.remark as comment,
            op.in_user_name as createUser,
            op.create_time as createTime
        FROM
            dbo.operatorPackage op with(nolock)
            <where> op.del_flag = 0
                <if test="req.searchValue!=null and req.searchValue!='' and req.searchOptions==1">
                    and op.Phone = #{req.searchValue}
                </if>
                <if test="req.searchValue!=null and req.searchValue!='' and req.searchOptions==2">
                    and op.package_name like '%' + #{req.searchValue} + '%'
                </if>
                <if test="req.searchValue!=null and req.searchValue!='' and req.searchOptions==3">
                    and op.remark like '%' + #{req.searchValue} + '%'
                </if>
                <if test="req.searchValue!=null and req.searchValue!='' and req.searchOptions==4">
                    and op.in_user_name  = #{req.searchValue}
                </if>
                <if test="req.startTime != null and req.endTime != null  ">
                    <if test="req.searchTimeType!=null and req.searchTimeType==1">
                        and op.handle_time between #{req.startTime} and #{req.endTime}
                    </if>
                    <if test="req.searchTimeType!=null and req.searchTimeType==2">
                        and op.service_effective_time between #{req.startTime} and #{req.endTime}
                    </if>
                    <if test="req.searchTimeType!=null and req.searchTimeType==3">
                        and op.service_end_time between #{req.startTime} and #{req.endTime}
                    </if>
                    <if test="req.searchTimeType!=null and req.searchTimeType==4">
                        and op.create_time between #{req.startTime} and #{req.endTime}
                    </if>
                </if>
                <if test="req.isBroadBand != null and req.isBroadBand != '' ">
                    and op.broadband_user = #{req.isBroadBand}
                </if>
            </where>
    </select>
    <select id="listMyPackage" resultType="com.jiuji.oa.oacore.operator.vo.res.PackageListRes">
        SELECT b.ppriceid,
               p.product_name+' '+isnull(p.product_color,'') as product_name,
               b.sub_id,
               ob.account,
               ob.handleTime,
               ob.service_start_time,
               ob.service_end_time
        from OperatorBasket ob with(nolock)
	inner join basket b
        with (nolock)
        on ob.basketId = b.basket_id
            left join sub s
        with (nolock)
        on s.sub_id = b.sub_id
            left join productinfo p
        with (nolock)
        on p.ppriceid = b.ppriceid
        <where>ob.status = 1 and s.userid = #{req.userId}
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                and (p.product_name+' '+isnull(p.product_color,'')) like '%' + #{req.searchValue}+'%'
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                and p.ppriceid = #{req.searchValue}
            </if>
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
                and p.productid = #{req.searchValue}
            </if>
            <if test="req.isWithinOneYear==1">
                and ob.service_end_time > DATEADD(YEAR,-1,GETDATE())
            </if>
        </where>
    </select>
    <select id="getOperatorStatisticSearchEnumAssociationTwo" resultType="java.lang.String">
        select distinct p.product_name + ' ' + ISNULL(p.product_color, '') as value
        from OperatorBasket ob with(nolock)
            left join basket b
        with (nolock)
        on ob.basketId=b.basket_id
            left join sub s
        with (nolock)
        on s.sub_id=b.sub_id
            left join OperatorBusinessConfig oc
        with (nolock)
        on ob.configId=oc.id
            left join Ok3w_qudao q
        with (nolock)
        on q.id=ob.channel
            left join productinfo p
        with (nolock)
        on p.ppriceid=oc.ppriceid
            left join areainfo a
        with (nolock)
        on a.id=s.areaid
        where ob.isdel=0
          and ob.status in (1
            , 2)
          and s.sub_check in (3
            , 9)
          and p.product_name + ' ' + ISNULL(p.product_color, '') like '%'+#{req.value}+'%'
    </select>
    <select id="getOperatorStatisticSearchEnumAssociationFour" resultType="java.lang.String">
        select distinct b.seller as value
        from OperatorBasket ob with(nolock)
            left join basket b
        with (nolock)
        on ob.basketId=b.basket_id
            left join sub s
        with (nolock)
        on s.sub_id=b.sub_id
            left join OperatorBusinessConfig oc
        with (nolock)
        on ob.configId=oc.id
            left join Ok3w_qudao q
        with (nolock)
        on q.id=ob.channel
            left join productinfo p
        with (nolock)
        on p.ppriceid=oc.ppriceid
            left join areainfo a
        with (nolock)
        on a.id=s.areaid
        where ob.isdel=0
          and ob.status in (1
            , 2)
          and s.sub_check in (3
            , 9)
          and b.seller like '%'+#{req.value}+'%'
    </select>
    <select id="getOperatorStatisticSearchEnumAssociationFive" resultType="java.lang.String">
        select distinct s.trader as value
        from OperatorBasket ob with(nolock)
            left join basket b
        with (nolock)
        on ob.basketId=b.basket_id
            left join sub s
        with (nolock)
        on s.sub_id=b.sub_id
            left join OperatorBusinessConfig oc
        with (nolock)
        on ob.configId=oc.id
            left join Ok3w_qudao q
        with (nolock)
        on q.id=ob.channel
            left join productinfo p
        with (nolock)
        on p.ppriceid=oc.ppriceid
            left join areainfo a
        with (nolock)
        on a.id=s.areaid
        where ob.isdel=0
          and ob.status in (1
            , 2)
          and s.sub_check in (3
            , 9)
          and s.trader like '%'+#{req.value}+'%'
    </select>


</mapper>