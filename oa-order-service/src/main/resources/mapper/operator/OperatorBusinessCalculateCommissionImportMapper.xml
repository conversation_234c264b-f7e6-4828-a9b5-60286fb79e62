<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorBusinessCalculateCommissionImportMapper">

    <select id="pageData" parameterType="com.jiuji.oa.oacore.operator.vo.req.OperatorBusinessCalculateCommissionAdminReq"
            resultType="com.jiuji.oa.oacore.operator.vo.res.OperatorBusinessCalculateCommissionAdminVO">
        select
            q.id as channelId, q.company channelName,p.product_name as productName,p.product_color as productColor,b.ppriceid as ppriceid,
            oc.operator_code as operatorCode,
            a.area,a.depart_id as departId, a.id as areaId,
            ob.status as handleStatus, case when ob.status = 1 then '已办理 'when ob.status = 2 then '已返销'  else  '' end as handleStatusStr,
            b.sub_id as subId,s.sub_check as subCheck,ob.handleMobile,ob.imei,ob.account as handleAccount,
            CONVERT(VARCHAR, ob.handleTime, 120) as handleTime,
            CONVERT(VARCHAR, s.tradeDate1, 120) as subCompleteTime,
            ob.actCommission,t.paidAmount,cc.unPaidAmount
        <include refid="pageDataSystem"></include>
    </select>
    <sql id="pageDataSystem">
        from (
        SELECT cc.basketId, sum(cc.amount) paidAmount
        FROM operator_business_calculate_commission cc with(nolock)
        where cc.is_del = 0  and cc.status = 1

        <if test="req.timeType != null and req.timeType != ''">
            <if test="req.timeType == 'month'">
                <if test="req.month != null and req.month != ''">
                    and CONVERT(VARCHAR(7), cc.indate, 120) = #{req.month}
                </if>
            </if>
            <if test="req.timeType == 'time'">
                <if test="req.timeStart != null and req.timeStart != '' and req.timeEnd != null and req.timeEnd != ''">
                    and FORMAT(cc.indate, 'yyyy-MM') BETWEEN #{req.timeStart} and #{req.timeEnd}
                </if>
            </if>
        </if>
        GROUP by cc.basketId
        ) t
        left join OperatorBasket ob with(nolock) on ob.basketId = t.basketId
        left join dbo.basket b with(nolock) on ob.basketId = b.basket_id
        left join productinfo p with(nolock) on b.ppriceid = p.ppriceid
        left join dbo.sub s with(nolock) on s.sub_id = b.sub_id
        left join OperatorBusinessConfig oc with(nolock) on ob.configId=oc.id
        left join Ok3w_qudao q with(nolock) on q.id=ob.channel
        left join areainfo a with(nolock) on a.id = s.areaid
        outer apply (
        SELECT sum(case when cc.status = 0 then cc.amount else 0 end) unPaidAmount,sum(case when cc.status = 1 then cc.amount else 0 end) paidAmount
        from operator_business_calculate_commission cc with(nolock)
        where cc.is_del = 0 and cc.basketId = ob.basketId
        ) cc
        <where>

            <if test="req.handleStatus != null">
                and ob.status = #{req.handleStatus}
            </if>
            <if test="req.subStatusList != null and req.subStatusList.size() > 0">
                AND s.sub_check IN
                <foreach collection="req.subStatusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.areaIdList != null and req.areaIdList.size() > 0">
                AND a.id IN
                <foreach collection="req.areaIdList" item="areaIds" open="(" separator="," close=")">
                    #{areaIds}
                </foreach>
            </if>

            <if test="req.channelIdList != null and req.channelIdList.size() > 0">
                AND q.id IN
                <foreach collection="req.channelIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.queryKey != null and req.queryKey != '' and req.queryValue != null and req.queryValue !='' ">
                <choose>

                    <when test="req.queryKey == 'productName'">
                        and p.product_name like CONCAT('%', #{req.queryValue}, '%')
                    </when>


                    <when test="req.queryKey == 'mobile'">
                        and ob.handleMobile = #{req.queryValue}
                    </when>

                    <when test="req.queryKey == 'subId'">
                        and  b.sub_id  = #{req.queryValue}
                    </when>

                    <when test="req.queryKey == 'imei'">
                        and  ob.imei  = #{req.queryValue}
                    </when>

                </choose>
            </if>

        </where>
    </sql>

    <select id="pageDataSum" parameterType="com.jiuji.oa.oacore.operator.vo.req.OperatorBusinessCalculateCommissionAdminReq"
            resultType="com.jiuji.oa.oacore.operator.vo.res.OperatorBusinessCalculateCommissionAdminVO">
        select
        sum(ob.actCommission) as actCommission,
        sum(cc.unPaidAmount) as unPaidAmount
        <include refid="pageDataSystem"></include>
    </select>

    <select id="pageImportData" parameterType="com.jiuji.oa.oacore.operator.vo.req.OperatorBusinessCalculateCommissionAdminReq"
            resultType="com.jiuji.oa.oacore.operator.vo.res.OperatorBusinessCalculateCommissionAdminVO">
        select
        q.id as channelId, q.company channelName,bc.product_name as productName,bc.product_color as productColor,bc.pprice_id as ppriceid,
        bc.operator_code as operatorCode,
        a.area,a.depart_id as departId, a.id as areaId,
        bc.handle_status as handleStatus, case when bc.handle_status = 1 then '已办理 'when bc.handle_status = 2 then '已返销'  else  '' end as handleStatusStr,
        bc.sub_id as subId,s.sub_check as subCheck,bc.handle_mobile as handleMobile,bc.imei,bc.handle_account as handleAccount,
        CONVERT(VARCHAR, bc.handle_time, 120) as handleTime,
        CONVERT(VARCHAR, s.tradeDate1, 120) as subCompleteTime,
        bc.act_commission as actCommission,bc.paid_amount as paidAmount,bc.unpaid_amount as unPaidAmount
        <include refid="pageImportData"></include>
    </select>
    <sql id="pageImportData">
        from operator_business_calculate_commission_import bc with(nolock)
        left join sub s with(nolock) on s.sub_id = bc.sub_id
        left join Ok3w_qudao q with(nolock) on q.id = bc.channel_id
        left join areainfo a with(nolock) on a.id = bc.area_id
        <where>
             bc.is_del = 0
            <if test="req.timeType != null and req.timeType != ''">
                <if test="req.timeType == 'month'">
                    <if test="req.month != null and req.month != ''">
                        and CONVERT(VARCHAR(7), bc.in_date, 120) = #{req.month}
                    </if>
                </if>
                <if test="req.timeType == 'time'">
                    <if test="req.timeStart != null and req.timeStart != '' and req.timeEnd != null and req.timeEnd != ''">
                        and FORMAT(bc.in_date, 'yyyy-MM') BETWEEN #{req.timeStart} and #{req.timeEnd}
                    </if>
                </if>
            </if>
            <if test="req.handleStatus != null">
                and bc.handle_status = #{req.handleStatus}
            </if>
            <if test="req.subStatusList != null and req.subStatusList.size() > 0">
                AND s.sub_check IN
                <foreach collection="req.subStatusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.areaIdList != null and req.areaIdList.size() > 0">
                AND a.id IN
                <foreach collection="req.areaIdList" item="areaIds" open="(" separator="," close=")">
                    #{areaIds}
                </foreach>
            </if>

            <if test="req.channelIdList != null and req.channelIdList.size() > 0">
                AND q.id IN
                <foreach collection="req.channelIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="req.queryKey != null and req.queryKey != '' and req.queryValue != null and req.queryValue !='' ">
                <choose>

                    <when test="req.queryKey == 'productName'">
                        and bc.product_name like CONCAT('%', #{req.queryValue}, '%')
                    </when>

                    <when test="req.queryKey == 'mobile'">
                        and bc.handle_mobile = #{req.queryValue}
                    </when>

                    <when test="req.queryKey == 'subId'">
                        and  bc.sub_id  = #{req.queryValue}
                    </when>

                    <when test="req.queryKey == 'imei'">
                        and  bc.imei  = #{req.queryValue}
                    </when>

                </choose>
            </if>

        </where>
    </sql>

    <select id="pageImportDataSum" parameterType="com.jiuji.oa.oacore.operator.vo.req.OperatorBusinessCalculateCommissionAdminReq"
            resultType="com.jiuji.oa.oacore.operator.vo.res.OperatorBusinessCalculateCommissionAdminVO">
        select
        sum(bc.act_commission) as actCommission,
        sum(bc.unpaid_amount) as unPaidAmount
        <include refid="pageImportData"></include>
    </select>
    <update id="delByMonth">
       update operator_business_calculate_commission_import set is_del = 1
       where is_del = 0  and FORMAT(in_date, 'yyyy-MM') = #{month}
    </update>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        INSERT INTO operator_business_calculate_commission_import
        (channel_id, sub_id,  area_id, pprice_id, product_name, product_color, handle_account, imei, handle_mobile, operator_code, handle_status,
        handle_time, in_date, act_commission, paid_amount, unpaid_amount, create_user)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.channelId}, #{item.subId},
            #{item.areaId}, #{item.ppriceid}, #{item.productName},
            #{item.productColor}, #{item.handleAccount}, #{item.imei},
            #{item.handleMobile}, #{item.operatorCode}, #{item.handleStatus},
            #{item.handleTime},  #{item.inDate},
            #{item.actCommission}, #{item.paidAmount}, #{item.unPaidAmount},
            #{item.createUser})
        </foreach>
    </insert>

    <select id="listImportRepeat" resultType="com.jiuji.oa.oacore.operator.po.OperatorBusinessCalculateCommissionImport">
         select
            bc.id, bc.channel_id as channelId, bc.product_name as productName,bc.product_color as productColor,
            bc.pprice_id as ppriceid, bc.operator_code as operatorCode,
            bc.area_id as areaId, bc.in_date as inDate,
            bc.handle_status as handleStatus,
            bc.sub_id as subId,bc.handle_mobile as handleMobile,bc.imei,bc.handle_account as handleAccount,
            bc.handle_time as handleTime
        from operator_business_calculate_commission_import bc with(nolock)
        where bc.is_del = 0 and  FORMAT(bc.in_date, 'yyyy-MM')  = #{month}
        <if test="subId != null">
            and bc.sub_id = #{subId}
        </if>
        <if test="ppid != null">
            and bc.pprice_id = #{ppid}
        </if>
        <if test="imei != null and imei != ''">
            and bc.imei = #{imei}
        </if>

        <if test="handleMobile != null and handleMobile != ''">
            and bc.handle_mobile = #{handleMobile}
        </if>
    </select>
</mapper>
