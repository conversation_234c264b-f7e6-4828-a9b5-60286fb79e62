<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorBusinessConfigMapper">
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.operator.po.OperatorBusinessConfig">
        <!--@mbg.generated-->
        <!--@Table OperatorBusinessConfig-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="ppriceid" jdbcType="INTEGER" property="ppriceid"/>
        <result column="category" jdbcType="INTEGER" property="category"/>
        <result column="channel" jdbcType="INTEGER" property="channel"/>
        <result column="deductionMoney" jdbcType="OTHER" property="deductionMoney"/>
        <result column="commission" jdbcType="OTHER" property="commission"/>
        <result column="agentPoints" jdbcType="OTHER" property="agentPoints"/>
        <result column="comment" jdbcType="VARCHAR" property="comment"/>
        <result column="xtenant" jdbcType="INTEGER" property="xtenant"/>
        <result column="authid" jdbcType="INTEGER" property="authid"/>
        <result column="areaids" jdbcType="VARCHAR" property="areaids"/>
        <result column="offsetName" jdbcType="VARCHAR" property="offsetName"/>
        <result column="offsetMoney" jdbcType="OTHER" property="offsetMoney"/>
        <result column="settlementMoney" jdbcType="OTHER" property="settlementMoney"/>
        <result column="preAbility" jdbcType="OTHER" property="preAbility"/>
        <result column="statisticType" jdbcType="INTEGER" property="statisticType"/>
        <result column="inuser" jdbcType="INTEGER" property="inuser"/>
        <result column="dtime" jdbcType="TIMESTAMP" property="dtime"/>
        <result column="isdel" jdbcType="INTEGER" property="isdel"/>
        <result column="isCheckActName" jdbcType="BIT" property="ischeckactname"/>
        <result column="checkRepeatCount" jdbcType="INTEGER" property="checkrepeatcount"/>
        <result column="actNames" jdbcType="VARCHAR" property="actnames"/>
        <result column="financeAccountKinds" jdbcType="TINYINT" property="financeaccountkinds"/>
        <result column="mustInputImei" jdbcType="BIT" property="mustinputimei"/>
        <result column="OperatorBusinessConfig_rv" jdbcType="TIMESTAMP" property="operatorbusinessconfigRv"/>
        <result column="bind_ppids" jdbcType="VARCHAR" property="bindPpids"/>
        <result column="bounty" jdbcType="OTHER" property="bounty"/>
        <result column="pay_way" jdbcType="INTEGER" property="payWay"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, ppriceid, category, channel, deductionMoney, commission, agentPoints,
        xtenant, authid, areaids, offsetName, offsetMoney, settlementMoney, preAbility, statisticType,
        inuser, dtime, isdel, isCheckActName, checkRepeatCount, actNames, financeAccountKinds,
        mustInputImei, OperatorBusinessConfig_rv, bind_ppids, bounty, pay_way, [comment]
    </sql>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into OperatorBusinessConfig
        (ppriceid,category,channel,deductionMoney,commission,agentPoints,[comment],xtenant,authid,areaids,offsetName,offsetMoney,
        settlementMoney,preAbility,statisticType,inuser,dtime,isdel,isCheckActName,checkRepeatCount,actNames,financeAccountKinds,
        mustInputImei,bind_ppids,bounty,pay_way,service_delay_kind,service_effective_month)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.ppriceid,jdbcType=INTEGER}, #{item.category,jdbcType=INTEGER}, #{item.channel,jdbcType=INTEGER},
            #{item.deductionMoney,jdbcType=OTHER}, #{item.commission,jdbcType=OTHER},
            #{item.agentPoints,jdbcType=OTHER}, #{item.comment,jdbcType=VARCHAR}, #{item.xtenant,jdbcType=INTEGER}, #{item.authid,jdbcType=INTEGER},
            #{item.areaids,jdbcType=VARCHAR}, #{item.offsetName,jdbcType=VARCHAR}, #{item.offsetMoney,jdbcType=OTHER},
            #{item.settlementMoney,jdbcType=OTHER}, #{item.preAbility,jdbcType=OTHER},
            #{item.statisticType,jdbcType=INTEGER},
            #{item.inuser,jdbcType=INTEGER}, #{item.dtime,jdbcType=TIMESTAMP}, #{item.isdel,jdbcType=INTEGER},
            #{item.ischeckactname,jdbcType=BIT}, #{item.checkrepeatcount,jdbcType=INTEGER},
            #{item.actnames,jdbcType=VARCHAR}, #{item.financeaccountkinds,jdbcType=TINYINT},
            #{item.mustinputimei,jdbcType=BIT},  #{item.bindPpids,jdbcType=VARCHAR}, #{item.bounty,jdbcType=OTHER}, #{item.payWay,jdbcType=INTEGER},#{item.serviceDelayKind},
            #{item.serviceEffectiveMonth}
            )
        </foreach>
    </insert>

    <select id="selectCompany" resultType="com.jiuji.oa.oacore.operator.bo.CompanyBo">
        SELECT q.id,
               q.company
        FROM dbo.Ok3w_qudao q WITH ( NOLOCK )
	LEFT JOIN dbo.channel_kind_link l
        WITH ( NOLOCK )
        ON q.id = l.channel_id
        WHERE l.kind = 11 AND l.channel_state = 1
        <!--UNION
                SELECT q.id,
                       q.company
                FROM dbo.Ok3w_qudao q WITH ( NOLOCK )
                WHERE q.kinds = 11 AND q.ispass = 1 -->
    </select>

    <select id="getInitData" resultType="com.jiuji.oa.oacore.operator.vo.res.BusinessConfigInitRes">
        select * from (
        <trim prefixOverrides="union all">
            <if test="req.type == 2 or req.type == null">
                SELECT
                bc.id,
                NULL channel,
                q.company channelName,
                bc.areaids,
                p.cid category,
                c.name categoryName,
                p.product_name + ' ' + ISNULL( p.product_color, '' ) productName,
                p.ppriceid ppriceid,
                NULL deductionMoney,
                NULL commission,
                NULL agentPoints,
                NULL offsetName,
                NULL offsetMoney,
                NULL inuser,
                NULL ch999Name,
                NULL dtime,
                bc.comment,
                bc.settlementMoney,
                bc.preAbility,
                bc.statisticType,
                sc.name statisticName,
                1 isCheckActName,
                1 checkRepeatCount,
                NULL actNames,
                NULL financeAccountKinds,
                0 mustInputImei,
                NULL bindPpids,
                NULL bounty,
                <if test="req.queryCalculateCommissionConfig == 1">
                    NULL calculateCommissionConfigStr,
                </if>
                NULL payWay,
                NULL serviceDelayKind,
                NULL serviceEffectiveMonth,
                bc.operator_code operatorCode,
                2 type
                FROM
                productinfo p WITH ( nolock )
                LEFT JOIN OperatorBusinessConfig bc WITH ( nolock ) ON bc.ppriceid= p.ppriceid
                AND bc.isdel= 0
                LEFT JOIN Ok3w_qudao q WITH ( nolock ) ON q.id= bc.channel
                LEFT JOIN OperatorStatisticConfig sc WITH ( nolock ) ON sc.id= bc.statisticType
                LEFT JOIN category c WITH ( nolock ) ON c.id= bc.category
                WHERE
                p.cid IN ( SELECT ID FROM dbo.f_category_children ( '3' ) )
                AND p.sale_channel= 0
                AND isnull( p.display, 0 ) = 1
                AND p.isuse= 1
                AND p.isdel= 0
                AND NOT EXISTS ( SELECT 1 FROM OperatorBusinessConfig obc with(nolock) WHERE obc.isdel= 0 AND obc.ppriceid= p.ppriceid )
                <include refid="initDataQuery"/>
            </if>
            <if test="req.type == 1 or req.type == null">
                union all
                SELECT
                bc.id,
                bc.channel,
                q.company channelName,
                bc.areaids,
                bc.category,
                c.name categoryName,
                p.product_name + ' ' + ISNULL( p.product_color, '' ) AS productName,
                bc.ppriceid,
                bc.deductionMoney,
                bc.commission,
                bc.agentPoints,
                bc.offsetName,
                bc.offsetMoney,
                bc.inuser,
                u.ch999_name ch999Name,
                bc.dtime,
                bc.comment,
                bc.settlementMoney,
                bc.preAbility,
                bc.statisticType,
                sc.name statisticName,
                bc.isCheckActName,
                bc.checkRepeatCount,
                bc.actNames,
                bc.financeAccountKinds,
                bc.mustInputImei,
                bc.bind_ppids bindPpids,
                bc.bounty,
                <!-- 查询计提配置 -->
                <if test="req.queryCalculateCommissionConfig == 1">
                    STUFF(
                    (SELECT ',' + CAST(t2.months AS NVARCHAR) + ':' + CAST(CAST(t2.Amount AS INT) AS NVARCHAR)
                    FROM operator_business_calculate_commission_config  t2 with(nolock)
                    WHERE t2.config_id = bc.id AND t2.is_del = 0
                    FOR XML PATH('')), 1, 1, ''
                    ) AS calculateCommissionConfigStr,
                </if>
                bc.pay_way payWay,
                bc.service_delay_kind serviceDelayKind,
                bc.service_effective_month serviceEffectiveMonth,
                bc.operator_code operatorCode,
                1 type
                FROM OperatorBusinessConfig bc WITH ( nolock )
                LEFT JOIN productinfo p with(nolock) ON p.ppriceid= bc.ppriceid
                LEFT JOIN Ok3w_qudao q WITH ( nolock ) ON q.id= bc.channel
                LEFT JOIN OperatorStatisticConfig sc WITH ( nolock ) ON sc.id= bc.statisticType
                LEFT JOIN category c WITH ( nolock ) ON c.id= bc.category
                LEFT JOIN ch999_user u WITH ( nolock ) ON bc.inuser = u.ch999_id
                WHERE bc.isdel=0
                <include refid="initDataQuery"/>
            </if>
        </trim>
        ) a
    </select>

    <sql id="initDataQuery">
        <if test="req.ids != null and req.ids.size() > 0">
            and p.ppriceid in
            <foreach collection="req.ids" index="index" item="ids" open="(" separator="," close=")">
                #{ids}
            </foreach>
        </if>
        <if test="req.category != null and req.category != 0">
            and p.cid in (select ID from dbo.f_category_children(#{req.category}) )
        </if>
        <choose>
            <when test="req.searchType == 1">
                <if test="req.key != null and req.key != '' and req.ids == null">
                    and p.product_color like concat('%',#{req.key},'%')
                </if>
            </when>
            <when test="req.searchType == 2">
                <if test="req.key != null and req.key != ''">
                    and p.ppriceid = #{req.key}
                </if>
            </when>
        </choose>
        <if test="req.channel != null and req.channel != 0">
            and bc.channel= #{req.channel}
        </if>
    </sql>

    <insert id="addBusinessConfig" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        INSERT INTO OperatorBusinessConfig (
        ppriceid,
        category,
        channel,
        deductionMoney,
        commission,
        agentPoints,
        comment,
        xtenant,
        authid,
        areaids,
        offsetName,
        offsetMoney,
        settlementMoney,
        inuser,
        dtime,
        preAbility,
        statisticType,
        isCheckActName,
        checkRepeatCount,
        actNames,
        mustInputImei,
        bind_ppids,
        bounty,
        pay_way,
        service_delay_kind,
        service_effective_month,
        pay_kind
        )VALUES
        (#{req.ppriceid,jdbcType=INTEGER},
        #{req.category,jdbcType=INTEGER},
        #{req.channel,jdbcType=INTEGER},
        #{req.deductionMoney,jdbcType=OTHER},
        #{req.commission,jdbcType=OTHER},
        #{req.agentPoints,jdbcType=OTHER},
        #{req.comment,jdbcType=VARCHAR},
        #{req.xtenant,jdbcType=INTEGER},
        #{req.authid,jdbcType=INTEGER},
        #{req.areaids,jdbcType=VARCHAR},
        #{req.offsetName,jdbcType=VARCHAR},
        #{req.offsetMoney,jdbcType=OTHER},
        #{req.settlementMoney,jdbcType=OTHER},
        #{req.inuser,jdbcType=INTEGER},
        #{req.dtime,jdbcType=DATE},
        #{req.preAbility,jdbcType=OTHER},
        #{req.statisticType,jdbcType=INTEGER},
        #{req.isCheckActName,jdbcType=BIT},
        #{req.checkRepeatCount,jdbcType=INTEGER},
        #{req.actNames,jdbcType=VARCHAR},
        #{req.mustInputImei,jdbcType=BIT},
        #{req.bindPpids,jdbcType=VARCHAR},
        #{req.bounty,jdbcType=OTHER},
        #{req.payWay,jdbcType=INTEGER},
        #{req.serviceDelayKind,jdbcType=INTEGER},
        #{req.serviceEffectiveMonth},
        #{req.payKind})
    </insert>

    <select id="countBusinessConfig" resultType="int">
        SELECT COUNT(1) FROM dbo.OperatorBusinessConfig c WITH(NOLOCK) WHERE c.ppriceid = #{ppid} AND ISNULL(c.isdel,0) = 0
    </select>

    <update id="updateOperatorBusinessConfig">
        UPDATE OperatorBusinessConfig
        SET channel =#{req.channel},
        comment =#{req.comment},
        areaids =#{req.areaids},
        offsetName =#{req.offsetName},
        offsetMoney =#{req.offsetMoney},
        settlementMoney =#{req.settlementMoney},
        statisticType =#{req.statisticType},
        isCheckActName =#{req.isCheckActName},
        checkRepeatCount =#{req.checkRepeatCount},
        actNames =#{req.actNames},
        mustInputImei =#{req.mustInputImei},
        pay_way =#{req.payWay},
        service_delay_kind =#{req.serviceDelayKind},
        service_effective_month = #{req.serviceEffectiveMonth},
        pay_kind = #{req.payKind},
        operator_code = #{req.operatorCode},
        physical_goods_ppid = #{req.physicalGoodsPpid},
        business_cash_back = #{req.businessCashBack}
        WHERE
            id =#{req.id}
    </update>

    <update id="addBusinessBindProduct">
        UPDATE dbo.OperatorBusinessConfig SET bind_ppids = ISNULL(bind_ppids,'') + #{ppids} WHERE id = #{configId}
    </update>

    <update id="delBusinessBindProduct">
        UPDATE dbo.OperatorBusinessConfig SET bind_ppids = #{ppids} WHERE id = #{configId}
    </update>

    <select id="getBusinessBindProductByBasketId" resultType="java.lang.Integer">
        SELECT c.id FROM dbo.basket b WITH(NOLOCK)
                INNER JOIN dbo.OperatorBusinessConfig c WITH(NOLOCK) ON b.ppriceid = c.ppriceid
        WHERE c.isdel = 0 AND b.basket_id = #{basketId}
    </select>

    <select id="quantityConfig" resultType="int">
        SELECT COUNT(1)
        FROM
        (
        SELECT DISTINCT
        bc.id
        FROM OperatorBusinessConfig bc WITH ( nolock )
        LEFT JOIN productinfo p with(nolock) ON p.ppriceid= bc.ppriceid
        LEFT JOIN brandCategory bra WITH ( nolock ) ON bra.categoryID= bc.category
        LEFT JOIN brand br WITH ( nolock ) ON br.id= bra.brandID
        WHERE ISNULL(bc.isdel,0) = 0
        <if test="category != null and category !='' ">
            AND p.cid IN (select ID from dbo.f_category_children(#{category}))
        </if>
        <if test="req.brandList != null and req.brandList.size() > 0">
            and br.id in
            <foreach collection="req.brandList" index="index" item="brand" open="(" separator="," close=")">
                #{brand}
            </foreach>
        </if>
        ) r
    </select>

    <select id="categoryList" resultType="com.jiuji.oa.oacore.operator.vo.res.ConfigureSynchronizationRes$Brand">
        SELECT
        id value,name
        FROM
        brand with(nolock) WHERE id in (SELECT brandID FROM brandCategory with(nolock) WHERE categoryID in
        <foreach collection="categoryList" index="index" item="category" open="(" separator="," close=")">
            #{category}
        </foreach>
        )
    </select>

    <select id="synchronousProcessing" resultType="com.jiuji.oa.oacore.operator.bo.SynchronousProcessingBo">
        SELECT
        bc.id,bc.ppriceid
        FROM OperatorBusinessConfig bc WITH ( nolock )
        LEFT JOIN productinfo p with(nolock) ON p.ppriceid= bc.ppriceid
        LEFT JOIN brandCategory bra WITH ( nolock ) ON bra.categoryID= bc.category
        LEFT JOIN brand br WITH ( nolock ) ON br.id= bra.brandID
        WHERE ISNULL(bc.isdel,0) = 0
        <if test="category != null and category !='' ">
            and p.cid in (select ID from dbo.f_category_children(#{category}))
        </if>
        <if test="req.brandList != null and req.brandList.size() > 0">
            and br.id in
            <foreach collection="req.brandList" index="index" item="brand" open="(" separator="," close=")">
                #{brand}
            </foreach>
        </if>
    </select>

    <update id="updateOperatorBasket">
        UPDATE dbo.OperatorBasket
        <set>
            <if test="req.dataSynchronizationType.contains(1)">
                precommission=#{req.commission},actCommission=#{req.commission},
            </if>
            <if test="req.dataSynchronizationType.contains(2)">
                agentPoints=#{req.agentPoints},
            </if>
            <if test="req.dataSynchronizationType.contains(3)">
                bounty=#{req.bounty},
            </if>
        </set>
        WHERE configId = #{req.id}
        AND isdel= 0 AND handleTime
        BETWEEN #{req.synchronizationStartTime} AND #{req.synchronizationEndTime}
    </update>

    <insert id="insertOperatorBusinessChangeLog">
        INSERT INTO dbo.operator_business_change_log(business_id,staff_id,change_range,begin_time,end_time,is_del,create_time)
        VALUES(#{req.id},#{req.staffId},#{req.changeRange},#{req.synchronizationStartTime},#{req.synchronizationEndTime},0,GETDATE())
    </insert>

    <select id="getBusinessConfigById" resultType="com.jiuji.oa.oacore.operator.vo.res.BusinessConfigByIdRes">
    SELECT
            bc.id,
            bc.channel,
            q.company channelName,
            bc.areaids,
            bc.category,
            c.name categoryName,
            p.product_name + ' ' + ISNULL( p.product_color, '' ) AS productName,
            bc.ppriceid,
            bc.deductionMoney,
            bc.commission,
            bc.agentPoints,
            bc.offsetName,
            bc.comment,
            bc.offsetMoney,
            bc.inuser,
            u.ch999_name ch999Name,
            bc.dtime,
            bc.settlementMoney,
            bc.preAbility,
            bc.statisticType,
            sc.name statisticName,
            bc.isCheckActName,
            bc.checkRepeatCount,
            bc.actNames,
            bc.financeAccountKinds,
            bc.mustInputImei,
            bc.bind_ppids bindPpids,
            bc.bounty,
            bc.pay_way payWay,
            bc.service_delay_kind serviceDelayKind,
            bc.service_effective_month serviceEffectiveMonth,
            bc.pay_kind payKind,
            bc.operator_code operatorCode,
            bc.physical_goods_ppid physicalGoodsPpid,
            bc.business_cash_back
    FROM OperatorBusinessConfig bc WITH ( nolock )
            LEFT JOIN productinfo p with(nolock) ON p.ppriceid= bc.ppriceid
            LEFT JOIN Ok3w_qudao q WITH ( nolock ) ON q.id= bc.channel
            LEFT JOIN OperatorStatisticConfig sc WITH ( nolock ) ON sc.id= bc.statisticType
            LEFT JOIN category c WITH ( nolock ) ON c.id= bc.category
            LEFT JOIN ch999_user u WITH ( nolock ) ON bc.inuser = u.ch999_id
    WHERE bc.isdel=0 and bc.id=#{id}
    </select>

    <select id="getProductName" resultType="com.jiuji.oa.oacore.operator.vo.res.BusinessConfigInitRes">
        <choose>
            <when test="type == 1">
                <include refid="select1"/>
            </when>
            <when test="type == 2">
                <include refid="select2"/>
            </when>
            <otherwise>
                <include refid="select2"/>
                UNION ALL
                <include refid="select1"/>
            </otherwise>
        </choose>
    </select>
    <select id="selectPartsAssociation" resultType="com.jiuji.oa.oacore.operator.vo.res.PartsAssociationRes">
        select top 100 p.product_id as productId, p.product_name as productName,p.ppriceid as ppid,p.product_color as productColor,p.ismobile1 as isMobile
        from dbo.productinfo p with (nolock)
        <where>
            ISNULL(p.que,0)!= 2
            <if test ="req.isMobile != null">
                and p.ismobile1 = #{req.isMobile}
            </if>
            <if test="req.selectKeyType.type == 1 and req.selectKeyType.value!=null ">
                and p.ppriceid =#{req.selectKeyType.value}
            </if>
            <if test="req.selectKeyType.type == 2 and req.selectKeyType.value!=null ">
                and p.product_id =#{req.selectKeyType.value}
            </if>
            <if test="req.selectKeyType.type == 3 and req.selectKeyType.value!=null and req.selectKeyType.value!=''">
                and p.product_name like CONCAT('%',#{req.selectKeyType.value},'%')
            </if>
            <if test="req.selectKeyType.type == 4 and req.selectKeyType.value!=null">
                and (p.ppriceid =#{req.selectKeyType.value} or p.product_id =#{req.selectKeyType.value})
            </if>
        </where>
        order by p.product_id desc
    </select>
    <sql id="select1">
        SELECT
                bc.id,
                bc.channel,
                q.company channelName,
                bc.areaids,
                bc.category,
                c.name categoryName,
                p.product_name + ' ' + ISNULL( p.product_color, '' ) AS productName,
                p.product_name as productName2,
                p.product_color AS productColor,
                bc.ppriceid,
                bc.deductionMoney,
                bc.commission,
                bc.agentPoints,
                bc.offsetName,
                bc.comment,
                bc.offsetMoney,
                bc.inuser,
                u.ch999_name ch999Name,
                bc.dtime,
                bc.settlementMoney,
                bc.preAbility,
                bc.statisticType,
                sc.name statisticName,
                bc.isCheckActName,
                bc.checkRepeatCount,
                bc.actNames,
                bc.financeAccountKinds,
                bc.mustInputImei,
                bc.bind_ppids bindPpids,
                bc.bounty,
                bc.pay_way payWay,
                bc.service_delay_kind serviceDelayKind,
                bc.service_effective_month serviceEffectiveMonth,
                1 type
        FROM OperatorBusinessConfig bc WITH ( nolock )
                LEFT JOIN productinfo p with(nolock) ON p.ppriceid= bc.ppriceid
                LEFT JOIN Ok3w_qudao q WITH ( nolock ) ON q.id= bc.channel
                LEFT JOIN OperatorStatisticConfig sc WITH ( nolock ) ON sc.id= bc.statisticType
                LEFT JOIN category c WITH ( nolock ) ON c.id= bc.category
                LEFT JOIN ch999_user u WITH ( nolock ) ON bc.inuser = u.ch999_id
        WHERE bc.isdel=0
    </sql>
    <sql id="select2">
        SELECT
                bc.id,
                NULL channel,
                q.company channelName,
                bc.areaids,
                p.cid category,
                c.name categoryName,
                p.product_name + ' ' + ISNULL( p.product_color, '' ) productName,
                p.product_name as productName2,
                p.product_color AS productColor,
                p.ppriceid ppriceid,
                NULL deductionMoney,
                NULL commission,
                NULL agentPoints,
                NULL offsetName,
                bc.comment,
                NULL offsetMoney,
                NULL inuser,
                NULL ch999Name,
                NULL dtime,
                bc.settlementMoney,
                bc.preAbility,
                bc.statisticType,
                sc.name statisticName,
                1 isCheckActName,
                1 checkRepeatCount,
                NULL actNames,
                NULL financeAccountKinds,
                0 mustInputImei,
                NULL bindPpids,
                NULL bounty,
                NULL payWay,
                NULL serviceDelayKind,
                NULL serviceEffectiveMonth,
                2 type
        FROM
                productinfo p WITH ( nolock )
                LEFT JOIN OperatorBusinessConfig bc WITH ( nolock ) ON bc.ppriceid= p.ppriceid
                AND bc.isdel= 0
                LEFT JOIN Ok3w_qudao q WITH ( nolock ) ON q.id= bc.channel
                LEFT JOIN OperatorStatisticConfig sc WITH ( nolock ) ON sc.id= bc.statisticType
                LEFT JOIN category c WITH ( nolock ) ON c.id= bc.category
        WHERE
                p.cid IN ( SELECT ID FROM dbo.f_category_children ( '3' ) )
          AND p.sale_channel= 0
          AND isnull( p.display, 0 ) = 1
          AND p.isuse= 1
          AND p.isdel= 0
          AND NOT EXISTS ( SELECT 1 FROM OperatorBusinessConfig obc with(nolock) WHERE obc.isdel= 0 AND obc.ppriceid= p.ppriceid )
    </sql>

    <update id="updateByIds">
        UPDATE dbo.OperatorBusinessConfig SET isdel = 1
        WHERE isdel= 0 and id in
        <foreach collection="configIds" index="index" item="configId" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </update>
</mapper>
