<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorBusinessFieldRecordMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.operator.po.OperatorBusinessFieldRecord">
    <!--@mbg.generated-->
    <!--@Table operator_business_field_record-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="field_id" jdbcType="INTEGER" property="fieldId" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="record_value" jdbcType="VARCHAR" property="recordValue" />
    <result column="is_public" jdbcType="BIT" property="isPublic" />
    <result column="field_label" jdbcType="VARCHAR" property="fieldLabel" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="kinds" jdbcType="TINYINT" property="kinds" />
    <result column="is_show" jdbcType="BIT" property="isShow" />
    <result column="is_require" jdbcType="BIT" property="isRequire" />
    <result column="is_can_edit" jdbcType="BIT" property="isCanEdit" />
    <result column="permission_value" jdbcType="VARCHAR" property="permissionValue" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, field_id, record_id, record_value, is_public, field_label, field_name, kinds, permission_value,
    is_show, is_require, is_can_edit, create_time, update_time, is_del
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into operator_business_field_record
    (field_id, record_id, record_value, is_public, field_label, field_name, kinds, is_show, 
      is_require, is_can_edit,permission_value, create_time, update_time, is_del
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.fieldId,jdbcType=INTEGER}, #{item.recordId,jdbcType=INTEGER}, #{item.recordValue,jdbcType=VARCHAR}, 
        #{item.isPublic,jdbcType=BIT}, #{item.fieldLabel,jdbcType=VARCHAR}, #{item.fieldName,jdbcType=VARCHAR}, 
        #{item.kinds,jdbcType=TINYINT}, #{item.isShow,jdbcType=BIT}, #{item.isRequire,jdbcType=BIT}, 
        #{item.isCanEdit,jdbcType=BIT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.isDel,jdbcType=BIT}, #{item.operatorBusinessFieldRecordRv,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>