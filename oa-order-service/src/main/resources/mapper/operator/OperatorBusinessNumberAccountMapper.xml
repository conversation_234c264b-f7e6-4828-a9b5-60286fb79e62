<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorBusinessNumberAccountMapper">
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.operator.po.OperatorBusinessNumberAccount">
        <!--@mbg.generated-->
        <!--@Table operator_business_number_account-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="main_job_number_id" jdbcType="INTEGER" property="mainJobNumberId"/>
        <result column="main_job_number" jdbcType="VARCHAR" property="mainJobNumber"/>
        <result column="amount_incurred_job_number_id" jdbcType="INTEGER" property="amountIncurredJobNumberId"/>
        <result column="amount_incurred_job_number" jdbcType="VARCHAR" property="amountIncurredJobNumber"/>
        <result column="related_order_id" jdbcType="BIGINT" property="relatedOrderId"/>
        <result column="kinds" jdbcType="INTEGER" property="kinds"/>
        <result column="kinds_name" jdbcType="VARCHAR" property="kindsName"/>
        <result column="begin_price" jdbcType="DECIMAL" property="beginPrice"/>
        <result column="add_price" jdbcType="DECIMAL" property="addPrice"/>
        <result column="reduce_price" jdbcType="DECIMAL" property="reducePrice"/>
        <result column="end_price" jdbcType="DECIMAL" property="endPrice"/>
        <result column="comment" jdbcType="VARCHAR" property="comment"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="in_user_id" jdbcType="BIGINT" property="inUserId"/>
        <result column="in_user_name" jdbcType="VARCHAR" property="inUserName"/>
        <result column="is_del" jdbcType="BIT" property="isDel"/>
    </resultMap>
    <resultMap id="BaseResultRes" type="com.jiuji.oa.oacore.operator.vo.res.BalanceDetailsRes">
        <!--@mbg.generated-->
        <!--@Table operator_business_number_account-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="main_job_number_id" jdbcType="INTEGER" property="mainJobNumberId"/>
        <result column="main_job_number" jdbcType="VARCHAR" property="mainJobNumber"/>
        <result column="amount_incurred_job_number_id" jdbcType="INTEGER" property="amountIncurredJobNumberId"/>
        <result column="amount_incurred_job_number" jdbcType="VARCHAR" property="amountIncurredJobNumber"/>
        <result column="related_order_id" jdbcType="BIGINT" property="relatedOrderId"/>
        <result column="kinds" jdbcType="INTEGER" property="kinds"/>
        <result column="kinds_name" jdbcType="VARCHAR" property="kindsName"/>
        <result column="begin_price" jdbcType="DECIMAL" property="beginPrice"/>
        <result column="add_price" jdbcType="DECIMAL" property="addPrice"/>
        <result column="reduce_price" jdbcType="DECIMAL" property="reducePrice"/>
        <result column="end_price" jdbcType="DECIMAL" property="endPrice"/>
        <result column="comment" jdbcType="VARCHAR" property="comment"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="in_user_id" jdbcType="BIGINT" property="inUserId"/>
        <result column="in_user_name" jdbcType="VARCHAR" property="inUserName"/>
        <result column="is_del" jdbcType="BIT" property="isDel"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, main_job_number_id, main_job_number, amount_incurred_job_number_id, amount_incurred_job_number,
        related_order_id, kinds, kinds_name, begin_price, add_price, reduce_price, end_price,
        [comment], create_time, update_time, in_user_id, in_user_name, is_del, operator_business_number_account_rv
    </sql>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into operator_business_number_account
        (main_job_number_id, main_job_number, amount_incurred_job_number_id, amount_incurred_job_number,
        related_order_id, kinds, kinds_name, begin_price, add_price, reduce_price, end_price,
        [comment], create_time, update_time, in_user_id, in_user_name, is_del, operator_business_number_account_rv
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.mainJobNumberId,jdbcType=INTEGER}, #{item.mainJobNumber,jdbcType=VARCHAR},
            #{item.amountIncurredJobNumberId,jdbcType=INTEGER}, #{item.amountIncurredJobNumber,jdbcType=VARCHAR},
            #{item.relatedOrderId,jdbcType=BIGINT}, #{item.kinds,jdbcType=INTEGER}, #{item.kindsName,jdbcType=VARCHAR},
            #{item.beginPrice,jdbcType=DECIMAL}, #{item.addPrice,jdbcType=DECIMAL},
            #{item.reducePrice,jdbcType=DECIMAL},
            #{item.endPrice,jdbcType=DECIMAL}, #{item.comment,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.inUserId,jdbcType=BIGINT},
            #{item.inUserName,jdbcType=VARCHAR},
            #{item.isDel,jdbcType=BIT}, #{item.operatorBusinessNumberAccountRv,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <select id="balanceDetails" resultType="com.jiuji.oa.oacore.operator.vo.res.BalanceDetailsRes">
        SELECT
        b.ppriceid as ppid,
        ob.handleMobile as phoneNumber,
        p.product_name+' '+isnull(p.product_color,'') as productName,
        obna.id,
        obna.main_job_number_id,
        obna.main_job_number,
        obna.amount_incurred_job_number_id,
        obna.amount_incurred_job_number,
        obna.kinds,
        obna.kinds_name,
        obna.begin_price,
        obna.add_price,
        obna.reduce_price,
        obna.end_price,
        obna.[comment],
        obna.create_time,
        obna.update_time,
        obna.in_user_id,
        obna.in_user_name,
        obna.is_del,
        b.sub_id relatedOrderId,
        s.areaid areaIdList
        FROM
        operator_business_number_account obna WITH ( nolock )
        LEFT JOIN OperatorAccountConfig oac WITH ( nolock ) ON oac.id = obna.main_job_number_id
        LEFT JOIN OperatorBasket ob WITH ( nolock ) ON ob.id = obna.related_order_id
        LEFT join basket b WITH ( nolock ) on ob.basketId = b.basket_id
        LEFT join sub s WITH ( nolock ) on b.sub_id = s.sub_id
        LEFT JOIN productinfo p with(nolock) ON p.ppriceid= b.ppriceid
        WHERE 1 = 1
        <if test="req.type != null and req.type != 0">
            and kinds = #{req.type}
        </if>
        <if test="req.key != null and req.key != ''">
            <choose>
                <when test="req.numberType == 1">
                    and obna.main_job_number like concat('%',#{req.key},'%')
                </when>
                <when test="req.numberType == 2">
                    and obna.amount_incurred_job_number like concat('%',#{req.key},'%')
                </when>
                <!--经办人-->
                <when test="req.numberType == 3">
                    and obna.in_user_name like concat('%',#{req.key},'%')
                </when>
                <!--涉及订单-->
                <when test="req.numberType == 4">
                    and b.sub_id = #{req.key}
                </when>
            </choose>
        </if>
        <if test="req.areaIdList != null and req.areaIdList.size() != 0">
            and s.areaid in
            <foreach collection="req.areaIdList" index="index" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        <choose>
            <when test="req.balanceType == 1">
                <if test="req.beginPrice !=null and req.beginPrice != ''">
                    AND ISNULL(obna.begin_price, 0) >= #{req.beginPrice}
                </if>
                <if test="req.endPrice != null and req.endPrice != ''">
                    AND ISNULL(obna.begin_price, 0) &lt;= #{req.endPrice}
                </if>
            </when>
            <when test="req.balanceType == 2">
                <if test="req.beginPrice !=null and req.beginPrice != ''">
                    AND ISNULL(obna.end_price, 0) >= #{req.beginPrice}
                </if>
                <if test="req.endPrice != null and req.endPrice != ''">
                    AND ISNULL(obna.end_price, 0) &lt;= #{req.endPrice}
                </if>
            </when>
            <when test="req.balanceType == 3">
                <if test="req.beginPrice !=null and req.beginPrice != ''">
                    AND ISNULL(obna.add_price, 0) >= #{req.beginPrice}
                </if>
                <if test="req.endPrice != null and req.endPrice != ''">
                    AND ISNULL(obna.add_price, 0) &lt;= #{req.endPrice}
                </if>
            </when>
            <when test="req.balanceType == 4">
                <if test="req.beginPrice !=null and req.beginPrice != ''">
                    AND ISNULL(obna.reduce_price, 0) >= #{req.beginPrice}
                </if>
                <if test="req.endPrice != null and req.endPrice != ''">
                    AND ISNULL(obna.reduce_price, 0) &lt;= #{req.endPrice}
                </if>
            </when>
        </choose>
        <if test="req.startTime != null and req.startTime != '' and req.endTime != null and req.endTime != ''">
            and obna.create_time between #{req.startTime} AND #{req.endTime}
        </if>
    </select>

    <select id="balanceDetailsSum" resultType="com.jiuji.oa.oacore.operator.vo.res.BalanceDetailsSumRes">
        SELECT
        COUNT(1) as totalCount,
        SUM(obna.add_price) as totalAddPrice,
        SUM(reduce_price) as totalReducePrice
        FROM
        operator_business_number_account obna WITH ( nolock )
        LEFT JOIN OperatorAccountConfig oac WITH ( nolock ) ON oac.id = obna.main_job_number_id
        LEFT JOIN OperatorBasket ob WITH ( nolock ) ON ob.id = obna.related_order_id
        LEFT join basket b WITH ( nolock ) on ob.basketId = b.basket_id
        LEFT join sub s WITH ( nolock ) on b.sub_id = s.sub_id
        WHERE 1 = 1
        <if test="req.type != null and req.type != 0">
            and kinds = #{req.type}
        </if>
        <if test="req.key != null and req.key != ''">
            <choose>
                <when test="req.numberType == 1">
                    and obna.main_job_number like concat('%',#{req.key},'%')
                </when>
                <when test="req.numberType == 2">
                    and obna.amount_incurred_job_number like concat('%',#{req.key},'%')
                </when>
                <!--经办人-->
                <when test="req.numberType == 3">
                    and obna.in_user_name like concat('%',#{req.key},'%')
                </when>
                <!--涉及订单-->
                <when test="req.numberType == 4">
                    and b.sub_id = #{req.key}
                </when>
            </choose>
        </if>
        <if test="req.areaIdList != null and req.areaIdList.size() != 0">
            and s.areaid in
            <foreach collection="req.areaIdList" index="index" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        <choose>
            <when test="req.balanceType == 1">
                <if test="req.beginPrice !=null and req.beginPrice != ''">
                    AND ISNULL(obna.begin_price, 0) >= #{req.beginPrice}
                </if>
                <if test="req.endPrice != null and req.endPrice != ''">
                    AND ISNULL(obna.begin_price, 0) &lt;= #{req.endPrice}
                </if>
            </when>
            <when test="req.balanceType == 2">
                <if test="req.beginPrice !=null and req.beginPrice != ''">
                    AND ISNULL(obna.end_price, 0) >= #{req.beginPrice}
                </if>
                <if test="req.endPrice != null and req.endPrice != ''">
                    AND ISNULL(obna.end_price, 0) &lt;= #{req.endPrice}
                </if>
            </when>
            <when test="req.balanceType == 3">
                <if test="req.beginPrice !=null and req.beginPrice != ''">
                    AND ISNULL(obna.add_price, 0) >= #{req.beginPrice}
                </if>
                <if test="req.endPrice != null and req.endPrice != ''">
                    AND ISNULL(obna.add_price, 0) &lt;= #{req.endPrice}
                </if>
            </when>
            <when test="req.balanceType == 4">
                <if test="req.beginPrice !=null and req.beginPrice != ''">
                    AND ISNULL(obna.reduce_price, 0) >= #{req.beginPrice}
                </if>
                <if test="req.endPrice != null and req.endPrice != ''">
                    AND ISNULL(obna.reduce_price, 0) &lt;= #{req.endPrice}
                </if>
            </when>
        </choose>
        <if test="req.startTime != null and req.startTime != '' and req.endTime != null and req.endTime != ''">
            and obna.create_time between #{req.startTime} AND #{req.endTime}
        </if>
    </select>

    <insert id="insertOne" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into operator_business_number_account
        (main_job_number_id, main_job_number, amount_incurred_job_number_id, amount_incurred_job_number,
        related_order_id, kinds, kinds_name, begin_price, add_price, reduce_price, end_price,
        [comment], create_time, update_time, in_user_id, in_user_name, is_del,voucherId
        )
        values(
            #{item.mainJobNumberId}, #{item.mainJobNumber},
            #{item.amountIncurredJobNumberId}, #{item.amountIncurredJobNumber},
            #{item.relatedOrderId}, #{item.kinds}, #{item.kindsName},
            #{item.beginPrice}, #{item.addPrice},
            #{item.reducePrice},
            #{item.endPrice}, #{item.comment}, #{item.createTime},
            #{item.updateTime}, #{item.inUserId},
            #{item.inUserName},
            #{item.isDel},#{item.voucherId})
    </insert>

</mapper>