<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorBasketMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.operator.po.OperatorBasket">
    <!--@mbg.generated-->
    <!--@Table OperatorBasket-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="configId" jdbcType="INTEGER" property="configid" />
    <result column="basketId" jdbcType="INTEGER" property="basketid" />
    <result column="bindBasketId" jdbcType="INTEGER" property="bindbasketid" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="deductionMoney" jdbcType="OTHER" property="deductionmoney" />
    <result column="precommission" jdbcType="OTHER" property="precommission" />
    <result column="actCommission" jdbcType="OTHER" property="actcommission" />
    <result column="offsetMoney" jdbcType="OTHER" property="offsetmoney" />
    <result column="settlementMoney" jdbcType="OTHER" property="settlementmoney" />
    <result column="agentPoints" jdbcType="OTHER" property="agentpoints" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="inuser" jdbcType="INTEGER" property="inuser" />
    <result column="dtime" jdbcType="TIMESTAMP" property="dtime" />
    <result column="isdel" jdbcType="INTEGER" property="isdel" />
    <result column="account" jdbcType="VARCHAR" property="account" />
    <result column="handleMobile" jdbcType="VARCHAR" property="handlemobile" />
    <result column="imei" jdbcType="VARCHAR" property="imei" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="handleUser" jdbcType="INTEGER" property="handleuser" />
    <result column="handleTime" jdbcType="TIMESTAMP" property="handletime" />
    <result column="isChecked" jdbcType="BIT" property="ischecked" />
    <result column="payWay" jdbcType="INTEGER" property="payway" />
    <result column="returnTime" jdbcType="TIMESTAMP" property="returntime" />
    <result column="returnUser" jdbcType="INTEGER" property="returnuser" />
    <result column="OperatorBasket_rv" jdbcType="TIMESTAMP" property="operatorbasketRv" />
    <result column="bounty" jdbcType="OTHER" property="bounty" />
    <result column="u8_voucher_id" jdbcType="INTEGER" property="u8VoucherId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, configId, basketId, bindBasketId, channel, deductionMoney, precommission, actCommission, 
    offsetMoney, settlementMoney, agentPoints, [comment], inuser, dtime, isdel, account, 
    handleMobile, imei, [status], handleUser, handleTime, isChecked, payWay, returnTime, 
    returnUser, OperatorBasket_rv, bounty, u8_voucher_id
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into OperatorBasket
    (configId, basketId, bindBasketId, channel, deductionMoney, precommission, actCommission, 
      offsetMoney, settlementMoney, agentPoints, [comment], inuser, dtime, isdel, account, 
      handleMobile, imei, [status], handleUser, handleTime, isChecked, payWay, returnTime, 
      returnUser, OperatorBasket_rv, bounty, u8_voucher_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.configid,jdbcType=INTEGER}, #{item.basketid,jdbcType=INTEGER}, #{item.bindbasketid,jdbcType=INTEGER}, 
        #{item.channel,jdbcType=INTEGER}, #{item.deductionmoney,jdbcType=OTHER}, #{item.precommission,jdbcType=OTHER}, 
        #{item.actcommission,jdbcType=OTHER}, #{item.offsetmoney,jdbcType=OTHER}, #{item.settlementmoney,jdbcType=OTHER}, 
        #{item.agentpoints,jdbcType=OTHER}, #{item.comment,jdbcType=VARCHAR}, #{item.inuser,jdbcType=INTEGER}, 
        #{item.dtime,jdbcType=TIMESTAMP}, #{item.isdel,jdbcType=INTEGER}, #{item.account,jdbcType=VARCHAR}, 
        #{item.handlemobile,jdbcType=VARCHAR}, #{item.imei,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, 
        #{item.handleuser,jdbcType=INTEGER}, #{item.handletime,jdbcType=TIMESTAMP}, #{item.ischecked,jdbcType=BIT}, 
        #{item.payway,jdbcType=INTEGER}, #{item.returntime,jdbcType=TIMESTAMP}, #{item.returnuser,jdbcType=INTEGER}, 
        #{item.operatorbasketRv,jdbcType=TIMESTAMP}, #{item.bounty,jdbcType=OTHER}, #{item.u8VoucherId,jdbcType=INTEGER}
        )
    </foreach>
  </insert>

  <!-- countOrdersByToday -->
  <select id="countOrdersByToday" resultType="java.lang.Integer">
    SELECT count(1)
    FROM OperatorBasket ob WITH(NOLOCK)
        LEFT JOIN basket b WITH(NOLOCK) ON ob.basketId = b.basket_id
        LEFT JOIN sub s WITH(NOLOCK) ON s.sub_id = b.sub_id
        LEFT JOIN OperatorBusinessConfig oc WITH(NOLOCK) ON ob.configId = oc.id
        LEFT JOIN areainfo a WITH(NOLOCK) ON a.id = s.areaid
    WHERE s.sub_check IN (3, 9) AND ISNULL(b.isdel,0) = 0 AND ob.status = 1
        AND s.tradeDate1 BETWEEN #{startTime} AND #{endTime}
  </select>
</mapper>