<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorBusinessNumberChargeMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.operator.po.OperatorBusinessNumberCharge">
    <!--@mbg.generated-->
    <!--@Table operator_business_number_charge-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="job_number_id" jdbcType="INTEGER" property="jobNumberId" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="in_user_id" jdbcType="BIGINT" property="inUserId" />
    <result column="in_user_name" jdbcType="VARCHAR" property="inUserName" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="operator_business_number_charge_rv" jdbcType="TIMESTAMP" property="operatorBusinessNumberChargeRv" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, job_number_id, price, [comment], create_time, update_time, in_user_id, in_user_name, 
    is_del, operator_business_number_charge_rv
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into operator_business_number_charge
    (job_number_id, price, [comment], create_time, update_time, in_user_id, in_user_name, 
      is_del, operator_business_number_charge_rv)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.jobNumberId,jdbcType=INTEGER}, #{item.price,jdbcType=DECIMAL}, #{item.comment,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.inUserId,jdbcType=BIGINT}, 
        #{item.inUserName,jdbcType=VARCHAR}, #{item.isDel,jdbcType=BIT}, #{item.operatorBusinessNumberChargeRv,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>

  <insert id="saveCharge" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
    insert into operator_business_number_charge
    (job_number_id, price, [comment], create_time, update_time, in_user_id, in_user_name,
    is_del,credit_code)
    values
    (#{item.jobNumberId}, #{item.price}, #{item.comment},
      #{item.createTime}, #{item.updateTime}, #{item.inUserId},
      #{item.inUserName}, #{item.isDel}, #{item.creditCode})
  </insert>
</mapper>