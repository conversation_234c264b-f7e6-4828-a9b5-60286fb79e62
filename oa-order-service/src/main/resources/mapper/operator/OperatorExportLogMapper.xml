<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorExportLogMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.operator.po.OperatorExportLog">
    <!--@mbg.generated-->
    <!--@Table OperatorExportLog-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="jsonContent" jdbcType="VARCHAR" property="jsoncontent" />
    <result column="inuser" jdbcType="VARCHAR" property="inuser" />
    <result column="isdel" jdbcType="BIT" property="isdel" />
    <result column="dtime" jdbcType="TIMESTAMP" property="dtime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, remark, jsonContent, inuser, isdel, dtime
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into OperatorExportLog
    (remark, jsonContent, inuser, isdel, dtime)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.remark,jdbcType=VARCHAR}, #{item.jsoncontent,jdbcType=VARCHAR}, #{item.inuser,jdbcType=VARCHAR}, 
        #{item.isdel,jdbcType=BIT}, #{item.dtime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>