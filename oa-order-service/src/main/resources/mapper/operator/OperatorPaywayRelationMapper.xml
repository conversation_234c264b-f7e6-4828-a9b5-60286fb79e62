<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.operator.dao.OperatorPaywayRelationMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.operator.po.OperatorPaywayRelation">
    <!--@mbg.generated-->
    <!--@Table operator_payway_relation-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="configId" jdbcType="INTEGER" property="configid" />
    <result column="xtenant" jdbcType="INTEGER" property="xtenant" />
    <result column="payWay" jdbcType="INTEGER" property="payway" />
    <result column="last_update_user" jdbcType="VARCHAR" property="lastUpdateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="operator_payway_relation_rv" jdbcType="TIMESTAMP" property="operatorPaywayRelationRv" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, configId, xtenant, payWay, last_update_user, create_time, update_time, is_del, 
    operator_payway_relation_rv
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into operator_payway_relation
    (configId, xtenant, payWay, last_update_user, create_time, update_time, is_del, operator_payway_relation_rv
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.configid,jdbcType=INTEGER}, #{item.xtenant,jdbcType=INTEGER}, #{item.payway,jdbcType=INTEGER}, 
        #{item.lastUpdateUser,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDel,jdbcType=BIT}, #{item.operatorPaywayRelationRv,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>