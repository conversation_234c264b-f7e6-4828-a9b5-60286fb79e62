<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.yading.mapper.YadingServiceStateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.yading.entity.YadingServiceState">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="register_state" property="registerState" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time,
        update_time,
        id, after_services_buy_id, register_state, is_delete
    </sql>
    <select id="getJiuxunServiceRegisterState" resultType="java.lang.Integer">
        select
            isnull(ys.register_state,0) as registerState
        from yading_service_record sr  WITH(nolock) left join yading_service_state ys WITH(nolock)
        on sr.basket_id = ys.id
        where sr.is_deleted=0 and ys.is_delete=0
          and sr.basket_id = #{detailId}
    </select>

</mapper>
