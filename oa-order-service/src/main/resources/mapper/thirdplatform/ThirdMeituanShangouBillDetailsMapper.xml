<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.order.mapper.ThirdMeituanShangouBillDetailsMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.order.entity.ThirdMeituanShangouBillDetails">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="storeId" column="store_id" jdbcType="VARCHAR"/>
            <result property="storeName" column="store_name" jdbcType="VARCHAR"/>
            <result property="currentSettlementId" column="current_settlement_id" jdbcType="VARCHAR"/>
            <result property="entrySettlementId" column="entry_settlement_id" jdbcType="VARCHAR"/>
            <result property="transactionType" column="transaction_type" jdbcType="VARCHAR"/>
            <result property="transactionDescription" column="transaction_description" jdbcType="VARCHAR"/>
            <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
            <result property="orderTime" column="order_time" jdbcType="TIMESTAMP"/>
            <result property="completionTime" column="completion_time" jdbcType="TIMESTAMP"/>
            <result property="refundTime" column="refund_time" jdbcType="TIMESTAMP"/>
            <result property="orderSequence" column="order_sequence" jdbcType="VARCHAR"/>
            <result property="prePurchaseCode" column="pre_purchase_code" jdbcType="VARCHAR"/>
            <result property="periodPurchaseNumber" column="period_purchase_number" jdbcType="VARCHAR"/>
            <result property="orderStatus" column="order_status" jdbcType="VARCHAR"/>
            <result property="deliveryMethod" column="delivery_method" jdbcType="VARCHAR"/>
            <result property="deliveryStatus" column="delivery_status" jdbcType="VARCHAR"/>
            <result property="settlementStatus" column="settlement_status" jdbcType="VARCHAR"/>
            <result property="billingDate" column="billing_date" jdbcType="DATE"/>
            <result property="belongingPeriod" column="belonging_period" jdbcType="VARCHAR"/>
            <result property="merchantReceivable" column="merchant_receivable" jdbcType="DECIMAL"/>
            <result property="invoiceAmount" column="invoice_amount" jdbcType="DECIMAL"/>
            <result property="commissionInvoiceAmount" column="commission_invoice_amount" jdbcType="DECIMAL"/>
            <result property="deliveryInvoiceAmount" column="delivery_invoice_amount" jdbcType="DECIMAL"/>
            <result property="paymentMethod" column="payment_method" jdbcType="VARCHAR"/>
            <result property="totalUserPayment" column="total_user_payment" jdbcType="DECIMAL"/>
            <result property="userProductPayment" column="user_product_payment" jdbcType="DECIMAL"/>
            <result property="userDeliveryFeeOriginal" column="user_delivery_fee_original" jdbcType="DECIMAL"/>
            <result property="totalDeliverySubsidy" column="total_delivery_subsidy" jdbcType="DECIMAL"/>
            <result property="userActualDeliveryFee" column="user_actual_delivery_fee" jdbcType="DECIMAL"/>
            <result property="totalProductPrice" column="total_product_price" jdbcType="DECIMAL"/>
            <result property="mealBoxFee" column="meal_box_fee" jdbcType="DECIMAL"/>
            <result property="packagingFee" column="packaging_fee" jdbcType="DECIMAL"/>
            <result property="totalMerchantSubsidy" column="total_merchant_subsidy" jdbcType="DECIMAL"/>
            <result property="merchantProductSubsidy" column="merchant_product_subsidy" jdbcType="DECIMAL"/>
            <result property="merchantStoreSubsidy" column="merchant_store_subsidy" jdbcType="DECIMAL"/>
            <result property="merchantDeliverySubsidy" column="merchant_delivery_subsidy" jdbcType="DECIMAL"/>
            <result property="merchantVoucherSubsidy" column="merchant_voucher_subsidy" jdbcType="DECIMAL"/>
            <result property="merchantRedPacketSubsidy" column="merchant_red_packet_subsidy" jdbcType="DECIMAL"/>
            <result property="isSuperVoucher" column="is_super_voucher" jdbcType="VARCHAR"/>
            <result property="totalPlatformSubsidy" column="total_platform_subsidy" jdbcType="DECIMAL"/>
            <result property="platformProductSubsidy" column="platform_product_subsidy" jdbcType="DECIMAL"/>
            <result property="platformStoreSubsidy" column="platform_store_subsidy" jdbcType="DECIMAL"/>
            <result property="platformDeliverySubsidy" column="platform_delivery_subsidy" jdbcType="DECIMAL"/>
            <result property="platformVoucherSubsidy" column="platform_voucher_subsidy" jdbcType="DECIMAL"/>
            <result property="platformRedPacketSubsidy" column="platform_red_packet_subsidy" jdbcType="DECIMAL"/>
            <result property="commission" column="commission" jdbcType="DECIMAL"/>
            <result property="commissionRate" column="commission_rate" jdbcType="VARCHAR"/>
            <result property="minimumAmount" column="minimum_amount" jdbcType="DECIMAL"/>
            <result property="maximumAmount" column="maximum_amount" jdbcType="DECIMAL"/>
            <result property="fulfillmentFee" column="fulfillment_fee" jdbcType="DECIMAL"/>
            <result property="basePrice" column="base_price" jdbcType="DECIMAL"/>
            <result property="distanceFee" column="distance_fee" jdbcType="DECIMAL"/>
            <result property="timePeriodFee" column="time_period_fee" jdbcType="DECIMAL"/>
            <result property="priceFee" column="price_fee" jdbcType="DECIMAL"/>
            <result property="weightFee" column="weight_fee" jdbcType="DECIMAL"/>
            <result property="basePriceSecond" column="base_price_second" jdbcType="DECIMAL"/>
            <result property="distanceFeeSecond" column="distance_fee_second" jdbcType="DECIMAL"/>
            <result property="timePeriodFeeSecond" column="time_period_fee_second" jdbcType="DECIMAL"/>
            <result property="categoryFee" column="category_fee" jdbcType="DECIMAL"/>
            <result property="weightFeeSecond" column="weight_fee_second" jdbcType="DECIMAL"/>
            <result property="specialDateFee" column="special_date_fee" jdbcType="DECIMAL"/>
            <result property="surgePriceFee" column="surge_price_fee" jdbcType="DECIMAL"/>
            <result property="charitableDonation" column="charitable_donation" jdbcType="DECIMAL"/>
            <result property="isAgent" column="is_agent" jdbcType="VARCHAR"/>
            <result property="shareDiscount" column="share_discount" jdbcType="VARCHAR"/>
            <result property="promotionActivity" column="promotion_activity" jdbcType="VARCHAR"/>
            <result property="merchantVoucherUserPaid" column="merchant_voucher_user_paid" jdbcType="DECIMAL"/>
            <result property="merchantVoucherCommissionRate" column="merchant_voucher_commission_rate" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="BIT"/>
            <result property="syncTime" column="sync_time" jdbcType="DATE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,store_id,store_name,
        current_settlement_id,entry_settlement_id,transaction_type,
        transaction_description,order_no,order_time,
        completion_time,refund_time,order_sequence,
        pre_purchase_code,period_purchase_number,order_status,
        delivery_method,delivery_status,settlement_status,
        billing_date,belonging_period,merchant_receivable,
        invoice_amount,commission_invoice_amount,delivery_invoice_amount,
        payment_method,total_user_payment,user_product_payment,
        user_delivery_fee_original,total_delivery_subsidy,user_actual_delivery_fee,
        total_product_price,meal_box_fee,packaging_fee,
        total_merchant_subsidy,merchant_product_subsidy,merchant_store_subsidy,
        merchant_delivery_subsidy,merchant_voucher_subsidy,merchant_red_packet_subsidy,
        is_super_voucher,total_platform_subsidy,platform_product_subsidy,
        platform_store_subsidy,platform_delivery_subsidy,platform_voucher_subsidy,
        platform_red_packet_subsidy,commission,commission_rate,
        minimum_amount,maximum_amount,fulfillment_fee,
        base_price,distance_fee,time_period_fee,
        price_fee,weight_fee,base_price_second,
        distance_fee_second,time_period_fee_second,category_fee,
        weight_fee_second,special_date_fee,surge_price_fee,
        charitable_donation,is_agent,share_discount,
        promotion_activity,merchant_voucher_user_paid,merchant_voucher_commission_rate,
        create_time,update_time,is_del,
        sync_time,third_meituan_shangou_bill_details_rv
    </sql>
</mapper>
