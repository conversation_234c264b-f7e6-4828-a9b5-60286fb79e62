<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.order.mapper.ThirdMeituanTuangouBillDetailsMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.order.entity.ThirdMeituanTuangouBillDetails">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
            <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
            <result property="incomeType" column="income_type" jdbcType="VARCHAR"/>
            <result property="businessType" column="business_type" jdbcType="VARCHAR"/>
            <result property="orderType" column="order_type" jdbcType="VARCHAR"/>
            <result property="couponCode" column="coupon_code" jdbcType="VARCHAR"/>
            <result property="orderTime" column="order_time" jdbcType="TIMESTAMP"/>
            <result property="verifyRefundAdjustTime" column="verify_refund_adjust_time" jdbcType="TIMESTAMP"/>
            <result property="groupBuyId" column="group_buy_id" jdbcType="VARCHAR"/>
            <result property="packageName" column="package_name" jdbcType="VARCHAR"/>
            <result property="consumeStoreId" column="consume_store_id" jdbcType="VARCHAR"/>
            <result property="consumeStore" column="consume_store" jdbcType="VARCHAR"/>
            <result property="specialAccountPeriodBusiness" column="special_account_period_business" jdbcType="VARCHAR"/>
            <result property="accountDate" column="account_date" jdbcType="DATE"/>
            <result property="paymentStatus" column="payment_status" jdbcType="VARCHAR"/>
            <result property="paymentNumber" column="payment_number" jdbcType="VARCHAR"/>
            <result property="settlementPrice" column="settlement_price" jdbcType="OTHER"/>
            <result property="totalIncome" column="total_income" jdbcType="OTHER"/>
            <result property="techServiceFeeRate" column="tech_service_fee_rate" jdbcType="OTHER"/>
            <result property="techServiceFee" column="tech_service_fee" jdbcType="OTHER"/>
            <result property="merchantMarketingFee" column="merchant_marketing_fee" jdbcType="OTHER"/>
            <result property="consumedRefund" column="consumed_refund" jdbcType="OTHER"/>
            <result property="otherAdjustments" column="other_adjustments" jdbcType="OTHER"/>
            <result property="selfPackingFee" column="self_packing_fee" jdbcType="OTHER"/>
            <result property="selfDeliveryFee" column="self_delivery_fee" jdbcType="OTHER"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="billingCycle" column="billing_cycle" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_code,account_name,
        income_type,business_type,order_type,
        coupon_code,order_time,verify_refund_adjust_time,
        group_buy_id,package_name,consume_store_id,
        consume_store,special_account_period_business,account_date,
        payment_status,payment_number,settlement_price,
        total_income,tech_service_fee_rate,tech_service_fee,
        merchant_marketing_fee,consumed_refund,other_adjustments,
        self_packing_fee,self_delivery_fee,remarks,
        billing_cycle
    </sql>
</mapper>
