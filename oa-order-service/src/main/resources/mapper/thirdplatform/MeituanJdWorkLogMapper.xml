<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.oplog.mapper.MeituanJdWorkLogMapper">
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.oplog.po.MeituanJdWorkLog">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="work_name" jdbcType="VARCHAR" property="workName"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="work_content" jdbcType="VARCHAR" property="workContent"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_del" jdbcType="BIT" property="isDel"/>
        <result column="log_type" jdbcType="INTEGER" property="logType"/>
        <result column="platform" jdbcType="INTEGER" property="platform"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, work_name,tenant_code, work_content, create_time, create_user, update_time,
        is_del, log_type ,platform
    </sql>

    <select id="getListByPage" resultType="com.jiuji.oa.oacore.thirdplatform.oplog.vo.res.MeituanJdWorkLogRes">
        SELECT
        <include refid="Base_Column_List"/>
        FROM meituan_jd_work_log mjwl WITH(NOLOCK) where 1 = 1
        <if test="req.platform != null and req.platform != 0">
            and mjwl.platform = #{req.platform}
        </if>
        <if test="req.logType != null and req.logType != 0 and req.logType != 6">
            and mjwl.log_type = #{req.logType}
        </if>
        <if test="req.logType != null and req.logType == 6">
            and mjwl.log_type = #{req.logType}
            and create_time BETWEEN CONVERT(varchar(100), GETDATE(), 23) +' 00:00:00' AND CONVERT(varchar(100), GETDATE(), 23) +' 23:59:59'
        </if>
        <if test="req.tenantCode != null and req.tenantCode != ''">
            and mjwl.tenant_code = #{req.tenantCode}
        </if>
    </select>
</mapper>