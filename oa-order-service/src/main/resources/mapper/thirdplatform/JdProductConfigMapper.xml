<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.productconfig.mapper.JdProductConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.productconfig.vo.ProductConfigVO">
        <id column="id" property="id"/>
        <result column="tenant_code" property="tenantCode"/>
        <result column="sku_id" property="skuId"/>
        <result column="product_code" property="productCode"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="price_split" property="priceSplit"/>
        <result column="sync_off" property="syncOff"/>
        <result column="sync_type" property="syncType"/>
        <result column="sync_ratio" property="syncRatio"/>
        <result column="sync_limit" property="syncLimit"/>
        <result column="sync_first" property="syncFirst"/>
        <result column="order_time" property="orderTime"/>
        <result column="tenant_name" property="tenantName"/>
        <result column="product_name" property="productName"/>
        <result column="product_color" property="productColor"/>
        <result column="ismobile1" property="ismobile"/>
        <result column="cid" property="cid"/>
        <result column="costprice" property="costprice"/>
        <result column="leftCount" property="leftCount"/>
    </resultMap>

    <sql id="columns">
        c.id,c.tenant_code,c.sku_id,c.product_code,c.ppriceid,c.price_split,c.sync_off,ISNULL(c.gift_off,0) as gift_off,
        c.sync_type,c.sync_ratio,c.sync_limit,c.sync_first,c.order_time
    </sql>

    <insert id="saveBatch">
        insert into jingdong_product_config (tenant_code,product_code,sku_id,ppriceid,
        price_split,sync_off,sync_type,sync_ratio,sync_limit,sync_first
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.tenantCode}, #{item.productCode}, #{item.skuId}, #{item.ppriceid},
            #{item.priceSplit},#{item.syncOff}, #{item.syncType}, #{item.syncRatio}, #{item.syncLimit}, #{item.syncFirst}
            )
        </foreach>
    </insert>


    <select id="productConfigList" resultMap="BaseResultMap">
        SELECT<include refid="columns"/>,t.tenant_name,p.product_name,p.product_color,p.ismobile1,ca.Name as cid,p.costprice,p.memberprice vipPrice,p.que,c.platform_cost
        FROM jingdong_product_config c WITH(NOLOCK)
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid=c.ppriceid
        LEFT JOIN jingdong_tenant t WITH(NOLOCK) ON t.tenant_code=c.tenant_code
        LEFT JOIN category ca WITH(NOLOCK) ON ca.id=p.cid
        <where>
            <if test="search.tenantid!=null">
                AND t.id = #{search.tenantid}
            </if>
            <if test="search.tenantCode != null and search.tenantCode != ''">
                AND c.tenant_code = #{search.tenantCode}
            </if>
            <if test="search.syncOff != null and search.syncOff ==1">
                AND c.sync_off = 1
            </if>
            <if test="search.syncOff != null and search.syncOff ==0">
                AND c.sync_off = 0
            </if>
            <if test="search.giftOff != null and search.giftOff ==1">
                AND ISNULL(c.gift_off,0) = 1
            </if>
            <if test="search.giftOff != null and search.giftOff ==0">
                AND ISNULL(c.gift_off,0) = 0
            </if>
            <if test="search.que != null">
                AND p.que = #{search.que}
            </if>
            <if test="search.skuId != null and search.skuId != ''">
                AND c.sku_id = #{search.skuId}
            </if>
            <if test="search.productCode != null and search.productCode != ''">
                AND c.product_code = #{search.productCode}
            </if>
            <if test="search.searchValue != null and search.searchValue != ''">
                <choose>
                    <when test="search.searchOptions == 1">
                        AND c.sku_id = #{search.searchValue}
                    </when>
                    <when test="search.searchOptions == 2">
                        AND c.ppriceid = #{search.searchValue}
                    </when>
                    <when test="search.searchOptions == 3">
                        AND p.product_name LIKE concat('%',#{search.searchValue},'%')
                    </when>
                    <when test="search.searchOptions == 4">
                        and c.product_code LIKE concat('%',#{search.searchValue},'%')
                    </when>
                </choose>
            </if>
        </where>
    </select>

    <select id="selectListWithStock" resultMap="BaseResultMap">
         SELECT s.area_id,s.store_code,c.*,k.leftCount FROM jingdong_store s WITH(NOLOCK)
                INNER JOIN dbo.product_kc k WITH(NOLOCK) ON k.areaid=s.area_id
                INNER JOIN jingdong_product_config c WITH(NOLOCK) ON c.ppriceid=k.ppriceid
                WHERE s.store_code=#{storeCode} AND c.sync_off=1
                UNION ALL
                SELECT s.area_id,s.store_code,c.*,k.leftCount FROM jingdong_store s WITH(NOLOCK)
                INNER JOIN (
                    SELECT k.areaid, k.ppriceid,COUNT(*) leftCount FROM dbo.product_mkc k WITH(NOLOCK) WHERE kc_check=3 AND isnull(k.mouldFlag,0)=0
                     AND NOT EXISTS(SELECT 1 FROM dbo.xc_mkc xc WITH(NOLOCK) WHERE xc.mkc_id=k.id AND isnull(xc.isLock,0)=1 )
                     GROUP BY k.areaid,k.ppriceid
                ) k ON k.areaid=s.area_id
                INNER JOIN jingdong_product_config c WITH(NOLOCK) ON c.ppriceid=k.ppriceid
                WHERE s.store_code=#{storeCode} AND c.sync_off=1
    </select>
    <select id="listJdProductTyingConfigByPage"
            resultType="com.jiuji.oa.oacore.thirdplatform.productconfig.vo.JdProductTyingConfigPageVO">
        SELECT * from (SELECT distinct tc.config_id as configId,c.tenant_code,c.sku_id,c.product_code,t.id as tenantid
        FROM jingdong_product_tying_config tc WITH(NOLOCK)
        LEFT JOIN jingdong_product_config c WITH(NOLOCK) ON c.id=tc.config_id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid=tc.ppriceid
        LEFT JOIN jingdong_tenant t WITH(NOLOCK) ON t.tenant_code=c.tenant_code
        <where>
            <if test="search.tenantid!=null">
                AND t.id = #{search.tenantid}
            </if>
            <if test="search.tenantCode != null and search.tenantCode != ''">
                AND c.tenant_code = #{search.tenantCode}
            </if>
            <if test="search.searchValue != null and search.searchValue != ''">
                <choose>
                    <when test="search.searchOptions == 1">
                        AND c.sku_id LIKE concat('%',#{search.searchValue},'%')
                    </when>
                    <when test="search.searchOptions == 4">
                        and c.product_code LIKE concat('%',#{search.searchValue},'%')
                    </when>
                    <when test="search.searchOptions == 3">
                        AND p.product_name LIKE concat('%',#{search.searchValue},'%')
                    </when>
                    <when test="search.searchOptions == 2">
                        AND tc.ppriceid LIKE concat('%',#{search.searchValue},'%')
                    </when>
                </choose>
            </if>
        </where>
        ) as temp
    </select>
    <select id="listJdProductTyingConfig"
            resultType="com.jiuji.oa.oacore.thirdplatform.productconfig.vo.JdProductTyingConfigVO">
        SELECT tc.id,c.id as
        configId,c.tenant_code,c.sku_id,c.product_code,tc.ppriceid,tc.create_user,tc.create_time,t.id as tenantid,
        tc.order_amount,t.tenant_name,p.product_name,p.product_color,p.ismobile1,p.costprice,p.memberprice
        vipPrice,p.que,c.platform_cost
        FROM jingdong_product_tying_config tc WITH(NOLOCK)
        LEFT JOIN dbo.jingdong_product_config c WITH(NOLOCK) ON c.id=tc.config_id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid=tc.ppriceid
        LEFT JOIN jingdong_tenant t WITH(NOLOCK) ON t.tenant_code=c.tenant_code
        <where>
            <if test="search.configId!=null">
                AND tc.config_id = #{search.configId}
            </if>
            <if test="search.tenantid!=null">
                AND t.id = #{search.tenantid}
            </if>
            <if test="search.tenantCode != null and search.tenantCode != ''">
                AND c.tenant_code = #{search.tenantCode}
            </if>
            <if test="search.ppriceid != null">
                AND tc.ppriceid = #{search.ppriceid}
            </if>
            <if test="search.searchValue != null and search.searchValue != ''">
                <choose>
                    <when test="search.searchOptions == 1">
                        AND c.sku_id LIKE concat('%',#{search.searchValue},'%')
                    </when>
                    <when test="search.searchOptions == 4">
                        and c.product_code LIKE concat('%',#{search.searchValue},'%')
                    </when>
                    <when test="search.searchOptions == 3">
                        AND p.product_name LIKE concat('%',#{search.searchValue},'%')
                    </when>
                    <when test="search.searchOptions == 2">
                        AND tc.ppriceid LIKE concat('%',#{search.searchValue},'%')
                    </when>
                </choose>
            </if>
        </where>
    </select>
    <insert id="insertProductConfigDelete">
        INSERT INTO jingdong_product_config_delete (tenant_code, sku_id, product_code, ppriceid, price_split, sync_off, sync_type, sync_ratio, sync_limit, sync_first, delete_date, delete_user)
        SELECT tenant_code, sku_id, product_code, ppriceid, price_split, sync_off, sync_type, sync_ratio, sync_limit, sync_first, #{dateTime}, #{username}
        FROM dbo.jingdong_product_config WHERE tenant_code=#{tenant};
    </insert>
    <insert id="insertProductConfigDeleteV2">
        INSERT INTO jingdong_product_config_delete (tenant_code, sku_id, product_code, ppriceid, price_split, sync_off, sync_type, sync_ratio, sync_limit, sync_first, delete_date, delete_user)
        SELECT tenant_code, sku_id, product_code, ppriceid, price_split, sync_off, sync_type, sync_ratio, sync_limit, sync_first, #{dateTime}, #{username}
        FROM dbo.jingdong_product_config WHERE tenant_code=#{tenant}
        and ppriceid in
        <foreach collection="skuidList" index="index" item="skuid" open="(" close=")" separator=",">
            #{skuid}
        </foreach>;
    </insert>
</mapper>
