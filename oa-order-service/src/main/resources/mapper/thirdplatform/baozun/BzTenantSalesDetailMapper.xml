<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.baozun.dao.BzTenantSalesDetailMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantSalesDetail">
    <result column="fk_tenant_id" jdbcType="INTEGER" property="fkTenantId" />
    <result column="line_number" jdbcType="VARCHAR" property="lineNumber" />
    <result column="upc" jdbcType="VARCHAR" property="upc" />
    <result column="combo_code" jdbcType="VARCHAR" property="comboCode" />
    <result column="unit_price" jdbcType="DECIMAL" property="unitPrice" />
    <result column="actual_price" jdbcType="DECIMAL" property="actualPrice" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="discount" jdbcType="DECIMAL" property="discount" />
    <result column="line_total" jdbcType="DECIMAL" property="lineTotal" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="baozun_tenant_sales_detail_rv" jdbcType="TIMESTAMP" property="baozunTenantSalesDetailRv" />
  </resultMap>
  <sql id="Base_Column_List">
    transaction_number, sub_id, fk_tenant_id, transaction_date, Transaction_time, store_code, slip_code,
    platform_source, transaction_type, line_number, upc, combo_code, unit_price, actual_price,
    quantity, discount, line_total, customer_id, email, name, telephone, mobile, transfer_fee,
    create_time, create_user, update_time,remark,message, is_del, baozun_tenant_sales_detail_rv
  </sql>

  <select id="listOrderItemByOutId" resultMap="BaseResultMap">
    SELECT tsd.*,p.memberprice,p.config,p.ismobile1,p.product_name,p.product_color
    FROM dbo.baozun_tenant_sales_detail tsd WITH(NOLOCK)
    LEFT JOIN dbo.productinfo p WITH(NOLOCK)
    ON p.ppriceid = tsd.ppid WHERE tsd.fk_transaction_number=#{transactionNumber}
  </select>
</mapper>

