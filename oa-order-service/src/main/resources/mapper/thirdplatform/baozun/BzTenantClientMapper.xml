<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.baozun.dao.BzTenantClientMapper">
    <insert id="insertProduct" keyProperty="master.id" useGeneratedKeys="true">
        INSERT INTO dbo.baozun_tenant_product
        (product_code, schema_code, product_no, product_name, brand, pos_cate_name, create_time, create_user, update_time, is_del, fk_tenant_id)
        VALUES(#{master.productCode}, #{master.schemaCode}
               <choose>
                   <when test="master.properties != null">
                       ,#{master.properties.productNo}, #{master.properties.productName}, #{master.properties.brand}, #{master.properties.posCateName}
                   </when>
                   <otherwise>
                       ,null,null,null,null
                   </otherwise>
               </choose>
               ,GETDATE(),'系统', GETDATE(),0,#{tenantId});
    </insert>
    <insert id="insertVariants">
        INSERT INTO dbo.baozun_tenant_variants
        (brand_sku_code, ext_code, fk_product_id,product_code, ppriceid, variant_code, schema_code, bar_code, platform_code
        ,custom_color,custom_size, create_time, create_user, update_time, is_del, fk_tenant_id,storage_capacity,network_type,time_limit)
        VALUES
         <foreach collection="variants" item="variant" separator=",">
             (#{variant.properties.brandSkuCode}, #{variant.extCode},
              <choose>
                  <when test="master != null">#{master.id},#{master.productCode},</when>
                  <otherwise>null,null,</otherwise>
              </choose>
              null, #{variant.variantCode},
              <choose>
                  <when test="master != null">#{master.schemaCode},</when>
                  <otherwise>null,</otherwise>
              </choose>
               #{variant.properties.barCode}, #{variant.properties.platformCode},
              <choose>
                  <when test="variant.properties.customColor != null">#{variant.properties.customColor.customColorName},</when>
                  <otherwise>null,</otherwise>
              </choose>
              #{variant.properties.customSize},GETDATE(), '系统',GETDATE(), 0, #{tenantId},#{variant.properties.storageCapacity}
              ,#{variant.properties.networkType},#{variant.properties.timeLimit})
         </foreach>
    </insert>
    <insert id="insertOrders">
        insert into baozun_tenant_sales_order (transaction_number, sub_id, fk_tenant_id,order_date, transaction_date, Transaction_time
        , store_code, slip_code, platform_source, customer_id, email, name, telephone, mobile, transfer_fee
        , remark, message, create_time, create_user, update_time, is_del,area_id,user_id)
        values
        <foreach collection="insertOrders" separator="," item="insertOrder">
            (#{insertOrder.orderCode}, null, #{tenantId},#{insertOrder.orderDate},null, null, #{insertOrder.warehouseCode}
            ,  #{insertOrder.platformOrderCode}, #{insertOrder.platformSource}, null, null, null, null, null, null
            , null, null, GETDATE(),'系统', GETDATE(), 0,#{insertOrder.areaId},#{insertOrder.userId})
        </foreach>
    </insert>
    <insert id="insertOrderDetails">
        insert into baozun_tenant_sales_detail
            (combo_code, line_number, fk_transaction_number, upc, unit_price, actual_price, quantity, discount, line_total, create_time, create_user, update_time, is_del,ppid,new_product_presale_flag)
            values
            <foreach collection="frezzingAndReleases" separator="," item="frezzingAndRelease">
                (null, null, #{frezzingAndRelease.orderCode}, #{frezzingAndRelease.upc}, null, null, abs(#{frezzingAndRelease.qty}), null, null, GETDATE(), '系统', GETDATE(), 0,#{frezzingAndRelease.ppid},#{frezzingAndRelease.newProductPresaleFlag})
            </foreach>

    </insert>
    <insert id="insertFile">
        insert into baozun_tenant_sales_file (file_name, file_content, is_parse, create_time, create_user, update_time, is_del)
        values (#{fileName}, #{content}, 0, GETDATE(), '系统', GETDATE(), 0)
    </insert>
    <insert id="batchInsertSaleOrders">
        INSERT INTO baozun_tenant_sales_order (transaction_number, sub_id, fk_tenant_id, order_date, transaction_date, Transaction_time, store_code
        , area_id, slip_code, platform_source, user_id, customer_id, email, name, telephone, mobile, transfer_fee,operator, remark, message, create_time
        , create_user, update_time, is_del, associated_order_number, buyer_memo)
        VALUES
               <foreach collection="isoList" item="salesDetail" separator=",">
                   (#{salesDetail.transactionNumber},#{salesDetail.subId},#{salesDetail.tenantId}, #{salesDetail.orderDate}, #{salesDetail.transactionDate}
                   , #{salesDetail.transactionTime}, #{salesDetail.storeCode}, #{salesDetail.areaId}, #{salesDetail.slipCode}, #{salesDetail.platformSource}
                   , #{salesDetail.userId}, #{salesDetail.customerID}, #{salesDetail.email}, #{salesDetail.name}, #{salesDetail.telephone}, #{salesDetail.mobile}
                   , #{salesDetail.transferFee},#{salesDetail.operator},null, null, GETDATE(), '系统', GETDATE(), 0, #{salesDetail.associatedOrderNumber}, #{salesDetail.buyerMemo})
               </foreach>

    </insert>
    <insert id="batchInsertSaleDetails">
        INSERT INTO baozun_tenant_sales_detail (combo_code, line_number, fk_transaction_number, transaction_type,order_label, upc, ppid, unit_price, actual_price
        , quantity, discount, line_total, refund_time, create_time, create_user, update_time, is_del, kc_deal_result, kc_deal_time,subinfo_deal_result, basket_id)
        VALUES
        <foreach collection="isodList" item="salesDetail" separator=",">
            (#{salesDetail.comboCode}, #{salesDetail.lineNumber}, #{salesDetail.transactionNumber}, #{salesDetail.transactionType},#{salesDetail.orderLabel}, #{salesDetail.upc},#{salesDetail.ppid}
            , #{salesDetail.unitPrice}, #{salesDetail.actualPrice}, #{salesDetail.quantity}, #{salesDetail.discount}, #{salesDetail.lineTotal}, null
            , GETDATE(), '系统', GETDATE(), 0, null,null,0, #{salesDetail.basketId})
        </foreach>
    </insert>
    <insert id="batchInsertSaleSn">
        INSERT INTO baozun_tenant_sales_detail_sninfo (fk_transaction_number, slip_code, line_number, upc, sn,name,email,mobile,create_time
        , create_user, update_time, is_del,inv_status)
        VALUES
        <foreach collection="sns" item="sn" separator=",">
            (#{sn.transactionNumber}, #{sn.slipCode}, #{sn.lineNumber}, #{sn.upc}, #{sn.sn},#{sn.name},#{sn.email},#{sn.mobile}, GETDATE(), '系统', GETDATE(), 0,#{sn.invStatus})
        </foreach>
    </insert>
    <insert id="batchInsertSaleSt">
        INSERT INTO baozun_tenant_sales_tender (fk_transaction_number, payment_type, transaction_amount, create_time, create_user, update_time, is_del, payment_Channel)
        VALUES
        <foreach collection="sts" item="st" separator=",">
            (#{st.transactionNumber}, #{st.paymentType},cast(#{st.transactionAmount} as decimal(18,4)), GETDATE(), '系统', GETDATE(), 0, #{st.paymentChannel})
        </foreach>
    </insert>
    <update id="updateProduct">
        UPDATE dbo.baozun_tenant_product
        <set>
            <if test="master.schemaCode != null">
                schema_code=#{master.schemaCode},
            </if>
            <if test="master.properties != null">
                <if test="master.properties.productNo != null">
                    product_no=#{master.properties.productNo},
                </if>
                <if test="master.properties.productName != null">
                    product_name=#{master.properties.productName},
                </if>
                <if test="master.properties.brand != null">
                    brand=#{master.properties.brand},
                </if>
                <if test="master.properties.posCateName != null">
                    pos_cate_name=#{master.properties.posCateName},
                </if>
            </if>
            update_time = GETDATE()
        </set>
            WHERE fk_tenant_id = #{tenantId} and product_code= #{master.productCode}

    </update>
    <update id="updateVariants">
        UPDATE dbo.baozun_tenant_variants
        <set>
            <trim prefix="fk_product_id=case" suffix=" end,">
                <foreach collection="variants" item="variant">
                    <if test="master != null and master.id != null">
                        when  brand_sku_code = #{variant.properties.brandSkuCode} then #{master.id}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_code=case" suffix=" end,">
                <foreach collection="variants" item="variant">
                    <if test="master != null and master.productCode != null">
                        when  brand_sku_code = #{variant.properties.brandSkuCode} then #{master.productCode}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ext_code=case" suffix=" end,">
                <foreach collection="variants" item="variant">
                    <if test="variant.extCode != null">
                        when  brand_sku_code = #{variant.properties.brandSkuCode} then #{variant.extCode}
                    </if>
                </foreach>
            </trim>
            <if test="master != null and master.schemaCode != null and master.schemaCode != ''">
                schema_code=#{master.schemaCode},
            </if>
            <trim prefix="bar_code=case" suffix=" end,">
                <foreach collection="variants" item="variant">
                    <if test="variant.properties.barCode != null and variant.properties.barCode != ''">
                        when  brand_sku_code = #{variant.properties.brandSkuCode} then #{variant.properties.barCode}
                    </if>
                </foreach>
            </trim>
            <trim prefix="platform_code=case" suffix=" end,">
                <foreach collection="variants" item="variant">
                    <if test="variant.properties.platformCode != null and variant.properties.platformCode != ''">
                        when  brand_sku_code = #{variant.properties.brandSkuCode} then #{variant.properties.platformCode}
                    </if>
                </foreach>
            </trim>
            <trim prefix="custom_color=case" suffix=" end,">
                <foreach collection="variants" item="variant">
                    <if test="variant.properties.customColor != null and variant.properties.customColor.customColorName != ''">
                        when  brand_sku_code = #{variant.properties.brandSkuCode} then #{variant.properties.customColor.customColorName}
                    </if>
                </foreach>
            </trim>
            <trim prefix="custom_size=case" suffix=" end,">
                <foreach collection="variants" item="variant">
                    <if test="variant.properties.customSize != null and variant.properties.customSize != ''">
                        when  brand_sku_code = #{variant.properties.brandSkuCode} then #{variant.properties.customSize}
                    </if>
                </foreach>
            </trim>
            <trim prefix="storage_capacity=case" suffix=" end,">
                <foreach collection="variants" item="variant">
                    <if test="variant.properties.storageCapacity != null and variant.properties.storageCapacity != ''">
                        when  brand_sku_code = #{variant.properties.brandSkuCode} then #{variant.properties.storageCapacity}
                    </if>
                </foreach>
            </trim>
            <trim prefix="network_type=case" suffix=" end,">
                <foreach collection="variants" item="variant">
                    <if test="variant.properties.networkType != null and variant.properties.networkType != ''">
                        when  brand_sku_code = #{variant.properties.brandSkuCode} then #{variant.properties.networkType}
                    </if>
                </foreach>
            </trim>
            <trim prefix="time_limit=case" suffix=" end,">
                <foreach collection="variants" item="variant">
                    <if test="variant.properties.timeLimit != null and variant.properties.timeLimit != ''">
                        when  brand_sku_code = #{variant.properties.brandSkuCode} then #{variant.properties.timeLimit}
                    </if>
                </foreach>
            </trim>
            <trim prefix="market_classification=case" suffix=" end,">
                <foreach collection="variants" item="variant">
                    <if test="variant.properties.marketClassification != null and variant.properties.marketClassification != ''">
                        when  brand_sku_code = #{variant.properties.brandSkuCode} then #{variant.properties.marketClassification}
                    </if>
                </foreach>
            </trim>
            update_time=GETDATE()
        </set>
        WHERE fk_tenant_id = #{tenantId} and brand_sku_code in
        <foreach collection="variants" item="variant" open="(" close=")" separator=",">
            #{variant.properties.brandSkuCode}
        </foreach>

    </update>
    <update id="updateOrders">
        UPDATE dbo.baozun_tenant_sales_order
        <set>
            fk_tenant_id = #{tenantId},update_time = GETDATE(),
            <trim prefix="order_date=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.orderDate != null and updateOrder.orderDate != ''">
                        when  transaction_number = #{updateOrder.orderCode} then #{updateOrder.orderDate}
                    </if>
                </foreach>
            </trim>
            <trim prefix="store_code=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.warehouseCode != null and updateOrder.warehouseCode != ''">
                        when  transaction_number = #{updateOrder.orderCode} then #{updateOrder.warehouseCode}
                    </if>
                </foreach>
            </trim>
            <trim prefix="slip_code=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.platformOrderCode != null and updateOrder.platformOrderCode != ''">
                        when  transaction_number = #{updateOrder.orderCode} then #{updateOrder.platformOrderCode}
                    </if>
                </foreach>
            </trim>
            <trim prefix="platform_source=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.platformSource != null and updateOrder.platformSource != ''">
                        when  transaction_number = #{updateOrder.orderCode} then #{updateOrder.platformSource}
                    </if>
                </foreach>
            </trim>
            <trim prefix="area_id=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.areaId != null and updateOrder.areaId != ''">
                        when  transaction_number = #{updateOrder.orderCode} then #{updateOrder.areaId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="user_id=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.userId != null and updateOrder.userId != ''">
                        when  transaction_number = #{updateOrder.orderCode} then #{updateOrder.userId}
                    </if>
                </foreach>
            </trim>
        </set>
        WHERE transaction_number in
            <foreach collection="updateOrders" item="updateOrder" separator="," open="(" close=")">
                #{updateOrder.orderCode}
            </foreach>
    </update>
    <update id="updateOrderDetails">
        UPDATE baozun_tenant_sales_detail
        <set>
            update_time = GETDATE(),
            <trim prefix="ppid=case" suffix=" end,">
                <foreach collection="updateOrderDetails" item="updateOrderDetail">
                    <if test="updateOrderDetail.ppid != null">
                        when  id = #{updateOrderDetail.orderDetailId} then #{updateOrderDetail.ppid}
                    </if>
                </foreach>
            </trim>
            <trim prefix="quantity=case" suffix=" end,">
                <foreach collection="updateOrderDetails" item="updateOrderDetail">
                    <if test="updateOrderDetail.qty != null">
                        when  id = #{updateOrderDetail.orderDetailId} then #{updateOrderDetail.qty}
                    </if>
                </foreach>
            </trim>
            <trim prefix="new_product_presale_flag=case" suffix=" end,">
                <foreach collection="updateOrderDetails" item="updateOrderDetail">
                    <if test="updateOrderDetail.newProductPresaleFlag != null">
                        when  id = #{updateOrderDetail.orderDetailId} then #{updateOrderDetail.newProductPresaleFlag}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_del=case" suffix="else 1 end,">
                <foreach collection="updateOrderDetails" item="updateOrderDetail">
                    when  id in
                            <foreach collection="updateOrderDetails" open="(" close=")" separator="," item="updateOrderDetail">
                                #{updateOrderDetail.orderDetailId}
                            </foreach>
                        then is_del
                </foreach>
            </trim>

        </set>
        WHERE fk_transaction_number in
            <foreach collection="allOrderIds" open="(" close=")" separator="," item="orderId">
                #{orderId}
            </foreach>

    </update>
    <update id="batchUpdateOrderMsg">
        update baozun_tenant_sales_order
        <set>
            <trim prefix="message=case" suffix=" end,">
                <foreach collection="orderSubIdList" item="order">
                    <if test="order.message != null and order.message != ''">
                        when transaction_number = #{order.orderCode} then RIGHT(isnull(message,'')+' '+#{order.message},500)
                    </if>
                </foreach>
            </trim>
            <trim prefix="remark=case" suffix=" end,">
                <foreach collection="orderSubIdList" item="order">
                    <if test="order.remark != null and order.remark != ''">
                        when transaction_number = #{order.orderCode} then #{order.remark}
                    </if>
                </foreach>
            </trim>
        </set>
            where transaction_number in
                <foreach collection="orderSubIdList" item="order" separator="," open="(" close=")">
                    #{order.orderCode}
                </foreach>
    </update>
    <update id="batchUpdateSaleOrders">
        UPDATE baozun_tenant_sales_order
        <set>
            <trim prefix="order_date=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.orderDate != null and updateOrder.orderDate != ''">
                        when  transaction_number = #{updateOrder.transactionNumber} then #{updateOrder.orderDate}
                    </if>
                </foreach>
            </trim>
            <trim prefix="transaction_date=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.transactionDate != null and updateOrder.transactionDate != ''">
                        when transaction_number = #{updateOrder.transactionNumber} then #{updateOrder.transactionDate}
                    </if>
                </foreach>
            </trim>
            <trim prefix="Transaction_time=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.transactionTime != null and updateOrder.transactionTime != ''">
                        when transaction_number = #{updateOrder.transactionNumber} then #{updateOrder.transactionTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix="store_code=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.storeCode != null and updateOrder.storeCode != ''">
                        when transaction_number = #{updateOrder.transactionNumber} then #{updateOrder.storeCode}
                    </if>
                </foreach>
            </trim>
            <trim prefix="slip_code=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.slipCode != null and updateOrder.slipCode != ''">
                        when transaction_number = #{updateOrder.transactionNumber} then #{updateOrder.slipCode}
                    </if>
                </foreach>
            </trim>
            <trim prefix="platform_source=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.platformSource != null and updateOrder.platformSource != ''">
                        when transaction_number = #{updateOrder.transactionNumber} then #{updateOrder.platformSource}
                    </if>
                </foreach>
            </trim>
            <trim prefix="customer_id=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.customerID != null and updateOrder.customerID != ''">
                        when transaction_number = #{updateOrder.transactionNumber} then #{updateOrder.customerID}
                    </if>
                </foreach>
            </trim>
            <trim prefix="email=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.email != null and updateOrder.email != ''">
                        when transaction_number = #{updateOrder.transactionNumber} then #{updateOrder.email}
                    </if>
                </foreach>
            </trim>
            <trim prefix="name=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.name != null and updateOrder.name != ''">
                        when transaction_number = #{updateOrder.transactionNumber} then #{updateOrder.name}
                    </if>
                </foreach>
            </trim>
            <trim prefix="telephone=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.telephone != null and updateOrder.telephone != ''">
                        when transaction_number = #{updateOrder.transactionNumber} then #{updateOrder.telephone}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mobile=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.mobile != null and updateOrder.mobile != ''">
                        when transaction_number = #{updateOrder.transactionNumber} then #{updateOrder.mobile}
                    </if>
                </foreach>
            </trim>
            <trim prefix="transfer_fee=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.transferFee != null">
                        when transaction_number = #{updateOrder.transactionNumber} then #{updateOrder.transferFee}
                    </if>
                </foreach>
            </trim>
            <trim prefix="operator=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.operator != null">
                        when transaction_number = #{updateOrder.transactionNumber} then #{updateOrder.operator}
                    </if>
                </foreach>
            </trim>
            <trim prefix="associated_order_number=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.associatedOrderNumber != null">
                        when transaction_number = #{updateOrder.transactionNumber} then #{updateOrder.associatedOrderNumber}
                    </if>
                </foreach>
            </trim>
            <!--增加更新字段 buyerMemo-->
            <trim prefix="buyer_memo=case" suffix=" end,">
                <foreach collection="updateOrders" item="updateOrder">
                    <if test="updateOrder.buyerMemo != null and updateOrder.buyerMemo != ''">
                        when transaction_number = #{updateOrder.transactionNumber} then #{updateOrder.buyerMemo}
                    </if>
                </foreach>
            </trim>

            update_time =  GETDATE()
        </set>
        WHERE transaction_number in
            <foreach collection="updateOrders" item="updateOrder" separator="," open="(" close=")">
                #{updateOrder.transactionNumber}
            </foreach>

    </update>
    <update id="batchUpdateSaleDetails">
        UPDATE baozun_tenant_sales_detail
        <set>
            <trim prefix="combo_code=case" suffix=" end,">
                <foreach collection="updateOrderDetails" item="updateOrderDetail">
                    <if test="updateOrderDetail.comboCode != null and updateOrderDetail.comboCode != ''">
                        when id = #{updateOrderDetail.orderDetailId} then #{updateOrderDetail.comboCode}
                    </if>
                </foreach>
            </trim>
            <trim prefix="line_number=case" suffix=" end,">
                <foreach collection="updateOrderDetails" item="updateOrderDetail">
                    <if test="updateOrderDetail.lineNumber != null and updateOrderDetail.lineNumber != ''">
                        when id = #{updateOrderDetail.orderDetailId} then #{updateOrderDetail.lineNumber}
                    </if>
                </foreach>
            </trim>
            <trim prefix="fk_transaction_number=case" suffix=" end,">
                <foreach collection="updateOrderDetails" item="updateOrderDetail">
                    <if test="updateOrderDetail.transactionNumber != null and updateOrderDetail.transactionNumber != ''">
                        when id = #{updateOrderDetail.orderDetailId} then #{updateOrderDetail.transactionNumber}
                    </if>
                </foreach>
            </trim>
            <trim prefix="transaction_type=case" suffix=" end,">
                <foreach collection="updateOrderDetails" item="updateOrderDetail">
                    <if test="updateOrderDetail.transactionType != null and updateOrderDetail.transactionType != ''">
                        when id = #{updateOrderDetail.orderDetailId} then #{updateOrderDetail.transactionType}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_label=case" suffix=" end,">
                <foreach collection="updateOrderDetails" item="updateOrderDetail">
                    <if test="updateOrderDetail.orderLabel != null and updateOrderDetail.orderLabel != ''">
                        when id = #{updateOrderDetail.orderDetailId} then #{updateOrderDetail.orderLabel}
                    </if>
                </foreach>
            </trim>
            <trim prefix="upc=case" suffix=" end,">
                <foreach collection="updateOrderDetails" item="updateOrderDetail">
                    <if test="updateOrderDetail.upc != null and updateOrderDetail.upc != ''">
                        when id = #{updateOrderDetail.orderDetailId} then #{updateOrderDetail.upc}
                    </if>
                </foreach>
            </trim>
            <trim prefix="unit_price=case" suffix=" end,">
                <foreach collection="updateOrderDetails" item="updateOrderDetail">
                    <if test="updateOrderDetail.unitPrice != null">
                        when id = #{updateOrderDetail.orderDetailId} then #{updateOrderDetail.unitPrice}
                    </if>
                </foreach>
            </trim>
            <trim prefix="actual_price=case" suffix=" end,">
                <foreach collection="updateOrderDetails" item="updateOrderDetail">
                    <if test="updateOrderDetail.actualPrice != null">
                        when id = #{updateOrderDetail.orderDetailId} then #{updateOrderDetail.actualPrice}
                    </if>
                </foreach>
            </trim>
            <trim prefix="quantity=case" suffix=" end,">
                <foreach collection="updateOrderDetails" item="updateOrderDetail">
                    <if test="updateOrderDetail.quantity != null">
                        when id = #{updateOrderDetail.orderDetailId} then #{updateOrderDetail.quantity}
                    </if>
                </foreach>
            </trim>
            <trim prefix="discount=case" suffix=" end,">
                <foreach collection="updateOrderDetails" item="updateOrderDetail">
                    <if test="updateOrderDetail.discount != null">
                        when id = #{updateOrderDetail.orderDetailId} then #{updateOrderDetail.discount}
                    </if>
                </foreach>
            </trim>
            <trim prefix="line_total=case" suffix=" end,">
                <foreach collection="updateOrderDetails" item="updateOrderDetail">
                    <if test="updateOrderDetail.lineTotal != null">
                        when id = #{updateOrderDetail.orderDetailId} then #{updateOrderDetail.lineTotal}
                    </if>
                </foreach>
            </trim>
            update_time= GETDATE(),kc_deal_result = case kc_deal_result when 0 then null else kc_deal_result end
        </set>
          WHERE id in
            <foreach collection="updateOrderDetails" item="updateOrderDetail" open="(" close=")" separator=",">
                #{updateOrderDetail.orderDetailId}
            </foreach>

    </update>
    <update id="batchUpdateSaleSn">
        UPDATE baozun_tenant_sales_detail_sninfo
        <set>
            <trim prefix="fk_transaction_number=case" suffix=" end,">
                <foreach collection="sns" item="sn">
                    <if test="sn.transactionNumber != null and sn.transactionNumber != ''">
                        when id = #{sn.snLocalId} then #{sn.transactionNumber}
                    </if>
                </foreach>
            </trim>
            <trim prefix="slip_code=case" suffix=" end,">
                <foreach collection="sns" item="sn">
                    <if test="sn.slipCode != null and sn.slipCode != ''">
                        when id = #{sn.snLocalId} then #{sn.slipCode}
                    </if>
                </foreach>
            </trim>
            <trim prefix="line_number=case" suffix=" end,">
                <foreach collection="sns" item="sn">
                    <if test="sn.lineNumber != null and sn.lineNumber != ''">
                        when id = #{sn.snLocalId} then #{sn.lineNumber}
                    </if>
                </foreach>
            </trim>
            <trim prefix="upc=case" suffix=" end,">
                <foreach collection="sns" item="sn">
                    <if test="sn.upc != null and sn.upc != ''">
                        when id = #{sn.snLocalId} then #{sn.upc}
                    </if>
                </foreach>
            </trim>
            <trim prefix="sn=case" suffix=" end,">
                <foreach collection="sns" item="sn">
                    <if test="sn.sn != null and sn.sn != ''">
                        when id = #{sn.snLocalId} then #{sn.sn}
                    </if>
                </foreach>
            </trim>
            <trim prefix="name=case" suffix=" end,">
                <foreach collection="sns" item="sn">
                    <if test="sn.name != null and sn.name != ''">
                        when id = #{sn.snLocalId} then #{sn.name}
                    </if>
                </foreach>
            </trim>
            <trim prefix="email=case" suffix=" end,">
                <foreach collection="sns" item="sn">
                    <if test="sn.email != null and sn.email != ''">
                        when id = #{sn.snLocalId} then #{sn.email}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mobile=case" suffix=" end,">
                <foreach collection="sns" item="sn">
                    <if test="sn.mobile != null and sn.mobile != ''">
                        when id = #{sn.snLocalId} then #{sn.mobile}
                    </if>
                </foreach>
            </trim>
            <trim prefix="inv_status=case" suffix=" end,">
                <foreach collection="sns" item="sn">
                    <if test="sn.invStatus != null and sn.invStatus != ''">
                        when id = #{sn.snLocalId} then #{sn.invStatus}
                    </if>
                </foreach>
            </trim>
            update_time = GETDATE()
        </set>
        WHERE id in
        <foreach collection="sns" item="sn" open="(" close=")" separator=",">
            #{sn.snLocalId}
        </foreach>
    </update>
    <update id="batchUpdateSaleSt">
        UPDATE baozun_tenant_sales_tender
        <set>
            <trim prefix="fk_transaction_number=case" suffix=" end,">
                <foreach collection="sts" item="st">
                    <if test="st.transactionNumber != null and st.transactionNumber != ''">
                        when id = #{st.stLocalId} then #{st.transactionNumber}
                    </if>
                </foreach>
            </trim>
            <trim prefix="payment_type=case" suffix=" end,">
                <foreach collection="sts" item="st">
                    <if test="st.paymentType != null and st.paymentType != ''">
                        when id = #{st.stLocalId} then #{st.paymentType}
                    </if>
                </foreach>
            </trim>
            <trim prefix="transaction_amount=case" suffix=" end,">
                <foreach collection="sts" item="st">
                    <if test="st.transactionAmount != null">
                        when id = #{st.stLocalId} then cast(#{st.transactionAmount} as decimal(18,4))
                    </if>
                </foreach>
            </trim>
            <!--增加payment_Channel-->
            <trim prefix="payment_Channel=case" suffix=" end,">
                <foreach collection="sts" item="st">
                    <if test="st.paymentChannel != null and st.paymentChannel != ''">
                        when id = #{st.stLocalId} then #{st.paymentChannel}
                    </if>
                </foreach>
            </trim>
            update_time = GETDATE()
        </set>
        WHERE id in
        <foreach collection="sts" item="st" open="(" close=")" separator=",">
            #{st.stLocalId}
        </foreach>
    </update>
    <update id="updateFileParse">
        UPDATE baozun_tenant_sales_file SET is_parse=1,update_time=getdate() WHERE id= #{fileInfo.id}
    </update>
    <update id="batchUpdateRefundOrderLocalInfo">
        update btso
        set btso.sub_id = btso3.sub_id,btso.area_id = btso3.area_id, btso.fk_tenant_id = btso3.fk_tenant_id,btso.order_date=btso3.order_date
          ,btso.user_id=btso3.user_id,btso.customer_id=btso3.customer_id,btso.email=btso3.email,btso.remark=btso3.remark
        from baozun_tenant_sales_order btso with(nolock)
        inner join (
            select btso.transaction_number,btso2.sub_id,btso2.area_id,btso2.fk_tenant_id,btso2.order_date,btso2.user_id,btso2.customer_id,
                    btso2.email,btso2.remark,ROW_NUMBER() over(partition by btso.transaction_number,btso2.platform_source,btso2.slip_code order by btso2.create_time asc,case s.sub_check when 3 then 1 else 2 end asc) rank
            from baozun_tenant_sales_order btso with(nolock)
            inner join baozun_tenant_sales_order btso2 with(nolock) on btso2.platform_source = btso.platform_source and btso2.slip_code = btso.slip_code
            and btso2.sub_id is not null and isnull(btso2.is_del,0)=0
            and exists(select 1 from baozun_tenant_sales_detail btsd2 with(nolock) where btsd2.fk_transaction_number = btso2.transaction_number and btsd2.transaction_type = 'S' and isnull(btsd2.is_del,0)=0)
            inner join sub s with(nolock) on s.sub_id = btso2.sub_id and s.sub_check not in (4,8,9)
            where  btso.sub_id is null and DATEADD(MINUTE, 1, btso.create_time) &gt;= btso2.create_time
            and btso.transaction_number in
            <foreach collection="transactionNumbers" item="transactionNumber" open="(" close=")" separator=",">
                #{transactionNumber}
            </foreach>
        ) btso3 on btso3.transaction_number = btso.transaction_number
        where btso3.rank = 1


    </update>
    <update id="batchUpdateRefundDetailLocalInfo">
        update btsd
        set btsd.ppid = btsd3.ppid
        from baozun_tenant_sales_detail btsd with(nolock)
        inner join (
            select btsd.id,btsd2.ppid,ROW_NUMBER() over(partition by btso.transaction_number,btso2.platform_source,btso2.slip_code,btsd.id order by btso2.create_time asc) rank
            from baozun_tenant_sales_detail btsd with(nolock)
            inner join baozun_tenant_sales_order btso with(nolock) on btso.transaction_number = btsd.fk_transaction_number
            inner join baozun_tenant_sales_order btso2 with(nolock) on btso2.platform_source = btso.platform_source and btso2.slip_code = btso.slip_code and isnull(btso2.is_del,0)=0
                and exists(select 1 from sub s with(nolock) where s.sub_id = btso2.sub_id  and s.sub_check not in (4,8,9))
            inner join baozun_tenant_sales_detail btsd2 with(nolock) on btsd2.fk_transaction_number = btso2.transaction_number and btsd2.upc = btsd.upc and isnull(btsd2.is_del,0)=0
            and btsd2.ppid is not null and btsd2.transaction_type = 'S'
            where btsd.ppid is null and btsd.fk_transaction_number in
            <foreach collection="transactionNumbers" item="transactionNumber" open="(" close=")" separator=",">
                #{transactionNumber}
            </foreach>
        ) btsd3 on btsd3.id = btsd.id
        where btsd3.rank = 1
    </update>
    <select id="getTenantApp" resultType="com.jiuji.oa.oacore.thirdplatform.baozun.bo.DecryptBodyBO">
        SELECT bt.app_key,bt.app_secret,bt.id tenantId
        FROM baozun_tenant bt with(nolock) where bt.app_key = #{appKey} and isnull(bt.is_del,0)=0 and isnull(bt.is_enable,0)=1
    </select>
    <select id="selectProductId" resultType="java.lang.Integer">
        select top 1 btp.id from baozun_tenant_product btp with(nolock) where isnull(btp.is_del,0)=0 and btp.fk_tenant_id = #{tenantId}
            and btp.product_code = #{master.productCode}
    </select>
    <select id="listVariantId" resultType="java.lang.String">
        SELECT brand_sku_code
        FROM dbo.baozun_tenant_variants  btv with(nolock)
        where isnull(is_del,0)=0 and fk_tenant_id = #{tenantId}  and btv.brand_sku_code in
        <foreach collection="variants" open="(" close=")" separator="," item="variant">
            #{variant.properties.brandSkuCode}
        </foreach>
    </select>
    <select id="existsOrderIds" resultType="com.jiuji.oa.oacore.thirdplatform.baozun.bo.S47RequestBodyBO$OrderSubIdBO">
        select distinct btsd.transaction_number orderCode,btsd.fk_tenant_id tenantId,btsd.sub_id subId,btsd.is_del isDel
        from baozun_tenant_sales_order btsd with(nolock)
        <where>
            btsd.transaction_number in
            <foreach collection="orderIds" open="(" close=")" separator="," item="orderId">
                #{orderId}
            </foreach>
        </where>
    </select>
    <select id="existsOrderUpcs"
            resultType="com.jiuji.oa.oacore.thirdplatform.baozun.bo.S47RequestBodyBO$OrderCodeUpcBO">
        select distinct btsd.id,btsd.upc,btsd.fk_transaction_number orderCode,btsd.quantity,btsd.line_number
        from baozun_tenant_sales_detail btsd with(nolock) where isnull(btsd.is_del,0)=0 and
            <foreach collection="frezzingAndReleases" open="(" close=")" separator="or" item="frezzingAndRelease">
                (btsd.fk_transaction_number = #{frezzingAndRelease.orderCode} and btsd.upc = #{frezzingAndRelease.upc})
            </foreach>
        order by btsd.line_number asc, btsd.id asc
    </select>
    <select id="listUpcPpid" resultType="com.jiuji.oa.oacore.thirdplatform.baozun.bo.S47RequestBodyBO$UpcPpidBO">
        select btv.brand_sku_code upc, btv.ppriceid
        from baozun_tenant_variants btv with(nolock) where isnull(is_del,0)=0 and btv.fk_tenant_id = #{tenantId} and btv.brand_sku_code in
            <foreach collection="upcList" item="upc" separator="," open="(" close=")">
                #{upc}
            </foreach>
    </select>
    <select id="listHouseArea"
            resultType="com.jiuji.oa.oacore.thirdplatform.baozun.bo.S47RequestBodyBO$HouseAreaBO">
        select area_id areaId, tenant_store_code warehouseCode, ai.xtenant xtenant
        from baozun_tenant_area bta with(nolock)
             left join areainfo ai with(nolock) on ai.id = bta.area_id
        where isnull(bta.is_del,0)=0 and isnull(bta.is_enable,0)=1 and bta.fk_tenant_id = #{tenantId}
           and bta.tenant_store_code in
           <foreach collection="houseSet" open="(" close=")" separator="," item="house">
               #{house}
           </foreach>
    </select>
    <select id="listPlatformUser"
            resultType="com.jiuji.oa.oacore.thirdplatform.baozun.bo.S47RequestBodyBO$PlatformUserBO">
        select child_merchant_name platformSource,user_id userId
        from baozun_tenant_app btap with(nolock)
        where isnull(btap.is_del,0)=0 and btap.fk_tenant_id = #{tenantId} and child_merchant_name in
        <foreach collection="platformSet" open="(" close=")" separator="," item="platform">
            #{platform}
        </foreach>
    </select>
    <select id="getSftpInfo" resultType="com.jiuji.oa.oacore.thirdplatform.baozun.bo.SftpInfoBO">
        select top 1 sakc.appapiUrl appApiUrl,sakc.appkey appKey,sakc.appsecret appSecret from sysAppkeyConfig sakc with(nolock) where sakc.appkeyType = 9
    </select>
    <select id="existsFile" resultType="java.lang.Boolean">
        select count(1) where exists(select 1 from baozun_tenant_sales_file btsf with(nolock) where file_name = #{fileName} and isnull(is_del,0)=0)
    </select>
    <select id="listNotParseFile" resultType="com.jiuji.oa.oacore.thirdplatform.baozun.bo.SftpInfoBO$FileInfo">
        select top 100 btsf.id,btsf.file_name name,btsf.file_content content
        from baozun_tenant_sales_file btsf with(nolock) where isnull(btsf.is_del,0)=0 and isnull(btsf.is_parse,0)=0
        order by create_time asc
    </select>
    <select id="existsSnIds" resultType="com.jiuji.oa.oacore.thirdplatform.baozun.bo.S47RequestBodyBO$OrderCodeUpcBO">
        select btsds.id,btsds.upc,btsds.fk_transaction_number orderCode
        from baozun_tenant_sales_detail_sninfo btsds with(nolock)
        where
            <foreach collection="snList" item="sn" separator="or">
                (btsds.fk_transaction_number = #{sn.transactionNumber} and btsds.upc = #{sn.upc})
            </foreach>
    </select>
    <select id="existsStIds"
            resultType="com.jiuji.oa.oacore.thirdplatform.baozun.bo.S47RequestBodyBO$OrderCodeUpcBO">
        select btst.id,btst.fk_transaction_number orderCode
        from baozun_tenant_sales_tender btst with(nolock)
        where btst.fk_transaction_number in
        <foreach collection="stList" item="st" separator="," open="(" close=")">
            #{st.transactionNumber}
        </foreach>
    </select>
    <select id="getInvoiceQRcodeInfo" resultType="com.jiuji.oa.oacore.thirdplatform.baozun.bo.InvoiceQRcodeInfoBo">
        SELECT top 1 isnull(isnull(btso.associated_order_number, btso.slip_code),s.sub_id) transactionNumber, bta.tenant_store_code storeCode,
               isnull(btso.platform_source, 'HDPOS') platformSource,bt.app_key appKey, bt.app_secret appSecret
        from sub s with(nolock)
            inner join baozun_tenant_area bta with(nolock) on bta.area_id = s.areaid and isnull(bta.is_del,0) = 0 and isnull(bta.is_enable, 1) = 1
            left join baozun_tenant_sales_order btso with(nolock) on btso.sub_id = s.sub_id
            and EXISTS(select 1 from baozun_tenant_sales_detail btsd with(nolock) where btsd.fk_transaction_number = btso.transaction_number and btsd.transaction_type = 'S')
            left join baozun_tenant bt with(nolock) on bt.id = bta.fk_tenant_id
        where s.sub_id = #{subId}
        order by s.sub_id desc, btso.create_time desc
    </select>
    <select id="existsAsIds"
            resultType="com.jiuji.oa.oacore.thirdplatform.baozun.bo.S47RequestBodyBO$OrderCodeUpcBO">
        select btas.id,btas.fk_transaction_number orderCode
        from baozun_tenant_sales_addservice btas with(nolock)
        where btas.fk_transaction_number in
        <foreach collection="assList" item="ass" separator="," open="(" close=")">
            #{ass.fkTransactionNumber}
        </foreach>
    </select>
    <select id="listTradeDateCompensate" resultType="java.lang.String">
        SELECT distinct btso.transaction_number
        FROM baozun_tenant_sales_order btso WITH (NOLOCK)
        INNER JOIN sub WITH (NOLOCK)
        ON sub.sub_id = btso.sub_id
        AND sub.sub_check IN (0, 1)
        WHERE btso.is_del = 0
        AND btso.platform_source != 'HDPOS'
        AND btso.create_time >= DATEADD(MINUTE, -#{timeout}, GETDATE())
    </select>
    <insert id="batchInsertSaleAs">
        INSERT INTO baozun_tenant_sales_addservice (fk_transaction_number, payment_type, amount, create_time, update_time, is_del)
        VALUES
        <foreach collection="assList" item="ass" separator=",">
            (#{ass.fkTransactionNumber}, #{ass.paymentType},cast(#{ass.amount} as decimal(18,4)), GETDATE(), GETDATE(), 0)
        </foreach>
    </insert>
    <update id="batchUpdateSaleAs">
        UPDATE baozun_tenant_sales_addservice
        <set>
            <trim prefix="fk_transaction_number=case" suffix=" end,">
                <foreach collection="assList" item="ass">
                    <if test="ass.fkTransactionNumber != null and ass.fkTransactionNumber != ''">
                        when id = #{ass.id} then #{ass.fkTransactionNumber}
                    </if>
                </foreach>
            </trim>
            <trim prefix="payment_type=case" suffix=" end,">
                <foreach collection="assList" item="ass">
                    <if test="ass.paymentType != null and ass.paymentType != ''">
                        when id = #{ass.id} then #{ass.paymentType}
                    </if>
                </foreach>
            </trim>
            <trim prefix="amount=case" suffix=" end,">
                <foreach collection="assList" item="ass">
                    <if test="ass.amount != null">
                        when id = #{ass.id} then cast(#{ass.amount} as decimal(18,4))
                    </if>
                </foreach>
            </trim>
            update_time = GETDATE()
        </set>
        WHERE id in
        <foreach collection="assList" item="ass" open="(" close=")" separator=",">
            #{ass.id}
        </foreach>
    </update>
</mapper>
