<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.baozun.dao.BzTenantSubMapper">
    <update id="updateSubCommentBySubId">
        UPDATE dbo.sub
        SET comment=isnull(comment,'')+';'+#{orderLabelComment}
        WHERE isnull(comment,'') not like concat('%',#{orderLabelComment}) and sub_id in
        <foreach collection="subIds" separator="," open="(" close=")" item="subId">
            #{subId}
        </foreach>

    </update>
    <update id="updateOrderRemarkBySubId">
        UPDATE dbo.baozun_tenant_sales_order
        SET remark=isnull(remark,'')+';'+#{orderLabelComment}
        WHERE isnull(remark,'') not like concat('%',#{orderLabelComment}) and transaction_number in
        <foreach collection="transactionNumbers" separator="," open="(" close=")" item="transactionNumber">
            #{transactionNumber}
        </foreach>
    </update>
    <update id="updateSubDealResult">
        UPDATE dbo.baozun_tenant_sales_detail
        SET subinfo_deal_result = isnull(subinfo_deal_result,0)-(isnull(subinfo_deal_result,0) / #{location} % 10 * #{location}) + #{result} * #{location}
        WHERE id in
        <foreach collection="ids" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </update>
    <update id="updateSubInuserBySubId">
        UPDATE dbo.sub
        <set>
            Inuser = #{ch999Name},
            <trim prefix="sub_to=case" suffix=" end,">
                <foreach collection="operators" item="operator">
                    <if test="operator.name != null and operator.name != ''">
                        when sub_id = #{operator.subId} then #{operator.name}
                    </if>
                </foreach>
            </trim>
            <trim prefix="sub_tel=case" suffix=" end,">
                <foreach collection="operators" item="operator">
                    <if test="operator.telephone != null and operator.telephone != ''">
                        when sub_id = #{operator.subId} then substring(#{operator.telephone},1,13)
                    </if>
                </foreach>
            </trim>
            <trim prefix="sub_mobile=case" suffix=" end,">
                <foreach collection="operators" item="operator">
                    <if test="operator.mobile != null and operator.mobile != ''">
                        when sub_id = #{operator.subId} then substring(#{operator.mobile},1,15)
                    </if>
                </foreach>
            </trim>
            <trim prefix="comment=isnull(comment,'')+';'+case" suffix=" end,">
                <foreach collection="operators" item="operator">
                    <if test="operator.associatedOrderNumber != null and operator.associatedOrderNumber != ''">
                        when sub_id = #{operator.subId} then #{operator.associatedOrderNumber}
                    </if>
                </foreach>
            </trim>
        </set>
        WHERE sub_id in
        <foreach collection="operators" separator="," open="(" close=")" item="operator">
            #{operator.subId}
        </foreach>
    </update>
    <update id="updateBasketInuserBySubId">
        UPDATE dbo.basket
        SET seller = #{ch999Name}
        WHERE sub_id in
        <foreach collection="ids" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </update>
    <update id="updateSalesOrderDealResult">
        UPDATE dbo.baozun_tenant_sales_order
        SET subinfo_deal_result = isnull(subinfo_deal_result,0)-(isnull(subinfo_deal_result,0) / #{location} % (#{location} * 10) * #{location}) + #{result} * #{location}
        WHERE transaction_number in
        <foreach collection="ids" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </update>
    <update id="emptyBaozunTenantSalesOrderSubId">
        UPDATE dbo.baozun_tenant_sales_order
        SET sub_id = null
        WHERE transaction_number in
        <foreach collection="transactionNumbers" separator="," open="(" close=")" item="transactionNumber">
            #{transactionNumber}
        </foreach>
    </update>
    <select id="listOrderLabel" resultType="cn.hutool.core.lang.Dict">
        SELECT distinct btsd.id,btsd.order_label orderLabel,btsd.fk_transaction_number transactionNumber,btso.sub_id subId,
                        btsds.sn,btsds.name,btsds.email,btsds.mobile, btsd.basket_id basketId
        from baozun_tenant_sales_detail btsd with(nolock)
          inner join baozun_tenant_sales_order btso with(nolock) on btso.transaction_number = btsd.fk_transaction_number
          left  join baozun_tenant_sales_detail_sninfo btsds with(nolock) on btsds.fk_transaction_number = btsd.fk_transaction_number and btsds.upc = btsd.upc
        <!--个位为orderLabel的处理状态 0 未处理 1 处理成功 2处理失败 3 为处理过了可以继续处理-->
        where isnull(btsd.subinfo_deal_result,0) % 10 in (0,3)
          and btsd.transaction_type = 'S'
          and order_label is not null and btso.sub_id is not null
            <choose>
                <when test="transactionNumbers != null and !transactionNumbers.isEmpty()">
                    and btso.transaction_number in
                    <foreach collection="transactionNumbers" separator="," open="(" close=")" item="transactionNumber">
                        #{transactionNumber}
                    </foreach>
                </when>
                <otherwise>
                    and btsd.update_time>=DATEADD(day,-#{days},getdate())
                </otherwise>
            </choose>
        order by subId desc,btsd.id desc
        offset #{pageStart} row fetch next #{pageSize} row only
    </select>
    <select id="listOrderOperator" resultType="com.jiuji.oa.oacore.thirdplatform.baozun.bo.OrderOperatorBo">
        SELECT distinct btso.sub_id subId,btso.operator,c9u.ch999_id ch999Id,s.Inuser inUser,c9u.ch999_name ch999Name,btso.transaction_number transactionNumber
        ,ROW_NUMBER() over(partition by c9u.mobile order by iszaizhi desc,c9u.ch999_id desc) rank,btso.name,btso.email,btso.telephone,btso.mobile
        ,btso.associated_order_number associatedOrderNumber
        from  dbo.baozun_tenant_sales_order btso with(nolock)
        inner join dbo.ch999_user c9u with(nolock) on c9u.mobile = btso.operator
        inner join dbo.sub s with(nolock) on s.sub_id = btso.sub_id
        <!--个位为operator的处理状态 0 未处理 1 处理成功 2处理失败 3 为处理过了可以继续处理-->
        where isnull(btso.subinfo_deal_result,0) % 10 in (0,3)
        and btso.operator is not null and btso.operator != '' and btso.sub_id is not null
        and exists(select 1 from baozun_tenant_sales_detail btsd with(nolock) where btsd.fk_transaction_number = btso.transaction_number and btsd.transaction_type = 'S')
        and isnull(s.Inuser,'') &lt;&gt; isnull(c9u.ch999_name,'')
        <choose>
            <when test="transactionNumbers != null and !transactionNumbers.isEmpty()">
                and btso.transaction_number in
                <foreach collection="transactionNumbers" separator="," open="(" close=")" item="transactionNumber">
                    #{transactionNumber}
                </foreach>
            </when>
            <otherwise>
                and btso.update_time>=DATEADD(day,-#{days},getdate())
            </otherwise>
        </choose>

        order by subId desc
        offset #{pageStart} row fetch next #{pageSize} row only
    </select>
</mapper>
