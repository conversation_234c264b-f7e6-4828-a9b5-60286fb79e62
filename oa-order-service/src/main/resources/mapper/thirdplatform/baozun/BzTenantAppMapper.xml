<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.baozun.dao.BzTenantAppMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantApp">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="fk_tenant_id" jdbcType="INTEGER" property="fkTenantId" />
    <result column="child_merchant_name" jdbcType="VARCHAR" property="childMerchantName" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="platform_kemu" jdbcType="VARCHAR" property="platformKemu" />
    <result column="customer_kemu" jdbcType="VARCHAR" property="customerKemu" />
    <result column="goods_arrival_kemu" jdbcType="VARCHAR" property="goodsArrivalKemu" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="baozun_tenant_app_rv" jdbcType="TIMESTAMP" property="baozunTenantAppRv" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fk_tenant_id, child_merchant_name, user_id, platform_kemu, customer_kemu, goods_arrival_kemu, 
    create_time, create_user, update_time, is_del, baozun_tenant_app_rv
  </sql>

  <insert id="saveBatchByTenantApp">
    INSERT INTO baozun_tenant_app(fk_tenant_id, child_merchant_name, user_id, platform_kemu, customer_kemu, goods_arrival_kemu,
    create_time, create_user, update_time, is_del) VALUES
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.fkTenantId},#{item.childMerchantName},#{item.userId},#{item.platformKemu},
      #{item.customerKemu},#{item.goodsArrivalKemu},#{item.createTime},#{item.createUser},#{item.updateTime},#{item.isDel})
    </foreach>
  </insert>
</mapper>