<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.baozun.dao.BzTenantVariantsMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantVariants">
    <id column="ext_code" jdbcType="VARCHAR" property="extCode" />
    <result column="fk_product_id" jdbcType="VARCHAR" property="fkProductId" />
    <result column="ppriceid" jdbcType="INTEGER" property="ppriceid" />
    <result column="variant_code" jdbcType="VARCHAR" property="variantCode" />
    <result column="schema_code" jdbcType="VARCHAR" property="schemaCode" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="platform_code" jdbcType="VARCHAR" property="platformCode" />
    <result column="brand_sku_code" jdbcType="VARCHAR" property="brandSkuCode" />
    <result column="custom_size" jdbcType="VARCHAR" property="customSize" />
    <result column="custom_color" jdbcType="VARCHAR" property="customColor" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="baozun_tenant_variants_rv" jdbcType="TIMESTAMP" property="baozunTenantVariantsRv" />
  </resultMap>
  <sql id="Base_Column_List">
    ext_code, fk_product_id, ppriceid, variant_code, schema_code, bar_code, platform_code,
    brand_sku_code, custom_size,custom_color, create_time, create_user, update_time, is_del, baozun_tenant_variants_rv
  </sql>

  <select id="getQueryList" resultType="com.jiuji.oa.oacore.thirdplatform.baozun.vo.res.BzTenantVariantsRes">
    SELECT  s.id,p.product_code AS productCode,s.ext_code, s.product_code,
    s.ppriceid, s.variant_code, s.schema_code, s.bar_code, s.platform_code,
    s.brand_sku_code, s.custom_size ,s.custom_color ,p.product_name AS productName,
    s.is_enabled, s.platform_code AS platformCode,s.npp_price,s.ecpp_price, s.market_classification as marketClassification
    FROM baozun_tenant_variants s WITH(NOLOCK)
    LEFT JOIN baozun_tenant_product p WITH(NOLOCK) ON s.fk_product_id = p.id
    LEFT JOIN baozun_tenant t WITH(NOLOCK) ON p.fk_tenant_id = t.id
    WHERE isnull(s.is_del,0) = 0 and isnull(p.is_del,0) = 0 and isnull(t.is_del,0) = 0
    <if test="req.tenantId != null">
      AND t.id = #{req.tenantId}
    </if>
    <if test="req.searchValue != null and req.searchValue != ''">
      <choose>
        <when test="req.searchOptions == 1">
          AND s.variant_code = #{req.searchValue}
        </when>
        <when test="req.searchOptions == 2">
          AND s.ppriceid = #{req.searchValue}
        </when>
        <when test="req.searchOptions == 3">
          AND p.product_name LIKE concat('%',#{req.searchValue},'%')
        </when>
        <when test="req.searchOptions == 4">
          AND p.product_code = #{req.searchValue}
        </when>
        <when test="req.searchOptions == 5">
          AND s.platform_code LIKE concat(#{req.searchValue},'%')
        </when>
      </choose>
    </if>
  </select>
  <select id="listMarketClassification" resultType="java.lang.String">
    SELECT DISTINCT market_classification FROM baozun_tenant_variants with(nolock) where market_classification is not null
  </select>
</mapper>
