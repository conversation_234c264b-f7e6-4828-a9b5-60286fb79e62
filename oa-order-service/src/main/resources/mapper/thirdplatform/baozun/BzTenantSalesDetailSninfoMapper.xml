<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.baozun.dao.BzTenantSalesDetailSninfoMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantSalesDetailSninfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="fk_transaction_number" jdbcType="VARCHAR" property="fkTransactionNumber" />
    <result column="slip_code" jdbcType="VARCHAR" property="slipCode" />
    <result column="line_number" jdbcType="VARCHAR" property="lineNumber" />
    <result column="upc" jdbcType="VARCHAR" property="upc" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="baozun_tenant_sales_detail_sninfo_rv" jdbcType="TIMESTAMP" property="baozunTenantSalesDetailSninfoRv" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fk_transaction_number, slip_code, line_number, upc, sn, create_time, create_user,
    update_time, is_del, baozun_tenant_sales_detail_sninfo_rv
  </sql>


</mapper>