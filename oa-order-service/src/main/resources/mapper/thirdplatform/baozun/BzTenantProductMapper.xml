<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.baozun.dao.BzTenantProductMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantProduct">

    <id column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="schema_code" jdbcType="VARCHAR" property="schemaCode" />
    <result column="product_no" jdbcType="VARCHAR" property="productNo" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="pos_cate_name" jdbcType="VARCHAR" property="posCateName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="baozun_tenant_product_rv" jdbcType="TIMESTAMP" property="baozunTenantProductRv" />
  </resultMap>
  <sql id="Base_Column_List">
    product_code, schema_code, product_no, product_name, brand, pos_cate_name, create_time,
    create_user, update_time, is_del, baozun_tenant_product_rv
  </sql>


</mapper>