<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.baozun.dao.BzTenantMapper">

  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenant">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
    <result column="tenant_name" jdbcType="VARCHAR" property="tenantName" />
    <result column="app_key" jdbcType="VARCHAR" property="appKey" />
    <result column="app_secret" jdbcType="VARCHAR" property="appSecret" />
    <result column="is_enable" jdbcType="BIT" property="isEnable" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="baozun_tenant_rv" jdbcType="TIMESTAMP" property="baozunTenantRv" />
  </resultMap>

  <resultMap id="tenantAppMap" type="com.jiuji.oa.oacore.thirdplatform.baozun.vo.res.BzTenantRes">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
    <result column="tenant_name" jdbcType="VARCHAR" property="tenantName" />
    <result column="app_key" jdbcType="VARCHAR" property="appKey" />
    <result column="app_secret" jdbcType="VARCHAR" property="appSecret" />
    <result column="npp_channel" jdbcType="VARCHAR" property="nppChannel" />
    <result column="hq_id" jdbcType="VARCHAR" property="hqId" />
    <collection property="tenantAppList" ofType="com.jiuji.oa.oacore.thirdplatform.baozun.vo.res.BzTenantRes$TenantAppReq" column="id" select="Bzlist" >
      <id column="a_id" property="id"/>
      <id column="child_merchant_name" property="childMerchantName" />
      <id column="user_id"  property="userId" />
      <id column="platform_kemu"  property="platformKemu" />
      <id column="customer_kemu"  property="customerKemu" />
      <id column="goods_arrival_kemu"  property="goodsArrivalKemu" />
    </collection>
  </resultMap>

  <sql id="Base_Column_List">
    id, tenant_code, tenant_name, app_key, app_secret, is_enable, create_time, create_user, 
    update_time, is_del, baozun_tenant_rv
  </sql>

  <select id="listByPage" resultMap="tenantAppMap" >
    SELECT  t.id, t.tenant_code, t.tenant_name,t. app_key, t.app_secret, t.npp_channel ,t.hq_id  FROM dbo.baozun_tenant t with(nolock)
  </select>

  <select id="Bzlist" resultType="com.jiuji.oa.oacore.thirdplatform.baozun.vo.res.BzTenantRes$TenantAppReq" >
    SELECT
           a.id as id,a.child_merchant_name,a.user_id,a.platform_kemu,a.customer_kemu,a.goods_arrival_kemu,a.refund_type
    FROM dbo.baozun_tenant_app a WITH (NOLOCK) where a.fk_tenant_id = #{id} and isnull(a.is_del,0) = 0
  </select>

</mapper>