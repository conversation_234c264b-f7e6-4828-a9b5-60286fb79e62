<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.baozun.dao.BzTenantAreaMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantArea">

    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="fk_tenant_id" jdbcType="INTEGER" property="fkTenantId" />
    <result column="area_id" jdbcType="INTEGER" property="areaId" />
    <result column="tenant_store_code" jdbcType="VARCHAR" property="tenantStoreCode" />
    <result column="is_enable" jdbcType="BIT" property="isEnable" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="baozun_tenant_area_rv" jdbcType="TIMESTAMP" property="baozunTenantAreaRv" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fk_tenant_id, area_id, tenant_store_code, is_enable, create_time, create_user, 
    update_time, is_del, baozun_tenant_area_rv
  </sql>

  <select id="getQueryList" resultType="com.jiuji.oa.oacore.thirdplatform.baozun.vo.res.BzTenantAreaRes">
    SELECT s.id, t.tenant_code,t.tenant_name, s.area_id, s.tenant_store_code, s.is_enable,a.area AS areaCode,a.area_name AS areaName
    FROM baozun_tenant_area s WITH(NOLOCK)
    LEFT JOIN areainfo a WITH(NOLOCK) ON s.area_id = a.id
    LEFT JOIN baozun_tenant t WITH(NOLOCK) ON s.fk_tenant_id = t.id
    WHERE isnull(s.is_del,0) = 0
    <if test="req.tenantId != null and req.tenantId != ''">
      AND t.id= #{req.tenantId}
    </if>
    <if test="req.searchValue != null and req.searchValue != ''">
      <choose>
        <when test="req.searchOptions == 1">
          AND s.tenant_store_code = #{req.searchValue}
        </when>
        <when test="req.searchOptions == 2">
          AND a.area = #{req.searchValue}
        </when>
        <when test="req.searchOptions == 3">
          AND a.area_name LIKE concat('%',#{req.searchValue},'%')
        </when>
      </choose>
    </if>

    </select>

  <insert id="saveBatch">
    INSERT INTO baozun_tenant_area(fk_tenant_id, area_id, tenant_store_code, is_enable, create_time, create_user,
    update_time, is_del) VALUES
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.fkTenantId},#{item.areaId},#{item.tenantStoreCode},#{item.isEnable},
      #{item.createTime},#{item.createUser},#{item.updateTime},#{item.isDel})
    </foreach>
  </insert>
</mapper>