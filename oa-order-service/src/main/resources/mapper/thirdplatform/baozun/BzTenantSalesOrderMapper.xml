<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.baozun.dao.BzTenantSalesOrderMapper">
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantSalesOrder">
        <!--@mbg.generated-->
        <!--@Table baozun_tenant_sales_order-->
        <id column="transaction_number" jdbcType="VARCHAR" property="transactionNumber"/>
        <result column="sub_id" jdbcType="INTEGER" property="subId"/>
        <result column="fk_tenant_id" jdbcType="INTEGER" property="fkTenantId"/>
        <result column="transaction_date" jdbcType="VARCHAR" property="transactionDate"/>
        <result column="Transaction_time" jdbcType="VARCHAR" property="transactionTime"/>
        <result column="store_code" jdbcType="VARCHAR" property="storeCode"/>
        <result column="slip_code" jdbcType="VARCHAR" property="slipCode"/>
        <result column="platform_source" jdbcType="VARCHAR" property="platformSource"/>
        <result column="customer_id" jdbcType="VARCHAR" property="customerId"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="telephone" jdbcType="VARCHAR" property="telephone"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="transfer_fee" jdbcType="DECIMAL" property="transferFee"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="message" jdbcType="VARCHAR" property="message"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_del" jdbcType="BIT" property="isDel"/>
        <result column="baozun_tenant_sales_order_rv" jdbcType="TIMESTAMP" property="baozunTenantSalesOrderRv"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        transaction_number, sub_id, fk_tenant_id, transaction_date, Transaction_time, store_code,
        slip_code, platform_source, transaction_type, customer_id, email, name, telephone,
        mobile, transfer_fee, remark, message, create_time, create_user, update_time, is_del,
        baozun_tenant_sales_order_rv
    </sql>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into baozun_tenant_sales_order
        (transaction_number, sub_id, fk_tenant_id, transaction_date, Transaction_time, store_code,
        slip_code, platform_source, customer_id, email, name, telephone,
        mobile, transfer_fee, remark, message, create_time, create_user, update_time, is_del,
        baozun_tenant_sales_order_rv)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.transactionNumber,jdbcType=VARCHAR}, #{item.subId,jdbcType=INTEGER},
            #{item.fkTenantId,jdbcType=INTEGER},
            #{item.transactionDate,jdbcType=VARCHAR}, #{item.transactionTime,jdbcType=VARCHAR},
            #{item.storeCode,jdbcType=VARCHAR}, #{item.slipCode,jdbcType=VARCHAR},
            #{item.platformSource,jdbcType=VARCHAR},
            #{item.customerId,jdbcType=VARCHAR},
            #{item.email,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.telephone,jdbcType=VARCHAR},
            #{item.mobile,jdbcType=VARCHAR}, #{item.transferFee,jdbcType=DECIMAL}, #{item.remark,jdbcType=VARCHAR},
            #{item.message,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createUser,jdbcType=VARCHAR},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDel,jdbcType=BIT},
            #{item.baozunTenantSalesOrderRv,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <select id="listByPage"
            resultType="com.jiuji.oa.oacore.thirdplatform.baozun.vo.res.BzTenantSalesOrderRes">
        SELECT
        <!--    平台下单时间-->
        btso.create_time,
        <!--       宝尊订单号 -->
        btso.transaction_number,
        <!--    商户号-->
        t.tenant_code,
        <!--    平台订单号-->
        btso.slip_code,
        <!--    平台门店-->
        bta.tenant_store_code,
        <!--    本地门店-->
        a.area AS areaCode,
        <!--    本地门店名称-->
        a.area_name AS areaName,
        <!--   子商户-->
        btso.platform_source,
        <!--    数量-->
        btsd.quantity,
        <!--    实际付款-->
        btsd.actual_price,
        <!--    折扣-->
        btsd.discount,
        <!--    单价 -->
        btsd.unit_price,
        <!--      商品名称-->
        btp.product_name,
        <!--    本地订单号-->
        btso.sub_id,
        <!--    本地订单状态-->
        s.sub_check ,
        <!--    订单备注-->
        btso.remark,
        <!--    返回信息-->
        btso.message
        FROM
        baozun_tenant_sales_order btso WITH (NOLOCK)
        LEFT JOIN baozun_tenant_area bta WITH (NOLOCK) ON btso.store_code = bta.tenant_store_code
        LEFT JOIN baozun_tenant_sales_detail btsd WITH ( NOLOCK ) ON btsd.fk_transaction_number =
        btso.transaction_number
        LEFT JOIN areainfo a WITH (NOLOCK) ON bta.area_id = a.id
        LEFT JOIN sub s WITH (NOLOCK) ON btso.sub_id = s.sub_id
        LEFT JOIN baozun_tenant t WITH (NOLOCK) ON btso.fk_tenant_id = t.id
        LEFT JOIN baozun_tenant_variants btv WITH ( NOLOCK ) ON btv.brand_sku_code = btsd.upc
        LEFT JOIN baozun_tenant_product btp WITH ( NOLOCK ) ON btp.product_code = btv.product_code
        WHERE isnull(btso.is_del,0) = 0 and isnull(bta.is_del,0) = 0 and isnull(t.is_del,0) = 0 and isnull(btv.is_del,0)
        = 0 and isnull(btp.is_del,0) = 0
        <if test="req.tenantId != null and req.tenantId != ''">
            AND btso.fk_tenant_id = #{req.tenantId}
        </if>
        <if test="req.subCheck != null and req.subCheck != ''">
            AND s.sub_check = #{req.subCheck}
        </if>
        <if test="req.startTime != null and req.startTime != '' and req.endTime != null and req.endTime != ''">
            AND btso.create_time BETWEEN #{req.startTime} AND #{req.endTime}
        </if>
        <!-- 1-商户号 2-平台订单号 3-平台门店 4-本地订单号 -->
        <if test="req.searchValue != null and req.searchValue != ''">
            <choose>
                <when test="req.searchOptions == 1">
                    AND t.tenant_code = #{req.searchValue}
                </when>
                <when test="req.searchOptions == 2">
                    AND btso.transaction_number = #{req.searchValue}
                </when>
                <when test="req.searchOptions == 3">
                    AND bta.tenant_store_code = #{req.searchValue}
                </when>
                <when test="req.searchOptions == 4">
                    AND btso.sub_id = #{req.searchValue}
                </when>
            </choose>
        </if>
    </select>

    <select id="queryList" resultType="com.jiuji.oa.oacore.thirdplatform.baozun.vo.res.BzTenantSalesOrderRes">
        SELECT
        <!--    平台下单时间-->
        btso.create_time,
        <!--       宝尊订单号 -->
        btso.transaction_number,
        <!--    商户号-->
        t.tenant_code,
        <!--    平台订单号-->
        btso.slip_code,
        <!--    平台门店-->
        bta.tenant_store_code,
        <!--    本地门店-->
        a.area AS areaCode,
        <!--    本地门店名称-->
        a.area_name AS areaName,
        <!--   子商户-->
        btso.platform_source,
        <!--    数量-->
        btsd.quantity,
        <!--    实际付款-->
        btsd.actual_price,
        <!--    折扣-->
        btsd.discount,
        <!--    单价 -->
        btsd.unit_price,
        <!--    商品名称-->
        btp.product_name,
        <!--    本地订单号-->
        btso.sub_id,
        <!--    本地订单状态-->
        s.sub_check ,
        <!--    订单备注-->
        btso.remark,
        <!--    返回信息-->
        btso.message,
        <!-- 平台关联单号-->
        btso.associated_order_number
        FROM
        baozun_tenant_sales_order btso WITH (NOLOCK)
        LEFT JOIN baozun_tenant_area bta WITH (NOLOCK) ON btso.store_code = bta.tenant_store_code
        LEFT JOIN baozun_tenant_sales_detail btsd WITH ( NOLOCK ) ON btsd.fk_transaction_number =
        btso.transaction_number
        LEFT JOIN areainfo a WITH (NOLOCK) ON bta.area_id = a.id
        LEFT JOIN sub s WITH (NOLOCK) ON btso.sub_id = s.sub_id
        LEFT JOIN baozun_tenant t WITH (NOLOCK) ON btso.fk_tenant_id = t.id
        LEFT JOIN baozun_tenant_variants btv WITH ( NOLOCK ) ON btv.brand_sku_code = btsd.upc
        LEFT JOIN baozun_tenant_product btp WITH ( NOLOCK ) ON btp.product_code = btv.product_code
        WHERE isnull(btso.is_del,0) = 0 and isnull(bta.is_del,0) = 0 and isnull(t.is_del,0) = 0 and isnull(btv.is_del,0)
        = 0 and isnull(btp.is_del,0) = 0
        <if test="req.tenantId != null and req.tenantId != ''">
            AND btso.fk_tenant_id = #{req.tenantId}
        </if>
        <if test="req.subCheck != null and req.subCheck != ''">
            AND s.sub_check = #{req.subCheck}
        </if>
        <if test="req.startTime != null and req.startTime != '' and req.endTime != null and req.endTime != ''">
            AND btso.create_time BETWEEN #{req.startTime} AND #{req.endTime}
        </if>
        <!-- 1-商户号 2-平台订单号 3-平台门店 4-本地订单号 -->
        <if test="req.searchValue != null and req.searchValue != ''">
            <choose>
                <when test="req.searchOptions == 1">
                    AND t.tenant_code = #{req.searchValue}
                </when>
                <when test="req.searchOptions == 2">
                    AND btso.transaction_number like CONCAT(#{req.searchValue}, '%')
                </when>
                <when test="req.searchOptions == 3">
                    AND bta.tenant_store_code = #{req.searchValue}
                </when>
                <when test="req.searchOptions == 4">
                    AND btso.sub_id = #{req.searchValue}
                </when>
                <when test="req.searchOptions == 5">
                    AND btso.platform_source = #{req.searchValue}
                </when>
                <when test="req.searchOptions == 6">
                    AND btso.slip_code like CONCAT(#{req.searchValue}, '%')
                </when>
                <when test="req.searchOptions == 7">
                    AND btso.associated_order_number like CONCAT(#{req.searchValue}, '%')
                </when>
            </choose>
        </if>
    </select>
</mapper>
