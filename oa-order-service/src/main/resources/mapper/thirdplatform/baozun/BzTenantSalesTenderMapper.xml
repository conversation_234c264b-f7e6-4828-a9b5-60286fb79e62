<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.baozun.dao.BzTenantSalesTenderMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantSalesTender">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="fk_transaction_number" jdbcType="VARCHAR" property="fkTransactionNumber" />
    <result column="payment_type" jdbcType="VARCHAR" property="paymentType" />
    <result column="transaction_amount" jdbcType="DECIMAL" property="transactionAmount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="baozun_tenant_sales_tender_rv" jdbcType="TIMESTAMP" property="baozunTenantSalesTenderRv" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fk_transaction_number, payment_type, transaction_amount, create_time, create_user,
    update_time, is_del, baozun_tenant_sales_tender_rv
  </sql>
</mapper>