<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.baozun.dao.BzTenantCloudStoreMapper">
    <select id="listCloudStoreUpc" parameterType="java.lang.String" resultType="com.jiuji.oa.oacore.thirdplatform.baozun.bo.BzTenantCloudStoreBo">
        SELECT btso.transaction_number transactionNumber ,bta.fk_tenant_id tenantId,btsd.id detailId, btsd.upc, btsd.quantity qty,
            bta.area_id areaId, isnull(btsd.subinfo_deal_result,0)/10 % 10 subinfoDealResult,btso.store_code storeCode
        from baozun_tenant_sales_order btso with(nolock)
        inner join baozun_tenant_sales_detail btsd with(nolock) on btsd.fk_transaction_number = btso.transaction_number and btsd.is_del = 0
            and btsd.transaction_type = 'S' and btsd.quantity &gt; 0
            and exists(select 1 from dbo.F_SPLIT(isnull(btsd.order_label,''),';') fs where fs.split_value = '9002')
        left join baozun_tenant_area bta with(nolock) on bta.tenant_store_code = btso.store_code and bta.is_del = 0
        where btso.is_del = 0
          and btso.create_time >= DATEADD(day, -15, GETDATE())
          and btso.transaction_number in
        <foreach collection="outboundNos" item="outboundNo" open="(" close=")" separator=",">
            #{outboundNo}
        </foreach>
    </select>
    <select id="listS47CloudStoreUpc" parameterType="java.lang.String" resultType="com.jiuji.oa.oacore.thirdplatform.baozun.bo.S47RequestBodyBO$InventoryFrezzingAndReleaseBO">
        SELECT btso.transaction_number transactionNumber ,btso.fk_tenant_id tenantId,
        btsd.id detailId, btsd.upc, btsd.quantity qty,btso.area_id areaId
        from baozun_tenant_sales_order btso with(nolock)
        inner join baozun_tenant_sales_detail btsd with(nolock) on btsd.fk_transaction_number = btso.transaction_number and btsd.is_del = 0
        and btsd.transaction_type = 'S' and btsd.quantity &gt; 0
        <!--十位: 为云仓单据生成采购单的状态 0 未处理 1 处理成功 2 处理失败 3 为处理过了可以重新开始处理 4 待生成采购单 5 采购单已生成-->
        and isnull(btsd.subinfo_deal_result,0)/10 % 10 = 1
        <!--百位: 为云仓单据预占的状态 0 未处理 1 处理成功 2 处理失败 3 为处理过了可以重新开始处理-->
        and isnull(btsd.subinfo_deal_result,0)/100 % 100 in(0,3)
        and exists(select 1 from dbo.F_SPLIT(isnull(btsd.order_label,''),';') fs where fs.split_value = '9002')
        where btso.is_del = 0
        and btso.create_time >= DATEADD(day, -15, GETDATE())
        and btso.transaction_number in
        <foreach collection="outboundNos" item="outboundNo" open="(" close=")" separator=",">
            #{outboundNo}
        </foreach>
    </select>
</mapper>
