<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.baozun.dao.BzWorkLogMapper">
  <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.baozun.po.BzWorkLog">
    <!--@mbg.generated-->
    <!--@Table baozun_work_log-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="work_name" jdbcType="VARCHAR" property="workName" />
    <result column="core_business_id" jdbcType="INTEGER" property="coreBusinessId" />
    <result column="work_content" jdbcType="VARCHAR" property="workContent" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="log_type" jdbcType="INTEGER" property="logType" />
    <result column="baozun_work_log_rv" jdbcType="TIMESTAMP" property="baozunWorkLogRv" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, work_name, core_business_id, work_content, create_time, create_user, update_time, 
    is_del, log_type, baozun_work_log_rv
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into baozun_work_log
    (work_name, core_business_id, work_content, create_time, create_user, update_time, 
      is_del, log_type)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.workName,jdbcType=VARCHAR}, #{item.coreBusinessId,jdbcType=INTEGER}, #{item.workContent,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.createUser,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.isDel,jdbcType=BIT}, #{item.logType,jdbcType=INTEGER}
        )
    </foreach>
  </insert>
    <select id="getListByPage" resultType="com.jiuji.oa.oacore.thirdplatform.baozun.vo.res.BzWorkLogRes">
        select bl.id, bl.work_name, bl.core_business_id, bl.work_content, bl.create_time, bl.create_user, bl.update_time,
            bl.log_type from baozun_work_log bl with(nolock)
        <where>
          and isnull(bl.is_del,0) = 0
          <if test="req.coreBusinesId != null">
            and bl.core_business_id = #{req.coreBusinesId}
          </if>
          <if test="req.logType != null">
            and bl.log_type = #{req.logType}
          </if>
        </where>
    </select>
</mapper>