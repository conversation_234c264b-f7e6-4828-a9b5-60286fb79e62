<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.stock.mapper.StockSyncMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.stock.entity.StockSync">
        <id column="id" property="id"/>
        <result column="sync_number" property="syncNumber"/>
        <result column="tenant_code" property="tenantCode"/>
        <result column="plat_code" property="platCode"/>
        <result column="store_code" property="storeCode"/>
        <result column="product_code" property="productCode"/>
        <result column="sku_id" property="skuId"/>
        <result column="sync_count" property="syncCount"/>
        <result column="sync_time" property="syncTime"/>
        <result column="is_sync" property="isSync"/>
    </resultMap>

    <sql id="columns">
        s.id,s.sync_number,s.tenant_code,s.store_code,s.plat_code,s.product_code,s.sku_id,s.sync_count,s.sync_time,s.is_sync
    </sql>

    <insert id="saveBatch">
        insert into third_platform_stock_sync (sync_number,tenant_code,store_code,plat_code,product_code,
        sku_id,sync_count,sync_time,is_sync) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.syncNumber}, #{item.tenantCode}, #{item.storeCode}, #{item.platCode},#{item.productCode},
            #{item.skuId}, #{item.syncCount},#{item.syncTime}, #{item.isSync}
            )
        </foreach>
    </insert>

    <update id="updateSyncSuccess">
        UPDATE third_platform_stock_sync SET is_sync=1,sync_time=GETDATE() WHERE id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateSyncFail">
        UPDATE third_platform_stock_sync SET is_sync=0,sync_time=GETDATE() WHERE id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <delete id="delSyncByFifteenDay">
        delete from third_platform_stock_sync where sync_time &lt; #{failIds}
    </delete>
    <select id="selectChangeStock" resultType="com.jiuji.oa.oacore.thirdplatform.stock.bo.ChangeStock">
        <trim prefixOverrides="union all">
            <if test="syncType == 'all' or syncType == 'big'">
                select c.ppriceid,mkc.areaId,a.area,mkc.update_time updateTime,1 changeType
                from dbo.third_platform_product_config c with (nolock)
                right join dbo.product_mkcjh mkc with(nolock ) on c.ppriceid = mkc.ppriceid and mkc.update_time is not
                null and mkc.update_time >= #{syncTime}
                left join dbo.areainfo a with(nolock) on a.id=mkc.areaid
                where c.sync_off = 1 and c.plat_code=#{platCode} and isnull(c.is_del,0) = 0
            </if>
            <if test="syncType == 'all' or syncType == 'small'">
                union all
                select c.ppriceid,kc.areaId,a.area,kc.update_time updateTime,2 changeType
                from dbo.third_platform_product_config c with (nolock)
                right join dbo.product_kcjh kc with(nolock) on c.ppriceid = kc.ppriceid and kc.update_time is not null
                and kc.update_time >= #{syncTime}
                left join dbo.areainfo a with(nolock) on a.id=kc.areaid
                where c.sync_off = 1 and c.plat_code=#{platCode} and isnull(c.is_del,0) = 0
            </if>
        </trim>
        union all
        select c.ppriceid,-1 as areaId,'' as area,c.create_time updateTime,3 changeType
        from dbo.third_platform_product_config c with (nolock)
        where c.sync_off = 1 and isnull(c.is_del,0) = 0
          and create_time >= #{syncTime}
          and c.plat_code=#{platCode}
    </select>
    <select id="getRarePpriceIdList" resultType="java.lang.Integer">
        select
            r.productid as ppriceid
        from dbo.rareProductConfig r with(nolock)
        where r.productid in
        <!--循环 allPpids-->
        <foreach collection="allPpids" item="ppid" index="index" open="(" separator="," close=")">
            #{ppid}
        </foreach>
    </select>
</mapper>
