<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.yading.mapper.YadingServiceMappingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.yading.entity.YadingServiceMapping">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="sku_id" property="skuId" />
        <result column="jiuxun_service_name" property="jiuxunServiceName" />
        <result column="cellphone_type" property="cellphoneType" />
        <result column="pro_type" property="proType" />
        <result column="insure_year" property="insureYear" />
        <result column="insure_price" property="insurePrice" />
        <result column="insure_category" property="insureCategory" />
        <result column="yading_code" property="yadingCode" />
        <result column="yading_code_son" property="yadingCodeSon" />
        <result column="descriptor" property="descriptor" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time,
        update_time,
        id, sku_id, jiuxun_service_name, cellphone_type, pro_type, insure_year, insure_price, insure_category, yading_code, yading_code_son, descriptor, is_delete
    </sql>
    <select id="getYadingServiceConfig" resultType="com.jiuji.oa.oacore.thirdplatform.yading.entity.YadingServiceMapping">
        select
               *
        from yading_service_mapping WITH(nolock)
        where is_delete=0
          and cellphone_type = #{phoneType}
          and sku_id = #{skuId}
            <!-- pro_type值为-1则不区分pro和非pro -->
          and (pro_type = #{proType} or pro_type = -1)
        <if test="androidPrice != null">
            and android_price_min &lt;= #{androidPrice}
            and android_price_max &gt;= #{androidPrice}
        </if>

    </select>


    <select id="getYadingServiceConfigOne" resultType="com.jiuji.oa.oacore.thirdplatform.yading.entity.YadingServiceMapping">
        select
          top 1  *
        from yading_service_mapping WITH(nolock)
        where is_delete=0
          and sku_id = #{skuId}

    </select>

</mapper>
