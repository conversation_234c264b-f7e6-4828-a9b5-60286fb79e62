<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.store.mapper.JdStoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.store.vo.StoreVO">
        <id column="id" property="id"/>
        <result column="tenant_code" property="tenantCode"/>
        <result column="area_id" property="areaId"/>
        <result column="store_code" property="storeCode"/>
        <result column="store_name" property="storeName"/>
        <result column="is_enable" property="isEnable"/>
        <result column="plat_code" property="platCode"/>
        <result column="area_code" property="areaCode"/>
        <result column="area_name" property="areaName"/>
        <result column="tenant_name" property="tenantName"/>
        <result column="associated_stores" property="associatedStores"/>
        <result column="associated_stores_flag" property="associatedStoresFlag"/>
    </resultMap>
    <resultMap id="storeCodeResult" type="java.lang.String" >
        <result column="store_code" property="storeCode" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="columns">
        s.id,s.tenant_code,s.area_id,s.store_code,s.store_name,s.is_enable,s.associated_stores,s.associated_stores_flag
    </sql>

    <insert id="saveBatch">
        insert into jingdong_store (tenant_code,area_id,store_code,is_enable,associated_stores,associated_stores_flag) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.tenantCode}, #{item.areaId}, #{item.storeCode}, #{item.isEnable}, #{item.associatedStores}, #{item.associatedStoresFlag})
        </foreach>
    </insert>

    <delete id="deleteByIdAfter">
        DELETE FROM jingdong_store WHERE id= #{id} AND NOT EXISTS(SELECT 1 FROM dbo.jingdong_order o WHERE o.store_code=jingdong_store.store_code)
    </delete>

    <select id="storeList" resultMap="BaseResultMap">
        SELECT<include refid="columns"/>,a.area as area_code,a.area_name,t.tenant_name
        FROM jingdong_store s WITH(NOLOCK)
        LEFT JOIN areainfo a WITH(NOLOCK) ON a.id=s.area_id
        LEFT JOIN jingdong_tenant t WITH(NOLOCK) ON t.tenant_code=s.tenant_code
        <where>
            <if test="search.tenantid!=null">
                AND t.id = #{search.tenantid}
            </if>
            <if test="search.tenantCode != null and search.tenantCode != ''">
                AND s.tenant_code = #{search.tenantCode}
            </if>
            <if test="search.searchValue != null and search.searchValue != ''">
                <choose>
                    <when test="search.searchOptions == 1">
                        AND s.store_code LIKE concat('%',#{search.searchValue},'%')
                    </when>
                    <when test="search.searchOptions == 2">
                        AND a.area LIKE concat('%',#{search.searchValue},'%')
                    </when>
                    <when test="search.searchOptions == 3">
                        AND a.area_name LIKE concat('%',#{search.searchValue},'%')
                    </when>
                </choose>
            </if>
        </where>
    </select>

    <select id="getSyncStoreCodeList" resultMap="storeCodeResult">
        SELECT DISTINCT s.store_code FROM jingdong_store s WITH(NOLOCK)
                                            INNER JOIN dbo.product_mkc k WITH(NOLOCK) ON k.areaid=s.area_id
                                            INNER JOIN jingdong_product_config c WITH(NOLOCK) ON c.ppriceid=k.ppriceid
                        WHERE s.is_enable=1 AND k.kc_check=3 AND ISNULL(k.mouldFlag,0)=0
                        AND EXISTS(SELECT 1 FROM dbo.jingdong_tenant xt WITH(NOLOCK) WHERE xt.tenant_code=s.tenant_code AND xt.is_enable=1)
                        AND NOT EXISTS(SELECT 1 FROM dbo.xc_mkc x WITH(NOLOCK) WHERE x.mkc_id=k.id)
                        UNION ALL
                        SELECT DISTINCT s.store_code FROM jingdong_store s WITH(NOLOCK)
                                            INNER JOIN dbo.product_kc k WITH(NOLOCK) ON k.areaid=s.area_id
                                            INNER JOIN jingdong_product_config c WITH(NOLOCK) ON c.ppriceid=k.ppriceid
                        WHERE s.is_enable=1 AND EXISTS(SELECT 1 FROM dbo.jingdong_tenant xt WITH(NOLOCK) WHERE xt.tenant_code=s.tenant_code AND xt.is_enable=1 )
    </select>

</mapper>
