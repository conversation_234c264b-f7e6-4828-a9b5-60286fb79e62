<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.order.mapper.JdOrderItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.order.entity.JdOrderItem">
        <id column="id" property="id"/>
        <result column="outjdid" property="outjdid"/>
        <result column="order_id" property="orderId"/>
        <result column="product_code" property="productCode"/>
        <result column="sku_id" property="skuId"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="price" property="price"/>
        <result column="sku_count" property="skuCount"/>
        <result column="store_price" property="storePrice"/>
        <result column="cost_price" property="costPrice"/>
        <result column="trade_price" property="tradePrice"/>
        <result column="price_split" property="priceSplit"/>
        <result column="memberprice" property="memberPrice"/>
        <result column="config" property="config"/>
        <result column="ismobile1" property="mobile1"/>
        <result column="product_name" property="productName"/>
        <result column="product_color" property="productColor"/>
    </resultMap>

    <select id="listOrderItemByOutId" resultMap="BaseResultMap">
        SELECT ob.*,p.memberprice,p.config,p.ismobile1,p.product_name,p.product_color FROM dbo.jingdong_order_items ob WITH(NOLOCK)
        LEFT JOIN dbo.productinfo p WITH(NOLOCK)
        ON p.ppriceid = ob.ppriceid WHERE outjdid in
          <foreach collection="outIds" item="outId" open="(" close=")" separator=",">
              #{outId}
          </foreach>

    </select>

    <insert id="saveBatch">
        INSERT INTO jingdong_order_items(outjdid,order_id,sku_id,product_code,ppriceid,price,sku_count,
        store_price,cost_price,trade_price,price_split) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.outjdid}, #{item.orderId}, #{item.skuId}, #{item.productCode},#{item.ppriceid}, #{item.price},#{item.skuCount},
            #{item.storePrice},#{item.costPrice}, #{item.tradePrice},#{item.priceSplit})
        </foreach>
    </insert>

</mapper>
