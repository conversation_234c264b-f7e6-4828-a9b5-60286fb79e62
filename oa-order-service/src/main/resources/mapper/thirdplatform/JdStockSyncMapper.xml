<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.stock.mapper.JdStockSyncMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.stock.entity.JdStockSync">
        <id column="id" property="id"/>
        <result column="sync_number" property="syncNumber"/>
        <result column="tenant_code" property="tenantCode"/>
        <result column="store_code" property="storeCode"/>
        <result column="product_code" property="productCode"/>
        <result column="sku_id" property="skuId"/>
        <result column="sync_count" property="syncCount"/>
        <result column="sync_time" property="syncTime"/>
        <result column="is_sync" property="isSync"/>
    </resultMap>

    <sql id="columns">
        s.id,s.sync_number,s.tenant_code,s.store_code,s.product_code,s.sku_id,s.sync_count,s.sync_time,s.is_sync
    </sql>

    <insert id="saveBatch">
        insert into jingdong_sync (sync_number,tenant_code,store_code,product_code,
        sku_id,sync_count,is_sync) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.syncNumber}, #{item.tenantCode}, #{item.storeCode} ,#{item.productCode},
            #{item.skuId}, #{item.syncCount}, #{item.isSync}
            )
        </foreach>
    </insert>

    <update id="updateSyncSuccess">
        UPDATE jingdong_sync SET is_sync=1,sync_time=GETDATE() WHERE id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateSyncFail">
        UPDATE jingdong_sync SET is_sync=0,sync_time=GETDATE() WHERE id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

</mapper>
