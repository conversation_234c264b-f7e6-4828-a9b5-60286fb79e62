<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.staff.mapper.StaffMapper">

    <delete id="deleteByBusiness">
        update third_platform_staff set is_del = 1
        where plat_code = #{platCode}
        and is_del = 0
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="tenantCode != null and tenantCode !=''">
            and tenant_code = #{tenantCode}
        </if>

        <if test="storeCode != null and storeCode !=''">
            and store_code = #{storeCode}
        </if>
    </delete>

    <select id="selectByBusiness" resultType="com.jiuji.oa.oacore.thirdplatform.staff.vo.res.StaffRes">
        SELECT tps.id, tps.tenant_code, tps.store_code, tps.plat_code, tps.ch999_id, cu.ch999_name, tps.salesperson_account
        FROM dbo.third_platform_staff tps with(NOLOCK)
        LEFT JOIN dbo.ch999_user cu WITH(NOLOCK) ON tps.ch999_id = cu.ch999_id
        WHERE tps.plat_code = #{platCode}
        <if test="tenantCode != null and tenantCode !=''">
            and tps.tenant_code = #{tenantCode}
        </if>
        <if test="storeCode != null and storeCode !=''">
            and tps.store_code = #{storeCode}
        </if>
        AND tps.is_del = 0
    </select>

    <insert id="insertBatch">
        INSERT INTO third_platform_staff
            (tenant_code, store_code, plat_code, ch999_id, salesperson_account, create_user, create_time, is_del, update_time)
        VALUES
        <foreach item="staff" collection="list" separator=",">
            (#{staff.tenantCode}, #{staff.storeCode}, #{staff.platCode}, #{staff.ch999Id}, #{staff.salespersonAccount}, #{staff.createUser}, GETDATE(), #{staff.isDel}, GETDATE())
        </foreach>
    </insert>
</mapper>
