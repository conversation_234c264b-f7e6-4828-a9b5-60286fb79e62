<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.tenant.mapper.JdTenantMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.tenant.vo.TenantVO">
        <id column="id" property="id"/>
        <result column="tenant_code" property="tenantCode"/>
        <result column="tenant_name" property="tenantName"/>
        <result column="app_key" property="appKey"/>
        <result column="app_secret" property="appSecret"/>
        <result column="is_enable" property="isEnable"/>
        <result column="UserId" property="userId"/>
        <result column="plat_kemu" property="platKemu"/>
        <result column="vender_kemu" property="venderKemu"/>
        <result column="orderSwitch" property="orderSwitch"/>
        <result column="order_status" property="orderStatus"/>
        <result column="refund_kemu" property="refundKemu"/>
        <result column="basket_type_gift" property="basketTypeGift"/>
        <result column="commodity_model" property="commodityModel"/>
    </resultMap>

    <sql id="columns">
        t.id,t.tenant_code,t.tenant_name,t.app_key,t.app_secret,t.is_enable,t.UserId,t.plat_kemu,t.vender_kemu,t.orderSwitch,t.order_status,t.refund_kemu,t.basket_type_gift,t.commodity_model,t.government_subsidy_kemu as governmentSubsidyKemu
    </sql>

    <select id="jdTenantList" resultMap="BaseResultMap">
        SELECT
        <include refid="columns"/>
        FROM jingdong_tenant t WITH(NOLOCK)
        <where>
            <if test="search.tenantCode != null and search.tenantCode != ''">
                AND t.tenant_code = #{search.tenantCode}
            </if>
            <if test="search.tenantName != null and search.tenantName != ''">
                AND t.tenant_name LIKE concat('%',#{search.tenantName},'%')
            </if>
        </where>
    </select>

</mapper>
