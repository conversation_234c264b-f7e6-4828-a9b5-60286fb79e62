<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.tuangou.mapper.TuangouCouponCodeMapper">
    <update id="updateCouponStatusById">
        update douyin_coupon_log set status = #{req.status}
        where id = #{req.id}
        and status = #{req.oldStatus}
    </update>
    <select id="selectCouponCodeListBySub"
            resultType="com.jiuji.oa.oacore.thirdplatform.tuangou.vo.res.QueryCouponCodeRes">
        select * from (
            select
            dcl.id,
            dcl.origin_code couponcode,
            dcl.coupon_kinds,
            dcl.status,
            dcl.sub_id,
            dcl.sub_kinds,
            row_number() over (partition by sub_id,sub_kinds,origin_code order by id desc) rn
            from douyin_coupon_log dcl with(nolock)
            where isnull(dcl.is_del,0) = 0
            and dcl.origin_code is not null
            and dcl.sub_id = #{req.subId}
        ) t
        where t.rn = 1
        and t.status in (1,3)
    </select>
    <select id="selectCouponCodeById"
            resultType="com.jiuji.oa.oacore.thirdplatform.tuangou.dto.CouponCodeDto">
        SELECT
            dcl.id,
            dcl.origin_code originCode,
            dcl.coupon_kinds couponKinds,
            dcl.status,
            dcl.sub_id subId,
            dcl.sub_kinds subKinds
        FROM
            douyin_coupon_log dcl with(nolock)
        WHERE
            dcl.id = #{id}
    </select>
</mapper>