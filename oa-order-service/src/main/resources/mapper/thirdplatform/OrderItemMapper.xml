<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.order.mapper.OrderItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.order.entity.OrderItem">
        <id column="id" property="id"/>
        <result column="outjdid" property="outjdid"/>
        <result column="order_id" property="orderId"/>
        <result column="product_code" property="productCode"/>
        <result column="sku_id" property="skuId"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="price" property="price"/>
        <result column="sku_count" property="skuCount"/>
        <result column="store_price" property="storePrice"/>
        <result column="cost_price" property="costPrice"/>
        <result column="trade_price" property="tradePrice"/>
        <result column="price_split" property="priceSplit"/>
        <result column="memberprice" property="memberPrice"/>
        <result column="config" property="config"/>
        <result column="ismobile1" property="mobile1"/>
        <result column="product_name" property="productName"/>
        <result column="product_color" property="productColor"/>
        <result column="gift_off" property="giftOff"/>
        <result column="to_basket_id" property="toBasketId"/>
        <result column="cancel_check" property="cancelCheck"/>
    </resultMap>

    <select id="listOrderItemByOutId" resultMap="BaseResultMap">
        SELECT distinct isnull(cg.gift_off,0) as gift_off ,ob.*,p.memberprice,p.config,p.ismobile1,p.product_name,p.product_color FROM dbo.third_platform_order_items ob WITH(NOLOCK)
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = ob.ppriceid
        LEFT JOIN dbo.third_platform_product_config cg WITH(NOLOCK) ON cg.ppriceid = ob.ppriceid
        WHERE outjdid=#{outjdid} and isnull(cg.is_del,0) = 0
    </select>

    <select id="listOrderItemByOutIdV2" resultMap="BaseResultMap">
        SELECT distinct isnull(cg.gift_off,0) as gift_off ,ob.*,p.memberprice,p.config,p.ismobile1,p.product_name,p.product_color FROM dbo.third_platform_order_items ob WITH(NOLOCK)
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = ob.ppriceid
        LEFT JOIN dbo.third_platform_product_config cg WITH(NOLOCK) ON cg.ppriceid = ob.ppriceid
        WHERE outjdid=#{outjdid} and cg.plat_code =#{platCode} and isnull(cg.is_del,0) = 0
    </select>

    <insert id="saveBatch">
        INSERT INTO third_platform_order_items(outjdid,order_id,sku_id,product_code,ppriceid,price,sku_count,
        store_price,cost_price,trade_price,price_split,type,mkc_id,biz_sub_order_id,out_sub_order_id) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.outjdid}, #{item.orderId}, #{item.skuId}, #{item.productCode},#{item.ppriceid}, #{item.price},#{item.skuCount},
            #{item.storePrice},#{item.costPrice}, #{item.tradePrice},#{item.priceSplit},#{item.type},#{item.mkcId},#{item.bizSubOrderId},#{item.outSubOrderId})
        </foreach>
    </insert>

    <select id="getKcCount" resultType="com.jiuji.oa.oacore.thirdplatform.order.bo.ProductKcBO">
        select leftcount,ppriceid from product_kc with(nolock) where 1=1
        <if test="ppidList != null and ppidList.size() != 0">
            and ppriceid in
            <foreach collection="ppidList" index="index" item="ppid" separator="," open="(" close=")">
                #{ppid}
            </foreach>
        </if>
        and areaid=#{areaId}
    </select>

    <select id="listOrderItemByMkcId" resultMap="BaseResultMap">
        SELECT 0 as gift_off,ob.*,p.memberprice,p.config,p.ismobile1,p.product_name,p.product_color,k.to_basket_id
        FROM dbo.third_platform_order_items ob WITH(NOLOCK)
        LEFT JOIN recover_mkc k with(nolock) ON k.id = ob.mkc_id
        left join productinfo p with(nolock) on p.ppriceid = k.ppriceid
        <!-- 查出该订单下的所有良品再后续通过ershou库进行筛选 -->
        where issalf = 1 and outjdid=#{outjdid}
    </select>

    <select id="listOrderItemByOutIdList" resultType="com.jiuji.oa.oacore.thirdplatform.order.entity.OrderItem">
        SELECT ob.*,p.memberprice,p.config,p.ismobile1,p.product_name,p.product_color FROM dbo.third_platform_order_items ob WITH(NOLOCK)
        LEFT JOIN dbo.productinfo p WITH(NOLOCK)
        ON p.ppriceid = ob.ppriceid
        WHERE outjdid in
        <foreach collection="outIdList" index="index" item="outId" separator="," open="(" close=")">
            #{outId}
        </foreach>
    </select>

    <select id="listValidIdByMkcIdList" resultType="java.lang.Integer">
        SELECT mkc_id
        from salfGoods WITH(NOLOCK)
        WHERE mkc_id in
        <foreach collection="mkcIdList" index="index" item="mkcId" separator="," open="(" close=")">
            #{mkcId}
        </foreach>
        and gisground in(0,1,2,3)
    </select>
    <select id="listOrderItemByOutSubOrderId"
            resultType="com.jiuji.oa.oacore.thirdplatform.order.entity.OrderItem">
        SELECT distinct isnull(cg.gift_off,0) as gift_off ,ob.*,p.memberprice,p.config,p.ismobile1,p.product_name,p.product_color
        FROM dbo.third_platform_order_items ob WITH(NOLOCK)
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid = ob.ppriceid
        LEFT JOIN dbo.third_platform_product_config cg WITH(NOLOCK) ON cg.ppriceid = ob.ppriceid
        WHERE ob.out_sub_order_id=#{outSubOrderId} and isnull(cg.is_del,0) = 0
    </select>
</mapper>
