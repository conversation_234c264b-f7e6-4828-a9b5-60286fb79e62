<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.order.mapper.XiaoMiOrderMapper">

    <select id="getXiaoMiStores" resultType="com.jiuji.oa.oacore.thirdplatform.order.dto.XiaoMiStoresVO">
        SELECT s.id,
               s.tenant_code,
               s.area_id,
               s.store_code,
               s.store_name,
               s.is_enable,
               s.plat_code,
               s.associated_stores,
               s.associated_stores_flag,
               s.large_channel_id,
               s.small_channel_id,
               a.area_name AS areaName,
               a.area AS area,
               CASE
                   WHEN LEFT(cityid, 2) = '52' THEN '贵州'
                   WHEN LEFT(cityid, 2) = '53' THEN '云南'
                   ELSE a.Province_name END AS provinceName,
               t.tenant_name as tenantName
        FROM third_platform_store s WITH(NOLOCK)
                 LEFT JOIN third_platform_tenant t WITH(NOLOCK) ON t.tenant_code = s.tenant_code
                 LEFT JOIN areainfo a WITH(NOLOCK) ON s.area_id = a.id
        WHERE s.plat_code = 'MI' AND s.is_enable = 1 AND t.is_enable = 1
        <if test="tenantCode != null and tenantCode != ''">
            AND s.tenant_code = #{tenantCode}
        </if>
    </select>

    <select id="getxiaoMiOrderVOPage" resultType="com.jiuji.oa.oacore.thirdplatform.order.dto.XiaoMiOrderVO">
        SELECT a.id as areaId,a.area,a.area_name,xo.id,tps.store_code,tps.store_name,tpt.tenant_name,tpt.tenant_code,xo.od_delivery_number,
               xo.sub_id,p.product_name,p.product_color,xo.quantity,xo.actual_selling_price,xo.shipping_fee,
               xo.oa_order_type,xo.payment_time,xo.delivery_time,s.tradeDate1 AS tradeTime,b.seller AS salesperson, s.sub_check AS orderStatus
        FROM xiaomi_order xo WITH(NOLOCK)
             LEFT JOIN third_platform_tenant tpt WITH(NOLOCK) ON tpt.tenant_code = xo.partner_id AND tpt.is_enable = 1 AND tpt.plat_code = 'MI'
             LEFT JOIN third_platform_store tps WITH(NOLOCK) ON tps.tenant_code = tpt.tenant_code AND tps.store_code = xo.store_code  AND tps.plat_code = 'MI' AND tps.is_enable = 1
             LEFT JOIN areainfo a WITH(NOLOCK) ON a.id = tps.area_id
             LEFT JOIN third_platform_product_config tpbc WITH(NOLOCK) ON tpbc.sku_id = xo.product_sku AND tpbc.tenant_code = 'ALL_MI' AND tpbc.plat_code = 'MI' AND ISNULL(tpbc.is_del,0) = 0
             LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON tpbc.ppriceid = p.ppriceid
             LEFT JOIN basket b WITH(NOLOCK) ON b.sub_id = xo.sub_id AND b.ppriceid = tpbc.ppriceid AND ISNULL(b.isdel,0) = 0
             LEFT JOIN sub s WITH(NOLOCK) ON s.sub_id = xo.sub_id
        WHERE xo.is_del = 0 and ISNULL(xo.actual_selling_price, 0) > 0 and isnull(tpbc.is_del,0) = 0
        <!--商户搜索枚举-->
        <if test="req.merchantSearchType != null and req.merchantSearchType > 0 and req.merchantSearchValue != null and req.merchantSearchValue != ''">
            <choose>
                <when test="req.merchantSearchType == 1">
                    AND tpt.tenant_code = #{req.merchantSearchValue}
                </when>
                <when test="req.merchantSearchType == 2">
                    AND tpt.tenant_name LIKE CONCAT('%', #{req.merchantSearchValue}, '%')
                </when>
            </choose>
        </if>

        <!--订单搜索枚举-->
        <if test="req.orderSearchType != null and req.orderSearchType > 0 and req.orderSearchValue != null and req.orderSearchValue != ''">
            <choose>
                <when test="req.orderSearchType == 1">
                    AND xo.od_delivery_number = #{req.orderSearchValue}
                </when>
                <when test="req.orderSearchType == 2">
                    AND xo.sub_id = #{req.orderSearchValue}
                </when>
                <when test="req.orderSearchType == 3">
                    AND p.product_name LIKE CONCAT('%', #{req.orderSearchValue}, '%')
                </when>
                <when test="req.orderSearchType == 4">
                    AND p.product_id = #{req.orderSearchValue}
                </when>
                <when test="req.orderSearchType == 5">
                    AND p.ppriceid = #{req.orderSearchValue}
                </when>
            </choose>
        </if>

        <!--订单状态-->
        <if test="req.orderStatus != null and req.orderStatus != 0">
            AND s.sub_check = #{req.orderStatus}
        </if>

        <!--订单类型-->
        <if test="req.orderType != null and req.orderType != 0">
            AND xo.oa_order_type = #{req.orderType}
        </if>

        <!-- 时间查询 -->
        <if test="req.timeType != null and req.startTime != null and req.endTime != null">
            <choose>
                <when test="req.timeType == 1">
                    AND xo.delivery_time BETWEEN #{req.startTime} AND #{req.endTime}
                </when>
                <when test="req.timeType == 2">
                    AND xo.payment_time BETWEEN #{req.startTime} AND #{req.endTime}
                </when>
            </choose>
        </if>

        <!-- 门店查询 -->
        <if test="req.areaIdList != null and req.areaIdList.size() > 0">
            AND a.id IN
            <foreach collection="req.areaIdList" item="areaIds" open="(" separator="," close=")">
                #{areaIds}
            </foreach>
        </if>
    </select>

    <update id="markAs">
        UPDATE xiaomi_order SET oa_order_type = #{oaOrderType}
        WHERE is_del = 0 AND (oa_order_type = 2 OR oa_order_type is null)
        and od_delivery_number in
        <foreach collection="orderIdList" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </update>

    <select id="getMarkCount" resultType="java.lang.Integer">
        select COUNT(DISTINCT od_delivery_number) FROM xiaomi_order with(nolock)
        WHERE is_del = 0 AND (oa_order_type = 2 OR oa_order_type is null)
        and od_delivery_number in
        <foreach collection="orderIdList" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </select>
</mapper>
