<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.tenant.mapper.ThirdPlatformTenantUserMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.tenant.entity.ThirdPlatformTenantUser">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="thirdPlatformTenantId" column="third_platform_tenant_id" jdbcType="INTEGER"/>
            <result property="userId" column="user_id" jdbcType="INTEGER"/>
            <result property="xtenant" column="xtenant" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,third_platform_tenant_id,user_id,
        xtenant,create_time,update_time,
        is_del,third_platform_tenant_user_rv
    </sql>
    <select id="getUserIdByTenant" resultType="java.lang.Integer">
        select user_id from third_platform_tenant_user with(nolock)
        where isnull(is_del,0) = 0
          and third_platform_tenant_id = #{req.tenantId}
          and xtenant = #{req.xtenant}
          and plat_code = #{req.platCode}
    </select>

    <select id="getByTenant" resultType="com.jiuji.oa.oacore.thirdplatform.tenant.entity.ThirdPlatformTenantUser">
        select * from third_platform_tenant_user with(nolock)
        where isnull(is_del,0) = 0
          and third_platform_tenant_id = #{req.tenantId}
          and xtenant = #{req.xtenant}
          and plat_code = #{req.platCode}
    </select>
</mapper>
