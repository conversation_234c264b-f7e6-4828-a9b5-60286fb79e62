<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.order.mapper.JdOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.order.vo.OrderVO">
        <id column="id" property="id"/>
        <result column="plat_code" property="platCode"/>
        <result column="order_id" property="orderId"/>
        <result column="order_time" property="orderTime"/>
        <result column="org_code" property="tenantCode"/>
        <result column="area_id" property="areaId"/>
        <result column="area_code2" property="areaCode"/>
        <result column="store_code" property="storeCode"/>
        <result column="buyer_pin" property="buyerPin"/>
        <result column="buyer_name" property="buyerName"/>
        <result column="buyer_address" property="buyerAddress"/>
        <result column="buyer_mobile" property="buyerMobile"/>
        <result column="buyer_tel" property="buyerTel"/>
        <result column="trade_time" property="tradeTime"/>
        <result column="order_time" property="orderTime"/>
        <result column="total_money" property="totalMoney"/>
        <result column="discount_money" property="discountMoney"/>
        <result column="good_money" property="goodMoney"/>
        <result column="point_money" property="pointMoney"/>
        <result column="freight_money" property="freightMoney"/>
        <result column="payable_money" property="payableMoney"/>
        <result column="buyer_city" property="buyerCity"/>
        <result column="buyer_city_name" property="buyerCityName"/>
        <result column="buyer_country" property="buyerCountry"/>
        <result column="buyer_country_name" property="buyerCountryName"/>
        <result column="buyer_remark" property="buyerRemark"/>
        <result column="order_status" property="orderStatus"/>
        <result column="sub_id" property="subId"/>
        <result column="sub_message" property="subMessage"/>
        <result column="pay_status" property="payStatus"/>
        <result column="vender_money" property="venderMoney"/>
        <result column="plat_money" property="platMoney"/>
        <result column="sub_check" property="subCheck"/>
        <result column="tenant_name" property="tenantName"/>
        <result column="area_name" property="areaName"/>
        <result column="user_cancel_flag" property="cancelCheck"/>
        <result column="government_subsidy_flag" property="governmentSubsidyFlag"/>
    </resultMap>


    <select id="orderList" resultMap="BaseResultMap">
        SELECT t.* ,s.sub_check,a.area as area_code2,a.area_name,b.tenant_name FROM jingdong_order t WITH(NOLOCK)
        <choose>
            <when test="search.agencyOperation != null and search.agencyOperation == 1">
                JOIN dbo.jingdong_store d with(nolock) ON t.store_code=d.store_code
             </when>
             <otherwise>
                LEFT JOIN dbo.jingdong_store d with(nolock) ON t.store_code=d.store_code
             </otherwise>
        </choose>
        LEFT JOIN sub s with(nolock) ON t.sub_id =s.sub_id
        LEFT JOIN areainfo a WITH(NOLOCK) ON a.id=d.area_id
        LEFT JOIN jingdong_tenant b WITH(NOLOCK) ON b.tenant_code=d.tenant_code
        <where>
            <if test="search.subCheck!=null">
                AND s.sub_check = #{search.subCheck}
            </if>
            <if test="search.startTime!=null">
                AND t.order_time >= #{search.startTime}
            </if>
            <if test="search.endTime!=null">
                AND t.order_time &lt;= #{search.endTime}
            </if>
            <if test="search.cancelCheck != null and search.cancelCheck == 1">
                AND ISNULL(t.user_cancel_flag,0) = 1
            </if>
            <if test="search.cancelCheck != null and search.cancelCheck == 0">
                AND ISNULL(t.user_cancel_flag,0) != 1
            </if>

            <!-- 1-商户号 2-平台订单号 3-平台门店 4-本地订单号 -->
            <if test="search.searchValue != null and search.searchValue != ''">
                <choose>
                    <when test="search.searchOptions == 1">
                        AND t.org_code = #{search.searchValue}
                    </when>
                    <when test="search.searchOptions == 2">
                        AND t.order_id = #{search.searchValue}
                    </when>
                    <when test="search.searchOptions == 3">
                        AND t.store_code  = #{search.searchValue}
                    </when>
                    <when test="search.searchOptions == 4">
                        AND t.sub_id  = #{search.searchValue}
                    </when>
                    <when test="search.searchOptions == 5">
                        AND t.buyer_remark like CONCAT('%',#{search.searchValue},'%')
                    </when>
                </choose>
            </if>
            <if test="search.generateManualSubFlag != null and search.generateManualSubFlag == 0">
                AND t.sub_id is null and DATEDIFF(MINUTE,t.order_time ,GETDATE())>5 and isnull(t.generate_manual_sub_flag,0) = 0
            </if>
            <if test="search.generateManualSubFlag != null and search.generateManualSubFlag == 1">
                AND t.sub_id is not null and isnull(t.generate_manual_sub_flag,0) = 1
            </if>
            <if test="search.governmentSubsidyFlag != null and search.governmentSubsidyFlag == 0">
                AND isnull(t.government_subsidy_flag,0) = 0
            </if>
            <if test="search.governmentSubsidyFlag != null and search.governmentSubsidyFlag == 1">
                AND isnull(t.government_subsidy_flag,0) = 1
            </if>
        </where>
    </select>

</mapper>
