<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.yading.mapper.YadingUserMapper">

    <select id="getNotSyncYadingUserList"
            resultType="com.jiuji.oa.oacore.thirdplatform.productconfig.bo.Ch999UserYadingBO">
        select isnull(yu.sync_yading_state, 0) as syncYadingState, u.*
        from ch999_user u WITH(nolock)
        left join yading_user yu
        WITH (nolock)
        on yu.ch999_id = u.ch999_id
        where isnull(yu.sync_yading_state, 0) = 0 and u.islogin = 0
    </select>


    <select id="getSyncYadingUserById"
            resultType="com.jiuji.oa.oacore.thirdplatform.productconfig.bo.Ch999UserYadingBO">
        select distinct isnull(yu.sync_yading_state,0) as syncYadingState,u.* from ch999_user u WITH(nolock)
        left join yading_user yu WITH(nolock) on yu.ch999_id = u.ch999_id
        <where>u.ch999_id = #{userid}
        </where>
    </select>

    <select id="getSyncYadingUserByName"
            resultType="com.jiuji.oa.oacore.thirdplatform.productconfig.bo.Ch999UserYadingBO">
        select distinct isnull(yu.sync_yading_state,0) as syncYadingState,u.* from ch999_user u WITH(nolock)
        left join yading_user yu WITH(nolock) on yu.ch999_id = u.ch999_id
        <where>u.ch999_name = #{userName}
        </where>
    </select>
    <sql id="getYadingList">
        select a.id as areaId,a.area as areaCode ,a.业务类型 as bizType,a.sub_id as sourceSubId,iif(a.业务类型 = 2,'R','')++cast(a.sub_id as varchar) as
        sub_id,a.tradedate as tradeTime,a.seller as seller,a.trader as trader,a.sub_to as customerName,a.sub_mobile as customerPhone,
        a.servicePpriceid as serviceSkuId,a.bindPpriceid as mainSkuId,a.imei as mainSkuImei,a.数量 as amount,a.原价 as
        oldPrice,a.实付金额 as actualPrice,a.成本 as inPrice,a.毛利 as profits,a.优惠码承担金额 as promoCodePrice,a.积分承担金额 as
        pointPrice,a.comment as comment,a.bindPpriceName as mainSkuName,a.bindPpriceColor as mainSkuProductColor,
        a.servicePpriceName as serviceSkuName,a.servicePpriceColor as serviceSkuProductColor,a.register_state
        from (
        <if test="req.bizType!=null and (req.bizType==0 or req.bizType==1 )">
            SELECT a.id,a.area,1 业务类型,sr.sub_id,sr.tradedate,srb.seller,s.trader,s.sub_to,s.sub_mobile,sr.areaid,
                   sr.ppriceid as servicePpriceid,mb.ppriceid as bindPpriceid,sr.imei,
            count(1) 数量,sum(srb.price1)/count(1) 原价,SUM(srb.price2) 实付金额,isnull(sum(be.third_platform_inPrice),0.0) 成本,
            sum(isnull(srb.price2,0.0)-isnull(be.third_platform_inPrice,0.0)) 毛利,isnull(sum(srb.youhuiPrice),0.0)
            优惠码承担金额,isnull(sum(srb.jifenPrice),0.0) 积分承担金额,s.comment,p.product_name as bindPpriceName,
            p.product_color as bindPpriceColor,p2.product_name as servicePpriceName,p2.product_color as servicePpriceColor,yss.register_state
            from ServiceRecord sr with(nolock)
            left join basket mb with(nolock) on mb.basket_id = sr.basket_idBind
            left join yading_service_record re with(nolock ) on re.basket_id = sr.basket_id
            left join yading_service_state yss with (nolock) on re.id = yss.id
            left join basket srb with(nolock) on srb.basket_id = sr.basket_id
            left join basket_extend be with(nolock) on be.basket_id = srb.basket_id
            left join sub s with(nolock) on s.sub_id = sr.sub_id
            left join areainfo a with(nolock) on a.id = sr.areaid
            LEFT JOIN productinfo p with(nolock) ON mb.ppriceid = p.ppriceid
            LEFT JOIN productinfo p2 with(nolock) ON sr.ppriceid = p2.ppriceid
            <where>sr.classification in (2,3)
                <!--交易时间-->
                <if test="req.startTime != null and req.endTime != null  ">
                    and sr.tradedate between #{req.startTime} and #{req.endTime}
                </if>
                <if test="req.yadingRegisterStateList != null and req.yadingRegisterStateList.size()>0 ">
                    and yss.register_state in
                    <foreach collection="req.yadingRegisterStateList" item="it" separator="," open="(" close=")">
                        #{it}
                    </foreach>
                </if>
                <!--门店-->
                <if test="req.searchAreaIdList != null and req.searchAreaIdList.size()>0 ">
                    and a.id in
                    <foreach collection="req.searchAreaIdList" item="it" separator="," open="(" close=")">
                        #{it}
                    </foreach>
                </if>
                <!--门店级别-->
                <if test="req.areaLevel!=null and req.areaLevel.size()!=0">
                    AND a.level1 IN
                    <foreach collection="req.areaLevel" item="item" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                <!--门店属性-->
                <if test="req.areaAttribute!=null and req.areaAttribute.size()!=0">
                    AND a.kind1 IN
                    <foreach collection="req.areaAttribute" item="item" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                <!--商品名称-->
                <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                    and p2.product_name like '%' + #{req.searchValue} + '%'
                </if>
                <!--skuid-->
                <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                    and p.ppriceid = #{req.searchValue}
                </if>
                <!--商品id-->
                <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
                    and p2.productid = #{req.searchValue}
                </if>
                <!--订单号-->
                <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==4">
                    and s.sub_id = #{req.searchValue}
                </if>
                <!--手机号码-->
                <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==5">
                    and s.sub_mobile = #{req.searchValue}
                </if>
                <!--订单备注-->
                <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==6">
                    and s.comment like '%' + #{req.searchValue} + '%'
                </if>
                <!--主商品imei-->
                <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==7">
                    and sr.imei = #{req.searchValue}
                </if>
                <!--服务商品id-->
                <if test="req.servicePpriceId!=null and req.servicePpriceId!=''">
                    and p2.ppriceid = #{req.servicePpriceId}
                </if>
                <!--员工搜索-->
                <if test="req.searchUserValue != null and req.searchUserValue!=''  ">
                    <!--销售人-->
                    <if test="req.searchUserType!=null and req.searchUserType==1">
                        AND srb.seller LIKE CONCAT('%',#{req.searchUserValue},'%')
                    </if>
                    <!--交易人-->
                    <if test="req.searchUserType!=null and req.searchUserType==2">
                        AND s.trader LIKE CONCAT('%',#{req.searchUserValue},'%')
                    </if>
                </if>
                <!--订单来源-->
                <if test="req.orderSource!=null and req.orderSource.size()!=0">
                    and s.subtype IN
                    <foreach collection="req.orderSource" item="item" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                <!--配送方式-->
                <if test="req.deliveryType!=null and req.deliveryType!=''">
                    and s.delivery = #{req.deliveryType}
                </if>
                <!--支付方式-->
                <if test="req.payType!=null and req.payType.size()!=0">
                    and s.sub_pay IN
                    <foreach collection="req.payType" item="item" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
            </where>
            group by
            a.id,a.area,sr.sub_id,sr.tradedate,srb.seller,s.trader,s.sub_to,s.sub_mobile,sr.areaid,sr.ppriceid,mb.ppriceid,sr.imei,s.comment,
            p.product_name,p.product_color,p2.product_name,p2.product_color,yss.register_state
        </if>
        <if test="req.bizType!=null and req.bizType==0">
        union all
        </if>
        <if test="req.bizType!=null and (req.bizType==0 or req.bizType==2 )">
            SELECT a.id,a.area,2 业务类型,isnull(s.subPID,sr.sub_id)
            sub_id,sp.qujiandate as tradedate,srb.seller,s.trader,s.sub_to,s.sub_mobile,sr.areaid,sr.ppriceid servicePpriceid,
            mb.ppriceid bindPpriceid,sr.imei,-count(1) 数量,sum(srb.price1)/count(1) 原价,-SUM(srb.price2)
            实付金额,-isnull(sum(be.third_platform_inPrice),0.0) 成本,
            -(sum(isnull(srb.price2,0.0)-isnull(be.third_platform_inPrice,0.0))) 毛利,-isnull(sum(srb.youhuiPrice),0.0)
            优惠码承担金额,-isnull(sum(srb.jifenPrice),0.0) 积分承担金额,s.comment,p.product_name as bindPpriceName,
              p.product_color as bindPpriceColor,p2.product_name as servicePpriceName,p2.product_color as servicePpriceColor,yss.register_state
            from ServiceRecord sr with(nolock)
            left join basket mb with(nolock) on mb.basket_id = sr.basket_idBind
            left join yading_service_record re with(nolock ) on re.basket_id = sr.basket_id
            left join yading_service_state yss with (nolock) on re.id = yss.id
            left join basket srb with(nolock) on srb.basket_id = sr.basket_id
            left join basket_extend be with(nolock) on be.basket_id = srb.basket_id
            left join sub s with(nolock) on s.sub_id = sr.sub_id
            left join areainfo a with(nolock) on a.id = sr.areaid
            left join SmallproBill sb with(nolock) on sb.basket_id = srb.basket_id
            left join Smallpro sp with(nolock) on sp.id = sb.smallproID
            LEFT JOIN productinfo p with(nolock) ON mb.ppriceid = p.ppriceid
            LEFT JOIN productinfo p2 with(nolock) ON sr.ppriceid = p2.ppriceid
            <where>sr.classification in (2,3) and isnull(sr.isdel,0) = 1
                <!--取件时间-->
                <if test="req.startTime != null and req.endTime != null  ">
                    and sp.qujiandate between #{req.startTime} and #{req.endTime}
                </if>
                <if test="req.yadingRegisterStateList != null and req.yadingRegisterStateList.size()>0 ">
                    and yss.register_state in
                    <foreach collection="req.yadingRegisterStateList" item="it" separator="," open="(" close=")">
                        #{it}
                    </foreach>
                </if>
                <!--门店-->
                <if test="req.searchAreaIdList != null and req.searchAreaIdList.size()>0 ">
                    and a.id in
                    <foreach collection="req.searchAreaIdList" item="it" separator="," open="(" close=")">
                        #{it}
                    </foreach>
                </if>
                <!--门店级别-->
                <if test="req.areaLevel!=null and req.areaLevel.size()!=0">
                    AND a.level1 IN
                    <foreach collection="req.areaLevel" item="item" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                <!--门店属性-->
                <if test="req.areaAttribute!=null and req.areaAttribute.size()!=0">
                    AND a.attribute IN
                    <foreach collection="req.areaAttribute" item="item" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                <!--商品名称-->
                <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                    and p.product_name like '%' + #{req.searchValue} + '%'
                </if>
                <!--skuid-->
                <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                    and p.ppriceid = #{req.searchValue}
                </if>
                <!--商品id-->
                <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
                    and p.productid = #{req.searchValue}
                </if>
                <!--订单号-->
                <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==4">
                    and s.sub_id = #{req.searchValue}
                </if>
                <!--手机号码-->
                <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==5">
                    and s.sub_mobile = #{req.searchValue}
                </if>
                <!--订单备注-->
                <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==6">
                    and s.comment like '%' + #{req.searchValue} + '%'
                </if>
                <!--主商品imei-->
                <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==7">
                    and sr.imei = #{req.searchValue}
                </if>
                <!--服务商品id-->
                <if test="req.servicePpriceId!=null and req.servicePpriceId!=''">
                    and p2.ppriceid = #{req.servicePpriceId}
                </if>
                <!--员工搜索-->
                <if test="req.searchUserValue != null and req.searchUserValue!=''  ">
                    <!--销售人-->
                    <if test="req.searchUserType!=null and req.searchUserType==1">
                        AND srb.seller LIKE CONCAT('%',#{req.searchUserValue},'%')
                    </if>
                    <!--交易人-->
                    <if test="req.searchUserType!=null and req.searchUserType==2">
                        AND s.trader LIKE CONCAT('%',#{req.searchUserValue},'%')
                    </if>
                </if>
                <!--订单来源-->
                <if test="req.orderSource!=null and req.orderSource.size()!=0">
                    and s.subtype IN
                    <foreach collection="req.orderSource" item="item" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                <!--配送方式-->
                <if test="req.deliveryType!=null and req.deliveryType!=''">
                    and s.delivery = #{req.deliveryType}
                </if>
                <!--支付方式-->
                <if test="req.payType!=null and req.payType.size()!=0">
                    and s.sub_pay IN
                    <foreach collection="req.payType" item="item" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
            </where>
            group by
            a.id,a.area,isnull(s.subPID,sr.sub_id),sp.qujiandate,srb.seller,s.trader,s.sub_to,s.sub_mobile,sr.areaid,sr.ppriceid,mb.ppriceid,sr.imei,
            s.comment,p.product_name,p.product_color,p2.product_name,p2.product_color,yss.register_state
        </if>
        ) a
    </sql>

    <select id="queryYadingServiceStatistics"
            resultType="com.jiuji.oa.oacore.thirdplatform.yading.vo.res.YadingServiceStatisticsRes">
        select temp.serviceSkuId,temp.serviceSkuName,temp.serviceSkuProductColor,
        ISNULL(SUM(temp.amount),0) as amount ,ISNULL(avg(temp.oldPrice),0) as oldPrice,ISNULL(SUM(temp.actualPrice),0) as actualPrice,
        ISNULL(SUM(temp.inPrice),0) as inPrice,ISNULL(SUM(temp.profits),0) as profits,
        ISNULL(SUM(temp.promoCodePrice),0) as promoCodePrice,ISNULL(SUM(temp.pointPrice),0) as pointPrice
        from ( <include refid="getYadingList"/>) as temp
        group by temp.serviceSkuId,temp.serviceSkuName,temp.serviceSkuProductColor
        order by temp.serviceSkuId
    </select>
    <select id="queryYadingServiceAndAreaStatistics"
            resultType="com.jiuji.oa.oacore.thirdplatform.yading.vo.res.YadingServiceStatisticsRes">
        select temp.areaId,temp.areaCode,temp.serviceSkuId,temp.serviceSkuName,temp.serviceSkuProductColor,
        ISNULL(SUM(temp.amount),0) as amount ,ISNULL(avg(temp.oldPrice),0) as oldPrice,ISNULL(SUM(temp.actualPrice),0) as actualPrice,
        ISNULL(SUM(temp.inPrice),0) as inPrice,ISNULL(SUM(temp.profits),0) as profits,
        ISNULL(SUM(temp.promoCodePrice),0) as promoCodePrice,ISNULL(SUM(temp.pointPrice),0) as pointPrice
        from ( <include refid="getYadingList"/>) as temp
        group by temp.areaId,temp.areaCode,temp.serviceSkuId,temp.serviceSkuName,temp.serviceSkuProductColor
        order by temp.areaId,temp.serviceSkuId
    </select>


    <select id="queryYadingListPage"
            resultType="com.jiuji.oa.oacore.thirdplatform.yading.vo.res.YadingServiceListPageRes">
        select * from ( <include refid="getYadingList"/>) as temp order by temp.sourceSubId desc,temp.tradeTime
    </select>
</mapper>
