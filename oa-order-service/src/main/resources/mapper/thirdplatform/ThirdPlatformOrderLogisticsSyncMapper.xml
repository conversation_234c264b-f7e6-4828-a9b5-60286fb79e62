<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.order.mapper.ThirdPlatformOrderLogisticsSyncMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.order.entity.ThirdPlatformOrderLogisticsSync">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
            <result property="subId" column="sub_id" jdbcType="BIGINT"/>
            <result property="companyCode" column="company_code" jdbcType="VARCHAR"/>
            <result property="logisticsCode" column="logistics_code" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="is_del" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_id,sub_id,
        company_code,logistics_code,create_time,
        update_time,is_del,third_platform_order_logistics_sync_rv
    </sql>
    <select id="getLogisticsSyncByKindAndOrderId"
            resultType="com.jiuji.oa.oacore.thirdplatform.order.entity.ThirdPlatformOrderLogisticsSync">
        select
            ls.id,ls.order_id,ls.sub_id,ls.kind,
            ls.company_code,ls.logistics_code
            from third_platform_order_logistics_sync ls with(nolock)
        where ls.is_del = 0
          and ls.order_id = #{orderId}
          and ls.kind = #{kind}
    </select>
    <select id="getOrderIdNeedSync" resultType="java.lang.String">
        select tp.order_id from sub s with(nolock)
        join third_platform_order tp with(nolock) on s.sub_id = tp.sub_id
        join wuliu w with(nolock) on s.sub_id = w.danhaobind and w.wutype in (4,6) and w.stats in (1,2,3,7)
        join third_platform_order_logistics_sync ls with(nolock) on s.sub_id = ls.sub_id and ls.kind = 0 and ls.is_del = 0
        where s.subtype in (32)
          and s.sub_check in (2,6)
          and isnull(w.nu,w.id) != ls.logistics_code
          and s.sub_date >= DATEADD(DAY, -7, GETDATE())
        union all
        select tp.order_id from sub s with(nolock)
        join third_platform_order tp with(nolock) on s.sub_id = tp.sub_id
        where s.subtype in (32)
          and s.sub_check in (2,6)
          and not exists(select 1 from third_platform_order_logistics_sync ls with(nolock) where ls.sub_id = s.sub_id and ls.kind = 0 and ls.is_del = 0)
          and s.sub_date >= DATEADD(DAY, -7, GETDATE())
    </select>
</mapper>
