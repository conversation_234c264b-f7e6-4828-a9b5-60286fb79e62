<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.store.mapper.StoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.store.vo.StoreVO">
        <id column="id" property="id"/>
        <result column="tenant_code" property="tenantCode"/>
        <result column="area_id" property="areaId"/>
        <result column="store_code" property="storeCode"/>
        <result column="storeName" property="store_name"/>
        <result column="is_enable" property="isEnable"/>
        <result column="plat_code" property="platCode"/>
        <result column="area_code" property="areaCode"/>
        <result column="area_name" property="areaName"/>
        <result column="tenant_name" property="tenantName"/>
        <result column="associated_stores" property="associatedStores"/>
        <result column="associated_stores_flag" property="associatedStoresFlag"/>
        <result column="delivery_type" property="deliveryType"/>
    </resultMap>
    <resultMap id="storeCodeResult" type="java.lang.String" >
        <result column="store_code" property="storeCode" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="columns">
        s.id,s.tenant_code,s.area_id,s.store_code,s.store_name,s.is_enable,s.plat_code,s.associated_stores,s.associated_stores_flag,s.large_channel_id,s.small_channel_id
    </sql>

    <insert id="saveBatch">
        insert into third_platform_store (tenant_code,area_id,store_code,is_enable,plat_code,associated_stores,s.large_channel_id,s.small_channel_id,delivery_type) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.tenantCode}, #{item.areaId}, #{item.storeCode}, #{item.isEnable},#{item.platCode},#{item.associatedStores},#{item.largeChannelId},#{item.smallChannelId},#{item.deliveryType})
        </foreach>
    </insert>

    <select id="storeList" resultMap="BaseResultMap">
        SELECT<include refid="columns"/>,a.area as area_code,a.area_name,t.tenant_name
        ,s.delivery_type
        FROM third_platform_store s WITH(NOLOCK)
        LEFT JOIN areainfo a WITH(NOLOCK) ON a.id=s.area_id
        LEFT JOIN third_platform_tenant t WITH(NOLOCK) ON t.tenant_code=s.tenant_code
        <where>
            <if test="search.tenantid!=null">
                AND t.id = #{search.tenantid}
            </if>
            <if test="search.tenantCode != null and search.tenantCode != ''">
                AND s.tenant_code = #{search.tenantCode}
            </if>
            <if test="search.platCode != null and search.platCode != ''">
                AND s.plat_code = #{search.platCode}
            </if>
            <if test="search.searchValue != null and search.searchValue != ''">
                <choose>
                    <when test="search.searchOptions == 1">
                        AND s.store_code LIKE concat('%',#{search.searchValue},'%')
                    </when>
                    <when test="search.searchOptions == 2">
                        AND a.area LIKE concat('%',#{search.searchValue},'%')
                    </when>
                    <when test="search.searchOptions == 3">
                        AND a.area_name LIKE concat('%',#{search.searchValue},'%')
                    </when>
                </choose>
            </if>
        </where>
    </select>

    <select id="getSyncStoreCodeList" resultMap="storeCodeResult">
        SELECT DISTINCT s.store_code FROM third_platform_store s WITH(NOLOCK)
                                            INNER JOIN dbo.product_mkc k WITH(NOLOCK) ON k.areaid=s.area_id
                                            INNER JOIN third_platform_product_config c WITH(NOLOCK) ON c.ppriceid=k.ppriceid
                        WHERE s.is_enable=1 AND k.kc_check=3 AND ISNULL(k.mouldFlag,0)=0 and isnull(c.is_del,0) = 0
                        AND EXISTS(SELECT 1 FROM dbo.third_platform_tenant xt WITH(NOLOCK) WHERE xt.tenant_code=s.tenant_code AND xt.is_enable=1)
                        AND NOT EXISTS(SELECT 1 FROM dbo.xc_mkc x WITH(NOLOCK) WHERE x.mkc_id=k.id)
                        UNION ALL
                        SELECT DISTINCT s.store_code FROM third_platform_store s WITH(NOLOCK)
                                            INNER JOIN dbo.product_kc k WITH(NOLOCK) ON k.areaid=s.area_id
                                            INNER JOIN third_platform_product_config c WITH(NOLOCK) ON c.ppriceid=k.ppriceid
                        WHERE s.is_enable=1 AND EXISTS(SELECT 1 FROM dbo.third_platform_tenant xt WITH(NOLOCK) WHERE xt.tenant_code=s.tenant_code AND xt.is_enable=1 ) and isnull(c.is_del,0) = 0
    </select>
    <select id="getChannelKindByChannelIdList" resultType="com.jiuji.oa.oacore.thirdplatform.store.entity.ChannelKindLink">
        select channel_id,kind FROM dbo.channel_kind_link with(nolock) where channel_state=1
        and channel_id in
        <foreach collection="channelIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectIsEnableStore" resultType="com.jiuji.oa.oacore.thirdplatform.store.entity.Store">
        SELECT
            store.id,
            store.tenant_code,
            store.area_id,
            store.store_code,
            store.store_name,
            store.is_enable,
            store.plat_code,
            store.associated_stores,
            store.associated_stores_flag,
            store.large_channel_id,
            store.small_channel_id
        FROM
            dbo.third_platform_store store with(nolock)
        left join dbo.third_platform_tenant tenant with(nolock ) on store.tenant_code =tenant.tenant_code
        WHERE
            store.plat_code = 'MI'
          AND isnull(tenant.is_enable,0) =1
          AND store.area_id = #{areaId}
          AND isnull(store.is_enable,0) = 1;
    </select>
    <select id="selectStoreByopPoiId" resultType="com.jiuji.oa.oacore.thirdplatform.store.entity.Store">
        select top 1 s.id,
            s.tenant_code,
            s.area_id,
            s.store_code,
            s.store_name,
            s.is_enable,
            s.plat_code,
            s.associated_stores,
            s.associated_stores_flag,
            s.large_channel_id,
            s.small_channel_id,
            s.op_poi_id
            FROM third_platform_store s with(nolock)
            WHERE s.op_poi_id = #{opPoiId}
            AND s.tenant_code = #{tenantCode}
            AND s.plat_code = #{platCode}
    </select>

</mapper>
