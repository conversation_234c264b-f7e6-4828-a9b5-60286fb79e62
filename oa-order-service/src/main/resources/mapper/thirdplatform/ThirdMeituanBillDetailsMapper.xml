<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.thirdplatform.order.mapper.ThirdMeituanBillDetailsMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.thirdplatform.order.entity.ThirdMeituanBillDetails">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="storeId" column="store_id" jdbcType="VARCHAR"/>
            <result property="storeName" column="store_name" jdbcType="VARCHAR"/>
            <result property="settlementId" column="settlement_id" jdbcType="VARCHAR"/>
            <result property="incomingSettlementId" column="incoming_settlement_id" jdbcType="VARCHAR"/>
            <result property="transactionType" column="transaction_type" jdbcType="VARCHAR"/>
            <result property="transactionDescription" column="transaction_description" jdbcType="VARCHAR"/>
            <result property="orderNumber" column="order_number" jdbcType="VARCHAR"/>
            <result property="orderTime" column="order_time" jdbcType="TIMESTAMP"/>
            <result property="completionTime" column="completion_time" jdbcType="TIMESTAMP"/>
            <result property="refundTime" column="refund_time" jdbcType="TIMESTAMP"/>
            <result property="orderStatus" column="order_status" jdbcType="VARCHAR"/>
            <result property="deliveryMethod" column="delivery_method" jdbcType="VARCHAR"/>
            <result property="deliveryStatus" column="delivery_status" jdbcType="VARCHAR"/>
            <result property="settlementStatus" column="settlement_status" jdbcType="VARCHAR"/>
            <result property="billDate" column="bill_date" jdbcType="DATE"/>
            <result property="billingPeriod" column="billing_period" jdbcType="VARCHAR"/>
            <result property="invoiceAmountCommission" column="invoice_amount_commission" jdbcType="DECIMAL"/>
            <result property="invoiceAmountDelivery" column="invoice_amount_delivery" jdbcType="DECIMAL"/>
            <result property="merchantReceivable" column="merchant_receivable" jdbcType="DECIMAL"/>
            <result property="totalProductPrice" column="total_product_price" jdbcType="DECIMAL"/>
            <result property="containerFee" column="container_fee" jdbcType="DECIMAL"/>
            <result property="packaging" column="packaging" jdbcType="DECIMAL"/>
            <result property="merchantActivityExpenditure" column="merchant_activity_expenditure" jdbcType="DECIMAL"/>
            <result property="promotionOrderActivity" column="promotion_order_activity" jdbcType="DECIMAL"/>
            <result property="everydaySpecialCouponMerchantBearing" column="everyday_special_coupon_merchant_bearing" jdbcType="DECIMAL"/>
            <result property="meituanActivitySubsidy" column="meituan_activity_subsidy" jdbcType="DECIMAL"/>
            <result property="commission" column="commission" jdbcType="DECIMAL"/>
            <result property="deliveryServiceFee" column="delivery_service_fee" jdbcType="DECIMAL"/>
            <result property="charityDonation" column="charity_donation" jdbcType="DECIMAL"/>
            <result property="userPaidDeliveryFee" column="user_paid_delivery_fee" jdbcType="DECIMAL"/>
            <result property="userOnlinePaymentAmount" column="user_online_payment_amount" jdbcType="DECIMAL"/>
            <result property="userOfflinePaymentAmount" column="user_offline_payment_amount" jdbcType="DECIMAL"/>
            <result property="rate" column="rate" jdbcType="VARCHAR"/>
            <result property="minimum" column="minimum" jdbcType="DECIMAL"/>
            <result property="capAmount" column="cap_amount" jdbcType="DECIMAL"/>
            <result property="sharingDiscount" column="sharing_discount" jdbcType="DECIMAL"/>
            <result property="merchantCouponMeituanSubsidy" column="merchant_coupon_meituan_subsidy" jdbcType="DECIMAL"/>
            <result property="merchantCouponMerchantSubsidy" column="merchant_coupon_merchant_subsidy" jdbcType="DECIMAL"/>
            <result property="merchantCouponUserPaid" column="merchant_coupon_user_paid" jdbcType="DECIMAL"/>
            <result property="merchantCouponCommissionRatio" column="merchant_coupon_commission_ratio" jdbcType="DECIMAL"/>
            <result property="periodicPurchasePeriods" column="periodic_purchase_periods" jdbcType="INTEGER"/>
            <result property="userPaymentMethod" column="user_payment_method" jdbcType="VARCHAR"/>
            <result property="orderSequence" column="order_sequence" jdbcType="VARCHAR"/>
            <result property="fruitBookingOrderCode" column="fruit_booking_order_code" jdbcType="VARCHAR"/>
            <result property="basicPrice" column="basic_price" jdbcType="DECIMAL"/>
            <result property="distanceCharge" column="distance_charge" jdbcType="DECIMAL"/>
            <result property="timeSlotCharge" column="time_slot_charge" jdbcType="DECIMAL"/>
            <result property="categoryCharge" column="category_charge" jdbcType="DECIMAL"/>
            <result property="weightCharge" column="weight_charge" jdbcType="DECIMAL"/>
            <result property="holidayCharge" column="holiday_charge" jdbcType="DECIMAL"/>
            <result property="peakOrderSurcharge" column="peak_order_surcharge" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,store_id,store_name,
        settlement_id,incoming_settlement_id,transaction_type,
        transaction_description,order_number,order_time,
        completion_time,refund_time,order_status,
        delivery_method,delivery_status,settlement_status,
        bill_date,billing_period,invoice_amount_commission,
        invoice_amount_delivery,merchant_receivable,total_product_price,
        container_fee,packaging,merchant_activity_expenditure,
        promotion_order_activity,everyday_special_coupon_merchant_bearing,meituan_activity_subsidy,
        commission,delivery_service_fee,charity_donation,
        user_paid_delivery_fee,user_online_payment_amount,user_offline_payment_amount,
        rate,minimum,cap_amount,
        sharing_discount,merchant_coupon_meituan_subsidy,merchant_coupon_merchant_subsidy,
        merchant_coupon_user_paid,merchant_coupon_commission_ratio,periodic_purchase_periods,
        user_payment_method,order_sequence,fruit_booking_order_code,
        basic_price,distance_charge,time_slot_charge,
        category_charge,weight_charge,holiday_charge,
        peak_order_surcharge
    </sql>
</mapper>
