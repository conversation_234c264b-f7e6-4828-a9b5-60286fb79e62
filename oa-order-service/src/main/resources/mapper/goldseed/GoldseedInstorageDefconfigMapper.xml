<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.goldseed.dao.GoldseedInstorageDefconfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.goldseed.po.GoldseedInstorageDefconfig">
        <id column="id" property="id" />
        <result column="sType" property="sType" />
        <result column="product_cid" property="productCid" />
        <result column="area_type" property="areaType" />
        <result column="to_area" property="toArea" />
        <result column="to_area_name" property="toAreaName" />
        <result column="is_enable" property="isEnable" />
        <result column="is_del" property="isDel" />
        <result column="xtenant" property="xtenant" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <select id="getBrandToAreaList" resultType="com.jiuji.oa.oacore.goldseed.bo.BrandToAreaBo">
        select pb.brand_id as brandId,idef.to_area as toArea,idef.to_area_name as toAreaName from goldseed_instorage_defconfig idef left join goldseed_product_config pc on idef.product_cid=pc.id
            left join goldseed_product_brand pb on pc.id = pb.product_cid
            where idef.is_del=0 and idef.is_enable=1 and pc.is_del=0
            <if test="type != null">
                and idef.type=#{type}
            </if>
            <if test="areaCode != null">
                and idef.area_code=#{areaCode}
            </if>
    </select>

</mapper>
