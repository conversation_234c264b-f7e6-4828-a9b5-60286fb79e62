<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.goldseed.dao.GoldseedProductBrandMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.goldseed.po.GoldseedProductBrand">
        <id column="id" property="id" />
        <result column="brand_id" property="brandId" />
        <result column="brand_name" property="brandName" />
        <result column="product_cid" property="productCid" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <select id="getPCidByBrandId" resultType="java.lang.Integer">
        select pc.id from goldseed_product_config pc left join goldseed_product_brand pb on pc.id = pb.product_cid
        where pc.is_del=0
        <if test="brandId != null and brandId > 0">
            and pb.brand_id = #{brandId}
        </if>
         <if test="areaCode != null and areaCode > 0">
             and pc.area_code = #{areaCode}
         </if>
         LIMIT 1
    </select>

    <select id="getPCidList" resultType="com.jiuji.oa.oacore.goldseed.bo.BrandPCidBo">
        select pc.id as pcid,pb.brand_id as brandId, pc.category_id as cid from goldseed_product_config pc left join goldseed_product_brand pb on pc.id = pb.product_cid
        where pc.is_del=0
        <if test="areaCode != null and areaCode > 0">
            and pc.area_code = #{areaCode}
        </if>
    </select>
</mapper>
