<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.goldseed.dao.ZitidianMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.goldseed.po.Zitidian">
        <id column="id" property="id" />
        <result column="cityid" property="cityid" />
        <result column="pid" property="pid" />
        <result column="zid" property="zid" />
        <result column="name" property="name" />
        <result column="address" property="address" />
        <result column="hours" property="hours" />
        <result column="tel1" property="tel1" />
        <result column="tel2" property="tel2" />
        <result column="Contact" property="Contact" />
        <result column="nickContact" property="nickContact" />
        <result column="zhoubian" property="zhoubian" />
        <result column="comment" property="comment" />
        <result column="comment2" property="comment2" />
        <result column="sex" property="sex" />
        <result column="zhengzhi" property="zhengzhi" />
        <result column="birthday" property="birthday" />
        <result column="national" property="national" />
        <result column="health" property="health" />
        <result column="idnumber" property="idnumber" />
        <result column="Origin" property="Origin" />
        <result column="Company" property="Company" />
        <result column="j_Address" property="jAddress" />
        <result column="j_Tel" property="jTel" />
        <result column="d_Address" property="dAddress" />
        <result column="d_Tel" property="dTel" />
        <result column="J_name" property="jName" />
        <result column="J_mobile" property="jMobile" />
        <result column="Jia_chenfu1" property="jiaChenfu1" />
        <result column="Jial_name1" property="jialName1" />
        <result column="Jial_work1" property="jialWork1" />
        <result column="Jial_tel1" property="jialTel1" />
        <result column="Jia_chenfu2" property="jiaChenfu2" />
        <result column="Jial_name2" property="jialName2" />
        <result column="Jial_work2" property="jialWork2" />
        <result column="Jial_tel2" property="jialTel2" />
        <result column="userid" property="userid" />
        <result column="ispass" property="ispass" />
        <result column="adddate" property="adddate" />
        <result column="cardid_pic" property="cardidPic" />
        <result column="photo_pic" property="photoPic" />
        <result column="Workid_pic" property="workidPic" />
        <result column="inuser" property="inuser" />
        <result column="rank" property="rank" />
        <result column="erdu" property="erdu" />
        <result column="did" property="did" />
        <result column="cardid_picId" property="cardidPicid" />
        <result column="photo_picId" property="photoPicid" />
        <result column="Workid_picId" property="workidPicid" />
        <result column="shopid" property="shopid" />
        <result column="applyId" property="applyId" />
        <result column="shopLevel" property="shopLevel" />
        <result column="bindAreaid" property="bindAreaid" />
        <result column="shopType" property="shopType" />
        <result column="xtenant" property="xtenant" />
        <result column="payCardNum" property="payCardNum" />
        <result column="payCardName" property="payCardName" />
    </resultMap>

    <select id="getCustomerById" resultType="com.jiuji.oa.oacore.goldseed.vo.CustomerVo">
        SELECT id, name FROM zitidian with(nolock) WHERE id = #{id}
    </select>

</mapper>
