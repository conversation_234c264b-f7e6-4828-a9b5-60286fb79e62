<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.goldseed.dao.Ok3wQudaoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.goldseed.po.Ok3wQudao">
        <id column="id" property="id" />
        <result column="kinds" property="kinds" />
        <result column="company" property="company" />
        <result column="username" property="username" />
        <result column="tel" property="tel" />
        <result column="fax" property="fax" />
        <result column="mobile" property="mobile" />
        <result column="QQ" property="qq" />
        <result column="wangwang" property="wangwang" />
        <result column="address" property="address" />
        <result column="Area" property="Area" />
        <result column="comment" property="comment" />
        <result column="adddate" property="adddate" />
        <result column="inuser" property="inuser" />
        <result column="ispass" property="ispass" />
        <result column="email" property="email" />
        <result column="insourceid" property="insourceid" />
        <result column="company_jc" property="companyJc" />
        <result column="g_zh1" property="gZh1" />
        <result column="g_hm1" property="gHm1" />
        <result column="g_khh1" property="gKhh1" />
        <result column="g_zh2" property="gZh2" />
        <result column="g_hm2" property="gHm2" />
        <result column="g_khh2" property="gKhh2" />
        <result column="s_zh1" property="sZh1" />
        <result column="s_hm1" property="sHm1" />
        <result column="s_khh1" property="sKhh1" />
        <result column="s_zh2" property="sZh2" />
        <result column="s_hm2" property="sHm2" />
        <result column="s_khh2" property="sKhh2" />
        <result column="cw_fzr" property="cwFzr" />
        <result column="cw_lxfs" property="cwLxfs" />
        <result column="s_zh3" property="sZh3" />
        <result column="s_hm3" property="sHm3" />
        <result column="s_khh3" property="sKhh3" />
        <result column="s_zh4" property="sZh4" />
        <result column="s_hm4" property="sHm4" />
        <result column="s_khh4" property="sKhh4" />
        <result column="s_zh5" property="sZh5" />
        <result column="s_hm5" property="sHm5" />
        <result column="s_khh5" property="sKhh5" />
        <result column="s_zh6" property="sZh6" />
        <result column="s_hm6" property="sHm6" />
        <result column="s_khh6" property="sKhh6" />
        <result column="s_zh7" property="sZh7" />
        <result column="s_hm7" property="sHm7" />
        <result column="s_khh7" property="sKhh7" />
        <result column="s_zh8" property="sZh8" />
        <result column="s_hm8" property="sHm8" />
        <result column="s_khh8" property="sKhh8" />
        <result column="kfp" property="kfp" />
        <result column="authorizeid" property="authorizeid" />
        <result column="signStartTime" property="signStartTime" />
        <result column="signEndTime" property="signEndTime" />
        <result column="cityid" property="cityid" />
        <result column="pid" property="pid" />
        <result column="zid" property="zid" />
        <result column="did" property="did" />
        <result column="CompanyNature" property="CompanyNature" />
        <result column="QuDaoNature" property="QuDaoNature" />
        <result column="QuDaoLevel" property="QuDaoLevel" />
        <result column="PayType" property="PayType" />
        <result column="RegisteredCapital" property="RegisteredCapital" />
        <result column="LegalRepresent" property="LegalRepresent" />
        <result column="LegalMobile" property="LegalMobile" />
        <result column="WeiXin" property="WeiXin" />
        <result column="shouhouContacts" property="shouhouContacts" />
        <result column="shouhouMobile" property="shouhouMobile" />
        <result column="yajin" property="yajin" />
        <result column="SameKindQuDao" property="SameKindQuDao" />
        <result column="LoanAmount_M" property="loanamountM" />
        <result column="LoanAmount" property="LoanAmount" />
        <result column="CaiWuCheckUser" property="CaiWuCheckUser" />
        <result column="CaiWuCheckTime" property="CaiWuCheckTime" />
        <result column="ShenJiCheckUser" property="ShenJiCheckUser" />
        <result column="ShenJiCheckTime" property="ShenJiCheckTime" />
        <result column="PanDianUser" property="PanDianUser" />
        <result column="PanDianTime" property="PanDianTime" />
        <result column="kemu" property="kemu" />
        <result column="charger" property="charger" />
        <result column="username_1" property="username1" />
        <result column="tel_1" property="tel1" />
        <result column="username_2" property="username2" />
        <result column="tel_2" property="tel2" />
        <result column="comment1" property="comment1" />
        <result column="seltPurchase" property="seltPurchase" />
        <result column="CooperationId" property="CooperationId" />
        <result column="CustomCode" property="CustomCode" />
        <result column="userid" property="userid" />
        <result column="ChannelType" property="ChannelType" />
        <result column="ChannelScale" property="ChannelScale" />
        <result column="DepositHasReceipt" property="DepositHasReceipt" />
        <result column="cids" property="cids" />
        <result column="brandid" property="brandid" />
        <result column="Receiver" property="Receiver" />
        <result column="ShippingAddress" property="ShippingAddress" />
        <result column="bindAreaId" property="bindAreaId" />
    </resultMap>

</mapper>
