<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.goldseed.dao.HwGoldSeedMapper">

    <select id="getStorageList" resultType="com.jiuji.oa.oacore.goldseed.bo.GoldseedStorageDataBo">
        SELECT
        k.imei,
        CONVERT ( nvarchar ( 10 ), dateadd( DAY,-#{day}, getdate()), 121 ) AS inStorageTime,
        CASE

        WHEN a.dcAreaID= a.id THEN
        a.area ELSE ''
        END AS storageNo,
        CASE

        WHEN a.dcAreaID= a.id THEN
        a.area_name ELSE ''
        END AS storageName,
        CASE

        WHEN a.dcAreaID= a.id THEN
        '' ELSE a.area
        END AS area,
        CASE

        WHEN a.dcAreaID= a.id THEN
        '' ELSE a.area_name
        END AS areaName,
        CAST ( k.inbeihuoprice AS INT ) AS purchasePrice,
        '中国' AS country,
        dbo.getProvinceName ( a.cityid ) AS provinceName,
        dbo.getCityNameByCityid ( a.cityid ) AS cityName,
        q.company as sCustomerName,
        q.id as sCustomerNo,
        p.brandID as brandId,
        p.product_id as pid,
        p.cid
        FROM
        dbo.product_mkc k WITH (nolock)
        LEFT JOIN dbo.productinfo p WITH (nolock) ON p.ppriceid = k.ppriceid
        LEFT JOIN dbo.areainfo a WITH (nolock) ON k.areaid= a.id
        LEFT JOIN dbo.Ok3w_qudao q WITH (nolock) ON k.insourceid2= q.id
        WHERE k.kc_check IN ( 3, 10 )
        AND k.imei IS NOT NULL
        <if test="brandIds != null and brandIds.size>0">
            AND p.brandID in
            <foreach collection="brandIds" index="index" item="brandId" separator="," open="(" close=")">
                #{brandId}
            </foreach>
        </if>
        <if test="cids != null and cids.size>0">
            AND p.cid in
            <foreach collection="cids" index="index" item="cid" separator="," open="(" close=")">
                #{cid}
            </foreach>
        </if>
        <if test="supplierIds != null and supplierIds.size>0">
            AND k.insourceid2 IN
            <foreach collection="supplierIds" index="index" item="supplierId" separator="," open="(" close=")">
                #{supplierId}
            </foreach>
        </if>
    </select>

    <select id="getSealDataList" resultType="com.jiuji.oa.oacore.goldseed.bo.GoldseedSealDataBo">
       SELECT
        '中国' AS country,
        dbo.getProvinceName (a.cityid) AS provinceName,
        dbo.getCityNameByCityid (a.cityid) AS cityName,
        CONVERT (nvarchar(10),s.tradeDate1,121) AS soDate,
        k.imei,
        CASE
        WHEN a.dcAreaID= a.id THEN
        a.area ELSE ''
        END AS storageNo,
        CASE
        WHEN a.dcAreaID= a.id THEN
        a.area_name ELSE ''
        END AS storageName,
        CASE
        WHEN a.dcAreaID= a.id THEN
        '' ELSE a.area
        END AS area,
        CASE
        WHEN a.dcAreaID= a.id THEN
        '' ELSE a.area_name
        END AS areaName,
        CAST (b.price AS INT) AS salePrice,
        null AS isGovernment,
        CASE

        WHEN s.delivery= 1 THEN
        '线下' ELSE '线上'
        END AS saleModel,
        q.company as sCustomerName,
        q.id as sCustomerNo,
        p.brandID AS brandId,
        p.product_id AS pid,
        s.userid as userId,
        p.product_name as productName,
        p.product_color as productColor,
        p.ppriceid as ppid,
        '' reserved3,
        p.cid
        FROM
        dbo.product_mkc k WITH (nolock)
        LEFT JOIN dbo.productinfo p WITH (nolock) ON p.ppriceid = k.ppriceid
        LEFT JOIN dbo.areainfo a WITH (nolock) ON k.areaid= a.id
        LEFT JOIN dbo.Ok3w_qudao q WITH (nolock) ON k.insourceid2= q.id
        LEFT JOIN dbo.basket b WITH (nolock) ON b.basket_id = k.basket_id
        AND isnull(b.isdel,0) = 0
        LEFT JOIN dbo.sub s WITH (nolock) ON s.sub_id = b.sub_id
        WHERE k.imei IS NOT NULL
        and s.subtype not in(13,14)
        and ((k.kc_check = 5 and s.sub_check = 3 and datediff(DAY,s.tradeDate1,getdate() - #{day}) = 0)
            or (s.sub_check = 9 and datediff(DAY,s.tradeDate1,getdate() - #{day}) = 0 and datediff(DAY,s.returnDate,getdate() - #{day}) = 0)
        )
        <if test="brandIds != null and brandIds.size>0">
            AND p.brandID in
            <foreach collection="brandIds" index="index" item="brandId" separator="," open="(" close=")">
                #{brandId}
            </foreach>
        </if>
        <if test="supplierIds != null and supplierIds.size>0">
            AND k.insourceid2 IN
            <foreach collection="supplierIds" index="index" item="supplierId" separator="," open="(" close=")">
                #{supplierId}
            </foreach>
        </if>
        <if test="cids != null and cids.size>0">
            AND p.cid in
            <foreach collection="cids" index="index" item="cid" separator="," open="(" close=")">
                #{cid}
            </foreach>
        </if>
        union all
        SELECT
        '中国' AS country,
        dbo.getProvinceName (a.cityid) AS provinceName,
        dbo.getCityNameByCityid (a.cityid) AS cityName,
        CONVERT (nvarchar(10),s.tradeDate1,121) AS soDate,
        k.imei,
        CASE
        WHEN a.dcAreaID= a.id THEN
        a.area ELSE ''
        END AS storageNo,
        CASE
        WHEN a.dcAreaID= a.id THEN
        a.area_name ELSE ''
        END AS storageName,
        CASE
        WHEN a.dcAreaID= a.id THEN
        '' ELSE a.area
        END AS area,
        CASE
        WHEN a.dcAreaID= a.id THEN
        '' ELSE a.area_name
        END AS areaName,
        CAST (b.price AS INT) AS salePrice,
        null AS isGovernment,
        CASE

        WHEN s.delivery= 1 THEN
        '线下' ELSE '线上'
        END AS saleModel,
        q.company as sCustomerName,
        q.id as sCustomerNo,
        p.brandID AS brandId,
        p.product_id AS pid,
        s.userid as userId,
        p.product_name as productName,
        p.product_color as productColor,
        p.ppriceid as ppid,
        'Y' reserved3,
        p.cid
        FROM
        dbo.product_mkc k WITH (nolock)
        LEFT JOIN dbo.productinfo p WITH (nolock) ON p.ppriceid = k.ppriceid
        LEFT JOIN dbo.areainfo a WITH (nolock) ON k.areaid= a.id
        LEFT JOIN dbo.Ok3w_qudao q WITH (nolock) ON k.insourceid2= q.id
        LEFT JOIN dbo.basket b WITH (nolock) ON b.basket_id = k.basket_id
        AND isnull(b.isdel,0) = 0
        LEFT JOIN dbo.sub s WITH (nolock) ON s.sub_id = b.sub_id
        WHERE k.kc_check = 5
        AND k.imei IS NOT NULL
        AND s.sub_check = 9
        AND datediff(DAY,s.returnDate,getdate() - #{day}) = 0
        and s.subtype not in(13,14)
        <if test="brandIds != null and brandIds.size>0">
            AND p.brandID in
            <foreach collection="brandIds" index="index" item="brandId" separator="," open="(" close=")">
                #{brandId}
            </foreach>
        </if>
        <if test="supplierIds != null and supplierIds.size>0">
            AND k.insourceid2 IN
            <foreach collection="supplierIds" index="index" item="supplierId" separator="," open="(" close=")">
                #{supplierId}
            </foreach>
        </if>
        <if test="cids != null and cids.size>0">
            AND p.cid in
            <foreach collection="cids" index="index" item="cid" separator="," open="(" close=")">
                #{cid}
            </foreach>
        </if>
    </select>

    <select id="getDisDataList" resultType="com.jiuji.oa.oacore.goldseed.bo.GoldseedDisDataBo">
        SELECT '中国' AS country,
        dbo.getProvinceName ( a.cityid ) AS provinceName,
        CONVERT ( nvarchar ( 10 ), s.tradeDate1, 121 ) AS sendGoodDate,
        k.imei,
        q.company as sName,
        q.id as sId,
        p.brandID AS brandId,
        p.product_id AS pid,
        s.userid as userId,
        ziti.id as customerId,
        ziti.name as customerName,
        dbo.getProvinceName ( ziti.cityid ) AS toProvinceName,
        dbo.getCityNameByCityid ( ziti.cityid ) AS toCityName,
        p.product_name as productName,
        p.product_color as productColor,
        p.ppriceid as ppid,
        p.cid
        FROM
        dbo.product_mkc k WITH (nolock)
        LEFT JOIN dbo.productinfo p WITH (nolock) ON p.ppriceid = k.ppriceid
        LEFT JOIN dbo.areainfo a WITH (nolock) ON k.areaid= a.id
        LEFT JOIN dbo.Ok3w_qudao q WITH (nolock) ON k.insourceid2= q.id
        LEFT JOIN dbo.basket b WITH (nolock) ON b.basket_id = k.basket_id
        AND isnull( b.isdel, 0 ) = 0
        LEFT JOIN dbo.sub s WITH (nolock) ON s.sub_id = b.sub_id
        LEFT JOIN zitidian ziti WITH (nolock) on ziti.userid=s.userid
        WHERE 1=1
        <if test="brandIds != null and brandIds.size>0">
            AND p.brandID in
            <foreach collection="brandIds" index="index" item="brandId" separator="," open="(" close=")">
                #{brandId}
            </foreach>
        </if>
        <if test="supplierIds != null and supplierIds.size>0">
            AND k.insourceid2 IN
            <foreach collection="supplierIds" index="index" item="supplierId" separator="," open="(" close=")">
                #{supplierId}
            </foreach>
        </if>
        <if test="cids != null and cids.size>0">
            AND p.cid in
            <foreach collection="cids" index="index" item="cid" separator="," open="(" close=")">
                #{cid}
            </foreach>
        </if>
        AND k.kc_check=5
        AND k.imei IS NOT NULL
        and s.subtype in(13,14)
        and ziti.ispass=1
        and ((s.sub_check=3 AND datediff( DAY, s.tradeDate1, getdate() - #{day} ) = 0)
            or (s.sub_check=9 AND datediff( DAY, s.tradeDate1, getdate() - #{day} ) = 0 AND datediff( DAY, s.returnDate, getdate() - #{day} ) = 0)
        )

    </select>

    <select id="getInstorageList" resultType="com.jiuji.oa.oacore.goldseed.bo.GoldseedInstorageDataBo">
        select
        '中国' as country
        ,dbo.getProvinceName(a.cityid) as provinceName
        ,dbo.getCityNameByCityid(a.cityid) as cityName
        ,k.imei
        ,q.company as sCustomerName
        ,q.id as sCustomerNo
        ,convert(nvarchar(10),isnull(k.imeidate,k.inbeihuodate),121) as instorageTime
        ,a.area_name as storageName
        ,a.area as storageNo
        ,cast(k.inbeihuoprice as int) as purchasePrice,
        p.brandID AS brandId,
        p.product_id AS pid,
        p.cid
        from dbo.product_mkc k WITH (nolock) left join dbo.productinfo p WITH (nolock) on p.ppriceid = k.ppriceid left join dbo.areainfo a WITH (nolock) on
        k.areaid=a.id
        left join dbo.Ok3w_qudao q WITH (nolock) on k.insourceid2=q.id where
        k.kc_check in (3,5,10)
        and k.imei is not null
        and datediff(day,isnull(k.imeidate,k.inbeihuodate),getdate() - #{day})=0
        <if test="brandIds != null and brandIds.size>0">
            AND p.brandID in
            <foreach collection="brandIds" index="index" item="brandId" separator="," open="(" close=")">
                #{brandId}
            </foreach>
        </if>
        <if test="supplierIds != null and supplierIds.size>0">
            AND k.insourceid2 IN
            <foreach collection="supplierIds" index="index" item="supplierId" separator="," open="(" close=")">
                #{supplierId}
            </foreach>
        </if>
        <if test="cids != null and cids.size>0">
            AND p.cid in
            <foreach collection="cids" index="index" item="cid" separator="," open="(" close=")">
                #{cid}
            </foreach>
        </if>

    </select>

    <select id="getDisReturnDataList" resultType="com.jiuji.oa.oacore.goldseed.bo.GoldseedDisReturnDataBo">
         SELECT '中国' AS country,
        k.imei,
        q.company as sName,
        q.id as sId,
        p.brandID AS brandId,
        p.product_id AS pid,
        s.userid as userId,
        ziti.id as customerId,
        ziti.name as customerName,
        dbo.getProvinceName ( ziti.cityid ) AS toProvinceName,
        p.product_name as productName,
        p.product_color as productColor,
        p.ppriceid as ppid,
        p.cid,
        CONVERT(varchar(100), s.returnDate, 23) as returnDate
        FROM
        dbo.product_mkc k WITH (nolock)
        LEFT JOIN dbo.productinfo p WITH (nolock) ON p.ppriceid = k.ppriceid
        LEFT JOIN dbo.areainfo a WITH (nolock) ON k.areaid= a.id
        LEFT JOIN dbo.Ok3w_qudao q WITH (nolock) ON k.insourceid2= q.id
        LEFT JOIN dbo.basket b WITH (nolock) ON b.basket_id = k.basket_id
        AND isnull( b.isdel, 0 ) = 0
        LEFT JOIN dbo.sub s WITH (nolock) ON s.sub_id = b.sub_id
        LEFT JOIN zitidian ziti WITH (nolock) on ziti.userid=s.userid
        WHERE 1=1
				AND k.kc_check=5
        AND k.imei IS NOT NULL
        AND s.sub_check=9
        AND datediff(DAY, s.returnDate, getdate() - #{day}) = 0
        and s.subtype in(13,14)
        and ziti.ispass=1
        <if test="brandIds != null and brandIds.size>0">
            AND p.brandID in
            <foreach collection="brandIds" index="index" item="brandId" separator="," open="(" close=")">
                #{brandId}
            </foreach>
        </if>
        <if test="supplierIds != null and supplierIds.size>0">
            AND k.insourceid2 IN
            <foreach collection="supplierIds" index="index" item="supplierId" separator="," open="(" close=")">
                #{supplierId}
            </foreach>
        </if>
        <if test="cids != null and cids.size>0">
            AND p.cid in
            <foreach collection="cids" index="index" item="cid" separator="," open="(" close=")">
                #{cid}
            </foreach>
        </if>
    </select>

</mapper>
