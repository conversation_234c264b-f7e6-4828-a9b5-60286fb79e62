<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.promocode.dao.NumberCardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.promocode.po.NumberCard">
        <id column="ID" property="id"/>
        <result column="GName" property="GName"/>
        <result column="Total" property="Total"/>
        <result column="CardID" property="CardID"/>
        <result column="State" property="State"/>
        <result column="Isdel" property="Isdel"/>
        <result column="StartTime" property="StartTime"/>
        <result column="EndTime" property="EndTime"/>
        <result column="AddTime" property="AddTime"/>
        <result column="Input" property="Input"/>
        <result column="limit" property="limit"/>
        <result column="userid" property="userid"/>
        <result column="use_count" property="useCount"/>
        <result column="limit1" property="limit1"/>
        <result column="limit2" property="limit2"/>
        <result column="limitprice" property="limitprice"/>
        <result column="limitType" property="limitType"/>
        <result column="limitids" property="limitids"/>
        <result column="areas" property="areas"/>
        <result column="takeMethod" property="takeMethod"/>
        <result column="limitWay" property="limitWay"/>
        <result column="limintClint" property="limintClint"/>
        <result column="ch999_id" property="ch999Id"/>
        <result column="binduid" property="binduid"/>
        <result column="noticetime" property="noticetime"/>
        <result column="isdjq" property="isdjq"/>
        <result column="authorizeid" property="authorizeid"/>
        <result column="other_id" property="otherId"/>
        <result column="areaids" property="areaids"/>
        <result column="seller" property="seller"/>
        <result column="isNoProfit" property="isNoProfit"/>
        <result column="intime" property="intime"/>
        <result column="lastUpdateTime" property="lastUpdateTime"/>
        <result column="lastUpdateUser" property="lastUpdateUser"/>
    </resultMap>
    <insert id="saveBatchNumberCard" useGeneratedKeys="true" keyProperty="id" keyColumn="ID">
        INSERT INTO NumberCard (
            GName,
            Total,
            limitprice,
            State,
            Isdel,
            StartTime,
            EndTime,
            AddTime,
            Input,
            limit,
            limit1,
            limit2,
            limitType,
            limitids,
            areaids,
            takeMethod,
            limitWay,
            limintClint,
            ch999_id,
            userid,
            isdjq,
            authorizeid,
            isNoProfit,
            binduid,
            seller,
            excludePpIds,
            rule_code,
            rule_id
        ) output inserted.CardID
        VALUES
            (
                #{bo.GName},
                #{bo.Total},
                #{bo.limitprice},
                0,
                0,
                #{bo.StartTime},
                #{bo.EndTime},
                getdate( ),
                #{bo.Input},
                #{bo.limits},
                #{bo.limit1},
                #{bo.limit2},
                #{bo.limitType},
                #{bo.limitids},
                #{bo.areas},
                #{bo.takeMethod},
                #{bo.limitWay},
                #{bo.limintClint},
                #{bo.ch999Id},
                #{bo.userid},
                #{bo.isdjq},
                #{bo.authorizeid},
                #{bo.isNoProfit},
                #{bo.binduid},
                #{bo.seller},
                #{bo.excludePpIds},
                #{bo.ruleCode},
                #{bo.ruleId}
            )
    </insert>

    <select id="getByCardId" resultType="com.jiuji.oa.oacore.promocode.po.NumberCard">
        SELECT
            *
        FROM
            NumberCard WITH(nolock)
        WHERE
            Isdel = 0
            AND cardid = #{num}
    </select>
    <select id="getCategy" resultType="com.jiuji.oa.oacore.promocode.bo.CategyBO">
      select c.ID id,c.Name name from dbo.f_category_children(#{collect}) f with(nolock) join dbo.category c with(nolock) on f.ID = c.ID
    </select>
    <select id="getProductIds" resultType="com.jiuji.oa.oacore.promocode.bo.CommonSwitchBO">
        select name from dbo.product with(nolock) where id in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getCardLogsNumByCarId" resultType="java.lang.Integer">
      select count(1) from dbo.cardLogs with(nolock) where cardid=#{id}  and datediff(month,PushTime,getdate())=0
    </select>
    <select id="getInstallServicesRecord" resultType="com.jiuji.oa.oacore.promocode.vo.res.ServiceCodeRes">
        SELECT
            tradedate tradeDate,
            serviceCode code,
            stats_ stats,
            sub_id subId,
            basket_id basketId,
            ppriceid ppriceid,
            useDate
        FROM
            dbo.installServicesRecord with(nolock)
        WHERE
            isdel = 0
            AND userid = #{userId}
        ORDER BY
            stats_
    </select>
    <select id="getCounts" resultType="java.lang.Integer">
        SELECT COUNT
            ( 1 )
        FROM
            (
            SELECT
                1 AS count_
            FROM
                dbo.shouhou_tuihuan t with(nolock)
                LEFT JOIN dbo.ReturnsDetail r with(nolock) ON t.id= r.SHTHID
            WHERE
                t.tuihuan_kind = 7
                AND isnull( t.isdel, 0 ) = 0
                AND r.BasketID = #{basketId} UNION ALL
            SELECT
                1 AS count_
            FROM
                shouhou_yuyue with(nolock)
            WHERE
                basket_id =#{basketId}
            AND ISNULL( isdel, 0 ) = 0
            ) p
    </select>
    <select id="getNumberCard" resultType="com.jiuji.oa.oacore.promocode.po.NumberCard">
        SELECT
        t.*
        FROM
        NumberCard t WITH(nolock)
        WHERE
        <trim prefix="(" suffix=")">
            t.userid in
            <foreach collection="userIds" item="userId" separator="," open="(" close=")" index="index">
                #{userId}
            </foreach>
            <foreach collection="teamIds" item="teamId" open="or t.team_id in (" close=")" separator=","  index="index">
                <if test="teamId>0">
                    #{teamId}
                </if>
            </foreach>
        </trim>
        AND t.isdel = 0
        AND ( t.State = 0 OR limit = 2 )
        AND t.EndTime > DATEADD( DAY,- 90, GETDATE( ) )
        <if test="ch999Id != null and ch999Id != 0">
            and t.ch999_id= #{ch999Id}
        </if>

    </select>
    <select id="getNumberCardQuery" resultType="com.jiuji.oa.oacore.promocode.po.NumberCard">
        SELECT
        t.*
        FROM
        NumberCard t WITH(nolock)
        LEFT JOIN cardLogs c WITH(nolock) ON t.id= c.cardid
        WHERE
        <trim prefix="(" suffix=")">
            t.userid in
            <foreach collection="userIds" item="userId" separator="," open="(" close=")" index="index">
                #{userId}
            </foreach>
            <foreach collection="teamIds" item="teamId" open="or t.team_id in (" close=")" separator=","  index="index">
                <if test="teamId>0">
                    #{teamId}
                </if>
            </foreach>
        </trim>
        and isnull(t.isdel,0) = 0
        AND t.State = 1
        AND CONVERT(varchar(10),c.PushTime,120) <![CDATA[>=]]> DATEADD( DAY,- 30, CONVERT(varchar(10),GETDATE(),120) )
        AND CONVERT(varchar(10),c.PushTime,120) <![CDATA[<=]]> CONVERT(varchar(10),GETDATE(),120) UNION
        SELECT
        t.*
        FROM
        NumberCard t WITH(nolock)
        WHERE
        t.userid in
        <foreach collection="userIds" item="userId" separator="," open="(" close=")" index="index">
            #{userId}
        </foreach>
        and t.isdel = 0
        AND t.EndTime <![CDATA[>]]> DATEADD( DAY,- 30, CONVERT(varchar(10),GETDATE(),120) )
        AND t.EndTime <![CDATA[<]]> CONVERT(varchar(10),GETDATE(),120)
    </select>
    <select id="getTeamCouponByUserID" resultType="com.jiuji.oa.oacore.promocode.po.NumberCard">
        <if test="type">
            SELECT
            t.*
            FROM
            NumberCard t WITH(nolock)
            LEFT JOIN cardLogs c WITH(nolock) ON t.id= c.cardid
            WHERE
            <trim prefix="(" suffix=")">
                t.team_id in
                <foreach collection="teamIds" item="teamId" separator="," open="(" close=")" index="index">
                    #{teamId}
                </foreach>
            </trim>
            and isnull(t.isdel,0) = 0
            AND t.State = 1
            AND CONVERT(varchar(10),c.PushTime,120) <![CDATA[>=]]> DATEADD( DAY,- 30, CONVERT(varchar(10),GETDATE(),120) )
            AND CONVERT(varchar(10),c.PushTime,120) <![CDATA[<=]]> CONVERT(varchar(10),GETDATE(),120) UNION
            SELECT
            t.*
            FROM
            NumberCard t WITH(nolock)
            WHERE
            t.team_id in
            <foreach collection="teamIds" item="teamId" separator="," open="(" close=")" index="index">
                #{teamId}
            </foreach>
            and t.isdel = 0
            AND t.EndTime <![CDATA[>]]> DATEADD( DAY,- 30, CONVERT(varchar(10),GETDATE(),120) )
            AND t.EndTime <![CDATA[<]]> CONVERT(varchar(10),GETDATE(),120)
        </if>
        <if test="!type">
            SELECT
            t.*
            FROM
            NumberCard t WITH(nolock)
            WHERE
            <trim prefix="(" suffix=")">
                t.team_id in
                <foreach collection="teamIds" item="teamId" separator="," open="(" close=")" index="index">
                    #{teamId}
                </foreach>
            </trim>
            AND t.isdel = 0
            AND ( t.State = 0 OR limit = 2 )
            AND t.EndTime > DATEADD( DAY,- 90, GETDATE( ) )
        </if>

    </select>
    <select id="listProductInfo" resultType="com.jiuji.oa.oacore.weborder.bo.ProductInfoBO">
        select ppriceid ppriceid,productid productId, product_name productName, product_color productColor, costprice
        costPrice
        from productinfo with (nolock)
        where ppriceid in
        <foreach collection="ppids" item="ppid" index="index" open="(" close=")" separator=",">
            #{ppid}
        </foreach>
    </select>
    <select id="listTeamIds" resultType="java.lang.Integer">
        select distinct team_id from dbo.BBSXP_Users with(nolock) where ID in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and team_id>0 and team_id is not null
    </select>

</mapper>
