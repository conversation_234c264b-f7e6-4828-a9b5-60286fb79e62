<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.staff.dao.StaffScoreRedpackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.staff.po.StaffScoreRedpack">
        <id column="id" property="id" />
        <result column="kinds" property="kinds" />
        <result column="userId" property="userId" />
        <result column="sendUser" property="sendUser" />
        <result column="dtime" property="dtime" />
        <result column="amount" property="amount" />
        <result column="tips" property="tips" />
        <result column="remark" property="remark" />
        <result column="isReceive" property="isReceive" />
        <result column="receiveTime" property="receiveTime" />
    </resultMap>
    <insert id="insertUserFen">
        insert into dbo.ch999_fen (ch999_id,yanyin,fen,inuser,fendate,labelID,bumen_id,jifenType,score,kinds,jfid)
        values(#{req.userId},#{req.remark},#{req.fen},#{req.inUser},getdate(),'',#{req.bumenId},#{req.jifenType},#{req.score},#{req.kind},#{req.jfId})
    </insert>

    <insert id="insertUserFenWithId" useGeneratedKeys="true" keyProperty="id" parameterType="com.jiuji.oa.oacore.tousu.po.Ch999Fen">
        insert into dbo.ch999_fen (ch999_id,yanyin,fen,inuser,fendate,labelID,bumen_id,jifenType,score,kinds,jfid)
        values(#{entity.ch999Id},#{entity.yanyin},#{entity.fen},#{entity.inuser},getdate(),'',#{entity.bumenId},#{entity.jifenType},#{entity.score},#{entity.kinds},#{entity.jfid})
    </insert>

    <select id="getStaffNewestRedpack" resultType="com.jiuji.oa.oacore.staff.po.StaffScoreRedpack">
        select top 1 * from dbo.StaffScoreRedpack p with(nolock) where isReceive = 0 and userId = #{userId} order by dtime
    </select>
    <select id="getFenByBumenId" resultType="java.lang.Integer">
        select top 1 fen from dbo.ch999_fen with(nolock)
        where bumen_id=#{bumenId}
        <if test="jifenType != null">
            and jifenType=#{jifenType}
        </if>
        <if test="isPositive">
            and fen <![CDATA[>]]> 0
        </if>
        <if test="!isPositive">
            and fen <![CDATA[<]]> 0
        </if>
    </select>

    <select id="getFenAndYanyinByBumenId" resultType="com.jiuji.oa.oacore.staff.vo.FenAndYanyinVO">
        select top 1 fen,yanyin from dbo.ch999_fen with(nolock)
        where bumen_id=#{bumenId}
        <if test="jifenType != null">
            and jifenType=#{jifenType}
        </if>
        <if test="isPositive">
            and fen <![CDATA[>]]> 0
        </if>
        <if test="!isPositive">
            and fen <![CDATA[<]]> 0
        </if>
    </select>
</mapper>
