<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.oacore.label.dao.ProductLabelHisPOMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.oacore.label.ProductLabelHisVO">
        <id column="id" property="id"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="create_userid" property="createBy"/>
        <result column="create_username" property="createName"/>
        <result column="create_time" property="createTime"/>
        <result column="remark" property="remark"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="label" property="label"/>
        <result column="pre_his_id" property="preHisId"/>
        <result column="sold" property="sold"/>
        <result column="pname" property="productName"/>

    </resultMap>

    <resultMap id="ProductResultMap" type="com.jiuji.oa.oacore.label.ProductLabelHisVO">
        <id column="id" property="id"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="createBy" property="createBy"/>
        <result column="createName" property="createName"/>
        <result column="createTime" property="createTime"/>
        <result column="remark" property="remark"/>
        <result column="productId" property="productId"/>
        <result column="productName" property="productName"/>
        <result column="label" property="label"/>
        <result column="preHisId" property="preHisId"/>
        <result column="sold" property="sold"/>
        <result column="productName" property="productName"/>
        <result column="display" property="display"/>

        <collection property="productPriceVoList" javaType="java.util.ArrayList" ofType="com.jiuji.oa.oacore.label.ProductPriceVo"
                    select="com.jiuji.oa.oacore.label.dao.ProductLabelHisPOMapper.listByProductId" column="{productid=productId}"></collection>
    </resultMap>


    <resultMap id="ProductResultMapV2" type="com.jiuji.oa.oacore.label.ProductLabelHisVO">
        <id column="id" property="id"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="createBy" property="createBy"/>
        <result column="createName" property="createName"/>
        <result column="createTime" property="createTime"/>
        <result column="remark" property="remark"/>
        <result column="productId" property="productId"/>
        <result column="productName" property="productName"/>
        <result column="label" property="label"/>
        <result column="preHisId" property="preHisId"/>
        <result column="sold" property="sold"/>
        <result column="productName" property="productName"/>
        <result column="display" property="display"/>
        <result column="xTenant" property="xTenant"/>
        <collection property="productPriceVoList" javaType="java.util.ArrayList" ofType="com.jiuji.oa.oacore.label.ProductPriceVo"
                    select="com.jiuji.oa.oacore.label.dao.ProductLabelHisPOMapper.listByProductIdV2" column="{productid=productId,xTenant=xTenant}"></collection>
    </resultMap>

    <update id="updateProductPriceLabel">
        UPDATE productprice SET pLabel=#{label} WHERE productid = #{productId}
    </update>

    <update id="updateProductPriceLabelByPpid">
        UPDATE productprice
        SET pLabel=#{req.pLabel}
        WHERE ppriceid = #{req.ppid}
    </update>

    <update id="updateProductPriceLabelByPpidAndXTenant">
        UPDATE product_xtenant_info
        SET product_label=#{req.pLabel}
        WHERE ppriceid = #{req.ppid}
          and xtenant = #{xTenant}
    </update>

    <update id="updateProductInfoByPpid">
        UPDATE t_product_info
        SET pLabel=#{req.pLabel}
        WHERE ppriceid = #{req.ppid}
    </update>

    <insert id="saveProductXtenantInfo">
        insert into dbo.product_xtenant_info (ppriceid, product_label, xtenant, create_user, create_time, is_del, update_time)
        values (#{req.ppriceid}, #{req.productLabel}, #{req.xtenant}, #{req.createUser}, #{req.createTime}, #{req.isDel}, #{req.updateTime})
    </insert>


    <sql id="areaWhereSql">
        <if test="req.areaKind != null and req.areaKind != 0">
            and exists(select 1 from dbo.areainfo a with(nolock) where a.id=s.areaid and a.kind1 = #{req.areaKind})
        </if>
        <if test="req.areaIds != null and req.areaIds.size() != 0">
            and exists(select 1 from dbo.areainfo a WITH(NOLOCK) where a.id=s.areaid
            and a.id in
            <foreach collection="req.areaIds" index="index" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
            )
        </if>
    </sql>

    <select id="sumSold" resultType="com.jiuji.oa.oacore.label.bo.ProductStatistics">
        select sum(b.basket_count) counts, p.productid productId from dbo.basket b with(nolock)
        left join dbo.sub s with(nolock) on b.sub_id=s.sub_id
        left join dbo.productinfo p with(nolock) on b.ppriceid=p.ppriceid
        where isnull(b.isdel,0)=0 and s.sub_check=3
        and p.productid in
        <foreach collection="req.productIds" index="index" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
        <if test="req.excludeIds != null and req.excludeIds.size() != 0">
            and s.areaid not in
            <foreach collection="req.excludeIds" index="index" item="excludeIds" open="(" separator="," close=")">
                #{excludeIds}
            </foreach>

        </if>

        <if test="req.startTime != null and req.endTime != null">
            and s.tradeDate1 between #{req.startTime} AND #{req.endTime}
        </if>
        <include refid="areaWhereSql"/>
        group by p.productid
    </select>

    <select id="sumCurrentSold" resultType="com.jiuji.oa.oacore.label.bo.ProductStatistics">
        select sum(b.basket_count) counts, p.productid productId from dbo.basket b with(nolock)
        left join dbo.sub s with(nolock) on b.sub_id=s.sub_id
        left join dbo.productinfo p with(nolock) on b.ppriceid=p.ppriceid
        where p.productid in
        <foreach collection="req.productIds" index="index" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
        <if test="req.excludeIds != null and req.excludeIds.size() != 0">
            and s.areaid not in
            <foreach collection="req.excludeIds" index="index" item="excludeIds" open="(" separator="," close=")">
                #{excludeIds}
            </foreach>

        </if>

        and EXISTS (select 1 from product_label_his ph WITH(NOLOCK) WHERE p.productid = ph.product_id and ph.sold IS NULL and s.tradeDate1 BETWEEN ph.start_time AND ph.end_time)
        and isnull(b.isdel,0)=0 and s.sub_check=3
        <if test="req.startTime != null and req.endTime != null">
            and s.tradeDate1 between #{req.startTime} AND #{req.endTime}
        </if>
        <include refid="areaWhereSql"/>
        group by p.productid
    </select>

    <select id="selectLastByProductId" resultMap="BaseResultMap">
        select top 1 * from product_label_his with (nolock) where product_id = #{productId} and xtenant = #{xTenant}
        and sold is null
        order by create_time desc
    </select>

    <select id="listByPage" resultMap="ProductResultMap">
        select p.display, p.id productId, p.name productName, p.adddate addDate, ph.create_username createName, ph.*
        from dbo.product p with(nolock)
        left join (select * from product_label_his with(nolock) where sold  is null) ph on p.id = ph.product_id
        where (p.ismobile = 0 or p.cid = 597)
        <include refid="labelWhereSql"/>
    </select>

    <select id="listByPageV2" resultMap="ProductResultMapV2">
        select p.display, p.id productId, p.name productName, p.adddate addDate, ph.create_username createName, ph.*
        from dbo.product p with(nolock)
        left join (select * from product_label_his p with(nolock) where sold  is null) ph on p.id = ph.product_id
        where (p.ismobile = 0 or p.cid = 597) and ph.xTenant = #{req.xTenant}
        <include refid="labelWhereSql"/>
    </select>

    <sql id="labelWhereSql">
        <if test="req.productIds != null and req.productIds.size() > 0">
            and p.id in
            <foreach collection="req.productIds" index="index" item="productId" open="(" separator="," close=")">
                #{productId}
            </foreach>
        </if>
        <if test="req.startStartTime != null and req.startEndTime != null">
            AND ph.start_time BETWEEN #{req.startStartTime}
            AND #{req.startEndTime}
        </if>
        <if test="req.endStartTime != null and req.endEndTime != null">
            AND ph.end_time BETWEEN #{req.endStartTime}
            AND #{req.endEndTime}
        </if>
        <if test="req.label != null">
            AND ph.label = #{req.label}
        </if>
        <if test="req.createBy != null and req.createBy != ''">
            AND ph.create_userid = #{req.createBy}
        </if>
        <if test="req.ppid != null and req.ppid != ''">
            AND exists (select 1 from productinfo pi with(nolock) where p.id = pi.product_id and pi.ppriceid1 = #{req.ppid} )
        </if>
        <if test="req.productName != null and req.productName != ''">
            AND p.name like CONCAT('%',#{req.productName},'%')
        </if>
        <if test="req.delay != null and req.delay != 0">
            AND ph.end_time &lt;= #{req.delayDate}
        </if>
        <if test="req.cids !=  null and req.cids != ''">
            and exists( select 1 from f_category_children(#{req.cids}) f where f.id=p.cid )
        </if>
        <if test="req.brandIds != null and req.brandIds.size() != 0">
            and p.brandID in
            <foreach collection="req.brandIds" index="index" item="brandId" open="(" separator="," close=")">
                #{brandId}
            </foreach>
        </if>
        <if test="req.timeStatus != null and req.timeStatus == 1">
            and CONVERT(VARCHAR(10),getdate(),111) > ph.end_time
        </if>
        <if test="req.timeStatus != null and req.timeStatus == 2">
            AND CONVERT(varchar(10),ph.end_time,120) <![CDATA[<=]]> DATEADD( DAY,7, CONVERT(varchar(10),GETDATE(),120) )
            AND CONVERT(varchar(10),ph.end_time,120) >= CONVERT(varchar(10),GETDATE(),120)
        </if>
        <if test="req.timeStatus != null and req.timeStatus == 3">
            AND CONVERT(varchar(10),ph.end_time,120) > DATEADD( DAY,7, CONVERT(varchar(10),GETDATE(),120) )
        </if>
        <if test="req.timeStatus != null and req.timeStatus == 4">
            AND ph.end_time is null
        </if>
        <if test="req.display != null">
            AND p.display = #{req.display}
        </if>
    </sql>

    <select id="listProfits" resultType="com.jiuji.oa.oacore.label.bo.ProductStatistics">
        select sum(basket_count) counts,sum(lirun*basket_count) profits,productid productId from dbo.sub_statistics s WITH(NOLOCK)
        where productid in
        <foreach collection="req.productIds" index="index" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
        and sub_check=3
        <if test="req.startTime != null and req.endTime != null">
            and s.tradeDate1 between #{req.startTime} AND #{req.endTime}
        </if>
        <if test="req.excludeIds != null and req.excludeIds.size() != 0">
            and s.areaid not in
            <foreach collection="req.excludeIds" index="index" item="excludeIds" open="(" separator="," close=")">
                #{excludeIds}
            </foreach>

        </if>

        <include refid="areaWhereSql"/>
        group by productid
    </select>

    <select id="listDisplayCount" resultType="com.jiuji.oa.oacore.label.bo.ProductStatistics">
        select p.productid productId,sum(s.count_) counts from dbo.displayProductInfo s WITH(NOLOCK)
        left join dbo.productinfo p WITH(NOLOCK) on s.ppriceid=p.ppriceid
        where p.productid in
        <foreach collection="req.productIds" index="index" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
        <if test="req.excludeIds != null and req.excludeIds.size() != 0">
            and s.areaid not in
            <foreach collection="req.excludeIds" index="index" item="excludeIds" open="(" separator="," close=")">
                #{excludeIds}
            </foreach>

        </if>

        and stats_=1 and isnull(s.isFlaw,0)=0
        <include refid="areaWhereSql"/>
        group by p.productid
    </select>

    <select id="listKcCount" resultType="com.jiuji.oa.oacore.label.bo.ProductStatistics">
        select aa.productid productId, sum(aa.lcount) counts from (
        select p.productid,s.lcount from dbo.product_kc s WITH(NOLOCK)
        left join dbo.productinfo p WITH(NOLOCK) on s.ppriceid=p.ppriceid
        where p.productid in
        <foreach collection="req.productIds" index="index" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
        <if test="req.excludeIds != null and req.excludeIds.size() != 0">
            and s.areaid not in
            <foreach collection="req.excludeIds" index="index" item="excludeIds" open="(" separator="," close=")">
                #{excludeIds}
            </foreach>

        </if>
        and s.lcount > 0
        <include refid="areaWhereSql"/>
        union all
        select p.productid,b.lcount counts from dbo.diaobo_basket b WITH(NOLOCK)
        left join dbo.diaobo_sub s WITH(NOLOCK) on b.sub_id=s.id
        left join dbo.productinfo p WITH(NOLOCK) on b.ppriceid=p.ppriceid
        where p.productid in
        <foreach collection="req.productIds" index="index" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
        <if test="req.excludeIds != null and req.excludeIds.size() != 0">
            and s.areaid not in
            <foreach collection="req.excludeIds" index="index" item="excludeIds" open="(" separator="," close=")">
                #{excludeIds}
            </foreach>

        </if>
        and s.stats in (2,3,5)
        <include refid="areaWhereSql"/>
        ) aa group by aa.productid
    </select>

    <select id="listAfterRepairCount" resultType="com.jiuji.oa.oacore.label.bo.ProductStatistics">
        select p.productid productId, sum(b.count) counts from dbo.Smallpro s WITH(NOLOCK)
        left join dbo.SmallproBill b WITH(NOLOCK) on s.id=b.smallproID
        left join dbo.productinfo p WITH(NOLOCK) on b.ppriceid=p.ppriceid
        where p.productid in
        <foreach collection="req.productIds" index="index" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
        and s.Kind in (1,2) and s.Stats=1
        <if test="req.startTime != null and req.endTime != null">
            and s.Indate between #{req.startTime} AND #{req.endTime}
        </if>
        <if test="req.excludeIds != null and req.excludeIds.size() != 0">
            and s.areaid not in
            <foreach collection="req.excludeIds" index="index" item="excludeIds" open="(" separator="," close=")">
                #{excludeIds}
            </foreach>

        </if>
        <include refid="areaWhereSql"/>
        group by p.productid
    </select>

    <select id="soldStatistics" resultType="com.jiuji.oa.oacore.label.bo.ProductStatistics">
        select sum(b.basket_count) counts, p.productid productId, CONVERT(VARCHAR(10),s.tradeDate1,111) tradeDate from dbo.basket b with(nolock)
        left join dbo.sub s with(nolock) on b.sub_id=s.sub_id
        left join dbo.productinfo p with(nolock) on b.ppriceid=p.ppriceid
        where isnull(b.isdel,0)=0 and s.sub_check=3
        <if test="req.startTime != null and req.endTime != null">
            and s.tradeDate1 between #{req.startTime} AND #{req.endTime}
        </if>
        <include refid="areaWhereSql"/>
        and p.productid = #{req.productId}
        group by p.productid, CONVERT(VARCHAR(10),s.tradeDate1,111)
        order by CONVERT(VARCHAR(10), s.tradeDate1, 111)
    </select>

    <select id="listByProductId" resultType="com.jiuji.oa.oacore.label.ProductPriceVo">
        select ppriceid ppid, product_color productColor, pLabel
        from productinfo p with(nolock)
        where productid = #{productid}
    </select>

    <select id="listByProductIdV2" resultType="com.jiuji.oa.oacore.label.ProductPriceVo">
        select p.ppriceid ppid, p.product_color productColor,pxi.product_label as pLabel
        from productinfo p with(nolock)
        left join product_xtenant_info pxi with(nolock) on pxi.ppriceid  = p.ppriceid
        and xTenant = #{xTenant}
        where productid = #{productid}
    </select>


    <select id="listCashInCount" resultType="com.jiuji.oa.oacore.label.bo.ProductStatistics">
        select p.productid productId, count(*) counts from shouhou_fanchang sf WITH(NOLOCK)
        left join dbo.SmallproBill b WITH(NOLOCK) on sf.smallproid=b.id
        left join dbo.Smallpro s WITH(NOLOCK) on s.id=b.smallproid
        left join dbo.productinfo p WITH(NOLOCK) on b.ppriceid=p.ppriceid
        where p.productid in
        <foreach collection="req.productIds" index="index" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
        <if test="req.startTime != null and req.endTime != null">
            and s.Indate between #{req.startTime} AND #{req.endTime}
        </if>
        <if test="req.excludeIds != null and req.excludeIds.size() != 0">
            and s.areaid not in
            <foreach collection="req.excludeIds" index="index" item="excludeIds" open="(" separator="," close=")">
                #{excludeIds}
            </foreach>

        </if>
        and sf.rstats = 2
        and s.Kind in (1,2)
        and s.Stats=1
        <include refid="areaWhereSql"/>
        group by p.productid
    </select>

    <select id="selectProductPriceByPpid" resultType="com.jiuji.oa.oacore.label.ProductPriceVo">
        select p.ppriceid ppid, p.product_color productColor, pxi.product_label as pLabel
        from productinfo p with(nolock)
        left join product_xtenant_info pxi with(nolock) on pxi.ppriceid  = p.ppriceid
        and xTenant = #{xTenant}
        where p.ppriceid = #{req.ppid}
    </select>

    <select id="listAfterRepairCountByMoth" resultType="com.jiuji.oa.oacore.label.bo.ProductStatistics">
        select sum(counts) counts,productId,tradeDate from (
        select p.productid productId, b.count counts, CONVERT(VARCHAR ( 7 ), s.Indate, 120) tradeDate from dbo.Smallpro s WITH(NOLOCK)
        left join dbo.SmallproBill b WITH(NOLOCK) on s.id=b.smallproID
        left join dbo.productinfo p WITH(NOLOCK) on b.ppriceid=p.ppriceid
        where p.productid = #{req.productId}
        and s.Kind in (1,2) and s.Stats=1
        <if test="req.startTime != null and req.endTime != null">
            and s.Indate between #{req.startTime} AND #{req.endTime}
        </if>
        ) t group by productId,tradeDate
    </select>
    <select id="listCashInCountByMoth" resultType="com.jiuji.oa.oacore.label.bo.ProductStatistics">
        select sum(counts) counts,productId,tradeDate from (
        select p.productid productId, 1 counts, CONVERT(VARCHAR ( 7 ), s.Indate, 120) tradeDate from dbo.shouhou_fanchang sf WITH(NOLOCK)
        left join dbo.SmallproBill b WITH(NOLOCK) on sf.smallproid=b.id
        left join dbo.Smallpro s WITH(NOLOCK) on s.id=b.smallproid
        left join dbo.productinfo p WITH(NOLOCK) on b.ppriceid=p.ppriceid
        where p.productid = #{req.productId}
        <if test="req.startTime != null and req.endTime != null">
            and s.Indate between #{req.startTime} AND #{req.endTime}
        </if>
        and sf.rstats = 2
        and s.Kind in (1,2)
        and s.Stats=1
        ) t group by productId,tradeDate
    </select>
    <select id="listSoldByMoth" resultType="com.jiuji.oa.oacore.label.bo.ProductStatistics">
        select sum(counts) counts,productId,tradeDate from (
        select b.basket_count counts, p.productid productId,CONVERT(VARCHAR ( 7 ), s.tradeDate1, 120) tradeDate from dbo.basket b with(nolock)
        left join dbo.sub s with(nolock) on b.sub_id=s.sub_id
        left join dbo.productinfo p with(nolock) on b.ppriceid=p.ppriceid
        where isnull(b.isdel,0)=0 and s.sub_check=3
        and p.productid = #{req.productId}
        <if test="req.startTime != null and req.endTime != null">
            and s.tradeDate1 between #{req.startTime} AND #{req.endTime}
        </if>
        ) t group by productId,tradeDate
    </select>

    <select id="initializeSchedule" resultType="java.lang.Integer">
        SELECT distinct product_id from  product_label_his with(nolock)
        where sold is NULL and xTenant is not NULL
        group by product_id HAVING COUNT(1) = 1
    </select>
    <select id="getNotExistProductId" resultType="java.lang.Integer">
        SELECT id
        from product p with(nolock)
        where NOT exists(SELECT 1 from product_label_his h with (nolock) where h.product_id = p.id)
        and isVirtualGoods = 0 and isuse = 1
    </select>


</mapper>
