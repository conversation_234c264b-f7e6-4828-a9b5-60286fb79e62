package com.jiuji.oa.oacore.common.util;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Author:zuofei
 * @Description
 * @Date 2019-04-29
 */
@Component
@Slf4j
public class MainRedisUtil {

    @Autowired
    private RedisTemplate mainRedisTemplate;
    /**
     * 批量删除对应的value
     *
     * @param key
     */
    public void del(String key) {
        mainRedisTemplate.delete(key);
    }

    /**
     * 判断缓存中是否有对应的value
     *
     * @param key
     * @return
     */
    public boolean exists(final String key) {
        return mainRedisTemplate.hasKey(key);
    }

    /**
     * 读取缓存
     *
     * @param key
     * @return
     */
    public String get(final String key) {
        ValueOperations<Serializable, Object> operations = mainRedisTemplate.opsForValue();
        Object result = operations.get(key);
        if(result==null){
            return null;
        }
        return result.toString();
    }
    /**
     * 批量读取缓存
     *
     * @param keys
     * @return
     */
    public List<String> multiGet(List<String> keys) {
        List<String> result = mainRedisTemplate.opsForValue().multiGet(keys);
        return result;
    }
    /**
     * 写入缓存  无过期时间
     *
     * @param key
     * @param value
     * @return
     */
    public boolean set(final String key, Object value) {
        boolean result = false;
        try {
            mainRedisTemplate.opsForValue().set(key, JSON.toJSONString(value));
            result = true;
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return result;
    }
    /**
     * 写入缓存  有过期时间和单位
     *
     * @param key
     * @param value
     * @return
     */
    public boolean set(final String key, Object value, Long expireTime,TimeUnit timeUnit) {
        boolean result = false;
        try {
            mainRedisTemplate.opsForValue().set(key, JSON.toJSONString(value));
            mainRedisTemplate.expire(key, expireTime, timeUnit);
            result = true;
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return result;
    }

    public boolean set(final String key, Object value, Long expireTime) {
        boolean result = false;
        try {
            mainRedisTemplate.opsForValue().set(key, JSON.toJSONString(value));
            mainRedisTemplate.expire(key, expireTime, TimeUnit.SECONDS);
            result = true;
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return result;
    }

    /**
     * redis  hash结构写入
     * @param key  主key
     * @param key2  子key
     * @param value  值
     * @return
     */
    public  boolean hmset(String key,String key2, String value) {
        boolean result = false;
        try {
            mainRedisTemplate.opsForHash().put(key,key2,value);
            result = true;
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return result;
    }

    /**
     * 删除 Redis Hash 结构中的指定字段
     * @param key   主键
     * @param key2  要删除的字段名
     * @return      是否成功删除 (true: 成功, false: 失败)
     */
    public boolean hmdel(String key, String key2) {
        try {
            // 执行删除操作并获取删除的字段数量
            Long deletedCount = mainRedisTemplate.opsForHash().delete(key, key2);

            // 返回删除是否成功（删除数量 > 0 表示成功）
            return deletedCount != null && deletedCount > 0;
        } catch (Exception e) {
            log.error("删除Redis Hash字段失败 | Key: {} | Field: {} | 错误信息: {}",
                    key, key2, e.getMessage(), e);
            return false;
        }
    }

    public  Map<String,String> hmget(String key) {
        Map<String,String> result =null;
        try {
            result=  mainRedisTemplate.opsForHash().entries(key);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return result;
    }


    public boolean isLock(String key){
        return  mainRedisTemplate.opsForValue().setIfAbsent(key, "lock");
    }
}
