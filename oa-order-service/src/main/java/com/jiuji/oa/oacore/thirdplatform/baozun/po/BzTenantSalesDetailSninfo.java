package com.jiuji.oa.oacore.thirdplatform.baozun.po;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jiuji.cloud.after.vo.refund.CardOriginRefundVo;
import com.jiuji.cloud.after.vo.refund.OtherRefundVo;
import com.jiuji.cloud.after.vo.refund.ThirdOriginRefundVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date ${Date} 18:37
 * @Description 宝尊销售详情单sn信息
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "宝尊销售详情单sn信息")
@Data
@TableName(value = "baozun_tenant_sales_detail_sninfo")
@Accessors(chain = true)
public class BzTenantSalesDetailSninfo extends Model<BzTenantSalesDetailSninfo> {
    private static final long serialVersionUID = 6001246806160872675L;
    /**
     * 主键,自增长
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键,自增长")
    private Integer id;

    /**
     * 宝尊订单号
     */
    @TableField(value = "fk_transaction_number")
    @ApiModelProperty(value = "宝尊订单号")
    private String fkTransactionNumber;

    /**
     * 平台订单号
     */
    @TableField(value = "slip_code")
    @ApiModelProperty(value = "平台订单号")
    private String slipCode;

    /**
     * 行号
     */
    @TableField(value = "line_number")
    @ApiModelProperty(value = "行号")
    private String lineNumber;

    /**
     * 商品
     */
    @TableField(value = "upc")
    @ApiModelProperty(value = "商品")
    private String upc;

    /**
     * SN信息
     */
    @TableField(value = "sn")
    @ApiModelProperty(value = "SN信息")
    private String sn;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField(value = "is_del")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDel;

    /**
     * 售后接件id
     */
    @TableField(value = "shouhou_id")
    @ApiModelProperty(value = "售后接件id")
    private Integer shouhouId;

    /**
     * 库存状态
     */
    @ApiModelProperty("库存状态")
    private Integer invStatus;

    /**
     * 售后退换id
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "售后退换id")
    @JSONField(serialize = false,deserialize = false)
    private Integer shouhouTuihuanId;

    /**
     * 当前三方收银记录
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "售后退换id")
    @JSONField(serialize = false,deserialize = false)
    private List<ThirdOriginRefundVo> thirdOriginRefundVos;

    /**
     * 现金收银
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "现金收银")
    @JSONField(serialize = false,deserialize = false)
    private List<OtherRefundVo> refundVos;
    /**
     * 刷卡收银
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "刷卡收银")
    @JSONField(serialize = false,deserialize = false)
    private List<CardOriginRefundVo> cardOriginRefundVoList;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @TableField(exist = false)
    private LocalDateTime baozunTenantSalesDetailSninfoRv;

}
