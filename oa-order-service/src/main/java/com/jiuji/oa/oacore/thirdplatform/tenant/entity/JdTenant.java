package com.jiuji.oa.oacore.thirdplatform.tenant.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jiuji.oa.oacore.common.util.FieldModified;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date 2021/7/22 16:53
 * @Description 京东租户配置实体
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "京东租户配置")
@TableName("jingdong_tenant")
public class JdTenant extends Model<JdTenant> {
    private static final long serialVersionUID = -2821521809095888037L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商户编码
     */
    private String tenantCode;

    /**
     * 商户名称
     */
    @FieldModified(displayName = "商户名称")
    private String tenantName;

    /**
     * 下单会员
     */
    @FieldModified(displayName = "会员id")
    @TableField("UserId")
    private Integer userId;

    /**
     * appKey
     */
    @FieldModified(displayName = "appKey")
    private String appKey;

    /**
     * appSecret
     */
    @FieldModified(displayName = "appSecret")
    private String appSecret;

    /**
     * 平台科目
     */
    @FieldModified(displayName = "平台科目")
    private String platKemu;

    /**
     * 商家科目
     */
    @FieldModified(displayName = "商家科目")
    private String venderKemu;

    /**
     * 启用状态
     */
    @FieldModified(displayName = "启用状态")
    @TableField("is_enable")
    private Boolean isEnable;

    /**
     * 是否自动接单
     */
    @FieldModified(displayName = "自动建单")
    @TableField("orderSwitch")
    private Boolean orderSwitch;

    /**
     * 订单状态更新
     */
    @FieldModified(displayName = "订单状态更新")
    @TableField("order_status")
    private Boolean orderStatus;

    /**
     * 退款科目配置
     */
    @FieldModified(displayName = "退款科目配置")
    @TableField("refund_kemu")
    private String refundKemu;

    @FieldModified(displayName = "京东赠品")
    @TableField("basket_type_gift")
    private String basketTypeGift;


    /**
     * 商品匹配模式
     * 1-商家sku编码
     * 2-京东sku编码
     */
    @FieldModified(displayName = "商品匹配模式")
    @TableField("commodity_model")
    private Integer commodityModel;


    @FieldModified(displayName = "国补收银方式配置")
    @TableField(value = "government_subsidy_kemu", strategy = FieldStrategy.IGNORED)
    private String governmentSubsidyKemu;


}
