package com.jiuji.oa.oacore.yearcardtransfer.vo;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jiuji.oa.oacore.thirdplatform.order.entity.AreaInfo;
import com.jiuji.oa.oacore.yearcardtransfer.entity.AreaGuestBookAccount;
import com.jiuji.oa.oacore.yearcardtransfer.entity.AreaGuestBookAccountLog;
import com.jiuji.oa.train.vo.translate.lesson.Areainfo;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 苹果GB账号配置
 * <AUTHOR>
 */
@Data
public class AreaGuestBookAccountVO extends AreaGuestBookAccount {
    /**
     * 所属门店名称
     */
    private String showAreaName;

    /**
     * 关联门店
     */
    private String affiliatedAreaCodeStr;


    /**
     * 关联门店IdList
     */
    List<Integer> affiliatedAreaIdList;

    private List<AreaGuestBookAccountLog> logList;


}
