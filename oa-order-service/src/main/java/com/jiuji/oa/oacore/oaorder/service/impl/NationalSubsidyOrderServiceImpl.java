package com.jiuji.oa.oacore.oaorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.config.properties.ImageProperties;
import com.jiuji.oa.oacore.common.constant.CommonConstant;
import com.jiuji.oa.oacore.common.constant.GoldseedConstant;
import com.jiuji.oa.oacore.common.constant.RedisKeyConstant;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.util.*;
import com.jiuji.oa.oacore.common.vo.FileResData;
import com.jiuji.oa.oacore.oaorder.enums.AttachmentDownloadNameConfigEnum;
import com.jiuji.oa.oacore.oaorder.enums.NationalSubsidyEnum;
import com.jiuji.oa.oacore.oaorder.enums.SubFlagRecordStateEnum;
import com.jiuji.oa.oacore.oaorder.mapper.NationalSubsidyOrderMapper;
import com.jiuji.oa.oacore.oaorder.mapstruct.NationalSubsidyOrderMapstruct;
import com.jiuji.oa.oacore.oaorder.po.ProductMark;
import com.jiuji.oa.oacore.oaorder.res.CityIDListBO;
import com.jiuji.oa.oacore.oaorder.res.NationalSubsidyEnumRes;
import com.jiuji.oa.oacore.oaorder.service.NationalSubsidyOrderService;
import com.jiuji.oa.oacore.oaorder.service.OaSysConfigService;
import com.jiuji.oa.oacore.oaorder.service.OrderDetailService;
import com.jiuji.oa.oacore.oaorder.vo.req.NationalSubsidyOrderDetailReq;
import com.jiuji.oa.oacore.oaorder.vo.res.*;
import com.jiuji.oa.oacore.sys.bo.AreaBelongsDcHqD1AreaId;
import com.jiuji.oa.oacore.sys.service.AuthConfigService;
import com.jiuji.oa.oacore.thirdplatform.order.service.IAreaInfoService;
import com.jiuji.oa.oacore.tousu.po.Attachments;
import com.jiuji.oa.oacore.tousu.service.AttachmentsService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.sysconfig.vo.SysConfigVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import freemarker.cache.ClassTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.OutputStream;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/12/28 10:29
 * @Description 国补管理
 */
@Slf4j
@Service
public class NationalSubsidyOrderServiceImpl implements NationalSubsidyOrderService {

    public static final String LOCAL_PATH = "/home/<USER>/oa-order-service/nationalSubsidyOrder/";

    public static final Integer CODE = 133000;

    private static final String LOCK_DOWNLOAD_URL = "OA:JAVA:ORDER:LOCK_DOWNLOAD_URL:%s";
    private static final String ORDER_SINGLE = "order_single:%s";
    private static final String ORDER_SINGLE_DAY = "order_single_day:%s";
    private static final int LOCK_MILLISECONDS = 500;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private OrderDetailService orderDetailService;

    private Template template;

    @Resource
    private IAreaInfoService areaInfoService;
    @Resource
    private MainRedisUtil mainRedisUtil;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private NationalSubsidyOrderMapper nationalSubsidyOrderMapper;
    @Resource
    private AttachmentsService attachmentsService;
    @Resource
    private ImageProperties imageProperties;
    @Resource
    private AuthConfigService authConfigService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private OaSysConfigService sysConfigService;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private SysConfigClient sysConfigClient;

    @Override
    public R<Page<NationalSubsidyOrderDetailRes>> queryNationalSubsidyOrderPage(NationalSubsidyOrderDetailReq req) {
        //处理查询数据
        if (CollUtil.isNotEmpty(req.getSubsidyPaymentTypes())) {
            List<String> subsidyPaymentTypes = req.getSubsidyPaymentTypes();
            Map<Boolean, List<Integer>> partitioned = subsidyPaymentTypes.stream()
                    .map(paymentType -> {
                        if (paymentType.startsWith("pos")) {
                            return Integer.parseInt(paymentType.substring(3)); // 提取并转换为 Integer
                        } else {
                            return Integer.parseInt(paymentType);
                        }
                    })
                    .collect(Collectors.partitioningBy(paymentType -> paymentType.toString().startsWith("pos"),
                            Collectors.toList()));
            //获取三方
            req.setSubsidyPaymentThreePartiesTypes(partitioned.get(false));
            //获取pos
            req.setSubsidyPaymentPosTypes(partitioned.get(true));
        }
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        req.setXTenant(oaUserBO.getXTenant());
        //查询是否授权隔离
        Boolean authPart = authConfigService.isAuthPart(oaUserBO);
        req.setAuthPart(authPart);
        req.setAuthorizeId(oaUserBO.getAuthorizeId());

        Page<NationalSubsidyOrderDetailRes> page = new Page<>(req.getCurrent(), req.getSize());
        page.setDesc("s.sub_id");
        //授权隔离
        authPartByUser(req, oaUserBO);
        //处理开票状态
        if(CollUtil.isNotEmpty(req.getInvoiceStatus())){
            if(req.getInvoiceStatus().contains(NationalSubsidyEnum.InvoiceStatusEnum.NOT_SUBMIT.getCode())){
                req.setQueryNoInvoice(true);
                req.getInvoiceStatus().remove(NationalSubsidyEnum.InvoiceStatusEnum.NOT_SUBMIT.getCode());
            }
        }
        // 查询数据
        Page<NationalSubsidyOrderDetailRes> nationalSubsidyOrderDetailResPage = nationalSubsidyOrderMapper.selectNationalSubsidyOrderList(page, req);
        List<NationalSubsidyOrderDetailRes> list = nationalSubsidyOrderDetailResPage.getRecords();
        List<Integer> subIds = list.stream().map(NationalSubsidyOrderDetailRes::getOrderNo).distinct().collect(Collectors.toList());
        Map<Integer, List<NationalSubsidyOrderDetailRes>> nationalSubsidySubsidyCashierMap = new HashMap<>();
        Map<Integer, List<NationalSubsidyOrderDetailRes>> nationalSubsidySubsidyInvoiceMap = new HashMap<>();
        //获取能效数据
        List<EfficiencyRes> efficiencyResList = new ArrayList<>();
        Map<Integer, String> idCardMap = new HashMap<>();
        Map<Integer, String> addressMap = new HashMap<>();
        Map<Integer, String> efficiencyCodeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(subIds)) {
            //发票信息
            List<NationalSubsidyOrderDetailRes> nationalSubsidySubsidyInvoiceList = CommonUtils.bigDataInQuery(NumberConstant.ONE_THOUSAND, subIds, ids ->
                    nationalSubsidyOrderMapper.selectNationalSubsidyInvoiceList(ids));
            if (CollUtil.isNotEmpty(nationalSubsidySubsidyInvoiceList)) {
                nationalSubsidySubsidyInvoiceMap = nationalSubsidySubsidyInvoiceList.stream().collect(Collectors.groupingBy(NationalSubsidyOrderDetailRes::getOrderNo));
            }

            // 国补收银69码信息
            if (Boolean.TRUE.equals(req.getQueryEfficiencyCode())) {
                List<NationalSubsidyEfficiencyCode> efficiencyCodeList = getEfficiencyCode(subIds);
                if (CollUtil.isNotEmpty(efficiencyCodeList)) {
                    // 过滤为空的数据
                    efficiencyCodeList = efficiencyCodeList.stream().filter(e -> StringUtils.isNotBlank(e.getEfficiencyCode())).collect(Collectors.toList());
                }
                if (CollUtil.isNotEmpty(efficiencyCodeList)) {
                    efficiencyCodeMap = efficiencyCodeList.stream().collect(Collectors.toMap(NationalSubsidyEfficiencyCode::getSubId, y -> y.getEfficiencyCode(), (v1, v2) -> v2));
                }
            }

            if (!req.getAttachmentDownloadFlag()) {
                efficiencyResList = nationalSubsidyOrderMapper.getEfficiencyByTGovernmentCategory(oaUserBO.getXTenant());
                //收银信息
                List<NationalSubsidyOrderDetailRes> nationalSubsidySubsidyCashierList = CommonUtils.bigDataInQuery(NumberConstant.ONE_THOUSAND, subIds, ids ->
                        nationalSubsidyOrderMapper.selectNationalSubsidyCashierList(ids));
                if (CollUtil.isNotEmpty(nationalSubsidySubsidyCashierList)) {
                    nationalSubsidySubsidyCashierMap = nationalSubsidySubsidyCashierList.stream().collect(Collectors.groupingBy(NationalSubsidyOrderDetailRes::getOrderNo));
                }
                //身份证信息:查询身份证号,需要bgsf权限
                if (Boolean.TRUE.equals(req.getQueryIdCard()) && oaUserBO.getRank().contains("gbsf")) {
                    //查询身份证信息
                    List<NationalSubsidyOrderDetailRes> idCardList = idCardList(subIds);
                    if (CollUtil.isNotEmpty(idCardList)) {
                        // 过滤身份证号为空的数据
                        idCardList = idCardList.stream().filter(e -> StringUtils.isNotBlank(e.getIdCard())).collect(Collectors.toList());
                    }
                    if (CollUtil.isNotEmpty(idCardList)) {
                        idCardMap = idCardList.stream().collect(Collectors.toMap(NationalSubsidyOrderDetailRes::getOrderNo, y -> y.getIdCard(), (v1, v2) -> v2));
                    }
                }
                // 地址信息
                if (Boolean.TRUE.equals(req.getQueryAddress())) {
                    List<NationalSubsidyOrderDetailRes> addressList = subAddressList(subIds);
                    if (CollUtil.isNotEmpty(addressList)) {
                        // 过滤地址为空的数据
                        addressList = addressList.stream().filter(e -> StringUtils.isNotBlank(e.getAddress())).collect(Collectors.toList());
                    }
                    if (CollUtil.isNotEmpty(addressList)) {
                        addressMap = addressList.stream().collect(Collectors.toMap(NationalSubsidyOrderDetailRes::getOrderNo, y -> y.getAddress(), (v1, v2) -> v2));
                    }
                }
            }
        }
        List<Attachments> attachmentsByLinkIds = attachmentsService.getAttachmentsByLinkIds(subIds, 10);
        attachmentsByLinkIds.forEach(att -> {
            if (StrUtil.isBlank(att.getFilepath())) {
                att.setFilepath(imageProperties.getSelectImgUrl() + "/newstatic/" + att.getFid().replace(",", "/") + att.getExtension());
            }
        });
        Map<Integer, List<Attachments>> attachmentsByLinkIdMap =
                attachmentsByLinkIds.stream().collect(Collectors.groupingBy(Attachments::getLinkedid));


        //处理数据
        for (NationalSubsidyOrderDetailRes nationalSubsidyOrderDetailRes : list) {
            //处理附件
            if (attachmentsByLinkIdMap.containsKey(nationalSubsidyOrderDetailRes.getOrderNo())) {
                List<Attachments> attachments = attachmentsByLinkIdMap.get(nationalSubsidyOrderDetailRes.getOrderNo());
                nationalSubsidyOrderDetailRes.setAttachmentCount(attachments.size());
                nationalSubsidyOrderDetailRes.setOrderAttachmentUrls(attachments);
            }

            //身份证信息
            nationalSubsidyOrderDetailRes.setIdCard(idCardMap.get(nationalSubsidyOrderDetailRes.getOrderNo()));
            // 地址信息
            nationalSubsidyOrderDetailRes.setAddress(addressMap.get(nationalSubsidyOrderDetailRes.getOrderNo()));
            // 国补收银69码信息
            nationalSubsidyOrderDetailRes.setEfficiencyCode(efficiencyCodeMap.get(nationalSubsidyOrderDetailRes.getOrderNo()));

            //处理收银
            if (nationalSubsidySubsidyCashierMap.containsKey(nationalSubsidyOrderDetailRes.getOrderNo())) {
                List<NationalSubsidyOrderDetailRes> resList = nationalSubsidySubsidyCashierMap.get(nationalSubsidyOrderDetailRes.getOrderNo());
                //如果查询出来的数据大于1 单独处理
                if (resList.size() > NumberConstant.ONE) {
                    //todo 暂时不知道怎么处理
                    cashierParameterAssignment(nationalSubsidyOrderDetailRes, resList.get(0));
                } else {
                    cashierParameterAssignment(nationalSubsidyOrderDetailRes, resList.get(0));
                }

            }

            //处理发票
            if (nationalSubsidySubsidyInvoiceMap.containsKey(nationalSubsidyOrderDetailRes.getOrderNo())) {
                List<NationalSubsidyOrderDetailRes> resList = nationalSubsidySubsidyInvoiceMap.get(nationalSubsidyOrderDetailRes.getOrderNo());
                //如果查询出来的数据大于1 单独处理
                if (resList.size() > NumberConstant.ONE) {
                    //todo 暂时不知道怎么处理
                    invoiceParameterAssignment(nationalSubsidyOrderDetailRes, resList.get(0));
                } else {
                    invoiceParameterAssignment(nationalSubsidyOrderDetailRes, resList.get(0));
                }
            }
            //处理序列号
            handleMultipleSerialNumbers(nationalSubsidyOrderDetailRes);

            //处理收银方式
            if (StrUtil.isNotBlank(nationalSubsidyOrderDetailRes.getPosPaymentType())) {
                nationalSubsidyOrderDetailRes.setSubsidyPaymentType(nationalSubsidyOrderDetailRes.getPosPaymentType());
            }
            if (StrUtil.isNotBlank(nationalSubsidyOrderDetailRes.getThreePartiesPaymentType())) {
                nationalSubsidyOrderDetailRes.setSubsidyPaymentType(nationalSubsidyOrderDetailRes.getThreePartiesPaymentType());
            }
            //订单状态转换
            nationalSubsidyOrderDetailRes.setOrderStatusName(EnumUtil.getMessageByCode(NationalSubsidyEnum.OrderStatusEnum.class, nationalSubsidyOrderDetailRes.getOrderStatus()));
            //发票转换
            if(null == nationalSubsidyOrderDetailRes.getInvoiceStatus()){
                nationalSubsidyOrderDetailRes.setInvoiceStatus(NationalSubsidyEnum.InvoiceStatusEnum.NOT_SUBMIT.getCode());
            }
            nationalSubsidyOrderDetailRes.setInvoiceStatusName(EnumUtil.getMessageByCode(NationalSubsidyEnum.InvoiceStatusEnum.class, nationalSubsidyOrderDetailRes.getInvoiceStatus()));
            //开票抬头
            nationalSubsidyOrderDetailRes.setHeaderTypeName(EnumUtil.getMessageByCode(NationalSubsidyEnum.HeaderTypeEnum.class, nationalSubsidyOrderDetailRes.getHeaderType()));
            //国补补贴金额 = 实付金额 * 国补补贴比例 < 国补补贴最高金额
            if (nationalSubsidyOrderDetailRes.getSubsidyRatio() != null && nationalSubsidyOrderDetailRes.getSubsidyRatio().compareTo(BigDecimal.ZERO) > 0) {
                String subsidyPrice = NumberUtil.mul(nationalSubsidyOrderDetailRes.getActualAmount(), NumberUtil.div(nationalSubsidyOrderDetailRes.getSubsidyRatio(), 100))
                        .setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                if (nationalSubsidyOrderDetailRes.getMaxSubsidyPrice().compareTo(BigDecimal.ZERO) > 0
                        && nationalSubsidyOrderDetailRes.getMaxSubsidyPrice().compareTo(new BigDecimal(subsidyPrice)) < 0) {
                    subsidyPrice = nationalSubsidyOrderDetailRes.getMaxSubsidyPrice().stripTrailingZeros().toPlainString();
                }
                nationalSubsidyOrderDetailRes.setSubsidyPrice(subsidyPrice);
            }

            //处理能效
            if (CommonUtil.isNotNullZero(nationalSubsidyOrderDetailRes.getEfficiencyLevelId()) && CollUtil.isNotEmpty(efficiencyResList)) {
                efficiencyResList.stream()
                        .filter(efficiencyRes -> Objects.equals(efficiencyRes.getValue(), nationalSubsidyOrderDetailRes.getEfficiencyLevelId()))
                        .findFirst()
                        .ifPresent(efficiencyRes -> nationalSubsidyOrderDetailRes.setEfficiencyLevel(efficiencyRes.getLabel()));
            }

        }
        return R.success(nationalSubsidyOrderDetailResPage);
    }

    /**
     *
     * @param subIdList
     * @return
     */
    private List<NationalSubsidyOrderDetailRes> idCardList(List<Integer> subIdList) {
        if(CollUtil.isEmpty(subIdList)){
            return Lists.newArrayList();
        }
        if(subIdList.size() <= 1000){
            return nationalSubsidyOrderMapper.getIdCard(subIdList);
        }
        // in查询超过2500 会报错，分多次查询
        List<NationalSubsidyOrderDetailRes> list = Lists.newArrayListWithCapacity(subIdList.size());
        List<List<Integer>> subIds = ListUtil.split(subIdList, 2000);
        for(List<Integer> integerList : subIds){
            List<NationalSubsidyOrderDetailRes> idCardList = nationalSubsidyOrderMapper.getIdCard(integerList);
            if(CollUtil.isNotEmpty(idCardList)){
                list.addAll(idCardList);
            }
        }
        return list;
    }

    /**
     * 国补收银时录入的69码
     * @param subIdList
     * @return
     */
    private List<NationalSubsidyEfficiencyCode> getEfficiencyCode(List<Integer> subIdList) {
        if(CollUtil.isEmpty(subIdList)){
            return Lists.newArrayList();
        }
        return CommonUtils.bigDataInQuery(NumberConstant.PROGRAMMER_NUMBER, subIdList, ids -> nationalSubsidyOrderMapper.getEfficiencyCode(ids));
    }

    private List<NationalSubsidyOrderDetailRes> subAddressList(List<Integer> subIdList) {
        if(CollUtil.isEmpty(subIdList)){
            return Lists.newArrayList();
        }
        List<NationalSubsidyOrderDetailRes> list = Lists.newArrayListWithCapacity(subIdList.size());

        if(subIdList.size() <= 1000){
            list = nationalSubsidyOrderMapper.getSubAddress(subIdList);
        }else {
            // in查询超过2500 会报错，分多次查询
            List<List<Integer>> subIds = ListUtil.split(subIdList, 2000);
            for(List<Integer> integerList : subIds){
                List<NationalSubsidyOrderDetailRes> idCardList = nationalSubsidyOrderMapper.getSubAddress(integerList);
                if(CollUtil.isNotEmpty(idCardList)){
                    list.addAll(idCardList);
                }
            }
        }

        if(CollectionUtils.isEmpty(list)){
            return list;
        }
        //处理地址信息
        for (NationalSubsidyOrderDetailRes detailRes : list){
            if(StringUtils.isBlank(detailRes.getAddress())){
                continue;
            }
            if(null == detailRes.getCityId() || detailRes.getCityId() <= 0){
                continue;
            }
            String cityId = detailRes.getCityId() + "";
            CityIDListBO areas = orderDetailService.getAreaIDByCityID(cityId, 1);
            detailRes.setAddress(areas.getPname() + areas.getZname() + areas.getDname() + detailRes.getAddress());
        }
        return list;
    }
    /**
     * 当地区为空的时候，授权隔离
     */
    private void authPartByUser(NationalSubsidyOrderDetailReq queryCondition, OaUserBO oaUserBO) {
        if (CollUtil.isEmpty(queryCondition.getAreaIdList())) {
            //获取所有门店信息
            List<AreaInfo> areaInfos = Optional.ofNullable(areaInfoClient.listAll()).filter(R::isSuccess).map(R::getData).orElseThrow(() -> new CustomizeException("门店接口请求异常，请稍后重试！"));

            String areaInfoByHq = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.DEPART_MANAGE_AREA)).map(R::getData).orElse(null);
            List<Integer> hqs = CommonUtils.toIntList(areaInfoByHq);
            AreaBelongsDcHqD1AreaId areaBelongsDcHqD1AreaId = authConfigService.getAreaBelongsDcHqD1AreaId(oaUserBO.getAreaId());
            //查询授权地区
            List<Integer> rankByAreaIds = areaInfoService.selectRankAreaId(oaUserBO.getUserId());
            queryCondition.setAreaIdList(rankByAreaIds);

            if (Optional.ofNullable(queryCondition.getAuthPart()).orElse(Boolean.FALSE)) {
                //当用户地区是总部
                if (CollUtil.contains(areaBelongsDcHqD1AreaId.getHqAreaIds(), oaUserBO.getAreaId()) && !CollUtil.contains(hqs, oaUserBO.getAreaId())) {
                    //查询当前授权的所有地区
                    List<Integer> collect = areaInfos.stream().filter(area -> Objects.equals(area.getAuthorizeId(), oaUserBO.getAuthorizeId()))
                            .map(AreaInfo::getId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
                    queryCondition.setAreaIdList(collect);
                } else if (CollUtil.contains(hqs, oaUserBO.getAreaId())) {
                    //查询所有地区的门店
                    queryCondition.setAreaIdList(null);
                }
            } else {
                if (CollUtil.contains(hqs, oaUserBO.getAreaId())) {
                    //查询所有地区的门店
                    queryCondition.setAreaIdList(null);
                }
            }
        }
    }

    private static void cashierParameterAssignment(NationalSubsidyOrderDetailRes nationalSubsidyOrderDetailRes, NationalSubsidyOrderDetailRes nationalSubsidyOrderDetailRes1) {
        nationalSubsidyOrderDetailRes.setContractNo(nationalSubsidyOrderDetailRes1.getContractNo());
        nationalSubsidyOrderDetailRes.setCashierAmount(nationalSubsidyOrderDetailRes1.getCashierAmount());
        nationalSubsidyOrderDetailRes.setSubsidyUnsettledAmount(nationalSubsidyOrderDetailRes1.getSubsidyUnsettledAmount());
        nationalSubsidyOrderDetailRes.setPosPaymentType(nationalSubsidyOrderDetailRes1.getPosPaymentType());
        nationalSubsidyOrderDetailRes.setThreePartiesPaymentType(nationalSubsidyOrderDetailRes1.getThreePartiesPaymentType());
    }

    private void invoiceParameterAssignment(NationalSubsidyOrderDetailRes nationalSubsidyOrderDetailRes, NationalSubsidyOrderDetailRes nationalSubsidyOrderDetailRes1) {
        nationalSubsidyOrderDetailRes.setInvoiceStatus(nationalSubsidyOrderDetailRes1.getInvoiceStatus());
        nationalSubsidyOrderDetailRes.setInvoiceStatusName(nationalSubsidyOrderDetailRes1.getInvoiceStatusName());
        nationalSubsidyOrderDetailRes.setInvoiceSubmitTime(nationalSubsidyOrderDetailRes1.getInvoiceSubmitTime());
        nationalSubsidyOrderDetailRes.setInvoiceOperateTime(nationalSubsidyOrderDetailRes1.getInvoiceOperateTime());
        nationalSubsidyOrderDetailRes.setHeaderType(nationalSubsidyOrderDetailRes1.getHeaderType());
        nationalSubsidyOrderDetailRes.setInvoiceHeader(nationalSubsidyOrderDetailRes1.getInvoiceHeader());
        nationalSubsidyOrderDetailRes.setSellerCompanyName(nationalSubsidyOrderDetailRes1.getSellerCompanyName());
        nationalSubsidyOrderDetailRes.setInvoiceRemark(nationalSubsidyOrderDetailRes1.getInvoiceRemark());
        nationalSubsidyOrderDetailRes.setInvoiceCode(nationalSubsidyOrderDetailRes1.getInvoiceCode());
        nationalSubsidyOrderDetailRes.setInvoiceNumber(nationalSubsidyOrderDetailRes1.getInvoiceNumber());
        //处理发票下载链接
        if (StrUtil.isNotBlank(nationalSubsidyOrderDetailRes1.getInvoiceUrls())) {
            String pdfUrl = imageProperties.getSelectImgUrl() + "/newstatic/" + nationalSubsidyOrderDetailRes1.getInvoiceUrls().replace(",", "/");
            //判断后缀是不是有.pdf 如果没有加上.pdf
            if (!pdfUrl.endsWith(".pdf")) {
                pdfUrl += ".pdf";
            }
            nationalSubsidyOrderDetailRes.setInvoiceUrls(pdfUrl);
        }
    }

    @Override
    public void exportNationalSubsidyOrder(NationalSubsidyOrderDetailReq req, HttpServletResponse response) throws Exception {
        // 导出限制5000条数据
        req.setCurrent(1);
        req.setSize(NumberConstant.FIVE_THOUSAND);
        // 导出要加身份证信息
        req.setQueryIdCard(true);
        // 导出要加地址
        req.setQueryAddress(true);
        // 导出要加收银69码
        req.setQueryEfficiencyCode(true);

        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();

        Page<NationalSubsidyOrderDetailRes> nationalSubsidyOrderDetailResPage = queryNationalSubsidyOrderPage(req).getData();
        List<NationalSubsidyOrderDetailRes> records = nationalSubsidyOrderDetailResPage.getRecords();
        List<NationalSubsidyOrderDetailExport> nationalSubsidyOrderDetailExportList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(records)) {
            nationalSubsidyOrderDetailExportList = NationalSubsidyOrderMapstruct.INSTANCE.toNationalSubsidyOrderDetailExportList(records);
        }

        // 创建字段映射
        Map<String, Function<NationalSubsidyOrderDetailExport, Object>> fieldMapping = new LinkedHashMap<>();
        fieldMapping.put("订单号", NationalSubsidyOrderDetailExport::getOrderNo);
        fieldMapping.put("门店代码", NationalSubsidyOrderDetailExport::getShopCode);
        fieldMapping.put("门店名称", NationalSubsidyOrderDetailExport::getShopName);
        fieldMapping.put("国补收银方式", NationalSubsidyOrderDetailExport::getSubsidyPaymentType);
        fieldMapping.put("合同号", NationalSubsidyOrderDetailExport::getContractNo);
        fieldMapping.put("加单时间", NationalSubsidyOrderDetailExport::getAddTime);
        fieldMapping.put("交易时间", NationalSubsidyOrderDetailExport::getTradeTime);
        fieldMapping.put("订单状态", NationalSubsidyOrderDetailExport::getOrderStatusName);
        fieldMapping.put("联系人", NationalSubsidyOrderDetailExport::getContactPerson);
        fieldMapping.put("联系人电话", NationalSubsidyOrderDetailExport::getContactPhone);

        // 根据权限添加身份证号字段
        if (oaUserBO.getRank().contains("gbsf")) {
            fieldMapping.put("身份证号", NationalSubsidyOrderDetailExport::getIdCard);
        }
        fieldMapping.put("订单收货地址", NationalSubsidyOrderDetailExport::getAddress);
        fieldMapping.put("销售人员", NationalSubsidyOrderDetailExport::getSalesperson);
        fieldMapping.put("商品名称", NationalSubsidyOrderDetailExport::getProductName);
        fieldMapping.put("规格", NationalSubsidyOrderDetailExport::getSpecification);
        fieldMapping.put("sku_id", NationalSubsidyOrderDetailExport::getSkuId);
        fieldMapping.put("分类", NationalSubsidyOrderDetailExport::getCategory);
        fieldMapping.put("品牌", NationalSubsidyOrderDetailExport::getBrand);
        fieldMapping.put("能效", NationalSubsidyOrderDetailExport::getEfficiencyLevel);
        fieldMapping.put("销售价", NationalSubsidyOrderDetailExport::getSalesPrice);
        fieldMapping.put("实付金额", NationalSubsidyOrderDetailExport::getActualAmount);
        fieldMapping.put("国补补贴金额", NationalSubsidyOrderDetailExport::getSubsidyPrice);
        fieldMapping.put("国补收银未结金额", NationalSubsidyOrderDetailExport::getSubsidyUnsettledAmount);
        fieldMapping.put("数量", NationalSubsidyOrderDetailExport::getQuantity);
        fieldMapping.put("序列号", NationalSubsidyOrderDetailExport::getSerialNumber);
        fieldMapping.put("辅助串号", NationalSubsidyOrderDetailExport::getAuxiliarySerialNumber);
        fieldMapping.put("商品SN", NationalSubsidyOrderDetailExport::getProductSN);
        fieldMapping.put("零售通单号", NationalSubsidyOrderDetailExport::getXiaomiBasketId);
        fieldMapping.put("69码", NationalSubsidyOrderDetailExport::getBarCode);
        fieldMapping.put("国补收银69码", NationalSubsidyOrderDetailExport::getEfficiencyCode);
        fieldMapping.put("附件数量", NationalSubsidyOrderDetailExport::getAttachmentCount);
        fieldMapping.put("开票状态", NationalSubsidyOrderDetailExport::getInvoiceStatusName);
        fieldMapping.put("开票提交时间", NationalSubsidyOrderDetailExport::getInvoiceSubmitTime);
        fieldMapping.put("开票完成时间", NationalSubsidyOrderDetailExport::getInvoiceOperateTime);
        fieldMapping.put("抬头类型", NationalSubsidyOrderDetailExport::getHeaderTypeName);
        fieldMapping.put("发票抬头", NationalSubsidyOrderDetailExport::getInvoiceHeader);
        fieldMapping.put("销方公司名称", NationalSubsidyOrderDetailExport::getSellerCompanyName);
        fieldMapping.put("发票备注", NationalSubsidyOrderDetailExport::getInvoiceRemark);
        fieldMapping.put("发票代码", NationalSubsidyOrderDetailExport::getInvoiceCode);
        fieldMapping.put("发票号码", NationalSubsidyOrderDetailExport::getInvoiceNumber);
        fieldMapping.put("订单附件下载链接", NationalSubsidyOrderDetailExport::getOrderAttachmentUrls);
        fieldMapping.put("发票下载链接", NationalSubsidyOrderDetailExport::getInvoiceUrls);

        // 创建表头
        List<List<String>> head = new ArrayList<>(fieldMapping.keySet()
                .stream()
                .map(this::createHeadItem)
                .collect(Collectors.toList()));

        // 创建导出数据
        List<List<Object>> exportData = new ArrayList<>();
        for (NationalSubsidyOrderDetailExport export : nationalSubsidyOrderDetailExportList) {
            List<Object> row = new ArrayList<>();
            for (String key : fieldMapping.keySet()) {
                row.add(fieldMapping.get(key).apply(export));
            }
            exportData.add(row);
        }

        // 写入 Excel
        ExcelWriterBuilder excelWriterBuilder = EasyExcel.write()
                .autoCloseStream(true)
                .file(getOutputStream(response))
                .head(head)
                .excelType(ExcelTypeEnum.XLSX);

        ExcelWriter excelWriter = excelWriterBuilder.build();
        excelWriter.write(exportData, new WriteSheet());
        excelWriter.finish();
    }

    private List<String> createHeadItem(String headName) {
        List<String> item = new ArrayList<>();
        item.add(headName);
        return item;
    }
    private static OutputStream getOutputStream(HttpServletResponse response) throws Exception {
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("data.xls", "UTF-8"));
        return response.getOutputStream();
    }

    /**
     * 处理多个序列号/SN号的情况
     */
    private void handleMultipleSerialNumbers(NationalSubsidyOrderDetailRes res) {
        // 处理序列号
        if (res.getSerialNumber() != null && res.getSerialNumber().contains(",")) {
            String[] serialNumbers = res.getSerialNumber().split(",");
            res.setSerialNumber(String.join("，", serialNumbers));
        }

        // 处理商品SN
        if (res.getProductSN() != null && res.getProductSN().contains(",")) {
            String[] productSNs = res.getProductSN().split(",");
            res.setProductSN(String.join("，", productSNs));
        }
    }

    @Override
    public R<List<NationalSubsidyEnumRes>> getInternalPurchaseEnum() {
        return R.success(NationalSubsidyEnum.toEnumVOList());
    }

    /**
     * 获取统计标签
     *
     * @return
     */
    @Override
    public R<List<TreeNode>> getTagStatistics() {
        //获取标签统计
        List<ProductMark> tagStatistics = nationalSubsidyOrderMapper.getTagStatistics();
        return R.success(buildTree(tagStatistics));
    }

    @Override
    public R<Boolean> isNationalSubsidy(Integer subId) {
        if (CommonUtil.isNotNullZero(subId)) {
            Integer isNationalSubsidy = nationalSubsidyOrderMapper.isNationalSubsidy(subId);
            if (Objects.equals(isNationalSubsidy, NumberConstant.ONE)) {
                return R.success(Boolean.TRUE);
            }
        }
        return R.success(Boolean.FALSE);
    }

    @Override
    public String getDownLoadUrl(NationalSubsidyOrderDetailReq req) {
        String downLoadUrl = "";
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        String orderSingleDay = String.format(ORDER_SINGLE_DAY, LocalDate.now().format(CommonConstant.DATE_FORMATTER_NO_SPACE));
        Map<String, String> existDayMap = mainRedisUtil.hmget(orderSingleDay);
        String dayKey = req.getStartTime().format(CommonConstant.DATE_FORMATTER_NO_SPACE) + ":" + req.getEndTime().format(CommonConstant.DATE_FORMATTER_NO_SPACE);
        if (existDayMap.containsKey(dayKey)){
            String s = existDayMap.get(dayKey);
            mainRedisUtil.hmdel(orderSingleDay,dayKey);
            return s;
        }
        String lockString = String.format(LOCK_DOWNLOAD_URL, oaUserBO.getUserId());
        RLock lock = redissonClient.getLock(lockString);
        try {
            lock.lock(LOCK_MILLISECONDS, TimeUnit.MILLISECONDS);
            int current = 1;
            int pageSize = 1000;
            req.setAttachmentDownloadFlag(true);
            List<NationalSubsidyOrderDetailRes> records = new ArrayList<>();
            while (true) {
                req.setCurrent(current);
                req.setSize(pageSize);
                Page<NationalSubsidyOrderDetailRes> nationalSubsidyOrderDetailResPage = queryNationalSubsidyOrderPage(req).getData();
                List<NationalSubsidyOrderDetailRes> tempRecords = nationalSubsidyOrderDetailResPage.getRecords();
                if (CollectionUtils.isNotEmpty(tempRecords)) {
                    records.addAll(tempRecords);
                } else {
                    break;
                }
                current++;
            }
            if (CollectionUtils.isEmpty(records)) {
                throw new CustomizeException("当前筛选条件无数据，请修改筛选条件重试");
            }
            List<SysConfigVo> sysConfigVoList = sysConfigService.getListByCoode(CODE);
            Map<Integer, String> xtenantNameMap = sysConfigVoList.stream().collect(Collectors.toMap(SysConfigVo::getXtenant, SysConfigVo::getValue));
            String xtenantValue = xtenantNameMap.get(oaUserBO.getXTenant());
            Map<Integer, List<NationalSubsidyOrderDetailRes>> orderListMap = records.stream().collect(Collectors.groupingBy(NationalSubsidyOrderDetailRes::getOrderNo));
            Map<Integer, String> orderAddressMap = new HashMap<>();
            Set<Integer> subIds = orderListMap.keySet();
            List<NationalSubsidyOrderDetailRes> nationalSubsidyOrderDetailRes = CommonUtils.bigDataInQuery(subIds, x -> this.nationalSubsidyOrderMapper.nationalSubShouyinOther(x));
            Map<Integer, String> map = nationalSubsidyOrderDetailRes.stream().collect(Collectors.toMap(NationalSubsidyOrderDetailRes::getOrderNo, NationalSubsidyOrderDetailRes::getSubsidySeqNum, (v1, v2) -> v1));
            GbOrderAttachmentDownLoadRes downLoadRes = new GbOrderAttachmentDownLoadRes();
            List<Integer> shopIdList = records.stream().map(NationalSubsidyOrderDetailRes::getShopId).collect(Collectors.toList());
            Map<Integer, String> areaAddressMap = areaInfoService.getAreaAddressMap(shopIdList);
            List<CompletableFuture<GbOrderAttachmentDownLoadRes.OrderSingle>> futures = new ArrayList<>();
            HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
            String token = MiniFileServerUtil.getToken(request);
            String format = String.format(ORDER_SINGLE, LocalDate.now().format(CommonConstant.DATE_FORMATTER_NO_SPACE));
            Map<String, String> existMap = mainRedisUtil.hmget(format);
            List<GbOrderAttachmentDownLoadRes.OrderSingle> existDownLoadOrderList = new ArrayList<>(orderListMap.size());
            for (Map.Entry<Integer, List<NationalSubsidyOrderDetailRes>> entry : orderListMap.entrySet()) {
                Integer orderId = entry.getKey();
                if (existMap.containsKey(orderId.toString())){
                    String s = existMap.get(orderId.toString());
                    existDownLoadOrderList.add(JSONUtil.toBean(s, GbOrderAttachmentDownLoadRes.OrderSingle.class));
                }else {
                    CompletableFuture<GbOrderAttachmentDownLoadRes.OrderSingle> future = CompletableFuture.supplyAsync(() -> getOrderSingle(token, xtenantValue, orderAddressMap, map, areaAddressMap, entry, orderId)).exceptionally(e -> null);
                    futures.add(future);
                }
            }
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allFutures.join(); // 等待所有任务完成
            // 获取每个 CompletableFuture 的结果
            List<GbOrderAttachmentDownLoadRes.OrderSingle> notExistDownLoadOrderList = new ArrayList<>(orderListMap.size());
            notExistDownLoadOrderList = futures.stream().map(CompletableFuture::join).collect(Collectors.toList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
            List<GbOrderAttachmentDownLoadRes.OrderSingle> downLoadOrderList = new ArrayList<>(orderListMap.size());
            downLoadOrderList.addAll(existDownLoadOrderList);
            downLoadOrderList.addAll(notExistDownLoadOrderList);
            downLoadRes.setList(downLoadOrderList);
            String downLoadKey = LocalDateTimeUtils.formatDateTime(LocalDateTime.now(), LocalDateTimeUtils.DATA_FORMAT_KEJIA) + "_" + oaUserBO.getUserId() + "_" + RandomStringUtils.randomAlphabetic(3);
            stringRedisTemplate.opsForValue().set(RedisKeyConstant.GB_ATTACHEMNT_DOWNLOAD + downLoadKey, JsonUtil.object2Json(downLoadRes), 24, TimeUnit.HOURS);
            downLoadUrl = SaasManagerUtils.getMoaUrl() + "/cloudapi_nc/orderservice/api/nationalSubsidyOrder/v1/gb-attachment-download/" + downLoadKey + "?xservicename=oa-orderservice";
            mainRedisUtil.hmset(orderSingleDay,dayKey,downLoadUrl);
        } catch (Exception e) {
            throw new CustomizeException("后台数据正在导出中，请稍后重试");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return downLoadUrl;
    }

    @NotNull
    private GbOrderAttachmentDownLoadRes.OrderSingle getOrderSingle(String token,String xtenantValue, Map<Integer, String> orderAddressMap, Map<Integer, String> map, Map<Integer, String> areaAddressMap, Map.Entry<Integer, List<NationalSubsidyOrderDetailRes>> entry, Integer orderId) {
        List<NationalSubsidyOrderDetailRes> orderList = entry.getValue();
        NationalSubsidyOrderDetailRes nationalSubsidyOrderDetailResS = orderList.get(0);
        GbOrderAttachmentDownLoadRes.OrderSingle orderSingle = new GbOrderAttachmentDownLoadRes.OrderSingle();
        String messageByCode = SubFlagRecordStateEnum.getMessageByCode(nationalSubsidyOrderDetailResS.getCheckState());
        List<GbOrderAttachmentDownLoadRes.AttachmentSingle> files = new ArrayList<>();
        try {
            String fileDateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            StringBuilder folderName = new StringBuilder();
            if (StringUtils.isNotEmpty(xtenantValue)) {
                String[] split = xtenantValue.split(StringPool.COMMA);
                for (String s : split) {
                    if (AttachmentDownloadNameConfigEnum.ORDERID.getCode().toString().equals(s)) {
                        folderName.append(StringPool.UNDERSCORE).append(orderId);
                    }
                    if (AttachmentDownloadNameConfigEnum.CONTACT.getCode().toString().equals(s)) {
                        folderName.append(StringPool.UNDERSCORE).append(nationalSubsidyOrderDetailResS.getContactPerson());
                    }
                    if (AttachmentDownloadNameConfigEnum.CONTACTPHONE.getCode().toString().equals(s)) {
                        folderName.append(StringPool.UNDERSCORE).append(nationalSubsidyOrderDetailResS.getContactPhone());
                    }
                    if (AttachmentDownloadNameConfigEnum.SN.getCode().toString().equals(s)) {
                        folderName.append(StringPool.UNDERSCORE).append(nationalSubsidyOrderDetailResS.getProductSN());
                    }
                    if (AttachmentDownloadNameConfigEnum.SERIAL_NO.getCode().toString().equals(s)) {
                        String s1 = map.get(orderId);
                        if (StringUtils.isNotEmpty(s1)) {
                            folderName.append(StringPool.UNDERSCORE).append(s1);
                        }
                    }
                }
            }else {
                folderName.append(StringPool.UNDERSCORE).append(orderId);
            }
            String folderNameString = folderName.toString();
            if (folderName.toString().startsWith(StringPool.UNDERSCORE)) {
                folderNameString = folderName.substring(1, folderName.length());
            }
            String folderName1 = folderNameString + "（" + messageByCode + "）";
            if(StringUtils.isNotBlank(folderName1)){
                if(folderName1.contains("\\")){
                    folderName1 = folderName1.replaceAll("\\\\", "反斜杠");
                }
                if(folderName1.contains("/")){
                    folderName1 =folderName1.replaceAll("/", "正斜杠");
                }
                if(folderName1.contains(":")){
                    folderName1 =folderName1.replaceAll(":", "冒号");
                }
                if(folderName1.contains("*")){
                    folderName1 =folderName1.replaceAll("\\*", "星号");
                }
                if(folderName1.contains("?")){
                    folderName1 =folderName1.replaceAll("\\?", "问号");
                }
                if(folderName1.contains("\"")){
                    folderName1 =folderName1.replaceAll("\"", "双引号");
                }
                if(folderName1.contains("<")){
                    folderName1 =folderName1.replaceAll("<", "小于符号");
                }
                if(folderName1.contains(">")){
                    folderName1 =folderName1.replaceAll(">", "大于符号");
                }
                if(folderName1.contains("|")){
                    folderName1 =folderName1.replaceAll("\\|", "竖线");
                }
                if(folderName1.contains("`")){
                    folderName1 =folderName1.replaceAll("`", "");
                }
                if(folderName1.contains("·")){
                    folderName1 =folderName1.replaceAll("·", "");
                }
            }
            orderSingle.setFolderName(folderName1);
            String fileName = orderId + "订单信息";
            char[] orderBytes = this.prepareOrderTxt(orderList, orderAddressMap.getOrDefault(orderId, ""), areaAddressMap.getOrDefault(nationalSubsidyOrderDetailResS.getShopId(), ""));
            File tempFile = FileUtil.createTxtFile(LOCAL_PATH + fileDateStr + "/", fileName, orderBytes);
            //上传到小文件服务器
            FileResData miniFile = MiniFileServerUtil.uploadFileWithToken(token,tempFile, 128);
            files.add(new GbOrderAttachmentDownLoadRes.AttachmentSingle(fileName + GoldseedConstant.FILE_TYPE_TXT, miniFile.getFilePath()));
        } catch (Exception e) {
            log.error("构建国补订单信息上传失败", e);
        }
        List<Attachments> sysAttachmentsVos = nationalSubsidyOrderDetailResS.getOrderAttachmentUrls();
        if (CollectionUtils.isNotEmpty(sysAttachmentsVos)) {
            List<GbOrderAttachmentDownLoadRes.AttachmentSingle> attachmentSingleList = sysAttachmentsVos.stream().map(it -> new GbOrderAttachmentDownLoadRes.AttachmentSingle(it.getFilename(), it.getFilepath())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(attachmentSingleList)) {
                //文件名特殊符号替换
                try {
                    fileNameSpecialSymbolsReplace(attachmentSingleList);
                } catch (Exception e) {
                    log.error("国补订单附件模版名称特殊符号替换失败", e);
                }
            }
            files.addAll(attachmentSingleList);
        }
        String invoiceUrls = nationalSubsidyOrderDetailResS.getInvoiceUrls();
        if (StringUtils.isNotEmpty(invoiceUrls)) {
            String fileName = nationalSubsidyOrderDetailResS.getInvoiceNumber();
            if (StrUtil.isBlank(fileName)) {
                fileName = "发票";
            }
            GbOrderAttachmentDownLoadRes.AttachmentSingle temp = new GbOrderAttachmentDownLoadRes.AttachmentSingle(StrUtil.format("{}.png", fileName), invoiceUrls + ".png");
            files.add(temp);
        }
        orderSingle.setFiles(files);
        String format = String.format(ORDER_SINGLE, LocalDate.now().format(CommonConstant.DATE_FORMATTER_NO_SPACE));
        mainRedisUtil.hmset(format,orderId.toString(),JSONUtil.toJsonStr(orderSingle));
        return orderSingle;
    }


    private void fileNameSpecialSymbolsReplace(List<GbOrderAttachmentDownLoadRes.AttachmentSingle> attachmentSingleList){
        attachmentSingleList.stream().peek(it->{
            if(StringUtils.isNotBlank(it.getName())){
                if(it.getName().contains("\\")){
                    it.setName(it.getName().replaceAll("\\\\", "反斜杠"));
                }
                if(it.getName().contains("/")){
                    it.setName(it.getName().replaceAll("/", "正斜杠"));
                }
                if(it.getName().contains(":")){
                    it.setName(it.getName().replaceAll(":", "冒号"));
                }
                if(it.getName().contains("*")){
                    it.setName(it.getName().replaceAll("\\*", "星号"));
                }
                if(it.getName().contains("?")){
                    it.setName(it.getName().replaceAll("\\?", "问号"));
                }
                if(it.getName().contains("\"")){
                    it.setName(it.getName().replaceAll("\"", "双引号"));
                }
                if(it.getName().contains("<")){
                    it.setName(it.getName().replaceAll("<", "小于符号"));
                }
                if(it.getName().contains(">")){
                    it.setName(it.getName().replaceAll(">", "大于符号"));
                }
                if(it.getName().contains("|")){
                    it.setName(it.getName().replaceAll("\\|", "竖线"));
                }
                if(it.getName().contains("`")){
                    it.setName(it.getName().replaceAll("`", ""));
                }
                if(it.getName().contains("|")){
                    it.setName(it.getName().replaceAll("·", ""));
                }
            }
        }).collect(Collectors.toList());
    }

    @SneakyThrows
    private char[] prepareOrderTxt(List<NationalSubsidyOrderDetailRes> details,String orderAddress,String areaAddress){
        if(template==null){
            initTemplate();
        }
        initTemplate();
        NationalSubsidyOrderDetailRes stateSubsidyOrderDetailVO = details.get(0);
        Map<String,Object> maps=new HashMap<>();
        maps.putAll(JsonUtil.jsonToMap(JsonUtil.object2Json(stateSubsidyOrderDetailVO)));
        maps.put("orderAddress",orderAddress);
        maps.put("areaAddress",areaAddress);
        if(stateSubsidyOrderDetailVO.getAddTime()!=null){
            maps.put("addTime", LocalDateTimeUtils.formatDateTime(stateSubsidyOrderDetailVO.getAddTime(),LocalDateTimeUtils.DATA_TIME_FORMAT));
        }
        if(stateSubsidyOrderDetailVO.getTradeTime()!=null){
            maps.put("orderCompleteTimeStr", LocalDateTimeUtils.formatDateTime(stateSubsidyOrderDetailVO.getTradeTime(),LocalDateTimeUtils.DATA_TIME_FORMAT));
        }
        if(stateSubsidyOrderDetailVO.getInvoiceSubmitTime()!=null){
            maps.put("invoiceCommitTimeStr", LocalDateTimeUtils.formatDateTime(stateSubsidyOrderDetailVO.getInvoiceSubmitTime(),LocalDateTimeUtils.DATA_TIME_FORMAT));
        }
        if(stateSubsidyOrderDetailVO.getInvoiceOperateTime()!=null){
            maps.put("invoiceHandleTimeStr", LocalDateTimeUtils.formatDateTime(stateSubsidyOrderDetailVO.getInvoiceOperateTime(),LocalDateTimeUtils.DATA_TIME_FORMAT));
        }
        List<Map<String, Object>> orderDetails = details.stream().map(it -> JsonUtil.jsonToMap(JsonUtil.object2Json(it))).collect(Collectors.toList());
        maps.put("details",orderDetails);
        StringWriter stringWriter = new StringWriter();
        template.process(maps, stringWriter);
        String orderInfo = stringWriter.toString();
//        log.info("国补附件下载订单信息{}", orderInfo);
        return orderInfo.toCharArray();
    }

    @Override
    public GbOrderAttachmentDownLoadRes getDownLoadData(String key) {
        String redisKey = RedisKeyConstant.GB_ATTACHEMNT_DOWNLOAD + key;
        String redisValue = stringRedisTemplate.opsForValue().get(redisKey);
        GbOrderAttachmentDownLoadRes downLoadRes = null;
        if (StringUtils.isNotEmpty(redisValue)) {
            downLoadRes = JSONUtil.toBean(redisValue, GbOrderAttachmentDownLoadRes.class);
        }
        if (downLoadRes == null) {
            throw new CustomizeException("链接已失效");
        }
        //周子贤说链接用一次就失效
//        stringRedisTemplate.delete(redisKey);
        return downLoadRes;
    }

    @Override
    public R<List<EfficiencyRes>> getEfficiency(String value) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        Integer xTenant = oaUserBO.getXTenant();
        String key = StrUtil.format("com.jiuji.oa.oacore.oaorder.service.impl.NationalSubsidyOrderServiceImpl.getEfficiency.{}",xTenant);
        // 1. 尝试从缓存中获取效率数据
        String cachedEfficiency = stringRedisTemplate.opsForValue().get(key);
        List<EfficiencyRes> efficiency;

        if (cachedEfficiency != null) {
            // 2. 如果缓存存在，直接解析缓存数据
            efficiency = JSON.parseArray(cachedEfficiency, EfficiencyRes.class);
        } else {
            // 3. 如果缓存不存在，查询数据库
            efficiency = new ArrayList<>(nationalSubsidyOrderMapper.getEfficiencyByTGovernmentCategory(xTenant));
            // 4. 将查询结果存入缓存，设置过期时间为10分钟
            stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(efficiency), 10, TimeUnit.MINUTES);
        }

        // 5. 模糊匹配
        List<EfficiencyRes> filteredEfficiency = efficiency.stream()
                .filter(item -> StrUtil.containsIgnoreCase(item.getLabel(), value))
                .limit(20)
                .collect(Collectors.toList());

        return R.success(filteredEfficiency);
    }


    @SneakyThrows
    private void initTemplate(){
        Configuration configuration = new Configuration();
        // 使用 ClassTemplateLoader 直接从类路径加载模板（无需文件系统路径）
        configuration.setTemplateLoader(
                new ClassTemplateLoader(getClass().getClassLoader(), "/template")
        );
        // 直接加载模板文件
        template = configuration.getTemplate("gbOrder.ftl");
    }

    public List<TreeNode> buildTree(List<ProductMark> flatList) {
        Map<Integer, TreeNode> nodeMap = new HashMap<>();
        List<TreeNode> tree = new ArrayList<>();

        // 创建所有节点，并将其放入 map 中
        for (ProductMark mark : flatList) {
            Integer id = mark.getId();
            String label = mark.getProMark();
            TreeNode node = new TreeNode(label, id);
            nodeMap.put(id, node);
        }

        // 建立父子关系
        for (ProductMark mark : flatList) {
            Integer id = mark.getId();
            Integer parentId = mark.getParentId();

            if (parentId == null || parentId == 0) {
                // 如果没有父节点，直接添加到树的顶部
                tree.add(nodeMap.get(id));
            } else {
                TreeNode parentNode = nodeMap.get(parentId);
                if (parentNode != null) {
                    // 否则，添加到相应的父节点
                    parentNode.getChildren().add(nodeMap.get(id));
                }
            }
        }
        return tree;
    }
}
