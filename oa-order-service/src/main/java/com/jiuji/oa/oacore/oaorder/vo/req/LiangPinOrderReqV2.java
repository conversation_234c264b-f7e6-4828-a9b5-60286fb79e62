package com.jiuji.oa.oacore.oaorder.vo.req;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.jiuji.oa.oacore.common.req.PageReq;
import com.jiuji.oa.oacore.mapstruct.CommonStructMapper;
import com.jiuji.tc.utils.fastjson.TrimDeSerializeCodec;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 11:51
 * @Description
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "良品查询条件")
public class LiangPinOrderReqV2 extends PageReq implements Serializable {
    private static final long serialVersionUID = -1083915272193568246L;

    @ApiModelProperty(value = "门店类型，1：自营店，2：加盟店，3：小店")
    private Integer areaKind;

    @ApiModelProperty("地区id")
    private List<String> areaIds;

    @ApiModelProperty("客户类别")
    private Integer customerType;

    @ApiModelProperty("配送方式")
    private Integer distributionType;

    @ApiModelProperty("商品配置id")
    private String productCid;

    @ApiModelProperty("订单状态")
    @JSONField(deserializeUsing = TrimDeSerializeCodec.class)
    private String subCheck;

    @ApiModelProperty("付款方式")
    private List<Integer> subPayList;

    @ApiModelProperty("品牌")
    private List<Integer> brandIdList;

    @ApiModelProperty("查询条件")
    private Integer limintClint;

    @ApiModelProperty("关键字")
    private String key;

    @ApiModelProperty("时间类别")
    private Integer timeType;

    @ApiModelProperty("起始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("是否大小件")
    private Integer sizePiece;

    @ApiModelProperty("门店的授权id")
    private Integer authorizeId;

    @ApiModelProperty("租户id")
    private Integer xtenant;

    @ApiModelProperty("是否授权隔离")
    private boolean authPart;

    @ApiModelProperty("当前门店id")
    private Integer currAreaId;

    @ApiModelProperty("门店类型")
    private Integer areaKind1;

    @ApiModelProperty("是否只查询历史库")
    private Integer isHistory;

    public LiangPinOrderReq toReq() {
        LiangPinOrderReq target = SpringUtil.getBean(CommonStructMapper.class).copyLiangPinOrderReq(this);

        //处理状态字段
        if(StrUtil.equalsIgnoreCase(this.getSubCheck(),"ing")){
            target.setSubCheck(10);
        }else{
            target.setSubCheck(Convert.toInt(this.getSubCheck()));
        }

        return target;
    }
}
