package com.jiuji.oa.oacore.weborder.wrapper;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jiuji.cloud.after.enums.ProductTypeEnum;
import com.jiuji.oa.oacore.assistantOrderInfo.enums.WxStatusEnum;
import com.jiuji.oa.oacore.common.enums.ReservationSubStateEnum;
import com.jiuji.oa.oacore.common.util.EnumUtil;
import com.jiuji.oa.oacore.common.util.StringTrimUtil;
import com.jiuji.oa.oacore.mapstruct.CommonStructMapper;
import com.jiuji.oa.oacore.oaorder.enums.BasketTypeEnum;
import com.jiuji.oa.oacore.oaorder.enums.InvoiceStatusEnum;
import com.jiuji.oa.oacore.oaorder.po.Basket;
import com.jiuji.oa.oacore.oaorder.res.TaxPiAoBO;
import com.jiuji.oa.oacore.oaorder.service.BasketService;
import com.jiuji.oa.oacore.weborder.bo.*;
import com.jiuji.oa.oacore.weborder.vo.*;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @since 2020/4/9
 */
@Slf4j
public class WebOrderWrapper {
    public static XinjiSubVO toXinjiSubVO(XinjiSubBO bo, Map<Integer, AreaInfo> areaInfoMap
            , List<Integer> evaluatedSubId
            , boolean isException
            , Map<Integer, List<XinjiProductBO>> productMap
            , Map<Integer, List<HuiShouSubBO>> huishouSubMap
            , Map<Integer, List<HuiShouProductBO>> huishouProductMap
            , Map<Integer, TaxPiAoBO> taxPiAoMap
            , Map<Integer, List<Ch999UserServiceVO>> exclusiveCustomerServiceMap, Map<Integer, BindSubInfoBo> newSubIdAndLpSubIdMap) {
        XinjiSubVO vo = new XinjiSubVO();
        BeanUtils.copyProperties(bo, vo);
        vo.setIsLock(bo.getIsLock() != null && bo.getIsLock() == 4);
        vo.setIsException(isException);
        vo.setIspj(false);
        vo.setSmallShop(false);

        vo.setSmallShop(vo.getZitidianId() != null && vo.getZitidianId() > 0);
        vo.setIspj(evaluatedSubId != null && evaluatedSubId.contains(vo.getSubId()));

        Optional.ofNullable(areaInfoMap).filter(CollectionUtils::isNotEmpty).map(aim -> aim.get(vo.getAreaId()))
                .ifPresent(areaInfo ->
                        vo.setDepartId(areaInfo.getArea())
                                .setAreaName(areaInfo.getAreaName())
                                .setAreaTel(areaInfo.getCompanyTel1())
                                .setAreaGps(areaInfo.getPosition())
                                .setAreaAddress(areaInfo.getCompanyAddress())
                                .setAreaIsWeb(areaInfo.getIsWeb())
                                .setAllowInvoiced(areaInfo.getTaxPiao())
                );

        if (vo.getDelivery() != null && 1 == vo.getDelivery()) {
            if (vo.getPaisongState() != null) {
                if (vo.getPaisongState() == 2) {
                    vo.setPaisongStatsName("已送出");
                } else if (vo.getPaisongState() == 4) {
                    vo.setPaisongStatsName("已送达");
                } else {
                    vo.setPaisongStatsName("未送出");
                }
            }
        }

        List<XinjiProductBO> xinjiProductBOS = productMap.get(vo.getSubId());
        if (xinjiProductBOS != null) {
            vo.setProductList(xinjiProductBOS.stream()
                    .map(WebOrderWrapper::toXinjiProductVO)
                    .collect(Collectors.toList()));
            vo.setIsJiuJiShield(vo.getProductList().stream().anyMatch(p -> Objects.equals(p.getType(),107)));
        }

        // 设置订单发票状态
        // 发票状态: 未开
        vo.setPiaoStatus(InvoiceStatusEnum.UNFINISHED.getCode());
        if (bo.getSubCheck() == 3) {
            // 交易完成，查询订单关联发票
            TaxPiAoBO taxPiAoBO = taxPiAoMap.get(bo.getSubId());
            if (null != taxPiAoBO) {
                vo.setPiaoid(taxPiAoBO.getPiAoId());
                if (taxPiAoBO.getFlag() == 3 || taxPiAoBO.getFlag() == 4) {
                    // 发票状态: 已开
                    vo.setPiaoStatus(InvoiceStatusEnum.FINISHED.getCode());
                } else if (taxPiAoBO.getFlag().equals(6)) {
                    //红冲状态
                    vo.setPiaoStatus(taxPiAoBO.getFlag());
                } else {
                    // 发票状态: 申请中
                    vo.setPiaoStatus(InvoiceStatusEnum.APPLYING.getCode());
                }
            }
        }

        //设置订单专属客服
        vo.setStaffList(exclusiveCustomerServiceMap.get(bo.getSubId()));
        vo.setBindSubInfo(newSubIdAndLpSubIdMap.get(bo.getSubId()));

        List<HuiShouSubVO> huiShouSubList = handleHuishouSubVO(vo.getSubId(), huishouSubMap, huishouProductMap);
        vo.setHuiShouSubList(huiShouSubList);
        Optional<HuiShouSubVO> newHuiShouOpt = huiShouSubList.stream().findFirst();
        vo.setHuiShouSub(newHuiShouOpt.orElse(null));
        vo.setRecoverSubId(newHuiShouOpt.map(HuiShouSubVO::getRecoverSubId).orElse(null));

        return vo;
    }

    private static List<HuiShouSubVO> handleHuishouSubVO(Integer subId
            , Map<Integer, List<HuiShouSubBO>> huishouSubMap
            , Map<Integer, List<HuiShouProductBO>> huishouProductMap) {

        List<HuiShouSubBO> huiShouSubBOS = huishouSubMap.get(subId);

        if (CollectionUtils.isNotEmpty(huiShouSubBOS)) {
            return huiShouSubBOS.stream().map(huiShouSub -> {
                HuiShouSubVO vo = toHuishouSubVO(huiShouSub);
                List<HuiShouProductBO> huiShouProductBOS = huishouProductMap.get(vo.getRecoverSubId());
                if (CollectionUtils.isNotEmpty(huiShouProductBOS)) {
                    List<HuiShouProductVO> huiShouProductVOS = huiShouProductBOS.stream()
                            .map(WebOrderWrapper::toHuishouProductVO)
                            .collect(Collectors.toList());
                    vo.setHuiShouProductList(huiShouProductVOS);
                }
                return vo;
            }).collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    public static XinjiProductVO toXinjiProductVO(XinjiProductBO bo) {
        XinjiProductVO vo = new XinjiProductVO();
        BeanUtils.copyProperties(bo, vo);
        if (vo.getDelivery() != null && vo.getDelivery() == 1 && vo.getBeiCount() != null) {
            if (vo.getBeiCount().equals(vo.getProductCount())) {
                vo.setProductState("已备货");
            } else {
                vo.setProductState("未备货");
            }
        }
        if (vo.getType() != null && vo.getType() == 11) {
            vo.setScareBuy("抢购");
        } else {
            vo.setScareBuy("非抢购");
        }
        return vo;
    }

    public static HuiShouSubVO toHuishouSubVO(HuiShouSubBO bo) {
        HuiShouSubVO vo = new HuiShouSubVO();
        BeanUtils.copyProperties(bo, vo);
        return vo;
    }

    public static HuiShouProductVO toHuishouProductVO(HuiShouProductBO bo) {
        HuiShouProductVO vo = new HuiShouProductVO();
        BeanUtils.copyProperties(bo, vo);
        return vo;
    }

    public static LiangpinSubVO toLiangpinSubVO(LiangpinSubBO bo, List<AreaInfo> areaInfoList
            , List<Integer> evaluatedSubId
            , boolean isException
            , Map<Integer, List<LiangpinProductBO>> productMap
            , Map<Integer, List<HuiShouSubBO>> huishouSubMap
            , Map<Integer, TaxPiAoBO> taxPiAoMap
            , Map<Integer, List<HuiShouProductBO>> huishouProductMap
            , Map<Integer, List<Ch999UserServiceVO>> exclusiveLiangPinCustomerServiceMap, Map<Integer, BindSubInfoBo> bindSubInfoMap) {
        LiangpinSubVO vo = new LiangpinSubVO();
        BeanUtils.copyProperties(bo, vo);
        vo.setIsException(isException);
        vo.setIspj(evaluatedSubId != null && evaluatedSubId.contains(vo.getSubId()));

        if (CollectionUtils.isNotEmpty(areaInfoList)) {
            Optional<AreaInfo> areaInfoO = areaInfoList.stream()
                    .filter(tmp -> tmp.getId().equals(vo.getAreaId()))
                    .findFirst();
            areaInfoO.ifPresent(areaInfo -> {
                vo.setDepartId(areaInfo.getArea())
                        .setAreaName(areaInfo.getAreaName())
                        .setAreaTel(areaInfo.getCompanyTel1())
                        .setAreaGps(areaInfo.getPosition())
                        .setAreaAddress(areaInfo.getCompanyAddress())
                        .setAreaIsWeb(areaInfo.getIsWeb())
                        .setAllowInvoiced(areaInfo.getTaxPiao())
                ;
            });
        }

        if (vo.getDelivery() == null || 1 != vo.getDelivery()) {
            if (vo.getPaisongState() != null) {
                if (vo.getPaisongState() == 2) {
                    vo.setPaisongStatsName("已送出");
                } else if (vo.getPaisongState() == 4) {
                    vo.setPaisongStatsName("已送达");
                } else {
                    vo.setPaisongStatsName("未送出");
                }
            }
        }

        List<LiangpinProductBO> productBOS = productMap.get(bo.getSubId());
        if (productBOS != null) {
            vo.setProductList(productBOS.stream()
                    .map(WebOrderWrapper::toLiangpinProductVO)
                    .collect(Collectors.toList()));
        }
        List<HuiShouSubVO> huiShouSubList = handleHuishouSubVO(vo.getSubId(), huishouSubMap, huishouProductMap);
        vo.setHuiShouSubList(huiShouSubList);
        Optional<HuiShouSubVO> newHuiShouOpt = huiShouSubList.stream().findFirst();
        vo.setHuiShouSub(newHuiShouOpt.orElse(null));
        vo.setRecoverSubId(newHuiShouOpt.map(HuiShouSubVO::getRecoverSubId).orElse(null));
        // 设置订单发票状态
        // 发票状态: 未开
        vo.setPiaoStatus(InvoiceStatusEnum.UNFINISHED.getCode());
        if (bo.getSubCheck() == 3) {
            // 交易完成，查询订单关联发票
            TaxPiAoBO taxPiAoBO = taxPiAoMap.get(bo.getSubId());
            if (null != taxPiAoBO) {
                vo.setPiaoid(taxPiAoBO.getPiAoId());
                if (taxPiAoBO.getFlag() == 3 || taxPiAoBO.getFlag() == 4) {
                    // 发票状态: 已开
                    vo.setPiaoStatus(InvoiceStatusEnum.FINISHED.getCode());
                }else if(taxPiAoBO.getFlag().equals(6)){
                    vo.setPiaoStatus(taxPiAoBO.getFlag());
                }
                else {
                    // 发票状态: 申请中
                    vo.setPiaoStatus(InvoiceStatusEnum.APPLYING.getCode());
                }
            }
        }
        //设置专属客服
        vo.setStaffList(exclusiveLiangPinCustomerServiceMap.get(vo.getSubId()));
        vo.setBindSubInfo(bindSubInfoMap.get(vo.getSubId()));
        return vo;
    }

    public static LiangpinProductVO toLiangpinProductVO(LiangpinProductBO bo) {
        LiangpinProductVO vo = new LiangpinProductVO();
        BeanUtils.copyProperties(bo, vo);
        if (bo.getDelivery() != null && bo.getDelivery() == 1) {
            if (bo.getKcAreaId() != null && bo.getKcAreaId().equals(bo.getAreaId())
                    && 3 == bo.getMkcCheck()) {
                vo.setProductState("已备货");
            } else {
                vo.setProductState("未备货");
            }
        }

        return vo;
    }

    public static RecoverSubVO toRecoverSubVO(HuiShouSubBO bo, List<AreaInfo> areaInfoList
            , List<Integer> evaluatedSubId, Map<Integer, List<HuiShouProductBO>> huishouProductMap) {
        RecoverSubVO vo = new RecoverSubVO();
        BeanUtils.copyProperties(bo, vo);

        vo.setIspj(evaluatedSubId != null && evaluatedSubId.contains(vo.getRecoverSubId()));

        if (CollectionUtils.isNotEmpty(areaInfoList)) {
            Optional<AreaInfo> areaInfoO = areaInfoList.stream()
                    .filter(tmp -> tmp.getId().equals(vo.getAreaId()))
                    .findFirst();
            areaInfoO.ifPresent(areaInfo -> {
                vo.setDepartId(areaInfo.getArea())
                        .setAreaAddress(areaInfo.getCompanyAddress())
                        .setAreaName(areaInfo.getAreaName())
                        .setAreaTel(areaInfo.getCompanyTel1())
                        .setShowWeb(areaInfo.getIsWeb())
                        .setAreaAddress(areaInfo.getCompanyAddress())
                ;
            });
        }

        List<HuiShouProductBO> huiShouProductBOS = huishouProductMap.get(vo.getRecoverSubId());
        if (CollectionUtils.isNotEmpty(huiShouProductBOS)) {
            List<HuiShouProductVO> huiShouProductVOS = huiShouProductBOS.stream()
                    .map(WebOrderWrapper::toHuishouProductVO)
                    .collect(Collectors.toList());
            vo.setHuiShouProductList(huiShouProductVOS);
        }
        return vo;
    }

    public static AfterServiceSubVO toAfterServiceSubVO(AfterServiceSubBO bo, Map<Integer, AreaInfo> areaInfoMap) {
        AfterServiceSubVO vo = SpringUtil.getBean(CommonStructMapper.class).toAfterServiceSubVO(bo);
        vo.setStateName(WxStatusEnum.getMessageByCode(bo.getState()));

        List<AfterServiceProductVO> productVOS = new ArrayList<>();
        AfterServiceProductVO proVo = new AfterServiceProductVO();
        proVo.setPpid(bo.getPpid())
                .setProductColor(bo.getProductColor())
                .setProductName(bo.getProductName())
                .setPrice(bo.getPrice())
                .setImei(bo.getImei());
        productVOS.add(proVo);
        vo.setProductList(productVOS);

        vo.setTroubleDesc(formatTroubleDesc(bo.getProblem(), bo.getYuyuePpids(), 1));
        if (CollectionUtils.isNotEmpty(areaInfoMap)) {
            vo.setAllowInvoiced(areaInfoMap.getOrDefault(vo.getAreaId(), new AreaInfo()).getTaxPiao());
        }
        ProductTypeEnum productTypeEnum = getProductTypeEnum(bo, false);
        vo.setProductType(productTypeEnum.getCode());
        return vo;
    }

    public static ReservationSubVO toReservationSubVO(ReservationSubBO bo, List<Integer> shouhouIdList) {
        ReservationSubVO vo = new ReservationSubVO();
        BeanUtils.copyProperties(bo, vo);
        vo.setIspj(CollectionUtils.isNotEmpty(shouhouIdList) && shouhouIdList.contains(bo.getShId()));
        vo.setStateName(EnumUtil.getMessageByCode(ReservationSubStateEnum.class, bo.getState()));
        if (StringUtils.isBlank(vo.getStateName())) {
            vo.setStateName("处理中");
        }

        List<ReservationProductVO> productVOS = new ArrayList<>();
        ReservationProductVO proVo = new ReservationProductVO();
        proVo.setSyPpid(bo.getSyPpid())
                .setShPpid(bo.getShPpid())
                .setSyName(bo.getSyName())
                .setSyColor(bo.getSyColor());
        productVOS.add(proVo);
        vo.setProductList(productVOS);

        vo.setTroubleDesc(formatTroubleDesc(bo.getProblem(), bo.getYuyuePpids(), 2));
        return vo;
    }

    public static String formatTroubleDesc(String problem, String yuyuePPids, Integer type) {
        String troubleDesc = "";
        if (type == 1) {
            troubleDesc = problem;
            if (StringUtils.isBlank(troubleDesc)) {
                if (StringUtils.isNotBlank(yuyuePPids)) {

                    try {
                        List<AfterServiceYuyuePpidBO> yuyuePpidBOS = JSON.parseArray(yuyuePPids,
                                AfterServiceYuyuePpidBO.class);
                        if (CollectionUtils.isNotEmpty(yuyuePpidBOS)) {
                            Optional<AfterServiceYuyuePpidBO> ppidBO = yuyuePpidBOS.stream()
                                    .filter(tmp -> StringUtils.isNotBlank(tmp.getTroubleDes()))
                                    .findFirst();
                            if (ppidBO.isPresent()) {
                                troubleDesc = ppidBO.get().getTroubleDes();
                            }
                        }
                    } catch (Exception e) {
                        log.error("序列化异常", e);
                    }
                }
            }
        } else {
            if (StringUtils.isNotBlank(yuyuePPids)) {
                try {
                    List<AfterServiceYuyuePpidBO> yuyuePpidBOS = JSON.parseArray(yuyuePPids,
                            AfterServiceYuyuePpidBO.class);
                    if (CollectionUtils.isNotEmpty(yuyuePpidBOS)) {
                        Optional<AfterServiceYuyuePpidBO> ppidBO = yuyuePpidBOS.stream()
                                .filter(tmp -> StringUtils.isNotBlank(tmp.getTroubleDes()))
                                .findFirst();
                        if (ppidBO.isPresent()) {
                            troubleDesc = ppidBO.get().getTroubleDes();
                        }
                    }
                } catch (Exception e) {
                    log.error("序列化异常", e);
                }
            }
        }
        troubleDesc = formatProblem(troubleDesc);
        if (StringUtils.isBlank(troubleDesc)) {
            return "其他故障";
        }
        if (troubleDesc.length() > 10) {
            troubleDesc = troubleDesc.substring(0, 10) + "...";
        }
        return troubleDesc;
    }

    private static String formatProblem(String problem) {
        if (StringUtils.isBlank(problem)) {
            return problem;
        }
        List<String> trimIn = Arrays.asList(" ", ";", "；", "：", ":");
        String res = StringTrimUtil.trimStart(problem, trimIn);
        if (res.startsWith("维修项目")) {
            res = res.replace("维修项目", "");
            res = StringTrimUtil.trimStart(res, trimIn);
        } else if (res.startsWith("客户描述")) {
            res = res.replace("客户描述", "");
            res = StringTrimUtil.trimStart(res, trimIn);
        }
        Integer maxIndex = Integer.MAX_VALUE;
        Integer tmpIndex = res.indexOf("(");
        if (tmpIndex >= 0 && tmpIndex < maxIndex) {
            maxIndex = tmpIndex;
        }
        tmpIndex = res.indexOf("（");
        if (tmpIndex >= 0 && tmpIndex < maxIndex) {
            maxIndex = tmpIndex;
        }
        tmpIndex = res.indexOf("；");
        if (tmpIndex >= 0 && tmpIndex < maxIndex) {
            maxIndex = tmpIndex;
        }
        if (Integer.MAX_VALUE != maxIndex) {
            return res.substring(0, maxIndex);
        }
        return res;
    }

    public static ProductTypeEnum getProductTypeEnum(AfterServiceSubBO shouhou, boolean isQueryYouPing) {
        //设置商品类型
        ProductTypeEnum productTypeEnum = ProductTypeEnum.NEW_MACHINE;
        if(Boolean.TRUE.equals(Convert.toBool(shouhou.getIshuishou()))){
            //良品
            productTypeEnum = ProductTypeEnum.GOOD_PRODUCT;
        }else if(ObjectUtil.defaultIfNull(shouhou.getSubId(),0)<=0){
            // 外修
            productTypeEnum = ProductTypeEnum.EXTERNAL_REPAIR_MACHINE;
        }else if(isQueryYouPing
                && ObjectUtil.defaultIfNull(shouhou.getBasketId(),0) >0
                && SpringUtil.getBean(BasketService.class)
                .lambdaQuery().eq(Basket::getBasketId, shouhou.getBasketId())
                .eq(Basket::getType, BasketTypeEnum.BASKET_TYPE_DEFECT_MACHINE.getCode()).count()>0){
            //优品
            productTypeEnum = ProductTypeEnum.EXCELLENT_PRODUCT;
        }
        return productTypeEnum;
    }

}
