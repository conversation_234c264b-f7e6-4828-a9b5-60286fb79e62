package com.jiuji.oa.oacore.tousu.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.res.PageRes;
import com.jiuji.oa.oacore.partner.feedback.vo.req.FeedbackSearchReq;
import com.jiuji.oa.oacore.partner.feedback.vo.res.FeedbackRes;
import com.jiuji.oa.oacore.tousu.bo.TousuCategoryRelationBO;
import com.jiuji.oa.oacore.tousu.bo.TousuDepartPointsBO;
import com.jiuji.oa.oacore.tousu.po.*;
import com.jiuji.oa.oacore.tousu.req.TousuPublicReq;
import com.jiuji.oa.oacore.tousu.res.AttachmentsRes;
import com.jiuji.oa.oacore.tousu.res.TousuPublicRes;
import com.jiuji.oa.oacore.tousu.vo.req.CoupleBackReq;
import com.jiuji.oa.oacore.tousu.vo.req.TouSuModelReq;
import com.jiuji.oa.oacore.tousu.vo.req.TousuCategoryReq;
import com.jiuji.oa.oacore.tousu.vo.res.CoupleBackRes;
import com.jiuji.oa.oacore.tousu.vo.res.TouSuDepartRes;
import com.jiuji.oa.oacore.tousu.vo.res.TouSuModelRes;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-22
 */
@Mapper
public interface TousuAreaMapper extends BaseMapper<TouSuModel> {

    List<TousuArea> selectByTouSuId(@Param("id") Integer id);

    List<TouSuTag> getAllTouSuTags();

    List<Integer> getTsIdsByDsc(@Param("processContent") String processContent);

    List<Integer> getTousuIdByUserId(@Param("zeRenRenId") Integer zeRenRenId);

    List<Integer> getTousuIdByUserName(@Param("zeRenRenName") String zeRenRenName);

    @DS("oanew")
    List<TouSuModelRes> touSuResList(@Param("officeName") String officeName, @Param("param") TouSuModelReq param, @Param("startIndex") long startIndex, @Param("pageSize") long pageSize);

    List<TousuArea> getAreaIdsCount(@Param("areaids") List<Integer> areaids);

    List<TousuArea> getAreaIdsCountByDate(@Param("areaids") List<Integer> areaids,@Param("dateNum")Integer dateNum);

    List<TouSuModel> getAreaIdCount(@Param("areaid") Integer areaid);

    List<TsProcess> getTsProcess(@Param("ids") List<Integer> ids);

    TouSuModel getTouSuById(@Param("id") Integer id);

    List<TsProcess> getTsProcessByTsId(@Param("id") Integer id);

    List<TouSuZenRenRen> getTousuZeRenRen(@Param("id") Integer id);

    List<TouSuZenRenRen> getTousuZeRenRens(@Param("ids") List<Integer> ids);

    List<TousuArea> getTousuAreaByTouSuId(@Param("id") Integer id);

    List<TousuCategoryRelationBO> getTouSUCategoryRelation(@Param("id") Integer id);

    List<TouSuZenRenRen> getTousuZeRenRenByUserIds(@Param("ids") List<Integer> ch999ids);

    TouSuZenRenRen getTousuZeRenRenByUserIdsAndTsId(@Param("id") Integer id);

    List<TouSuZenRenRen> getTousuZeRenRenByTousuId(@Param("tousuId") Integer tousuId);

    @DS("oanew")
    List<Integer> getTousuPunish(@Param("id") Integer id);
    List<TousuCategory> getAllTousuCategory(@Param("kind")Integer kind);

    /**
     * 根据标签批量查询投诉分类
     * 
     * @param kindList 标签列表
     * @return 投诉分类
     */
    List<TousuCategory> listAllTousuCategory(@Param("kindList") Collection<Integer> kindList);

    @DS("office")
    default List<TousuCategory> getAllTousuCategory(){
        return getAllTousuCategory(1);
    }
    @DS("office")
    List<TousuCategory> getTousuCategoryByIds(@Param("ids") Collection list);
    @DS("office")
    List<TousuCategory> getAllTousuCategoryWithNoKind();
    @DS("officeWrite")
    Integer addNoticeTousu(@Param("notice") TousuNotice tousuNotice);
    @DS("officeWrite")
    Integer addNewTouSu(TouSuModel ts);
    @DS("officeWrite")
    Integer updateCustomerEndTime(@Param("userName") String userName, @Param("tousuId") Integer tousuId);
    @DS("officeWrite")
    Integer setTousuEndAndinvalid(@Param("id") Integer id);

    @DS(DataSourceConstants.OFFICE_WRITE)
    Integer updateZeRenRenTousuPoints(@Param("zeRenRen") TouSuZenRenRen touSuZenRenRen);

    @DS("officeWrite")
    Integer updateZeRenRen(@Param("zeRenRen") TouSuZenRenRen touSuZenRenRen);

    List<TousuNotice> getTouSuNoticeByTsId(@Param("id") Integer id, @Param("userId") Integer userId);
    @DS("officeWrite")
    void updateTousuStatus(@Param("tousuId") Integer tousuId, @Param("status") Integer status);

    Integer getTousuProcessTimeByTsId(@Param("tousuId") Integer tousuId);
    @DS("officeWrite")
    Integer insertTousuStatus(@Param("tousuId") Integer tousuId, @Param("processTime") LocalDateTime processTime);
    @DS("officeWrite")
    Integer updateTousuProcessTime(@Param("user") String userName, @Param("status") Integer status, @Param("tousuId") Integer tousuId);


    @DS("smallpro_write")
    Integer insertCh99Jifen(@Param("param") Ch999Fen param);

    @DS("oanew")
    Integer queryCh999FenByUserIdAndTouSuId(@Param("touSuId") Integer touSuId, @Param("userId") Integer userId);

    @DS("oanew")
    List<String> getCh999IdByMainRole(@Param("roleId") Integer roleId);
    @DS("officeWrite")
    void saveTouSuZenRenRen(@Param("zeRenRen") TouSuZenRenRen touSuZenRenRen);

    @DS(DataSourceConstants.OFFICE_WRITE)
    void saveTouSuZenRenRenV2(@Param("zeRenRen") TouSuZenRenRen touSuZenRenRen);

    @DS("officeWrite")
    Integer saveTousuAreaByTouSuId(@Param("tousuId") Integer tousuId, @Param("areaId") Integer areaId, @Param("userName") String userName,@Param("departId") Integer departId);

    @DS("oanew")
    Integer getXtenant(Integer userid);

    @DS("oanew")
    List<AttachmentsRes> getTouSuPicByIds(String attIds);

    Integer setTousuAreaByTouSuId(@Param("tousuId") Integer tousuId, @Param("areaId") Integer areaId,
                                  @Param("userName") String userName,@Param("departId") Integer departId);

    List<TousuNotice> getTousuNoticeByTime(@Param("time1") LocalDateTime time1, @Param("time2") LocalDateTime time2);
    @DS("officeWrite")
    void updateStatusById(@Param("id") Integer id, @Param("userId") Integer userId, @Param("tousuId") Integer tousuId);

    List<TsProcess> getTsProcessByTsIdAndUser(@Param("tsId") Integer tousuID, @Param("userName") String toUserName, @Param("time1") LocalDateTime createTime,@Param("time2") LocalDateTime lastTime);

    Integer getFollowTimeoutCountByUserAndTouSuId(@Param("tsId") Integer touSuId, @Param("userId") Integer followUserId,@Param("noticeId") Integer noticeId);
    @DS("officeWrite")
    Integer updateNoticeTousuById(@Param("time") LocalDateTime time, @Param("userId") Integer touser, @Param("tsId") Integer tsId);
    @DS("officeWrite")
    Integer deleteTousuAreaByTouSuId(@Param("tousuId") Integer tousuId, @Param("areaId") Integer areaId);
    @DS("officeWrite")
    Integer deleteTousuCategoryRelation(@Param("cateId") Integer cateId, @Param("tousuId") Integer tousuId, @Param("departId") Integer id);

    /**
     * 获取责任门店、部门
     * @param tsIds
     * @param orderByCateFlag
     * @return
     */
    List<TouSuDepartRes> getTouSuDepartsByTsIds(@Param("tsIds") List<Integer> tsIds, @Param("orderByCateFlag") Boolean orderByCateFlag);

    @DS("officeWrite")
    int deleteZeRenRen(@Param("param") TouSuZenRenRen touSuZenRenRen);

    Integer getTouSuCategoryByName(@Param("name") String name, @Param("id") Integer id);

    Integer getTouSuCategoryById(Integer id);
    @DS("officeWrite")
    Integer saveTouSuCategory(TousuCategoryReq touSuCategory);
    @DS("officeWrite")
    Integer updateTouSuCategory(@Param("param") TousuCategoryReq touSuCategory);

    @DS("oanew")
    Long getListCount(@Param("officeName") String officeName,@Param("param") TouSuModelReq param);

    List<CoupleBackRes> getAllCoupleBack(PageRes<CoupleBackRes> page, @Param("req") CoupleBackReq coupleBackReq,@Param("startIndex") long startIndex, @Param("pageSize") long pageSize);

    Integer coupleBackListCount(@Param("req") CoupleBackReq coupleBackReq);

    Integer getTsProcessByTsIdAndUserAndDsc(Integer tsId, String userName,@Param("createTime") LocalDateTime createTime, @Param("lastTime") LocalDateTime lastTime);

    List<TouSuZenRenRen> getComplaintByDate(@Param("renRs") List<TouSuZenRenRen> renRs);

    /**
     * @description 获取合作伙伴反馈列表
     * <AUTHOR>
     * @date 2021/7/20 16:59
     * @param req
     */
    @DS("oanew")
    List<FeedbackRes> getFeedbackList(@Param(value = "param") FeedbackSearchReq req,
                                       @Param("startIndex") long startIndex, @Param("pageSize") long pageSize);
    @DS("oanew")
    Long getFeedbackListCount(@Param(value = "param") FeedbackSearchReq req);

    /**
     * 获取投诉受理人
     * @param id
     * @return
     */
    String getComplainReceiver(@Param(value = "id") Integer id);

    /**
     * 投诉公示分页统计
     *
     * @param tousuPublicReq 请求参数
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW)
    Integer countTousuPublic(@Param("officeName") String officeName, @Param(value = "param") TousuPublicReq tousuPublicReq);

    /**
     * 投诉公示分页查询
     *
     * @param tousuPublicReq 请求参数
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW)
    List<TousuPublicRes> pageTousuPublic(PageRes<TousuPublicRes> pageRes,
                                         @Param("officeName") String officeName,
                                         @Param(value = "param") TousuPublicReq tousuPublicReq,
                                         @Param("startIndex") long startIndex,
                                         @Param("pageSize") long pageSize);

    /**
     * 根据投诉id批量查询在网站展示的投诉进程
     *
     * @param tsIds 投诉id
     */
    List<TsProcess> listTsProcessWithShowWebByTsIds(@Param(value = "tsIds") List<Integer> tsIds);

    /**
     * 获取部门积分
     *
     * @param bumen
     * @param month
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW)
    TousuDepartPointsBO getTousuDepartPoints(@Param("bumen") String bumen, @Param("type") Integer type, @Param("month") String month);

    /**
     * 更新部门积分
     *
     * @param tousuDepartPointsBO
     * @return
     */
    @DS("oanewWrite")
    int updateTousuDepartFen(@Param("param") TousuDepartPointsBO tousuDepartPointsBO);

    /**
     * 添加部门积分日志
     *
     * @param tousuDepartPointsBO
     * @return
     */
    @DS("oanewWrite")
    int addTousuDepartFenLog(@Param("param") TousuDepartPointsBO tousuDepartPointsBO);

}
