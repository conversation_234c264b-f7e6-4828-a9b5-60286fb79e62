package com.jiuji.oa.oacore;

import com.jiuji.oa.oacore.common.config.properties.ImageProperties;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration;
import org.springframework.boot.context.ApplicationPidFileWriter;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * description: <订单服务启动类>
 * translation: <Order service startup class>
 *
 * <AUTHOR>
 * @date 14:12 2019/11/11
 * @since 1.0.0
 **/
@SpringBootApplication(exclude = {FlywayAutoConfiguration.class})
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@EnableAsync(proxyTargetClass = true)
@EnableDiscoveryClient
@EnableConfigurationProperties({ImageProperties.class})
@EnableTransactionManagement
@ComponentScan(value = {"com.jiuji.tc.utils.common", "com.jiuji.oa.oacore","cn.hutool.extra.spring"})
@EnableFeignClients(basePackages = "com.jiuji")
//@ServletComponentScan
public class OrderServiceApplication {

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(OrderServiceApplication.class);
        app.addListeners(new ApplicationPidFileWriter());
        app.run(args);
    }

    @Bean
    MeterRegistryCustomizer<MeterRegistry> configurer(@Value("${spring.application.name}") String applicationName) {
        return registry -> registry.config().commonTags("application", applicationName);
    }

}
