package com.jiuji.oa.oacore.oaorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alicp.jetcache.anno.CacheType;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.cloud.product.service.PriceCloud;
import com.jiuji.cloud.product.vo.request.ClientPriceReqVO;
import com.jiuji.oa.oacore.brand.dji.vo.ProductInfoVO;
import com.jiuji.oa.oacore.cloud.PickCloud;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.enums.MTableInfoEnum;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.oaorder.bo.ProductIsMobileInfoBO;
import com.jiuji.oa.oacore.oaorder.bo.ProductSimpleBO;
import com.jiuji.oa.oacore.oaorder.client.vo.Result;
import com.jiuji.oa.oacore.oaorder.dao.BasketMapper;
import com.jiuji.oa.oacore.oaorder.dao.ProductinfoMapper;
import com.jiuji.oa.oacore.oaorder.dao.SubAddressMapper;
import com.jiuji.oa.oacore.oaorder.enums.SubCheckEnum;
import com.jiuji.oa.oacore.oaorder.po.ProductMkc;
import com.jiuji.oa.oacore.oaorder.po.Productinfo;
import com.jiuji.oa.oacore.oaorder.res.ProductInfoSimpleVo;
import com.jiuji.oa.oacore.oaorder.res.SearchProductInfoVO;
import com.jiuji.oa.oacore.oaorder.service.CategoryService;
import com.jiuji.oa.oacore.oaorder.service.ProductMkcService;
import com.jiuji.oa.oacore.oaorder.service.ProductinfoService;
import com.jiuji.oa.oacore.oaorder.vo.res.OutboundOrderRes;
import com.jiuji.oa.oacore.oaorder.vo.res.ProductServiceType;
import com.jiuji.oa.oacore.promocode.bo.CommonSwitchBO;
import com.jiuji.oa.oacore.subRecommended.vo.bo.PriceData;
import com.jiuji.oa.oacore.subRecommended.vo.res.ProductBaseInfo;
import com.jiuji.oa.oacore.thirdplatform.order.entity.AreaInfo;
import com.jiuji.oa.oacore.thirdplatform.order.service.IAreaInfoService;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.oacore.weborder.req.DiaoboStockInfoReqVO;
import com.jiuji.oa.oacore.weborder.req.GiftStockReqVO;
import com.jiuji.oa.oacore.weborder.req.WebStockInfoReqVO;
import com.jiuji.oa.oacore.weborder.req.WebStockPriceReqVO;
import com.jiuji.oa.oacore.weborder.res.DiaoboStockInfoResVO;
import com.jiuji.oa.oacore.weborder.res.GiftStockResVO;
import com.jiuji.oa.oacore.weborder.res.WebStockInfoResVO;
import com.jiuji.oa.oacore.weborder.res.WebStockPriceResVO;
import com.jiuji.oa.oacore.weborder.vo.req.StockInfoReqVO;
import com.jiuji.oa.oacore.weborder.vo.res.CheckStockInfoResVO;
import com.jiuji.oa.oacore.weborder.vo.res.StockCountVO;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.xtenant.Namespaces;
import jodd.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.concurrent.TimeUnit;
import com.alicp.jetcache.anno.Cached;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-06
 */
@DS("oanew")
@Service
@Slf4j
public class ProductinfoServiceImpl extends ServiceImpl<ProductinfoMapper, Productinfo> implements ProductinfoService {

    @Resource
    private PickCloud pickCloud;

    @Resource
    private SubAddressMapper subAddressMapper;

    @Resource
    private BasketMapper basketMapper;
    @Resource
    private IAreaInfoService areaInfoService;

    @Resource
    private ProductMkcService productMkcService;
    /**
     * 严选体系
     */
    private final static Integer StrictSelectionSystem = 75;

    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;

    @Resource
    @Lazy
    private ProductinfoService productinfoService;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private PriceCloud priceCloud;


    private static final Integer DIY_PRODUCT_ID = 47113;

    private static final Integer ANNUAL_PACKAGE_TYPE = 108;

    @Resource
    private SmsService smsService;

    @Resource
    private CategoryService categoryService;

    /**
     * 判断商品是否为年包
     * @return
     */
    @Override
    public List<Integer> annualPackageList() {
        List<Integer> list = new ArrayList<>();
        R<String> res = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.MOA_URL, (int) Namespaces.get());
        log.warn("年包商品查询获取域名结果：{}，传入参数：{}", JSONUtil.toJsonStr(res), SysConfigConstant.MOA_URL+":"+Namespaces.get());
        if(res.isSuccess()){
            String host = res.getData();
            String url= host+"/web/api/product/opening/getAllServiceConfig/v1?t=";
            log.warn("获取年包商品传入参数：{}",url);
            String result = HttpUtil.get(url);
            log.warn("获取年包商品返回结果：{}",result);
            R selectResult = JSONUtil.toBean(JSONUtil.toJsonStr(result), R.class);
            if(selectResult.isSuccess()){
                Object data = selectResult.getData();
                List<ProductServiceType> serviceTypes = JSONUtil.toList(JSONUtil.parseArray(data), ProductServiceType.class);
                list = serviceTypes.stream()
                        .filter(item->ANNUAL_PACKAGE_TYPE.equals(item.getServiceType()))
                        .map(ProductServiceType::getPpid)
                        .collect(Collectors.toList());
            } else {
                throw new CustomizeException("获取年包商品失败:"+Optional.ofNullable(selectResult.getUserMsg()).orElse(selectResult.getMsg()));
            }
        }
        //DIY保护壳年包处理
        list.addAll(this.baseMapper.selectPpidByProductId(DIY_PRODUCT_ID));
        return list;
    }

    /**
     *  调用主站的接口可以实现逻辑
     *  全网抢购价>区域价>会员价，即优先取全网抢购价，若无全网抢购价则取区域价，如无区域价则取会员价；
     *  获取区域价时，根据配置人员所属地区所在城市来获取对应的区域价；
     * @param productBaseInfoList
     * @param areaId -- 首次配置创建时候点归宿地
     * @return
     */
    private Map<Integer,BigDecimal> getOriginalPriceMap(List<Integer> ppidList,Integer areaId){
       // OaUserBO userBO = Optional.ofNullable(abstractCurrentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("获取当前人员信息失败"));
        Map<Integer,BigDecimal> map = new HashMap<>();
        if(CollectionUtils.isEmpty(ppidList)){
            return map;
        }
      //  Integer areaId = Optional.ofNullable(userBO.getArea1id()).orElseThrow(() -> new CustomizeException("获取当前人员信息地区失效"));
        AreaInfo area = Optional.ofNullable(areaInfoService.getById(areaId)).orElseThrow(() -> new CustomizeException("配置归属地区不存在"));
        Integer cityId = areaInfoService.conversionCityId(Optional.ofNullable(area.getCityid()).orElseThrow(() -> new CustomizeException("地区城市信息为空")));
        // 调用主站接口获取价格信息
        ClientPriceReqVO clientPriceReqVO = new ClientPriceReqVO();
        clientPriceReqVO.setAreaCode(cityId);
        clientPriceReqVO.setPpidList(ppidList);
        return getPriceByWeb(clientPriceReqVO);
    }

    /**
     * 调用主站接口获取价格信息
     * @param clientPriceReqVO
     * @return
     */
    @Override
    public Map<Integer, BigDecimal> getPriceByWeb(ClientPriceReqVO clientPriceReqVO){
        R<Map<Integer, BigDecimal>> result = new R<>();
        try {
            clientPriceReqVO.setNeedLimitBuyPrice(Boolean.TRUE);
            result = priceCloud.batchGetClientPrice(clientPriceReqVO, XtenantEnum.getXtenant());
            log.warn("调用主站获取价格信息失败传入参数：{}，xtenant：{}，返回结果：{}", JSONUtil.toJsonStr(clientPriceReqVO),XtenantEnum.getXtenant(),JSONUtil.toJsonStr(result));
        } catch (Exception e){
            RRExceptionHandler.logError("调用主站获取价格信息失败传！", Dict.create().set("clientPriceReqVO",clientPriceReqVO).set("xtenant", XtenantEnum.getXtenant()), e, smsService::sendOaMsgTo9JiMan);
        }
        if(!result.isSuccess()){
            throw new CustomizeException("调用主站获取价格信息失败:"+Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
        }
        return result.getData();
    }

    /**
     *
     * @param ppids
     * @param areaId 归属地
     * @return
     */
    @Override
    public Map<Integer,ProductBaseInfo> selectProductBaseInfoMap(List<Integer> ppids,Integer areaId) {
        return selectProductBaseInfoMap(ppids, areaId, true);
    }
    
    /**
     *
     * @param ppids
     * @param areaId 归属地
     * @param useCache 是否使用缓存版本的annualPackageList
     * @return
     */
    @Override
    public Map<Integer,ProductBaseInfo> selectProductBaseInfoMap(List<Integer> ppids,Integer areaId, boolean useCache) {
        Map<Integer, ProductBaseInfo> map = new HashMap<>();
        if(CollectionUtils.isEmpty(ppids)){
            return map;
        }
        ppids.removeIf(Objects::isNull);
        Map<Integer, ProductSimpleBO> productMapByPpidsNew = getProductMapByPpidsNew(ppids);
        if(CollectionUtils.isEmpty(productMapByPpidsNew)){
            return map;
        }
        //获取原价
        Map<Integer, BigDecimal> originalPriceMap = getOriginalPriceMap(ppids,areaId);
        List<ProductBaseInfo> productBaseInfoList = productMapByPpidsNew.values().stream().map(item -> {
            ProductBaseInfo productBaseInfo = new ProductBaseInfo();
            productBaseInfo.setProductColor(item.getProductColor())
                    .setProductName(item.getProductName())
                    .setCid(item.getCid())
                    .setProductId(item.getPid())
                    .setIsMobile(item.getIsMobile())
                    .setOriginalPrice(originalPriceMap.getOrDefault(item.getPpid(),item.getMemberprice()))
                    .setPpid(item.getPpid());
            return productBaseInfo;
        }).collect(Collectors.toList());
        R<String> result = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST);
        if(result.isSuccess()){
            String host = result.getData();
            String ppidStr = ppids.stream().filter(ObjectUtil::isNotNull).map(item -> item.toString()).collect(Collectors.joining(","));
            String url = String.format(host + "/ajax.ashx?act=getinprice&ppriceid=%s&kind1=1", ppidStr);
            log.warn("调用inwcf接口获取商品信息传入参数：{}",url);
            String data = HttpUtil.get(url);
            log.warn("调用inwcf接口获取商品信息返回结果：{}",data);
            List<PriceData> priceDataList = JSONUtil.toList(JSONUtil.toJsonStr(data), PriceData.class);
            if(CollectionUtils.isNotEmpty(priceDataList)){
                Map<Integer, PriceData> priceDataMap = priceDataList.stream().collect(Collectors.toMap(PriceData::getPpriceid, Function.identity()));
                productBaseInfoList.forEach(item -> {
                    PriceData priceData = priceDataMap.getOrDefault(item.getPpid(), new PriceData());
                    item.setProductCount(priceData.getLcount())
                            .setCostPrice(priceData.getInbeihuoprice());
                });
            }
            productBaseInfoList.forEach(item->{
                BigDecimal costPrice = item.getCostPrice();
                if(ObjectUtil.isNull(costPrice) || costPrice.compareTo(BigDecimal.ZERO) == 0){
                    item.setCostPrice(productMapByPpidsNew.getOrDefault(item.getPpid(), new ProductSimpleBO()).getCostPrice());
                }
            });
        }else{
            throw new CustomizeException(Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
        }
        //年包ppid
        List<Integer> annualPackageList;
        if (useCache) {
            try {
                annualPackageList = productinfoService.annualPackageListWithCache();
                // 如果缓存返回空数据，则直接调用原始方法
                if (CollUtil.isEmpty(annualPackageList)) {
                    annualPackageList = productinfoService.annualPackageList();
                }
            } catch (Exception e) {
                // 如果缓存方法调用出错，则直接调用原始方法
                log.error("从缓存获取年包列表失败，将直接调用原始方法", e);
                annualPackageList = productinfoService.annualPackageList();
            }
        } else {
            annualPackageList = productinfoService.annualPackageList();
        }
        //获取壳膜分类
        List<Integer> caseOrFilmCidList = categoryService.selectCaseOrFilmCid();
        List<Integer> finalAnnualPackageList = annualPackageList;
        return productBaseInfoList.stream().peek(item->{
            item.setIsAnnualPackage(finalAnnualPackageList.contains(item.getPpid()));
            item.setIsCaseOrFilm(caseOrFilmCidList.contains(item.getCid()));
        }).collect(Collectors.toMap(ProductBaseInfo::getPpid, Function.identity()));
    }
    
    /**
     * 获取年包ppid列表（带5分钟缓存）
     * @return 年包ppid列表
     */
    @Override
    @Cached(name = "ProductinfoService.annualPackageList", expire = 5, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.LOCAL)
    public List<Integer> annualPackageListWithCache() {
        List<Integer> result = productinfoService.annualPackageList();
        return result != null ? result : new ArrayList<>();
    }

    @Override
    public ProductInfoSimpleVo getProductInfoByImei(String imei) {
        if (StringUtils.isEmpty(imei)) {
            return null;
        }
        return baseMapper.getProductInfoByImei(imei);
    }

    /**
     * 根据名称获取小件商品信息热销top5
     *
     * @param name name
     */
    @Override
    public List<SearchProductInfoVO> getTop5SmallProductByNameLike(String name) {
        try {
            Integer integer = Integer.valueOf(name);
            return baseMapper.getTop5SmallProductByIdOrLike(integer);
        } catch (NumberFormatException e) {
            return baseMapper.getTop5SmallProductByNameLike(name);
        }
    }

    @Override
    public Map<Integer, ProductSimpleBO> getProductMapByPpids(List<Integer> ppids) {
        if (CollectionUtils.isEmpty(ppids)) {
            return new HashMap<>();
        }
        List<ProductSimpleBO> list = baseMapper.getProductMapByPpids(ppids);
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(e -> e.getPpid(), e -> e));
    }

    @Override
    public Map<Integer, ProductSimpleBO> getProductMapByPpidsNew(List<Integer> ppids) {
        if (CollectionUtils.isEmpty(ppids)) {
            return new HashMap<>();
        }
        List<ProductSimpleBO> list=CommonUtils.bigDataInQuery(ppids,ppid->baseMapper.getProductMapByPpids(ppid));
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(ProductSimpleBO::getPpid, Function.identity(), (n1, n2) -> n2));
    }

    @Override
    public List<CommonSwitchBO> getNameByIds(String limitIdsStr) {
        if (StringUtils.isEmpty(limitIdsStr)) {
            return new ArrayList<>();
        }

        return baseMapper.getNameByIds(CommonUtils.str2List(limitIdsStr));
    }

    @Override
    public List<ProductInfoVO> listProductInfoByBrandId(Integer brandId) {
        return baseMapper.listProductInfoByBrandId(brandId);
    }

    @Override
    public List<SearchProductInfoVO> getProductNamesByPpids(String ppids) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(CommonUtils.covertIdStr(ppids))) {
            return new ArrayList<>();
        }
        return baseMapper.getProductNamesByPpids(CommonUtils.covertIdStr(ppids));
    }

    @Override
    public List<Productinfo> getProductinfoByPpid(List<Integer> ppid) {
        return CommonUtils.bigDataInQuery(ppid,ids->baseMapper.getProductListByPpids(ids));
    }

    @Override
    public Map<Integer, List<ProductSimpleBO>> getProductInfoByProductId(List<Integer> productIdList) {
        Map<Integer, List<ProductSimpleBO>> map = new HashMap<>();
        if(CollUtil.isEmpty(productIdList)){
            return map;
        }
        List<ProductSimpleBO> productinfos = CommonUtils.bigDataInQuery(productIdList, ids -> baseMapper.getProductMapByProductIds(ids));
        if(CollUtil.isEmpty(productinfos)){
            return map;
        }
        return productinfos.stream().collect(Collectors.groupingBy(ProductSimpleBO::getPid));
    }

    @Override
    public List<SearchProductInfoVO> getProductNamesByMkcId(String mkcId) {
        return productinfoService.getProductNamesByMkcId(mkcId, true);
    }

    @Override
    public List<SearchProductInfoVO> listProductNamesOnlyByMkcId(List<Integer> mkcIds){
        if(CollUtil.isEmpty(mkcIds)){
            return Collections.emptyList();
        }
        return baseMapper.listProductNamesOnlyByMkcId(mkcIds);
    }

    @Override
    public List<SearchProductInfoVO> getProductNamesByMkcId(String mkcId, boolean isToBasketIdNull) {
        List<Integer> integers = CommonUtils.covertIdStr(mkcId);
        if (CollUtil.isEmpty(integers)) {
            return new ArrayList<>();
        }
        return CommonUtils.bigDataInQuery(integers,ids -> baseMapper.getProductNamesByMkcId(ids, isToBasketIdNull));
    }

    @Override
    public R<OutboundOrderRes> getOutboundOrder(Integer orderNo) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();

        if (oaUserBO == null || oaUserBO.getArea() == null) {
            return R.error("用户信息校验失败");
        }

//        if (!OutboundOrderRes.addressHashMap.containsKey(oaUserBO.getAreaId())){
//            return R.error("该登录地区不可打印出货单");
//        }


        //通过订单号获取收货人信息
        OutboundOrderRes.ReceiverInfo receiverInfo = CommonUtil.autoQueryHist(()->subAddressMapper.getReceiverInfo(orderNo), MTableInfoEnum.SUB, orderNo);
        if (receiverInfo == null) {
            return R.error("该订单不存在");
        }
        Integer status = receiverInfo.getStatus();
        if (!SubCheckEnum.SUB_CHECK_OUT_OF_STOCK.getCode().equals(status)
                && !SubCheckEnum.SUB_CHECK_COMPLETED.getCode().equals(status)
                && !SubCheckEnum.SUB_CHECK_CONFIRMED.getCode().equals(status)
                && !SubCheckEnum.SUB_CHECK_ARREARS.getCode().equals(status)) {
            return R.error("已出库、已完成、已确认、欠款状态下的订单可打印发货单");
        }

        //通过订单号获取商品列表
        List<OutboundOrderRes.OutboundOrderProduct> outboundOrderProducts = CommonUtil.autoQueryHist(()->basketMapper.listOutboundsByOrderNoV2(orderNo), MTableInfoEnum.SUB, orderNo);
        //通过商品列表获取得商品ppID列表
        List<Integer> ppIdList = outboundOrderProducts.stream().map(OutboundOrderRes.OutboundOrderProduct::getPpriceid).collect(Collectors.toList());
        //通过商品ppID列表获取得到商品信息列表
        List<Productinfo> productInfos = getProductinfoByPpid(ppIdList);
        Map<Integer, Productinfo> productInfoMap = productInfos.stream().collect(Collectors.toMap(Productinfo::getPpriceid, Function.identity(), (key1, key2) -> key2));

        //总数量和总价格
        int totalCount = 0;
        BigDecimal totalPrice = new BigDecimal(0);
        for (int i = 0; i < outboundOrderProducts.size(); i++) {
            OutboundOrderRes.OutboundOrderProduct outboundOrderProduct = outboundOrderProducts.get(i);
            Productinfo product = productInfoMap.get(outboundOrderProduct.getPpriceid());
            StringBuilder productName = new StringBuilder(product.getProductName());
            productName.append(" ").append(product.getProductColor()).append(" ");
            if (product.getIsmobile1()) {
                List<ProductMkc> productMkc = productMkcService.getProductMkcByBasketId(outboundOrderProduct.getBasketId());
                if (CollectionUtils.isNotEmpty(productMkc)) {
                    productName.append(StringUtils.join(productMkc.stream().map(ProductMkc::getOrderid).collect(Collectors.toList()), StringPool.COMMA));
                }
            }

            outboundOrderProduct.setProductName(productName.toString());
            outboundOrderProduct.setNo(i + 1);
            totalCount += outboundOrderProduct.getBasketCount();
            totalPrice = totalPrice.add(outboundOrderProduct.getTotalPrice());
        }


        OutboundOrderRes outboundOrderRes = new OutboundOrderRes();
        Integer authorizeId = oaUserBO.getAuthorizeId();

        if (StrictSelectionSystem.equals(authorizeId)) {
            outboundOrderRes.setShipperName("深圳九讯供应链管理有限公司");
            Result<Long> yxNoOrderNo = pickCloud.getYxNoOrderNo(orderNo, XtenantEnum.getXtenant());
            outboundOrderRes.setYxNo(yxNoOrderNo.getData());
        } else {
            outboundOrderRes.setShipperName("云南九机电子产品有限公司");
        }
        AreaInfo areaInfo = Optional.ofNullable(areaInfoService.getById(oaUserBO.getAreaId())).orElse(new AreaInfo());
        outboundOrderRes.setShipperAddress(areaInfo.getCompanyAddress2());
        outboundOrderRes.setReceiverInfo(receiverInfo);
        //订单状态为已出库、欠款、已完成，出货单的发货日期获取出库时间
        if((Arrays.asList(SubCheckEnum.SUB_CHECK_OUT_OF_STOCK.getCode(),
                SubCheckEnum.SUB_CHECK_COMPLETED.getCode(),
                SubCheckEnum.SUB_CHECK_ARREARS.getCode()).contains(status))){
            outboundOrderRes.setPrintDate(Optional.ofNullable(receiverInfo.getTradeDate()).orElse(LocalDateTime.now()));
        }  else {
            //订单状态为已确认，出货单的发货日期获取打印时间
            outboundOrderRes.setPrintDate(LocalDateTime.now());
        }
        outboundOrderRes.setOrderNo(orderNo);
        outboundOrderRes.setProductList(outboundOrderProducts);
        outboundOrderRes.setTotalCount(totalCount);
        outboundOrderRes.setTotalPrice(totalPrice);

        return R.success(outboundOrderRes);
    }

    @Override
    public List<ProductIsMobileInfoBO> getProductIsmobileByPpids(List<Integer> ppids) {
        if (CollUtil.isEmpty(ppids)){
            return new ArrayList<>();
        }
        return CommonUtils.bigDataInQuery(ppids,ids -> baseMapper.getProductIsmobileByPpids(ids));
    }

    /**
     * 查询大件库存
     * 过滤稀缺商品
     * @param ppids
     * @return
     */
    @Override
    public List<StockCountVO> getProductMkcCountByPpids(List<Integer> ppids, StockInfoReqVO req) {
        if (CollUtil.isEmpty(ppids)){
            return new ArrayList<>();
        }
        return CommonUtils.bigDataInQuery(ppids,ids -> baseMapper.getProductMkcCountByPpids(ppids, req));
    }

    /**
     * 查询小件库存
     * 过滤稀缺商品
     * @param ppids
     * @return
     */
    @Override
    public List<StockCountVO> getProductKcCountByPpids(List<Integer> ppids, StockInfoReqVO req) {
        if (CollUtil.isEmpty(ppids)){
            return new ArrayList<>();
        }
        return CommonUtils.bigDataInQuery(ppids,ids -> baseMapper.getProductKcCountByPpids(ppids, req));
    }

    /**
     * 查询大件库存
     * 过滤稀缺商品
     * @param req
     * @return
     */
    @Override
    public List<WebStockInfoResVO> getProductMkcStockByPpids(WebStockInfoReqVO req) {
        if (CollUtil.isEmpty(req.getPpids())){
            return new ArrayList<>();
        }
        return baseMapper.getProductMkcStockByPpids(req);
    }

    /**
     * 查询小件库存
     * 过滤稀缺商品
     * @param req
     * @return
     */
    @Override
    public List<WebStockInfoResVO> getProductKcStockByPpids(WebStockInfoReqVO req) {
        if (CollUtil.isEmpty(req.getPpids())){
            return new ArrayList<>();
        }
        return baseMapper.getProductKcStockByPpids(req);
    }

    @Override
    public List<DiaoboStockInfoResVO> getDiaoboByPpidAndToArea(DiaoboStockInfoReqVO req) {
        return baseMapper.getDiaoboByPpidAndToArea(req);
    }

    @Override
    public List<WebStockPriceResVO> getInPriceByPpid(WebStockPriceReqVO req) {
        return baseMapper.getInPriceByPpid(req);
    }

    /**
     * 查询大件库存数量
     *
     * @param ppids
     * @param areaIds
     * @return
     */
    @Override
    public List<CheckStockInfoResVO> getMkcStockByPpidsAndAreaIds(String ppids, String areaIds) {
        if (StrUtil.isEmpty(ppids) || StrUtil.isEmpty(areaIds)) {
            return new ArrayList<>();
        }
        return baseMapper.getMkcStockByPpidsAndAreaIds(ppids, areaIds);
    }

    /**
     * 查询小件库存数量
     *
     * @param ppids
     * @param areaIds
     * @return
     */
    @Override
    public List<CheckStockInfoResVO> getKcStockByPpidsAndAreaIds(String ppids, String areaIds) {
        if (StrUtil.isEmpty(ppids) || StrUtil.isEmpty(areaIds)) {
            return new ArrayList<>();
        }
        return baseMapper.getKcStockByPpidsAndAreaIds(ppids,areaIds);
    }

    /**
     * 查询赠品库存
     *
     * @param req
     * @return
     */
    @Override
    public List<GiftStockResVO> getGiftStockByAreaIds(GiftStockReqVO req) {
        return baseMapper.getGiftStockByAreaIds(req);
    }

    /**
     * 查询商品成本
     *
     * @param req
     * @return
     */
    @Override
    public List<WebStockPriceResVO> getProductInPriceByPpid(WebStockPriceReqVO req) {
        return baseMapper.getProductInPriceByPpid(req);
    }
}
