package com.jiuji.oa.oacore.thirdplatform.douyinlife.utils;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/7/5 14:57
 */
@Slf4j
public class DouyinLifeUtil {

    /**
     * get请求
     * @param url
     * @param params
     * @param accessToken
     * @return
     */
    public static String get (String url, Map<String, Object> params, String accessToken) {
        String result = HttpUtil.createGet(url)
                .header("Content-Type", "application/json")
                .header("access-token", accessToken)
                .form(params)
                .execute().body();
        log.info("调用抖音接口，url={},params={},result={}", url, params, result);
        return result;
    }

    /**
     * post
     * @param url
     * @param jsonParam
     * @return
     */
    public static String post (String url, String jsonParam) {
        HttpResponse res = HttpUtil.createPost(url)
                .header("Content-Type", "application/json")
                .body(jsonParam)
                .setFollowRedirects(true)
                .execute();
        String result = res.body();
        log.info("调用抖音接口，url={},params={},result={}", url, jsonParam, result);
        return result;
    }

    /**
     * post
     * @param url
     * @param jsonParam
     * @param accessToken
     * @return
     */
    public static String sendPost (String url, String jsonParam, String accessToken) {
        String result = HttpUtil.createPost(url)
                .header("Content-Type", "application/json")
                .header("access-token", accessToken)
                .body(jsonParam)
                .execute().body();
        log.info("调用抖音接口，url={},params={},result={}", url, jsonParam, result);
        return result;
    }

    /**
     * post
     * @param url
     * @param jsonParam
     * @param accessToken
     * @return
     */
    public static String post (String url, String jsonParam, String accessToken) {
        try {
            return sendPost(url, jsonParam, accessToken);
        } catch (Exception e) {
            if (Optional.ofNullable(e.getMessage()).orElse("").contains("timed out")) {
                throw new CustomizeException(R.error("调用抖音平台接口超时异常，请稍后再试"));
            }
            throw new CustomizeException(R.error("调用抖音平台接口异常，请稍后再试"));
        }
    }
}
