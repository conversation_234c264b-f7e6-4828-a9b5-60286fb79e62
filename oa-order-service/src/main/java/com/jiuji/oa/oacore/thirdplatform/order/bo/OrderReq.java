/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.oacore.thirdplatform.order.bo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import lombok.Data;

import java.util.List;


/**
 * 订单请求参数实体
 *
 * <AUTHOR>
 */
@Data
public class OrderReq extends Model<OrderReq> {
    private static final long serialVersionUID = 10L;

    @JsonSetter("app_id")
    @JSONField(name = "app_id")
    private String appId;
    private String sig;
    private long timestamp;
    /**
     * 订单号
     */
    @JsonSetter("order_id")
    @JSONField(name = "order_id")
    private long orderId;
    /**
     * 订单展示ID
     */
    @JsonSetter("wm_order_id_view")
    @JSONField(name = "wm_order_id_view")
    private long wmOrderIdView;

    /**
     * 门店id
     */
    @JsonSetter("app_poi_code")
    @JSONField(name = "app_poi_code")
    private String appPoiCode;

    /**
     * 商家门店名称
     */
    @JsonSetter("wm_poi_name")
    @JSONField(name = "wm_poi_name")
    private String wmPoiName;

    /**
     * 商家门店地址
     */
    @JsonSetter("wm_poi_address")
    @JSONField(name = "wm_poi_address")
    private String wmPoiAddress;

    /**
     * 商家门店联系电话
     */
    @JsonSetter("wm_poi_phone")
    @JSONField(name = "wm_poi_phone")
    private String wmPoiPhone;

    /**
     * 订单收货人地址
     */
    @JsonSetter("recipient_address")
    @JSONField(name = "recipient_address")
    private String recipientAddress;

    /**
     * 订单收货人电话
     */
    @JsonSetter("recipient_phone")
    @JSONField(name = "recipient_phone")
    private String recipientPhone;

    /**
     * 备用隐私号
     */
    @JsonSetter("backup_recipient_phone")
    @JSONField(name = "backup_recipient_phone")
    private List<String> backupRecipientPhone;

    /**
     * 订单收货人姓名
     */
    @JsonSetter("recipient_name")
    @JSONField(name = "recipient_name")
    private String recipientName;

    /**
     * 门店配送费
     */
    @JsonSetter("shipping_fee")
    @JSONField(name = "shipping_fee")
    private Double shippingFee;

    /**
     * 预约最早送达时间(单位: 秒)
     */
    @JsonSetter("delivery_time_left")
    @JSONField(name = "delivery_time_left")
    private Long deliveryTimeLeft;

    /**
     * 预约最晚送达时间(单位: 秒)
     */
    @JsonSetter("delivery_time_right")
    @JSONField(name = "delivery_time_right")
    private Long deliveryTimeRight;

    /**
     * 订单的实际在线支付总价，单位是元。此字段数据为用户实际支付的订单总金额，含打包袋、配送费等。
     */
    private Double total;

    /**
     * 订单的总原价，单位是元。此字段数据为未扣减所有优惠前订单的总金额，含打包袋、配送费等。
     */
    @JsonSetter("original_price")
    @JSONField(name = "original_price")
    private Double originalPrice;

    /**
     * 0--普通配送
     * 1--用户到店自取
     */
    @JsonSetter("pick_type")
    @JSONField(name = "pick_type")
    private Integer pickType;

    /**
     * 订单备注信息
     */
    private String caution;

    /**
     * 配送员联系电话
     */
    @JsonSetter("shipper_phone")
    @JSONField(name = "shipper_phone")
    private String shipperPhone;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 城市id
     */
    @JsonSetter("city_id")
    @JSONField(name = "city_id")
    private String cityId;

    /**
     * 订单创建时间，为10位秒级的时间戳，此字段为用户提交订单的时间
     */
    private long ctime;

    /**
     * 订单更新时间，为10位秒级的时间戳，此字段信息为当前订单最新订单/配送单状态更新的时间
     */
    private long utime;

    /**
     * 预计送达时间
     */
    @JsonSetter("delivery_time")
    @JSONField(name = "delivery_time")
    private long deliveryTime;

    /**
     * 系统自动计算预计送达时间 为10位秒级的时间戳
     */
    @JsonSetter("estimate_arrival_time")
    @JSONField(name = "estimate_arrival_time")
    private long estimateArrivalTime;

    /**
     * 是否是第三方配送平台配送
     */
    @JsonSetter("is_third_shipping")
    @JSONField(name = "is_third_shipping")
    private int isThirdShipping;

    /**
     * 支付类型：1-货到付款，2-在线支付。目前订单只支持在线支付，此字段推送信息为2
     */
    @JsonSetter("pay_type")
    @JSONField(name = "pay_type")
    private int payType;

    /**
     * 订单维度的商家对账信息
     */
    @JsonSetter("poi_receive_detail_yuan")
    @JSONField(name = "poi_receive_detail_yuan")
    private ReceiveDetailYuan poiReceiveDetailYuan;


    @JsonSetter("user_member_info")
    @JSONField(name = "user_member_info")
    private UserMemberInfo userMemberInfo;

    /**
     * 订单优惠信息
     */
    @JsonSetter("extras")
    @JSONField(name = "extras")
    private List<ExtrasBO> extras;

    /**
     * 订单优惠信息
     */
    @JsonSetter("sku_benefit_detail")
    @JSONField(name = "sku_benefit_detail")
    private List<SkuBenefitDetailBO> skuBenefitDetail;

    /**
     * 订单收货地址的经度
     */
    private String longitude;

    /**
     * 订单标签列表
     */
    @JsonSetter("order_tag_list")
    @JSONField(name = "order_tag_list")
    private List<Integer> orderTagList;


    /**
     * 订单收货地址的纬度
     */
    private String latitude	;

    /**
     * 商品明细
     */
    private List<OrderProduct> detail;

    /**
     * 内部流转数据 start
     */
    @JSONField(serialize = false, deserialize = false)
    @JsonIgnore
    private transient Tenant tenant;
    /**
     * 内部流转数据 end
     */

}
