package com.jiuji.oa.oacore.thirdplatform.doudian.message.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.TypeReference;
import com.doudian.open.core.msg.DoudianOpMsgParamRecord;
import com.doudian.open.core.msg.DoudianOpMsgRequest;
import com.doudian.open.core.msg.MsgParam;
import com.doudian.open.exception.DoudianOpException;
import com.doudian.open.gson.JsonElement;
import com.doudian.open.utils.JsonUtil;
import com.jiuji.cloud.oaapi.vo.request.UserModelCloudVO;
import com.jiuji.cloud.oaapi.vo.response.UserRegResultCloudVO;
import com.jiuji.oa.oacore.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.thirdplatform.common.enums.DouDianCodeEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.LogTypeEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.PlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.common.util.JsonUtils;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.DoudianFactory;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.MyAccessToken;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.util.DoudianUtil;
import com.jiuji.oa.oacore.thirdplatform.doudian.enums.UserOpenPlatformEnum;
import com.jiuji.oa.oacore.thirdplatform.doudian.message.bo.DdMsgResultBo;
import com.jiuji.oa.oacore.thirdplatform.doudian.message.service.MessageMqService;
import com.jiuji.oa.oacore.thirdplatform.doudian.message.service.MessageService;
import com.jiuji.oa.oacore.thirdplatform.doudian.message.vo.DouYinMemberReq;
import com.jiuji.oa.oacore.thirdplatform.doudian.message.vo.DouYinMemberRes;
import com.jiuji.oa.oacore.thirdplatform.douyinlife.constant.DouyinLifeConstant;
import com.jiuji.oa.oacore.thirdplatform.douyinlife.service.DouyinLifeService;
import com.jiuji.oa.oacore.thirdplatform.douyinlife.utils.DouyinLifeUtil;
import com.jiuji.oa.oacore.thirdplatform.douyinlife.vo.DouyinLifeClientToken;
import com.jiuji.oa.oacore.thirdplatform.oplog.po.MeituanJdWorkLog;
import com.jiuji.oa.oacore.thirdplatform.oplog.service.MeituanJdWorkLogService;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.TenantService;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.oacore.weborder.req.UpdateDouYinMemberReq;
import com.jiuji.oa.oacore.weborder.res.UpdateDouYinMemberRes;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 抖音消息处理的服务接口
 * <AUTHOR>
 * @since 2022/3/20 20:55
 */
@Service
@Slf4j
public class MessageMqServiceImpl implements MessageMqService {

    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private DoudianFactory doudianFactory;
    @Resource
    private MeituanJdWorkLogService meituanJdWorkLogService;
    @Resource
    private TenantService tenantService;
    @Resource
    private SysConfigClient sysConfigClient;

    private final static String DYTG = "111000";


    private Boolean tiktokSignatureVerification(HttpServletRequest request, String body){
        //获取门店配置
        Tenant tenant = tenantService.lambdaQuery().eq(Tenant::getTenantCode, DYTG).list().get(0);
        if(Objects.isNull(tenant)){
            throw new CustomizeException("抖音会员同步tenant查询为空");
        }
        String format = String.format("%s&client_key=%s&timestamp=%s&http_body=%s", tenant.getAppSecret(), request.getParameter("client_key"), request.getParameter("timestamp"), body);
        String createSign = DigestUtil.sha256Hex(format);
        String xLifeSign = ServletUtil.getHeader(request, "X-Life-Sign", StandardCharsets.UTF_8);
        log.warn("createSign:{},xLifeSign:{}，加密前：{}", createSign, xLifeSign,format);
        return createSign.equals(xLifeSign);
    }

    /**
     * 抖音会员创建
     * @param request
     * @return
     */

    @Override
    public R createDouYinMember(HttpServletRequest request) {
        String body = ServletUtil.getBody(request);
        log.warn("抖音创建会员回调: {}", body);
        DouYinMemberRes data = new DouYinMemberRes();
        data.setPoint_amount_cent(NumberConstant.ZERO);
        data.setUser_level(NumberConstant.ONE);
        data.setIs_new_member(Boolean.FALSE);
        data.setDescription("success");
        data.setError_code(NumberConstant.ONE_HUNDRED);
        try {
            //抖音验签
            if(!tiktokSignatureVerification(request,body)){
                throw new Exception("抖音会员同步验签失败");
            }
            DouYinMemberReq memberReq = JSONUtil.toBean(body, DouYinMemberReq.class);
            //调用主站接口进行会员的创建
            UserModelCloudVO userModelCloudVO = new UserModelCloudVO();
            userModelCloudVO.setCheckParam(Boolean.FALSE);
            userModelCloudVO.setUserName(memberReq.getMobile());
            userModelCloudVO.setErdu(BigDecimal.ZERO);
            userModelCloudVO.setRealname("");
            userModelCloudVO.setClient(UserOpenPlatformEnum.TIKTOK.getName());
            userModelCloudVO.setMobile(memberReq.getMobile());
            userModelCloudVO.setOpenId(memberReq.getOpen_id());
            userModelCloudVO.setAccountId(memberReq.getAccount_id());
            R result ;
            String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.M_URL)).map(R::getData).filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
            String evidenceUrl = host + "/cloudapi_nc/oaApi/api/bbsxpUsers/register/v1" ;
            HttpResponse evidenceResult = HttpUtil.createGet(evidenceUrl)
                    .header("xservicename", "oaapi-9ji")
                    .header("xtenant", XtenantEnum.getXtenant().toString())
                    .body(JSONUtil.toJsonStr(userModelCloudVO))
                    .execute();
            log.warn("抖音调用主站会员注册接口传入参数：{},Xtenant:{},返回结果：{}", JSONUtil.toJsonStr(userModelCloudVO),XtenantEnum.getXtenant(),JSONUtil.toJsonStr(evidenceResult));
            if(evidenceResult.isOk()){
                result = JSONUtil.toBean(JSONUtil.toJsonStr(evidenceResult.body()), R.class);
            } else {
                log.warn("抖音调用主站会员注册接口异传入参数：{}",JSONUtil.toJsonStr(userModelCloudVO));
                throw new IOException("抖音调用主站会员注册接口异常");
            }
            UserRegResultCloudVO userRegResultCloudVO = JSONUtil.toBean(JSONUtil.toJsonStr(result.getData()), UserRegResultCloudVO.class);
            String userId = "";
            if(!result.isSuccess()){
                //判断会员是否已经注册过了
                if(ObjectUtil.isNotNull(userRegResultCloudVO.getUserId()) && !NumberConstant.ZERO.equals(userRegResultCloudVO.getUserId())){
                    userId = userRegResultCloudVO.getUserId().toString();
                    log.warn("抖音注册会员调用主站接口 会员已经注册过：{},userId:{}",JSONUtil.toJsonStr(userRegResultCloudVO),userId);
                    data.setError_code(NumberConstant.ZERO);
                } else {
                    String msg = Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg());
                    throw new CustomizeException(msg);
                }
            } else {
                //设置会员等级
                data.setIs_new_member(Boolean.TRUE);
                data.setError_code(NumberConstant.ZERO);
            }
        } catch (CustomizeException e){
            RRExceptionHandler.logError("抖音创建会员业务异常："+e.getMessage(), body, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            data.setDescription(e.getMessage());
            data.setError_code(NumberConstant.TWO_HUNDRED);
        } catch (Exception e){
            RRExceptionHandler.logError("抖音创建会员异常："+e.getMessage(), body, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            data.setDescription(e.getMessage());
        }
        log.warn("抖音创建会员回调返回结果: {}", JSONUtil.toJsonStr(data));
        return R.success(data);
    }

    @Override
    public UpdateDouYinMemberRes updateDouYinMember(UpdateDouYinMemberReq req) {
        Tenant tenant = tenantService.lambdaQuery().eq(Tenant::getTenantCode, DYTG).list().get(0);
        if(Objects.isNull(tenant)){
            throw new CustomizeException("抖音会员同步tenant查询为空");
        }
        DouyinLifeClientToken accessToken = SpringUtil.getBean(DouyinLifeService.class).getAccessToken(tenant.getAppKey(), tenant.getAppSecret());
        String url = DouyinLifeConstant.BASE_URL + DouyinLifeConstant.UPDATE_MEMBER;
        String post = DouyinLifeUtil.post(url, JsonUtils.toJson(req), accessToken.getAccessToken());
        log.warn("抖音修改会员信息传入参数：{}，返回结果：{}", JSONUtil.toJsonStr(req), post);
        UpdateDouYinMemberRes res = JSONUtil.toBean(post, UpdateDouYinMemberRes.class);
        if(!NumberConstant.ZERO.equals(Optional.ofNullable(res.getData()).orElse(new UpdateDouYinMemberRes.DouYinMemberInfoResult()).getError_code())){
            RRExceptionHandler.logError("抖音修改会员异常："+res.getData().getDescription() , req, null, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
        return res;
    }

    /**
     * 抖音推送消息处理
     * @param request
     * @return
     */
    @Override
    public DdMsgResultBo push(HttpServletRequest request) {
        DoudianOpMsgRequest<JsonElement> msgRequest = null;
        DdMsgResultBo result = DdMsgResultBo.success();
        try {
            msgRequest = doudianFactory.getMsgRequest(request);
            log.warn("抖音推送内容: {}", msgRequest.getParam().getRequestBody());
            List<DoudianOpMsgParamRecord<JsonElement>> requestBody = DoudianUtil.getRequestBody(msgRequest);
            //按门店分组
            Map<Long, List<DoudianOpMsgParamRecord<JsonElement>>> shopIdMap = requestBody.stream().filter(Objects::nonNull)
                    .filter(mpr -> Objects.nonNull(mpr.getData()) && mpr.getData().isJsonObject())
                    .collect(Collectors.groupingBy(mpr -> ObjectUtil.defaultIfNull(DoudianUtil.getShopId(mpr),0L)));
            if(shopIdMap.isEmpty()){
                //联通性测试,没有消息
                return result;
            }

            final DoudianOpMsgRequest<JsonElement> msgRequestFinal = msgRequest;
            boolean isCheckSignSuccess = shopIdMap.entrySet().stream().filter(entry -> Objects.nonNull(entry.getKey()))
                    .filter(entry -> ObjectUtil.defaultIfNull(entry.getKey(),0L).compareTo(Long.valueOf(0))>0)
                    .anyMatch(entry -> {
                        MyAccessToken myAccessToken = doudianFactory.getMyAccessToken(entry.getKey());
                        if (Objects.isNull(myAccessToken)) {
                            return false;
                        }
                        msgRequestFinal.setConfig(myAccessToken.getOpConfig());
                        return myAccessToken != null && msgRequestFinal.checkSign();
                    });
            //按门店分组进行验签 任意一个成功,验签通过
            if(!isCheckSignSuccess){
                return DdMsgResultBo.error(DouDianCodeEnum.MSG_CHECK_SIGN_ERROR);
            }
            //消息推送到mq
            shopIdMap.entrySet().stream().map(Map.Entry::getValue).flatMap(List::stream)
                    .forEach(v -> rabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_DOUYIN_MESSAGE,
                            JsonUtil.toJson(v)));
        } catch (DoudianOpException e) {
            String param = Optional.ofNullable(msgRequest).map(DoudianOpMsgRequest::getParam).map(MsgParam::getRequestBody).orElse(null);
            result.setCode(DouDianCodeEnum.UNRECOGNIZED_EXCEPTION.getCode()).setMsg(e.getMessage());
            MeituanJdWorkLog structure = meituanJdWorkLogService.structure("抖音消息接收", StrUtil.format("{} 参数: {}",e.getMessage(),param),
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> meituanJdWorkLogService.save(structure)).commit();
            return DdMsgResultBo.error(Optional.ofNullable(e.getCode()).map(Enum::name).map(DouDianCodeEnum::valueOf)
                    .orElse(DouDianCodeEnum.UNRECOGNIZED_EXCEPTION));
        }catch (Exception e) {
            AtomicReference<String> msg = new AtomicReference<>();
            String param = Optional.ofNullable(msgRequest).map(DoudianOpMsgRequest::getParam).map(MsgParam::getRequestBody).orElse(null);
            if(e instanceof CustomizeException && e.getCause() == null){
                //客户异常,只需要记录日志
                msg.set(e.getMessage());
                log.warn("发生[{}]异常, 参数: {}", msg.get(), param);
            }else{
                RRExceptionHandler.logError("抖音消息接收", param, e, errorMsg -> {
                    SpringUtil.getBean(SmsService.class).sendOaMsgTo9JiMan(errorMsg);
                    msg.set(errorMsg);
                });
            }

            result.setCode(DouDianCodeEnum.UNRECOGNIZED_EXCEPTION.getCode()).setMsg(msg.get());
            MeituanJdWorkLog structure = meituanJdWorkLogService.structure("抖音消息接收", StrUtil.format("{} 参数: {}",msg.get(),param),
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> meituanJdWorkLogService.save(structure)).commit();
        }

        return result;
    }



    /**
     * mq消息的消费
     * @param message
     * @param channel
     * @throws IOException
     */
    @Override
    public void douyinMessage(Message message, Channel channel) throws IOException {
        //解析为对象
        String msg = new String(message.getBody());
        try {
            log.warn("消费到抖音消息: {}", msg);
            handleMessage(msg);
        }catch (Exception e){
            //记录到日志表
            RRExceptionHandler.logError("抖音消费消息异常", msg, e,
                    errorMsg ->
                    {
                        MeituanJdWorkLog structure = meituanJdWorkLogService.structure("抖音消费消息异常", StrUtil.format("{} 参数:{}",errorMsg,msg),
                                "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
                        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> meituanJdWorkLogService.save(structure)).commit();
                    });

        }finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }
    }

    @Override
    public R handleMessage(String msg) {
        DoudianOpMsgParamRecord<JsonElement> msgParamRecord = DoudianUtil.fromJson(msg, new TypeReference<DoudianOpMsgParamRecord<JsonElement>>(){}.getType());
        if(msgParamRecord == null){
            return R.error("消息内容不能为空");
        }
        //获取处理消息的处理器并消费消息
        return SpringUtil.getApplicationContext().getBeansOfType(MessageService.class)
                .entrySet().stream().map(Map.Entry::getValue).filter(ms -> Objects.equals(ms.acceptTag().getCode(),
                Optional.ofNullable(msgParamRecord).map(DoudianOpMsgParamRecord::getTag).orElse(null)))
                .findFirst().orElse(SpringUtil.getBean("defalutMessageService", MessageService.class))
                .handleMessage(msg, msgParamRecord);
    }

}
