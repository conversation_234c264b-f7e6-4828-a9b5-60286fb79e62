package com.jiuji.oa.oacore.common.enums;

import com.jiuji.oa.oacore.common.util.DateUtil;
import com.jiuji.oa.oacore.common.util.SaasManagerUtils;
import com.jiuji.oa.oacore.oaorder.vo.res.OrderNoticeRes;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 11:37
 * @Description
 */
@Getter
public enum OrderNoticeEnum {

    /**
     * 订单通知枚举值
     */
    ORDER_NOTIFICATION(1) {
        @Override
        public OrderNoticeRes getOrderNotice(Integer subNumber) {
            OrderNoticeRes result = new OrderNoticeRes();
            return result.setUnKey("1")
                    .setTitle(MESSAGE
                            + subNumber + "个]订单未完成")
                    .setLink(SaasManagerUtils.getMoaUrl() + "/app/native/OrderListNew?orderType=1")
                    .setTime(DateUtil.getCurentDateStr(PATTERN));
        }
    },
    LIANGPING_ORDER_NOTIFICATION(2) {
        @Override
        public OrderNoticeRes getOrderNotice(Integer subNumber) {
            OrderNoticeRes result = new OrderNoticeRes();
            return result.setUnKey("2")
                    .setTitle(MESSAGE
                            + subNumber + "个]良品订单未完成")
                    .setLink(SaasManagerUtils.getMoaUrl() + "/app/native/OrderListNew?orderType=2")
                    .setTime(DateUtil.getCurentDateStr(PATTERN));
        }
    },
    SHOUHOUYUYUE_ORDER_NOTIFICATION(3) {
        @Override
        public OrderNoticeRes getOrderNotice(Integer subNumber) {
            OrderNoticeRes result = new OrderNoticeRes();
            return result.setUnKey("3")
                    .setTitle(MESSAGE
                            + subNumber + "个]售后预约单需要跟进")
                    .setLink(SaasManagerUtils.getMoaUrl() + "/app/native/OrderListNew?orderType=3")
                    .setTime(DateUtil.getCurentDateStr(PATTERN));
        }
    },
    SHOUHOU_ORDER_NOTIFICATION(4) {
        @Override
        public OrderNoticeRes getOrderNotice(Integer subNumber) {
            OrderNoticeRes result = new OrderNoticeRes();
            return result.setUnKey("4")
                    .setTitle(MESSAGE
                            + subNumber + "个]售后单需要跟进")
                    .setLink(SaasManagerUtils.getMoaUrl() + "/app/native/OrderListNew?orderType=4")
                    .setTime(DateUtil.getCurentDateStr(PATTERN));
        }
    },
    SHOUHOUPEIJIAN_ORDER_NOTIFICATION(5) {
        @Override
        public OrderNoticeRes getOrderNotice(Integer subNumber) {
            OrderNoticeRes result = new OrderNoticeRes();
            return result.setUnKey("5")
                    .setTitle(MESSAGE
                            + subNumber + "个]售后配件调拨单需要跟进")
                    .setLink(SaasManagerUtils.getMoaUrl() + "/app/native/OrderListNew?orderType=5")
                    .setTime(DateUtil.getCurentDateStr(PATTERN));
        }
    },
    SHOUHOUXIAOJIAN_ORDER_NOTIFICATION(6) {
        @Override
        public OrderNoticeRes getOrderNotice(Integer subNumber) {
            OrderNoticeRes result = new OrderNoticeRes();
            return result.setUnKey("7")
                    .setTitle(MESSAGE
                            + subNumber + "个]售后小件单需要跟进")
                    .setLink(SaasManagerUtils.getMoaUrl() + "/app/native/OrderListNew?orderType=7")
                    .setTime(DateUtil.getCurentDateStr(PATTERN));
        }
    },
    HUOSHOU_ORDER_NOTIFICATION(8) {
        @Override
        public OrderNoticeRes getOrderNotice(Integer subNumber) {
            OrderNoticeRes result = new OrderNoticeRes();
            return result.setUnKey("8")
                    .setTitle(MESSAGE
                            + subNumber + "个]回收订单需要跟进")
                    .setLink(SaasManagerUtils.getMoaUrl() + "/app/native/OrderListNew?orderType=8")
                    .setTime(DateUtil.getCurentDateStr(PATTERN));
        }
    },
    HUOSHOU_GOLD_NOTIFICATION(13) {
        @Override
        public OrderNoticeRes getOrderNotice(Integer subNumber) {
            OrderNoticeRes result = new OrderNoticeRes();
            return result.setUnKey("13")
                    .setTitle(MESSAGE
                            + subNumber + "个]黄金回收预约单需要跟进")
                    .setLink(SaasManagerUtils.getMoaUrl() + "/huiShou/recovery-gold/appoint-list/{id}")
                    .setTime(DateUtil.getCurentDateStr(PATTERN));
        }
    },
    HUOSHOU_PENDING_RECOVERTOAREA_NOTIFICATION(14) {
        @Override
        public OrderNoticeRes getOrderNotice(Integer subNumber) {
            OrderNoticeRes result = new OrderNoticeRes();
            return result.setUnKey("14")
                    .setTitle(MESSAGE
                            + subNumber + "]个回收商品待发回总部")
                    .setLink(SaasManagerUtils.getMoaUrl() + "/app/native/inventory/allocationGoodProducts?type=1")
                    .setTime(DateUtil.getCurentDateStr(PATTERN));
        }
    },
    ZHUANSHOU_BORROW_ORDER_NOTIFICATION(9) {
        @Override
        public OrderNoticeRes getOrderNotice(Integer subNumber) {
            OrderNoticeRes result = new OrderNoticeRes();
            return result.setUnKey("9")
                    .setTitle(MESSAGE
                            + subNumber + "]个内部借用渠道需要跟进")
                    .setLink(SaasManagerUtils.getMoaUrl() + "/app/native/orderMatterPanel?isMenu=true")
                    .setTime(DateUtil.getCurentDateStr(PATTERN));
        }
    },
    ZHUANSHOU_BUY_ORDER_NOTIFICATION(10) {
        @Override
        public OrderNoticeRes getOrderNotice(Integer subNumber) {
            OrderNoticeRes result = new OrderNoticeRes();
            return result.setUnKey("10")
                    .setTitle(MESSAGE
                            + subNumber + "]个内部购买渠道需要跟进")
                    .setLink(SaasManagerUtils.getMoaUrl() + "/app/native/orderMatterPanel?isMenu=true")
                    .setTime(DateUtil.getCurentDateStr(PATTERN));
        }
    },
    ZHUANSHOU_RANSOM_ORDER_NOTIFICATION(11) {
        @Override
        public OrderNoticeRes getOrderNotice(Integer subId) {
            OrderNoticeRes result = new OrderNoticeRes();
            return result.setUnKey("11")
                    .setTitle("回收用户发起回购订单["+subId+"]，请及时跟进")
                    .setLink(SaasManagerUtils.getMoaUrl() + "/mstockout/editOrder?SubID="+subId)
                    .setTime(DateUtil.getCurentDateStr(PATTERN));
        }
    }

    ;

    private static final String MESSAGE = "您目前有[";
    private static final String PATTERN = "yyyy-MM-dd HH:mm:ss";
    private final Integer code;

    OrderNoticeEnum(Integer code) {
        this.code = code;
    }

    public static OrderNoticeEnum getByValue(int value) {
        for (OrderNoticeEnum code : values()) {
            if (code.getCode() == value) {
                return code;
            }
        }
        return null;
    }

    public abstract OrderNoticeRes getOrderNotice(Integer subNumber);
}
