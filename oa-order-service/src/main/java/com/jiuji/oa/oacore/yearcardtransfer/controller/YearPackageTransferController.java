package com.jiuji.oa.oacore.yearcardtransfer.controller;

import cn.hutool.core.util.ObjectUtil;
import com.jiuji.oa.oacore.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.yearcardtransfer.dto.YearPackageTransferClaimDto;
import com.jiuji.oa.oacore.yearcardtransfer.dto.YearPackageTransferDetailDto;
import com.jiuji.oa.oacore.yearcardtransfer.dto.YearPackageTransferCancelDto;
import com.jiuji.oa.oacore.yearcardtransfer.service.IYearPackageTransferService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.annotation.LogRecordAround;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

@RestController
@RequestMapping("/year-package-transfer")
public class YearPackageTransferController {

    @Resource
    private IYearPackageTransferService yearPackageTransferService;

    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;

    /**
     * 获取转赠明细信息
     * @param transferCode
     * @param userId
     * @return
     */
    @GetMapping("/transfer-detail")
    public R<YearPackageTransferDetailDto> getTransferDetail(
            @RequestParam(required = true) String transferCode,
            @RequestParam(required = false) Integer userId) {
        YearPackageTransferDetailDto detail = yearPackageTransferService.getTransferDetail(transferCode,
                ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), userId));
        return R.success(detail);
    }

    /**
     * 获取转赠明细信息
     */
    @GetMapping("/list-transfer-detail")
    public R<List<YearPackageTransferDetailDto>> listTransferDetail(
            @RequestParam("bindBasketId") Collection<Integer> bindBasketIds,
            @RequestParam(required = false) Integer userId){
        List<YearPackageTransferDetailDto> result = yearPackageTransferService.listTransferDetail(bindBasketIds,
                ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), userId));
        return R.success(result);
    }

    /**
     * 领取转赠
     * @param req
     * @return
     */
    @PostMapping("/claim")
    @RepeatSubmitCheck(expression = "#{packageFullName}:#{methodSignName}:#{req.transferCode}", message = "该转赠码已经被领取")
    public R<YearPackageTransferDetailDto> claimTransfer(@Valid @RequestBody YearPackageTransferClaimDto req) {
        YearPackageTransferDetailDto result = yearPackageTransferService.claimTransfer(req.getTransferCode(),
                ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), req.getUserId()));
        return R.success(result);
    }

    /**
     * 撤销赠送
     * @param req
     * @return
     */
    @LogRecordAround(value = "贴膜赠送撤销")
    @PostMapping("/revoke")
    @RepeatSubmitCheck(expression = "#{packageFullName}:#{methodSignName}:#{req.transferCode}", message = "该转赠码正在处理中")
    public R<Boolean> revokeTransfer(@Valid @RequestBody YearPackageTransferCancelDto req) {
        return yearPackageTransferService.revokeTransfer(req);
    }


}
