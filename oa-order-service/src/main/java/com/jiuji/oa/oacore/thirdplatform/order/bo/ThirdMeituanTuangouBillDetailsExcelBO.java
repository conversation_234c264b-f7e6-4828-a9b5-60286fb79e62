package com.jiuji.oa.oacore.thirdplatform.order.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 美团团购账单明细
 */
@Data
public class ThirdMeituanTuangouBillDetailsExcelBO implements Serializable {
    /**
     * 账户名称
     */
    @ExcelProperty("账户名称")
    private String accountName;

    /**
     * 收益类型
     */
    @ExcelProperty("收益类型")
    private String incomeType;

    /**
     * 业务类型
     */
    @ExcelProperty("业务类型")
    private String businessType;

    /**
     * 订单类型
     */
    @ExcelProperty("订单类型")
    private String orderType;

    /**
     * 券号
     */
    @ExcelProperty("券号")
    private String couponCode;

    /**
     * 下单时间
     */
    @ExcelProperty("下单时间")
    private String orderTime;

    /**
     * 验券/退款/调整时间
     */
    @ExcelProperty("验券/退款/调整时间")
    private String verifyRefundAdjustTime;

    /**
     * 团购ID/通兑券ID
     */
    @ExcelProperty("团购ID/通兑券ID")
    private String groupBuyId;

    /**
     * 套餐名
     */
    @ExcelProperty("套餐名")
    private String packageName;

    /**
     * 消费门店Id
     */
    @ExcelProperty("消费门店Id")
    private String consumeStoreId;

    /**
     * 消费门店
     */
    @ExcelProperty("消费门店")
    private String consumeStore;

    /**
     * 特殊账期业务
     */
    @ExcelProperty("特殊账期业务")
    private String specialAccountPeriodBusiness;

    /**
     * 入账日期
     */
    @ExcelProperty("入账日期")
    private String accountDate;

    /**
     * 打款状态
     */
    @ExcelProperty("打款状态")
    private String paymentStatus;

    /**
     * 打款单号
     */
    @ExcelProperty("打款单号")
    private String paymentNumber;

    /**
     * 结算价(总收入-美团点评技术服务费-商家营销费用-消费后退-其他调整)（元）
     */
    @ExcelProperty("结算价(总收入-美团点评技术服务费-商家营销费用-消费后退-其他调整)（元）")
    private String settlementPrice;

    /**
     * 总收入（元）
     */
    @ExcelProperty("总收入（元）")
    private String totalIncome;

    /**
     * 技术服务费费率
     */
    @ExcelProperty("技术服务费费率")
    private String techServiceFeeRate;

    /**
     * 平台技术服务费（元）
     */
    @ExcelProperty(value = "平台技术服务费（元）")
    private String techServiceFee;

    /**
     * 商家营销费用（元）
     */
    @ExcelProperty(value = "商家营销费用（元）")
    private String merchantMarketingFee;

    /**
     * 已消费后退款（元）
     */
    @ExcelProperty(value = "已消费后退款（元）")
    private String consumedRefund;

    /**
     * 其他调整（元）
     */
    @ExcelProperty(value = "其他调整（元）")
    private String otherAdjustments;

    /**
     * 自配打包费（元）
     */
    @ExcelProperty(value = "自配打包费（元）")
    private String selfPackingFee;

    /**
     * 自配送费（元）
     */
    @ExcelProperty(value = "自配送费（元）")
    private String selfDeliveryFee;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;

    /**
     * 账单周期
     */
    @ExcelProperty(value = "账单周期")
    private String billingCycle;

    private static final long serialVersionUID = 1L;
}