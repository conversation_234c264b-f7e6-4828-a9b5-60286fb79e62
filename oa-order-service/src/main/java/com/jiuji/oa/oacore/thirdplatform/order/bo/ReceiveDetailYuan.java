package com.jiuji.oa.oacore.thirdplatform.order.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 订单维度的商家对账信息
 * <AUTHOR>
 */
@Data
public class ReceiveDetailYuan implements Serializable {
    private static final long serialVersionUID = 1905122041950251207L;

    /**
     * 商品分成，即平台服务费
     */
    private Double foodShareFeeChargeByPoi;

    /**
     * 配送费
     */
    private Double logisticsFee;

    /**
     * 在线支付款
     */
    private Double onlinePayment;

    /**
     * 商家应收
     */
    private Double poiReceive;

    /**
     * 国家补贴垫资
     */
    private GovernmentCharge governmentCharge;


    /**
     * 技术服务费
     */
    private Double productShareFeeChargeByPoi;

    /**
     * 美团承担明细
     */
    private List<OrderChargeByMt> actOrderChargeByMt;

    /**
     * 商家承担明细
     */
    private List<OrderChargeByPoi> actOrderChargeByPoi;


    /**
     * 履约服务费
     */
    private ReconciliationExtrasMt reconciliationExtras;

}
