package com.jiuji.oa.oacore.thirdplatform.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 良品上架状态
 */
@Getter
@AllArgsConstructor
public enum LogisticsIdMeiTuanEnum {
    JOIN(5, "加盟"),
    CITY_AGENCY(8, "城市代理"),
    EXPRESS_DELIVERY(101, "快送"),
    MIXED_DELIVERY(102, "混合送（专送+快送）"),
    CITY_DELIVERY(103, "全城送"),
    MIXED_FRANCHISE_DELIVERY(301, "混合加盟"),
    MIXED_SELF_DELIVERY(302, "混合自建"),
    MIXED_EXPRESS_DELIVERY(303, "混合快送"),
    SPECIAL_DELIVERY(1007, "专送")

    ;


    private int code;
    private String desc;

    /**
     * 获取到店自取的物流方式
     * @return
     */
    public static List<Integer> getSelfToShop() {
        return Arrays.asList(LogisticsIdMeiTuanEnum.JOIN.getCode(), LogisticsIdMeiTuanEnum.CITY_AGENCY.getCode(), LogisticsIdMeiTuanEnum.EXPRESS_DELIVERY.getCode(),
                LogisticsIdMeiTuanEnum.MIXED_DELIVERY.getCode(),LogisticsIdMeiTuanEnum.CITY_DELIVERY.getCode(),
                LogisticsIdMeiTuanEnum.MIXED_SELF_DELIVERY.getCode(), LogisticsIdMeiTuanEnum.MIXED_FRANCHISE_DELIVERY.getCode(),
                LogisticsIdMeiTuanEnum.MIXED_EXPRESS_DELIVERY.getCode(), SPECIAL_DELIVERY.getCode());
    }

}
