package com.jiuji.oa.oacore.thirdplatform.order.service;

import com.jiuji.oa.oacore.thirdplatform.order.vo.meituan.MeituanDownloadBillFileReq;
import com.jiuji.oa.oacore.thirdplatform.order.vo.meituan.MeituanFinanceCurlReq;
import com.jiuji.tc.common.vo.R;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

public interface MeiTuanFinanceService {
    /**
     * 从currl中获取美团header
     * @param req
     * @return
     */
    R getHeadersByCurl(MeituanFinanceCurlReq req);

    /**
     * 生成美团账单导出任务
     * @return
     */
    R createBillExportTask(String appKey);

    /**
     * 生成美团账单导出任务
     * @return
     */
    R createBillExportTask(String appKey, String beginDate, String endDate);

    void getBillDownloadList(String appKey, String taskNo);

    /**
     * 处理美团账单
     * @return
     */
    R<String> handleMeiTuanFinanceBill();

    /**
     * 导入美团账单文件
     * @param file
     * @return
     */
    Map<String, Object> importBillFile(MultipartFile file);

    void downloadBillFile(MeituanDownloadBillFileReq req);

    R saveUploadedMeituanFinanceFile(MultipartFile file);
}
