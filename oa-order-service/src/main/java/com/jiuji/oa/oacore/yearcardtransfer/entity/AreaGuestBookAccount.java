package com.jiuji.oa.oacore.yearcardtransfer.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 苹果GB账号配置
 * <AUTHOR>
 */
@Data
@TableName("area_guestbook_account")
public class AreaGuestBookAccount {
    /**
     * 主键ID
     */
    @JsonIgnore
    @TableId(value = "config_id", type = IdType.AUTO)
    private Integer configId;

    @TableId("id")
    private Integer id;

    /**
     * 所属门店
     */
    @NotNull(message = "所属门店不能为空")
    @TableField(value = "show_area_id")
    private Integer showAreaId;

    /**
     * 所属门店编码
     */
    @NotBlank(message = "所属门店编码不能为空")
    @TableField(value = "show_area_code")
    private String showAreaCode;

    /**
     * 关联门店 多个逗号间隔
     */
    @TableField("affiliated_area_id")
    private String affiliatedAreaId;

    /**
     *目前不清楚这个字段意义，按照历史数据的规律
     * 取 show_area_code 字段填充进去
     */
    @TableField("search_area_code")
    private String searchAreaCode;

    /**
     *账号
     */
    @NotBlank(message = "账号不能为空")
    @TableField("guestbook_account")
    private String guestBookAccount;
    /**
     *密码
     */
    @NotBlank(message = "密码不能为空")
    @TableField("guestbook_password")
    private String guestBookPassword;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 添加人
     */
    @TableField("in_user")
    private String inUser;

    /**
     * 启用禁用
     */
    @TableField("enable_status")
    private Boolean enableStatus;

    @TableLogic
    @TableField("del_flag")
    private Boolean delFlag;







}
