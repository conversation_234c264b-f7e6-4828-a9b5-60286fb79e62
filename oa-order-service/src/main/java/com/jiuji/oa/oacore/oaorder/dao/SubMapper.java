package com.jiuji.oa.oacore.oaorder.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.oacore.brand.dji.vo.SalesInfoVo;
import com.jiuji.oa.oacore.brand.vo.SalesVO;
import com.jiuji.oa.oacore.oaorder.bo.CheckOrderUserBO;
import com.jiuji.oa.oacore.oaorder.bo.NetOrderInfoBO;
import com.jiuji.oa.oacore.oaorder.bo.SubDataBO;
import com.jiuji.oa.oacore.oaorder.po.Basket;
import com.jiuji.oa.oacore.oaorder.po.Sub;
import com.jiuji.oa.oacore.oaorder.po.SubCollection;
import com.jiuji.oa.oacore.oaorder.req.CheckOrderUserReq;
import com.jiuji.oa.oacore.oaorder.res.DeviceRes;
import com.jiuji.oa.oacore.oaorder.res.EvaluateScoreBO;
import com.jiuji.oa.oacore.oaorder.vo.req.SubReq;
import com.jiuji.oa.oacore.server.entity.ServiceRecord;
import com.jiuji.oa.oacore.weborder.res.BasketSearchRes;
import com.jiuji.oa.oacore.weborder.res.TaxSubInfoRes;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * description: <Sub表对应的Mapper>
 * translation: <Mapper corresponding to Sub table>
 *
 * <AUTHOR>
 * @date 14:55 2019/11/11
 * @since 1.0.0
 **/
@Mapper
public interface SubMapper extends BaseMapper<Sub> {


    /**
     * 更具basketId 查询订单
     * @param basketId
     * @return
     */
    Sub selectSubByBasketId(@Param("basketId") Integer basketId);
    /**
     * description: <获取未完成商品网单>
     * translation: <Get unfinished product online order>
     *
     * @param areaId   地区Id
     * @param subCheck 处理类别
     * @return java.util.List<com.jiuji.oa.oacore.oaorder.bo.NetOrderInfoBO>
     * <AUTHOR>
     * @date 10:08 2020/3/27
     * @since 1.0.0
     **/
    List<NetOrderInfoBO> listUnfinishedNetworkSub(@Param("areaId") Integer areaId,
                                                  @Param("subCheck") Integer subCheck);

    /**
     * description: <获取未完成良品网单>
     * translation: <Get Unfinished Goods online order>
     *
     * @param areaId   地区Id
     * @param subCheck 处理类别
     * @return java.util.List<com.jiuji.oa.oacore.oaorder.bo.NetOrderInfoBO>
     * <AUTHOR>
     * @date 10:09 2020/3/27
     * @since 1.0.0
     **/
    List<NetOrderInfoBO> listUnfinishedNetworkSubGoodProduct(@Param("areaId") Integer areaId,
                                                             @Param("subCheck") Integer subCheck);

    /**
     * description: <获取未处理网单数量>
     * translation: <Get the number of unprocessed net orders>
     *
     * @param areaId 地区Id
     * @return java.util.List<com.jiuji.oa.oacore.oaorder.bo.NetOrderCountBO>
     * <AUTHOR>
     * @date 10:05 2020/3/27
     * @since 1.0.0
     **/
    List<Integer> getNetworkSubOrderCount(@Param("areaId") Integer areaId, @Param("isOverTime") boolean isOverTime);

    List<Integer> getNetworkSubOrderCountV2(@Param("areaId") Integer areaId, @Param("isOverTime") boolean isOverTime,
                                            @Param("timeStamp") Long timeStamp);

    Map isSendArea(@Param("areaId") Integer areaId);

    /**
     * 根据单号获取线下-整体订单相关员工
     *
     * @param subId 单号
     * @return 员工id
     */
    Integer getXianXiaAllUserId(Integer subId);

    /**
     * 根据单号获取线下-整体订单相关员工姓名
     *
     * @param subId 单号
     * @return 员工姓名
     */
    String getXianXiaAllUsername(Integer subId);

    /**
     * 获取交易员
     *
     * @param subId 订单id
     * @return 交易员姓名
     */
    String getTrader(Integer subId);

    /**
     * OA进行判断在这个时间内此用户是否产生以下条件的订单：
     * 1、商品ID相同
     * 2、状态为已确认、已出库、已完成的
     * 3、订单类型为：良品、优品、新机
     *
     * @param checkOrderUserReq
     * @return
     */
    List<CheckOrderUserBO> getOrderUserComplete(@Param("req") CheckOrderUserReq checkOrderUserReq);

    /**
     * 获取订单关注人员记录
     *
     * @param subIds
     * @param type
     * @return
     */
    List<SubCollection> getSubCollentUser(@Param("subIds") List<Long> subIds, @Param("type") Integer type);

    /**
     * 是否有催单
     *
     * @param subId
     * @param comment
     * @return
     */
    int getIsRemind(@Param("subId") Long subId, @Param("comment") String comment);

    /**
     * 添加催单
     *
     * @param subId
     * @param comment
     * @param mobile
     * @return
     */
    int addRemind(@Param("subId") Long subId, @Param("comment") String comment, @Param("mobile") String mobile);

    HashMap<String, String> getInfo(@Param("subId") long subId);

    @DS("oanew")
    List<Integer> getUserIds(@Param("ids") List<Integer> ids, @Param("subId") long subId);

    String getSubTrader(@Param("subId") Integer subId);

    Integer getAreaId(@Param("subId") Integer subId);

    IPage<SubDataBO> getSubData(@Param("page") Page<SubDataBO> page, @Param("smallShop") boolean smallShop, @Param("zitidianId") Integer zitidianId,
                                @Param("userId") String userId, @Param("code") Integer code,
                                @Param("subIds") List<String> subIds, @Param("type") int type,
                                @Param("xtenant") long xtenant, @Param("isweixiu") String isweixiu, @Param("recover") boolean recover);

    List<EvaluateScoreBO> getEvaluateScore(@Param("subIds") List<Integer> subId, @Param("officeName") String officeName);

    List<BasketSearchRes> getTaoCanData(@Param("asList") List<Integer> asList);

    List<BasketSearchRes> getHaoMaData(@Param("asList") List<Integer> asList);

    List<Integer> getSubIds(SubReq subReq);

    List<Integer> getRecoverIds(SubReq subReq);

    /**
     * 大疆订单
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 订单列表
     */
    List<SalesInfoVo> listDjiSalesInfo(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime
            , @Param("areaIds") List<Integer> areaIds, @Param("djAreaIds") List<Integer> djAreaIds);

    /**
     * 小米订单
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 订单列表
     */
    List<SalesVO> listXiaomiSalesInfo(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime,
                                      @Param("cityIds") List<Integer> cityIds, @Param("cidList") List<Integer> cidList, @Param("brandIdList") List<Integer> brandIdList);


    /**
     * 小米品牌占比
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 订单列表
     */
    BigDecimal xiaomiSalesProportion(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime,
                                     @Param("cityIds") List<Integer> cityIds, @Param("cidList") List<Integer> cidList, @Param("brandIdList") List<Integer> brandIdList);

    /**
     * 进行中的订单信息
     * @param userId
     * @return
     */
    Integer existsProcessingOrderInfo(@Param("userId") Integer userId);

    /**
     * 根据订单获取basket_id
     *
     * @param subId 订单id
     */
    List<Integer> getBasketIdsBySubId(@Param("subId") int subId);

    /**
     * 根据basketId获取售后记录数量
     *
     * @param basketIdsBySubId basketIds
     * @return 数量
     */
    Integer getAfterSaleQuantity(@Param("basketIdsBySubId") List<Integer> basketIdsBySubId);

    /**
     * 根据订单id获取小件售后记录数量
     * @param subId
     * @return
     */
    Integer getSmallProQuantity(@Param("subId") int subId);

    /**
     * 获取开票的订单信息
     * @param subId 订单id
     * @param type 订单类型 {@link TaxSubInfoRes.TaxSubTypeEnum}
     * @param xtenant 租户id
     * @return
     */
    TaxSubInfoRes getTaxSubInfo(@Param("subId") Integer subId, @Param("type") Integer type, @Param("xtenant") long xtenant);

    /**
     * 获取开票的订单信息
     * @param subId 订单id
     * @param type 订单类型 {@link TaxSubInfoRes.TaxSubTypeEnum}
     * @param xtenant 租户id
     * @return
     */
    @DS("oanew_his")
    TaxSubInfoRes getTaxSubInfoHis(@Param("subId") Integer subId, @Param("type") Integer type, @Param("xtenant") long xtenant);

    /**
     * 根据门店属性统计订单量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 运营商订单量 key: 门店属性code，value: 订单量
     */
    List<Map<String, Integer>> countOrdersByAreaAttr(@Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime);

    Integer getNetworkYuyueCountCount(@Param("areaId") Integer areaId, @Param("isOverTime") boolean isOverTime,
                                      @Param("timeStamp") Long timeStamp);

    List<NetOrderInfoBO> listUnfinishedNetworkYuYue(@Param("areaId") Integer areaId, @Param("subCheck") Integer subCheck);

    /**
     * 获取网络订单-订单列表
     * @param areaId 地区Id
     * @param subCheck 订单确认状态 0 未处理 1已处理
     * @return
     */
    List<NetOrderInfoBO> listUnfinishedNetworkRecycle(@Param("areaId") Integer areaId, @Param("subCheck") Integer subCheck);

    /**
     * 追加更新订单备注信息
     * @param spickupGoodsCode
     * @param subIds
     * @return
     */
    boolean updateSubComment(@Param("spickupGoodsCode") String spickupGoodsCode,
                             @Param("subIds") List<Long> subIds);

    List<ServiceRecord> getDevicesByUser(@Param("imeiList") List<String> imeiList);


    List<DeviceRes> getSubById(@Param("subMobile") String subMobile, @Param("imei") String imei);

    List<DeviceRes.Gift> getGiftByBasketIds(@Param("bindBasketIds") List<Integer> bindBasketIds);

    Basket getBasketById(@Param("basketId") Integer basketId);

    List<Integer> getNationalSubsidy(@Param("subIdList") List<Integer> subIdList);
}
