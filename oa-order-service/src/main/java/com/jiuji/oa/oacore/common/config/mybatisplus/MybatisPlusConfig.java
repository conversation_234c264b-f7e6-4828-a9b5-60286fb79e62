package com.jiuji.oa.oacore.common.config.mybatisplus;//package com.jiuji.oa.orderservice.common.config.mybatisplus;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.parser.ISqlParser;
import com.baomidou.mybatisplus.core.injector.ISqlInjector;
import com.baomidou.mybatisplus.extension.injector.LogicSqlInjector;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.plugins.tenant.TenantHandler;
import com.baomidou.mybatisplus.extension.plugins.tenant.TenantSqlParser;
import com.jiuji.oa.oacore.common.config.xtenant.JiujiSqlServerWithNolockSqlParser;
import com.jiuji.oa.oacore.common.config.xtenant.JiujiTenantSqlParser;
import com.jiuji.oa.oacore.common.config.xtenant.OutPutSqlParser;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.config.DynamicContextHolder;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.xtenant.Namespaces;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import org.apache.ibatis.plugin.Intercepts;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.*;

/**
 * Created by Lucare.Feng on 2017/2/24.
 */
@Slf4j
@Configuration
public class MybatisPlusConfig {

    /**
     * 需要进行租户过滤的表
     */
    public static List<String> tenantTbs = new ArrayList<>();
    static {
        MybatisPlusConfig.tenantTbs.add("goldseed_distribution_config");
        MybatisPlusConfig.tenantTbs.add("goldseed_distribution_defconfig");
        MybatisPlusConfig.tenantTbs.add("goldseed_expshop_config");
        MybatisPlusConfig.tenantTbs.add("goldseed_instorage_config");
        MybatisPlusConfig.tenantTbs.add("goldseed_instorage_defconfig");
        MybatisPlusConfig.tenantTbs.add("goldseed_product_config");
        MybatisPlusConfig.tenantTbs.add("goldseed_product_except");
        MybatisPlusConfig.tenantTbs.add("goldseed_supplier_config");
    }



    @Bean
    @Primary
    public PaginationInterceptor paginationInterceptor() {
        PaginationInterceptor paginationInterceptor = new MyPaginationInterceptor();
        List<ISqlParser> sqlParserList = new ArrayList<>();
        TenantSqlParser tenantSqlParser = new JiujiTenantSqlParser();
        tenantSqlParser.setTenantHandler(new TenantHandler() {
            @Override
            public Expression getTenantId() {
                return new LongValue(Namespaces.get());
            }

            @Override
            public String getTenantIdColumn() {
                return "xtenant";
            }

            @Override
            public boolean doTableFilter(String tableName) {
                //如果隔离大于共享,就用黑名单.
                // 这里添加白名单,哪张支持多租户字段,配置哪个
                return !CommonUtil.containIgnoreCase(tenantTbs, tableName);
            }
        });
        OutPutSqlParser outPutSqlParser =  new OutPutSqlParser();
        sqlParserList.add(outPutSqlParser);
        sqlParserList.add(tenantSqlParser);
        ISqlParser nolockSqlParser = new JiujiSqlServerWithNolockSqlParser();
        sqlParserList.add(nolockSqlParser);
        paginationInterceptor.setSqlParserList(sqlParserList);
        return paginationInterceptor;
    }

    /**
     * 动态表名拦截器 - 用于处理attachments表名动态变化
     * 在3.1.0版本中，我们使用自定义拦截器实现动态表名功能
     */
    @Bean
    public DynamicTableNameInterceptor dynamicTableNameInterceptor() {
        DynamicTableNameInterceptor interceptor = new DynamicTableNameInterceptor();
        return interceptor;
    }



    /**
     * 注入主键生成器
     */
  /*  @Bean
    public IKeyGenerator keyGenerator() {
        return new DB2KeyGenerator();
    }*/

    /**
     * 注入sql注入器
     */
    @Bean
    public ISqlInjector sqlInjector() {
        return new LogicSqlInjector();
    }

}
