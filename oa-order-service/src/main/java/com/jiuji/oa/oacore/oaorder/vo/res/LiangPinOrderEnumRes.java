package com.jiuji.oa.oacore.oaorder.vo.res;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/11/22 16:19
 * @Description
 */

@Data
@Accessors(chain = true)
public class LiangPinOrderEnumRes implements Serializable {
    private static final long serialVersionUID = -3656692708140368452L;
    private String label;
    private String name;
    private Boolean ismulti;
    private Object value;
}