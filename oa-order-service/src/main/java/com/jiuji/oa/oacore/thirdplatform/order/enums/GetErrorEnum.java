package com.jiuji.oa.oacore.thirdplatform.order.enums;

import lombok.Getter;

/**
 * 美团国补SN查询接口错误码枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum GetErrorEnum {
    /**
     * 缺少参数，数据不完整
     */
    PARAM_MISSING(701, "缺少参数，数据不完整，请补充完整入参"),
    /**
     * 不存在此订单
     */
    ORDER_NOT_EXIST(806, "不存在此订单或查询时效已过期"),
    /**
     * 该订单不是国补/团补订单
     */
    NOT_ALLOWED_ORDER(1199, "该订单不是国补/团补订单，不允许调用此接口"),
    /**
     * 获取需上传的SN、IMEI信息异常
     */
    GET_INFO_ERROR(1201, "获取需上传的SN、IMEI信息异常，请重试"),
    /**
     * 订单尚未支付
     */
    ORDER_NOT_PAID(1204, "订单尚未支付，请稍后重试");
    
    private final Integer code;
    private final String message;
    
    GetErrorEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    
    /**
     * 根据错误码获取错误信息
     * @param code 错误码
     * @return 错误信息
     */
    public static String getMessage(Integer code) {
        for (GetErrorEnum errorEnum : GetErrorEnum.values()) {
            if (errorEnum.getCode().equals(code)) {
                return errorEnum.getMessage();
            }
        }
        return "未知错误，错误码: " + code;
    }
} 