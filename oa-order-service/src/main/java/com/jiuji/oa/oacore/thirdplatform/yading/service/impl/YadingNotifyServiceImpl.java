package com.jiuji.oa.oacore.thirdplatform.yading.service.impl;


import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.utils.Exceptions;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.oacore.common.enums.OaMesTypeEnum;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.oaorder.po.Basket;
import com.jiuji.oa.oacore.oaorder.po.BasketExtend;
import com.jiuji.oa.oacore.oaorder.service.BasketService;
import com.jiuji.oa.oacore.oaorder.service.IBasketExtendService;
import com.jiuji.oa.oacore.server.service.IServiceRecordService;
import com.jiuji.oa.oacore.thirdplatform.productconfig.bo.Ch999UserYadingBO;
import com.jiuji.oa.oacore.thirdplatform.yading.bo.YadingCallbackBo;
import com.jiuji.oa.oacore.thirdplatform.yading.dto.YadingCallbackDto;
import com.jiuji.oa.oacore.thirdplatform.yading.entity.YadingNotify;
import com.jiuji.oa.oacore.thirdplatform.yading.entity.YadingServiceRecord;
import com.jiuji.oa.oacore.thirdplatform.yading.entity.YadingServiceState;
import com.jiuji.oa.oacore.thirdplatform.yading.enums.YadingCallbackMethodEnum;
import com.jiuji.oa.oacore.thirdplatform.yading.enums.YadingNotifyHandleStateEnum;
import com.jiuji.oa.oacore.thirdplatform.yading.enums.YadingRegisterStateEnum;
import com.jiuji.oa.oacore.thirdplatform.yading.enums.YadingResultEnum;
import com.jiuji.oa.oacore.thirdplatform.yading.mapper.YadingNotifyMapper;
import com.jiuji.oa.oacore.thirdplatform.yading.mapper.YadingUserMapper;
import com.jiuji.oa.oacore.thirdplatform.yading.service.IYadingNotifyService;
import com.jiuji.oa.oacore.thirdplatform.yading.service.IYadingServiceRecordService;
import com.jiuji.oa.oacore.thirdplatform.yading.service.IYadingServiceStateService;
import com.jiuji.oa.oacore.thirdplatform.yading.util.DateUtil;
import com.jiuji.oa.oacore.thirdplatform.yading.util.WXPayConstants;
import com.jiuji.oa.oacore.thirdplatform.yading.util.WXPayUtil;
import com.jiuji.oa.oacore.thirdplatform.yading.util.YadingUtil;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */
@Slf4j
@Service
@DS("smallpro_write")
public class YadingNotifyServiceImpl extends ServiceImpl<YadingNotifyMapper, YadingNotify> implements IYadingNotifyService {


    @Value("${yading.sign-key:}")
    private String yadingSignKey;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private SubLogsCloud subLogsCloud;

    @Autowired
    private SmsService smsService;

    @Resource
    private IBasketExtendService basketExtendService;

    @Resource
    private BasketService basketService;

    @Resource
    private YadingUserMapper yadingUserMapper;

    @Resource
    private IServiceRecordService serviceRecordService;
    @Resource
    private IYadingServiceRecordService yadingServiceRecordService;
    @Resource
    private IYadingServiceStateService yadingServiceStateService;

    @Override
    public YadingResultEnum yadingNotifyHandle(YadingNotify yadingNotify) {
        // 验签
        YadingResultEnum resultEnum = verifySign(yadingNotify);
        if (!resultEnum.equals(YadingResultEnum.SUCCESS)) {
            // 如果没有成功，则记录数据并return
            yadingNotify.setFailCause(resultEnum.getMsg());
            yadingNotify.setHandleState(YadingNotifyHandleStateEnum.HANDLE_FAILED1.getValue());
            this.updateById(yadingNotify);
            return resultEnum;
        }
        // 解析参数
        YadingCallbackDto allParamDto = packageCallbackParam(yadingNotify);
        // 校验业务类型是否存在
        YadingCallbackMethodEnum methodEnum = allParamDto.getMethodEnum();
        if (allParamDto.getMethodEnum() == null) {
            yadingNotify.setFailCause(YadingResultEnum.UNKNOWN_METHOD.getMsg());
            yadingNotify.setHandleState(YadingNotifyHandleStateEnum.HANDLE_FAILED1.getValue());
            this.updateById(yadingNotify);
            return YadingResultEnum.UNKNOWN_METHOD;
        }
        // 校验业务单号是否存在
        YadingServiceRecord serviceRecord = yadingServiceRecordService.getByServiceDetailId(allParamDto.getSubId());
        if (serviceRecord == null) {
            yadingNotify.setFailCause(YadingResultEnum.UNKNOWN_SUBID.getMsg());
            yadingNotify.setHandleState(YadingNotifyHandleStateEnum.HANDLE_FAILED1.getValue());
            this.updateById(yadingNotify);
            return YadingResultEnum.UNKNOWN_SUBID;
        }
        // 校验审核状态是否为空 除提交成功通知外，其他通知都需要审核状态
        if (!methodEnum.equals(YadingCallbackMethodEnum.REGISTER_SUBMIT) && allParamDto.getCheckStatus() == null) {
            yadingNotify.setFailCause(YadingResultEnum.UNKNOWN_CHECK_STATUS.getMsg());
            yadingNotify.setHandleState(YadingNotifyHandleStateEnum.HANDLE_FAILED1.getValue());
            this.updateById(yadingNotify);
            return YadingResultEnum.UNKNOWN_CHECK_STATUS;
        }
        // 校验公司编号
        if (!YadingUtil.getGroupCode().equals(allParamDto.getCompanyCode())) {
            yadingNotify.setFailCause(YadingResultEnum.COMPANY_CODE_ERROR.getMsg());
            yadingNotify.setHandleState(YadingNotifyHandleStateEnum.HANDLE_FAILED1.getValue());
            this.updateById(yadingNotify);
            return YadingResultEnum.COMPANY_CODE_ERROR;
        }

        YadingServiceState yadingServiceState = yadingServiceStateService
                .lambdaQuery()
                .eq(YadingServiceState::getId, serviceRecord.getId())
                .one();
        if (yadingServiceState == null) {
            yadingServiceState = new YadingServiceState();
            yadingServiceState.setId(Convert.toLong(serviceRecord.getId()));
            yadingServiceState.setRegisterState(YadingRegisterStateEnum.UNREGISTERED.getValue());
            yadingServiceStateService.save(yadingServiceState);
        }

        // 处理业务
        YadingCallbackBo callbackBo = new YadingCallbackBo();
        callbackBo.setYadingNotify(yadingNotify);
        callbackBo.setParamDto(allParamDto);
        callbackBo.setServiceRecord(serviceRecord);
        try {
            IYadingNotifyService currentProxy = (IYadingNotifyService) AopContext.currentProxy();
            switch (methodEnum) {
                case REGISTER_SUBMIT:
                    // 注册提交成功
                    currentProxy.doRegisterSubmit(callbackBo);
                    break;
                case REGISTER_CHECK:
                    // 注册单审核
                    currentProxy.doRegisterCheck(callbackBo);
                    break;
                case CLAIM_CHECK:
                    // 理赔单审核
                    break;
                case RENEW_CHECK:
                    // 换机完成审核
                    break;
                case CHANGE_IMEI_CHECK:
                    // 更换IMEI审核
                    break;
                case RESUPPLY_CHECK:
                    // 照片补交审核
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.warn("处理亚丁回调信息异常", e);
            yadingNotify.setFailCause(Exceptions.getStackTraceAsString(e));
            yadingNotify.setHandleState(YadingNotifyHandleStateEnum.HANDLE_FAILED2.getValue());
            this.updateById(yadingNotify);
        }
        return YadingResultEnum.SUCCESS;
    }


    /**
     * @Description 定时任务重新消费处理失败的回调
     * <AUTHOR>
     * @Date 2022/5/24 11:22
     */
    @Override
    public int reloadCronTask() {
        List<YadingNotify> notifyList = this.lambdaQuery()
                // 处理状态不等于成功的
                .ne(YadingNotify::getHandleState, YadingNotifyHandleStateEnum.HANDLE_SUCCESSFUL.getValue())
                // 重试次数小于10的
                .lt(YadingNotify::getRetries, 10)
                .list();
        if (CollectionUtils.isNotEmpty(notifyList)) {
            for (YadingNotify yadingNotify : notifyList) {
                // 重新处理回调数据
                this.yadingNotifyHandle(yadingNotify);
                // 对重试次数加1
                int retries = Optional.ofNullable(yadingNotify.getRetries()).orElse(0);
                this.lambdaUpdate()
                        .eq(YadingNotify::getId, yadingNotify.getId())
                        .set(YadingNotify::getRetries, retries + 1)
                        .update();
            }
        }
        return notifyList.size();
    }

    /**
     * @Description 注册提交成功
     * @Param ydStateId
     * <AUTHOR>
     * @Date 21:31 2022/5/12
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doRegisterSubmit(YadingCallbackBo callbackBo) {
        YadingNotify yadingNotify = callbackBo.getYadingNotify();
        YadingCallbackDto paramDto = callbackBo.getParamDto();
        YadingServiceRecord serviceRecord = callbackBo.getServiceRecord();
        // 修改状态为审核中
        YadingServiceState ydState = yadingServiceStateService.getById(serviceRecord.getId());
        // 校验数据
        if (YadingRegisterStateEnum.REGISTERED.getValue().equals(ydState.getRegisterState())) {
            // 如果已经审核通过，服务生效中,那就不要修改为提交成功了
            this.lambdaUpdate().eq(YadingNotify::getId, yadingNotify.getId())
                    .set(YadingNotify::getFailCause, YadingResultEnum.REPETITION_SUBMIT_REGISTER.getMsg())
                    .set(YadingNotify::getServiceStateId, ydState.getId())
                    .update();
            return;
        }
        // 更新数据
        yadingServiceStateService.lambdaUpdate().eq(YadingServiceState::getId, ydState.getId())
                .set(YadingServiceState::getRegisterState, YadingRegisterStateEnum.REGISTING.getValue())
                .set(YadingServiceState::getRegisterSubmitTime, paramDto.getSubmitTime())
                .update();
        // 更新处理状态
        updateNotifySuccess(callbackBo);
        SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
        subLogsNewReq.setComment("九讯服务" + paramDto.getMethodEnum().getLabel());
        subLogsNewReq.setDTime(LocalDateTime.now());
        subLogsNewReq.setSubId(serviceRecord.getSubId());
        subLogsNewReq.setInUser("系统");
        subLogsNewReq.setType(1);
        subLogsNewReq.setShowType(false);
        CompletableFuture.runAsync(() -> subLogsCloud.addSubLog(subLogsNewReq));
    }

    /**
     * @Description 注册审核通知
     * @Param dto
     * @Param serviceRecord
     * <AUTHOR>
     * @Date 21:31 2022/5/12
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doRegisterCheck(YadingCallbackBo callbackBo) {
        YadingCallbackDto paramDto = callbackBo.getParamDto();
        YadingServiceRecord serviceRecord = callbackBo.getServiceRecord();
        YadingRegisterStateEnum stateEnum = null;
        Boolean checkStatus = paramDto.getCheckStatus();
        String comment = "";
        if (Boolean.TRUE.equals(checkStatus)) {
            stateEnum = YadingRegisterStateEnum.REGISTERED;
            comment = "九讯服务注册" + stateEnum.getYdLabel();
        } else {
            stateEnum = YadingRegisterStateEnum.UNAPPROVE;
            comment = "九讯服务注册" + stateEnum.getYdLabel() + ",原因:" + paramDto.getRemark()
                    + ",请即时跟进重新注册";
        }
        // 修改状态为审核通过或者审核拒绝
        YadingServiceState ydState = yadingServiceStateService.getById(serviceRecord.getId());
        ydState.setRegisterState(YadingRegisterStateEnum.REGISTING.getValue());
        LambdaUpdateChainWrapper<YadingServiceState> wapper = yadingServiceStateService.lambdaUpdate();
        wapper.eq(YadingServiceState::getId, ydState.getId())
                .set(YadingServiceState::getRegisterState, stateEnum.getValue())
                .set(YadingServiceState::getRegisterCheckTime, paramDto.getCheckTime())
                .set(YadingServiceState::getRegisterRemark, paramDto.getRemark());
        if (Objects.nonNull(paramDto.getEfficientStartDate())) {
            wapper.set(YadingServiceState::getEfficientStartDate, paramDto.getEfficientStartDate());
        }
        if (Objects.nonNull(paramDto.getEfficientEndDate())) {
            wapper.set(YadingServiceState::getEfficientEndDate, paramDto.getEfficientEndDate());
        }
        wapper.update();

        //更新服务表
        LambdaUpdateChainWrapper<YadingServiceRecord> serviceRecordLambdaUpdateChainWrapper = yadingServiceRecordService.lambdaUpdate();
        serviceRecordLambdaUpdateChainWrapper.eq(YadingServiceRecord::getId, ydState.getId());
        if (Objects.nonNull(paramDto.getEfficientStartDate())
                && Objects.nonNull(paramDto.getEfficientEndDate())) {
            serviceRecordLambdaUpdateChainWrapper.set(YadingServiceRecord::getStarttime, paramDto.getEfficientStartDate());
            serviceRecordLambdaUpdateChainWrapper.set(YadingServiceRecord::getEndtime, paramDto.getEfficientEndDate());
            serviceRecordLambdaUpdateChainWrapper.update();
        }
        //回传更新成本
        if (Objects.nonNull(paramDto.getCostPrice())) {
            basketExtendService.lambdaUpdate().eq(BasketExtend::getBasketId, serviceRecord.getBasketId())
                    .set(BasketExtend::getThirdPlatformInPrice, paramDto.getCostPrice())
                    .update();
            //更新出库成本
            basketService.lambdaUpdate().eq(Basket::getBasketId, serviceRecord.getBasketId())
                    .set(Basket::getInprice, paramDto.getCostPrice())
                    .update();
            //记录日志: 亚丁服务回调,出库成本改为
            SubLogsNewReq subLogsNewReq = LambdaBuild.create(new SubLogsNewReq())
                    .set(SubLogsNewReq::setComment, StrUtil.format("亚丁服务回调,出库成本改为{},{}", paramDto.getCostPrice(),comment))
                    .set(SubLogsNewReq::setDTime, LocalDateTime.now())
                    .set(SubLogsNewReq::setSubId, serviceRecord.getSubId())
                    .set(SubLogsNewReq::setInUser, "系统")
                    .set(SubLogsNewReq::setType, 1)
                    .set(SubLogsNewReq::setShowType, false)
                    .build();
            CompletableFuture.runAsync(() -> {
                R<Boolean> logR = subLogsCloud.addSubLog(subLogsNewReq);
                if(!logR.isSuccess()){
                    RRExceptionHandler.logError("记录亚丁成本日志", subLogsNewReq, new CustomizeException(logR), smsService::sendOaMsgTo9JiMan);
                }
            });
        }

        // 更新处理状态
        updateNotifySuccess(callbackBo);

        SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
        subLogsNewReq.setComment(comment);
        subLogsNewReq.setDTime(LocalDateTime.now());
        Integer subId = serviceRecord.getSubId();
        subLogsNewReq.setSubId(subId);
        subLogsNewReq.setInUser("系统");
        subLogsNewReq.setType(1);
        subLogsNewReq.setShowType(false);
//        CompletableFuture.runAsync(() -> subLogsCloud.addSubLog(subLogsNewReq));
        Basket orderDetail;
        if (serviceRecord.getBasketIdbind() != null) {
            orderDetail = basketService.getById(serviceRecord.getBasketIdbind());
        }else {
            orderDetail = basketService.getById(serviceRecord.getBasketId());
        }
        if (Objects.nonNull(orderDetail) && StringUtils.isNotEmpty(orderDetail.getSeller())) {
            Ch999UserYadingBO syncYadingUserByName = yadingUserMapper.getSyncYadingUserByName(orderDetail.getSeller());
            if (Objects.nonNull(syncYadingUserByName) && Objects.nonNull(syncYadingUserByName.getCh999Id())) {
                Integer ch999Id = syncYadingUserByName.getCh999Id();
                String host = sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL).getData();
                String link = host + "/order/editorder?SubID=" + subId;
                smsService.sendOaMsg("您跟进的订单，单号：" + subId
                        + comment, link, ch999Id.toString(), String.valueOf(OaMesTypeEnum.DDTZ.getCode()));
            }
        }
    }

    /**
     * @Description 更新处理状态
     * @Param callbackBo
     * <AUTHOR>
     * @Date 19:26 2022/5/13
     **/
    private void updateNotifySuccess(YadingCallbackBo callbackBo) {
        // 更新处理状态
        this.lambdaUpdate().eq(YadingNotify::getId, callbackBo.getYadingNotify().getId())
                .set(YadingNotify::getHandleState, YadingNotifyHandleStateEnum.HANDLE_SUCCESSFUL.getValue())
                .set(YadingNotify::getDetailId, callbackBo.getParamDto().getSubId())
                .set(YadingNotify::getServiceStateId, callbackBo.getServiceRecord().getId())
                .update();
    }

    /**
     * @return YadingCallbackAllParamDto
     * @Description 解析参数
     * @Param yadingNotify
     * <AUTHOR>
     * @Date 21:21 2022/5/12
     **/
    private YadingCallbackDto packageCallbackParam(YadingNotify yadingNotify) {
        String originalMsg = yadingNotify.getOriginalMsg();
        JSONObject originalJson = JSON.parseObject(originalMsg);
        String methodStr = originalJson.getString(YadingCallbackDto.METHOD_ENUM);
        YadingCallbackMethodEnum methodEnum = YadingCallbackMethodEnum.ofLabel(methodStr);
        String companyCodeStr = originalJson.getString(YadingCallbackDto.COMPANY_CODE);
        String timestampStr = originalJson.getString(YadingCallbackDto.TIMESTAMP);
        String subIdStr = originalJson.getString(YadingCallbackDto.SUB_ID);

        JSONObject contentJson = originalJson.getJSONObject(YadingCallbackDto.CONTENT);
        Boolean checkStatus = contentJson.getBoolean(YadingCallbackDto.CHECK_STATUS);
        // 时间格式为13位的毫秒值时间戳
        String checkTimeStr = contentJson.getString(YadingCallbackDto.CHECK_TIME);
        String remark = contentJson.getString(YadingCallbackDto.REMARK);
        String insuranceCode = contentJson.getString(YadingCallbackDto.INSURANCE_CODE);
        String newImei = contentJson.getString(YadingCallbackDto.NEW_IMEI);
        String oldImei = contentJson.getString(YadingCallbackDto.OLD_IMEI);
        String costPrice = contentJson.getString(YadingCallbackDto.COST_PRICE);
        // 保险生效起止日期格式为 yyyy-MM-dd
        String efficientStartTimeStr = contentJson.getString(YadingCallbackDto.EFFICIENT_START_TIME);
        String efficientEndTimeStr = contentJson.getString(YadingCallbackDto.EFFICIENT_END_TIME);
        String submitTimeStr = contentJson.getString(YadingCallbackDto.SUBMIT_TIME);

        YadingCallbackDto callbackDto = new YadingCallbackDto();
        callbackDto.setMethodEnum(methodEnum);
        callbackDto.setTimestamp(convertTimestamp(timestampStr));
        callbackDto.setCompanyCode(companyCodeStr);
        callbackDto.setSubId(Long.parseLong(subIdStr));
        callbackDto.setCheckStatus(checkStatus);
        callbackDto.setCheckTime(convertTimestamp(checkTimeStr));
        callbackDto.setRemark(remark);
        callbackDto.setInsuranceCode(insuranceCode);
        callbackDto.setNewImei(newImei);
        callbackDto.setOldImei(oldImei);
        callbackDto.setCostPrice(Convert.toBigDecimal(costPrice));
        callbackDto.setEfficientStartDate(
                DateUtil.toLocalDate(efficientStartTimeStr));
        callbackDto.setEfficientEndDate(DateUtil.toLocalDate(efficientEndTimeStr));
        callbackDto.setSubmitTime(convertTimestamp(submitTimeStr));
        return callbackDto;
    }

    /**
     * @Description 验签
     * <AUTHOR>
     * @Date 19:08 2022/5/12
     **/
    private YadingResultEnum verifySign(YadingNotify yadingNotify) {
        Map<String, String> bodyMap = null;
        try {
            bodyMap = JSON.parseObject(yadingNotify.getOriginalMsg(), Map.class);
        } catch (Exception e) {
            log.warn("亚丁回调信息解析异常", e);
            return YadingResultEnum.PARAMETER_FORMAT_ERROR;
        }
        String signkey1 = bodyMap.get(WXPayConstants.FIELD_SIGN);
        String signkey2 = "";
        try {
            signkey2 = WXPayUtil.generateSignature(bodyMap, yadingSignKey, WXPayConstants.SignType.MD5);
        } catch (Exception e) {
            e.printStackTrace();
            return YadingResultEnum.SING_ERROR_1;
        }
        if (!signkey2.equals(signkey1)) {
            return YadingResultEnum.SUCCESS;
//            return YadingResultEnum.SING_ERROR_2;
        }
        return YadingResultEnum.SUCCESS;
    }

    /**
     * @return LocalDateTime
     * @Description 时间戳
     * @Param timestampStr
     * <AUTHOR>
     * @Date 20:40 2022/5/12
     **/
    private LocalDateTime convertTimestamp(String timestampStr) {
        if (StringUtils.isBlank(timestampStr)) {
            return null;
        }
        long timestamp = Long.parseLong(timestampStr);
        return LocalDateTime.ofEpochSecond(timestamp / 1000, 0, ZoneOffset.ofHours(8));
    }

}
