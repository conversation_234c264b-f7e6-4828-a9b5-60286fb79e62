package com.jiuji.oa.oacore.tousu.vo.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class TouSuDutyUserRes {

    private Integer id;

    @ApiModelProperty(value = "投诉id")
    private Integer tousuId;

    @ApiModelProperty(value = "责任人id")
    private Integer userId;

    @ApiModelProperty(value = "责任人")
    private String userName;

    @ApiModelProperty(value = "部门id")
    private Integer departId;

    @ApiModelProperty(value = "门店Id")
    private Integer area1id;

    @ApiModelProperty(value = "投诉等级")
    private Integer tousuRank;

    @ApiModelProperty(value = "投诉扣除积分")
    private Integer tousuPoint;

    @ApiModelProperty(value = "投诉扣分")
    private BigDecimal tousuLosePoint;

    @ApiModelProperty(value = "90天内被投诉的次数")
    private Integer complaintCount;

    @ApiModelProperty(value = "门店类型")
    private Integer areaType;

    /**
     * 奖励积分
     */
    private Integer bonusPoint;
}
