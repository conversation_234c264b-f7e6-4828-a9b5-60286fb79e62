package com.jiuji.oa.oacore.yearcardtransfer.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 年包表[责任小组:销售]
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tiemoCard")
public class TiemoCard implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    @TableField("userid")
    private Integer userid;

    /**
     * 子订单ID
     */
    @TableField("sub_id")
    private Integer subId;

    /**
     * 区域ID
     */
    @TableField("areaid")
    private Integer areaid;

    /**
     * 购物车ID
     */
    @TableField("basketid")
    private Integer basketid;

    /**
     * 绑定购物车ID
     */
    @TableField("basket_idBind")
    private Integer basketIdBind;

    /**
     * 价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 产品价格ID
     */
    @TableField("ppriceid")
    private Integer ppriceid;

    /**
     * 已使用次数
     */
    @TableField("useCount")
    private Integer useCount;

    /**
     * 总次数
     */
    @TableField("allCount")
    private Integer allCount;

    /**
     * 录入用户
     */
    @TableField("inuser")
    private String inuser;

    /**
     * 录入时间
     */
    @TableField("dtime")
    private LocalDateTime dtime;

    /**
     * 过期时间
     */
    @TableField("etime")
    private LocalDateTime etime;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField("isdel")
    private Boolean isdel;

    /**
     * 类型
     */
    @TableField("type_")
    private Integer type;

    /**
     * 开始时间
     */
    @TableField("stime")
    private LocalDateTime stime;

    /**
     * 过期后(60天内)使用次数
     */
    @TableField("last_count")
    private Integer lastCount;

}
