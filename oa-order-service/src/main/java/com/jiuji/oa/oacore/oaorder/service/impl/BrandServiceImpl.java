package com.jiuji.oa.oacore.oaorder.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.jiuji.oa.oacore.oaorder.po.Brand;
import com.jiuji.oa.oacore.oaorder.dao.BrandMapper;
import com.jiuji.oa.oacore.oaorder.service.BrandService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.oacore.oaorder.vo.res.BrandRes;
import com.jiuji.oa.oacore.promocode.bo.CommonSwitchBO;
import com.jiuji.tc.utils.common.CommonUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-06
 */
@Service
public class BrandServiceImpl extends ServiceImpl<BrandMapper, Brand> implements BrandService {

    @Override
    public List<BrandRes> getBrandsByCid(List<Integer> cids) {
        if(CollectionUtils.isEmpty(cids)){
            return null;
        }
        List<BrandRes> list = baseMapper.getBrandsByCid(cids);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        list = list.stream().distinct().collect(Collectors.toList());
        return list;
    }

    @Override
    public List<CommonSwitchBO> getByIds(String limitIdsStr) {
        if(StringUtils.isEmpty(limitIdsStr)){
            return new ArrayList<>();
        }
        return baseMapper.getByIds(CommonUtils.str2List(limitIdsStr));
    }

    @Override
    public Map<Integer, List<BrandRes>> getCidAndBrandsMap(List<Integer> cids) {
        Map<Integer, List<BrandRes>> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(cids)){
            return new HashMap<>();
        }
        cids.forEach(i -> resultMap.put(i,new ArrayList<>()));
        List<BrandRes> list = baseMapper.getCidAndBrands(cids);
        if(CollectionUtils.isEmpty(list)){
            return resultMap;
        }
        Map<Integer, List<BrandRes>> map  = list.stream().collect(Collectors.groupingBy(BrandRes::getCid));
        for (Map.Entry<Integer, List<BrandRes>> e : resultMap.entrySet()){
            List<BrandRes> brands = map.get(e.getKey());
            if (CollectionUtils.isNotEmpty(brands)){
                e.getValue().addAll(brands);
            }
        }
        return resultMap;
    }
}
