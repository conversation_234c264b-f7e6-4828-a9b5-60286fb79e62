package com.jiuji.oa.oacore.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.system.HostInfo;
import cn.hutool.system.SystemUtil;
import com.jiuji.oa.oacore.common.constant.RequestAttrKeys;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.tc.utils.common.TraceIdUtil;
import org.slf4j.MDC;
import org.springframework.context.ApplicationContext;
import org.springframework.context.i18n.LocaleContext;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @Author: qiweiqing
 * @Date: 2020/10/13/19:24
 * @Description:
 */
@Component
public class SpringContextUtil extends SpringUtil {

    /**
     * 非请求的缓存
     */
    private static final ThreadLocal<Dict> NOT_REQ_ATTR_MAP = ThreadLocal.withInitial(()-> Dict.create());

    public static List<String> getActiveProfileList(){
        String[] profiles = SpringUtil.getApplicationContext().getEnvironment().getActiveProfiles();
        if(profiles.length != 0){
            return Arrays.asList(profiles);
        }
        return Collections.emptyList();
    }


    public static ApplicationContext getContext(){
        return SpringUtil.getApplicationContext();
    }

    public static Optional<HttpServletRequest> getRequest(){
        return getServletRequestAttributes().map(ServletRequestAttributes::getRequest);
    }

    /**
     * 添加错误消息
     * @param errorTemplate
     * @param params
     */
    public static void addRequestErrorMsg(String errorTemplate,Object... params) {
        addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_ERROR,errorTemplate,params);
    }

    /**
     * 添加错误消息
     * @param msgTemplate
     * @param params
     */
    public static void addRequestKeyMsg(String attKey, String msgTemplate, Object... params) {
        Optional<HttpServletRequest> requestOpt = getRequest();
        Set<String> msgSet = null;
        if(requestOpt.isPresent()){
            HttpServletRequest req = requestOpt.get();
            msgSet = (Set<String>) req.getAttribute(attKey);
            if (msgSet == null) {
                msgSet = new LinkedHashSet<>();
                req.setAttribute(attKey, msgSet);
            }
        }else{
            Dict localAttrMap = getLocalAttrMap();
            if (localAttrMap != null){
                msgSet = localAttrMap.getBean(attKey);
                if (msgSet == null) {
                    msgSet = new LinkedHashSet<>();
                    localAttrMap.set(attKey, msgSet);
                }
            }
        }
        if(msgSet != null){
            msgSet.add(StrUtil.format(msgTemplate,params));
        }

    }

    private static Dict getLocalAttrMap() {
        String traceId = MDC.get(TraceIdUtil.TRACE_ID_KEY);
        if(StrUtil.isBlank(traceId)){
            return null;
        }
        Dict attrMap = NOT_REQ_ATTR_MAP.get();
        if(ObjectUtil.notEqual(attrMap.getStr(TraceIdUtil.TRACE_ID_KEY), traceId)){
            // traceId 已经变化,清空字典
            attrMap.clear();
            attrMap.set(TraceIdUtil.TRACE_ID_KEY, traceId);
        }
        return attrMap;
    }

    /**
     * 获取错误消息
     */
    public static List<String> getRequestErrorMsg() {
        return getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_ERROR);
    }

    /**
     * 获取错误消息
     */
    public static List<String> getRequestKeyListMsg(String attrKey) {
        return getRequest().map(req -> (Set<String>) req.getAttribute(attrKey))
                .orElseGet(() -> {
                    Dict localAttrMap = getLocalAttrMap();
                    if(localAttrMap == null){
                        return Collections.emptySet();
                    }
                    return localAttrMap.get(attrKey, Collections.emptySet());
                }).stream().collect(Collectors.toList());
    }

    public static Optional<HttpServletResponse> getResponse(){
        return getServletRequestAttributes().map(ServletRequestAttributes::getResponse);
    }

    public static Optional<ServletRequestAttributes> getServletRequestAttributes(){
        return Optional.ofNullable((ServletRequestAttributes) RequestContextHolder.getRequestAttributes());

    }

    /**
     * 获取当前服务的ip和端口号
     * @return
     */
    public static Optional<String> getServerIpPort() {
        Environment env = SpringContextUtil.getContext().getEnvironment();
        Optional<String> ipPort = Optional.ofNullable(SystemUtil.getHostInfo()).map(HostInfo::getAddress)
                .map(add -> SpringContextUtil.getRequest().map(req -> Convert.toStr(req.getServerPort()))
                        .map(sp -> StrUtil.format("{}:{}", add, ObjectUtil.defaultIfBlank(env.getProperty("server.port"),
                                ObjectUtil.defaultIfBlank(env.getProperty("spring.cloud.consul.discovery.ip-address"), sp)))).orElse(null));
        return ipPort;
    }

    /**
     * 是否为生产环境
     * @return
     */
    public static boolean isProduce(){
        return CollUtil.intersection(SpringContextUtil.getActiveProfileList(),Arrays.asList("dev","test","10050")).isEmpty();
    }

    /**
     * 请求级别的缓存
     * @param callback
     * @param <T>
     * @return
     */
    public static <T> T reqCache(Supplier<T> callback, String format, Object ...args){
        //缓存到请求属性中
        String cacheKey = StrUtil.format(RequestAttrKeys.REQUEST_CACHE_PREV, StrUtil.format(format, args));
        Optional<HttpServletRequest> reqOpt = getRequest();
        return reqOpt.map(req -> (T)req.getAttribute(cacheKey)).orElseGet(()->{
            T t = callback.get();
            reqOpt.ifPresent(req -> req.setAttribute(cacheKey, t));
            return t;
        });
    }

    /**
     * 带上下文执行
     * @param supplier
     * @return
     */
    public static <U> Supplier<U> supplierWithContext(Supplier<U> supplier) {
        // 传递请求的上线
        // 将上下文设置到子线程中
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        LocaleContext localeContext = LocaleContextHolder.getLocaleContext();
        // 传递租户编号
        Integer xtenant = XtenantEnum.getXtenant();
        // 传递traceId
        String traceId = MDC.get(TraceIdUtil.TRACE_ID_KEY);
        Thread currentThread = Thread.currentThread();
        return () -> {
            // 如果是同一线程则直接执行
            if (currentThread == Thread.currentThread()) {
                return supplier.get();
            }
            try {
                RequestContextHolder.setRequestAttributes(requestAttributes);
                LocaleContextHolder.setLocaleContext(localeContext);
                XtenantEnum.setXtenant(xtenant);
                MDC.put(TraceIdUtil.TRACE_ID_KEY, traceId);
                return supplier.get();
            } finally {
                RequestContextHolder.resetRequestAttributes();
                LocaleContextHolder.resetLocaleContext();
                MDC.remove(TraceIdUtil.TRACE_ID_KEY);
            }
        };
    }



    /**
     * 带上下文执行
     * @param runnable
     * @return
     */
    public static Runnable runnableWithContext(Runnable runnable) {
        // 传递请求的上线
        // 将上下文设置到子线程中
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        LocaleContext localeContext = LocaleContextHolder.getLocaleContext();
        // 传递租户编号
        Integer xtenant = XtenantEnum.getXtenant();
        // 传递traceId
        String traceId = MDC.get(TraceIdUtil.TRACE_ID_KEY);
        Thread currentThread = Thread.currentThread();
        return () -> {
            // 如果是同一线程则直接执行
            if (currentThread == Thread.currentThread()) {
                runnable.run();
                return;
            }
            try {
                RequestContextHolder.setRequestAttributes(requestAttributes);
                LocaleContextHolder.setLocaleContext(localeContext);
                XtenantEnum.setXtenant(xtenant);
                MDC.put(TraceIdUtil.TRACE_ID_KEY, traceId);
                runnable.run();
            } finally {
                RequestContextHolder.resetRequestAttributes();
                LocaleContextHolder.resetLocaleContext();
                MDC.remove(TraceIdUtil.TRACE_ID_KEY);
            }
        };
    }
}
