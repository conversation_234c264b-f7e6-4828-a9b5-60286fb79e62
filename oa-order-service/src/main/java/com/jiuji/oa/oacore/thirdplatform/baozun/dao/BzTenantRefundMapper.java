package com.jiuji.oa.oacore.thirdplatform.baozun.dao;

import com.jiuji.cloud.after.vo.refund.CardOriginRefundVo;
import com.jiuji.cloud.after.vo.refund.OtherRefundVo;
import com.jiuji.cloud.after.vo.refund.ThirdOriginRefundVo;
import com.jiuji.oa.oacore.oaorder.po.*;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.AfterXianhuoBo;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.BzRefundBO;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.BzTenantSalesDetailBO;
import com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantApp;
import com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantSalesDetailSninfo;
import com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantSalesOrder;
import com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantSalesTender;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 宝尊退款mapper
 * <AUTHOR>
 * @since 2021/10/9 9:31
 */
@Mapper
public interface BzTenantRefundMapper {
    /**
     * 查询退款订单数
     * @param orderIds
     * @return
     */
    List<BzTenantSalesDetailBO> queryRefundOrderDetail(@Param("orderIds") List<String> orderIds, @Param("isNotTest") boolean isNotTest);

    /**
     * 查询退款订单数
     * @param orderIds
     * @return
     */
    List<BzTenantSalesDetailBO> queryRefundOrderDetailForXXL(@Param("orderIds") List<String> orderIds, @Param("isNotTest") boolean isNotTest);

    /**
     * 查询未处理的退款订单信息
     * @param maxRefundOrderNum
     * @param transactionNumbers
     * @return
     */
    List<BzTenantSalesOrder> queryRefundOrder(@Param("topNum") int maxRefundOrderNum, @Param("transactionNumbers") List<String> transactionNumbers);

    /**
     * 查询退款订单的串号信息
     * @param orderIds
     * @param isNotTest
     * @return
     */
    List<BzTenantSalesDetailSninfo> queryRefundSninfo(@Param("orderIds") List<String> orderIds, @Param("isNotTest") boolean isNotTest);

    /**
     * 查询退款金额信息
     * @param orderIds
     * @return
     */
    List<BzTenantSalesTender> querySalesTenders(@Param("orderIds") List<String> orderIds);

    /**
     * 查找订单详情信息
     * @param bzRefunds
     * @return
     */
    List<Basket> queryBaskets(@Param("bzRefunds") List<BzRefundBO> bzRefunds);

    /**
     * 追加订单日志信息
     * @param transactionNumber
     * @param message
     * @return
     */
    int appendOrderMsg(@Param("transactionNumber") String transactionNumber, @Param("message") String message);

    /**
     * 获取所有的订单信息
     * @param subIds
     * @return
     */
    List<Sub> listSub(@Param("subIds") List<Integer> subIds);

    /**
     * 通过ppid获取所有产品信息
     * @param ppids
     * @return
     */
    List<Productinfo> listProductInfoByPpid(@Param("ppids") List<Integer> ppids);

    /**
     * 获取productMkc信息
     * @param salesDetails
     * @return
     */
    List<ProductMkc> listProductMkc(@Param("salesDetails") List<BzTenantSalesDetailBO> salesDetails);

    /**
     * 插入售后信息
     * @param bzRefund
     * @param salesDetail
     * @param sn
     * @param orderId
     * @return
     */
    int insertShouhou(@Param("bzRefund") BzRefundBO bzRefund, @Param("salesDetail") BzTenantSalesDetailBO salesDetail
            , @Param("sn") BzTenantSalesDetailSninfo sn, @Param("orderId") String orderId);

    /**
     * 插入退换信息
     * @param salesDetail
     * @param sn
     * @param mkc
     * @return
     */
    int insertReturnCb(@Param("salesDetail") BzTenantSalesDetailBO salesDetail, @Param("sn") BzTenantSalesDetailSninfo sn
            , @Param("mkc") ProductMkc mkc);

    /**
     * 根据串号获取售后id
     * @param subId
     * @param sn
     * @return
     */
    Integer getShouhouId(@Param("subId") Integer subId,@Param("sn") String sn);

    /**
     * 插入售后接件时间线
     * @param shouhouId
     * @return
     */
    int insertShouhouTimePoint(@Param("shouhouId") Integer shouhouId);

    /**
     * 插入售后退款信息
     * @param bzRefund
     * @param salesDetail
     * @param sn
     * @param productMkc
     * @return
     */
    int insertShouhouTuihuan(@Param("bzRefund") BzRefundBO bzRefund, @Param("salesDetail") BzTenantSalesDetailBO salesDetail
            , @Param("sn") BzTenantSalesDetailSninfo sn, @Param("productMkc") ProductMkc productMkc);

    /**
     * 获取app信息
     * @param salesOrders
     * @return
     */
    List<BzTenantApp> listApp(@Param("salesOrders") List<BzTenantSalesOrder> salesOrders);

    int updateSnShouhouId(@Param("id") Integer id, @Param("shouhouId") Integer shouhouId);

    int updateOrderDetailDealResult(@Param("id") Integer id, @Param("dealResult") boolean dealResult);

    /**
     * 插入小件单
     * @param bzRefund
     * @param name
     * @return
     */
    int insertSmallPro(@Param("bzRefund") BzRefundBO bzRefund, @Param("name") String name);

    int batchInsertSmallProBill(@Param("smallProId") Integer smallProId, @Param("smallSalesDetails") List<BzTenantSalesDetailBO> smallSalesDetails);

    /**
     * 插入小件退货信息
     * @return
     * @param bzRefund
     * @param tuikuanM
     * @param inPrice
     * @param currOriginRefundVos
     */
    int insertSmallProTuihuan(@Param("bzRefund") BzRefundBO bzRefund, @Param("tuikuanM") BigDecimal tuikuanM, @Param("inPrice") BigDecimal inPrice,
                              @Param("thirdOriginRefundVos") List<ThirdOriginRefundVo> thirdOriginRefundVos);

    /**
     *
     * @param smallProTuihuanId
     * @param smallSalesDetails
     * @return
     */
    int batchInsertSmallProTuihuanBill(@Param("smallProTuihuanId") Integer smallProTuihuanId, @Param("smallSalesDetails") List<BzTenantSalesDetailBO> smallSalesDetails);

    /**
     * 有效的小件单
     * @param bzRefund
     * @return
     */
    Smallpro getSmallPro(@Param("bzRefund") BzRefundBO bzRefund);

    String getSecretByCode(@Param("code") Integer code);

    /**
     * 更新取机前需要的数据
     * @param shouhouId
     * @return
     */
    int updateShouhouQujiData(@Param("shouhouId") Integer shouhouId);

    /**
     * 插入方法加密密钥
     * @param code
     * @param secret
     * @return
     */
    int insertSecret(@Param("code") Integer code, @Param("secret") String secret);

    /**
     * 获取现货单id
     * @param shouhouId
     * @return
     */
    AfterXianhuoBo getXianHuoByFromId(@Param("shouhouId") Integer shouhouId);

    /**
     * 获取售后转出的id
     *
     * @param id
     * @param mkcId
     * @param isCheck1
     * @param isCheck2
     * @return
     */
    Integer getMkdIdByMkcId(@Param("id") Integer id, @Param("mkcId") Integer mkcId, @Param("isCheck1") boolean isCheck1, @Param("isCheck2") boolean isCheck2);

    int batchInsertThirdOrigin(@Param("tuihuanId") Integer tuihuanId, @Param("userName") String userName, @Param("myTuiWayDetails") List<ThirdOriginRefundVo> myTuiWayDetails);

    int batchInsertCardPayOriginWay(@Param("tuihuanId") Integer tuihuanId, @Param("userName") String userName, @Param("tuiWayDetails") List<CardOriginRefundVo> myTuiWayDetails);

    int batchInsertOtherRefundVo(@Param("tuihuanId") Integer tuihuanId, @Param("userName") String userName, @Param("tuiWayDetails") List<OtherRefundVo> myTuiWayDetails);

    int batchUpdateThirdOriginShouyingOther(@Param("myTuiWayDetails") Collection<ThirdOriginRefundVo> myTuiWayDetails);

}
