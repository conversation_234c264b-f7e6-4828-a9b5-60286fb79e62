package com.jiuji.oa.oacore.yearcardtransfer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ch999.common.util.vo.Result;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.yearcardtransfer.entity.AreaGuestBookAccountLog;
import com.jiuji.oa.oacore.yearcardtransfer.service.AreaGuestBookAccountService;
import com.jiuji.oa.oacore.yearcardtransfer.vo.AreaGuestBookAccountQuery;
import com.jiuji.oa.oacore.yearcardtransfer.vo.AreaGuestBookAccountVO;
import lombok.Data;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/AreaGuestBookAccount")
public class AreaGuestBookAccountController {

    @Resource
    private AreaGuestBookAccountService areaGuestBookAccountService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;


    @PostMapping("/page")
    public Result<IPage<AreaGuestBookAccountVO>> page(@RequestBody AreaGuestBookAccountQuery req){
        return Result.success(areaGuestBookAccountService.page(req));
    }

//    @GetMapping("/log")
//    public Result<IPage<AreaGuestBookAccountLog>> page(@RequestParam("configId") Integer configId,
//                                                       @RequestParam("currentPage") Integer currentPage,
//                                                       @RequestParam("pageSize") Integer pageSize){
//        return Result.success(areaGuestBookAccountService.pageLog(configId, currentPage, pageSize));
//    }

    @GetMapping("/detail")
    public Result<AreaGuestBookAccountVO> page(@RequestParam("configId") Integer configId){
        return Result.success(areaGuestBookAccountService.getDetail(configId));
    }


    @PostMapping("/addOrUpdate")
    public Result<Boolean> addOrUpdate(@RequestBody @Valid AreaGuestBookAccountVO accountVO){
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if(null == oaUserBO){
            return Result.error("请登陆");
        }
        areaGuestBookAccountService.addOrUpdate(accountVO, oaUserBO);
        return Result.success();
    }


    @PostMapping("/updateEnableStatus")
    public Result<Boolean> updateEnableStatus(@RequestBody UpdateDate updateDate){
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if(null == oaUserBO){
            return Result.error("请登陆");
        }
        areaGuestBookAccountService.updateEnableStatus(updateDate.getConfigId(), updateDate.getEnableStatus(), oaUserBO);
        return Result.success();
    }

    @Data
    public static class UpdateDate {
        private Integer configId;
        private Boolean enableStatus;
    }
}
