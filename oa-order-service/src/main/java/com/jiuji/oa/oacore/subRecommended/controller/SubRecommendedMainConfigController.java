package com.jiuji.oa.oacore.subRecommended.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.oacore.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.oaorder.service.ProductinfoService;
import com.jiuji.oa.oacore.promocode.vo.req.ClearPriceCloseServiceReq;
import com.jiuji.oa.oacore.promocode.vo.req.CloseServiceReq;
import com.jiuji.oa.oacore.subRecommended.enums.MainConfigSelectTypeEnum;
import com.jiuji.oa.oacore.subRecommended.enums.MainConfigStateEnum;
import com.jiuji.oa.oacore.subRecommended.enums.MainConfigTimeTypeEnum;
import com.jiuji.oa.oacore.subRecommended.service.SubRecommendedMainConfigService;
import com.jiuji.oa.oacore.subRecommended.service.SubRecommendedProductServiceConfigService;
import com.jiuji.oa.oacore.subRecommended.vo.req.*;
import com.jiuji.oa.oacore.subRecommended.vo.res.*;
import com.jiuji.oa.oacore.weborder.req.RecommendedSelectAppReq;
import com.jiuji.oa.oacore.weborder.res.recommendedRes.RecommendedSelectOaRes;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.EnumVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/recommendedMainConfig")
public class SubRecommendedMainConfigController {

    @Resource
    private SubRecommendedMainConfigService mainConfigService;
    @Resource
    private ProductinfoService productinfoService;
    @Resource
    private SubRecommendedProductServiceConfigService serviceConfigService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;




    /**
     * 关闭服务
     * @param copyInfoReq
     * @return
     */
    @GetMapping("/initializationData/v1")
    public R<String> initializationData() {

        return mainConfigService.initializationData();
    }

    @GetMapping(value = "/getEnums")
    public R<Map<String, List<EnumVO>>> getEnums() {
        Map<String, List<EnumVO>> enumMap = new HashMap<>();
        List<EnumVO> selectTypeEnum = EnumUtil.toEnumVOList(MainConfigSelectTypeEnum.class);
        List<EnumVO> stateEnum = EnumUtil.toEnumVOList(MainConfigStateEnum.class);
        List<EnumVO> mainConfigTimeTypeEnum = EnumUtil.toEnumVOList(MainConfigTimeTypeEnum.class);
        enumMap.put("selectTypeEnum", selectTypeEnum);
        enumMap.put("stateEnum", stateEnum);
        enumMap.put("mainConfigTimeTypeEnum", mainConfigTimeTypeEnum);
        return R.success(enumMap);
    }

    /**
     * 关闭服务
     * @param copyInfoReq
     * @return
     */
    @PostMapping("/clearPriceCloseService/v1")
    public R<String> clearPriceCloseService(@RequestBody ClearPriceCloseServiceReq closeServiceReq) {
        R<String> result = serviceConfigService.clearPriceCloseService(closeServiceReq);
        log.warn("价格清0关闭服务传入参数：{},返回结果：{}", JSONUtil.toJsonStr(closeServiceReq), JSONUtil.toJsonStr(result));
        return result;
    }


    /**
     * 关闭服务
     * @param copyInfoReq
     * @return
     */
    @PostMapping("/closeService/v1")
    public R<String> closeService(@RequestBody CloseServiceReq closeServiceReq) {
        R<String> result = serviceConfigService.closeService(closeServiceReq);
        log.warn("关闭服务传入参数：{},返回结果：{}", JSONUtil.toJsonStr(closeServiceReq), JSONUtil.toJsonStr(result));
        return result;
    }

    /**
     * 服务查询
     * @param copyInfoReq
     * @return
     */
    @PostMapping("/selectServiceInfo/v1")
    public R<List<ServiceInfoRes>> selectServiceInfo(@RequestBody ServiceInfoReq req) {
        return R.success(serviceConfigService.selectServiceInfo(req));
    }

    /**
     * 套餐复制
     * @param req
     * @return
     */
    @RepeatSubmitCheck
    @PostMapping("/copyInfo/v1")
    public R<String> copyInfo(@RequestBody @Valid CopyInfoReq copyInfoReq) {
        mainConfigService.copyInfo(copyInfoReq);
        return R.success("操作成功");
    }


    /**
     * 套餐修改主配置信息
     * @param req
     * @return
     */
    @RepeatSubmitCheck
    @PostMapping("/updateMainInfo/v1")
    public R<String> updateMainInfo(@RequestBody @Valid UpdateMainInfoReq req) {
        log.warn("套餐修改主配置信息前端传入参数：{}", JSONUtil.toJsonStr(req));
        mainConfigService.updateMainInfo(req);
        return R.success("操作成功");
    }




    /**
     * 套餐分页查询
     * @param req
     * @return
     */
    @PostMapping("/recommendedPage/v1")
    public R<Page<RecommendedPageRes>> recommendedPage(@RequestBody @Valid RecommendedPageReq req) {
        return mainConfigService.recommendedPage(req);
    }



    /**
     * OA 推荐套餐查询
     * @param req
     * @return
     */
    @PostMapping("/recommendedSelectOa/v1")
    public R<RecommendedSelectOaRes> recommendedSelectOa(@RequestBody @Valid RecommendedSelectOaReq req) {
        log.warn("OA 推荐套餐查询传入参数：{}", JSONUtil.toJsonStr(req));
        R<RecommendedSelectOaRes> recommendedSelectOaResR = mainConfigService.recommendedSelectOa(req);
        log.warn("OA 推荐套餐查询返回结果：{}", JSONUtil.toJsonStr(recommendedSelectOaResR));
        return recommendedSelectOaResR;
    }
    /**
     * APP 推荐套餐查询
     * @param req
     * @return
     */
    @PostMapping("/recommendedSelectApp/v1")
    public R<RecommendedSelectOaRes> recommendedSelectApp(@RequestBody @Valid RecommendedSelectAppReq req) {
        log.warn("App 推荐套餐查询传入参数：{}", JSONUtil.toJsonStr(req));
        R<RecommendedSelectOaRes> recommendedSelectOaResR = mainConfigService.recommendedSelectApp(req);
        log.warn("App 推荐套餐查询返回结果：{}", JSONUtil.toJsonStr(recommendedSelectOaResR));
        return recommendedSelectOaResR;
    }


    /**
     * 网站推荐套餐查询
     * @param req
     * @return
     */
    @PostMapping("/recommendedSelectNoToken/v1")
    public R<RecommendedSelectOaRes> recommendedSelectNoToken(@RequestBody @Valid RecommendedSelectAppReq req) {
        log.warn("网站 推荐套餐查询传入参数：{}", JSONUtil.toJsonStr(req));
        R<RecommendedSelectOaRes> recommendedSelectOaResR = mainConfigService.recommendedSelectApp(req);
        log.warn("网站 推荐套餐查询返回结果：{}", JSONUtil.toJsonStr(recommendedSelectOaResR));
        return recommendedSelectOaResR;
    }

    /**
     * 前端 推荐套餐查询
     * @param req
     * @return
     */
    @PostMapping("/getRecommendedById/v1")
    public R<RecommendedInfo> getRecommendedById(@RequestBody @Valid RecommendedByIdReq req) {
        return R.success(mainConfigService.getRecommendedById(req));
    }

    /**
     * 根据subid查询套餐信息
     * @param req
     * @return
     */
    @PostMapping("/recommendedSelectOaBySubId/v1")
    public R<OaBySubIdRes> recommendedSelectOaBySubId(@RequestBody @Valid OaBySubIdReq req) {
        log.warn("根据subid查询套餐信息传入参数：{}", JSONUtil.toJsonStr(req));
        OaBySubIdRes oaBySubIdRes = mainConfigService.recommendedSelectOaBySubId(req);
        log.warn("根据subid查询套餐信息返回结果：{}", JSONUtil.toJsonStr(oaBySubIdRes));
        return R.success(oaBySubIdRes);
    }




    /**
     * 保存或者修改套餐信息
     * @param req
     * @return
     */
    @RepeatSubmitCheck
    @PostMapping("/saveOrUpdate/v1")
    public R<SaveOrUpdateRes> saveOrUpdate(@RequestBody @Valid RecommendedInfo req) {
        log.warn("保存或者修改套餐信息前端传入参数：{}", JSONUtil.toJsonStr(req));
        return R.success(mainConfigService.saveOrUpdate(req));
    }


    /**
     * 查询商品信息
     * @param req
     * @return
     */
    @PostMapping("/selectProductBaseInfoList/v1")
    public R<List<ProductBaseInfo>> selectProductBaseInfoList(@RequestBody ProductBaseInfoReq req) {
        OaUserBO userBO = Optional.ofNullable(abstractCurrentRequestComponent.getCurrentStaffId()).orElseThrow(() -> new CustomizeException("请先登录"));
        Integer areaId = Optional.ofNullable(req.getCreateAreaId()).orElse(userBO.getArea1id());
        Map<Integer, ProductBaseInfo> integerProductBaseInfoMap = productinfoService.selectProductBaseInfoMap(req.getPpidList(),areaId,true);
        List<ProductBaseInfo> list = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(integerProductBaseInfoMap)){
            list=integerProductBaseInfoMap.values().stream().collect(Collectors.toList());
        }
        return R.success(list);
    }
}
