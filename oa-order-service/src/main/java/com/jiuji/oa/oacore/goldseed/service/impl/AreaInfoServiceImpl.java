package com.jiuji.oa.oacore.goldseed.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.jiuji.oa.oacore.common.component.CurrentRequestComponent;
import com.jiuji.oa.oacore.goldseed.service.AreaInfoService;
import com.jiuji.oa.oacore.thirdplatform.order.mapper.IAreainfoMapper;
import com.jiuji.oa.oacore.tousu.bo.SmsChannelBo;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfoBasicVO;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: gengjiaping
 * @date: 2019/12/10
 */
@Service
@Slf4j
public class AreaInfoServiceImpl implements AreaInfoService {

    @Autowired
    private CurrentRequestComponent currentRequestComponent;

    @Autowired
    private AreaInfoClient areaInfoClient;

    private IAreainfoMapper areainfoMapper;

    @Override
    public Integer getDepartCode() {
        String areaStr = currentRequestComponent.getOaCurrentArea();
        if (StringUtils.isEmpty(areaStr)) {
            return null;
        }
        R<AreaInfoBasicVO> result = areaInfoClient.getByArea(areaStr);
        if (result.getCode() == ResultCode.SUCCESS) {
            AreaInfoBasicVO basicVO = result.getData();
            return basicVO.getAuthorizeId();
        }
        return null;
    }

    @Override
    public Integer getAuthorizeid() {
        String areaStr = currentRequestComponent.getOaCurrentArea();
        if (StringUtils.isEmpty(areaStr)) {
            return null;
        }
        R<AreaInfoBasicVO> result = areaInfoClient.getByArea(areaStr);
        if (result.getCode() == ResultCode.SUCCESS && result.getData() != null) {
            return result.getData().getAuthorizeId();
        }
        return null;
    }

    @Override
    public List<String> getAreaByIds(String areaids) {
        if (StringUtils.isEmpty(areaids)) {
            return new ArrayList<>();
        }
        List<Integer> collect =
                Arrays.stream(areaids.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        R<List<AreaInfo>> listR = areaInfoClient.listAreaInfo(collect);
        List<AreaInfo> areaInfos = Optional.ofNullable(listR).map(R::getData).orElse(new ArrayList<>());
        return areaInfos.stream().map(AreaInfo::getArea).collect(Collectors.toList());
    }

    @Override
    public List<AreaInfo> getIdsByAreas(List<String> areas) {
        List<AreaInfo> list = SpringUtil.getBean(AreaInfoService.class).getAreaInfoList();
        return Optional.ofNullable(list).orElse(new ArrayList<>());
    }

    @Override
    public AreaInfo getAreaInfoById(int areaId) {
        R<AreaInfo> areaR = areaInfoClient.getAreaInfoById(areaId);
        if(!areaR.isSuccess()){
            log.warn("获取门店异常: {}", JSON.toJSONString(areaR));
        }
        return areaR.getData();
    }

    @Override
    public Map<Integer, AreaInfo> getAreaInfoMap() {
        R<List<AreaInfo>> allAreaR = areaInfoClient.listAll();
        if (allAreaR.getCode() != ResultCode.SUCCESS) {
            return Collections.EMPTY_MAP;
        }
        return allAreaR.getData().parallelStream().collect(Collectors.toMap(AreaInfo::getId, Function.identity(), (key1, key2) -> key2));
    }

    @Override
    public SmsChannelBo getSmsChannelById(Integer areaId) {
        return areainfoMapper.getSmsChannelById(areaId);
    }

    @Override
    @Cached(name = "orderservice.AreaInfoService.getAreaInfoList", expire = 30, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.LOCAL)
    public List<AreaInfo> getAreaInfoList() {
        R<List<AreaInfo>> allAreaR = areaInfoClient.listAll();
        //失败再查一次
        if (allAreaR.getCode() != ResultCode.SUCCESS) {
            return Optional.ofNullable(areaInfoClient.listAll()).map(R::getData).orElse(new ArrayList<>());
        }
        return allAreaR.getData();
    }
}
