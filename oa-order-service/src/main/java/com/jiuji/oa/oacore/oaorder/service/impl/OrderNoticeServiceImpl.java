package com.jiuji.oa.oacore.oaorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.oacore.apollo.ApolloKeys;
import com.jiuji.oa.oacore.common.constant.RequestAttrKeys;
import com.jiuji.oa.oacore.common.enums.OrderNoticeEnum;
import com.jiuji.oa.oacore.common.util.SaasManagerUtils;
import com.jiuji.oa.oacore.common.util.SpringContextUtil;
import com.jiuji.oa.oacore.oaorder.bo.UndoneOrderBo;
import com.jiuji.oa.oacore.oaorder.bo.UndoneOrderNumberBO;
import com.jiuji.oa.oacore.oaorder.dao.EvaluateMapper;
import com.jiuji.oa.oacore.oaorder.dao.OrderNoticeMapper;
import com.jiuji.oa.oacore.oaorder.po.Ch999User;
import com.jiuji.oa.oacore.oaorder.service.Ch999UserService;
import com.jiuji.oa.oacore.oaorder.service.OrderNoticeService;
import com.jiuji.oa.oacore.oaorder.vo.req.OrderNoticeReq;
import com.jiuji.oa.oacore.oaorder.vo.res.OrderNoticeRes;
import com.jiuji.oa.oacore.weborder.enums.OrderNoticeV3Enum;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.TimeFormatConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 15:24
 * @Description
 */
@Service
@Slf4j
public class OrderNoticeServiceImpl implements OrderNoticeService {
    @Resource
    private EvaluateMapper evaluateMapper;
    @Resource
    private OrderNoticeMapper orderNoticeMapper;

    @Resource
    private Ch999UserService ch999UserService;

    /**
     * 根据员工id获取待办消息
     *
     * @param userId 用户id
     * @return 待办消息
     */
    @Override
    public Page<OrderNoticeRes> getOrderNotice(Integer userId) {
        Page<OrderNoticeRes> page = new Page<>();
        //根据员工id获取员工姓名
        String userName = evaluateMapper.getUserInfoById(userId);
        //根据订单获取未完成订单信息
        UndoneOrderNumberBO undoneOrderNumberBO = orderNoticeMapper.getUndoneOrder(userName);
        //当未完成订单数量大于零
        if (undoneOrderNumberBO.getSubNumber() > 0) {
            List<OrderNoticeRes> list = new ArrayList<>();
            list.add(Objects.requireNonNull(OrderNoticeEnum.getByValue(undoneOrderNumberBO.getKind())).getOrderNotice(undoneOrderNumberBO.getSubNumber()));
            page.setRecords(list);
            page.setPages(1);
            page.setTotal(1);
            return page;
        }
        return page;
    }


    /**
     * 根据员工id获取待办消息
     *
     * @param userId 用户id
     * @return 待办消息
     */
    @Override
    public Page<OrderNoticeRes> getOrderNoticeV2(Integer userId, Integer xtenant) {
        Page<OrderNoticeRes> page = new Page<>();

        Ch999User ch999User = ch999UserService.getCh999UsersById(Arrays.asList(userId)).stream().findFirst().orElse(null);
        if (Objects.isNull(ch999User)) {
            return page;
        }
        String userName = ch999User.getCh999Name();
        String mobile = ch999User.getMobile();
        // 用户所在门店
        Integer areaId  = ch999User.getArea1id();
        //根据员工id获取员工姓名
        //根据订单获取未完成订单信息
        UndoneOrderNumberBO undoneOrderNumberBO = orderNoticeMapper.getUndoneOrder(userName);

//        //当未完成订单数量大于零
//        if (undoneOrderNumberBO.getSubNumber() > 0 && xtenant < NumberConstant.ONE_THOUSAND) {
//            List<OrderNoticeRes> list = new ArrayList<>();
//            list.add(Objects.requireNonNull(OrderNoticeEnum.getByValue(undoneOrderNumberBO.getKind())).getOrderNotice(undoneOrderNumberBO.getSubNumber()));
//            list.forEach(l -> l.setLink(SaasManagerUtils.getMoaUrl() + "/app/native/orderMatterPanel"));
//            page.setRecords(list);
//            page.setPages(1);
//            page.setTotal(1);
//            return page;
//        }
//        //当未完成订单数量大于零
//        if (undoneOrderNumberBO.getSubNumber() > 0) {
//            List<OrderNoticeRes> list = new ArrayList<>();
//            list.add(Objects.requireNonNull(OrderNoticeEnum.getByValue(undoneOrderNumberBO.getKind())).getOrderNotice(undoneOrderNumberBO.getSubNumber()));
//            page.setRecords(list);
//            page.setPages(1);
//            page.setTotal(1);
//            return page;
//        }
        //当未完成订单数量大于零
        List<OrderNoticeRes> list = new ArrayList<>();
        if (undoneOrderNumberBO.getSubNumber() > 0) {
            list.add(Objects.requireNonNull(OrderNoticeEnum.getByValue(undoneOrderNumberBO.getKind())).getOrderNotice(undoneOrderNumberBO.getSubNumber()));
        }

        // 转售单待办提醒 （内部购买渠道，内部借用渠道）
        List<UndoneOrderNumberBO> zhuanShouUndoneOrders = orderNoticeMapper.getZhuanShouUndoneOrder(userName);
        for (UndoneOrderNumberBO bo : zhuanShouUndoneOrders) {
            if (bo.getSubNumber() > 0) {
                list.add(Objects.requireNonNull(OrderNoticeEnum.getByValue(bo.getKind())).getOrderNotice(bo.getSubNumber()));
            }
        }

        // 黄金待办提醒
        UndoneOrderNumberBO goldOrderOrders = orderNoticeMapper.getGoldOrder(userName);
        if (Objects.nonNull(goldOrderOrders) && goldOrderOrders.getSubNumber() > 0) {
            list.add(Objects.requireNonNull(OrderNoticeEnum.getByValue(goldOrderOrders.getKind())).getOrderNotice(goldOrderOrders.getSubNumber()));
        }
//        // 回收未发货的数量待办提醒
//        UndoneOrderNumberBO pendingRecoverToArea = orderNoticeMapper.getPendingRecoverToArea(areaId);
//        if (Objects.nonNull(pendingRecoverToArea) && pendingRecoverToArea.getSubNumber() > 0) {
//            list.add(Objects.requireNonNull(OrderNoticeEnum.getByValue(pendingRecoverToArea.getKind())).getOrderNotice(pendingRecoverToArea.getSubNumber()));
//        }

        // 回收赎回 kind=11
        List<UndoneOrderNumberBO> recoverRedeems = orderNoticeMapper.getRecoverRedemption(areaId);
        if (CollectionUtils.isNotEmpty(recoverRedeems)){
            for (UndoneOrderNumberBO recoverRedeem : recoverRedeems) {
                list.add(Objects.requireNonNull(OrderNoticeEnum.getByValue(recoverRedeem.getKind())).getOrderNotice(recoverRedeem.getSubId()));
            }
        }

        list.forEach(l -> {
            //黄金
            if ("13".equals(l.getUnKey())) {
                l.setLink(SaasManagerUtils.getMoaUrl() + "/new/#/recycle/recovery-gold/appoint-list/" + userId);
                l.setLabel("黄金");
            } else {
                l.setLabel("订单");
            }
        });
        page.setRecords(list);
        page.setPages(1);
        page.setTotal(list.size());
        return page;
    }

    @Override
    public R<Page<OrderNoticeRes>> getOrderNoticeV3(OrderNoticeReq req) {
        R<Page<OrderNoticeRes>> checkR = checkOrderNoticeParam(req);
        if (!checkR.isSuccess()) {
            return checkR;
        }
        R<Page<OrderNoticeRes>> returnR = handleOrderNoticeParam(req);
        if (returnR.isSuccess()) {
            return returnR.addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
        }
        //根据员工id获取员工姓名
        if (StrUtil.isBlank(req.getUserName()) && ObjectUtil.defaultIfNull(req.getUserId(), 0) > 0) {
            String userName = evaluateMapper.getUserInfoById(req.getUserId());
            req.setUserName(userName);
        }
        if (StrUtil.isBlank(req.getUserName())) {
            req.setUserName(null);
        }
        if (ObjectUtil.defaultIfNull(req.getAreaId(), 0) == 0) {
            req.setAreaId(null);
        }
        long total = orderNoticeMapper.countUndoneOrder(req);
        List<UndoneOrderBo> datas = Collections.emptyList();
        if (total > 0) {
            datas = orderNoticeMapper.listUndoneOrder(req);
        }
        //批量查询门店信息
        AreaInfoClient areaInfoClient = SpringUtil.getBean(AreaInfoClient.class);
        List<Integer> areaIds = datas.stream().flatMap(uo -> Stream.of(uo.getAreaId(), uo.getInAreaId(), uo.getOutAreaId()))
                .filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
        Map<Integer, AreaInfo> areaInfoMap = CommonUtils.getResultData(areaInfoClient.listAreaInfo(areaIds),
                errMsg -> {
                    SpringContextUtil.addRequestErrorMsg("批量获取门店信息接口报[{}]异常", errMsg);
                    return Collections.emptyList();
                }).stream().collect(Collectors.toMap(AreaInfo::getId, Function.identity(), (v1, v2) -> v1));

        Page<OrderNoticeRes> result = new Page<>(req.getCurrent(), req.getSize(), total);
        String moaUrl = SaasManagerUtils.getMoaUrl();
        result.setRecords(datas.stream()
                .map(r -> {
                    r.setArea(Optional.ofNullable(areaInfoMap.get(r.getAreaId())).map(AreaInfo::getArea).orElse(Convert.toStr(r.getAreaId())));
                    r.setAreaIn(Optional.ofNullable(areaInfoMap.get(r.getInAreaId())).map(AreaInfo::getArea).orElse(Convert.toStr(r.getInAreaId())));
                    r.setAreaOut(Optional.ofNullable(areaInfoMap.get(r.getOutAreaId())).map(AreaInfo::getArea).orElse(Convert.toStr(r.getOutAreaId())));
                    return toOrderNotice(moaUrl, r);
                })
                .collect(Collectors.toList()));
        return R.success(result).addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    /**
     * 处理请求参数
     *
     * @param req
     * @return
     */
    private R<Page<OrderNoticeRes>> handleOrderNoticeParam(OrderNoticeReq req) {
        Page<OrderNoticeRes> result = new Page<>(req.getCurrent(), req.getSize());
        if (req.getOrderTypes().stream().anyMatch(ot -> OrderNoticeV3Enum.isDiaoBo(ot))
                && StrUtil.splitTrim(ApolloKeys.getApolloProperty(ApolloKeys.order_notice_diaobo_exclude_areaIds, "16,113"), StringPool.COMMA)
                .contains(Convert.toStr(req.getAreaId()))) {
            //调拨单 指定门店不进行查询
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "当前门店不进行待办推送");
            return R.success(result);
        }
        return R.error("调用方法继续往下执行");
    }

    private R<Page<OrderNoticeRes>> checkOrderNoticeParam(OrderNoticeReq req) {
        Integer userId = req.getUserId();
        Integer xtenant = req.getXtenant();
        if (req == null) {
            return R.error("参数对象不能为空");
        }
        if (CollUtil.isEmpty(req.getOrderTypes())) {
            return R.error("订单类型不能为空");
        }
        if (xtenant == null) {
            return R.error("租户id不能为空！");
        }
        return R.success("参数校验通过");
    }

    /**
     * 通知枚举转为结果对象
     *
     * @param moaUrl
     * @param udo
     * @return
     */
    private OrderNoticeRes toOrderNotice(String moaUrl, UndoneOrderBo udo) {
        if (udo == null) {
            return null;
        }

        return Arrays.stream(OrderNoticeV3Enum.values()).filter(v -> Objects.equals(udo.getOrderType(), v.getCode())).findFirst()
                .map(v -> {
                    Dict dict = new Dict(BeanMap.create(udo));
                    dict.put("moaUrl", moaUrl);
                    OrderNoticeRes orderNoticeRes = new OrderNoticeRes();
                    orderNoticeRes.setUnKey(StrUtil.format(v.getUnKeyFormat(), dict));
                    orderNoticeRes.setLink(StrUtil.format(v.getLinkFormat(), dict));
                    orderNoticeRes.setTitle(StrUtil.format(v.getTitleFormat(), dict));
                    orderNoticeRes.setTime(ObjectUtil.defaultIfNull(udo.getTime(), LocalDateTime.now())
                            .format(DateTimeFormatter.ofPattern(TimeFormatConstant.YYYY_MM_DD_HH_MM_SS)));
                    orderNoticeRes.setLabel(v.getLabel());
                    return orderNoticeRes;
                })
                .orElse(null);
    }
}
