package com.jiuji.oa.oacore.thirdplatform.order.enums;

import lombok.Getter;

/**
 * 美团国补SN保存接口错误码枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum SaveErrorEnum {
    /**
     * 请求错误
     */
    REQUEST_ERROR(700, "请求错误，请尝试重新请求，如仍有问题，可提报工单寻求帮助"),
    /**
     * 缺少参数，数据不完整
     */
    PARAM_MISSING(701, "缺少参数，数据不完整，请根据文档补充完整入参"),
    /**
     * 参数格式错误
     */
    PARAM_FORMAT_ERROR(705, "参数格式错误，请检查IMEI码上传数量以及格式"),
    /**
     * 不存在此订单
     */
    ORDER_NOT_EXIST(806, "不存在此订单或订单查询时效过期"),
    /**
     * 该订单不是国补/国补订单
     */
    NOT_ALLOWED_ORDER(1199, "该订单不是国补/国补订单，不允许调用此接口"),
    /**
     * 获取需上传的SN、IMEI信息异常
     */
    GET_INFO_ERROR(1201, "获取需上传的SN、IMEI信息异常，请重试或提供工单联系合作中心"),
    /**
     * 订单尚未支付
     */
    ORDER_NOT_PAID(1204, "订单尚未支付，请稍后重试"),
    /**
     * 设备识别信息保存失败
     */
    SAVE_FAILED(1205, "设备识别信息保存失败，请根据处理结果进行解决"),
    /**
     * 设备识别信息已存在
     */
    INFO_ALREADY_EXIST(1209, "设备识别信息已存在，请使用修改操作"),
    /**
     * 设备识别信息不存在
     */
    INFO_NOT_EXIST(1210, "设备识别信息不存在，请使用新增操作"),
    /**
     * 到店自取订单，不允许调用修改上传设备信息
     */
    SELF_PICKUP_NO_MODIFY(1211, "到店自取订单，不允许调用修改上传设备信息"),
    /**
     * 非到店自取订单，仅允许发配前修改设备信息
     */
    ONLY_BEFORE_DELIVERY(1212, "非到店自取订单，仅允许发配前修改设备信息");
    
    private final Integer code;
    private final String message;
    
    SaveErrorEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    
    /**
     * 根据错误码获取错误信息
     * @param code 错误码
     * @return 错误信息
     */
    public static String getMessage(Integer code) {
        for (SaveErrorEnum errorEnum : SaveErrorEnum.values()) {
            if (errorEnum.getCode().equals(code)) {
                return errorEnum.getMessage();
            }
        }
        return "未知错误，错误码: " + code;
    }
} 