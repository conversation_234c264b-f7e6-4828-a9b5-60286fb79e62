package com.jiuji.oa.oacore.tousu.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.utils.XtenantJudgeUtil;
import com.ch999.common.util.vo.Result;
import com.google.common.collect.Lists;
import com.jiuji.cloud.office.service.MsgPushCloud;
import com.jiuji.oa.nc.BaseCommentCloud;
import com.jiuji.oa.nc.DislikeCloud;
import com.jiuji.oa.nc.comment.BaseCommentAddReq;
import com.jiuji.oa.nc.common.req.AttachmentsVO;
import com.jiuji.oa.nc.dislike.DislikeReq;
import com.jiuji.oa.nc.dislike.DislikeRes;
import com.jiuji.oa.oacore.cloud.IMCloud;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.component.UserComponent;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.config.properties.ImageProperties;
import com.jiuji.oa.oacore.common.config.properties.JiujiSystemProperties;
import com.jiuji.oa.oacore.common.constant.RedisKeyConstant;
import com.jiuji.oa.oacore.common.enums.EvaluateTypeEnum;
import com.jiuji.oa.oacore.common.enums.OaMesTypeEnum;
import com.jiuji.oa.oacore.common.enums.SubCollectTypeEnum;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.req.TodoListMqVo;
import com.jiuji.oa.oacore.common.source.MoaUrlSource;
import com.jiuji.oa.oacore.common.util.ActiveProfileJudgeUtil;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.common.util.DecimalFormatUtils;
import com.jiuji.oa.oacore.oaorder.dao.BbsxpUsersMapper;
import com.jiuji.oa.oacore.oaorder.enums.EvaluateJobEnum;
import com.jiuji.oa.oacore.oaorder.po.BbsxpUsers;
import com.jiuji.oa.oacore.oaorder.po.EvaluateScore;
import com.jiuji.oa.oacore.oaorder.po.SubCollection;
import com.jiuji.oa.oacore.oaorder.res.UserBO;
import com.jiuji.oa.oacore.oaorder.service.BbsxpUsersService;
import com.jiuji.oa.oacore.oaorder.service.Ch999UserService;
import com.jiuji.oa.oacore.oaorder.service.EvaluateScoreService;
import com.jiuji.oa.oacore.partner.evaluate.util.SpringContext;
import com.jiuji.oa.oacore.partner.evaluate.util.TenantUtils;
import com.jiuji.oa.oacore.partner.feedback.enums.SmsMsgTypeEnum;
import com.jiuji.oa.oacore.tousu.bo.*;
import com.jiuji.oa.oacore.tousu.constant.ComplainConstant;
import com.jiuji.oa.oacore.tousu.dao.TouSuDepartMapper;
import com.jiuji.oa.oacore.tousu.dao.TouSuMapper;
import com.jiuji.oa.oacore.tousu.dao.TousuAreaMapper;
import com.jiuji.oa.oacore.tousu.enums.*;
import com.jiuji.oa.oacore.tousu.po.*;
import com.jiuji.oa.oacore.tousu.req.AddProcessReq;
import com.jiuji.oa.oacore.tousu.res.AttachmentsRes;
import com.jiuji.oa.oacore.tousu.service.*;
import com.jiuji.oa.oacore.tousu.vo.IpInfoRes;
import com.jiuji.oa.oacore.tousu.vo.req.ProcessReq;
import com.jiuji.oa.oacore.tousu.vo.req.TouSuDemarcationReq;
import com.jiuji.oa.oacore.tousu.vo.req.TouSuProcessReq;
import com.jiuji.oa.oacore.tousu.vo.res.MemberRes;
import com.jiuji.oa.oacore.tousu.vo.res.TouSuDepartRes;
import com.jiuji.oa.oacore.tousu.vo.res.TouSuDutyUserRes;
import com.jiuji.oa.oacore.tousu.vo.res.TouSuEndInfoRes;
import com.jiuji.oa.oacore.tousu.vo.res.TouSuRes;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.departinfo.client.DepartInfoClient;
import com.jiuji.oa.orginfo.member.client.MemberClient;
import com.jiuji.oa.orginfo.member.res.MemberBasicRes;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserInfoVo;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.office.BaseCommentRefTypeEnum;
import com.jiuji.tc.utils.enums.office.BaseLikeRefTypeEnum;
import com.jiuji.tc.utils.xtenant.Namespaces;
import lombok.extern.slf4j.Slf4j;
import org.apache.avalon.framework.service.ServiceException;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-20
 */
@Service
@Slf4j
@DS("office")
public class TouSuDepartServiceImpl extends ServiceImpl<TouSuDepartMapper, TousuDepart> implements TouSuDepartService {
    @Autowired
    private TousuAreaServiceImpl tousuAreaService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private TousuAreaMapper tousuAreaMapper;
    @Autowired
    private TouSuServiceImpl touSuService;
    @Resource
    private ImageProperties imageProperties;
    @Resource
    private TouSuDepartMapper touSuDepartMapper;
    @Resource
    private BbsxpUsersMapper bbsxpUsersMapper;
    @Autowired
    private AttachmentsService attachmentsService;
    @Autowired
    private TouSuMapper touSuMapper;
    @Autowired
    private SmsService smsService;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Autowired
    private TsProcessService tsProcessService;
    @Autowired
    private DepartInfoClient departInfoClient;
    @Autowired
    private TousuAnswerService tousuAnswerService;
    @Resource
    private ITousuTimeoutService tousuTimeoutService;
    @Resource
    private UserInfoClient userInfoClient;
    @Resource
    private MsgPushCloud msgPushCloud;
    @Resource
    private UserComponent userComponent;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private MoaUrlSource moaUrlSource;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private ComplainPushRelationService complainPushRelationService;
    private static final int COMPLAETE_STATE = 3;
    @Resource
    private Ch999UserService ch999UserService;
    @Resource
    private MemberClient memberClient;
    @Resource
    private IMCloud imCloud;
    @Resource
    private EvaluateScoreService evaluateScoreService;
    @Resource
    private BbsxpUsersService bbsxpUsersService;
    @Resource(name = "oaAsyncRabbitTempe")
    private RabbitTemplate oaAsyncRabbitTemplate;
    @Resource
    private DislikeCloud dislikeCloud;
    @Resource
    private BaseCommentCloud baseCommentCloud;
    @Resource
    private JiujiSystemProperties jiujiSystemProperties;

    private static final String SPOT = ".";
    /**
     * 客评投诉高级权值 99
     */
    private static String COMPLAIN_HIGH_ONE = "99";
    /**
     * 客评投诉高级权值 gjkp
     */
    private static String COMPLAIN_HIGH_TWO = "gjkp";

    /**
     * 获得会员信息
     *
     * @param id
     */
    @Override
    @DS("officeWrite")
    public MemberRes getMemberBasicInfo(Integer id, String mobile, Integer tag) throws Exception {
        TouSuModel touSu = tousuAreaService.getTouSu(id);

        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        MemberRes member = new MemberRes();
        // 判断投诉是否被删且是否有高级权值
        if (!isDel(touSu)) {
            return member;
        }
        // 更新投诉人手机号
        if (StringUtils.isNotEmpty(mobile)) {
            tousuAreaService.saveMobile(oaUserBO.getUserName(), mobile, touSu);
        }
        // 更新投诉tag
        if (tag != null && !Objects.equals(tag, touSu.getTag())) {
            Integer oldTag = touSu.getTag();
            touSu.setTag(tag);
            tousuAreaService.updateTousuModel(touSu);
            if (!Objects.equals(tag, oldTag)) {
                TouSuProcessBO process = new TouSuProcessBO();
                process.setTsId(touSu.getId());
                process.setOpUser(oaUserBO.getUserName());
                process.setShow(false);
                process.setNotice(false);
                process.setDsc(String.format("投诉性质由%s修改为%s", Objects.nonNull(oldTag) ? TouSuTagEnum.getNameByTag(oldTag) : "-", TouSuTagEnum.getNameByTag(tag)));
                process.setAttachFiles("");
                touSuService.addTsProcess(process, null);
            }

        }
        //会员电话
        if (Objects.isNull(touSu.getUserId())) {
            return member;
        }
        BbsxpUsers bbsxpUsers = bbsxpUsersMapper.getInfoByUserId(new LambdaUpdateWrapper<BbsxpUsers>()
                .eq(BbsxpUsers::getId, touSu.getUserId()));
        if (Objects.nonNull(bbsxpUsers)) {
            Map<Integer, Boolean> judgeUserSpecialMap = bbsxpUsersService.judgeUserSpecial(Lists.newArrayList(bbsxpUsers.getId()));
            member.setMemberMobile(touSu.getMobile());
            member.setMemberRealName(bbsxpUsers.getRealname());
            if (touSu.getMemberName() != null) {
                member.setMemberUserName(touSu.getMemberName());
            } else {
                member.setMemberUserName(bbsxpUsers.getUserName());
            }
            member.setRealMemberMobile(bbsxpUsers.getMobile());
            member.setUserclassName(CommonUtil.getUserType(bbsxpUsers.getUserclass()));
            member.setXtenant(EnumUtil.getMessageByCode(XtenantEnum.class, bbsxpUsers.getXtenant()));
            member.setTcount(touSu.getTcount());
            member.setUserId(touSu.getUserId());
            member.setTag(EnumUtil.getMessageByCode(TouSuTagEnum.class, touSu.getTag()));
            member.setAddTime(touSu.getAddTime());
            member.setDealTimeout(touSu.getDealTimeout());
            member.setTimeOut(touSu.getTimeOut());
            member.setCustomerEndTime(touSu.getCustomerEndTime());
            member.setSupplier(touSu.getSupplier());
            // 查询ip信息
            List<IpInfoRes> ipInfo = getIpInfo(touSu.getWriteIp());
            String ip = null;
            if (CollectionUtils.isNotEmpty(ipInfo) && StringUtils.isNotEmpty(touSu.getWriteIp())) {
                ip = StrUtil.format(ComplainConstant.IP_WORD, touSu.getWriteIp(), StringUtils.isNotBlank(ipInfo.get(0).getShortArea()) ? ipInfo.get(0).getShortArea() : ipInfo.get(0).getArea() , ipInfo.get(0).getOperator());
            }
            member.setWriteIp(ip != null ? ip : touSu.getWriteIp());
            member.setEmail(touSu.getEmail());
            member.setSpecialFlag(judgeUserSpecialMap.get(bbsxpUsers.getId()));
            if (Boolean.TRUE.equals(member.getSpecialFlag())) {
                member.setSpecialIcon(ComplainConstant.SPECIAL_USER_ICON);
            }
        }
        R<Integer> smsChannelR = sysConfigClient.getSmsChannel(oaUserBO.getAreaId(), 2);
        if (CommonUtils.isRequestSuccess(smsChannelR)) {
            member.setSmsChannel(smsChannelR.getData());
        }
        return member;
    }

    /**
     * 投诉基础信息
     *
     * @param id
     * @return
     */
    @Override
    public R getTouSuInfo(Integer id) {
        TouSuModel touSu = tousuAreaService.getTouSu(id);
        // 判断投诉是否被删且是否有高级权值
        if (!isDel(touSu)) {
            // 和前端约定的code 50004
            return R.error(50004,"您没有查看权限");
        }

        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        TouSuRes touSuRes = new TouSuRes();
        BeanUtils.copyProperties(touSu, touSuRes);
        touSuRes.setDeleteFlag(touSu.getIsdel());
        List<TousuAnswer> list = tousuAnswerService.list(new LambdaQueryWrapper<TousuAnswer>().eq(TousuAnswer::getTsId, id));
        if (CollectionUtils.isNotEmpty(list)) {
            String content = touSu.getContent();
            String question = "";
            String answer = "";
            for (TousuAnswer tousuAnswer : list) {
                List<AnswerBO> bos = JSONObject.parseArray(tousuAnswer.getAnswer(), AnswerBO.class);
                for (AnswerBO answerBO : bos) {
                    if (answerBO.getLabel()) {
                        answer = answerBO.getAnswer();
                    }
                }
                question += tousuAnswer.getQuestion() + answer;
            }
            content = question + "<br/>" + content;
            touSuRes.setContent(content);
        }
        // 业务流程图
        touSuRes.setBusinessFlowChart(getTousuBusinessFlowChart());
        if (StringUtils.isNotBlank(touSu.getDealUser())) {
            touSuRes.setDealUsers(touSu.getDealUser());
        }

        List<UserBO> userList = bbsxpUsersService.getUserList(Objects.nonNull(touSu.getUserId()) ? String.valueOf(touSu.getUserId()) : "");
        if (CollectionUtils.isNotEmpty(userList)) {
            UserBO userBO = userList.get(0);
            touSuRes.setMemberStarLevel(userComponent.getUserStarLevel(userBO.getGrowth()));
        }
        touSuRes.setTsTypeStr(EnumUtil.getMessageByCode(TsTypeEnum.class, touSu.getType()));
        List<TouSuDepartRes> touSuDeparts = tousuAreaMapper.getTouSuDepartsByTsIds(Lists.newArrayList(touSu.getId()), Boolean.TRUE);
        if (CollectionUtils.isNotEmpty(touSuDeparts)) {
            touSuRes.setCat(touSuDeparts.get(0).getCat());
            if (ComplaintCatEnum.SHARE.getCode().equals(touSuDeparts.get(0).getCat())) {
                touSuRes.setCatName("警示投诉");
            } else {
                touSuRes.setCatName(ComplaintCatEnum.of(touSuDeparts.get(0).getCat()) == null ? "" : ComplaintCatEnum.of(touSuDeparts.get(0).getCat()).getMessage());
            }
        }
        DislikeReq dislikeReq = new DislikeReq();
        dislikeReq.setCh999Id(oaUserBO.getUserId());
        dislikeReq.setRefTypes(Lists.newArrayList(BaseLikeRefTypeEnum.COMPLAINT.getCode()));
        dislikeReq.setRefIds(Lists.newArrayList(touSu.getId().longValue()));
        R<List<DislikeRes>> dislikeRes = dislikeCloud.batchGet(dislikeReq);
        if (dislikeRes.isSuccess() && CollectionUtils.isNotEmpty(dislikeRes.getData())) {
            DislikeRes dislike = dislikeRes.getData().get(0);
            touSuRes.setLikeCount(dislike.getLikeCount());
            touSuRes.setLikeStatus(dislike.getLikeFlag());
            touSuRes.setDislikeCount(dislike.getDislikeCount());
            touSuRes.setDislikeStatus(dislike.getDislikeFlag());
        }
        touSuRes.setFollowFlag(CollectionUtils.isNotEmpty(touSuMapper.getTsCollection(touSu.getId().longValue(), Objects.nonNull(oaUserBO.getUserId()) ? oaUserBO.getUserId() : 0, SubCollectTypeEnum.SUB_COLLECT_TYPE_TYPE_4.getCode())));
        return R.success(touSuRes);
    }

    /**
     * 获取投诉管理的业务流程图
     *
     * @return 业务流程图
     */
    private String getTousuBusinessFlowChart() {
        R<String> result = sysConfigClient.getValueByCode(SysConfigConstant.TOUSU_BUSINESS_FLOW_CHART);
        if (Objects.nonNull(result)
                && result.isSuccess()
                && StringUtils.isNotEmpty(result.getData())) {
            return result.getData();
        }
        return StringUtils.EMPTY;
    }

    /**
     * 投诉处理跟进
     *
     * @param touSuProcess
     * @return
     */
    @Override
    public String touSuProcessDeal(TouSuProcessReq touSuProcess) {
        Assert.isTrue(StringUtils.isNotBlank(touSuProcess.getOpUser()), "操作人不能为空");

        TouSuModel touSu = tousuAreaMapper.getTouSuById(touSuProcess.getTsId());

        // 超时处理
        handleTimeout(touSuProcess.getStates(), touSu);

        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (CollectionUtils.isNotEmpty(touSuProcess.getFiles())) {
            String attachIds = ((TouSuDepartService) AopContext.currentProxy()).uploadFiles(touSuProcess.getFiles());
            touSuProcess.setAttachFiles(attachIds);
        }

        TouSuProcessBO process = new TouSuProcessBO();
        BeanUtils.copyProperties(touSuProcess, process);
        process.setOpUser(oaUserBO.getUserName());

        Integer oldstate = touSuProcess.getOldStates();


        Integer state = touSuProcess.getStates();
        if (ActiveProfileJudgeUtil.isJiuJiEnvironment() && TousuStateEnum.TousuStateEnum_11.getCode().equals(state)
                && TouSuTagEnum.COMPLAIN.getCode().equals(touSu.getTag()) && StringUtils.isEmpty(touSu.getDealUser())) {
            throw new CustomizeException("请先填写投诉处理人再完结客诉");
        }
        String noticeMsg = "";
        boolean haveItem = touSuProcess.getNoticeUsers() != null && touSuProcess.getNoticeUsers().size() > 0;
        Boolean isPartner = ComplainConstant.PARTNER.equals(touSu.getType());
        if (haveItem) {
            List<TouSuProcessReq.NoticeUsers> noticeUsers = touSuProcess.getNoticeUsers();
            noticeMsg = SpringContext.isJiuJiEnvironment() ? "(内部聊天 已对接通知:" : "(站内信 已对接通知:";
            for (TouSuProcessReq.NoticeUsers noticeUser : noticeUsers) {
                tousuAreaService.addNoticeTouSu(process.getTsId(), noticeUser.getCh999Id(), noticeUser.getNoticeUser(), process.getDsc(), process.getEndTime(), touSu, oaUserBO.getUserName(), state != null ? state : oldstate, touSuProcess.getPlatform());
                noticeMsg += noticeUser.getNoticeUser() + "、";
            }
            noticeMsg = noticeMsg.substring(0, noticeMsg.length() - 1) + ")";
        }
        // 判断是不是通知人来处理进程,仅查主动通知的 noticeType = 1
        List<TousuNotice> notices = tousuAreaMapper.getTouSuNoticeByTsId(process.getTsId(), oaUserBO.getUserId());
        if (CollectionUtil.isNotEmpty(notices)) {
            notices.forEach(notice -> {
                // 修改发送通知状态
                tousuAreaMapper.updateStatusById(notice.getId(), oaUserBO.getUserId(), process.getTsId());
                // 待办事项处理完毕
                oaAsyncRabbitTemplate.convertAndSend("office.direct.to_do_list",JSON.toJSONString(TodoListMqVo.builder().type(17).businessNo(notice.getId()+"").staffId(notice.getToUserId()).mqKind(2).build()));
                //
                // 超时的情况 添加一条进程
                LocalDateTime now = LocalDateTime.now();
                // 判断该通知是否添加过投诉超时记录
                Integer timeOutCount = tousuAreaMapper.getFollowTimeoutCountByUserAndTouSuId(process.getTsId(), oaUserBO.getUserId(), notice.getId());
                // 是否超时
                boolean isTimeOut = now.isAfter(notice.getLastTime());
                if (isTimeOut && timeOutCount == 0) {
                    Duration duration = Duration.between(now, notice.getLastTime());
                    long minutes = Math.abs(duration.toMinutes());
//                    touSuMapper.saveTouSuProcess(new TouSuProcessBO()
//                            .setTsId(process.getTsId())
//                            .setOpUser("系统")
//                            .setIntime(touSuProcess.getIntime())
//                            .setCate(0)
//                            .setDsc(notice.getToUserName() + "跟进超时,超时时长" + minutes + "分钟"));

                    TsProcess tsProcess = new TsProcess();
                    tsProcess.setTsId(process.getTsId());
                    tsProcess.setOpUser("系统");
                    tsProcess.setCate(0);
                    tsProcess.setIntime(LocalDateTime.now());
                    tsProcess.setDsc(notice.getToUserName() + "跟进超时,超时时长" + this.calculateDuration(minutes));
                    tsProcessService.saveProcess(tsProcess);

                    // 跟进人超时记录
                    tousuTimeoutService.insertTimeoutRecord(
                            new TousuTimeout()
                                    .setTousuId(touSu.getId())
                                    .setType(TousuTimeoutTypeEnum.FOLLOW_TIMEOUT.getCode())
                                    .setTimeout(notice.getLastTime())
                                    .setHandleTime(LocalDateTime.now())
                                    .setStatus(TousuTimeoutStatusEnum.ALREADY_HANDLE.getCode())
                                    .setFollowUserId(notice.getToUserId())
                                    .setNoticeId(notice.getId())
                                    .setPartner(isPartner)
                    );
                }
            });
        }
        // 通知关注的人
        if (Objects.nonNull(touSu.getId()) && StringUtils.isNotEmpty(touSuProcess.getDsc())) {
            String buttonName = state != null
                    && state.equals(TousuStateEnum.TousuStateEnum_11.getCode())
                    && StringUtils.isNotEmpty(touSuProcess.getButtonName())
                    ? "客诉完结" : touSuProcess.getButtonName();
            this.noticeTsFollower(touSu.getId(), buttonName, touSuProcess.getDsc(), oaUserBO.getUserId());
        }

        //状态变更处理
        try {
            if (state != null && StringUtils.isNotEmpty(touSuProcess.getButtonName())) {
                if (state.equals(TousuStateEnum.TousuStateEnum_11.getCode())) {
                    process.setDsc("客诉完结：" + process.getDsc());
                    tousuAreaMapper.updateCustomerEndTime(oaUserBO.getUserName(), touSuProcess.getTsId());
                } else {
                    process.setDsc(process.getDsc() + "<span style='color:red;'>*" + touSuProcess.getButtonName() + "</span>");
                }
                // 在【已还原】状态下 点击【事件还原】状态依然是已还原
                // 在【已处理】状态下 点击【事件还原、已处理】按钮 状态依然是已处理
                // 上述情况都不更新状态
                boolean restoreCheck = Objects.equals(touSu.getStates(), 7) && StringUtils.equals("事件还原", touSuProcess.getButtonName());
                boolean processedCheck = Objects.equals(touSu.getStates(), 5)
                        && (StringUtils.equals("事件还原", touSuProcess.getButtonName()) || StringUtils.equals("已处理", touSuProcess.getButtonName()));
                if (state != 11 && !restoreCheck && !processedCheck) {
                    tousuAreaService.updateTouSuStatus(touSuProcess.getTsId(), oldstate, state, oaUserBO.getUserName(), touSuProcess.getDsc());
                    // 如果状态变更成完成，主动删除所有待办
                    if (XtenantJudgeUtil.isJiujiMore() && Objects.equals(state,TousuStateEnum.TousuStateEnum_3.getCode())){
                        List<TousuNotice> noticeList = tousuAreaMapper.getTouSuNoticeByTsId(process.getTsId(), null);
                        if (CollectionUtils.isNotEmpty(noticeList)){
                            noticeList.forEach(tousuNotice-> oaAsyncRabbitTemplate.convertAndSend("office.direct.to_do_list",JSON.toJSONString(TodoListMqVo.builder().type(17).businessNo(tousuNotice.getId()+"").staffId(tousuNotice.getToUserId()).mqKind(2).build())));
                        }
                    }
                }
            } else {
                if (process.getCate() != null && process.getCate() == 1) {
                    process.setDsc(process.getDsc() + " <span style='color:red;'>*客户追问</span>");
                } else {
                    process.setDsc(process.getDsc() + " <span style='color:red;'>*处理</span>");
                }
            }
            process.setDsc(process.getDsc() + noticeMsg);
            touSuService.addTsProcess(process, touSu);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return e.getMessage();
        }

        // 合作伙伴投诉推送处理
        if (ComplainConstant.PARTNER.equals(touSu.getType())) {
            handlePartnerPush(touSu, touSuProcess);
        }
        // 投诉完结推送消息
        if (ActiveProfileJudgeUtil.isJiuJiEnvironment() && TousuStateEnum.TousuStateEnum_11.getCode().equals(state)
                && TouSuTagEnum.COMPLAIN.getCode().equals(touSu.getTag()) && StringUtils.isNotBlank(touSu.getDealUser())) {
            try {
                handleComplainMessage(touSu);
            } catch (Exception e) {
                log.error("投诉完结评价推送处理异常", e);
            }
        }
        // 界定类型的进程同步添加到评论表中
        if (ActiveProfileJudgeUtil.isJiuJiEnvironment() && Objects.equals(touSuProcess.getCate(), 7)) {
            try {
                BaseCommentAddReq addReq = new BaseCommentAddReq();
                addReq.setRefId(touSu.getId().longValue());
                addReq.setRefType(BaseCommentRefTypeEnum.COMPLAINT_DEMARCATION.getCode());
                addReq.setContent(touSuProcess.getDsc());
                addReq.setUserId(oaUserBO.getUserId());
                addReq.setUserName(oaUserBO.getUserName());
                // nc的附件表没有视频封面字段，这里不新建字段存，直接传值给filePath，播放地址前端用fid获取
                if (CollectionUtils.isNotEmpty(touSuProcess.getFiles())) {
                    List<AttachmentsVO.FileBO> attachmentList = touSuProcess.getFiles().stream().map(f -> {
                        AttachmentsVO.FileBO fileBO = new AttachmentsVO.FileBO();
                        fileBO.setFid(f.getFid());
                        if (StringUtils.isNotBlank(f.getFramePath())) {
                            fileBO.setFilePath(f.getFramePath());
                            fileBO.setFileUrl(f.getFramePath());
                        } else {
                            fileBO.setFilePath(f.getFilepath());
                            fileBO.setFileUrl(f.getFilepath());
                        }
                        fileBO.setFileName(f.getFilename());
                        return fileBO;
                    }).collect(Collectors.toList());
                    addReq.setAttachmentsList(attachmentList);
                }
                log.info("进程同步添加评论,addReq:{}", JSON.toJSONString(addReq));
                R<Boolean> comment4Cloud = baseCommentCloud.addComment4Cloud(addReq);
                log.info("进程同步添加评论,res:{}", JSON.toJSONString(comment4Cloud));
            } catch (Exception e) {
                log.error("进程同步添加评论处理异常", e);
            }
        }
        return "成功";
    }

    @Override
    public Boolean saveTsDemarcation(TouSuDemarcationReq touSuDemarcationReq) {
        OaUserBO oaUser = abstractCurrentRequestComponent.getCurrentStaffId();
        TouSuModel touSuModel = touSuService.getById(touSuDemarcationReq.getTsId());
        if (Objects.isNull(touSuModel)) {
            throw new CustomizeException("对应投诉不存在");
        }
        try {
            // 这里的界定目前不用传附件
            BaseCommentAddReq addReq = new BaseCommentAddReq();
            addReq.setRefId(touSuModel.getId().longValue());
            addReq.setRefType(BaseCommentRefTypeEnum.COMPLAINT_DEMARCATION.getCode());
            addReq.setContent(touSuDemarcationReq.getDsc());
            addReq.setUserId(oaUser.getUserId());
            addReq.setUserName(oaUser.getUserName());
            log.info("添加投诉界定,addReq:{}", JSON.toJSONString(addReq));
            R<Boolean> comment4Cloud = baseCommentCloud.addComment4Cloud(addReq);
            log.info("添加投诉界定,res:{}", JSON.toJSONString(comment4Cloud));
            if (comment4Cloud.isSuccess()) {
                this.noticeTsFollower(touSuModel.getId(), "投诉界定", touSuDemarcationReq.getDsc(), oaUser.getUserId());
            }
        } catch (Exception e) {
            log.error("添加投诉界定处理异常", e);
            throw new CustomizeException("添加投诉界定处理异常");
        }
        // 通知
        if (Boolean.TRUE.equals(touSuDemarcationReq.getNotice()) && CollectionUtils.isNotEmpty(touSuDemarcationReq.getNoticeUsers())) {
            for (TouSuDemarcationReq.NoticeUsers noticeUser : touSuDemarcationReq.getNoticeUsers()) {
                tousuAreaService.addNoticeTouSu(touSuModel.getId(), noticeUser.getCh999Id(), noticeUser.getNoticeUser(), touSuDemarcationReq.getDsc(),
                        touSuDemarcationReq.getEndTime(), touSuModel, oaUser.getUserName(), touSuModel.getStates(), touSuDemarcationReq.getPlatform());
            }
            // 记录日志，归到投诉进程下
            TouSuProcessBO process = new TouSuProcessBO();
            List<String> noticeUsers = touSuDemarcationReq.getNoticeUsers().stream().map(TouSuDemarcationReq.NoticeUsers::getNoticeUser).collect(Collectors.toList());
            process.setTsId(touSuModel.getId())
                    .setOpUser(oaUser.getUserName())
                    // cate=9 代表投诉处理
                    .setCate(9)
                    .setDsc("添加投诉界定，并通过内部聊天对接通知" + StringUtils.join(noticeUsers, ","))
                    .setNotice(false);
            touSuService.addTsProcess(process, touSuModel);
        }
        return true;
    }

    /**
     * 发送通知给投诉关注人
     */
    private void noticeTsFollower(Integer tsId, String buttonName, String content, Integer currentUserId) {
        try {
            List<SubCollection> tsCollection = touSuMapper.getTsCollection(tsId.longValue(), null, SubCollectTypeEnum.SUB_COLLECT_TYPE_TYPE_4.getCode());
            if (CollectionUtils.isNotEmpty(tsCollection)) {
                String followContent = String.format("投诉ID【%s】%s，详细内容：%s", tsId, StringUtils.isNotBlank(buttonName) ? buttonName : "处理", content);
                List<Integer> pushUserIds = tsCollection.stream()
                        .map(SubCollection::getCh999Id)
                        .filter(ch999Id -> !Objects.equals(ch999Id, currentUserId))
                        .distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(pushUserIds)) {
                    String link = String.format("%s/new/#/complain/detail/%s", jiujiSystemProperties.getMoa(), tsId);
                    smsService.sendOaMsg(followContent, link, StringUtils.join(pushUserIds, ","), OaMesTypeEnum.TSTZ);
                }
            }
        } catch (Exception e) {
            log.error("发送通知给投诉关注人处理异常", e);
        }
    }

    private String calculateDuration(long duration) {
        if (duration >= NumberConstant.SIXTY) {
            return duration / 60 + "小时" + duration % 60 + "分钟";
        }
        return duration + "分钟";
    }

    private void handleComplainMessage(TouSuModel touSu) {
        boolean wxFollowFlag = false;
        // 评价界面
        String complaintEvaluateShortLink = ch999UserService.getComplaintEvaluateShortLink(touSu.getId());
        if (StringUtils.isBlank(complaintEvaluateShortLink)) {
            throw new CustomizeException("生成投诉短链失败");
        }
        String evaluateUrl = "9ji.cn/TS" + complaintEvaluateShortLink;
        R<Object> wxFollowStatus = memberClient.checkUserWxFollowStatus(touSu.getUserId(), (int) Namespaces.get());
        if (wxFollowStatus != null && wxFollowStatus.getData() != null && wxFollowStatus.getData() instanceof Boolean) {
            wxFollowFlag = (boolean) wxFollowStatus.getData();
        }
        Result<String> evaluateMsg = new Result<>();
        String wxOpenIdByUserId = StringUtils.EMPTY;
        if (wxFollowFlag) {
            Integer wxId = 1;
            R<Integer> imCloudWxId = imCloud.getWxId(Namespaces.get());
            if (imCloudWxId.isSuccess() && Objects.nonNull(imCloudWxId.getData())) {
                wxId = imCloudWxId.getData();
            }
            wxOpenIdByUserId = ch999UserService.getWxOpenIdByUserId(touSu.getUserId(), wxId);
            if (StringUtils.isNotBlank(wxOpenIdByUserId)) {
                evaluateMsg = imCloud.sendServiceEvaluateMsg(wxOpenIdByUserId, evaluateUrl, "诚邀请您对本次投诉处理的服务人员做出评价", touSu.getProcessUser(), "投诉处理", "感谢您的使用");
                log.info("客诉完结微信推送,evaluateMsg:{}" , evaluateMsg.toString());
            } else {
                log.error("客诉完结,获取用户微信openid失败,tsId:{}", touSu.getId());
            }
        }
        // 推送失败或者没关注
        if (!wxFollowFlag || !evaluateMsg.isSuccess() || StringUtils.isBlank(wxOpenIdByUserId)) {
            String content = "亲爱的顾客，为给您提供更好的消费体验，现邀请您对本次投诉处理的服务人员评价，点击" + evaluateUrl + "进行评价";
            R<MemberBasicRes> memberBasicInfo = memberClient.getMemberBasicInfo(touSu.getUserId());
            if (memberBasicInfo.isSuccess() && Objects.nonNull(memberBasicInfo.getData()) && StringUtils.isNotBlank(memberBasicInfo.getData().getMobile())) {
                String mobile = memberBasicInfo.getData().getMobile();
                String addTime = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm").format(LocalDateTime.now());
                R<Boolean> sendSmsNew = smsService.sendSmsNew(mobile, content, addTime, "系统", SmsMsgTypeEnum.MARKETING.getCode(), (int) Namespaces.get());
                log.info("客诉完结短信推送,sendSmsNew:{}", sendSmsNew.toString());
            } else {
                log.error("客诉完结,获取用户手机号失败,tsId:{}", touSu.getId());
            }
        }
        // 推送站内消息
        String appContent = "尊敬的顾客，您的投诉现已处理完成，为帮助我们更好的改进，现邀请您对我们的服务做出评价";
        pushAppMsg(touSu.getUserId(), appContent, complaintEvaluateShortLink);
        R<Ch999UserVo> userByUserName = userInfoClient.getCh999UserByUserName(touSu.getProcessUser());
        Ch999UserVo userVo = new Ch999UserVo();
        if (userByUserName.isSuccess() && Objects.nonNull(userByUserName.getData())) {
            userVo = userByUserName.getData();
        }
        List<EvaluateScore> scoreList = Lists.newArrayList();
        EvaluateScore processEvaluateScore = new EvaluateScore();
        processEvaluateScore.setJob(EvaluateJobEnum.FOLLOWUPPERSON.getCode());
        processEvaluateScore.setRelateCh999Id(userVo.getCh999Id());
        processEvaluateScore.setSubId(touSu.getId());
        processEvaluateScore.setUserid(touSu.getUserId());
        processEvaluateScore.setAreaid(userVo.getArea1id());
        processEvaluateScore.setType(EvaluateTypeEnum.COMPLAINT.getCode());
        processEvaluateScore.setDtime(LocalDate.now());
        scoreList.add(processEvaluateScore);
        List<Integer> dealUserIds = Arrays.stream(StringUtils.split(touSu.getDealUser(), ",")).filter(StringUtils::isNumeric).map(Integer::parseInt).collect(Collectors.toList());
        List<Ch999UserInfoVo> dealUsers = Lists.newArrayList();
        R<List<Ch999UserInfoVo>> ch999UsersInfo = userInfoClient.getUserInfoByUserId(dealUserIds);
        if (ch999UsersInfo.isSuccess() && CollectionUtils.isNotEmpty(ch999UsersInfo.getData())) {
            dealUsers = ch999UsersInfo.getData();
        }
        dealUsers.forEach(dealUser -> {
            EvaluateScore evaluateScore = new EvaluateScore();
            evaluateScore.setJob(EvaluateJobEnum.DEALPERSON.getCode());
            evaluateScore.setRelateCh999Id(dealUser.getCh999Id());
            evaluateScore.setSubId(touSu.getId());
            evaluateScore.setUserid(touSu.getUserId());
            evaluateScore.setAreaid(dealUser.getArea1Id());
            evaluateScore.setType(EvaluateTypeEnum.COMPLAINT.getCode());
            evaluateScore.setDtime(LocalDate.now());
            scoreList.add(evaluateScore);
        });
        evaluateScoreService.batchSave(scoreList);

        TsProcess tsProcess = new TsProcess();
        tsProcess.setTsId(touSu.getId());
        tsProcess.setOpUser("系统");
        tsProcess.setCate(0);
        tsProcess.setIntime(LocalDateTime.now());
        tsProcess.setDsc("推送投诉评价");
        tsProcessService.saveProcess(tsProcess);
    }

    public void pushAppMsg(Integer userId, String msg, String shortUrl) {
        if (Objects.isNull(userId) || StringUtils.isBlank(msg) || StringUtils.isBlank(shortUrl)) {
            return;
        }
        String webUrl = StringUtils.EMPTY;
        R<String> mUrl = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.M_URL, (int) Namespaces.get());
        if (mUrl.isSuccess() && StringUtils.isNotBlank(mUrl.getData())) {
            webUrl = mUrl.getData();
        }
        if (StringUtils.isBlank(webUrl)) {
            return;
        }
        String finalUrl = String.format(ComplainConstant.WEB_COMPLAINT_URL, webUrl, shortUrl);
        // 商城消息中心
        UserMsg userMsg = new UserMsg()
                .setUserID(userId)
                .setTitle("服务评价提醒")
                .setContent(msg)
                .setLink(finalUrl)
                // 消息通知
                .setKindID(12)
                .setAddTime(new Date());
        HttpRequest.post(String.format(ComplainConstant.WEB_APP_MSG_URL, webUrl)).body(JSON.toJSONString(userMsg)).execute().body();
        Map<String, Object> params = new HashMap<>(16);
        HashMap<String, String> extra = new HashMap<>();
        extra.put("value", "");
        extra.put("type", "3");
        // 必填，应用名称，和App上报的注册信息保持一致； oa就是oa，商城是web
        params.put("appName", "web");
        // 必填，推送消息的标题，长度不能超过20字符，中英文均算一个字符；
        params.put("title", "服务评价提醒");
        // 必填，推送消息的内容，建议长度不超过50字符，中英文均算一个字符，超过50个字符的，将自动转成前47个字符加上...的形式推送；
        params.put("content", msg);
        // 可选，推送消息的业务附加消息，格式必须为json；
        params.put("extra", JSON.toJSONString(extra));
        // 可选，当使用别名推送消息时，指定该字段，最多100个alias；推送会员id
        params.put("alias", Collections.singletonList(userId));
        // 极光推送
        HttpRequest.post(String.format(ComplainConstant.WEB_APP_PUSH_URL, webUrl)).body(JSON.toJSONString(params)).execute().body();
    }

    /**
     * 处理超时统计
     */
    private void handleTimeout(Integer state, TouSuModel touSu) {
        LocalDateTime addTime =touSu.getAddTime();
        Integer touSuId = touSu.getId();
        Boolean partner = ComplainConstant.PARTNER.equals(touSu.getType());
        if (state != null && addTime != null) {
            TousuDictionaryItem stateEnum = TousuDictionaryItem.of(state);
            switch (stateEnum) {
                case TOUSU_DICTIONARY_2:
                    // 受理超时判定
                    judgeIsTimeoutAndSaveRecord(TousuTimeoutTypeEnum.HANDLE_TIMEOUT.getCode(), addTime, TousuTimeoutTypeEnum.HANDLE_TIMEOUT.getOverTime(), touSuId, partner);
                    break;
                case TOUSU_DICTIONARY_7:
                    // 还原超时判定
                    judgeIsTimeoutAndSaveRecord(TousuTimeoutTypeEnum.REDUCTION_TIMEOUT.getCode(), addTime, TousuTimeoutTypeEnum.REDUCTION_TIMEOUT.getOverTime(), touSuId, partner);
                    break;
                case TOUSU_DICTIONARY_3:
                    // 完结超时判定
                    judgeIsTimeoutAndSaveRecord(TousuTimeoutTypeEnum.FINISH_TIMEOUT.getCode(), addTime, TousuTimeoutTypeEnum.FINISH_TIMEOUT.getOverTime(), touSuId, partner);
                    break;
                default:
            }
        }

    }

    /**
     * 判断是否超时，如果超时保存超时记录
     *
     * @param addTime  投诉添加时间
     * @param overTime 多久算超时（分）
     */

    private void judgeIsTimeoutAndSaveRecord(Integer type, LocalDateTime addTime, Long overTime, Integer tousuId, Boolean partner) {
        Duration duration = Duration.between(addTime, LocalDateTime.now());
        if (duration.toMinutes() > overTime) {
            // 已超时，保存超时节点记录
            tousuTimeoutService.insertTimeoutRecord(
                    new TousuTimeout()
                            .setTousuId(tousuId)
                            .setType(type)
                            .setTimeout(addTime.plusMinutes(overTime))
                            .setHandleTime(LocalDateTime.now())
                            .setStatus(TousuTimeoutStatusEnum.ALREADY_HANDLE.getCode())
                            .setPartner(partner)
            );
        }
    }

    /**
     * 扣除个人积分 并通知
     *
     * @param userId     被扣分人id
     * @param tousuPoint 投诉扣分
     * @param tsId       投诉id
     * @param type       投诉类型
     */
    private Integer deductPoints(Integer userId, Integer tousuPoint, Integer tsId, Integer type) {

        Ch999Fen param = new Ch999Fen();
        param.setCh999Id(userId);
        param.setYanyin("投诉处理责任人减积分");
        param.setFen(-tousuPoint);
        param.setInuser("系统");
        // bumenId 不是部门Id
        param.setBumenId(tsId);
        param.setJifenType(3);
        Integer count = tousuAreaMapper.insertCh99Jifen(param);
        String moaUrl = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.MOA_URL, (int) Namespaces.get()).getData();
        String link;
        if (ComplainConstant.PARTNER.equals(type)) {
            link = moaUrl + "/new/#/operation/feedback/jiujiDetail/" + tsId;
        } else {
            link = moaUrl + "/new/#/operation/complaint/links?conplaintid=" + tsId;
        }
        String msg = "您被判定为【投诉ID:" + tsId + "】的责任人，系统将扣除您" + tousuPoint + "积分，如有疑问，请联系客评组。";
        smsService.sendOaAppAndWeiXing(msg, userId + "", 0, link);
        return count;
    }


    /**
     * 保存责任人
     *
     * @param touSuZenRenRen
     * @return
     */
    @Override
    @DS("officeWrite")
    public String saveZeRenRen(TouSuZenRenRen touSuZenRenRen) {
        boolean jiuJiEnv = ActiveProfileJudgeUtil.isJiuJiEnvironment();
        try {
            String userName = abstractCurrentRequestComponent.getCurrentStaffId().getUserName();
            Integer tsId = touSuZenRenRen.getTousuId();
            TouSuModel touSuModel = Optional.ofNullable(touSuService.getById(tsId)).orElseThrow(() -> new ServiceException("投诉" + tsId + "不存在！"));
            TsProcess process = new TsProcess();
            // 投诉扣分
            BigDecimal tousuLosePoint = Optional.ofNullable(touSuZenRenRen.getTousuLosePoint()).orElse(BigDecimal.ZERO);
            // 投诉扣除积分
            Integer tousuPoint = touSuZenRenRen.getTousuPoint();
            if (tousuPoint != null && tousuPoint > 0) {
                // 完成状态下 直接扣除个人积分
                RLock rLock = redissonClient.getFairLock("complain:deduct:point:" + touSuZenRenRen.getId());
                boolean res = rLock.tryLock(4, 20, TimeUnit.SECONDS);
                Integer result;
                if (res) {
                    result = deductPoints(touSuZenRenRen.getUserId(), touSuZenRenRen.getTousuPoint(), tsId, touSuModel.getType());
                } else {
                    throw new CustomizeException("扣除积分失败，请稍后再重试！");
                }

                if (result == 1) {
                    // 插入日志
                    process.setTsId(touSuZenRenRen.getTousuId());
                    process.setOpUser(userName);
                    process.setIntime(LocalDateTime.now());
                    process.setDsc(String.format(ComplainConstant.COMPLAIN_MODIFY_SCORE_ADD, touSuZenRenRen.getUserName(), touSuZenRenRen.getTousuPoint()));
                    if (jiuJiEnv && tousuLosePoint.compareTo(BigDecimal.ZERO) > 0) {
                        process.setDsc(process.getDsc() +
                                String.format("，扣%s分", formatLosePoints(tousuLosePoint)));
                    }
                    tsProcessService.save(process);
                }
            } else {
                process.setTsId(touSuZenRenRen.getTousuId());
                process.setOpUser(userName);
                process.setIntime(LocalDateTime.now());
                process.setDsc(String.format("添加投诉责任人%s", touSuZenRenRen.getUserName()));
                if (jiuJiEnv && tousuLosePoint.compareTo(BigDecimal.ZERO) > 0) {
                    process.setDsc(process.getDsc() + String.format("，扣%s分", formatLosePoints(tousuLosePoint)));
                }
                tsProcessService.save(process);
            }
            touSuZenRenRen.setTousuPoint(Optional.ofNullable(touSuZenRenRen.getTousuPoint()).orElse(0));
            if (jiuJiEnv) {
                touSuZenRenRen.setTousuLosePoint(tousuLosePoint);
                tousuAreaMapper.saveTouSuZenRenRenV2(touSuZenRenRen);
            } else {
                tousuAreaMapper.saveTouSuZenRenRen(touSuZenRenRen);
            }
            this.tsZeRenRenBonus(touSuZenRenRen.getId(), touSuZenRenRen.getTousuId(), touSuZenRenRen.getUserId(), userName, touSuZenRenRen.getBonusPoints(), null, 1, touSuModel.getType());
            return "保存成功";
        } catch (Exception e) {
            log.error(e.getMessage());
            return "保存失败";
        }
    }

    /**
     * 修改责任人
     *
     * @param touSuZenRenRen
     * @return
     */
    @Override
    @DS("officeWrite")
    public String modifyZeRenRen(TouSuZenRenRen touSuZenRenRen) {
        boolean jiuJiEnv = ActiveProfileJudgeUtil.isJiuJiEnvironment();
        try {
            String userName = abstractCurrentRequestComponent.getCurrentStaffId().getUserName();
            Integer tsId = touSuZenRenRen.getTousuId();
            // 投诉扣分
            BigDecimal tousuLosePoint = Optional.ofNullable(touSuZenRenRen.getTousuLosePoint())
                    .orElse(BigDecimal.ZERO);
            TouSuModel touSuModel = Optional.ofNullable(touSuService.getById(tsId)).orElseThrow(() -> new ServiceException("投诉" + tsId + "不存在！"));
            String msg = "";
            TouSuZenRenRen old = tousuAreaMapper.getTousuZeRenRenByUserIdsAndTsId(touSuZenRenRen.getId());
            if (touSuZenRenRen.getTousuPoint() != null) {
                Ch999Fen param = new Ch999Fen();
                TsProcess process = new TsProcess();

                process.setTsId(touSuZenRenRen.getTousuId());
                process.setOpUser(userName);
                process.setIntime(LocalDateTime.now());
                process.setCate(4);
                param.setCh999Id(touSuZenRenRen.getUserId());
                param.setYanyin("投诉责任人扣分");
                param.setFen(-touSuZenRenRen.getTousuPoint());
                param.setBumenId(tsId);
                param.setInuser("系统");
                param.setJifenType(3);
                // 进程
                List<String> processDescs = new ArrayList<>();
                if (old != null && old.getTousuPoint() != null) {
                    int socre = old.getTousuPoint() - touSuZenRenRen.getTousuPoint();
                    // 扣分类型
                    String losePointType = jiuJiEnv ? "扣除积分" : "扣分";
                    // 消息
                    List<String> noticeMsgs = new ArrayList<>();
                    if (socre > 0) {
                        param.setYanyin("投诉责任积分补偿");
                        param.setFen(socre);
                        noticeMsgs.add(String.format("%s修改为%s分，系统补偿您%s积分",
                                losePointType, touSuZenRenRen.getTousuPoint(), socre));
                        processDescs.add(String.format(ComplainConstant.COMPLAIN_MODIFY_SCORE_BACK,
                                touSuZenRenRen.getUserName(), losePointType, old.getTousuPoint(), touSuZenRenRen.getTousuPoint(), socre));
                        tousuAreaMapper.insertCh99Jifen(param);
                    }
                    if (socre < 0) {
                        param.setYanyin("投诉责任积分再扣除");
                        param.setFen(socre);
                        noticeMsgs.add(String.format("%s修改为%s分，系统再扣除您%s积分",
                                losePointType, touSuZenRenRen.getTousuPoint(), -socre));
                        processDescs.add(String.format(ComplainConstant.COMPLAIN_MODIFY_SCORE_DEDUCT,
                                touSuZenRenRen.getUserName(), losePointType, old.getTousuPoint(), touSuZenRenRen.getTousuPoint(), Math.abs(socre)));
                        tousuAreaMapper.insertCh99Jifen(param);
                    }
                    BigDecimal loseScore = Optional.ofNullable(old.getTousuLosePoint())
                            .orElse(BigDecimal.ZERO)
                            .subtract(tousuLosePoint);
                    if (loseScore.compareTo(BigDecimal.ZERO) > 0) {
                        noticeMsgs.add(String.format("扣分修改为%s分，系统补偿您%s分",
                                touSuZenRenRen.getTousuLosePoint(), formatLosePoints(tousuLosePoint)));
                        processDescs.add(String.format(ComplainConstant.COMPLAIN_MODIFY_LOSE_SCORE_BACK,
                                touSuZenRenRen.getUserName(), formatLosePoints(old.getTousuLosePoint()),
                                formatLosePoints(touSuZenRenRen.getTousuLosePoint()), formatLosePoints(loseScore)));
                    }
                    if (loseScore.compareTo(BigDecimal.ZERO) < 0) {
                        noticeMsgs.add(String.format("扣分修改为%s分，系统再扣除您%s分",
                                formatLosePoints(touSuZenRenRen.getTousuLosePoint()), formatLosePoints(tousuLosePoint)));
                        processDescs.add(String.format(ComplainConstant.COMPLAIN_MODIFY_LOSE_SCORE_DEDUCT,
                                touSuZenRenRen.getUserName(), formatLosePoints(old.getTousuLosePoint()),
                                formatLosePoints(touSuZenRenRen.getTousuLosePoint()),
                                formatLosePoints(tousuLosePoint)));
                    }
                    if (CollectionUtils.isNotEmpty(noticeMsgs)) {
                        msg = String.format("【投诉ID:%s】的判断结果有变更，判定结果%s，请及时确认，如有疑问，请联系客评组。",
                                tsId, String.join("；", noticeMsgs));
                    }
                } else {
                    // 消息
                    List<String> noticeMsgs = new ArrayList<>();
                    noticeMsgs.add(String.format("扣除您%s积分", touSuZenRenRen.getTousuPoint()));
                    processDescs.add(String.format(ComplainConstant.COMPLAIN_MODIFY_SCORE_ADD,
                            touSuZenRenRen.getUserName(), touSuZenRenRen.getTousuPoint()));
                    if (jiuJiEnv && tousuLosePoint.compareTo(BigDecimal.ZERO) > 0) {
                        noticeMsgs.add(String.format("扣除您%s分", formatLosePoints(touSuZenRenRen.getTousuLosePoint())));
                        processDescs.add(String.format(ComplainConstant.COMPLAIN_MODIFY_LOSE_SCORE_ADD,
                                touSuZenRenRen.getUserName(), formatLosePoints(touSuZenRenRen.getTousuLosePoint())));
                    }
                    if (CollectionUtils.isNotEmpty(noticeMsgs)) {
                        msg = String.format("您被判定为【投诉ID:%s】的责任人，系统将%s，如有疑问，请联系客评组。",
                                tsId, String.join("；", noticeMsgs));
                    }
                    tousuAreaMapper.insertCh99Jifen(param);
                }
                if (CollectionUtils.isNotEmpty(processDescs)) {
                    process.setDsc(String.join("；", processDescs));
                }

                if (StringUtils.isNotEmpty(process.getDsc())) {
                    tsProcessService.save(process);
                }

                if (StringUtils.isNotEmpty(msg)) {
                    String moaUrl = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.MOA_URL, (int) Namespaces.get()).getData();
                    String link;
                    if (ComplainConstant.PARTNER.equals(touSuModel.getType())) {
                        link = moaUrl + "/new/#/operation/feedback/jiujiDetail/" + tsId;
                    } else {
                        link = moaUrl + "/new/#/operation/complaint/links?conplaintid=" + tsId;
                    }
                    R<Boolean> flag = msgPushCloud.singleSend(touSuZenRenRen.getUserId(), msg, link, 0, userComponent.getXtenant(true));
                    if (!flag.isSuccess() || !flag.getData()) {
                        log.error("投诉责任人消息发送失败，userId：{}，msg", touSuZenRenRen.getUserId(), msg);
                    }
                }
            }
            if (jiuJiEnv) {
                touSuZenRenRen.setTousuLosePoint(tousuLosePoint);
                tousuAreaMapper.updateZeRenRenTousuPoints(touSuZenRenRen);
            } else {
                tousuAreaMapper.updateZeRenRen(touSuZenRenRen);
            }
            Integer bonusPoints = touSuZenRenRen.getBonusPoints();
            if (Objects.isNull(bonusPoints)) {
                bonusPoints = 0;
            }
            this.tsZeRenRenBonus(touSuZenRenRen.getId(), touSuZenRenRen.getTousuId(), touSuZenRenRen.getUserId() , userName, bonusPoints, old, 2, touSuModel.getType());
            return "修改成功";
        } catch (Exception e) {
            log.error(e.getMessage());
            return "修改失败";
        }
    }

    /**
     * 删除责任人
     *
     * @param zenRenRen
     * @return String
     */
    @Override
    @DS("officeWrite")
    public String deleteZeRenRen(TouSuZenRenRen zenRenRen) {
        try {
            String userName = abstractCurrentRequestComponent.getCurrentStaffId().getUserName();
            TouSuModel touSuModel = Optional.ofNullable(touSuService.getById(zenRenRen.getTousuId())).orElseThrow(() -> new CustomizeException("投诉" + zenRenRen.getTousuId() + "不存在！"));
            TouSuZenRenRen old = tousuAreaMapper.getTousuZeRenRenByUserIdsAndTsId(zenRenRen.getId());

            int count = tousuAreaMapper.deleteZeRenRen(zenRenRen);
            if (count == 1) {

                Integer point = Optional.ofNullable(old.getTousuPoint()).orElse(0);
                BigDecimal losePoint = Optional.ofNullable(old.getTousuLosePoint()).orElse(BigDecimal.ZERO);
                if (point > 0) {
                    // 退还积分
                    Ch999Fen param = new Ch999Fen();
                    param.setCh999Id(zenRenRen.getUserId());
                    param.setFen(zenRenRen.getTousuPoint());
                    param.setBumenId(zenRenRen.getTousuId());
                    param.setFen(old.getTousuPoint());
                    param.setYanyin("投诉责任积分补偿");
                    param.setInuser("系统");
                    param.setJifenType(3);
                    tousuAreaMapper.insertCh99Jifen(param);
                }


                TsProcess process = new TsProcess();
                process.setTsId(zenRenRen.getTousuId());
                process.setOpUser(userName);
                process.setIntime(LocalDateTime.now());
                // dsc
                String processDsc = String.format(ComplainConstant.COMPLAIN_MODIFY_SCORE_DELETE, zenRenRen.getUserName(), point);
                if (losePoint.compareTo(BigDecimal.ZERO) > 0) {
                    processDsc += String.format(ComplainConstant.COMPLAIN_MODIFY_LOSE_SCORE_DELETE, zenRenRen.getUserName(), formatLosePoints(losePoint));
                }
                process.setDsc(processDsc);
                tsProcessService.save(process);
                Integer bonusPoints = old.getBonusPoints();
                old.setBonusPoints(0);
                this.tsZeRenRenBonus(zenRenRen.getId(), zenRenRen.getTousuId(), zenRenRen.getUserId(), userName, bonusPoints, old, 3, touSuModel.getType());
                return "删除成功";
            }
            return "删除失败";
        } catch (Exception e) {
            log.error(e.getMessage());
            return "删除失败";
        }
    }

    /**
     * 投诉责任人奖励积分
     */
    private void tsZeRenRenBonus(Integer id, Integer tsId, Integer userId, String opUser, int fen,
                                 TouSuZenRenRen oldEntity, Integer type, Integer tsType) {
        if (Objects.nonNull(oldEntity) && Objects.equals(fen, oldEntity.getBonusPoints())) {
            return;
        }
        if (fen == 0 && Objects.nonNull(oldEntity) && Objects.isNull(oldEntity.getBonusPoints())) {
            return;
        }
        String userName = "";
        R<Ch999UserVo> ch999UserByUserId = userInfoClient.getCh999UserByUserId(userId);
        if (ch999UserByUserId.isSuccess() && Objects.nonNull(ch999UserByUserId.getData()) && StringUtils.isNotBlank(ch999UserByUserId.getData().getCh999Name())) {
            userName = ch999UserByUserId.getData().getCh999Name();
        }
        RLock rLock = redissonClient.getFairLock("complainZenRenRen:bonus:point:" + id);
        try {
            boolean res = rLock.tryLock(2, 10, TimeUnit.SECONDS);
            if (!res) {
                log.error("投诉责任人奖励积分，获取锁失败");
                return;
            }
            int oldFen = (Objects.isNull(oldEntity) || Objects.isNull(oldEntity.getBonusPoints())) ? 0 : oldEntity.getBonusPoints();
            int finalFen = (Objects.isNull(oldEntity) || Objects.isNull(oldEntity.getBonusPoints())) ? fen : fen - oldEntity.getBonusPoints();
            String comment = "";
            switch (type) {
                case 1:
                    comment = String.format("%s同学获得客户表扬，收获%s积分，感谢您的辛苦付出与真挚服务！", userName, finalFen);
                    break;
                case 2:
                    int commentFen = Math.abs(finalFen);
                    comment = String.format("投诉责任人积分由%s修改为%s，%s", oldFen, fen, (finalFen >= 0 ? "需补发" : "需扣减") + commentFen + "积分");
                    break;
                case 3:
                    comment = String.format("删除投诉责任人，需扣减%s积分奖励", finalFen);
                    break;
                default:
            }
            Ch999Fen param = new Ch999Fen();
            param.setCh999Id(userId);
            param.setFen(finalFen);
            param.setBumenId(tsId);
            param.setYanyin(comment);
            param.setInuser("系统");
            param.setJifenType(3);
            tousuAreaMapper.insertCh99Jifen(param);
            log.info("奖励积分成功，id: {}, 分值: {}", id, finalFen);
            TsProcess process = new TsProcess();
            process.setTsId(tsId);
            process.setOpUser(opUser);
            process.setIntime(LocalDateTime.now());
            process.setDsc(comment);
            tsProcessService.save(process);
            String link;
            if (ComplainConstant.PARTNER.equals(tsType)) {
                link = jiujiSystemProperties.getMoa() + "/new/#/operation/feedback/jiujiDetail/" + tsId;
            } else {
                link = jiujiSystemProperties.getMoa() + "/new/#/operation/complaint/links?conplaintid=" + tsId;
            }
            SpringUtil.getBean(SmsService.class).sendOaMsg(comment, link, userId.toString(), OaMesTypeEnum.SYSTEM);
        } catch (Exception e) {
            log.error("投诉责任人奖励积分处理异常:{}", e.getMessage(), e);
        } finally {
            if (rLock != null && rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }

    /**
     * 勾选显示进程
     *
     * @param touSuProcess 进程
     * @return
     */
    @Override
    @DS("officeWrite")
    public R<Boolean> isShowProcessInfo(List<TsProcess> touSuProcess) {
        // 仅允许勾选一条进程为网站可见
        long showWebCount = touSuProcess.stream()
                .filter(item -> Boolean.TRUE.equals(item.getShowWeb()))
                .count();
        if (showWebCount > 1) {

            return R.error("仅允许勾选一条进程为网站可见");
        }
        // 更新进程
        Boolean updateResult = tsProcessService.updateBatchProcessById(touSuProcess);
        // 勾选了进程网站可见，则投诉默认为网站可见
        if (showWebCount > 0) {
            Integer tsId = touSuProcess.get(0).getTsId();
            touSuService.updateTousuToShowWeb(tsId);
        }
        return R.success(updateResult);
    }

    @Override
    public String processDeal(ProcessReq processReq) {
        try {
            OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
            TouSuProcessBO process = new TouSuProcessBO();
//            BeanUtils.copyProperties(processReq, process);
//            process.setCate(0);
//            process.setOpUser(oaUserBO.getUserName());
            Integer oldstate = processReq.getOldStates();
            Integer state = processReq.getStates();
            TsProcess tsProcess = new TsProcess();
            BeanUtils.copyProperties(processReq, tsProcess);
            tsProcess.setCate(0);
            tsProcess.setOpUser(oaUserBO.getUserName());
            tsProcess.setIsShow(process.getShow());
            tsProcess.setIntime(LocalDateTime.now());
            //状态变更处理
            if (state != null) {
                tousuAreaService.updateTouSuStatus(tsProcess.getTsId(), oldstate, state, oaUserBO.getUserName(), tsProcess.getDsc());
                tsProcessService.saveProcess(tsProcess);
//                touSuMapper.saveTouSuProcess(process);
            } else {
                tsProcessService.saveProcess(tsProcess);
//                touSuMapper.saveTouSuProcess(process);
            }
        } catch (Exception e) {
            return e.getMessage();
        }
        return "成功";
    }

    @Override
    @DS("officeWrite")
    public Boolean delete(TousuDepart req) {
        return this.removeById(req);
    }

    @Override
    @DS("officeWrite")
    public Boolean add(TousuDepart touSuDepartReq) {
        return this.save(touSuDepartReq);
    }

    @Override
    @DS("officeWrite")
    public Boolean modifyTouSuDepart(TousuDepart touSuDepartReq) {
        return this.updateById(touSuDepartReq);
    }

    @DS("officeWrite")
    @Override
    public Boolean addProcess(AddProcessReq processReq) {
        TsProcess process = new TsProcess();
        process.setTsId(processReq.getComplainId());
        process.setOpUser("系统");
        process.setIntime(LocalDateTime.now());
        process.setCate(4);
        process.setIsShow(processReq.getShowWebFlag());
        process.setDsc(processReq.getContent());
        return tsProcessService.saveProcess(process);
    }


    /**
     * 投诉结果信息
     *
     * @param id
     * @return
     */
    @Override
    public TouSuEndInfoRes getTouSuEndInfo(Integer id) {
        TouSuEndInfoRes touSuEndInfoRes = new TouSuEndInfoRes();

        List<TouSuZenRenRen> tousuZeRenRen = tousuAreaMapper.getTousuZeRenRen(id);
        TouSuModel touSu = tousuAreaService.getTouSu(id);
        // 判断投诉是否被删且是否有高级权值
        if (!isDel(touSu)) {
            return touSuEndInfoRes;
        }

        if (CollectionUtils.isNotEmpty(tousuZeRenRen)) {
            // 责任人所属地区，判断是否前后端，前端区分展示颜色
            List<Integer> userIds = tousuZeRenRen.stream().map(TouSuZenRenRen::getUserId).distinct().collect(Collectors.toList());
            R<List<Ch999UserVo>> staffByIds = userInfoClient.getCh999UsersInfoWithLeave(userIds);
            List<Ch999UserVo> staffList = new ArrayList<>();
            if (staffByIds.isSuccess() && CollectionUtils.isNotEmpty(staffByIds.getData())) {
                staffList = staffByIds.getData();
            }
            Map<Integer, Integer> staffAreaIdMap = staffList.stream().collect(Collectors.toMap(Ch999UserVo::getCh999Id, Ch999UserVo::getArea1id, (s1, s2) -> s1));
            Collection<Integer> staffAreaIds = staffAreaIdMap.values();
            List<AreaInfo> areaInfoList = new ArrayList<>();
            R<List<AreaInfo>> listAreaInfo = areaInfoClient.listAreaInfo(new ArrayList<>(staffAreaIds));
            if (listAreaInfo.isSuccess() && CollectionUtils.isNotEmpty(listAreaInfo.getData())) {
                areaInfoList = listAreaInfo.getData();
            }
            Map<Integer, Integer> areaTypeMap = areaInfoList.stream().collect(Collectors.toMap(AreaInfo::getId, AreaInfo::getAreaType, (a1, a2) -> a2));
            List<TouSuZenRenRen> collect = tousuZeRenRen.stream().
                    filter(t -> t.getUserId() != null).collect(Collectors.toList());
            if (collect.size() > 0) {
                List<TouSuZenRenRen> list = tousuAreaMapper.getComplaintByDate(tousuZeRenRen);
                Map<Integer, List<TouSuZenRenRen>> listMap = list.stream()
                        .collect(Collectors.groupingBy(TouSuZenRenRen::getUserId));
                //一条投诉同一个人多次,去重
                HashMap<Integer, Integer> hashMap = new HashMap<>();
                Iterator<Map.Entry<Integer, List<TouSuZenRenRen>>> iterator = listMap.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<Integer, List<TouSuZenRenRen>> next = iterator.next();
                    hashMap.put(next.getKey(), next.getValue().stream()
                            .collect(Collectors.groupingBy(TouSuZenRenRen::getTousuId)).size());

                }
                tousuZeRenRen.stream().forEach(t -> t.setComplaintCount(hashMap.get(t.getUserId()) != null ?
                        hashMap.get(t.getUserId()) : 0));
            }
            List<TouSuDutyUserRes> dutyUserResList = tousuZeRenRen.stream().map(it -> {
                TouSuDutyUserRes dutyUserRes = new TouSuDutyUserRes();
                dutyUserRes.setId(it.getId());
                dutyUserRes.setTousuId(it.getTousuId());
                dutyUserRes.setUserId(it.getUserId());
                dutyUserRes.setUserName(it.getUserName());
                dutyUserRes.setDepartId(it.getDepartId());
                dutyUserRes.setArea1id(it.getArea1id());
                dutyUserRes.setTousuRank(it.getTousuRank());
                dutyUserRes.setTousuPoint(it.getTousuPoint());
                dutyUserRes.setTousuLosePoint(it.getTousuLosePoint());
                dutyUserRes.setComplaintCount(it.getComplaintCount());
                dutyUserRes.setAreaType(areaTypeMap.get(staffAreaIdMap.get(it.getUserId())));
                dutyUserRes.setBonusPoint(it.getBonusPoints());
                return dutyUserRes;
            }).collect(Collectors.toList());
            touSuEndInfoRes.setZeRenRenList(dutyUserResList);
        }
        TouSuEndInfoRes moneyById = touSuDepartMapper.getMoneyById(id);
        if (moneyById != null) {
            touSuEndInfoRes.setBonusMoney(moneyById.getBonusMoney());
            touSuEndInfoRes.setFineMoney(moneyById.getFineMoney());
        }
        List<TouSuDepartRes> departs = touSuDepartMapper.getTouSuDepartByTsId(id);

        if (CollectionUtils.isNotEmpty(departs)) {
            List<Integer> touSuDepartIds = departs.stream().map(TouSuDepartRes::getId).collect(Collectors.toList());
//     根据投诉Id查找所有投诉原因
            List<TousuCategoryRelationBO> tousuCategoryRelation = touSuDepartMapper.getTouSUCategoryRelation(id, touSuDepartIds);
//     根据 departId 卡片分组
            Map<Integer, List<TousuCategoryRelationBO>> map = new HashMap<>();
            if (CollectionUtils.isNotEmpty(tousuCategoryRelation)) {
                map = tousuCategoryRelation.stream().filter(touSuCategoryRelationBO -> touSuCategoryRelationBO.getDepartId() != null).collect(Collectors.groupingBy(TousuCategoryRelationBO::getDepartId));
            }
            for (TouSuDepartRes touSuDepart : departs) {
                if (map.containsKey(touSuDepart.getId())) {
                    List<TousuCategoryRelationBO> relation = map.get(touSuDepart.getId());
                    List<Integer> cateIds = relation.stream().distinct().map(TousuCategoryRelationBO::getCateId).collect(Collectors.toList());
                    //投诉原因
                    if (CollectionUtils.isNotEmpty(cateIds)) {
                        if (ComplainConstant.PARTNER.equals(touSu.getType())) {
                            touSuDepart.setTouSuTypes(cateIds);
                            List<TouSuTypeBO> stringMap = getPartnerTouSuCategoryRelation(cateIds);
                            touSuDepart.setTouSuType(stringMap);
                        }else {
                            touSuDepart.setTouSuTypes(cateIds);
                            List<TouSuTypeBO> stringMap = setTouSuCategoryRelation(cateIds);
                            touSuDepart.setTouSuType(stringMap);
                        }
                    }
                }

                tousuAreaService.touSuDepartSetName(touSuDepart, departInfoClient);
            }
            touSuEndInfoRes.setTouSuDeparts(departs);
        }
        BigDecimal bonusMoney = touSu.getBonusMoney();
        // 投诉微信现金
        BigDecimal wechatMoney = complainPushRelationService.getWechatMoneyByComplainId(id);
        if (wechatMoney.compareTo(BigDecimal.ZERO) > 0) {
            bonusMoney = wechatMoney.add(Optional.ofNullable(bonusMoney)
                    .orElse(BigDecimal.ZERO));
        }
        touSuEndInfoRes.setBonusMoney(bonusMoney);
        touSuEndInfoRes.setCouponMoney(touSu.getBonusMoney());
        touSuEndInfoRes.setWechatMoney(wechatMoney);
        touSuEndInfoRes.setFineMoney(touSu.getFineMoney());

        return touSuEndInfoRes;
    }

    private List<TouSuTypeBO> getPartnerTouSuCategoryRelation(List<Integer> cateIds) {
        List<TousuCategory> list = tousuAreaMapper.getAllTousuCategoryWithNoKind();
        Map<Integer, TousuCategory> categoryMap = list.stream().collect(Collectors.toMap(TousuCategory::getId, Function.identity()));
        return getCateShow(cateIds, categoryMap);
    }

    // 疯狂套娃
    public static List<TouSuTypeBO> getCateShow(List<Integer> ids, Map<Integer, TousuCategory> categoryMap) {
        ArrayList<TouSuTypeBO> res = new ArrayList<>();
        for (Integer id : ids) {
            TousuCategory tousuCategory = categoryMap.get(id);
            if (tousuCategory != null) {
                TousuCategory parent1 = categoryMap.get(tousuCategory.getParentId());
                // 只有一级
                if (parent1 == null) {
                    TouSuTypeBO bo = new TouSuTypeBO();
                    bo.setName(tousuCategory.getName());
                    bo.setValue(tousuCategory.getName());
                    res.add(bo);
                } else {
                    TousuCategory parent2 = categoryMap.get(parent1.getParentId());
                    // 只有二级
                    if (parent2 == null) {
                        TouSuTypeBO bo = new TouSuTypeBO();
                        bo.setName(parent1.getName());
                        bo.setValue(tousuCategory.getName());
                        res.add(bo);
                    } else {
                        TouSuTypeBO bo = new TouSuTypeBO();
                        bo.setName(parent2.getName() + "-" + parent1.getName());
                        bo.setValue(tousuCategory.getName());
                        res.add(bo);
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(res)) {
            ArrayList<TouSuTypeBO> res1 = new ArrayList<>();
            Map<String, List<TouSuTypeBO>> resMap = res.stream().collect(Collectors.groupingBy(TouSuTypeBO::getName));
            resMap.forEach((k, v) -> {
                if (v.size() > 1) {
                    TouSuTypeBO bo = new TouSuTypeBO();
                    bo.setName(k);
                    bo.setValue(v.stream().map(TouSuTypeBO::getValue).collect(Collectors.joining("，")));
                    res1.add(bo);
                } else {
                    res1.add(v.get(0));
                }
            });
            res = res1;
        }
        return res;
    }

    private List<TouSuTypeBO> setTouSuCategoryRelation(List<Integer> cateIds) {
        List<TouSuTypeBO> types = new ArrayList<>();
        List<TousuCategory> list = tousuAreaService.listTousuCategoryByKind(0);
        List<TousuCategory> categoryList = touSuDepartMapper.getTouSuCategoryByIds(cateIds);
        for (TousuCategory childrenCategory : categoryList) {
//            得到 pid==0的 category
            TousuCategory parentCategory = getTouSuCategoryParents(list, childrenCategory);
            if (parentCategory != null) {
                TouSuTypeBO type = new TouSuTypeBO();
                type.setName(parentCategory.getName()).setValue(childrenCategory.getName());
                types.add(type);
            }
        }
        List<TouSuTypeBO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(types)) {
            Map<String, List<TouSuTypeBO>> map = types.stream().collect(Collectors.groupingBy(TouSuTypeBO::getName));
            map.forEach((key, value1) -> {
                TouSuTypeBO res = new TouSuTypeBO();
                String value = value1.stream().map(TouSuTypeBO::getValue).collect(Collectors.joining(","));
                res.setName(key);
                res.setValue(value);
                result.add(res);
            });
        }
        return result;
    }

    private TousuCategory getTouSuCategoryParents(List<TousuCategory> list, TousuCategory childrenCategory) {
        Integer parentId = childrenCategory.getParentId();
        childrenCategory = list.stream().filter(p -> p.getId().equals(parentId)).findFirst().orElse(null);
        if (childrenCategory != null && !childrenCategory.getParentId().equals(0)) {
            return getTouSuCategoryParents(list, childrenCategory);
        }
        return childrenCategory;
    }

    /**
     * 保存附件信息
     *
     * @param files 附件
     * @return
     */
    @Override
    @DS("smallpro_write")
    public String uploadFiles(List<Attachments> files) {

        List<Integer> id = new ArrayList<>();
        String ids = "";
        try {
            for (Attachments attachments : files) {
                if (attachments.getFid() != null && attachments.getFilename() != null) {
                    attachments.setDtime(LocalDateTime.now());
                    setExtension(attachments);
                    attachmentsService.save(attachments);
                    id.add(attachments.getId());
                } else {
                    return "附件上传参数不对";
                }
            }
            ids = StringUtils.join(id, ",");
        } catch (Exception e) {
            log.error(e.getMessage());
            return ids;
        }
        return ids;
    }

    /**
     * 存储附件后缀
     */
    private void setExtension(Attachments attachments) {
        if (StringUtils.isBlank(attachments.getExtension())) {
            if (attachments.getFilename().lastIndexOf(SPOT) != -1 && attachments.getFilename().lastIndexOf(SPOT) != 0) {
                attachments.setExtension(SPOT + attachments.getFilename().substring(attachments.getFilename().lastIndexOf(SPOT) + 1));
            }
        }
    }

    @Override
    public String removeFiles(Attachments files) {
        LambdaQueryWrapper<Attachments> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(files.getId() != null, Attachments::getId, files.getId());
        return attachmentsService.remove(wrapper) ? "删除成功" : "删除失败";
    }



    /**
     * 展示进程
     *
     * @param id
     * @param requestType
     * @return
     */
    @Override
    public List<TsProcess> showProcessInfo(Integer id, String requestType) {
        List<TsProcess> tsProcessByTsId = new ArrayList<>();

        TouSuModel touSu = tousuAreaService.getTouSu(id);
        // 判断投诉是否被删且是否有高级权值
        if (!isDel(touSu)) {
            return tsProcessByTsId;
        }

        tsProcessByTsId = tousuAreaMapper.getTsProcessByTsId(id);

        for (TsProcess tsProcess : tsProcessByTsId) {
            if (org.apache.commons.lang.StringUtils.isNotEmpty(tsProcess.getAttachFiles())) {
                // 投诉附件信息
                List<AttachVO> attarUrls = new ArrayList<>();
                List<AttachmentsRes> touSuPicByIds = tousuAreaMapper.getTouSuPicByIds(tsProcess.getAttachFiles());
                if (CollectionUtils.isNotEmpty(touSuPicByIds)) {
                    for (AttachmentsRes a : touSuPicByIds) {
                        String fid = StringUtils.isNotBlank(a.getFid()) ? a.getFid().replace(",", "/") : a.getFid();
                        String imgUrl = imageProperties.getSelectImgUrl() + "newstatic/" + fid;
                        AttachVO attach = new AttachVO();
                        attach.setName(a.getFilename());
                        attach.setFramePath(a.getFramePath());
                        attach.setExtension(a.getExtension());
                        attach.setFid(a.getFid());
                        attach.setImgUrl(imgUrl);
                        attarUrls.add(attach);
                    }
                }
                tsProcess.setAttachHtml(attarUrls);
            }
            // 如果是m版请求，替换url
            if ("m".equals(requestType) && tsProcess.getDsc() != null && tsProcess.getDsc().contains("punish/PunishDetail")) {
                String dsc = tsProcess.getDsc().replace("/punish/PunishDetail", "/m/mpunish/editpunish");
                tsProcess.setDsc(dsc);
            }
        }
        return tsProcessByTsId;
    }

    private boolean isDel(TouSuModel touSu){
        boolean flag = true;
        // 判断是否被删除
        if (touSu.getIsdel() != null && touSu.getIsdel()) {
            OaUserBO userBO = abstractCurrentRequestComponent.getCurrentStaffId();
            // 有gjkp（高级客评）和99权限的可以看已删除投诉
            Predicate<OaUserBO> biPredicate = (user) ->
                    user != null && user.getRank() != null && (user.getRank().contains(COMPLAIN_HIGH_TWO) || user.getRank().contains(COMPLAIN_HIGH_ONE));
            flag = biPredicate.test(userBO);
        }
        return flag;
    }

    private String changeFid(String fileName, String fid) {
        String suffix = "";
        if (StringUtils.isNotBlank(fileName) && StringUtils.contains(fileName, ".")) {
            suffix = org.apache.commons.lang3.StringUtils.substring(fileName, fileName.lastIndexOf(".") + 1);
        }
        if (StringUtils.isNotBlank(fid)) {
            return fid.replace(",", "/") + "." + suffix;
        }
        return "";
    }

    private void handlePartnerPush(TouSuModel complain, TouSuProcessReq touSuProcess) {
        // 获取投诉受理人
        String complainReceiver = tousuAreaMapper.getComplainReceiver(complain.getId());
        Integer xtenant = userComponent.getXtenant(true);
        if (StringUtils.isNotEmpty(complainReceiver) && !touSuProcess.getOpUser().equals(complainReceiver)) {
            R<Ch999UserVo> ch999UserR = userInfoClient.getCh999UserByUserName(complainReceiver);
            if (ch999UserR.getCode() == ResultCode.SUCCESS && ch999UserR.getData() != null) {
                String moaUrl = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.MOA_URL, (int) Namespaces.get()).getData();
                String link = moaUrl + "/new/#/operation/feedback/jiujiDetail/" + complain.getId();
                // 给受理人推送
                R<Boolean> pushR = msgPushCloud.singleSend(ch999UserR.getData().getCh999Id(), String.format(ComplainConstant.ACCEPT_PUSH_MESSAGE, complain.getId(), touSuProcess.getDsc()), link, OaMesTypeEnum.HZHBTSJYTZ.getCode(), xtenant);
                if (!pushR.isSuccess() || !pushR.getData()) {
                    log.error("给受理人推送失败，投诉id：{}, 内容：{}", complain.getId(), touSuProcess.getDsc());
                }
            }
        }

        // 显示给合作伙伴的推送消息
        if (touSuProcess.getShow()) {
            // 投诉发起人推送
            R<Boolean> pushR = msgPushCloud.singleSend(complain.getUserId(), String.format(ComplainConstant.COMPLAIN_PUSH_MESSAGE, complain.getId(), touSuProcess.getDsc()), "", OaMesTypeEnum.TSCLJCTZ.getCode(), TenantUtils.tenantToxTenant(complain.getXtenant()));
            if (!pushR.isSuccess() || !pushR.getData()) {
                log.error("给投诉发起人失败，投诉id：{}, 内容：{}", complain.getId(), touSuProcess.getDsc());
            }
        }
    }

    private List<IpInfoRes> getIpInfo(String ip) {
        if (StringUtils.isEmpty(ip)) {
            return null;
        }

        // 先查缓存
        String redisResult = stringRedisTemplate.opsForValue().get(RedisKeyConstant.IP_REDIS_KEY_V2+ip);
        if (StringUtils.isNotEmpty(redisResult)) {
            return JSON.parseObject(redisResult, new TypeReference<List<IpInfoRes>>() {
            });
        }
        R<List<IpInfoRes>> listR = new R<>();
        try {
            String s = HttpUtil.get(StrUtil.format(ComplainConstant.IP_INFO_URL, ip));
            listR = JSON.parseObject(s, new TypeReference<R<List<IpInfoRes>>>() {
            });
        } catch (Exception e) {
            log.error("查询ip信息失败,ip:{}",ip,e.getMessage());
        }

        if (!listR.isSuccess() || CollectionUtils.isEmpty(listR.getData())) {
            return null;
        }

        // 缓存7天
        stringRedisTemplate.opsForValue().set(RedisKeyConstant.IP_REDIS_KEY_V2 + ip, JSON.toJSONString(listR.getData()), 7, TimeUnit.DAYS);
        return listR.getData();
    }

    private String formatLosePoints(BigDecimal losePoint) {
        return DecimalFormatUtils.decimalFormat(losePoint, "#.##");
    }

}
