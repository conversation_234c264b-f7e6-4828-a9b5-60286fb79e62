package com.jiuji.oa.oacore.thirdplatform.doudian.service.Impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.doudian.open.api.afterSale_Detail.AfterSaleDetailRequest;
import com.doudian.open.api.afterSale_Detail.AfterSaleDetailResponse;
import com.doudian.open.api.afterSale_Detail.data.AfterSaleDetailData;
import com.doudian.open.api.afterSale_Detail.param.AfterSaleDetailParam;
import com.doudian.open.api.instantShopping_createDelivery.InstantShoppingCreateDeliveryRequest;
import com.doudian.open.api.instantShopping_createDelivery.InstantShoppingCreateDeliveryResponse;
import com.doudian.open.api.instantShopping_createDelivery.data.InstantShoppingCreateDeliveryData;
import com.doudian.open.api.instantShopping_createDelivery.param.InstantShoppingCreateDeliveryParam;
import com.doudian.open.api.instantShopping_getDeliveryListByOrderId.InstantShoppingGetDeliveryListByOrderIdRequest;
import com.doudian.open.api.instantShopping_getDeliveryListByOrderId.InstantShoppingGetDeliveryListByOrderIdResponse;
import com.doudian.open.api.instantShopping_getDeliveryListByOrderId.data.InstantShoppingGetDeliveryListByOrderIdData;
import com.doudian.open.api.instantShopping_getDeliveryListByOrderId.param.InstantShoppingGetDeliveryListByOrderIdParam;
import com.doudian.open.api.instantShopping_notifyDeliveryStatus.InstantShoppingNotifyDeliveryStatusRequest;
import com.doudian.open.api.instantShopping_notifyDeliveryStatus.InstantShoppingNotifyDeliveryStatusResponse;
import com.doudian.open.api.instantShopping_notifyDeliveryStatus.data.InstantShoppingNotifyDeliveryStatusData;
import com.doudian.open.api.instantShopping_notifyDeliveryStatus.param.InstantShoppingNotifyDeliveryStatusParam;
import com.doudian.open.api.instantShopping_notifyPickingStatus.InstantShoppingNotifyPickingStatusRequest;
import com.doudian.open.api.instantShopping_notifyPickingStatus.InstantShoppingNotifyPickingStatusResponse;
import com.doudian.open.api.instantShopping_notifyPickingStatus.data.InstantShoppingNotifyPickingStatusData;
import com.doudian.open.api.instantShopping_notifyPickingStatus.param.InstantShoppingNotifyPickingStatusParam;
import com.doudian.open.api.instantShopping_reportRiderLocation.InstantShoppingReportRiderLocationRequest;
import com.doudian.open.api.instantShopping_reportRiderLocation.InstantShoppingReportRiderLocationResponse;
import com.doudian.open.api.instantShopping_reportRiderLocation.data.InstantShoppingReportRiderLocationData;
import com.doudian.open.api.instantShopping_reportRiderLocation.param.InstantShoppingReportRiderLocationParam;
import com.doudian.open.api.order_batchDecrypt.OrderBatchDecryptRequest;
import com.doudian.open.api.order_batchDecrypt.OrderBatchDecryptResponse;
import com.doudian.open.api.order_batchDecrypt.data.OrderBatchDecryptData;
import com.doudian.open.api.order_batchDecrypt.param.OrderBatchDecryptParam;
import com.doudian.open.api.order_batchSensitive.OrderBatchSensitiveRequest;
import com.doudian.open.api.order_batchSensitive.OrderBatchSensitiveResponse;
import com.doudian.open.api.order_batchSensitive.data.OrderBatchSensitiveData;
import com.doudian.open.api.order_batchSensitive.param.CipherInfosItem;
import com.doudian.open.api.order_batchSensitive.param.OrderBatchSensitiveParam;
import com.doudian.open.api.order_logisticsAdd.OrderLogisticsAddRequest;
import com.doudian.open.api.order_logisticsAdd.OrderLogisticsAddResponse;
import com.doudian.open.api.order_logisticsAdd.data.OrderLogisticsAddData;
import com.doudian.open.api.order_logisticsAdd.param.OrderLogisticsAddParam;
import com.doudian.open.api.order_logisticsEdit.OrderLogisticsEditRequest;
import com.doudian.open.api.order_logisticsEdit.OrderLogisticsEditResponse;
import com.doudian.open.api.order_logisticsEdit.data.OrderLogisticsEditData;
import com.doudian.open.api.order_logisticsEdit.param.OrderLogisticsEditParam;
import com.doudian.open.api.order_orderDetail.OrderOrderDetailRequest;
import com.doudian.open.api.order_orderDetail.OrderOrderDetailResponse;
import com.doudian.open.api.order_orderDetail.data.ShopOrderDetail;
import com.doudian.open.api.order_orderDetail.param.OrderOrderDetailParam;
import com.doudian.open.api.shop_editStore.ShopEditStoreRequest;
import com.doudian.open.api.shop_editStore.ShopEditStoreResponse;
import com.doudian.open.api.shop_editStore.data.ShopEditStoreData;
import com.doudian.open.api.shop_editStore.param.ShopEditStoreParam;
import com.doudian.open.api.shop_storeSuspend.ShopStoreSuspendRequest;
import com.doudian.open.api.shop_storeSuspend.ShopStoreSuspendResponse;
import com.doudian.open.api.shop_storeSuspend.data.ShopStoreSuspendData;
import com.doudian.open.api.shop_storeSuspend.param.ShopStoreSuspendParam;
import com.doudian.open.api.shop_unsuspendStore.ShopUnsuspendStoreRequest;
import com.doudian.open.api.shop_unsuspendStore.ShopUnsuspendStoreResponse;
import com.doudian.open.api.shop_unsuspendStore.data.ShopUnsuspendStoreData;
import com.doudian.open.api.shop_unsuspendStore.param.ShopUnsuspendStoreParam;
import com.doudian.open.api.sku_list.SkuListRequest;
import com.doudian.open.api.sku_list.SkuListResponse;
import com.doudian.open.api.sku_list.data.DataItem;
import com.doudian.open.api.sku_list.param.SkuListParam;
import com.doudian.open.api.sku_syncStock.SkuSyncStockRequest;
import com.doudian.open.api.sku_syncStock.SkuSyncStockResponse;
import com.doudian.open.api.sku_syncStock.param.SkuSyncStockParam;
import com.doudian.open.exception.DoudianOpException;
import com.doudian.open.msg.instantShopping_DeliveryChange.param.InstantShoppingDeliveryChangeParam;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.oaorder.service.RetryService;
import com.jiuji.oa.oacore.thirdplatform.annotation.AddLogKind;
import com.jiuji.oa.oacore.thirdplatform.annotation.AddOrderLog;
import com.jiuji.oa.oacore.thirdplatform.common.ThirdPlatformCommonConst;
import com.jiuji.oa.oacore.thirdplatform.common.enums.AppTypeEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.LogTypeEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.PlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.DoudianFactory;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.MyAccessToken;
import com.jiuji.oa.oacore.thirdplatform.doudian.enums.DouDianResultEnum;
import com.jiuji.oa.oacore.thirdplatform.doudian.service.DefaultDouDianService;
import com.jiuji.oa.oacore.thirdplatform.oplog.po.MeituanJdWorkLog;
import com.jiuji.oa.oacore.thirdplatform.oplog.service.MeituanJdWorkLogService;
import com.jiuji.oa.oacore.thirdplatform.order.service.LogisticsStatusStrategy;
import com.jiuji.oa.oacore.thirdplatform.order.service.impl.LogisticsStatusContext;
import com.jiuji.oa.oacore.thirdplatform.productconfig.service.ProductConfigService;
import com.jiuji.oa.oacore.thirdplatform.productconfig.vo.ProductConfigVO;
import com.jiuji.oa.oacore.thirdplatform.stock.entity.StockSync;
import com.jiuji.oa.oacore.thirdplatform.store.entity.Store;
import com.jiuji.oa.oacore.thirdplatform.store.service.StoreService;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.TenantService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCodeEnum;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.retry.RetryException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jiuji.oa.oacore.thirdplatform.stock.service.impl.StockServiceImpl.getFinalSyncCount;
import static com.jiuji.oa.oacore.thirdplatform.stock.service.impl.StockServiceImpl.syncCountCalc;

/**
 * <AUTHOR>
 * @Date 13:36
 * @Description
 */
@Service
@Slf4j
public class DefaultDouDianServiceImpl implements DefaultDouDianService {
    @Resource
    private StoreService storeService;
    @Resource
    private ProductConfigService productConfigService;
    @Resource
    private DoudianFactory doudianFactory;
    @Resource
    private MeituanJdWorkLogService meituanJdWorkLogService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private DefaultDouDianService defaultDouDianService;
    @Resource
    private LogisticsStatusContext logisticsStatusContext;
    /**
     * 根据商品ID获取sku列表
     * @param productIdList 商品ID
     * @param myAccessToken  accessToken
     * @return sku列表
     */
    @Override
    public Map<String, DataItem> getSkuList(List<String> productIdList, MyAccessToken myAccessToken) {
        if (CollUtil.isEmpty(productIdList)) {
            throw new CustomizeException(StrUtil.format("库存同步失败，商品ID为空"));
        }
        productIdList = productIdList.stream().distinct().collect(Collectors.toList());
        Map<String, DataItem> skuList = new HashMap<>();
        for (String integer : productIdList) {
            if (Boolean.FALSE.equals(CommonUtil.isNumer(integer))) {
                throw new CustomizeException(StrUtil.format("库存同步失败，商品ID不正确{}", integer));
            }
            SkuListRequest request = new SkuListRequest();
            request.setConfig(myAccessToken.getOpConfig());
            SkuListParam param = request.getParam();
            param.setProductId(Long.valueOf(integer));
            SkuListResponse response = new SkuListResponse();
            try {
                response = request.execute(myAccessToken.getAccessToken());
            } catch (DoudianOpException e) {
                //处理异常
                log.error("抖音sku列表获取失败", e);
            }
            if (!Objects.equals(response.getCode(), DouDianResultEnum.SUCCESS.getCode())) {
                log.error("库存同步失败，调用抖音SKU返回结果，productIdList：{}，传入参数{}，返回结果{}",productIdList,myAccessToken,response);
                throw new CustomizeException(StrUtil.format("库存同步失败，调用抖音SKU返回结果{}", response));
            } else {
                Map<String, DataItem> collect = response.getData().stream().filter(ObjectUtil::isNotEmpty)
                        .filter(a -> ObjectUtil.isNotEmpty(a.getCode()))
                        .collect(Collectors.toMap(DataItem::getCode, Function.identity(), (v1, v2) -> v1));
                skuList.putAll(collect);
            }
        }
        return skuList;
    }

    /**
     * 抖音库存同步 只同步新机库存
     * @param tenantCode tenantCode
     * @return Boolean
     */
    @Override
    public Boolean syncToDouDian(String tenantCode) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        String userName;
        if (Objects.isNull(oaUserBO)){
            userName = "系统定时操作";
        }else {
            userName = oaUserBO.getUserName();
        }
        List<StockSync> stockSyncList = new ArrayList<>();
        String syncNumber = DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN);
        //获取所有门店code
        List<Store> stores = storeService.list(new LambdaQueryWrapper<Store>().eq(Store::getIsEnable, Boolean.TRUE)
                .eq(Store::getPlatCode,ThirdPlatformCommonConst.THIRD_PLAT_DY).eq(Store::getTenantCode,tenantCode));
        List<String> storeCodeList = stores.stream().map(Store::getStoreCode).collect(Collectors.toList());
        //查询所有
        List<ProductConfigVO> productList = productConfigService.selectListWithStockV2(storeCodeList,tenantCode);
        String finalTenantCode1 = tenantCode;
        productList = productList.stream().filter(pr -> Objects.equals(pr.getTenantCode(), finalTenantCode1)).collect(Collectors.toList());
        if (CollUtil.isEmpty(storeCodeList) || CollUtil.isEmpty(productList)) {
            meituanJdWorkLogService.saveByLog("库存同步失败", StrUtil.format("门店信息：{} 库存信息: {}",storeCodeList,productList),
                    userName, LogTypeEnum.STOCK_SYN_LOG.getCode(), PlatfromEnum.DY.getCode(), tenantCode);
            return false;
        }
        //以门店做维度循环
        for (Map.Entry<String, List<ProductConfigVO>> stringListEntry : productList.stream().collect(Collectors.groupingBy(ProductConfigVO::getStoreCode)).entrySet()) {
            //以平台的sku做维度计算库存
            Map<String, List<ProductConfigVO>> skuIdMap = stringListEntry.getValue().stream().collect(Collectors.groupingBy(ProductConfigVO::getSkuId));
            LocalDateTime dateTime = LocalDateTime.now();
            for (Map.Entry<String, List<ProductConfigVO>> entry : skuIdMap.entrySet()) {
                List<ProductConfigVO> subList = entry.getValue();
                //若设置了某个商品优先同步，优先选取该商品的最终同步库存量进行同步
                List<ProductConfigVO> firstList = subList.stream().filter(pro->pro.getSyncFirst()!=null && pro.getSyncFirst() == ThirdPlatformCommonConst.COMMON_INTEGER_TRUE).collect(Collectors.toList());
                int finalCount;
                if (CollUtil.isNotEmpty(firstList)) {
                    if (firstList.size() == 1) {
                        ProductConfigVO first = firstList.get(0);
                        finalCount = syncCountCalc(first.getSyncLimit(), first.getLeftCount(), first.getSyncRatio(), first.getSyncType());
                    } else {
                        //一套映射中，如果指定多个优先配置，在配置内计算后取最小值进行同步
                        finalCount = getFinalSyncCount(firstList);
                    }
                } else {
                    //若没有指明某商品要优先同步，选取该套商品中最终同步库存量最小值进行同步
                    finalCount = getFinalSyncCount(subList);
                }
                ProductConfigVO product = subList.get(0);
                StockSync stockSync = new StockSync();
                stockSync.setSyncNumber(syncNumber);
                stockSync.setPlatCode(product.getPlatCode());
                stockSync.setTenantCode(product.getTenantCode());
                stockSync.setStoreCode(stringListEntry.getKey());
                stockSync.setProductCode(product.getProductCode());
                stockSync.setSkuId(entry.getKey());
                stockSync.setSync(true);
                stockSync.setSyncTime(dateTime);
                stockSync.setSyncCount(finalCount);
                stockSyncList.add(stockSync);
            }
        }
        //执行同步操作
        excuteSync(stockSyncList);
        //添加日志操作
        String finalTenantCode = tenantCode;
        tenantCode = stockSyncList.stream().map(StockSync::getTenantCode).filter(code -> Objects.equals(code, finalTenantCode))
                .findFirst().orElseThrow(() -> new RuntimeException("商户code错误！"));
        MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.STOCK_SYN.getMessage(), "执行库存同步操作",
                userName, LogTypeEnum.STOCK_SYN.getCode(), PlatfromEnum.DY.getCode(),tenantCode);
        //开启事务 切换写库
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> meituanJdWorkLogService.save(structure)).commit();
        return true;
    }

    /**
     * 根据订单id获取订单详情
     * @param orderId 订单ID
     * @param myAccessToken myAccessToken
     * @return 订单详情
     */
    @Override
    public R<ShopOrderDetail> getOrderDetail(String orderId, MyAccessToken myAccessToken) {
        OrderOrderDetailRequest request = new OrderOrderDetailRequest();
        OrderOrderDetailParam param = request.getParam();
        param.setShopOrderId(orderId);
        request.setConfig(myAccessToken.getOpConfig());
        OrderOrderDetailResponse response ;
        try {
            response = SpringUtil.getBean(RetryService.class).retryByMeituanException(() -> request.execute(myAccessToken.getAccessToken()));
        } catch (Exception e) {
            //处理异常
            log.error("抖音订单详情接口调用失败", e);
            return R.error(e.getMessage());
        }
        if(!response.isSuccess()){
            AtomicReference<String> msgRef = new AtomicReference<>("");
            RRExceptionHandler.logError("抖音订单详情获取", Dict.create().set("orderId",orderId)
                    .set("myAccessToken",myAccessToken).set("response",response),new CustomizeException(
                    ObjectUtil.defaultIfNull(response.getMsg(), ObjectUtil.defaultIfNull(response.getMsg(),response.getSubMsg()))),msgRef::set);
            return R.error(msgRef.get());
        }
//        if (ObjectUtil.defaultIfNull(response.getErrNo(),0L)>0) {
//            AtomicReference<String> msgRef = new AtomicReference<>("");
//            RRExceptionHandler.logError("抖音订单详情获取", Dict.create().set("orderId",orderId)
//                    .set("myAccessToken",myAccessToken).set("response",response),new CustomizeException(
//                            ObjectUtil.defaultIfNull(response.getMessage(), ObjectUtil.defaultIfNull(response.getMsg(),response.getSubMsg()))),msgRef::set);
//            return R.error(msgRef.get());
//        }
        return R.success(response.getData().getShopOrderDetail());
    }

    /**
     * 抖音脱敏接口
     * @param cipherInfos 脱敏信息
     * @param myAccessToken myAccessToken
     * @return 脱敏结果
     */
    @Override
    public R<OrderBatchSensitiveData> batchSensitive(MyAccessToken myAccessToken, List<CipherInfosItem> cipherInfos) {
        OrderBatchSensitiveRequest request = new OrderBatchSensitiveRequest();
        OrderBatchSensitiveParam param = request.getParam();
        param.setCipherInfos(cipherInfos);
        request.setConfig(myAccessToken.getOpConfig());
        OrderBatchSensitiveResponse response;
        try {
            response = request.execute(myAccessToken.getAccessToken());
        } catch (DoudianOpException e) {
            //处理异常
            log.error("抖音批量脱敏接口调用失败", e);
            return R.error(e.getMessage());
        }
        if (!Objects.equals(response.getCode(), DouDianResultEnum.SUCCESS.getCode())) {
            log.error("抖音批量脱敏接口调用失败，cipherInfos：{}，传入参数：{}，返回结果：{}",cipherInfos,myAccessToken,response);
            return R.error(response.getMsg());
        }
        return R.success(response.getData());
    }

    /**
     * 获取售后单详情信息
     * @param afterSaleId 售后id
     * @param myAccessToken
     * @return
     */
    @Override
    @Retryable(value = {RetryException.class, IOException.class},maxAttempts = 5,backoff = @Backoff(delay = 500,multiplier = 1.5))
    public R<AfterSaleDetailData> afterSaleDetail(Long afterSaleId, MyAccessToken myAccessToken) {
        AfterSaleDetailRequest request = new AfterSaleDetailRequest();
        AfterSaleDetailParam param = request.getParam();
        param.setAfterSaleId(String.valueOf(afterSaleId));
        request.setConfig(myAccessToken.getOpConfig());
        AfterSaleDetailResponse response;
        try {
            response = request.execute(myAccessToken.getAccessToken());
        } catch (DoudianOpException e) {
            //处理异常
            log.error("抖音获取售后单详情信息接口调用失败", e);
            return R.error(e.getMessage());
        }
        if (!Objects.equals(response.getCode(), DouDianResultEnum.SUCCESS.getCode())) {
            log.error("抖音获取售后单详情信息接口调用失败，afterSaleId：{}，传入参数：{}，返回结果：{}",afterSaleId,myAccessToken,response);
            int errorCode;
            DouDianResultEnum resultEnum = EnumUtil.getEnumByCode(DouDianResultEnum.class, response.getCode());
            switch (resultEnum){
                case INVALID_PARAMETER_BUSINESS:
                    errorCode = ResultCodeEnum.PARAM_ERROR.getCode();
                    break;
                case SERVICE_ERROR:
                case SERVICE_TIMEOUT:
                    throw new RetryException(response.getMsg());
                default:
                    errorCode = ResultCodeEnum.SERVER_ERROR.getCode();
            }
            return R.error(errorCode,response.getMsg());
        }
        return R.success(response.getData());
    }

    /**
     * 调用美团同步库存
     * @param list list
     */
    @DS(DataSourceConstants.SMALLPRO_WRITE)
    public void excuteSync(List<StockSync> list) {
        log.warn("开始同步库存到抖音...");
        if (CollUtil.isEmpty(list)) {
            return;
        }
        for (Map.Entry<String, List<StockSync>> stringListEntry : list.stream().collect(Collectors.groupingBy(StockSync::getStoreCode)).entrySet()) {
            List<List<StockSync>> listList = ListUtils.partition(stringListEntry.getValue(), 180);
            listList.forEach(entry -> {
                try {
                    retailSkuStock(stringListEntry.getKey(), entry);
                }catch (RuntimeException e){
                    meituanJdWorkLogService.saveByLog("库存同步失败", StrUtil.format("异常信息：{}",e),
                            "系统", LogTypeEnum.STOCK_SYN_LOG.getCode(), PlatfromEnum.DY.getCode(), null);
                }
            });
        }
        log.warn("同步库存到抖音结束...");
    }

    /**
     * 调用美团接口同步库存并处理返回结果
     * @param storeCode 门店code
     * @param list 需要同步的数据
     * @return 同步失败的数据
     * @throws Exception
     */
    public void retailSkuStock(String storeCode, Collection<StockSync> list) {
        //抖音小时达获取token需要使用总店id
        Tenant tenant = SpringUtil.getBean(TenantService.class).getTenantBy(PlatfromEnum.DY.name(), String.valueOf(storeCode));
        //构建抖音连接
        MyAccessToken myAccessToken;
        if (Objects.nonNull(tenant) && Objects.equals(AppTypeEnum.MALL_HOURS.getCode(),tenant.getAppType())) {
            myAccessToken = doudianFactory.getMyAccessToken(Convert.toLong(tenant.getTenantCode(), 0L));
        } else {
            myAccessToken = doudianFactory.getMyAccessToken(Long.valueOf(storeCode));
        }
        SkuSyncStockRequest request = new SkuSyncStockRequest();
        request.setConfig(myAccessToken.getOpConfig());
        //取出所有Id
        List<String> productIdList = list.stream().map(StockSync::getProductCode).distinct().collect(Collectors.toList());
        //查询到商品sku列表
        Map<String, DataItem> skuList = defaultDouDianService.getSkuList(productIdList, myAccessToken);
        //构建库存同步对象
        SkuSyncStockRequest skuSyncStockRequest = new SkuSyncStockRequest();
        //同步失败对象
        Map<String, StockSync> failStockSyncsMap = new HashMap<>();
        for (StockSync stockSync : list) {
            DataItem dataItem = skuList.get(stockSync.getSkuId());
            if (ObjectUtil.isNull(dataItem)) {
                continue;
            }
            if (Objects.equals(stockSync.getSkuId(), dataItem.getCode())) {
                SkuSyncStockParam param = skuSyncStockRequest.getParam();
                param.setCode(dataItem.getCode());
                param.setSkuId(dataItem.getId());
                param.setOutSkuId(dataItem.getOutSkuId());
                param.setProductId(dataItem.getProductId());
                param.setSupplierId(dataItem.getSupplierId());
                //true表示增量库存，false表示全量库存，默认为false
                param.setIncremental(false);
                //库存 (可以为0)
                param.setStockNum((long) stockSync.getSyncCount());
                if (Objects.nonNull(tenant) && Objects.equals(AppTypeEnum.MALL_HOURS.getCode(),tenant.getAppType())) {
                    param.setStoreId(Convert.toLong(stockSync.getStoreCode()));
                }
                request.setParam(param);
                SkuSyncStockResponse response = new SkuSyncStockResponse();
                try {
                    response = request.execute(myAccessToken.getAccessToken());
                } catch (DoudianOpException e) {
                    //处理异常
                    log.error("抖音库存同步接口调用失败", e);
                }
                //同步失败 记录同步失败的skuId 和库存
                if (!Objects.equals(response.getCode(), DouDianResultEnum.SUCCESS.getCode())) {
                    failStockSyncsMap.put(response.getMsg(), stockSync);
                }
            }
        }
        meituanJdWorkLogService.saveByLog("同步失败商品", StrUtil.format("同步失败缓存信息：{}",JSON.toJSONString(failStockSyncsMap)),
                "系统", LogTypeEnum.SYNCHRONIZATION_FAILED_CACHE.getCode(), PlatfromEnum.DY.getCode(), null);
    }

    /**
     * 即时零售下发配送任务
     * @param param
     * @param myAccessToken myAccessToken
     * @return
     */
    @Override
    public R<InstantShoppingCreateDeliveryData> createDelivery(MyAccessToken myAccessToken, InstantShoppingCreateDeliveryParam param) {
        InstantShoppingCreateDeliveryRequest request = new InstantShoppingCreateDeliveryRequest();
        request.setConfig(myAccessToken.getOpConfig());
        InstantShoppingCreateDeliveryResponse response;
        try {
            request.setParam(param);
            response = request.execute(myAccessToken.getAccessToken());
            log.warn("抖音即时零售下发配送任务接口调用成功param={},response={}", param, response);
        } catch (DoudianOpException e) {
            //处理异常
            log.error("抖音即时零售下发配送任务接口调用失败", e);
            return R.error(e.getMessage());
        }
        if (!Objects.equals(response.getCode(), DouDianResultEnum.SUCCESS.getCode())) {
            log.error("抖音即时零售下发配送任务接口调用失败，cipherInfos：{}，传入参数：{}，返回结果：{}",param,myAccessToken,response);
            return R.error(response.getMsg() + ":" + response.getSubMsg());
        }
        return R.success(response.getData());
    }

    /**
     * 即时零售自配订单同步配送信息
     * @param param 即时零售自配订单同步配送信息
     * @param myAccessToken myAccessToken
     * @return 脱敏结果
     */
    @Override
    public R<InstantShoppingNotifyDeliveryStatusData> notifyDeliveryStatus(MyAccessToken myAccessToken, InstantShoppingNotifyDeliveryStatusParam param) {
        InstantShoppingNotifyDeliveryStatusRequest request = new InstantShoppingNotifyDeliveryStatusRequest();
        request.setConfig(myAccessToken.getOpConfig());
        InstantShoppingNotifyDeliveryStatusResponse response;
        try {
            request.setParam(param);
            response = request.execute(myAccessToken.getAccessToken());
        } catch (DoudianOpException e) {
            //处理异常
            log.error("抖音即时零售下发配送任务接口调用失败", e);
            return R.error(e.getMessage());
        }
        if (!Objects.equals(response.getCode(), DouDianResultEnum.SUCCESS.getCode())) {
            log.error("抖音即时零售下发配送任务接口调用失败，cipherInfos：{}，传入参数：{}，返回结果：{}",param,myAccessToken,response);
            return R.error(response.getMsg() + ":" + response.getSubMsg());
        }
        return R.success(response.getData());
    }

    /**
     * 即时零售自配订单同步配送信息
     * @param param 即时零售自配订单同步配送信息
     * @param myAccessToken myAccessToken
     * @return 脱敏结果
     */
    @Override
    public R<InstantShoppingReportRiderLocationData> reportRiderLocation(MyAccessToken myAccessToken, InstantShoppingReportRiderLocationParam param) {
        InstantShoppingReportRiderLocationRequest request = new InstantShoppingReportRiderLocationRequest();
        request.setConfig(myAccessToken.getOpConfig());
        InstantShoppingReportRiderLocationResponse response;
        try {
            request.setParam(param);
            response = request.execute(myAccessToken.getAccessToken());
        } catch (DoudianOpException e) {
            //处理异常
            log.error("抖音即时零售下发配送任务接口调用失败", e);
            return R.error(e.getMessage());
        }
        if (Objects.equals(response.getCode(), DouDianResultEnum.BUSINESS_FAILED.getCode())) {
            return R.error(Convert.toInt(response.getCode()),response.getMsg() + ":" + response.getSubMsg());
        }else if (!Objects.equals(response.getCode(), DouDianResultEnum.SUCCESS.getCode())) {
            log.error("抖音即时零售下发配送任务接口调用失败，cipherInfos：{}，传入参数：{}，返回结果：{}",param,myAccessToken,response);
            return R.error(response.getMsg() + ":" + response.getSubMsg());
        }
        return R.success(response.getData());
    }

    /**
     * 批量解密接口
     * @param param
     * @param myAccessToken myAccessToken
     * @return
     */
    @Override
    public R<OrderBatchDecryptData> batchDecrypt(MyAccessToken myAccessToken, OrderBatchDecryptParam param) {
        OrderBatchDecryptRequest request = new OrderBatchDecryptRequest();
        request.setConfig(myAccessToken.getOpConfig());
        OrderBatchDecryptResponse response;
        try {
            request.setParam(param);
            response = request.execute(myAccessToken.getAccessToken());
            log.warn("抖音批量解密接口，cipherInfos：{}，传入参数：{}，返回结果：{}",param,myAccessToken,response);
        } catch (DoudianOpException e) {
            //处理异常
            log.error("抖音批量解密接口调用失败", e);
            return R.error(e.getMessage());
        }
        if (response.getData() != null) {
            return R.success(response.getData());
        } else {
            return R.error(response.getMsg() + ":" + response.getSubMsg());
        }
    }

    /**
     * 批量解密接口
     * @param param
     * @param myAccessToken myAccessToken
     * @return
     */
    @Override
    public Map<String,com.doudian.open.api.order_batchDecrypt.data.DecryptInfosItem> batchDecryptV1(MyAccessToken myAccessToken, OrderBatchDecryptParam param) {
        Map<String,com.doudian.open.api.order_batchDecrypt.data.DecryptInfosItem> decryptMap = new HashMap<>();
        for (int i = 0; i < 3; i++) {
            if (param.getCipherInfos().size() == decryptMap.size()) {
                break;
            }
            R<OrderBatchDecryptData> orderBatchDecryptDataR = defaultDouDianService.batchDecrypt(myAccessToken, param);
            if (orderBatchDecryptDataR.isSuccess()) {
                OrderBatchDecryptData orderBatchDecryptData = orderBatchDecryptDataR.getData();
                decryptMap.putAll(orderBatchDecryptData.getDecryptInfos().stream().filter(v -> Objects.equals(0L, v.getErrNo())).collect(Collectors.toMap(com.doudian.open.api.order_batchDecrypt.data.DecryptInfosItem::getCipherText, Function.identity(), (v1, v2) -> v1)));
            }
            if (param.getCipherInfos().size() > decryptMap.size()) {
                List<com.doudian.open.api.order_batchDecrypt.param.CipherInfosItem> cipherInfosItems = param.getCipherInfos().stream().filter(v -> !decryptMap.containsKey(v.getCipherText())).collect(Collectors.toList());
                if (CollUtil.isEmpty(cipherInfosItems)) {
                    break;
                }
                param.setCipherInfos(cipherInfosItems);
                R<OrderBatchDecryptData> orderBatchDecryptData1 = defaultDouDianService.batchDecrypt(myAccessToken, param);
                if (orderBatchDecryptData1.isSuccess()) {
                    OrderBatchDecryptData orderBatchDecryptData = orderBatchDecryptData1.getData();
                    decryptMap.putAll(orderBatchDecryptData.getDecryptInfos().stream().filter(v -> Objects.equals(0L, v.getErrNo())).collect(Collectors.toMap(com.doudian.open.api.order_batchDecrypt.data.DecryptInfosItem::getCipherText, Function.identity(), (v1, v2) -> v1)));
                }
            }
        }
        return decryptMap;
    }

    /**
     * 订单发货接口
     * @param param 暂时只支持整单出库，即接口调用时入参只能传父订单号。
     * @param myAccessToken myAccessToken
     * @return 脱敏结果
     */
    @Override
    @AddOrderLog(type = AddLogKind.DOU_DIAN_LOGISTICS_ADD)
    public R<OrderLogisticsAddData> logisticsAdd(MyAccessToken myAccessToken, OrderLogisticsAddParam param) {
        OrderLogisticsAddRequest request = new OrderLogisticsAddRequest();
        request.setConfig(myAccessToken.getOpConfig());
        OrderLogisticsAddResponse response;
        try {
            request.setParam(param);
            response = request.execute(myAccessToken.getAccessToken());
        } catch (DoudianOpException e) {
            //处理异常
            log.error("抖音即时零售订单发货接口调用失败", e);
            return R.error(e.getMessage());
        }
        if (!Objects.equals(response.getCode(), DouDianResultEnum.SUCCESS.getCode())) {
            log.error("抖音即时零售订单发货接口调用失败，cipherInfos：{}，传入参数：{}，返回结果：{}",param,myAccessToken,response);
            return R.error(response.getMsg() + ":" + response.getSubMsg());
        }
        return R.success(response.getData());
    }

    /**
     * 更新整单发货物流信息
     * @param param
     * @param myAccessToken myAccessToken
     */
    @Override
    @AddOrderLog(type = AddLogKind.DOU_DIAN_LOGISTICS_EDIT)
    public R<OrderLogisticsEditData> logisticsEdit(MyAccessToken myAccessToken, OrderLogisticsEditParam param) {
        OrderLogisticsEditRequest request = new OrderLogisticsEditRequest();
        request.setConfig(myAccessToken.getOpConfig());
        OrderLogisticsEditResponse response;
        try {
            request.setParam(param);
            response = request.execute(myAccessToken.getAccessToken());
        } catch (DoudianOpException e) {
            //处理异常
            log.error("抖音即时零售订单更新整单发货物流信息调用失败", e);
            return R.error(e.getMessage());
        }
        if (!Objects.equals(response.getCode(), DouDianResultEnum.SUCCESS.getCode())) {
            log.error("抖音即时零售订单更新整单发货物流信息接口调用失败，传入参数：{}，返回结果：{}",param,response);
            return R.error(response.getMsg() + ":" + response.getSubMsg());
        }
        return R.success(response.getData());
    }

    /**
     * 回传商家拣货状态
     * @param param
     * @param myAccessToken myAccessToken
     * @return
     */
    @Override
    public R<InstantShoppingNotifyPickingStatusData> notifyPickingStatus(MyAccessToken myAccessToken, InstantShoppingNotifyPickingStatusParam param) {
        InstantShoppingNotifyPickingStatusRequest request = new InstantShoppingNotifyPickingStatusRequest();
        request.setConfig(myAccessToken.getOpConfig());
        InstantShoppingNotifyPickingStatusResponse response;
        try {
            request.setParam(param);
            response = request.execute(myAccessToken.getAccessToken());
        } catch (DoudianOpException e) {
            //处理异常
            log.error("抖音即时零售订单回传商家拣货状态失败", e);
            return R.error(e.getMessage());
        }
        if (!Objects.equals(response.getCode(), DouDianResultEnum.SUCCESS.getCode())) {
            log.error("抖音即时零售订单回传商家拣货状态失败，cipherInfos：{}，传入参数：{}，返回结果：{}",param,myAccessToken,response);
            return R.error(response.getMsg() + ":" + response.getSubMsg());
        }
        return R.success(response.getData());
    }

    /**
     * 平台运力查询配送信息
     * @param myAccessToken myAccessToken
     * @param param
     * @return
     */
    @Override
    public R<InstantShoppingGetDeliveryListByOrderIdData> getDeliveryListByOrderId(MyAccessToken myAccessToken, InstantShoppingGetDeliveryListByOrderIdParam param) {
        InstantShoppingGetDeliveryListByOrderIdRequest request = new InstantShoppingGetDeliveryListByOrderIdRequest();
        request.setConfig(myAccessToken.getOpConfig());
        InstantShoppingGetDeliveryListByOrderIdResponse response;
        try {
            request.setParam(param);
            response = request.execute(myAccessToken.getAccessToken());
            log.error("平台运力查询配送信息接口，传入参数：{}，返回结果：{}", param, response);
        } catch (DoudianOpException e) {
            //处理异常
            log.error("平台运力查询配送信息接口失败", e);
            return R.error(e.getMessage());
        }
        if (!Objects.equals(response.getCode(), DouDianResultEnum.SUCCESS.getCode())) {
            log.error("平台运力查询配送信息接口失败，cipherInfos：{}，传入参数：{}，返回结果：{}", param, myAccessToken, response);
            return R.error(response.getMsg() + ":" + response.getSubMsg());
        }
        return R.success(response.getData());
    }


    /**
     *
     * @param shoppingDeliveryChangeParam
     * @param subIdList
     * @return
     */
    @AddOrderLog(type = AddLogKind.DOU_DIAN_STATUS_SYNCHRONIZATION)
    @Override
    public R<Boolean> deliverySynchronization(InstantShoppingDeliveryChangeParam shoppingDeliveryChangeParam, List<Long> subIdList) {
        LogisticsStatusStrategy statusStrategy = logisticsStatusContext.getDouDianStatusStrategy(shoppingDeliveryChangeParam.getStatus());
        if (statusStrategy == null) {
            return R.success(Boolean.TRUE);
        }
        //调用查询配送信息
        MyAccessToken myAccessToken = SpringUtil.getBean(DoudianFactory.class).getMyAccessToken(shoppingDeliveryChangeParam.getShopId());
        InstantShoppingGetDeliveryListByOrderIdParam param = new InstantShoppingGetDeliveryListByOrderIdParam();
        param.setShopOrderId(String.valueOf(shoppingDeliveryChangeParam.getShopOrderId()));
        R<InstantShoppingGetDeliveryListByOrderIdData> deliveryListByOrderIdR = getDeliveryListByOrderId(myAccessToken, param);
        if (!deliveryListByOrderIdR.isSuccess()) {
            throw new CustomizeException(StrUtil.format("调用查询抖音小时达配送信息失败，cipherInfos：{}，传入参数：{}，返回结果：{}", myAccessToken, param, deliveryListByOrderIdR));
        }
        statusStrategy.handleDouDianOrderInfo(deliveryListByOrderIdR.getData(), subIdList);
        return R.success(Boolean.TRUE);
    }

    /**
     * 编辑门店
     *
     * @param param param
     * @param myAccessToken myAccessToken
     * @return
     */
    @Override
    public R<ShopEditStoreData> editStore(MyAccessToken myAccessToken, ShopEditStoreParam param) {
        ShopEditStoreRequest request = new ShopEditStoreRequest();
        request.setConfig(myAccessToken.getOpConfig());
        ShopEditStoreResponse response;
        try {
            request.setParam(param);
            response = request.execute(myAccessToken.getAccessToken());
            log.info("同步抖音编辑门店，参数:{},结果：{}", JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(response));
        } catch (DoudianOpException e) {
            //处理异常
            log.error("抖音编辑门店状态失败", e);
            return R.error(e.getMessage());
        }
        if (!Objects.equals(response.getCode(), DouDianResultEnum.SUCCESS.getCode())) {
            log.error("抖音编辑门店状态失败，cipherInfos：{}，传入参数：{}，返回结果：{}", param, myAccessToken, response);
            return R.error(response.getMsg() + ":" + response.getSubMsg());
        }
        return R.success(response.getData());
    }

    /**
     * 暂停营业
     *
     * @param param param
     * @param myAccessToken myAccessToken
     * @return
     */
    @Override
    public R<ShopStoreSuspendData> storeSuspend(MyAccessToken myAccessToken, ShopStoreSuspendParam param) {
        ShopStoreSuspendRequest request = new ShopStoreSuspendRequest();
        request.setConfig(myAccessToken.getOpConfig());
        ShopStoreSuspendResponse response;
        try {
            request.setParam(param);
            response = request.execute(myAccessToken.getAccessToken());
            log.info("同步抖音暂停营业，参数:{},结果：{}", JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(response));
        } catch (DoudianOpException e) {
            //处理异常
            log.error("抖音暂停营业状态失败", e);
            return R.error(e.getMessage());
        }
        if (!Objects.equals(response.getCode(), DouDianResultEnum.SUCCESS.getCode())) {
            log.error("抖音暂停营业状态失败，cipherInfos：{}，传入参数：{}，返回结果：{}", param, myAccessToken, response);
            return R.error(response.getMsg() + ":" + response.getSubMsg());
        }
        return R.success(response.getData());
    }

    /**
     * 恢复营业
     * @param myAccessToken myAccessToken
     * @param param param
     * @return
     */
    @Override
    public R<ShopUnsuspendStoreData> unsuspendStore(MyAccessToken myAccessToken, ShopUnsuspendStoreParam param) {
        ShopUnsuspendStoreRequest request = new ShopUnsuspendStoreRequest();
        request.setConfig(myAccessToken.getOpConfig());
        ShopUnsuspendStoreResponse response;
        try {
            request.setParam(param);
            response = request.execute(myAccessToken.getAccessToken());
            log.info("同步抖音恢复营业，参数:{},结果：{}", JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(response));
        } catch (DoudianOpException e) {
            //处理异常
            log.error("抖音恢复营业状态失败", e);
            return R.error(e.getMessage());
        }
        if (!Objects.equals(response.getCode(), DouDianResultEnum.SUCCESS.getCode())) {
            log.error("抖音恢复营业状态失败，cipherInfos：{}，传入参数：{}，返回结果：{}", param, myAccessToken, response);
            return R.error(response.getMsg() + ":" + response.getSubMsg());
        }
        return R.success(response.getData());
    }
}
