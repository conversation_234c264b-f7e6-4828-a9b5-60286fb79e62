package com.jiuji.oa.oacore.cloud;

import com.jiuji.oa.oacore.cloud.fallback.AfterCloudFallbackFactory2;
import com.jiuji.oa.oacore.oaorder.client.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
/**
 * <AUTHOR>
 */
@FeignClient( value = "pick-web", path = "/pick",url = "${pick-webservice.url:}",fallbackFactory = AfterCloudFallbackFactory2.class)
public interface PickCloud {

    /**
     * 通过订单号获取严选单号
     * @param orderNo
     * @param xtenant
     * @return
     */
    @ApiOperation(value = "通过SaleNo获取ordeId")
    @GetMapping("/api/order/getOrderIdBySaleNo/v1")
    Result<Long> getYxNoOrderNo(@RequestParam(required = false,value = "saleNo") long orderNo,
                                @RequestHeader(value = "xtenant") Integer xtenant);

}
