/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.oacore.thirdplatform.tenant.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jiuji.oa.oacore.common.util.FieldModified;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.ThirdPlatformTenantUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商户配置Entity
 *
 * <AUTHOR>
 * @date 2021-05-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "商户配置")
public class TenantBO{
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Integer id;

    @ApiModelProperty(value = "商户编码")
    @NotBlank(message = "商户编码不能为空")
    @Length(max = 50 ,message = "商户编码过长")
    private String tenantCode;

    @ApiModelProperty(value = "商户名称")
    @NotBlank(message = "商户名称不能为空")
    @Length(max = 50 ,message = "商户名称过长")
    private String tenantName;

    @ApiModelProperty(value = "下单会员")
    @TableField("UserId")
    @Max(value = Integer.MAX_VALUE ,message = "下单会员编号错误")
    private Integer userId;

    @ApiModelProperty(value = "appKey")
    @NotBlank(message = "appKey不能为空")
    @Length(max = 100 ,message = "appKey错误")
    private String appKey;

    @ApiModelProperty(value = "appSecret")
    @NotBlank(message = "appSecret不能为空")
    @Length(max = 100 ,message = "appSecret错误")
    private String appSecret;

    @ApiModelProperty(value = "平台科目")
    private String platKemu;

    @ApiModelProperty(value = "商家科目")
    private String venderKemu;

    @ApiModelProperty(value = "退款科目配置")
    private String refundKemu;

    @ApiModelProperty(value = "启用状态")
    private Boolean isEnable;

    @ApiModelProperty(value = "是否自动接单")
    @TableField("orderSwitch")
    private Boolean orderSwitch;

    @ApiModelProperty(value = "订单状态更新")
    private Boolean orderStatus;


    /**
     * 美团闪购赠品
     */
    @ApiModelProperty(value = "美团闪购赠品配置  ppid多个以逗号分隔")
    private String basketTypeGift;

    /**
     * 平台编码（JD-京东;MT-美团）
     */
    @ApiModelProperty(value = "平台编码")
    @NotBlank(message = "平台编码不能为空")
    private String platCode;

    /**
     * 商品匹配模式
     * 1-商家sku编码
     * 2-京东sku编码
     */
    private Integer commodityModel;

    /**
     * 应用类型
     */
    private Integer appType;

    /**
     * 国补科目配置
     */
    @Length(max = 45 ,message = "国补收银方式配置过长")
    private String governmentSubsidyKemu;

    /**
     * 零售通账号
     */
    @ApiModelProperty(value = "零售通账号")
    private String retailAccount;

    /**
     * 零售通密码
     */
    @ApiModelProperty(value = "零售通密码")
    private String retailPassword;

    /**
     * 零售通Token
     */
    @ApiModelProperty(value = "零售通Token")
    private String retailPcToken;


    /**
     * 零售通Token
     */
    @ApiModelProperty(value = "零售通Token")
    private String retailAppToken;

    /**
     * 零售通ID
     */
    @ApiModelProperty(value = "零售通ID")
    private String retailCompanyId;

    /**
     * 客户等级附加返利
     */
    @ApiModelProperty(value = "客户等级附加返利")
    private BigDecimal customerLevelRebate;



    /**
     * 下单会员id
     */
    private List<ThirdPlatformTenantUser> tenantUserList;

}
