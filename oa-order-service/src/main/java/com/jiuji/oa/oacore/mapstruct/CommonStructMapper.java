package com.jiuji.oa.oacore.mapstruct;

import com.jiuji.cloud.after.vo.refund.CardOriginRefundVo;
import com.jiuji.cloud.after.vo.refund.OtherRefundVo;
import com.jiuji.cloud.after.vo.refund.ThirdOriginRefundVo;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.oacore.oaorder.vo.req.LiangPinOrderReq;
import com.jiuji.oa.oacore.oaorder.vo.req.LiangPinOrderReqV2;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.MyAccessToken;
import com.jiuji.oa.oacore.thirdplatform.dto.MyAccessTokenDTO;
import com.jiuji.oa.oacore.thirdplatform.dto.TenantDTO;
import com.jiuji.oa.oacore.thirdplatform.order.entity.AreaInfo;
import com.jiuji.oa.oacore.thirdplatform.order.entity.Order;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import org.mapstruct.*;
import com.jiuji.oa.oacore.weborder.bo.AfterServiceSubBO;
import com.jiuji.oa.oacore.weborder.bo.AfterServiceSubSmallBO;
import com.jiuji.oa.oacore.weborder.vo.AfterServiceSubVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Mappings;

/**
 * <AUTHOR>
 * @since 2023/7/10 11:06
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface CommonStructMapper {

    /**
     * 本地门店信息转为client的门店信息
     * @param areaInfo
     * @return
     */
    @Mappings({})
    com.jiuji.oa.orginfo.areainfo.vo.AreaInfo areaInfoToClientAreaInfo(AreaInfo areaInfo);

    /**
     * 复制三方原路径退的信息
     * @param source
     * @return target
     */
    @Mappings({})
    ThirdOriginRefundVo copyThirdOriginRefund(ThirdOriginRefundVo source);

    /**
     * 复制刷卡支付信息
     * @param source
     * @return
     */
    @Mappings({})
    CardOriginRefundVo copyCardOriginRefundVo(CardOriginRefundVo source);


    /**
     * 复制其他支付信息
     * @param source
     * @return
     */
    @Mappings({})
    OtherRefundVo copyOtherRefundVo(OtherRefundVo source);



    /**
     * 复制订单日志信息
     * @param source
     * @return target
     */
    @Mappings({})
    SubLogsNewReq copySubLogsNewReq(SubLogsNewReq source);

    /**
     * 复制订单日志信息
     * @param source
     * @return target
     */
    @Mappings({
            // 忽略 subCheck
            @Mapping(target = "subCheck", ignore = true)
    })
    LiangPinOrderReq copyLiangPinOrderReq(LiangPinOrderReqV2 source);

    /**
     * 复制三方订单信息
     * @param source
     * @return target
     */
    @Mappings({})
    Order copyOrder(Order source);

    /**
     * 复制小件单到售后列表实体
     * @param source
     * @return target
     */
    @Mappings({
            @Mapping(target = "addTime", source = "inDate"),
            @Mapping(target = "subId", source = "smallProId"),
            @Mapping(target = "feiyong", source = "feiYong"),
            @Mapping(target = "orderType", constant = "2"),
    })
    AfterServiceSubVO toAfterServiceSubVO(AfterServiceSubSmallBO source);

    /**
     * 复制小件单到售后列表实体
     * @param source
     * @return target
     */
    @Mappings({
            @Mapping(target = "basketId", source = "newMachineBasketId"),
            @Mapping(target = "feiyong", source = "feiyong"),
            @Mapping(target = "orderType", constant = "1"),
    })
    AfterServiceSubVO toAfterServiceSubVO(AfterServiceSubBO source);

    /**
     * 复制抖音token
     * @param source
     * @return
     */
    @Mappings({})
    MyAccessTokenDTO toMyAccessTokenDTO(MyAccessToken source);

    /**
     * 复制抖音token
     * @param source
     * @return
     */
    @InheritConfiguration
    MyAccessToken toMyAccessToken(MyAccessTokenDTO source);

    /**
     * 复制
     * @param source
     * @return
     */
    @Mappings({})
    TenantDTO toTenantDTO(Tenant source);


}
