/*
 * Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
 *
 * All rights reserved.
 */
package com.jiuji.oa.oacore.common.config.redis;

import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;

/**
 * <AUTHOR>
 */
@Configuration
public class RedisConfig {

    @Value("${redis.oa2.clusterNode}")
    private String clusterNode;
    @Value("${redis.oa2.password}")
    private String oa2Password;
    @Autowired
    private RedisProperties redisProperties;

    @Bean
    @Primary
    public RedisConnectionFactory defaultRedisConnectionFactory() {
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(redisProperties.getHost());
        config.setPort(redisProperties.getPort());
        config.setPassword(redisProperties.getPassword());
        config.setDatabase(redisProperties.getDatabase());

        LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
                .commandTimeout(redisProperties.getTimeout())
                .build();

        return new LettuceConnectionFactory(config, clientConfig);
    }

    @Bean
    @Primary
    public RedisTemplate<?, ?> redisTemplate() {
        RedisTemplate<?, ?> template = new RedisTemplate<>();
        template.setConnectionFactory(defaultRedisConnectionFactory());
        template.setKeySerializer(new MyStringSerializer());
        return template;
    }

    @Bean
    public StringRedisTemplate stringRedisTemplate() {
        StringRedisTemplate template = new StringRedisTemplate(defaultRedisConnectionFactory());
        template.setEnableDefaultSerializer(false);
        template.setKeySerializer(new MyStringSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new StringRedisSerializer());
        template.setValueSerializer(new StringRedisSerializer());
        template.afterPropertiesSet();
        return template;
    }


    @Bean("redisClusterConfig4")
    public RedisConfiguration redisClusterConfig4() {
        String[] hostPortCluster = clusterNode.split(",");
        if (hostPortCluster.length > 1) {
            RedisClusterConfiguration redisClusterConfiguration = new RedisClusterConfiguration();
            for (String hostAndPort : hostPortCluster) {
                String[] array = hostAndPort.split(":");
                redisClusterConfiguration.clusterNode(array[0], NumberUtils.toInt(array[1]));
            }
            redisClusterConfiguration.setPassword(oa2Password);
            return redisClusterConfiguration;
        } else {
            String[] array = hostPortCluster[0].split(":");
            RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration();
            redisStandaloneConfiguration.setHostName(array[0]);
            redisStandaloneConfiguration.setPort(NumberUtils.toInt(array[1]));
            redisStandaloneConfiguration.setPassword(oa2Password);
            return redisStandaloneConfiguration;
        }

    }
    @Bean("factory4")
    public LettuceConnectionFactory factory4(GenericObjectPoolConfig redisPool, RedisConfiguration redisClusterConfig4) {
        LettuceClientConfiguration clientConfiguration = LettucePoolingClientConfiguration.builder()
                .poolConfig(redisPool)
                .commandTimeout(Duration.ofSeconds(2*1000))
                .build();
        if (clusterNode.split(",").length > 1) {
            RedisClusterConfiguration redisClusterConfiguration = (RedisClusterConfiguration) redisClusterConfig4;
            LettuceConnectionFactory factory4 = new LettuceConnectionFactory(redisClusterConfiguration, clientConfiguration);
            return factory4;
        } else {
            RedisStandaloneConfiguration redisStandaloneConfiguration = (RedisStandaloneConfiguration) redisClusterConfig4;
            LettuceConnectionFactory factory4 = new LettuceConnectionFactory(redisStandaloneConfiguration, clientConfiguration);
            return factory4;
        }
    }

    @Bean("redisTemplate4")
    public RedisTemplate<?, ?> redisTemplate4(@Qualifier("factory4") RedisConnectionFactory factory4) {
        RedisTemplate<?, ?> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory4);
        redisTemplate.setKeySerializer(new MyStringSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new StringRedisSerializer());
        return redisTemplate;
    }

    @Bean("redisPool")
    public GenericObjectPoolConfig redisPool() {
        GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
        genericObjectPoolConfig.setMaxIdle(16);
        genericObjectPoolConfig.setMinIdle(0);
        genericObjectPoolConfig.setMaxTotal(128);
        genericObjectPoolConfig.setMaxWaitMillis(10*1000);
        return genericObjectPoolConfig;
    }



}
