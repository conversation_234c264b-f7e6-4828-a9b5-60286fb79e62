package com.jiuji.oa.oacore.thirdplatform.doudian.message.service;

import com.doudian.open.api.product_detail.data.ProductDetailData;
import com.doudian.open.api.product_detail.data.SpecPricesItem;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.thirdplatform.productconfig.entity.ProductConfig;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.enums.sms.SmsReceiverClassfyEnum;

import java.util.Collection;
import java.util.Optional;
import java.util.Set;

/**
 * 商品消息处理器
 * <AUTHOR>
 * @since 2023/11/6 15:11
 */
public interface ProductChangeMessageService extends MessageService {
    /**
     * 获取通知的用户信息
     * @return
     */
    Set<Integer> getNoticeCh999Ids();

    Set<Integer> getNoticeCh999Ids(SmsReceiverClassfyEnum classfyEnum);

    /**
     * 保存或更新良品信息
     * @param shopId
     * @param detailData
     * @param mkcId
     * @param specPrice
     * @param ch999Ids
     * @return
     */
    R<Boolean> saveOrUpdateLpConfig(Long shopId, ProductDetailData detailData, Integer mkcId, SpecPricesItem specPrice, Collection<Integer> ch999Ids);

    /**
     * 保存或更新良品配置
     * @param shopId
     * @param mkcId
     * @param productCode
     * @param skuId
     * @param priceSplit
     * @param oaUser
     * @param configOpt
     * @return
     */
    R<Boolean> saveOrUpdateLpConfig(Long shopId, Integer mkcId, String productCode, String skuId, String priceSplit, OaUserBO oaUser, Optional<ProductConfig> configOpt);
}
