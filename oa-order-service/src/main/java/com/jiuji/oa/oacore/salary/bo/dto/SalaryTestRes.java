package com.jiuji.oa.oacore.salary.bo.dto;

public class SalaryTestRes {
    public static String result = "{\n" +
            "    \"salaryConfigLogRes\": [\n" +
            "      {\n" +
            "        \"amount\": 15.00,\n" +
            "        \"businessDataResList\": [\n" +
            "          {\n" +
            "            \"amount\": 10,\n" +
            "            \"cardinalNumber\": 1.00,\n" +
            "            \"orderKind\": \"marketing\",\n" +
            "            \"ppId\": 191889,\n" +
            "            \"returnDate\": \"2023-03-11 19:31:52\",\n" +
            "            \"subId\": 1564380,\n" +
            "            \"tradeDate\": \"2023-03-05 12:48:29\",\n" +
            "            \"wxkcoutputId\": null\n" +
            "          },\n" +
            "          {\n" +
            "            \"amount\": 20,\n" +
            "            \"cardinalNumber\": 1.00,\n" +
            "            \"orderKind\": \"marketing\",\n" +
            "            \"ppId\": 189343,\n" +
            "            \"returnDate\": null,\n" +
            "            \"subId\": 1573993,\n" +
            "            \"tradeDate\": \"2023-03-11 22:08:48\",\n" +
            "            \"wxkcoutputId\": null\n" +
            "          },\n" +
            "          {\n" +
            "            \"amount\": 30,\n" +
            "            \"cardinalNumber\": 1.00,\n" +
            "            \"orderKind\": \"marketing\",\n" +
            "            \"ppId\": 191866,\n" +
            "            \"returnDate\": null,\n" +
            "            \"subId\": 1573189,\n" +
            "            \"tradeDate\": \"2023-03-10 20:59:12\",\n" +
            "            \"wxkcoutputId\": null\n" +
            "          },\n" +
            "          {\n" +
            "            \"amount\": 40,\n" +
            "            \"cardinalNumber\": 1.00,\n" +
            "            \"orderKind\": \"marketing\",\n" +
            "            \"ppId\": 81716,\n" +
            "            \"returnDate\": null,\n" +
            "            \"subId\": 1588266,\n" +
            "            \"tradeDate\": \"2023-03-19 13:22:38\",\n" +
            "            \"wxkcoutputId\": null\n" +
            "          },\n" +
            "          {\n" +
            "            \"amount\": 50,\n" +
            "            \"cardinalNumber\": 1.00,\n" +
            "            \"orderKind\": \"marketing\",\n" +
            "            \"ppId\": 196995,\n" +
            "            \"returnDate\": null,\n" +
            "            \"subId\": 1576754,\n" +
            "            \"tradeDate\": \"2023-03-12 20:44:52\",\n" +
            "            \"wxkcoutputId\": null\n" +
            "          },\n" +
            "          {\n" +
            "            \"amount\": 60,\n" +
            "            \"cardinalNumber\": 1.00,\n" +
            "            \"orderKind\": \"marketing\",\n" +
            "            \"ppId\": 51852,\n" +
            "            \"returnDate\": \"2023-03-12 18:58:19\",\n" +
            "            \"subId\": 1576415,\n" +
            "            \"tradeDate\": \"2023-03-12 13:14:05\",\n" +
            "            \"wxkcoutputId\": null\n" +
            "          },\n" +
            "          {\n" +
            "            \"amount\": 70,\n" +
            "            \"cardinalNumber\": -1.00,\n" +
            "            \"orderKind\": \"marketing\",\n" +
            "            \"ppId\": 206801,\n" +
            "            \"returnDate\": \"2023-03-07 19:35:56\",\n" +
            "            \"subId\": 1552356,\n" +
            "            \"tradeDate\": \"2023-02-27 19:11:03\",\n" +
            "            \"wxkcoutputId\": null\n" +
            "          },\n" +
            "          {\n" +
            "            \"amount\": 80,\n" +
            "            \"cardinalNumber\": -1.00,\n" +
            "            \"orderKind\": \"marketing\",\n" +
            "            \"ppId\": 191889,\n" +
            "            \"returnDate\": \"2023-03-11 19:31:52\",\n" +
            "            \"subId\": 1564380,\n" +
            "            \"tradeDate\": \"2023-03-05 12:57:24\",\n" +
            "            \"wxkcoutputId\": null\n" +
            "          },\n" +
            "          {\n" +
            "            \"amount\": 90,\n" +
            "            \"cardinalNumber\": -1.00,\n" +
            "            \"orderKind\": \"marketing\",\n" +
            "            \"ppId\": 51852,\n" +
            "            \"returnDate\": \"2023-03-12 18:58:19\",\n" +
            "            \"subId\": 1576415,\n" +
            "            \"tradeDate\": \"2023-03-12 13:14:12\",\n" +
            "            \"wxkcoutputId\": null\n" +
            "          }\n" +
            "        ],\n" +
            "        \"cardinalNumber\": 3.00,\n" +
            "        \"effectiveEndTime\": \"2023-03-31\",\n" +
            "        \"effectiveStartTime\": \"2023-03-01\",\n" +
            "        \"salaryCategoryId\": 233,\n" +
            "        \"salaryCategoryName\": \"大件\",\n" +
            "        \"salaryCategorySort\": 1,\n" +
            "        \"salaryChildCategoryId\": 240,\n" +
            "        \"salaryChildCategoryName\": \"良品\",\n" +
            "        \"salaryConfigLogResId\": null,\n" +
            "        \"salaryId\": 126163,\n" +
            "        \"salaryKinds\": \"订单商品数量\",\n" +
            "        \"salaryKindsId\": 2,\n" +
            "        \"salaryMode\": \"固定值\",\n" +
            "        \"salaryModeId\": 1,\n" +
            "        \"salaryName\": \"良品销售网络提成\",\n" +
            "        \"salaryPath\": \"/custom-salary-admin/#/custom-salary/salary-rule/marketing?configId=126163&ruleType=marketing&orderType=9-2\",\n" +
            "        \"salaryType\": 1\n" +
            "      }\n" +
            "    ],\n" +
            "    \"salaryMatchMsgList\": [],\n" +
            "    \"totalMoney\": 2843.58\n" +
            "  }";
}
