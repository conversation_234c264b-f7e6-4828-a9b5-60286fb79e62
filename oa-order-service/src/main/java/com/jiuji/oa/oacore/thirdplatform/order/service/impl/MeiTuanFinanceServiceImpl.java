package com.jiuji.oa.oacore.thirdplatform.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.jiuji.infra.lmstfy.anotation.LmstfyConsume;
import com.jiuji.oa.oacore.apollo.ApolloEntity;
import com.jiuji.oa.oacore.common.config.lmstfy.LmstfyConfig;
import com.jiuji.oa.oacore.common.constant.RedisKeyConstant;
import com.jiuji.oa.oacore.common.enums.OaMesTypeEnum;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.common.util.MiniFileServerUtil;
import com.jiuji.oa.oacore.thirdplatform.common.ThirdPlatformCommonConst;
import com.jiuji.oa.oacore.thirdplatform.mapstruct.OrderMapstruct;
import com.jiuji.oa.oacore.thirdplatform.order.bo.ThirdMeituanBillDetailsExcelBO;
import com.jiuji.oa.oacore.thirdplatform.order.bo.ThirdMeituanShangouBillDetailsExcelBO;
import com.jiuji.oa.oacore.thirdplatform.order.bo.ThirdMeituanTuangouBillDetailsExcelBO;
import com.jiuji.oa.oacore.thirdplatform.order.entity.ThirdMeituanBillDetails;
import com.jiuji.oa.oacore.thirdplatform.order.entity.ThirdMeituanShangouBillDetails;
import com.jiuji.oa.oacore.thirdplatform.order.entity.ThirdMeituanTuangouBillDetails;
import com.jiuji.oa.oacore.thirdplatform.order.service.MeiTuanFinanceService;
import com.jiuji.oa.oacore.thirdplatform.order.service.ThirdMeituanBillDetailsService;
import com.jiuji.oa.oacore.thirdplatform.order.service.ThirdMeituanShangouBillDetailsService;
import com.jiuji.oa.oacore.thirdplatform.order.service.ThirdMeituanTuangouBillDetailsService;
import com.jiuji.oa.oacore.thirdplatform.order.vo.meituan.*;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.TenantService;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.TraceIdUtil;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.meitu.platform.lmstfy.Job;
import com.meitu.platform.lmstfy.client.LmstfyClient;
import com.meitu.platform.lmstfy.exception.LmstfyException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MeiTuanFinanceServiceImpl implements MeiTuanFinanceService {
    public static final String CREATE_BILL_EXPORT_TASK_URL = "https://shangoue.meituan.com/finance/pc/api/mall/v1/billDownload/createBillExportTask?ignoreSetRouterProxy=true&billType=1&beginDate={}&endDate={}&sku=2&wmSettleId=-1&yodaReady=h5&csecplatform=4&csecversion=3.0.0";
    public static final String GET_BILL_DOWNLOAD_LIST_URL = "https://shangoue.meituan.com/finance/pc/api/mall/v1/billDownload/getBillDownloadList?ignoreSetRouterProxy=true&pageSize=10&pageNo=1&sku=2&yodaReady=h5&csecplatform=4&csecversion=3.0.0";
    public static final String EXPORT_DOWNLOAD_LIST_URL = "https://shangoue.meituan.com/finance/v2/finance/orderChecking/export/download/";
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private OrderMapstruct orderMapstruct;
    @Resource
    private ThirdMeituanBillDetailsService thirdMeituanBillDetailsService;
    @Resource
    private ThirdMeituanTuangouBillDetailsService thirdMeituanTuangouBillDetailsService;
    @Resource
    private ThirdMeituanShangouBillDetailsService thirdMeituanShangouBillDetailsService;
    @Resource
    private TenantService tenantService;
    @Resource(name = "firstLmstfyClient")
    private LmstfyClient firstLmstfyClient;
    @Resource
    private ApolloEntity apolloEntity;

    public R getHeadersByCurl(MeituanFinanceCurlReq req) {
        String curlCommand = req.getCurl();
        Pattern pattern = Pattern.compile("(?:-H|--header)\\s+\\$*'([^']*)'");
        Matcher matcher = pattern.matcher(curlCommand);
        Map<String, String> headersMap = new HashMap<>();
        while (matcher.find()) {
            String headStr = matcher.group(1);
            String[] split = headStr.split(":",2);
            if (split.length == 2) {
                headersMap.put(split[0].trim(), split[1].trim());
            }
        }
        String key = StrUtil.format(RedisKeyConstant.MEITUAN_FINANCE_TOKEN, req.getAppKey());
        stringRedisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(headersMap), NumberConstant.TEN, TimeUnit.DAYS);
        return R.success("操作成功");
    }

    public R createBillExportTask(String appKey) {
        //获取请求header
        String key = StrUtil.format(RedisKeyConstant.MEITUAN_FINANCE_TOKEN, appKey);
        String headersStr = stringRedisTemplate.opsForValue().get(key);
        if (StrUtil.isBlank(headersStr)) {
            return R.success("请先配置token");
        }

        //获取上次导出时间
        String lastBillDateKey = StrUtil.format(RedisKeyConstant.MEITUAN_FINANCE_LAST_BILL_DATE, appKey);
        String lastBillDate = stringRedisTemplate.opsForValue().get(lastBillDateKey);
        String beginDate = DateUtil.format(LocalDateTime.now().minusDays(1), "yyyy-MM-dd");
        String endDate = beginDate;
        if (StrUtil.isNotBlank(lastBillDate)) {
            LocalDateTime localDateTime = DateUtil.parseLocalDateTime(lastBillDate, "yyyy-MM-dd").plusDays(1);
            beginDate = DateUtil.format(localDateTime, "yyyy-MM-dd");
            if (LocalDateTime.now().isBefore(localDateTime)
                    || beginDate.equals(DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd"))) {
                return R.success("已经导出过账单了");
            }
        }
        //创建导出任务
        return createBillExportTask(appKey, beginDate, endDate);
    }

    public R createBillExportTask(String appKey, String beginDate, String endDate) {
        //获取请求header
        String key = StrUtil.format(RedisKeyConstant.MEITUAN_FINANCE_TOKEN, appKey);
        String headersStr = stringRedisTemplate.opsForValue().get(key);
        if (StrUtil.isBlank(headersStr)) {
            return R.success("请先配置token");
        }
        Map<String, String> headersMap = JSONUtil.toBean(headersStr, Map.class);
        //创建导出任务
        String url = StrUtil.format(CREATE_BILL_EXPORT_TASK_URL, beginDate, endDate);
        String result = HttpRequest.get(url)
                .headerMap(headersMap, true)
                .execute().body();
        log.warn("美团创建导出任务url={}，result={}", url, result);
        MeiTuanBillRes<CreateBillExportTaskData> meiTuanBillRes = JSONUtil.toBean(result, new TypeReference<MeiTuanBillRes<CreateBillExportTaskData>>() {
        }, true);
        if ("1001".equals(meiTuanBillRes.getCode())) {
            //发送oa消息
            SpringUtil.getBean(SmsService.class).sendOaMsg("美团三方平台对账token失效，请进行token更新","",apolloEntity.getMeituanFinanceTokenCh999ids(), OaMesTypeEnum.SYSTEM);
        } else if (!"0".equals(meiTuanBillRes.getCode()) || Objects.isNull(meiTuanBillRes.getData())) {
            //发送oa消息
            SpringUtil.getBean(SmsService.class).sendOaMsgTo9JiMan("美团创建导出任务错误");
        } else {
            CreateBillExportTaskData meiTuanBillData = meiTuanBillRes.getData();
            String taskNo = meiTuanBillData.getTaskNo();
            sendBillExportTask(appKey, taskNo);
            String handBillDateKey = StrUtil.format(RedisKeyConstant.MEITUAN_FINANCE_LAST_BILL_DATE, appKey) + ":" + taskNo;
            stringRedisTemplate.opsForValue().set(handBillDateKey, endDate, 1, TimeUnit.DAYS);
        }
        return R.success("成功");
    }

    private void sendBillExportTask(String appKey,String taskNo) {
        MeituanBillExportTaskReq vo = new MeituanBillExportTaskReq();
        vo.setTaskNo(taskNo);
        vo.setAppKey(appKey);
        try {
            String publish = firstLmstfyClient.publish(LmstfyConfig.resolve(LmstfyConfig.MEITUAN_FINANCE_BILL_DOWNLOAD),
                    JSONUtil.toJsonStr(vo).getBytes(), 0, (short) 1, 900);
            log.warn("美团创建导出任务推送导出任务成功，队列名称{}，推送参数{}，返回结果{}", LmstfyConfig.MEITUAN_FINANCE_BILL_DOWNLOAD
                    , JSONUtil.toJsonStr(vo), publish);
        } catch (LmstfyException e) {
            log.error("美团创建导出任务推送导出任务异常，队列名称{}，推送参数{}", LmstfyConfig.MEITUAN_FINANCE_BILL_DOWNLOAD, JSONUtil.toJsonStr(vo), e);
        }
    }

    public void getBillDownloadList(String appKey, String taskNo) {
        try {
            String key = StrUtil.format(RedisKeyConstant.MEITUAN_FINANCE_TOKEN, appKey);
            String headersStr = stringRedisTemplate.opsForValue().get(key);
            Map<String, String> headersMap = JSONUtil.toBean(headersStr, Map.class);
            String result = HttpRequest.get(GET_BILL_DOWNLOAD_LIST_URL)
                    .headerMap(headersMap, true)
                    .execute().body();
            MeiTuanBillRes<BillDownloadListData> meiTuanBillRes = JSONUtil.toBean(result, new TypeReference<MeiTuanBillRes<BillDownloadListData>>() {
            }, true);
            if ("1001".equals(meiTuanBillRes.getCode())) {
                //发送oa消息
                SpringUtil.getBean(SmsService.class).sendOaMsg("美团三方平台对账token失效，请进行token更新","",apolloEntity.getMeituanFinanceTokenCh999ids(), OaMesTypeEnum.SYSTEM);
                sendBillExportTask(appKey,taskNo);
            }
            if ("0".equals(meiTuanBillRes.getCode()) || Objects.nonNull(meiTuanBillRes.getData())) {
                BillDownloadListData billDownloadListData = meiTuanBillRes.getData();
                BillDownloadListItem billDownloadListItem = billDownloadListData.getTaskList().stream().filter(v -> taskNo.equals(v.getTaskNo())).findFirst().orElse(null);
                if (Objects.nonNull(billDownloadListItem) && StrUtil.isNotBlank(billDownloadListItem.getTaskUrl())) {
                    File billDownloadFlie = getBillDownloadFlie(appKey, billDownloadListItem.getTaskUrl());
                    if (Objects.nonNull(billDownloadFlie)) {
                        List<ThirdMeituanBillDetailsExcelBO> dataList = EasyExcel.read(billDownloadFlie).head(ThirdMeituanBillDetailsExcelBO.class).sheet("订单明细").doReadSync();
                        if (CollUtil.isNotEmpty(dataList)) {
                            List<ThirdMeituanBillDetails> thirdMeituanBillDetailsList = dataList.stream().map(orderMapstruct::toThirdMeituanBillDetails).collect(Collectors.toList());
                            thirdMeituanBillDetailsService.saveThirdMeituanBillDetailsList(thirdMeituanBillDetailsList);
                            String lastBillDateKey = StrUtil.format(RedisKeyConstant.MEITUAN_FINANCE_LAST_BILL_DATE, appKey);
                            String handleBillDate = Optional.ofNullable(stringRedisTemplate.opsForValue().get(lastBillDateKey + ":" + taskNo)).orElse("");
                            stringRedisTemplate.opsForValue().set(lastBillDateKey, handleBillDate, NumberConstant.THIRTY_ONE, TimeUnit.DAYS);
                        }
                        FileUtil.del(billDownloadFlie);
                    } else {
                        SpringUtil.getBean(SmsService.class).sendOaMsgTo9JiMan(StrUtil.format("美团下载文件错误,taskUrl={}", billDownloadListItem.getTaskUrl()));
                    }
                } else {
                    sendBillExportTask(appKey,taskNo);
                }
            } else {
                //发送oa消息
                SpringUtil.getBean(SmsService.class).sendOaMsgTo9JiMan("美团获取导出任务列表错误");
                sendBillExportTask( appKey,taskNo);
            }
        } catch (Exception e) {
            RRExceptionHandler.logError("处理导出下载美团账单数据异常", taskNo, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
    }

    public File getBillDownloadFlie(String appKey, String taskUrl) {
        String key = StrUtil.format(RedisKeyConstant.MEITUAN_FINANCE_TOKEN, appKey);
        String headersStr = stringRedisTemplate.opsForValue().get(key);
        Map<String, String> headersMap = JSONUtil.toBean(headersStr, Map.class);
        String rootPath = System.getProperty("java.io.tmpdir");
        File file = FileUtil.file(rootPath, "excel", taskUrl);
        final HttpResponse fileResponse = HttpRequest.get(EXPORT_DOWNLOAD_LIST_URL + taskUrl)
                .headerMap(headersMap, true)
                .setFollowRedirects(true)
                .timeout(100000).executeAsync();
        if (fileResponse.isOk()) {
            final File outFile = fileResponse.completeFileNameFromHeader(file);
            fileResponse.writeBody(outFile, null);
            return outFile;
        }
        return null;
    }

    /**
     * 处理美团账单
     *
     * @return
     */
    @Override
    public R<String> handleMeiTuanFinanceBill() {
        //获取美团配置
        List<Tenant> tenantList = tenantService.lambdaQuery().eq(Tenant::getPlatCode, ThirdPlatformCommonConst.THIRD_PLAT_MT).list();
        if (CollUtil.isNotEmpty(tenantList)) {
            for (Tenant tenant : tenantList) {
                createBillExportTask(tenant.getAppKey());
            }
        }
        return R.success("成功");
    }

    /**
     * 导入美团账单文件
     *
     * @param billFile
     * @return
     */
    @Override
    @SneakyThrows
    public Map<String, Object> importBillFile(MultipartFile billFile) {
        if (Objects.nonNull(billFile)) {
            List<ThirdMeituanBillDetailsExcelBO> dataList = EasyExcel.read(billFile.getInputStream()).head(ThirdMeituanBillDetailsExcelBO.class).sheet("订单明细").doReadSync();
            if (CollUtil.isNotEmpty(dataList)) {
                List<ThirdMeituanBillDetails> thirdMeituanBillDetailsList = dataList.stream().map(orderMapstruct::toThirdMeituanBillDetails).collect(Collectors.toList());
                thirdMeituanBillDetailsService.saveThirdMeituanBillDetailsList(thirdMeituanBillDetailsList);
            }
        } else {
            throw new CustomizeException("美团账单文件为空");
        }

        return Collections.emptyMap();
    }

    @Override
    public void downloadBillFile(MeituanDownloadBillFileReq req) {
        File billDownloadFlie = downloadFlie(req.getDownloadFileUrl(), DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN) + "美团账单.xlsx");
        if (Objects.nonNull(billDownloadFlie)) {
            if (Objects.equals(1,req.getBillFileType())) {
                List<ThirdMeituanShangouBillDetailsExcelBO> dataList = EasyExcel.read(billDownloadFlie).head(ThirdMeituanShangouBillDetailsExcelBO.class).headRowNumber(2).sheet("订单计费明细").doReadSync();
                if (CollUtil.isNotEmpty(dataList)) {
                    List<ThirdMeituanShangouBillDetails> thirdMeituanBillDetailsList = dataList.stream().map(v -> {
                        ThirdMeituanShangouBillDetails billDetails = orderMapstruct.toThirdMeituanShangouBillDetails(v);
                        billDetails.setTenantCode(req.getTenantCode());
                        billDetails.setSyncTime(req.getSyncTime());
                        return billDetails;
                    }).collect(Collectors.toList());
                    thirdMeituanShangouBillDetailsService.saveThirdMeituanBillDetailsList(thirdMeituanBillDetailsList);
                }
            } else if (Objects.equals(2,req.getBillFileType())) {
                List<ThirdMeituanTuangouBillDetailsExcelBO> dataList = EasyExcel.read(billDownloadFlie).head(ThirdMeituanTuangouBillDetailsExcelBO.class).headRowNumber(2).sheet("收益明细表").doReadSync();
                if (CollUtil.isNotEmpty(dataList)) {
                    List<ThirdMeituanTuangouBillDetails> billDataList = dataList.stream().map(v -> {
                        ThirdMeituanTuangouBillDetails tuangouBillDetails = orderMapstruct.toThirdMeituanTuangouBillDetails(v);
                        tuangouBillDetails.setTenantCode(req.getTenantCode());
                        tuangouBillDetails.setSyncTime(req.getSyncTime());
                        return tuangouBillDetails;
                    }).collect(Collectors.toList());
                    thirdMeituanTuangouBillDetailsService.saveBillDetailsList(billDataList);
                }
            }
            FileUtil.del(billDownloadFlie);
        } else {
            SpringUtil.getBean(SmsService.class).sendOaMsgTo9JiMan(StrUtil.format("美团下载文件错误,taskUrl={}", req.getDownloadFileUrl()));
        }
    }

    private File downloadFlie(String downloadFlieUrl,String fileName) {
        String rootPath = System.getProperty("java.io.tmpdir");
        File file = FileUtil.file(rootPath, "excel", fileName);

        if (!StrUtil.startWith(downloadFlieUrl, "http")){
            // 上传的文件
            return FileUtil.file(rootPath, "excel", downloadFlieUrl);
        }

        final HttpResponse fileResponse = HttpRequest.get(downloadFlieUrl)
                .setFollowRedirects(true)
                .timeout(100000).executeAsync();
        if (fileResponse.isOk()) {
            final File outFile = fileResponse.completeFileNameFromHeader(file);
            fileResponse.writeBody(outFile, null);
            return outFile;
        }
        return null;
    }

    /**
     * 美团账单导出下载
     * @param job
     */
    @LmstfyConsume(queues = LmstfyConfig.MEITUAN_FINANCE_BILL_DOWNLOAD, clientBeanName = "firstLmstfyClient")
    public void meituanFinanceBillDownload(Job job) {
        if(job == null){
            return;
        }
        log.warn("美团账单导出下载队列消费到数据：{}", job.getData());
        MDC.put(TraceIdUtil.TRACE_ID_KEY, TraceIdUtil.getTraceId());
        String data = job.getData();
        Optional<MeituanBillExportTaskReq> reqOpt = Optional.empty();
        try {
            MeituanBillExportTaskReq req = JSON.parseObject(data, MeituanBillExportTaskReq.class);
            reqOpt = Optional.ofNullable(req);
            if (req != null) {
                getBillDownloadList(req.getAppKey(), req.getTaskNo());
            }
        }catch (Exception e){
            RRExceptionHandler.logError(StrUtil.format("美团账单导出下载[{}]", reqOpt.map(MeituanBillExportTaskReq::getTaskNo)
                    .orElse(null)), data, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }finally {
            MDC.remove(TraceIdUtil.TRACE_ID_KEY);
        }
    }

    @Override
    @SneakyThrows
    public R saveUploadedMeituanFinanceFile(MultipartFile file) {
        if (file.isEmpty()) {
            return R.error("文件不能为空");
        }
        String rootPath = System.getProperty("java.io.tmpdir");
        File diskFile = FileUtil.file(rootPath, file.getOriginalFilename());
        file.transferTo(diskFile);
       return  R.success(MiniFileServerUtil.uploadFile(diskFile, 1));
    }

}
