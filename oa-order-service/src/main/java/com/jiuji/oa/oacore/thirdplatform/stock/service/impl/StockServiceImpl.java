package com.jiuji.oa.oacore.thirdplatform.stock.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.Tuple;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.constant.RedisKeyConstant;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.oaorder.service.RetryService;
import com.jiuji.oa.oacore.thirdplatform.common.ThirdPlatformCommonConst;
import com.jiuji.oa.oacore.thirdplatform.common.enums.LogTypeEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.PlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.common.util.JsonUtils;
import com.jiuji.oa.oacore.thirdplatform.oplog.po.MeituanJdWorkLog;
import com.jiuji.oa.oacore.thirdplatform.oplog.service.MeituanJdWorkLogService;
import com.jiuji.oa.oacore.thirdplatform.productconfig.service.ProductConfigService;
import com.jiuji.oa.oacore.thirdplatform.productconfig.vo.ProductConfigVO;
import com.jiuji.oa.oacore.thirdplatform.stock.bo.ChangeStock;
import com.jiuji.oa.oacore.thirdplatform.stock.bo.StockReq;
import com.jiuji.oa.oacore.thirdplatform.stock.bo.StockRes;
import com.jiuji.oa.oacore.thirdplatform.stock.bo.SyncToMeiTuanExtend;
import com.jiuji.oa.oacore.thirdplatform.stock.entity.StockSync;
import com.jiuji.oa.oacore.thirdplatform.stock.entity.SyncMeituanStoreStock;
import com.jiuji.oa.oacore.thirdplatform.stock.enums.IncrementTypeEnum;
import com.jiuji.oa.oacore.thirdplatform.stock.mapper.StockSyncMapper;
import com.jiuji.oa.oacore.thirdplatform.stock.service.StockService;
import com.jiuji.oa.oacore.thirdplatform.stock.service.sync.ThirdPlatStockSyncService;
import com.jiuji.oa.oacore.thirdplatform.store.entity.Store;
import com.jiuji.oa.oacore.thirdplatform.store.service.StoreService;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.TenantService;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.TimeFormatConstant;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import com.sankuai.meituan.shangou.open.sdk.domain.SystemParam;
import com.sankuai.meituan.shangou.open.sdk.exception.SgOpenException;
import com.sankuai.meituan.shangou.open.sdk.request.RetailSkuStockRequest;
import com.sankuai.meituan.shangou.open.sdk.response.SgOpenResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 库存管理接口实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class StockServiceImpl extends ServiceImpl<StockSyncMapper, StockSync> implements StockService {

    @Value("${meituan.shangou.appId:6322}")
    private String meituanAppId;

    @Value("${meituan.shangou.appSecret:8d5697553c53e7d1f26dff66dce21fec}")
    private String meituanAppSecret;

    @Autowired
    private StoreService storeService;

    @Resource
    private RedisTemplate redisTemplate;

    private RedissonClient redissonClient;

    @Autowired
    private ProductConfigService productConfigService;
    @Resource
    private TenantService tenantService;
    @Resource
    private AreaInfoClient areaInfoClient;

    private static final Integer NO_AREAID=-1;
    @Resource
    private MeituanJdWorkLogService meituanJdWorkLogService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;


    @Override
    public Boolean syncToMeiTuan() {
        List<String> storeCodeList;
        //判断是否是九机 获取所有门店code
        if (XtenantEnum.isJiujiXtenant(XtenantEnum.getXtenant())){
            storeCodeList = storeService.getSyncStoreCodeByJiuJiList(ThirdPlatformCommonConst.THIRD_PLAT_MT);
        }else {
            //查询需要同步的门店
            storeCodeList  = storeService.getSyncStoreCodeList();
        }
        if (CollectionUtils.isEmpty(storeCodeList)) {
            return false;
        }
        String syncNumber = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        List<StockSync> stockSyncList = new ArrayList<>();
        //以门店做维度 同步每家门店的
        for (String storeCode : storeCodeList) {
            List<ProductConfigVO> productList;
            //如果是九机 查询对应门店管理的门店code
            if (XtenantEnum.isJiujiXtenant(XtenantEnum.getXtenant())){
                productList = productConfigService.selectListWithStockByJiuJi(storeCode);
            }else {
                productList = productConfigService.selectListWithStock(storeCode);
            }
            if (CollectionUtils.isEmpty(productList)) {
                continue;
            }
            //计算库存
            Map<String, List<ProductConfigVO>> skuidMap = productList.stream().collect(Collectors.groupingBy(ProductConfigVO::getSkuId));
            for (Map.Entry<String, List<ProductConfigVO>> entry : skuidMap.entrySet()) {
                List<ProductConfigVO> subList = entry.getValue();
                //若设置了某个商品优先同步，优先选取该商品的最终同步库存量进行同步
                List<ProductConfigVO> firstList = subList.stream().filter(pro->pro.getSyncFirst()!=null && pro.getSyncFirst() == ThirdPlatformCommonConst.COMMON_INTEGER_TRUE).collect(Collectors.toList());
                int finalCount = 0;
                if (CollectionUtils.isNotEmpty(firstList)) {
                    if (firstList.size() == 1) {
                        ProductConfigVO first = firstList.get(0);
                        finalCount = syncCountCalc(first.getSyncLimit(), first.getLeftCount(), first.getSyncRatio(), first.getSyncType());
                    } else {
                        //一套映射中，如果指定多个优先配置，在配置内计算后取最小值进行同步
                        finalCount = getFinalSyncCount(firstList);
                    }
                } else {
                    //若没有指明某商品要优先同步，选取该套商品中最终同步库存量最小值进行同步
                    finalCount = getFinalSyncCount(subList);
                }
                ProductConfigVO product = subList.get(0);
                StockSync stockSync = new StockSync();
                stockSync.setSyncNumber(syncNumber);
                stockSync.setPlatCode(product.getPlatCode());
                stockSync.setTenantCode(product.getTenantCode());
                stockSync.setStoreCode(storeCode);
                stockSync.setProductCode(product.getProductCode());
                stockSync.setSkuId(entry.getKey());
                stockSync.setSync(false);
                stockSync.setSyncCount(finalCount);
                stockSyncList.add(stockSync);
            }
        }
        //存储到同步表
        if (CollectionUtils.isNotEmpty(stockSyncList)) {
            List<List<StockSync>> listList = ListUtils.partition(stockSyncList, 100);
            for (List<StockSync> subList : listList) {
                baseMapper.saveBatch(subList);
            }
        }
        //后期需改成MQ消费
        //TODO
        //同步到美团
        for (String storeCode : storeCodeList) {
            excuteSync(ThirdPlatformCommonConst.THIRD_PLAT_MT, storeCode, syncNumber);
        }
        //添加日志操作
        String tenantCode;
        if (XtenantEnum.isJiujiXtenant(XtenantEnum.getXtenant())){
            tenantCode = stockSyncList.stream().map(StockSync::getTenantCode).findFirst().orElse("6900");
        }else {
            tenantCode = stockSyncList.stream().map(StockSync::getTenantCode).findFirst().orElse(meituanAppId);
        }
        MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.STOCK_SYN.getMessage(), "执行库存同步操作",
                abstractCurrentRequestComponent.getCurrentStaffId().getUserName(), LogTypeEnum.STOCK_SYN.getCode(), PlatfromEnum.MT.getCode(),tenantCode);
        //开启事务 切换写库
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> meituanJdWorkLogService.save(structure)).commit();
        return true;
    }

    /**
     * 库存同步接口优化
     * @return
     * @param param
     */
    @Override
    public Boolean syncToMeiTuanV1(Dict param) {
        Optional<Dict> paramOpt = Optional.ofNullable(param);
        List<Integer> ppids = paramOpt.map(dic -> Convert.toList(Integer.class, dic.get("ppids"))).orElse(Collections.emptyList());
        List<String> storeCodeList = paramOpt.map(dic -> Convert.toList(String.class, dic.get("storeCodeList"))).orElse(Collections.emptyList());
        Map<String,SyncMeituanStoreStock> syncMeituanStoreStockMap = paramOpt.map(dic ->
                Convert.toMap(String.class, SyncMeituanStoreStock.class, dic.get("syncMeituanStoreStockMap")))
                .orElse(Collections.emptyMap());
        String platCodeParam = paramOpt.map(dic -> dic.getStr("platCode")).orElse(ThirdPlatformCommonConst.THIRD_PLAT_MT);
        String tenantCodeParam = paramOpt.map(dic -> dic.getStr("tenantCode")).orElse(null);
        Integer incrementType = paramOpt.map(item -> item.getInt("incrementType")).orElse(IncrementTypeEnum.ZERO.getCode());

        if(StrUtil.isNotBlank(tenantCodeParam)){
            storeCodeList.addAll(storeService.lambdaQuery().eq(Store::getTenantCode, tenantCodeParam).eq(Store::getIsEnable, Boolean.TRUE)
                    .select(Store::getTenantCode, Store::getStoreCode).list().stream().map(Store::getStoreCode)
                    .collect(Collectors.toList()));
        }

        if (CollUtil.isNotEmpty(syncMeituanStoreStockMap)){
            // 按门店维度来同步, 优先级最高
            Set<Map.Entry<String, SyncMeituanStoreStock>> smssm = syncMeituanStoreStockMap.entrySet();
            ppids = smssm.stream().map(entry -> entry.getValue()).flatMap(smss -> smss.getPpids().stream())
                    .distinct().collect(Collectors.toList());
            storeCodeList = smssm.stream().map(entry -> entry.getKey()).collect(Collectors.toList());
        }
        Collection<ProductConfigVO> productList;
        Collection<StockSync> allStockSyncList = new ConcurrentLinkedQueue<>();
        String syncNumber = DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN);
        List<Integer> areaIdList = paramOpt.map(dic -> Convert.toList(Integer.class, dic.get("areaIdList"))).orElse(Collections.emptyList());
        if (CollectionUtils.isNotEmpty(areaIdList)) {
            List<Store> store = storeService.getStoreByAreaIdList(platCodeParam,areaIdList);
            if (CollectionUtils.isNotEmpty(store)) {
                storeCodeList = Stream.concat(storeCodeList.stream(),store.stream().map(Store::getStoreCode)).collect(Collectors.toList());
            }
        }
        //判断是否是九机 获取所有门店code
        storeCodeList = CollUtil.isEmpty(storeCodeList) ? storeService.getSyncStoreCodeByJiuJiList(platCodeParam) : storeCodeList;
        productList = productConfigService.selectListWithStockByJiuJiV1(platCodeParam, storeCodeList, ppids, syncMeituanStoreStockMap);

        if (CollectionUtils.isEmpty(storeCodeList) || CollectionUtils.isEmpty(productList)) {
            return false;
        }
        List<Integer> allPpids = productList.stream().map(ProductConfigVO::getPpriceid).distinct().collect(Collectors.toList());
        List<Integer> rarePpriceIdList = CommonUtils.bigDataInQuery(allPpids, ids -> this.baseMapper.getRarePpriceIdList(ids));
        Map<String, Map<String,Object>> syncCountMap = new HashMap<>();
        //以门店做维度循环
        Set<Map.Entry<String, List<ProductConfigVO>>> groupByStoreCodeEntrySet = productList.parallelStream().collect(Collectors.groupingBy(ProductConfigVO::getStoreCode)).entrySet();
        // 是否为全量同步
        boolean isAllSync = IncrementTypeEnum.All.getCode().equals(incrementType);
        // 获取或初始化缓存
        getOrInitSyncCache(platCodeParam, syncCountMap, groupByStoreCodeEntrySet, isAllSync);
        groupByStoreCodeEntrySet.parallelStream()
                .forEach(stringListEntry -> {
            //以平台的sku做维度计算库存
            Map<String, List<ProductConfigVO>> skuIdMap = stringListEntry.getValue().stream().collect(Collectors.groupingBy(ProductConfigVO::getSkuId));
            LocalDateTime dateTime = LocalDateTime.now();
            for (Map.Entry<String, List<ProductConfigVO>> entry : skuIdMap.entrySet()) {
                List<ProductConfigVO> subList = entry.getValue();
                //若设置了某个商品优先同步，优先选取该商品的最终同步库存量进行同步
                List<ProductConfigVO> firstList = subList.stream().filter(pro->pro.getSyncFirst()!=null && pro.getSyncFirst() == ThirdPlatformCommonConst.COMMON_INTEGER_TRUE).collect(Collectors.toList());
                int finalCount = 0;
                if (CollectionUtils.isNotEmpty(firstList)) {
                    if (firstList.size() == 1) {
                        ProductConfigVO first = firstList.get(0);
                        log.debug("商品[{}](良品mkcId否则为ppid)",Optional.ofNullable(first.getMkcId()).orElse(first.getPpriceid()));
                        finalCount = syncCountCalc(first.getSyncLimit(), first.getLeftCount(), first.getSyncRatio(), first.getSyncType());
                    } else {
                        //一套映射中，如果指定多个优先配置，在配置内计算后取最小值进行同步
                        finalCount = getFinalSyncCount(firstList);
                    }
                } else {
                    //若没有指明某商品要优先同步，选取该套商品中最终同步库存量最小值进行同步
                    finalCount = getFinalSyncCount(subList);
                }
                ProductConfigVO product = subList.get(0);
                if (rarePpriceIdList.contains(product.getPpriceid())) {
                    finalCount = 0;
                }
                StockSync stockSync = new StockSync();
                stockSync.setSyncNumber(syncNumber);
                stockSync.setPlatCode(product.getPlatCode());
                stockSync.setTenantCode(product.getTenantCode());
                stockSync.setStoreCode(stringListEntry.getKey());
                stockSync.setProductCode(product.getProductCode());
                stockSync.setSkuId(entry.getKey());
                stockSync.setSync(true);
                stockSync.setSyncTime(dateTime);
                stockSync.setSyncCount(finalCount);
                String storeCode = stockSync.getStoreCode();
                //ppid 超过5个才考虑缓存
                if(allPpids.size() * groupByStoreCodeEntrySet.size() > 1500
                        && ObjectUtil.equal(syncCountMap.get(storeCode).get(stockSync.getSkuId()), finalCount)){
                    // 与之前同步库存相同,不需要同步
                    // 全量需要, 重新放入到缓存中
                    if(isAllSync){
                        String syncCountMapKey = StockService.getSyncCountMapKey(platCodeParam, storeCode);
                        redisTemplate.opsForHash().put(syncCountMapKey, stockSync.getSkuId(), stockSync.getSyncCount());
                    }
                    continue;
                }
                allStockSyncList.add(stockSync);
            }
        });

        log.warn("需要同步到{}数据总量: {}", platCodeParam, allStockSyncList.size());
        Map<Tuple, List<StockSync>> tenantCodeMap = allStockSyncList.stream().collect(Collectors.groupingBy(ss -> new Tuple(ss.getPlatCode(),ss.getTenantCode())));
        for (Map.Entry<Tuple, List<StockSync>> entry : tenantCodeMap.entrySet()) {
            SyncToMeiTuanExtend syncToMeiTuanExtend = new SyncToMeiTuanExtend();
            List<StockSync> stockSyncList = entry.getValue();
            String platCode = entry.getKey().get(0);
            String tenantCode = entry.getKey().get(1);
            Tenant tenant = tenantService.getOneTenantBy(tenantCode, platCode);
            if(tenant == null || StrUtil.isBlank(tenant.getAppKey()) || StrUtil.isBlank(tenant.getAppSecret())){
                log.warn("平台: {}, 商户: {} 用户信息为空,不进行库存同步", platCode, tenantCode);
                continue;
            }
            syncToMeiTuanExtend.setTenant(tenant);
            syncToMeiTuanExtend.setIncrementType(incrementType);
            syncToMeiTuanExtend.setSyncCountMap(syncCountMap);
            PlatfromEnum platEnum = PlatfromEnum.valueOf(platCode);
            //同步到美团
            if(platCode.equals(ThirdPlatformCommonConst.THIRD_PLAT_MT)){
                excuteSyncV1(platCode, stockSyncList, syncToMeiTuanExtend);
            }else{
                SpringUtil.getBeansOfType(ThirdPlatStockSyncService.class).entrySet().stream()
                        .filter(syncEntry -> syncEntry.getValue().support(platEnum))
                        .forEach(syncEntry -> syncEntry.getValue().excuteSync(platCode, stockSyncList, syncToMeiTuanExtend));
            }

            OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
            String userName;
            if (Objects.isNull(oaUserBO)) {
                userName = "系统定时操作";
            } else {
                userName = oaUserBO.getUserName();
            }
            MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.STOCK_SYN.getMessage(), "执行库存同步操作",
                    userName, LogTypeEnum.STOCK_SYN.getCode(), platEnum.getCode(), tenantCode);
            //开启事务 切换写库
            MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> meituanJdWorkLogService.save(structure)).commit();
        }
        return true;
    }

    private void getOrInitSyncCache(String platCode, Map<String, Map<String, Object>> syncCountMap, Set<Map.Entry<String, List<ProductConfigVO>>> storeCodeEntrySet, boolean isAllSync) {
        // 缓存处理
        for (Map.Entry<String, List<ProductConfigVO>> gscEntry : storeCodeEntrySet) {
            String storeCode = gscEntry.getKey();
            String syncCountMapKey = StockService.getSyncCountMapKey(platCode, storeCode);
            Boolean hasKey = redisTemplate.hasKey(syncCountMapKey);
            if(hasKey){
                // 有缓存, 放到map中
                if(isAllSync){
                    syncCountMap.put(storeCode, redisTemplate.opsForHash().entries(syncCountMapKey));
                }else{
                    Map<String, Object> map = CollUtil.newHashMap();
                    Set<String> ppidSet = storeCodeEntrySet.stream()
                            .flatMap(entry -> entry.getValue().stream()).map(ProductConfigVO::getSkuId).collect(Collectors.toSet());
                    ppidSet.forEach(ppid -> map.put(ppid, redisTemplate.opsForHash().get(syncCountMapKey, ppid)));
                    syncCountMap.put(storeCode, map);
                }

            }else{
                // 没有缓存放个默认值
                syncCountMap.put(storeCode, Collections.emptyMap());
            }
            if(isAllSync){
                //清理之前的缓存
                redisTemplate.delete(syncCountMapKey);
                hasKey = false;
            }
            if(!hasKey){
                // 没有缓存, 或者全量都需要重新设置缓存
                redisTemplate.opsForHash().put(syncCountMapKey, "initialMap", 0);
                // 随机时间,避免同时失效
                redisTemplate.expireAt(syncCountMapKey, DateUtil.endOfDay(new Date()).offset(DateField.MINUTE, RandomUtil.randomInt(120, 420)) .toJdkDate());
            }
        }
    }


    /**
     * 根据输入的List获取库存最小值
     *
     * @param subList
     * @return
     */
    public static int getFinalSyncCount(List<ProductConfigVO> subList) {
        List<Integer> countList = new ArrayList<>();
        for (ProductConfigVO vo : subList) {
            int leftCount = syncCountCalc(vo.getSyncLimit() ,vo.getLeftCount(), vo.getSyncRatio(), vo.getSyncType());
            countList.add(leftCount);
        }
        return Collections.min(countList);
    }

    /**
     * 根据规则计算库存数量
     *
     * @param count
     * @param ratio
     * @param type
     * @return
     */
    public static int syncCountCalc(Integer syncLimit,int count, Double ratio, int type) {
        int leftCount = 0;
        Double tmp = Double.valueOf(count);
        switch (type) {
            case ThirdPlatformCommonConst.SYNC_TYPE_ADD:
                //相加
                leftCount = (int) Math.floor(tmp + ratio);
                break;
            case ThirdPlatformCommonConst.SYNC_TYPE_SUBTRACT:
                //相减
                leftCount = (int) Math.floor(tmp - ratio);
                break;
            case ThirdPlatformCommonConst.SYNC_TYPE_MULTI:
                //相乘
                leftCount = (int) Math.floor(tmp * ratio);
                break;
            case ThirdPlatformCommonConst.SYNC_TYPE_FIX:
                //固定值
                leftCount = (int) Math.floor(ratio);
                break;
            default:
                leftCount = count;
                break;
        }
        if(null != syncLimit && syncLimit >= 0 && syncLimit < leftCount){
            log.debug("使用上限库存数: {}",syncLimit);
           return syncLimit;
        }
        if (leftCount < 0){
            return 0;
        }
        return leftCount;
    }

    /**
     * 同步库存到美团
     *
     * @param platCode
     * @param storeCode
     * @param syncNumber
     */
    @DS("smallpro_write")
    public void excuteSync(String platCode, String storeCode, String syncNumber) {
        //查询当天的为同步成功的数据
        QueryWrapper<StockSync> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("plat_code", platCode);
        queryWrapper.eq("store_code", storeCode);
        queryWrapper.eq("is_sync", false);
        queryWrapper.eq("sync_number", syncNumber);
        List<StockSync> list = list(queryWrapper);
        //更新数据，防止重复同步
        StockSync entity = new StockSync();
        entity.setSync(true);
        update(entity, queryWrapper);
        log.warn("开始同步库存到美团...");
        if (CollectionUtils.isNotEmpty(list)) {
            List<List<StockSync>> listList = ListUtils.partition(list, 100);
            for (List<StockSync> subList : listList) {
                try {
                    retailSkuStock(storeCode, subList);
                } catch (Exception e) {
                    //异常了回滚本次数据
                    StockSync rollbackEntity = new StockSync();
                    rollbackEntity.setSync(false);
                    QueryWrapper<StockSync> rollbackWrapper = new QueryWrapper<>();
                    List<Integer> ids = subList.stream().map(StockSync::getId).collect(Collectors.toList());
                    rollbackWrapper.in("id", ids);
                    update(rollbackEntity, rollbackWrapper);
                    log.error("同步库存数据到美团异常", e);
                }
            }
        }
        log.warn("同步库存到美团结束...");
    }

    /**
     * 调用美团同步库存
     *
     * @param platCode
     * @param list
     */
    @DS("smallpro_write")
    public void excuteSyncV1(String platCode, List<StockSync> list, SyncToMeiTuanExtend syncToMeiTuanExtend) {
        log.warn("开始同步库存到美团...");
        if (CollectionUtils.isNotEmpty(list)) {
            Set<Map.Entry<String, List<StockSync>>> entries = list.parallelStream().collect(Collectors.groupingBy(StockSync::getStoreCode)).entrySet();
            //存储同步失败的数据
            List<StockSync> failList = new ArrayList<>();
            // 是否为全量同步
            boolean isAllSync = IncrementTypeEnum.All.getCode().equals(syncToMeiTuanExtend.getIncrementType());
            for (Map.Entry<String, List<StockSync>> stringListEntry : entries) {
                List<List<StockSync>> listList = ListUtils.partition(stringListEntry.getValue(), 200);
                for (List<StockSync> ssList : listList) {
                        try {
                            // 全量的时候需要检测是否已经被增量同步了, 如果已经增量同步了,数据需要过滤掉
                            // 全量需要, 重新放入到缓存中
                            if(isAllSync){
                                String syncCountMapKey = StockService.getSyncCountMapKey(platCode, stringListEntry.getKey());
                                Map nowSyncCountMap = redisTemplate.opsForHash().entries(syncCountMapKey);
                                ssList = ssList.stream().filter(ss -> ObjectUtil.notEqual(nowSyncCountMap.get(ss.getSkuId()), ss.getSyncCount()))
                                        .collect(Collectors.toList());
                                if(ssList.isEmpty()){
                                    // 全部过滤掉了, 直接跳过
                                    continue;
                                }
                            }

                            List<StockSync> stockSyncs = retailSkuStockV1(platCode, stringListEntry.getKey(), ssList, syncToMeiTuanExtend);
                            if (CollUtil.isNotEmpty(stockSyncs.stream().filter(Objects::nonNull).collect(Collectors.toList()))) {
                                failList.addAll(stockSyncs);
                            }
                        } catch (Exception e) {
                            log.error("同步库存数据到美团异常", e);
                        }
                }
            }
        }
        log.warn("同步库存到美团结束...");
    }

    /**
     * 调用美团接口同步库存并处理返回结果
     *
     * @param storeCode
     * @param list
     */
    public void retailSkuStock(String storeCode, List<StockSync> list) throws Exception {
        //数据转换
        Map<String, List<StockSync>> spuidMap = list.stream().collect(Collectors.groupingBy(StockSync::getProductCode));
        List<StockReq.FoodData> foodDataList = new ArrayList<>();
        for (String spuid : spuidMap.keySet()) {
            List<StockSync> tmpList = spuidMap.get(spuid);
            List<StockReq.Sku> skuList = new ArrayList<>();
            StockReq.FoodData foodData = new StockReq.FoodData();
            for (StockSync tmp : tmpList) {
                StockReq.Sku sku = new StockReq.Sku();
                sku.setSkuId(tmp.getSkuId());
                sku.setStock(String.valueOf(tmp.getSyncCount()));
                skuList.add(sku);
            }
            foodData.setAppFoodCode(spuid);
            foodData.setSkus(skuList);
            foodDataList.add(foodData);
        }
        String foodDataStr = JsonUtils.toJson(foodDataList);
        //获取配置的appid和AppSecret，如果没有则传递上面配置死的参数
        //根据storeCode 获取
        Optional<String> first = list.stream().filter(Objects::nonNull).map(StockSync::getTenantCode).findFirst();
        Optional<String> first1 = list.stream().filter(Objects::nonNull).map(StockSync::getPlatCode).findFirst();
        if (first.isPresent() && first1.isPresent()) {
            Tenant oneTenantBy = tenantService.getOneTenantBy(first.get(), first1.get());
            if (oneTenantBy != null) {
                meituanAppId = oneTenantBy.getTenantCode();
                meituanAppSecret = oneTenantBy.getAppSecret();
            }
        }
        log.warn("请求参数 meituanAppId: {} ，与meituanAppSecret: {}",meituanAppId,meituanAppSecret);
        //组建请求参数,如有其它参数请补充完整
        SystemParam systemParam = new SystemParam(meituanAppId, meituanAppSecret);
        RetailSkuStockRequest retailSkuStockRequest = new RetailSkuStockRequest(systemParam);
        //门店编码
        retailSkuStockRequest.setApp_poi_code(storeCode);
        retailSkuStockRequest.setFood_data(foodDataStr);
        SgOpenResponse sgOpenResponse;
        try {
            sgOpenResponse = retailSkuStockRequest.doRequest();
        } catch (SgOpenException e) {
            log.error("调用美团库存同步接口异常！", e);
            throw new Exception(e);
        } catch (IOException e) {
            log.error("调用美团库存同步接口异常！", e);
            throw new Exception(e);
        }
        if (null == sgOpenResponse) {
            log.error("调用美团库存同步接口异常：返回结果为空");
            return;
        }
        //发起请求时的sig，用来联系美团员工排查问题时使用
        String requestSig = sgOpenResponse.getRequestSig();
        //请求返回的结果，按照官网的接口文档自行解析即可
        String requestResult = sgOpenResponse.getRequestResult();
        if (StringUtils.isBlank(requestResult)) {
            log.warn("调用美团库存同步接口异常：requestResult为空");
        }
        log.warn("调用美团库存同步接口requestSig：{}", requestSig);
        JSONObject rrObj = JSON.parseObject(requestResult);
        rrObj.fluentPut("msg",JSON.parseArray(rrObj.getString("msg")));
        StockRes res = JsonUtils.fromJson(rrObj.toJSONString(), StockRes.class);
        List<Integer> allIds = list.stream().map(StockSync::getId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Integer> failIds = new ArrayList<>();
        List<Integer> successIds = new ArrayList<>();
        //返回成功
        if (StringUtils.equals(StockRes.RESULT_OK, res.getData())) {
            //失败部分需要回滚
            List<StockRes.Message> msgList = res.getMsg();
            if (CollectionUtils.isNotEmpty(msgList)) {
                for (StockRes.Message msg : msgList) {
                    for (StockSync obj : list) {
                        if (StringUtils.equals(obj.getProductCode(), msg.getAppFoodCode()) && StringUtils.equals(obj.getSkuId(), msg.getSkuId())) {
                            failIds.add(obj.getId());
                            break;
                        }
                    }
                }
            }
            successIds.addAll(allIds.stream().filter(e -> !failIds.contains(e)).collect(Collectors.toList()));
        } else {
            failIds.addAll(allIds);
        }
        if (CollectionUtils.isNotEmpty(successIds)) {
            //更新同步成功的记录
            baseMapper.updateSyncSuccess(successIds);
        }
        if (CollectionUtils.isNotEmpty(failIds)) {
            //更新同步失败的记录
            baseMapper.updateSyncFail(failIds);
        }
    }


    /**
     * 调用美团接口同步库存并处理返回结果
     *
     *  @param platCode
     * @param storeCode 门店code
     * @param list      需要同步的数据
     * @return 同步失败的数据
     * @throws Exception
     */
    public List<StockSync> retailSkuStockV1(String platCode, String storeCode, List<StockSync> list, SyncToMeiTuanExtend syncToMeiTuanExtend) throws Exception {
        //数据转换
        Map<String, List<StockSync>> spuidMap = list.stream().collect(Collectors.groupingBy(StockSync::getProductCode));
        List<StockReq.FoodData> foodDataList = new ArrayList<>();
        for (String spuid : spuidMap.keySet()) {
            List<StockSync> tmpList = spuidMap.get(spuid);
            List<StockReq.Sku> skuList = new ArrayList<>();
            StockReq.FoodData foodData = new StockReq.FoodData();
            for (StockSync tmp : tmpList) {
                StockReq.Sku sku = new StockReq.Sku();
                sku.setSkuId(tmp.getSkuId());
                sku.setStock(String.valueOf(tmp.getSyncCount()));
                skuList.add(sku);
            }
            foodData.setAppFoodCode(spuid);
            foodData.setSkus(skuList);
            foodDataList.add(foodData);
        }
        String foodDataStr = JsonUtils.toJson(foodDataList);
        log.warn("同步美团,门店: {}, 商品参数:{}", storeCode,foodDataStr);
        //获取配置的appid和AppSecret，如果没有则传递上面配置死的参数
        //根据storeCode 获取
/*        Optional<String> first = list.stream().filter(Objects::nonNull).map(StockSync::getTenantCode).findFirst();
        Optional<String> first1 = list.stream().filter(Objects::nonNull).map(StockSync::getPlatCode).findFirst();
        if (first.isPresent() && first1.isPresent()) {
            Tenant oneTenantBy = tenantService.getOneTenantBy(first.get(), first1.get());
            if (oneTenantBy != null) {
                meituanAppId = oneTenantBy.getTenantCode();
                meituanAppSecret = oneTenantBy.getAppSecret();
            }
        }*/
        Tenant tenant = Optional.ofNullable(syncToMeiTuanExtend.getTenant()).orElse(new Tenant());
        meituanAppId = tenant.getTenantCode();
        meituanAppSecret = tenant.getAppSecret();
        log.warn("请求参数 meituanAppId: {} ，与meituanAppSecret: {}",meituanAppId,meituanAppSecret);
        //组建请求参数,如有其它参数请补充完整
        SystemParam systemParam = new SystemParam(meituanAppId, meituanAppSecret);
        Integer incrementType = syncToMeiTuanExtend.getIncrementType();
        RetailSkuStockRequest retailSkuStockRequest;
        if(false && Stream.of(IncrementTypeEnum.ONE,IncrementTypeEnum.All).anyMatch(itEnum -> Objects.equals(itEnum.getCode(),incrementType))){
            retailSkuStockRequest = new MyRetailSkuStockRequest(systemParam);
        } else {
            retailSkuStockRequest = new RetailSkuStockRequest(systemParam);
        }
        //门店编码
        retailSkuStockRequest.setApp_poi_code(storeCode);
        retailSkuStockRequest.setFood_data(foodDataStr);
        SgOpenResponse sgOpenResponse;
        SmsService smsService = SpringUtil.getBean(SmsService.class);
        Dict param = Dict.create().set("storeCode", storeCode).set("list", list).set("syncToMeiTuanExtend", syncToMeiTuanExtend);
        try {
            sgOpenResponse = SpringUtil.getBean(RetryService.class).retryByMeituanException(() -> retailSkuStockRequest.doRequest());
        } catch (SgOpenException e) {
            RRExceptionHandler.logError("调用美团库存同步接口SgOpen异常！", param, e, smsService::sendOaMsgTo9JiMan);
            throw new Exception(e);
        } catch (IOException e) {
            RRExceptionHandler.logError("调用美团库存同步接口io异常！", param, e, smsService::sendOaMsgTo9JiMan);
            throw new Exception(e);
        }
        if (null == sgOpenResponse) {
            log.error("调用美团库存同步接口异常：返回结果为空");
            return null;
        }
        //发起请求时的sig，用来联系美团员工排查问题时使用
        String requestSig = sgOpenResponse.getRequestSig();
        //请求返回的结果，按照官网的接口文档自行解析即可
        String requestResult = sgOpenResponse.getRequestResult();
        if (StringUtils.isBlank(requestResult)) {
            log.warn("调用美团库存同步接口异常：requestResult为空");
        }
        log.warn("调用美团库存同步接口requestSig：{}", requestSig);
        JSONObject rrObj = JSON.parseObject(requestResult);
        rrObj.fluentPut("msg",JSON.parseArray(rrObj.getString("msg")));
        StockRes res = JsonUtils.fromJson(rrObj.toJSONString(), StockRes.class);
        List<StockSync> failIds = new ArrayList<>();
        //返回成功
        if (StringUtils.equals(StockRes.RESULT_OK, res.getData())) {
            //失败部分需要回滚
            List<StockRes.Message> msgList = res.getMsg();
            if (CollectionUtils.isNotEmpty(msgList)) {
                msgList.forEach(msg -> list.forEach(obj ->{
                    if (StringUtils.equals(obj.getProductCode(), msg.getAppFoodCode()) && StringUtils.equals(obj.getSkuId(), msg.getSkuId())) {
                        failIds.add(obj);
                    }
                }));
            }
            String syncCountMapKey = StockService.getSyncCountMapKey(platCode, storeCode);
            list.stream().filter(ss -> !failIds.contains(ss))
                    // 成功的才进行缓存
                    .forEach(stockSync -> {
                        redisTemplate.opsForHash().put(syncCountMapKey, stockSync.getSkuId(), stockSync.getSyncCount());
                    });
        } else {
            log.warn("美团同步失败结果: {}",requestResult);
            failIds.addAll(list);
        }
        return failIds;
    }

    @Override
    @DS(DataSourceConstants.SMALLPRO_WRITE)
    public Boolean syncStore(List<String> area) {
        if (Objects.equals(area.get(0), "all")) {
            //查询所有门店
            List<Store> list = storeService.list(new LambdaQueryWrapper<>());
            List<Integer> collect = list.stream().map(Store::getAreaId).collect(Collectors.toList());
            R<List<AreaInfo>> listR = areaInfoClient.listAreaInfo(collect);
            List<AreaInfo> areaInfos = Optional.ofNullable(listR).map(R::getData).orElse(new ArrayList<>());
            extracted(areaInfos);
        }else if(Objects.equals(area.get(0), "del")){
            storeService.update(new LambdaUpdateWrapper<Store>().set(Store::getAssociatedStores,null));
        } else {
            R<List<AreaInfo>> listR = areaInfoClient.listAreaInfoByAreas(area);
            List<AreaInfo> areaInfos = Optional.ofNullable(listR).map(R::getData).orElse(new ArrayList<>());
            extracted(areaInfos);
        }
        return Boolean.TRUE;
    }

    /**
     * syncTime 单位为分钟
     */
    @Override
    public void syncToMtStock(String platCode, Integer syncTime, String syncType) {
        // 计算同步时间
        String incrementalLastTimeKey = StrUtil.format(RedisKeyConstant.MEITUAN_INCREMENTAL_LAST_TIME,
                Optional.of(platCode).filter(pc -> ObjectUtil.notEqual(pc, ThirdPlatformCommonConst.THIRD_PLAT_MT))
                        .orElse("meituan"), syncType);
        String lastTimeStr = (String)redisTemplate.opsForValue().get(incrementalLastTimeKey);
        LocalDateTime syncStartTime;
        if(StrUtil.isNotBlank(lastTimeStr)){
            syncStartTime = LocalDateTime.parse(lastTimeStr, DateTimeFormatter.ofPattern(TimeFormatConstant.YYYY_MM_DD_HH_MM_SS));
        }else{
            // 放大两倍, 避免漏数据
            syncStartTime = LocalDateTime.now().plusMinutes(syncTime*2);
        }
        //查询变动库存数据
        List<ChangeStock> changeStocks = this.baseMapper.selectChangeStock(platCode,syncStartTime, syncType);
        if(CollectionUtils.isEmpty(changeStocks)){
            return;
        }

        // 使用 groupingBy 将 ChangeStock 按照 changeType 分组
        Map<Integer, Optional<ChangeStock>> maxTimesByType = changeStocks.stream()
                .collect(Collectors.groupingBy(ChangeStock::getChangeType,
                        Collectors.maxBy(Comparator.comparing(ChangeStock::getUpdateTime))));
        // 获取每个类型中最大同步时间, 不同类型取较小时间, 作为下一次同步时间
        Optional<ChangeStock> allTypeMinTimeOpt = maxTimesByType.entrySet().stream().map(entry -> entry.getValue())
                // 不能与当前同步时间相同,否则会一直不变
                .filter(Optional::isPresent).map(Optional::get).filter(cs -> Math.abs(Duration.between(cs.getUpdateTime(),syncStartTime).getSeconds())>1)
                .min(Comparator.comparing(ChangeStock::getUpdateTime));
        //获取没有门店id的数据
        List<ChangeStock> noAreaIdList = changeStocks.stream().filter(item -> NO_AREAID.equals(item.getAreaId())).collect(Collectors.toList());
        //获取有门店id的数据
        List<ChangeStock> areaIdList = changeStocks.stream().filter(item -> !NO_AREAID.equals(item.getAreaId())).collect(Collectors.toList());
        //异步同步没有门店id的商品
        syncToMtStockNoAreaId(platCode, noAreaIdList);
        //同步存在门店id的商品
        syncToMtStockAreaId(platCode, areaIdList);
        allTypeMinTimeOpt.ifPresent(cs -> redisTemplate.opsForValue().set(incrementalLastTimeKey,
                cs.getUpdateTime().format(DateTimeFormatter.ofPattern(TimeFormatConstant.YYYY_MM_DD_HH_MM_SS))));
    }



    /**
     * 同步没有门店id的商品
     *
     * @param platCode
     * @param changeStockList
     */
    public void syncToMtStockAreaId(String platCode, List<ChangeStock> changeStockList){
        if(CollectionUtils.isEmpty(changeStockList)){
            return;
        }
        //查询美团门店相关相关信息
        List<Store> storeList = storeService.lambdaQuery()
                .eq(Store::getPlatCode,platCode)
                .eq(Store::getIsEnable,Boolean.TRUE)
                .select(Store::getAreaId,Store::getStoreCode, Store::getAssociatedStores)
                .list();
        if(CollectionUtils.isEmpty(storeList)){
            return ;
        }
        // 主门店分组map
        Map<Integer, List<Store>> areaIdStoreMap = storeList.stream().collect(Collectors.groupingBy(Store::getAreaId));
        // 关联门店分组map
        Map<String, List<Store>> assoStoreMap = storeList.stream()
                .flatMap(s -> StrUtil.splitTrim(s.getAssociatedStores(), StringPool.COMMA).stream().map(ass -> new Tuple(ass, s)))
                .collect(Collectors.groupingBy(tup -> tup.get(0),Collectors.mapping(tup -> tup.get(1), Collectors.toList())));
        Map<String,SyncMeituanStoreStock> syncMeituanStoreStockMap = new HashMap<>();
        //修改主门店
        changeStockList.forEach(item->{
            Integer areaId = item.getAreaId();
            //判断是否存在主门店id
            List<Store> mainStores = areaIdStoreMap.getOrDefault(areaId,Collections.emptyList());
            List<Store> assoStores = assoStoreMap.getOrDefault(item.getArea(),Collections.emptyList());
            if(CollUtil.isNotEmpty(mainStores) || CollUtil.isNotEmpty(assoStores)){
                Stream.concat(mainStores.stream(), assoStores.stream())
                        .forEach(s -> {
                            SyncMeituanStoreStock smss = syncMeituanStoreStockMap.get(s.getStoreCode());
                            if (smss == null) {
                                smss = new SyncMeituanStoreStock().setStoreCode(s.getStoreCode()).setPpids(new LinkedList<>());
                                syncMeituanStoreStockMap.put(s.getStoreCode(), smss);
                            }
                            smss.getPpids().add(item.getPpriceId());
                        });
            }
        });

        // 如果没有同步的门店,直接返回
        if(syncMeituanStoreStockMap.isEmpty()){
            return;
        }

        syncToMeiTuanV1(Dict.create()
                .set("platCode", platCode)
                .set("syncMeituanStoreStockMap", syncMeituanStoreStockMap)
                .set("incrementType", IncrementTypeEnum.ONE.getCode()));
    }
    /**
     * 同步没有门店id的商品
     *
     * @param platCode
     * @param noAreaIdList
     */
    public void syncToMtStockNoAreaId(String platCode, List<ChangeStock> noAreaIdList){
        if(CollectionUtils.isEmpty(noAreaIdList)){
            return;
        }
        List<Integer> ppidList = noAreaIdList.stream().map(ChangeStock::getPpriceId).collect(Collectors.toList());
        Dict dict = new Dict();
        dict.set("ppids",ppidList);
        dict.set("incrementType", IncrementTypeEnum.ONE.getCode());
        dict.set("platCode", platCode);
        syncToMeiTuanV1(dict);
    }


    private void extracted(List<AreaInfo> areaInfos) {
        for (AreaInfo areaInfo : areaInfos) {
            List<String> saveData = storeService.getNearShops(areaInfo.getArea(), 3000);
            String join = String.join(",", saveData);
            Store store = new Store();
            store.setAssociatedStores(join);
            store.setAreaId(areaInfo.getId());
            store.setEnable(Boolean.TRUE);
            storeService.update(store,new LambdaUpdateWrapper<Store>().eq(Store::getAreaId,areaInfo.getId()));
        }
    }


}
