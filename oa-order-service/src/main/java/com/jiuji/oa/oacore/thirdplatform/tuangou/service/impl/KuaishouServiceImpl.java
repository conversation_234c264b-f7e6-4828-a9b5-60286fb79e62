package com.jiuji.oa.oacore.thirdplatform.tuangou.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.jiuji.oa.oacore.common.constant.RedisKeyConstant;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.thirdplatform.common.enums.PlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.TuangouPlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.common.util.JsonUtils;
import com.jiuji.oa.oacore.thirdplatform.mapstruct.KuaishouMapstruct;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.TenantService;
import com.jiuji.oa.oacore.thirdplatform.tenant.vo.TenantStoreReq;
import com.jiuji.oa.oacore.thirdplatform.tuangou.dto.TenantStoreDto;
import com.jiuji.oa.oacore.thirdplatform.tuangou.service.KuaishouService;
import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.MeituanTuangouToken;
import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.req.*;
import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.res.*;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.kuaishou.locallife.open.api.KsLocalLifeApiException;
import com.kuaishou.locallife.open.api.client.KsLocalLifeAccessTokenClient;
import com.kuaishou.locallife.open.api.client.oauth.OAuthAccessTokenKsClient;
import com.kuaishou.locallife.open.api.request.locallife_trade.*;
import com.kuaishou.locallife.open.api.response.locallife_trade.*;
import com.kuaishou.locallife.open.api.response.oauth.KsAccessTokenPreviousVersionResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 抖音生活服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class KuaishouServiceImpl implements KuaishouService {
    @Resource
    private TenantService tenantService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private KuaishouMapstruct kuaishouMapstruct;
    @Resource
    private SmsService smsService;

    @Override
    public boolean isMyPlatfrom(String platfrom) {
        return TuangouPlatfromEnum.KSTG.getPlatfromCode().equals(platfrom);
    }

    @Override
    public String certificatePrepare (CertificatePrepareReq req) {
        MeituanTuangouToken token = getTokenByCache(req.getAreaId());
        KsLocalLifeAccessTokenClient client = KsLocalLifeAccessTokenClient.Builder.newBuilder().setAccessToken(token.getAccessToken()).build();
        //验券准备参数
        GoodlifeV1FulfilmentCertificatePrepareRequest request = new GoodlifeV1FulfilmentCertificatePrepareRequest();
        request.setCode(req.getCode());
        request.setEncrypted_data(req.getEncryptedData());
        CertificatePrepareData certificatePrepare;
        try {
            GoodlifeV1FulfilmentCertificatePrepareResponse response = client.execute(request);
            log.info("调用快手验券准备接口request={}，response={}", JsonUtils.toJson(request), JsonUtils.toJson(response));
            certificatePrepare = kuaishouMapstruct.toCertificatePrepareData(response.getData());
        } catch (KsLocalLifeApiException e) {
            RRExceptionHandler.logError("调用快手验券准备接口异常", request, e, smsService::sendOaMsgTo9JiMan);
            throw new CustomizeException("调用快手验券准备接口异常,请稍后重试");
        }
        TuangouDataRes<CertificatePrepareData> res = new TuangouDataRes<>();
        res.setData(certificatePrepare);
        return JsonUtils.toJson(res);
    }

    @Override
    public String certificateVerify (CertificateVerifyReq req) {
        if (CollectionUtils.isEmpty(req.getEncryptedCodes())) {
            throw new CustomizeException("核销券码不能为空");
        }
        MeituanTuangouToken token = getTokenByCache(req.getAreaId());
        //核销的快手门店id
        String poiId = token.getOpenShopUuid();

        KsLocalLifeAccessTokenClient client = KsLocalLifeAccessTokenClient.Builder.newBuilder().setAccessToken(token.getAccessToken()).build();
        //验券参数
        GoodlifeV1FulfilmentCertificateVerifyRequest request = new GoodlifeV1FulfilmentCertificateVerifyRequest();
        request.setVerify_token(req.getVerifyToken());
        request.setPoi_id(poiId);
        request.setEncrypted_codes(req.getEncryptedCodes());
        request.setCodes(req.getCodes());
        request.setOrder_id(req.getOrderId());
        CertificateVerifyData certificateVerifyData;
        try {
            GoodlifeV1FulfilmentCertificateVerifyResponse response = client.execute(request);
            log.info("调用快手验券接口request={}，response={}", JsonUtils.toJson(request), JsonUtils.toJson(response));
            certificateVerifyData = kuaishouMapstruct.toCertificateVerifyData(response.getData());
        } catch (KsLocalLifeApiException e) {
            RRExceptionHandler.logError("调用快手验券接口异常", request, e, smsService::sendOaMsgTo9JiMan);
            throw new CustomizeException("调用快手验券接口异常,请稍后重试");
        }

        TuangouDataRes<CertificateVerifyData> res = new TuangouDataRes<>();
        res.setData(certificateVerifyData);
        return JsonUtils.toJson(res);
    }

    @Override
    public String certificateCancel (CertificateCancelReq req) {
        MeituanTuangouToken token = getTokenByCache(req.getAreaId());
        KsLocalLifeAccessTokenClient client = KsLocalLifeAccessTokenClient.Builder.newBuilder().setAccessToken(token.getAccessToken()).build();
        //撤销参数
        GoodlifeV1FulfilmentCertificateCancelRequest request = new GoodlifeV1FulfilmentCertificateCancelRequest();
        request.setCertificate_id(req.getCertificateId());
        request.setVerify_id(req.getVerifyId());

        CertificateCancelData certificateCancelData;
        try {
            GoodlifeV1FulfilmentCertificateCancelResponse response = client.execute(request);
            log.info("调用快手撤销验券接口request={}，response={}", JsonUtils.toJson(request), JsonUtils.toJson(response));
            certificateCancelData = kuaishouMapstruct.toCertificateCancelData(response.getData());
        } catch (KsLocalLifeApiException e) {
            RRExceptionHandler.logError("调用快手撤销验券接口异常", request, e, smsService::sendOaMsgTo9JiMan);
            throw new CustomizeException("调用快手撤销验券接口异常,请稍后重试");
        }
        TuangouDataRes<CertificateCancelData> res = new TuangouDataRes<>();
        res.setData(certificateCancelData);
        return JsonUtils.toJson(res);
    }

    @Override
    public String certificateGet (CertificateGetReq req) {
        MeituanTuangouToken token = getTokenByCache(req.getAreaId());
        KsLocalLifeAccessTokenClient client = KsLocalLifeAccessTokenClient.Builder.newBuilder().setAccessToken(token.getAccessToken()).build();
        GoodlifeV1FulfilmentCertificateGetRequest request = new GoodlifeV1FulfilmentCertificateGetRequest();
        request.setEncrypted_code(req.getEncryptedCode());
        try {
            GoodlifeV1FulfilmentCertificateGetResponse response = client.execute(request);
            log.info("调用快手券状态查询接口request={}，response={}", JsonUtils.toJson(request), JsonUtils.toJson(response));
        } catch (KsLocalLifeApiException e) {
            RRExceptionHandler.logError("调用快手券状态查询接口异常", request, e, smsService::sendOaMsgTo9JiMan);
            throw new CustomizeException("调用快手券状态查询接口异常,请稍后重试");
        }
        return null;
    }

    @Override
    public String certificateQuery (CertificateQueryReq req) {
        MeituanTuangouToken token = getTokenByCache(req.getAreaId());
        KsLocalLifeAccessTokenClient client = KsLocalLifeAccessTokenClient.Builder.newBuilder().setAccessToken(token.getAccessToken()).build();

        GoodlifeV1FulfilmentCertificateQueryRequest request = new GoodlifeV1FulfilmentCertificateQueryRequest();
        request.setEncrypted_code(req.getEncryptedCode());
        request.setOrder_id(req.getOrderId());
        CertificateQueryData certificateQueryData = new CertificateQueryData();
        try {
            GoodlifeV1FulfilmentCertificateQueryResponse response = client.execute(request);
            log.info("调用快手券状态查询接口request={}，response={}", JsonUtils.toJson(request), JsonUtils.toJson(response));
            certificateQueryData = kuaishouMapstruct.toCertificateQueryData(response.getData());
        } catch (KsLocalLifeApiException e) {
            RRExceptionHandler.logError("调用快手券状态查询接口异常！", req, e, smsService::sendOaMsgTo9JiMan);
            throw new CustomizeException("调用快手券状态查询接口异常,请稍后重试");
        }
        //只可撤销当天核销且未超过10分钟的团购券
        certificateQueryData.setCancelLimitTime(NumberConstant.SIXTY);
        TuangouDataRes<CertificateQueryData> res = new TuangouDataRes<>();
        res.setData(certificateQueryData);
        return JsonUtils.toJson(res);
    }

    /**
     * 授权后查询缓存token
     * @param code
     * @param state
     * @return
     */
    @Override
    public String getToken (String code, String state) {
        String appKey = "";
        if (StringUtils.isNotBlank(state)) {
            String[] stateSplit = state.split(StrUtil.DASHED);
            if (stateSplit.length == NumberConstant.THREE) {
                appKey = stateSplit[2];
            }
        }

        Tenant tenant = tenantService.getTenantByPlatCodeAndAppkey(PlatfromEnum.KSTG.name(), appKey);
        if (Objects.isNull(tenant)) {
            throw new CustomizeException("未查询到快手团购配置信息");
        }
        appKey = tenant.getAppKey();
        String appSecret = tenant.getAppSecret();
        OAuthAccessTokenKsClient client = new OAuthAccessTokenKsClient(appKey, appSecret);
        String token = "";
        try {
            KsAccessTokenPreviousVersionResponse response = client.getAccessToken(code);
            log.info("获取快手toekn,code={},response={}", code,response);

            MeituanTuangouToken tuangouToken = new MeituanTuangouToken();
            tuangouToken.setAppKey(tenant.getAppKey());
            tuangouToken.setAppSecret(tenant.getAppSecret());
            tuangouToken.setAccessToken(response.getAccessToken());
            tuangouToken.setRefreshToken(response.getRefreshToken());
            tuangouToken.setBid(response.getOpenId());
            tuangouToken.setExpiresIn(response.getExpiresIn());
            tuangouToken.setScope(JSONUtil.toJsonStr(response.getScopes()));
            tuangouToken.setExpiresTime(LocalDateTime.now().plusSeconds(tuangouToken.getExpiresIn()).minusSeconds(NumberConstant.SIXTY));

            String tokenKey = RedisKeyConstant.KUAI_SHOU_TOKEN + appKey;
            stringRedisTemplate.opsForValue().set(tokenKey, JsonUtils.toJson(tuangouToken), NumberConstant.ONE_HUNDRED_EIGHTY, TimeUnit.DAYS);
            token = response.getAccessToken();
        } catch (KsLocalLifeApiException e) {
            RRExceptionHandler.logError("获取快手toekn异常！", code, e, smsService::sendOaMsgTo9JiMan);
        }
        return token;
    }

    /**
     * 从缓存查询token
     * @return
     */
    public MeituanTuangouToken getTokenByCache (Integer areaId) {
        TenantStoreReq tenantStoreReq = TenantStoreReq.builder().platCode(PlatfromEnum.KSTG.name()).areaId(areaId).build();
        TenantStoreDto tenantStore = tenantService.getTenantStore(tenantStoreReq);
        String appKey = tenantStore.getAppKey();

        String tokenKey = RedisKeyConstant.KUAI_SHOU_TOKEN + appKey;
        String tokenStr = stringRedisTemplate.opsForValue().get(tokenKey);
        if (StringUtils.isBlank(tokenStr)) {
            throw new CustomizeException("快手授权已过期，请到快手商家后台重新授权！");
        }
        MeituanTuangouToken meituanTuangouToken = JsonUtils.fromJson(tokenStr, MeituanTuangouToken.class);
        if (Objects.nonNull(meituanTuangouToken.getExpiresTime()) && LocalDateTime.now().isAfter(meituanTuangouToken.getExpiresTime())) {
            meituanTuangouToken = refreshToken(meituanTuangouToken);
            //设置过期时间
            meituanTuangouToken.setExpiresTime(LocalDateTime.now().plusSeconds(meituanTuangouToken.getExpiresIn()).minusSeconds(NumberConstant.SIXTY));
            stringRedisTemplate.opsForValue().set(tokenKey, JsonUtils.toJson(meituanTuangouToken), NumberConstant.ONE_HUNDRED_EIGHTY, TimeUnit.DAYS);
        }
        meituanTuangouToken.setOpenShopUuid(tenantStore.getStoreCode());
        return meituanTuangouToken;
    }

    /**
     * 刷新token
     * @param meituanTuangouToken
     * @return
     */
    public MeituanTuangouToken refreshToken (MeituanTuangouToken meituanTuangouToken) {
        try {
            OAuthAccessTokenKsClient client = new OAuthAccessTokenKsClient(meituanTuangouToken.getAppKey(), meituanTuangouToken.getAppSecret());
            KsAccessTokenPreviousVersionResponse ksAccessTokenResponse = client.refreshAccessToken(meituanTuangouToken.getRefreshToken());

            MeituanTuangouToken tuangouToken = new MeituanTuangouToken();
            tuangouToken.setAppKey(meituanTuangouToken.getAppKey());
            tuangouToken.setAppSecret(meituanTuangouToken.getAppSecret());
            tuangouToken.setAccessToken(ksAccessTokenResponse.getAccessToken());
            tuangouToken.setRefreshToken(ksAccessTokenResponse.getRefreshToken());
            tuangouToken.setBid(ksAccessTokenResponse.getOpenId());
            tuangouToken.setExpiresIn(ksAccessTokenResponse.getExpiresIn());
            tuangouToken.setScope(JSONUtil.toJsonStr(ksAccessTokenResponse.getScopes()));
            return tuangouToken;
        } catch (Exception e) {
            RRExceptionHandler.logError("刷新快手token异常！", meituanTuangouToken, e, smsService::sendOaMsgTo9JiMan);
            throw new CustomizeException("快手appkey:"+meituanTuangouToken.getAppKey()+"刷新token异常");
        }
    }
}
