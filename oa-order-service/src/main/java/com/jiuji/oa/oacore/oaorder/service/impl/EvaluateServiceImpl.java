package com.jiuji.oa.oacore.oaorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.utils.XtenantJudgeUtil;
import com.ch999.common.util.vo.Result;
import com.google.common.collect.Lists;
import com.jiuji.cloud.office.service.MsgPushCloud;
import com.jiuji.cloud.org.service.AreaInfoCloud;
import com.jiuji.cloud.org.service.RoleInfoCloud;
import com.jiuji.cloud.org.vo.enums.RoleTermModuleEnum;
import com.jiuji.cloud.org.vo.enums.RoleTermTypeEnum;
import com.jiuji.cloud.org.vo.response.RoleTermRes;
import com.jiuji.oa.nc.MessagePushCloud;
import com.jiuji.oa.nc.StoreAreaInfoCloud;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.bo.ZnSendConnBo;
import com.jiuji.oa.oacore.common.component.UserComponent;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.config.properties.ImageProperties;
import com.jiuji.oa.oacore.common.config.properties.JiujiSystemProperties;
import com.jiuji.oa.oacore.common.config.properties.SmsProperties;
import com.jiuji.oa.oacore.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.oacore.common.config.redis.CacheTemplateService;
import com.jiuji.oa.oacore.common.constant.RedisKeyConstant;
import com.jiuji.oa.oacore.common.constant.UrlConstant;
import com.jiuji.oa.oacore.common.enums.EvaluateTypeEnum;
import com.jiuji.oa.oacore.common.enums.*;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.res.PageRes;
import com.jiuji.oa.oacore.common.source.InwcfSource;
import com.jiuji.oa.oacore.common.source.MUrlSource;
import com.jiuji.oa.oacore.common.source.MoaUrlSource;
import com.jiuji.oa.oacore.common.source.appIndex.AppIndexWwwUrlSource;
import com.jiuji.oa.oacore.common.util.*;
import com.jiuji.oa.oacore.common.util.profileutil.InnerProfileJudgeUtil;
import com.jiuji.oa.oacore.goldseed.service.AreaInfoService;
import com.jiuji.oa.oacore.oaorder.bo.*;
import com.jiuji.oa.oacore.oaorder.client.EvaluateProductClient;
import com.jiuji.oa.oacore.oaorder.client.vo.EvaluateProductCommentVO;
import com.jiuji.oa.oacore.oaorder.client.vo.ProductCommentAggBO;
import com.jiuji.oa.oacore.oaorder.dao.*;
import com.jiuji.oa.oacore.oaorder.enums.*;
import com.jiuji.oa.oacore.oaorder.po.*;
import com.jiuji.oa.oacore.oaorder.req.*;
import com.jiuji.oa.oacore.oaorder.res.*;
import com.jiuji.oa.oacore.oaorder.service.*;
import com.jiuji.oa.oacore.oaorder.vo.req.ChangeEvaluateUserReq;
import com.jiuji.oa.oacore.oaorder.vo.req.EvaluateListExQuery;
import com.jiuji.oa.oacore.oaorder.vo.req.EvaluateRedPackReq;
import com.jiuji.oa.oacore.oaorder.vo.req.WuXiaoReq;
import com.jiuji.oa.oacore.oaorder.vo.res.EvaluateEndInfo;
import com.jiuji.oa.oacore.oaorder.vo.res.EvaluateEndInfo.EvaluateDepartInfo;
import com.jiuji.oa.oacore.oaorder.vo.res.LiangPinCommentRes;
import com.jiuji.oa.oacore.oaorder.vo.res.LiangPinCommentVO;
import com.jiuji.oa.oacore.partner.evaluate.util.SpringContext;
import com.jiuji.oa.oacore.promocode.po.NumberCard;
import com.jiuji.oa.oacore.promocode.service.NumberCardService;
import com.jiuji.oa.oacore.promocode.vo.req.NumberCardAddReq;
import com.jiuji.oa.oacore.promocode.vo.res.CommonResult;
import com.jiuji.oa.oacore.staff.enums.RedPackCommentEnum;
import com.jiuji.oa.oacore.staff.service.StaffScoreRedpackService;
import com.jiuji.oa.oacore.staff.vo.FenAndYanyinVO;
import com.jiuji.oa.oacore.staff.vo.req.AddUserFenReq;
import com.jiuji.oa.oacore.tcc.config.SeataAlerter;
import com.jiuji.oa.oacore.tcc.service.ITccEvaluateUpdateAction;
import com.jiuji.oa.oacore.tcc.service.ITccStaffScoreRedpackAction;
import com.jiuji.oa.oacore.thirdplatform.common.util.JsonUtils;
import com.jiuji.oa.oacore.thirdplatform.common.util.SerializationException;
import com.jiuji.oa.oacore.tousu.bo.StaffPraiseBO;
import com.jiuji.oa.oacore.tousu.bo.TousuAreaBO;
import com.jiuji.oa.oacore.tousu.bo.UserMsg;
import com.jiuji.oa.oacore.tousu.bo.ZeRenRen;
import com.jiuji.oa.oacore.tousu.dao.TouSuMapper;
import com.jiuji.oa.oacore.tousu.dao.TousuAreaMapper;
import com.jiuji.oa.oacore.tousu.enums.DataTypeEnum;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.oacore.tousu.po.AreaInfoModel;
import com.jiuji.oa.oacore.tousu.po.TousuCategory;
import com.jiuji.oa.oacore.tousu.res.EvaluateOrderInfoRes;
import com.jiuji.oa.oacore.tousu.res.EvaluateRewardInfo;
import com.jiuji.oa.oacore.tousu.res.HighOpinionRes;
import com.jiuji.oa.oacore.tousu.res.XtenantSubject;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.oacore.tousu.service.TousuAreaService;
import com.jiuji.oa.oacore.tousu.service.impl.TouSuServiceImpl;
import com.jiuji.oa.office.evaluate.client.EvaluateClient;
import com.jiuji.oa.office.evaluate.vo.EvaluateDetailVO;
import com.jiuji.oa.operation.outdoorstore.res.AreaFindVo;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.departinfo.client.DepartInfoClient;
import com.jiuji.oa.orginfo.departinfo.vo.DepartInfoVO;
import com.jiuji.oa.orginfo.member.client.MemberClient;
import com.jiuji.oa.orginfo.member.res.MemberBasicRes;
import com.jiuji.oa.orginfo.member.res.WeixinRes;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.IntConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.xtenant.Namespaces;
import com.jiuji.wcf.wcfclient.csharp.gen.member;
import com.jiuji.wcf.wcfclient.csharp.gen.oaenum.EvaluateType;
import com.jiuji.wcf.wcfclient.model.oa.EvaluateRankResult;
import io.seata.common.util.NetUtil;
import io.seata.spring.annotation.GlobalTransactional;

import java.math.RoundingMode;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-31
 */
@DS("office")
@Service
@Slf4j
@RequiredArgsConstructor
public class EvaluateServiceImpl extends ServiceImpl<EvaluateMapper, Evaluate> implements EvaluateService {

    private static final Integer AREA_EVALUATE_TAG_TYPE = 19;
    private static final Pattern NUM_PATTERN = Pattern.compile("^[0-9]*$");
    private final MemberClient memberClient;
    private final Object lock = new Object();
    private final Object obj = new Object();
    private final Object jifenLock = new Object();
    private final RecoverBasketService recoverBasketService;
    private final BasketService basketService;
    private final ShouhouService shouhouService;
    private final Ch999UserService ch999UserService;
    private final EvaluateScoreMapper evaluateScoreMapper;
    private final EvaluateMapper evaluateMapper;
    private final AreaInfoService areaInfoService;
    private final RedisTemplate redisTemplate;
    private final CacheTemplateService cacheTemplateService;
    private static final String JIUJI_PRINT_NAME = "九机网";
    private static final DateTimeFormatter PROCESS_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
    @Autowired
    private BbsxpUsersService bbsxpUsersService;
    @Autowired
    private BbsxpUsersMapper bbsxpUsersMapper;
    @Autowired
    private Ch999UserMapper ch999UserMapper;
    @Autowired
    private JiujiSystemProperties jiujiSystemProperties;
    @Autowired
    private EvaluateService evaluateService;
    @Autowired
    private EvaluateTagService evaluateTagService;
    @Autowired
    private EvaluateTagRecordService evaluateTagRecordService;
    @Autowired
    private MsoftService msoftService;
    @Autowired
    private MsoftMapper msoftMapper;
    @Autowired
    private AmqpTemplate amqpTemplate;
    @Autowired
    private EvaluateIdAndMsoftIdService evaluateIdAndMsoftIdService;
    @Autowired
    private CallCenterServiceEvalRecordService callCenterServiceEvalRecordService;
    @Autowired
    private MUrlSource mUrlSource;
    @Autowired
    private OrderBaseInfoService orderBaseInfoService;
    @Autowired
    private BasketMapper basketMapper;
    @Autowired
    private ShouyingMapper shouyingMapper;
    @Autowired
    private SubMapper subMapper;
    @Autowired
    private WuliuMapper wuliuMapper;
    @Autowired
    private UserInfoClient userInfoClient;
    @Autowired
    private ShouhouMapper shouhouMapper;
    @Autowired
    private CallCenterServiceEvalRecordMapper callCenterServiceEvalRecordMapper;
    @Autowired
    private RecoverMarketsubinfoMapper recoverMarketsubinfoMapper;
    @Autowired
    private MsgCenterQueueMapper msgCenterQueueMapper;
    @Autowired
    private SmallproMapper smallproMapper;
    @Autowired
    private RecoverSubMapper recoverSubMapper;
    @Autowired
    private TousuMapper tousuMapper;
    @Autowired
    private TouSuMapper complaintMapper;
    @Autowired
    private DoorVisitRecordMapper doorVisitRecordMapper;
    @Autowired
    private ApplyPrizeMapper applyPrizeMapper;
    @Autowired
    private TApprovallogMapper tApprovallogMapper;
    @Resource
    private MessagePushCloud messagePushCloud;
    @Autowired
    private EvaluateTagRecordMapper evaluateTagRecordMapper;
    @Autowired
    private EvaluateScoreService evaluateScoreService;
    @Autowired
    private EvaluateInvalidRecordService evaluateInvalidRecordService;
    @Autowired
    private Ch999EvaluateInfoService ch999EvaluateInfoService;
    @Autowired
    private Ch999EvaluateInfoMapper ch999EvaluateInfoMapper;
    @Autowired
    private EvaluateTagMapper evaluateTagMapper;
    @Autowired
    private EvaluateQuestionAnswerService evaluateQuestionAnswerService;
    @Autowired
    private EvaluateQuestionAnswerMapper evaluateQuestionAnswerMapper;
    @Autowired
    private TouSuMapper touSuMapper;
    @Autowired
    private InwcfSource inwcfSource;
    @Autowired
    private AppIndexWwwUrlSource wwwUrlSource;
    @Autowired
    private JifenService jifenService;
    @Resource
    private SmsProperties smsProperties;
    @Autowired
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Autowired
    private AreaInfoClient areaInfoClient;
    @Resource
    private MoaUrlSource moaUrlSource;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private CategoryMapper categoryMapper;
    @Autowired
    private BrandMapper brandMapper;
    @Autowired
    private ProductinfoMapper productMapper;
    @Autowired
    private NumberCardService numberCardService;
    @Autowired
    private SubService subService;
    @Autowired
    private StaffScoreRedpackService staffScoreRedpackService;
    @Autowired
    private EvaluateClient evaluateClient;
    @Autowired
    private DepartInfoClient departInfoClient;
    @Autowired
    private SmsService smsService;
    @Resource
    private EvaluateProductClient evaluateProductClient;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private SysConfigClient sysConfigClient;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ITccEvaluateUpdateAction tccEvaluateUpdateAction;
    @Autowired
    private ITccStaffScoreRedpackAction tccStaffScoreRedpackAction;
    @Autowired
    private SeataAlerter seataAlerter;
    @Autowired
    private StoreAreaInfoCloud storeAreaInfoCloud;
    @Resource
    private UserComponent userComponent;
    @Resource
    private TouSuServiceImpl touSuService;
    @Resource
    private TousuAreaMapper tousuAreaMapper;
    @Resource
    private TousuAreaService tousuAreaService;
    @Resource
    private MsgPushCloud msgPushCloud;
    @Resource
    private RoleInfoCloud roleInfoCloud;
    @Resource
    private IEvaluateDepartService evaluateDepartService;
    @Resource
    private IEvaluateCategoryRelationService evaluateCategoryRelationService;
    @Resource
    private AreaInfoCloud areaInfoCloud;
    @Resource
    private CategoryService categoryService;

     @Resource
    private ImageProperties imageProperties;

    @Value("${jiuji.sys.pc}")
    private String oaUrl;


    private static final String ERROR_MSG_WUXIAO_FENSHU = "无效的分数类型";
    private static final String ERROR_MSG_WUXIAO_DIPING = "低评无效，退回扣除积分：";
    private static final String WEB_APP_PUSH_URL = "https://m.9ji.com/cloudapi_nc/pushGateway/api/pushGateway/pushMsg/v1?xservicename=push-gateway";
    private static final String WEB_APP_MSG_URL = "https://m.9ji.com/web/inApi/userMsg/insert";

    private static final String EVALUATE_URL = "{}/new/#/operation/customers-appraisal/detail/{}";

    @Override
    public PageRes<EvaluateInfoRes> getEvaluatePages(Integer current, Integer size, Integer type, Integer authId) {
        if (current == null || current == 0) {
            current = 1;
        }
        if (size == null || size == 0) {
            size = 20;
        }
        PageRes<EvaluateInfoRes> page = new PageRes<>(current, size);
        EvaluateTypeEnum typeEnum = EnumUtil.getEnumByCode(EvaluateTypeEnum.class, type);
        if (typeEnum == null) {
            return page;
        }
        boolean jiujiMore = XtenantJudgeUtil.isJiujiMore();
        int startRow = (current - 1) * size;
        if (Boolean.FALSE.equals(jiujiMore) && startRow >= 100) {
            return page;
        }
        List<EvaluateInfoRes> allList = ((EvaluateService) AopContext.currentProxy()).getEvaluateListByCache(type, authId, jiujiMore);
        page.setTotal(allList.size());
        //进行假分页
        int i = current * size;
        if (allList.size() < i) {
            i = allList.size();
        }
        List<EvaluateInfoRes> records = allList.subList(startRow, i);
        page.setRecords(records);
        return page;
    }

    @Override
    public List<EvaluateInfoRes> getEvaluateListByCache(Integer type, Integer authId, boolean jiujiMore) {
        // 为预防缓存失效后大量请求同时访问数据库而加锁。
        synchronized (lock) {
            List<EvaluateInfoRes> list = baseMapper.getEvaluatePages(jiujiMore,0, 100, type, authId);
            buildEvaluateInfoResData(list, type);
            return list;
        }
    }

    private void buildEvaluateInfoResData(List<EvaluateInfoRes> list, Integer type) {
        if (CollectionUtils.isNotEmpty(list)) {
            List<Integer> userIds = list.stream().map(EvaluateInfoRes::getUserId).distinct().collect(Collectors.toList());
            List<Integer> subIds = list.stream().map(EvaluateInfoRes::getSubId).distinct().collect(Collectors.toList());
            R<List<MemberBasicRes>> memberListRet = memberClient.getMemberBasicList(userIds);
            Map<Integer, MemberBasicRes> userMap = null;
            if (ResultCode.SUCCESS == memberListRet.getCode() && CollectionUtils.isNotEmpty(memberListRet.getData())) {
                userMap = memberListRet.getData().stream().collect(Collectors.toMap(MemberBasicRes::getId, e -> e));
            }
            Map<Integer, BasketProductBO> productInfoMap = null;
            if (EvaluateTypeEnum.Recycle.getCode().equals(type)) {
                productInfoMap = recoverBasketService.getRecoverProduct(subIds);
            } else if (EvaluateTypeEnum.Shouhou.getCode().equals(type)) {
                productInfoMap = shouhouService.getShouhouMapByShouhouIds(subIds);
            }
            for (EvaluateInfoRes evaluateInfoRes : list) {
                if (productInfoMap != null && productInfoMap.containsKey(evaluateInfoRes.getSubId())) {
                    BasketProductBO basketProductBO = productInfoMap.get(evaluateInfoRes.getSubId());
                    BeanUtils.copyProperties(basketProductBO, evaluateInfoRes);
                    if (EvaluateTypeEnum.Recycle.getCode().equals(type)) {
                        evaluateInfoRes.setTradeWayName(EnumUtil.getMessageByCode(RecoverTradeWayEnum.class,
                                evaluateInfoRes.getTradeWay()));
                    }
                }
                if (userMap != null && userMap.containsKey(evaluateInfoRes.getUserId())) {
                    MemberBasicRes memberBasicRes = userMap.get(evaluateInfoRes.getUserId());
                    evaluateInfoRes.setUserName(memberBasicRes.getUserName());
                    String photo = StringUtils.isNotEmpty(memberBasicRes.getWxHeadImg()) ?
                            memberBasicRes.getWxHeadImg() : memberBasicRes.getHeadImg();
                    evaluateInfoRes.setPhoto(photo);
                }
            }
        }
    }

    @Override
    public IPage<Evaluate> getPage(IPage<Evaluate> page, Wrapper<Evaluate> queryWrapper) {
        return this.page(page, queryWrapper);
    }

    /**
     * 服务评价统计接口服务
     *
     * @see .net:oa2019:oa999Services:userServices:EvaluateResult
     */
    @Override
    public member.EvaluateResult evaluateResult(member.EvaluateConn conn, int xtenant) {
        member.EvaluateResult result = new member.EvaluateResult();
        member.ResultModel_ info = new member.ResultModel_();
        EvaluateRankResult.EvaluateOne item = new EvaluateRankResult.EvaluateOne();

//        var c39User.get() = new oa999BLL.Ch999UserManage().getUser().FirstOrDefault(t => t.Ch999ID == conn.getCh999Id());
        Optional<Ch999User> c39User = this.ch999UserService.getCh999Users().stream().filter(ch999User -> ch999User.getCh999Id().equals(conn.getCh999Id())).findFirst();
        if (!c39User.isPresent()) {
            result.getInfo().setResult("查无相关员工信息");
            return setInfoAndItemReturn(result, item, info);
        }

        info.setStats(1);
        item.setCh999Id(c39User.get().getCh999Id());
        item.setCh999Name(c39User.get().getCh999Name());

        item.setHeadImg(c39User.get().getSphoto());
        item.setUType(conn.getJob().name());

        item.setTotalDays((int) Duration.between(c39User.get().getIndate(), LocalDateTime.now()).get(ChronoUnit.DAYS));
        //TODO 原服务有缓存这里直接查表了
        item.setSellProductCount(this.evaluateMapper.getSellProductCount(conn.getCh999Id(), conn.getJob().getValue()));
        item.setToUserSellCount(this.evaluateScoreMapper.getToUserSellCount(conn.getCh999Id(), conn.getJob().getValue(), conn.getUserId()));
        AreaInfo uarea = areaInfoService.getAreaInfoById(c39User.get().getArea1id());
        if (!Objects.equals(null, uarea)) {
            Integer departId = uarea.getDepartId();
            R<List<DepartInfoVO>> listR = departInfoClient.listAll();
            List<DepartInfoVO> departInfoVOList = listR.getData();
            Map<Integer, DepartInfoVO> departInfoVOMap = departInfoVOList.stream().collect(Collectors.toMap(e -> e.getId(), e -> e, (v1, v2) -> v1));
            DepartInfoVO departInfoVO = departInfoVOMap.get(departId);
            String departPath = departInfoVO.getDepartPath();
            String[] splitDepartIds = departPath.split(",");
            Integer tarDepartId = null;
            for (int i = 0; i < splitDepartIds.length; i++){
                DepartInfoVO tarDepartInfoVO = departInfoVOMap.get(splitDepartIds[i]);
                Integer dataType = tarDepartInfoVO.getDataType();
                if (dataType.equals(DataTypeEnum.L_COMMUNITY)){
                    tarDepartId = tarDepartInfoVO.getId();
                    break;
                }
            }

            item.setAreaName(uarea.getProvinceName() + " " + uarea.getCityName() + " " + uarea.getAreaName());
            String areaKey = "EvaluateAreaSort_" + c39User.get().getArea1id() + "_" + conn.getJob().getValue();
            String areaCodeKey = "EvaluateAreaCodeSort_" + tarDepartId + "_" + conn.getJob().getValue();
            String allKey = "EvaluateAllSort_" + conn.getJob().getValue() + "_" + xtenant;
            NumberFormat nf = NumberFormat.getNumberInstance();
            nf.setMaximumFractionDigits(2);
            //本店排名
            item.setAreaSort(getDecrementIndexBySorted(areaKey, conn.getCh999Id()));
            //本店排名百分比
            Long areaSort = redisTemplate.opsForZSet().count(areaKey, Double.MIN_VALUE, Double.MAX_VALUE);
            if (areaSort > 0 && item.getAreaSort() != -1) {
                item.setAreaSortPrecent(nf.format((((Long) (areaSort - item.getAreaSort() + 1L)).doubleValue() / areaSort.doubleValue())));
            }
            //全区排名
            item.setDistrictSort(getDecrementIndexBySorted(areaCodeKey, conn.getCh999Id()));
            //全区排名百分比
            Long districtSort = redisTemplate.opsForZSet().count(areaCodeKey, Double.MIN_VALUE, Double.MAX_VALUE);
            if (districtSort > 0 && item.getDistrictSort() != -1) {
                item.setDistrictSortPercent(nf.format((((Long) (districtSort - item.getDistrictSort() + 1)).doubleValue() / districtSort.doubleValue())));
            }
            //全国排名
            item.setCountrySort(getDecrementIndexBySorted(allKey, conn.getCh999Id()));
            //全国排名百分比
            Long countrySort = redisTemplate.opsForZSet().count(allKey, Double.MIN_VALUE, Double.MAX_VALUE);
            if (countrySort > 0 && item.getCountrySort() != -1) {
                item.setCountrySortPercent(nf.format((((Long) (countrySort - item.getCountrySort() + 1)).doubleValue() / countrySort.doubleValue())));
            }
            //计算平均分
            item.setAvgScore(Objects.requireNonNull(redisTemplate.opsForZSet().score(areaKey, conn.getCh999Id())).toString());
        }
        item.setEPriceList(getEvaluatePriceUserList(conn.getCh999Id(), 6));

        //打赏次数
        item.setTotalReword(totalReword(conn.getCh999Id()));

        //获取印象标签
        item.setTags(getCh999UserTags(conn).stream().map(
                (Function<Map<String, Integer>, HashMap<String, Object>>) HashMap::new
        ).collect(Collectors.toList()));

        return setInfoAndItemReturn(result, item, info);
    }

    private member.EvaluateResult setInfoAndItemReturn(member.EvaluateResult evaluateResult, EvaluateRankResult.EvaluateOne evaluateOne, member.ResultModel_ resultModel_) {
        evaluateResult.setInfo(resultModel_);
        evaluateResult.setItem(evaluateOne);
        return evaluateResult;
    }

    private Long getDecrementIndexBySorted(String key, Object hashId) {
        long rank = redisTemplate.opsForZSet().reverseRank(key, hashId.toString());
        return rank < 0 ? rank : rank + 1;
    }

    /// <summary>
    /// 获取打赏列表(缓存10分钟)
    /// </summary>
    /// <param name="ch999_id"></param>
    /// <returns></returns>
    public List<EvaluateRankResult.Evaluateuprices> getEvaluatePriceUserList(int ch999_id, int top) {
        List<EvaluateRankResult.Evaluateuprices> info = new ArrayList<>();
        String cacheKey = "EvaluatePriceUserList_" + ch999_id + "_" + "top";
        if (redisTemplate.hasKey(cacheKey)) {
            String jsonStr = Objects.requireNonNull(redisTemplate.opsForValue().get(cacheKey)).toString();
            if (StringUtils.isNotEmpty(jsonStr)) {
                return JSON.parseArray(jsonStr, EvaluateRankResult.Evaluateuprices.class);
            }
            return info;
        }
        List<Map<String, Object>> scores = this.evaluateScoreMapper.getEvaluatePriceUserList(
                top == 0 ? "" : " top " + top + " "
                , ch999_id
        );

        if (null != scores && scores.size() > 0) {
            info = scores.stream().map(evaluateScore -> {
                EvaluateRankResult.Evaluateuprices evaluateuprices = new EvaluateRankResult.Evaluateuprices();
                evaluateuprices.setUserId((int) evaluateScore.get("userid"));
                evaluateuprices.setPrices(BigDecimal.valueOf((Double) evaluateScore.get("uprices")));
                evaluateuprices.setDate(LocalDate.parse(evaluateScore.get("udate").toString()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                evaluateuprices.setUserName(evaluateScore.get("UserName").toString());
                return evaluateuprices;
            }).collect(Collectors.toList());

        }
        redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(info), 10, TimeUnit.MINUTES);
        return info;
    }

    //打赏总次数(缓存60分钟)
    public int totalReword(int ch999_id) {
        String cacheKey = "oa:order:EvaluateScoreCountKey_" + ch999_id;
        if (redisTemplate.hasKey(cacheKey)) {
            return Integer.parseInt(Objects.requireNonNull(redisTemplate.opsForValue().get(cacheKey)).toString());
        }
        int counts = this.evaluateScoreMapper.totalReword(ch999_id);
        redisTemplate.opsForValue().set(cacheKey, counts, 1, TimeUnit.HOURS);
        return counts;
    }

    //获取员工的印象标签(缓存1天)
    private List<Map<String, Integer>> getCh999UserTags(member.EvaluateConn conn) {
        String key = "oa:order:EvaluateTagKey_" + conn.getCh999Id();
        if (redisTemplate.hasKey(key)) {
            return JSON.parseArray(Objects.requireNonNull(redisTemplate.opsForValue().get(key)).toString(), Map.class)
                    .stream().map(hashMap -> {
                        Map<String, Integer> rtn = new HashMap<>();
                        hashMap.keySet().forEach(o -> rtn.put(o.toString(), (Integer) hashMap.get(o)));
                        return rtn;
                    }).collect(Collectors.toList());

        }
        //这里.net有缓存
        List<Map<String, Integer>> ch999UserTags = new ArrayList<>();
        List<Map<String, Integer>> dbs =
                this.evaluateMapper.getCh999UserTags(conn.getUserId());
        if (!Objects.equals(null, dbs) && dbs.size() > 0) {
            ch999UserTags.addAll(dbs.stream().map(stringObjectMap -> {
                Map<String, Integer> map = new HashMap<>();
                String name;
                Integer num;
                try {
                    name = stringObjectMap.get("Name").toString();
                } catch (NullPointerException e) {
                    name = "";
                }
                try {
                    num = stringObjectMap.get("num");
                } catch (NumberFormatException | ClassCastException | NullPointerException e) {
                    num = 0;
                }
                map.put(name, num);
                return map;
            }).collect(Collectors.toList()));
        }
        return ch999UserTags;
    }

    @Override
    @DS("oanew")
    public ResultModelBO addEvaluateNew(Evaluate info, String type, boolean isapp, int sourceFrom, String version) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        log.info("订单评价,type:" + type + ",sourceFrom:" + sourceFrom + ",isapp:" + isapp + ",version:" + version + ",info:[" + JSONObject.toJSONString(info) + "]");
        String[] jobStarLevels = info.getJobStarLevel().split(",");
        ResultModelBO result = new ResultModelBO();
        /// 测试人员id
        List<Integer> testUserIds = new ArrayList<>();
        testUserIds.add(616209);
        testUserIds.add(4923339);
        testUserIds.add(4711578);
        testUserIds.add(4883597);
        testUserIds.add(5138919);
        testUserIds.add(2192235);
        testUserIds.add(4879253);
        testUserIds.add(4879259);
        testUserIds.add(4879267);
        testUserIds.add(4883590);
        testUserIds.add(4883592);
        testUserIds.add(4883593);
        testUserIds.add(4883596);
        testUserIds.add(5113097);
        testUserIds.add(4856453);
        try {
            synchronized (obj) {
                BbsxpUsers user = bbsxpUsersService.getById(info.getUserId());
                Integer specialType = user.getSpecialType();

                //积分赠送标识
                boolean JiFenFlag = (specialType & (int) EUserSpecialTypeEnum.E_USER_SPECIAL_TYPE_ENUM_1.getCode()) == 0;
                //良品及普通订单 不再给积分 积分 Java端已给分了
                if (JiFenFlag && Arrays.toString(new String[]{"V", "L"}).contains(type.toUpperCase())) {
                    JiFenFlag = false;
                }

                //不再有客评系数影响
                boolean evaluateRankFlag = (specialType & EUserSpecialTypeEnum.E_USER_SPECIAL_TYPE_ENUM_4.getCode()) == 0;
                Integer areaId = info.getAreaId();
                boolean isInvalid = false; //是否无效

                switch (type.toUpperCase()) {
                    case "V": {
                        Integer isExist = evaluateMapper.isExistV(info.getSubId());
                        //表示已过期
                        if (isExist > 0) {
                            isInvalid = true;
                            evaluateRankFlag = false;
                        }
                        Integer isExists = evaluateMapper.isExistsV(info.getSubId());
                        if (!testUserIds.toString().contains(info.getUserId() + "") && isExists > 0) {
                            result.setStats(0);
                            result.setResult("交易后30分钟才可以评价");
                            return result;
                        }
                        Integer exists = evaluateMapper.existsV(info.getSubId());
                        if (exists > 0) {
                            result.setStats(0);
                            result.setResult("内部员工不可评价");
                            return result;
                        }
                        break;
                    }
                    case "S": {
                        Integer isExist = evaluateMapper.isExistS(info.getSubId());
                        if (isExist > 0) {
                            isInvalid = true;
                            evaluateRankFlag = false;
                        }
                        Integer isExists = evaluateMapper.existsS(info.getSubId());
                        if (!testUserIds.toString().contains(info.getUserId() + "") && isExists > 0) {
                            result.setStats(0);
                            result.setResult("内部员工不可评价");
                            return result;
                        }
                        break;
                    }
                    case "L": {
                        Integer isExist = evaluateMapper.isExistL(info.getSubId());
                        //表示已过期
                        if (isExist > 0) {
                            isInvalid = true;
                            evaluateRankFlag = false;
                        }
                        Integer isExists = evaluateMapper.isExistsL(info.getSubId());
                        if (!testUserIds.toString().contains(info.getUserId() + "") && isExists > 0) {
                            result.setStats(0);
                            result.setResult("交易后30分钟才可以评价");
                            return result;
                        }
                        Integer exists = evaluateMapper.existsL(info.getSubId());
                        if (exists > 0) {
                            result.setStats(0);
                            result.setResult("内部员工不可评价");
                            return result;
                        }
                        break;
                    }
                    case "R": {
                        Integer isExist = evaluateMapper.isExistR(info.getSubId());
                        if (isExist > 0) {
                            isInvalid = true;
                            evaluateRankFlag = false;
                        }
                        Integer isExists = evaluateMapper.isExistsR(info.getSubId());
                        if (!testUserIds.toString().contains(info.getUserId() + "") && isExists > 0) {
                            result.setStats(0);
                            result.setResult("内部员工不可评价");
                            return result;
                        }
                        break;
                    }
                    case "KX":
                    case "KT": {
                        Integer isExist = evaluateMapper.isExistKX(info.getSubId());
                        //表示已过期
                        if (isExist > 0) {
                            isInvalid = true;
                            evaluateRankFlag = false;
                        }
                        Integer isExists = evaluateMapper.isExistsKX(info.getSubId());
                        if (!testUserIds.toString().contains(info.getUserId() + "") && isExists > 0) {
                            result.setStats(0);
                            result.setResult("内部员工不可评价");
                            return result;
                        }
                        break;
                    }
                    case "H": {
                        Integer isExist = evaluateMapper.isExistH(info.getSubId());
                        //表示已过期
                        if (isExist > 0) {
                            isInvalid = true;
                            evaluateRankFlag = false;
                        }
                        Integer isExists = evaluateMapper.isExistsH(info.getSubId());
                        if (!testUserIds.toString().contains(info.getUserId() + "") && isExists > 0) {
                            result.setStats(0);
                            result.setResult("交易后30分钟才可以评价");
                            return result;
                        }
                        Integer exists = evaluateMapper.existsH(info.getSubId());
                        if (exists > 0) {
                            result.setStats(0);
                            result.setResult("内部员工不可评价");
                            return result;
                        }
                        break;
                    }
                    case "Z1":
                    case "Z2": {
                        isInvalid = true;
                        evaluateRankFlag = false;

                        Integer exists = evaluateMapper.existsZ1(info.getUserId());
                        if (exists > 0) {
                            result.setStats(0);
                            result.setResult("内部员工不可评价");
                            return result;
                        }
                        break;
                    }
                    default:
                        break;
                }
                checkEvaluate(info);

                List<Integer> tagIds = tryParseEvaluateTagIds(info.getTagIdStr());
                List<Integer> recommendationWebsiteTagIds = tryParseEvaluateTagIds(info.getCommendedRemark());

                int evaluateId = 0;
                Evaluate evaluateData = evaluateMapper.getEvaluateData(info.getSubId(), info.getEvaluateType());
                if (evaluateData != null) {
                    evaluateId = evaluateData.getId();
                }
                if (evaluateId != 0) {
                    if ("2.0".equals(version)) {
                        if (evaluateData.getCommendedScore() == -1 && info.getCommendedScore() >= 0) {
                            List<EvaluateTag> recommendationWebsiteTags = getEvaluateTags(recommendationWebsiteTagIds);
                            // 插入分网站推荐
                            insertRecommendationWebsiteTagRecord(evaluateId, recommendationWebsiteTags);
                        }
                        //如果评价过，并且是2.0 可以修改评价内容 和 推荐指数内容 推荐指数
                        if (upEvaluateContentAndCommentedRemark(evaluateId, info.getContent(), info.getCommendedRemark(), info.getCommendedScore())) {
                            result.setStats(1);
                            result.setResult("提交评论成功!");
                            return result;
                        } else {
                            result.setStats(0);
                            result.setResult("提交评论失败，请稍后再试！!");
                            return result;
                        }
                    } else {
                        result.setStats(0);
                        result.setResult("已参与过评论!");
                        return result;
                    }


                }
                if (evaluateId == 0) {
                    List<EvaluateTag> tags = getEvaluateTags(tagIds);
                    List<EvaluateTag> recommendationWebsiteTags = getEvaluateTags(recommendationWebsiteTagIds);
                    Integer versionNum = null;
                    Integer id = insertEvaluateNew(info, tags, type, sourceFrom, isInvalid, versionNum);

                    if (id <= 0) {
                        log.error("客户评价信息保存失败");
                    }
                    HashMap<EvaluateJobEnum, Integer> userIds = getEvaluateTagRelateCh999IdsNew(info.getSubId(), EnumUtil.getEnumByCode(EvaluateTypeEnum.class, info.getEvaluateType()));

                    insertEvaluateTagRecord(id, tags, userIds);

                    insertEvaluateQuestionAnswer(id, info.getQuestionAnswer());
                    // 插入分网站推荐
                    insertRecommendationWebsiteTagRecord(id, recommendationWebsiteTags);

                    ArrayList<HashMap<Integer, EvaluateType>> scoreList = new ArrayList<>();

                    //如果评价类型是R【技术服务】，那么要转换成订单id
                    if ("R".equals(type.toUpperCase())) {
                        Msoft msoft = msoftService.getById(info.getSubId());
                        areaId = msoft.getAreaid();
                    }
                    for (String jobStarLevel : jobStarLevels) {
                        int jobId = 0;
                        int star = 0;
                        String[] keyValue = jobStarLevel.split(":");
                        if (keyValue.length == 2 && keyValue[0].matches("^[0-9]*$") && keyValue[1].matches("^[0-9]*$")) {
                            jobId = Integer.parseInt(keyValue[0]);
                            star = Integer.parseInt(keyValue[1]);
                            //其他服务 人员开始已经确定 无需再确定
                            HashMap<EvaluateJobEnum, Integer> others = new HashMap<>();
                            others.put(EnumUtil.getEnumByCode(EvaluateJobEnum.class, jobId), getEvaluateTagRelateCh999Id(info.getSubId(), EnumUtil.getEnumByCode(EvaluateJobEnum.class, jobId), type));
                            insertEvaluateScore(id, star, others, info, type, info.getSubId(), info.getAreaId(), evaluateRankFlag, info.getUserId());
                        } else {
                            result.setStats(0);
                            result.setResult("评分解析出错");
                            return result;
                        }
                    }
                    ////删除多余的评价
                    evaluateScoreMapper.removeEvaluateScore(id);
                    Integer obj = evaluateScoreMapper.getEvaluateScoreId(info.getSubId(), info.getEvaluateType());
                    if (obj != null) {
                        evaluateScoreMapper.updateEvaluateScore(info.getSubId(), info.getEvaluateType());
                    }

                    int[] arr = {EvaluateTypeEnum.Shouhou.getCode(),
                            EvaluateTypeEnum.Recycle.getCode(),
                            EvaluateTypeEnum.Electrombile.getCode(),
                            EvaluateTypeEnum.JiShuService.getCode(),
                            EvaluateTypeEnum.ZujiGet.getCode()};
                    boolean contains = Arrays.toString(arr).contains(info.getEvaluateType() + "");

                    String redisInfo = (String) redisTemplate.opsForValue().get("pcLogin_" + oaUserBO.getUserId());
                    LoginInfoBO loginInfoBO = new LoginInfoBO();
                    if (redisInfo != null && !redisInfo.isEmpty()) {
                        // 转JSON数据，装填数据
                        loginInfoBO = JSONObject.parseObject(redisInfo, LoginInfoBO.class);
                    }

                    if (JiFenFlag) {
                        if (contains) {
                            JiFen1 jiFen1 = new JiFen1();
                            jiFen1.setUserid(info.getUserId());
                            jiFen1.setSubId(info.getSubId());
                            jiFen1.setJifen(50);
                            jiFen1.setComment("恭喜！您完成了订单" + info.getSubId() + "的评价，获得50积分奖励。");
                            jiFen1.setInuser("系统");
                            jiFen1.setIsinput(1);
                            ((EvaluateService) AopContext.currentProxy()).jiFenManage1(jiFen1, loginInfoBO.getIp());
                        }
                    }
                    //管理层低评通知
                    if (scoreList.size() > 0) {
                        managementEvaluateNotifyPush(id);
                    }

                } else {
                    Evaluate evaluate = evaluateService.getById(evaluateId);
                    evaluate.setContent(evaluate.getContent() + " " + info.getContent());
                    evaluateService.updateById(evaluate);
                }
                result.setStats(1);
                result.setResult("添加成功");
            }
        } catch (Exception e) {
            result.setStats(1);
            result.setResult(e.getMessage());
            log.error(e.getMessage());
        }
        return result;
    }

    /**
     * 管理层低评通知
     *
     * @param evaluateId
     */
    private void managementEvaluateNotifyPush(Integer evaluateId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("act", "ManagementEvaluateNotifyPush");
        jsonObject.put("data", evaluateId);
        amqpTemplate.convertAndSend(RabbitMqConfig.QUEUE_TOPIC_OAASYNC, jsonObject.toString());
    }

    //    积分管理 内置事务
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String jiFenManage1(JiFen1 jifenM, String ip) {
        JiFen1 m = new JiFen1();
        m.setPrice(0.0);
        m.setUserid(jifenM.getUserid());
        m.setSubId(jifenM.getSubId());
        m.setJifen(jifenM.getJifen());
        m.setInuser(jifenM.getInuser());
        m.setComment(jifenM.getComment());
        m.setIsinput(jifenM.getIsinput());
        if (m.getComment().contains("租机")) {
            m.setPrice(jifenM.getPrice());
        }
        try {

            if (m.getJifen() == -1) {
                m.setJifen(getJifen(m.getPrice()));
            }
            if (m.getJifen() <= 0) {
                return "无操作积分";
            }
            if (StringUtils.isNotEmpty(m.getComment())) {
                if (m.getComment().length() > 200) {
                    m.setComment(m.getComment().substring(0, 200));
                }
            }
            boolean ischeckclass = false;
            if (StringUtils.isNotEmpty(m.getComment())) {
                if (m.getComment().length() > 200) {
                    m.setComment(m.getComment().substring(0, 200));
                }
            }

            if (m.getJifen() == -1) {
                m.setJifen(getJifen(m.getPrice()));
            }
            if (m.getJifen() <= 0) {
                return "无操作积分";
            }

            try {
                BbsxpUsers bbsxpUsers =
                        bbsxpUsersService.getInfoByUserId(new LambdaUpdateWrapper<BbsxpUsers>().eq(BbsxpUsers::getId,
                                m.getUserid()));
                if (bbsxpUsers != null) {
                    AreaInfo areaSubject = getAreaSubject(bbsxpUsers.getAreaid());
                    if (m.getIsinput() == 1) //消费增加积分成长值
                    {
                        bbsxpUsersMapper.updateBBSXPUsers(m.getJifen(), m.getPrice(), m.getUserid());
                        ischeckclass = true;
                        if (m.getComment().contains("签到")) {
                            redisTemplate.opsForHash().put("JifenSignIncrease", m.getUserid(), null);
                        }
                        if (areaSubject.getIsSend()) {
                            SendThread st = new SendThread();
                            ZnSendConnBo parmters = new ZnSendConnBo();
                            parmters.setKind(6);
                            parmters.setSmsnumber(m.getUserid() + "");
                            parmters.setTitle("积分到账");
                            parmters.setLink(StringUtils.isEmpty(jifenM.getLink()) ? "vipclub" : jifenM.getLink());
                            parmters.setContent("恭喜您！你有" + m.getJifen() + "积分已到账，点击查看积分变动详情。");
                            parmters.setExtraData(m.getComment());
                            st.setParmters(parmters);
                            String url = inwcfSource.getBasicUrl() + wwwUrlSource.getZnMsg();
                            st.setUrl(url);
                            Thread t = new Thread(st);
                            t.start();
                        }
                    } else if (m.getIsinput() == 0) //积分兑换
                    {
                        long points = Long.parseLong(String.valueOf(bbsxpUsers.getPoints()));
                        if (m.getJifen() > points) {
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return "操作失败，积分可用额度已不够扣减！";

                        } else {
                            bbsxpUsers.setPoints(bbsxpUsers.getPoints() - m.getJifen());
                            bbsxpUsersService.update(bbsxpUsers, new LambdaUpdateWrapper<BbsxpUsers>().eq(BbsxpUsers::getId,
                                    m.getUserid()));
                        }
                    } else if (m.getIsinput() == -1) //换货、换购等
                    {
                        bbsxpUsersMapper.updateBBSXPUser(m.getJifen(), m.getPrice(), m.getUserid());
                    } else if (m.getIsinput() == 2) //删单 积分返还
                    {
                        bbsxpUsers.setPoints(bbsxpUsers.getPoints() + m.getJifen());
                        bbsxpUsers.setTotalpoint(bbsxpUsers.getTotalpoint() + m.getJifen());
                        bbsxpUsersService.update(bbsxpUsers, new LambdaUpdateWrapper<BbsxpUsers>().eq(BbsxpUsers::getId,
                                m.getUserid()));
                    }
                    Jifen jifen = new Jifen();
                    jifen.setPoints(m.getJifen());
                    jifen.setUserid(m.getUserid());
                    jifen.setSubNumber(m.getSubId() + "");
                    jifen.setJifendate(LocalDateTime.now());
                    jifen.setIsinput(m.getIsinput());
                    jifen.setInuser(m.getInuser());
                    jifen.setComment(m.getComment());
                    jifen.setKinds(1);
                    jifen.setType(m.getType());
                    jifenService.save(jifen);
                    if (ischeckclass && m.getUserid() != 6234) {
                        int newClass = 0;
                        String newClassName = "";
                        double newjifen = m.getPrice() + bbsxpUsers.getChengzhangzhi();
                        newClass = getNewUserClass(newjifen);
                        if (bbsxpUsers.getUserclass() != newClass && newClass > bbsxpUsers.getUserclass() && m.getUserid() != 6234) {
                            bbsxpUsers.setUserclass(newClass);
                            bbsxpUsersService.update(bbsxpUsers, new LambdaUpdateWrapper<BbsxpUsers>().eq(BbsxpUsers::getId,
                                    m.getUserid()));

                            newClassName = CommonUtil.getUserType(newClass);
                            if (areaSubject.getIsSend()) {
                                String msg = "尊敬的" + areaSubject.getPrintName() + "会员，您的会员成长值已达" + newjifen + ",系统已为您升级为" + newClassName + ",点击查看" + newClassName + "特权9ji.cn/o33P2";
                                String url = smsProperties.getUrl();
                                CommonUtil.sendSms(bbsxpUsers.getMobile(), msg, "9", "系统", url);
                                SendThread st = new SendThread();
                                ZnSendConnBo parmters = new ZnSendConnBo();
                                parmters.setKind(15);
                                parmters.setSmsnumber(m.getUserid() + "");
                                parmters.setTitle("会员等级变更");
                                parmters.setLink(areaSubject.getMUrl() + "/vip/privilege?type=" + newClass);
                                parmters.setContent("恭喜您！您已累计获得" + newjifen + "成长值，会员等级升级为" + newClassName + "会员，已解锁更多专属权益。");
                                parmters.setExtraData("");
                                st.setParmters(parmters);
                                String url1 = inwcfSource.getBasicUrl() + wwwUrlSource.getZnMsg();
                                st.setUrl(url1);
                                Thread t = new Thread(st);
                                t.start();
                            }
                            List<AddModelBO> modles = new ArrayList<>();
                            LocalDateTime now = LocalDateTime.now();
                            String d1 = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                            LocalDateTime localDateTime = LocalDateTime.of(now.getYear(), now.getMonth(), 1, 0, 0);
                            LocalDateTime localDateTime1 = localDateTime.plusMonths(1).plusDays(-1);
                            String d2 = localDateTime1.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                            if (CommonUtil.startNewUserClass()) {

                            } else {
                                List<AddModelBO> numberList = getMemberCard(EnumUtil.getEnumByCode(EUserClassNewEnum.class, newClass));
                                if (numberList.size() > 0) {
                                    for (AddModelBO addModelBO : numberList) {
                                        addModelBO.setGName(addModelBO.getGName() + m.getUserid());
                                        addModelBO.setLimit1("3");
                                        addModelBO.setLimit2("0");
                                        addModelBO.setCount(1);
                                        addModelBO.setUserid(m.getUserid());
                                        addModelBO.setStartTime(d1);
                                        addModelBO.setEndTime(d2);
                                        modles.add(addModelBO);
                                    }
                                }
                            }
                            if (modles.size() > 0) {
                                getRunNumber(modles);
                            }
                        }

                    }
                } else {
                    throw new Exception("用户查找失败！");
                }
                return "1";
            } catch (Exception e) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                String desc = "积分接口事务异常：(" + e.getMessage() + "),ip:" + ip + "data(" + JSON.toJSONString(m) + ")";
                messagePushCloud.pushQyWeiXinMessage("1109", "0", "系统通知", desc, "", "");
                return e.getMessage();
            }
        } catch (Exception e) {
            String desc = "积分接口事务异常：(" + e.getMessage() + "),ip:" + ip + "data(" + JSON.toJSONString(m) + ")";
            messagePushCloud.pushQyWeiXinMessage("1109", "0", "系统通知", desc, "", "");
            return "积分处理异常";
        }
    }

    private CommonResult getRunNumber(List<AddModelBO> models) {
        List<String> codes = new ArrayList<>();
        CommonResult json = new CommonResult();
        String username = "系统";
        json.setStatus(0);
        if (models.size() > 0) {
            try {
                CommonResult result = getRunNumber(models, username, codes);
                BeanUtils.copyProperties(json, result);
                if (result.getStatus() != 1) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                }
            } catch (Exception e) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                json.setMsg(e.getMessage());
                return json;
            }
        } else {
            json.setMsg("数据为空");
        }

        List<AddModelBO> collect = models.stream().filter(t -> t.getUserid() > 0).collect(Collectors.toList());
        if (json.getStatus() == 1 && collect.size() > 0) {
            BbsxpUsers bbsxpUsers =
                    bbsxpUsersService.getInfoByUserId(new LambdaUpdateWrapper<BbsxpUsers>().eq(BbsxpUsers::getId,
                            collect.get(0).getUserid()));
            if (bbsxpUsers.getXtenant().equals(EXtenantEnum.TYPE1.getCode())) {
                R<WeixinRes> weixinR = memberClient.getMyWx(collect.get(0).getUserid());
                if (weixinR.getCode() == ResultCode.SUCCESS && weixinR.getData() != null) {
                    String openid = weixinR.getData().getOpenid();
                    if (StringUtils.isNotEmpty(openid)) {
                        String date = LocalDateTime.now().format(PROCESS_TIME_FORMAT);
                        arrivalAccountNotice(openid, CommonUtil.getUrlByXtenant(bbsxpUsers.getXtenant(), 3) + "/member/coupon", "优惠到账通知",
                                "亲，恭喜您获得" + models.stream().collect(Collectors.summingDouble(AddModelBO::getTotal)) + "元九机网优惠券礼包", date,
                                "礼包包含" + models.size() + "张消费优惠券，赶快去使用吧",
                                "九机网支持线上下单，1小时送货上门哦");
                    }
                }
            }

        }
        return json;
    }

    private String arrivalAccountNotice(String openid, String url, String title, String price, String time, String detail, String remark) {
        String color = "#ff0000";
        String tempID = "3GNB40NYrY-3yA9hLgniYTuVIzO2VPdh9mk4thKThC0";
        String result = "";
        String topcolor = "#666";
        if (StringUtils.isNotEmpty(tempID)) {
            JSONObject request = new JSONObject();
            request.put("first", new ColorBO(title, topcolor));
            request.put("keyword1", new ColorBO(price, color));
            request.put("keyword2", new ColorBO(time, topcolor));
            request.put("keyword3", new ColorBO(detail, color));
            request.put("remark", new ColorBO(remark, topcolor));
            result = sendTemplateMsg(openid, tempID, url, request, topcolor);
        }
        return result;
    }

    private CommonResult getRunNumber(List<AddModelBO> models, String username, List<String> codes) {
        List<Map<String,Object>> cardList = new ArrayList<>();
        CommonResult json = new CommonResult();
        int cont = 0;
        for (AddModelBO model :
                models) {
            if (model.getUserid() != null && model.getUserid() > 0) {
                BbsxpUsers user =
                        bbsxpUsersService.getInfoByUserId(new LambdaUpdateWrapper<BbsxpUsers>().eq(BbsxpUsers::getId,
                                model.getUserid()));
                if (user.getSpecialType().equals(EUserSpecialTypeEnum.E_USER_SPECIAL_TYPE_ENUM_8.getCode())) {
                    json.setMsg("黄牛用户，生成优惠码失败！");
                    return json;
                }
            }
            if (model.getCount() == 0 || StringUtils.isEmpty(model.getGName()) || StringUtils.isEmpty(model.getStartTime()) ||
                    StringUtils.isEmpty(model.getEndTime()) || StringUtils.isEmpty(model.getLimit1())) {
                json.setMsg("参数不足");
                return json;
            }
            if (StringUtils.isEmpty(model.getLimit2())) {
                model.setLimit2("0");
            }
            if (StringUtils.isEmpty(model.getLimitPrice())) {
                model.setLimitPrice("0");
            }


            if (StringUtils.isEmpty(model.getLimitType())) {
                model.setLimitIds("");
            }
            if (StringUtils.isNotEmpty(model.getLimitIds())) {
                model.setLimitIds("");
                if (model.getLimitIds().contains("，")) {
                    json.setMsg("限制类别中包含中文逗号，请使用英文逗号分隔！");
                    return json;
                }
                if (model.getLimitIds().split(",").length == 0) {
                    json.setMsg("限制类别错误！");
                    return json;
                }
                if (StringUtils.isEmpty(model.getLimitType())) {
                    json.setMsg("请选择限制类别");
                    return json;
                }
                List<Integer> oldids = Arrays.stream(model.getLimitIds().split(",")).map(x -> Integer.parseInt(x)).collect(Collectors.toList());
                List<Integer> newids = new ArrayList<>();
                switch (model.getLimitType()) {
                    case "1":
                        newids = categoryMapper.getCategoryd(oldids);
                        break;
                    case "2":
                        newids = brandMapper.getBrandIds(oldids);
                        break;
                    case "3":
                        newids = productMapper.getProducId(oldids);
                        break;
                    case "4":
                        newids = productMapper.getProductpriceId(oldids);
                        break;
                    default:
                        throw new IllegalStateException("Unexpected value: " + model.getLimitType());
                }
                if (newids.size() > 0) {
                    List<String> collect = newids.stream().map(x -> x + "").collect(Collectors.toList());
                    List<String> collect1 = oldids.stream().map(x -> x + "").collect(Collectors.toList());
                    for (String s :
                            collect1) {
                        List<String> other = collect.stream().filter(x -> !x.contains(s)).collect(Collectors.toList());
                        if (other.size() > 0) {
                            json.setMsg(EnumUtil.getMessageByCode(EnumberCardLimitTypeEnum.class, Integer.parseInt(model.getLimitType())) + "对应ID无效：" +
                                    JSON.toJSONString(json));

                            return json;
                        } else {
                            json.setMsg(EnumUtil.getMessageByCode(EnumberCardLimitTypeEnum.class, Integer.parseInt(model.getLimitType())) + "对应ID无效：");
                            return json;
                        }
                    }
                }
                if (StringUtils.isNotEmpty(model.getAreas())) {
                    if (model.getAreas().contains("，")) {
                        json.setMsg("地区中包含中文逗号，请使用英文逗号分隔！");
                        return json;
                    }
                }
                String areaIds = tryConvertToAreaIdStr(model.getAreas());
                HashMap<String, Object> one = new HashMap<>();
                List<String> cardids = new ArrayList<>(); //所生成的优惠码
                for (int i = 0; i < model.getCount(); i++) {
                    NumberCard numberCard = new NumberCard();
                    BeanUtils.copyProperties(model, numberCard);
                    numberCard.setInput(username);
                    boolean save = numberCardService.save(numberCard);
                    if (save) {
                        String code = JSON.toJSONString(numberCard);
                        cardids.add(code);
                        codes.add(code);
                    }
                }
                if (StringUtils.isNotEmpty(model.getType())) {
                    one.put(model.getGName(), cardids);
                    cardList.add(one);
                }
                cont++;
            }
        }
        json.setStatus(1);
        json.setResult(cardList);
        return json;

    }

    public String tryConvertToAreaIdStr(String areaStr) {
        if (StringUtils.isEmpty(areaStr)) {
            return "";
        }
        String[] areas = areaStr.split(",");
        List<AreaInfoModel> allAreas = touSuMapper.loadAllAreaInfo();
        List<String> collect = allAreas.stream().map(info -> info.getArea()).collect(Collectors.toList());
        if (areas.length > 0) {
            for (String area : areas
            ) {
                List<String> collect1 = collect.stream().filter(x -> !x.contains(area)).collect(Collectors.toList());
                if (collect1 == null) {
                    log.error("地区编码[{0}]不是有效编码" + JSON.toJSONString(collect1));
                }
            }
        }
        return collect.toString();
    }

    public String sendTemplateMsg(String openid, String templateid, String url, JSONObject data, String topcolor) {
        if (StringUtils.isEmpty(topcolor)) {
            topcolor = "#0048a3";
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("touser", openid);
        map.put("template_id", templateid);
        map.put("url", url);
        map.put("topcolor", topcolor);
        map.put("data", data);
        String accessToken = smsService.getAccessToken();
        String jsonStr = JSONObject.toJSONString(map);
        String sendMsgUrl = String.format(UrlConstant.SEND_MSG, accessToken);
        String result = HttpUtil.post(sendMsgUrl, jsonStr);
        log.info("客评发送优惠券,sendMsgUrl:{},result:{}", sendMsgUrl, result);
        return result;
    }

    /**
     * 会员优惠码配置
     *
     * @param uClass
     * @return
     */
    private List<AddModelBO> getMemberCard(EUserClassNewEnum uClass) {
        int[] WeixiuCodeLimitIds = new int[]{24, 25, 26, 27, 28, 29, 30, 31, 40, 53, 54,
                68, 69, 70, 159, 311, 393, 410, 463, 476, 481, 482, 522};
        List<AddModelBO> list = new ArrayList<>();
        AddModelBO e = new AddModelBO();
        if (uClass == EUserClassNewEnum.EUserClassNew_2) {
            e.setGName("白银会员每月优惠码");
            e.setTotal(5.0);
            e.setLimitPrice("50");
            e.setCh999Id(-1);
            list.add(e);
            e.setGName("营销备注：该优惠码仅限九机网自主维修使用，不能与九机服务叠加");
            e.setTotal(30.0);
            e.setLimitPrice("100");
            e.setLimitType("1");
            e.setCh999Id(-1);
            e.setLimitIds(Arrays.toString(WeixiuCodeLimitIds));
            list.add(e);
        } else if (uClass == EUserClassNewEnum.EUserClassNew_3) {
            e.setGName("黄金会员每月优惠码");
            e.setTotal(5.0);
            e.setLimitPrice("50");
            e.setCh999Id(-1);
            list.add(e);
            e.setGName("黄金会员每月优惠码");
            e.setTotal(30.0);
            e.setLimitPrice("500");
            e.setCh999Id(-1);
            list.add(e);
            list.add(e);
            e.setGName("黄金会员每月优惠码");
            e.setTotal(30.0);
            e.setLimitPrice("500");
            e.setCh999Id(-1);
            list.add(e);
            e.setGName("营销备注：该优惠码仅限九机网自主维修使用，不能与九机服务叠加");
            e.setTotal(30.0);
            e.setLimitPrice("100");
            e.setLimitType("1");
            e.setCh999Id(-1);
            e.setLimitIds(Arrays.toString(WeixiuCodeLimitIds));
            list.add(e);

            String time1 = "2020-09-01 00:00:00";
            DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime localDateTime = LocalDateTime.parse(time1, dtf2);

            if (localDateTime.isBefore(LocalDateTime.now())) {
                e.setGName("黄金会员每月回收加价码");
                e.setTotal(60.0);
                e.setLimitPrice("200");
                e.setCh999Id(-1);
                e.setLimintClint("4");
                e.setLimit1("1");
                e.setLimit2("0");
                list.add(e);
            } else {
                e.setGName("黄金会员每月回收加价码");
                e.setTotal(30.0);
                e.setLimitPrice("500");
                e.setCh999Id(-1);
                e.setLimintClint("4");
                e.setLimit1("1");
                e.setLimit2("0");
                list.add(e);
            }
        } else if (uClass == EUserClassNewEnum.EUserClassNew_5) {

            e.setGName("钻石会员每月优惠码");
            e.setTotal(5.0);
            e.setLimitPrice("50");
            e.setCh999Id(-1);
            list.add(e);
            e.setGName("钻石会员每月优惠码");
            e.setTotal(10.0);
            e.setLimitPrice("100");
            e.setCh999Id(-1);
            list.add(e);
            e.setGName("钻石会员每月优惠码");
            e.setTotal(50.0);
            e.setLimitPrice("1000");
            e.setCh999Id(-1);
            list.add(e);
            e.setGName("钻石会员每月优惠码");
            e.setTotal(50.0);
            e.setLimitPrice("1000");
            e.setCh999Id(-1);
            list.add(e);
            e.setGName("钻石会员每月优惠码");
            e.setTotal(30.0);
            e.setLimitPrice("500");
            e.setCh999Id(-1);
            list.add(e);
            e.setGName("钻石会员每月优惠码");
            e.setTotal(30.0);
            e.setLimitPrice("500");
            e.setCh999Id(-1);
            list.add(e);
            e.setGName("钻石会员月供维修优惠码");
            e.setTotal(50.0);
            e.setLimitPrice("200");
            e.setLimitType("1");
            e.setCh999Id(-1);
            e.setLimitIds(Arrays.toString(WeixiuCodeLimitIds));
            list.add(e);
            e.setGName("钻石会员月供维修优惠码");
            e.setTotal(30.0);
            e.setLimitPrice("100");
            e.setLimitType("1");
            e.setCh999Id(-1);
            e.setLimitIds(Arrays.toString(WeixiuCodeLimitIds));
            list.add(e);

            String time1 = "2020-09-01 00:00:00";
            DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime localDateTime = LocalDateTime.parse(time1, dtf2);

            if (localDateTime.isBefore(LocalDateTime.now())) {
                e.setGName("钻石会员每月回收加价码");
                e.setTotal(80.0);
                e.setLimitPrice("1000");
                e.setCh999Id(-1);
                e.setLimintClint("4");
                e.setLimit1("1");
                e.setLimit2("0");
                list.add(e);
            } else {
                e.setGName("钻石会员每月回收加价码");
                e.setTotal(50.0);
                e.setLimitPrice("500");
                e.setCh999Id(-1);
                e.setLimintClint("4");
                e.setLimit1("1");
                e.setLimit2("0");
                list.add(e);

            }
        } else if (uClass == EUserClassNewEnum.EUserClassNew_6) {
            e.setGName("双钻会员每月优惠码");
            e.setTotal(5.0);
            e.setLimitPrice("50");
            e.setCh999Id(-1);
            list.add(e);
            e.setGName("双钻会员每月优惠码");
            e.setTotal(10.0);
            e.setLimitPrice("100");
            e.setCh999Id(-1);
            list.add(e);
            e.setGName("双钻会员每月优惠码");
            e.setTotal(60.0);
            e.setLimitPrice("1000");
            e.setCh999Id(-1);
            list.add(e);
            e.setGName("双钻会员每月优惠码");
            e.setTotal(60.0);
            e.setLimitPrice("1000");
            e.setCh999Id(-1);
            list.add(e);
            e.setGName("双钻会员每月优惠码");
            e.setTotal(50.0);
            e.setLimitPrice("1000");
            e.setCh999Id(-1);
            list.add(e);
            e.setGName("双钻会员每月优惠码");
            e.setTotal(50.0);
            e.setLimitPrice("1000");
            e.setCh999Id(-1);
            list.add(e);
            e.setGName("双钻会员每月优惠码");
            e.setTotal(30.0);
            e.setLimitPrice("500");
            e.setCh999Id(-1);
            list.add(e);
            e.setGName("双钻会员每月优惠码");
            e.setTotal(30.0);
            e.setLimitPrice("500");
            e.setCh999Id(-1);
            list.add(e);
            e.setGName("双钻会员月供维修优惠码");
            e.setTotal(50.0);
            e.setLimitPrice("200");
            e.setLimitType("1");
            e.setCh999Id(-1);
            e.setLimitIds(Arrays.toString(WeixiuCodeLimitIds));
            list.add(e);
            e.setGName("双钻会员月供维修优惠码");
            e.setTotal(30.0);
            e.setLimitPrice("100");
            e.setLimitType("1");
            e.setCh999Id(-1);
            e.setLimitIds(Arrays.toString(WeixiuCodeLimitIds));
            list.add(e);

            String time1 = "2020-09-01 00:00:00";
            DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime localDateTime = LocalDateTime.parse(time1, dtf2);

            if (localDateTime.isBefore(LocalDateTime.now())) {
                e.setGName("双钻会员每月回收加价码");
                e.setTotal(100.0);
                e.setLimitPrice("1500");
                e.setCh999Id(-1);
                e.setLimintClint("4");
                e.setLimit1("1");
                e.setLimit2("0");
                list.add(e);
            } else {
                e.setGName("双钻会员每月回收加价码");
                e.setTotal(60.0);
                e.setLimitPrice("500");
                e.setCh999Id(-1);
                e.setLimintClint("4");
                e.setLimit1("1");
                e.setLimit2("0");
                list.add(e);

            }
        }
        return list;
    }

    /**
     * 获取用户新会员级别
     *
     * @param newjifen
     * @return
     */
    private int getNewUserClass(double newjifen) {
        int newClass;
        if (CommonUtil.startNewUserClass()) {
            if (newjifen >= 50000) {
                newClass = EUserClassNewEnum.EUserClassNew_4.getCode();
            } else if (newjifen >= 20000) {
                newClass = EUserClassNewEnum.EUserClassNew_3.getCode();
            } else if (newjifen >= 5000) {
                newClass = EUserClassNewEnum.EUserClassNew_2.getCode();
            } else {
                newClass = EUserClassNewEnum.EUserClassNew_1.getCode();
            }
        } else {
            if (newjifen >= 100000) {
                newClass = 6;
            } else if (newjifen >= 50000) {
                newClass = 5;
            } else if (newjifen >= 20000) {
                newClass = 3;
            } else if (newjifen >= 5000) {
                newClass = 2;
            } else if (newjifen >= 1) {
                newClass = 1;
            } else {
                newClass = 0;
            }
        }

        return newClass;
    }

    private Integer getJifen(double price) {
        if (price < 10) {
            return 0;
        } else if (price < 500) {
            return (int) Math.round(0.1 * price);
        } else if (price < 1000) {
            return (int) Math.round(0.15 * price);
        } else if (price < 4000) {
            return (int) Math.round(0.2 * price);
        } else {
            return 800;
        }
    }

    private AreaInfo getAreaSubject(Integer nowArea) {
        AreaInfo areaInfo = new AreaInfo();
        if (!Objects.equals(null, nowArea) && nowArea != 0) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(nowArea);
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                areaInfo = areaInfoR.getData();
            }
        } else {
            areaInfo.setId(nowArea);
            areaInfo.setIsSend(true);
            areaInfo.setPrintName("九机网");
            areaInfo.setXtenant(0);
            areaInfo.setAuthorizeId(1);
            areaInfo.setLogo(moaUrlSource.getBasicUrl() + "/static/24,37835b86a5e75b");
            areaInfo.setSmsChannel(JiujiSmsChannelEnum.JIUJI_SMS_CHANNEL_JIUJI.getCode().toString());
            areaInfo.setSmsChannelMarketing(JiujiSmsChannelEnum.JIUJI_SMS_CHANNEL_JIUJI_MARKETING.getCode().toString());
            areaInfo.setMUrl(JiujiJumpUrlEnum.JIUJI_JUMP_URL_JIUJI_M_URL.getCode());
            areaInfo.setWebUrl(JiujiJumpUrlEnum.JIUJI_JUMP_URL_JIUJI_WEB_URL.getCode());
            areaInfo.setHsUrl(JiujiJumpUrlEnum.JIUJI_JUMP_URL_JIUJI_HS_URL.getCode());
        }
        if (areaInfo.getPrintName().equals("华为授权")) {
            areaInfo.setSmsChannel("27");
        }
        return areaInfo;
    }

    private void insertEvaluateScore(Integer evaluateId, int starLevel, HashMap<EvaluateJobEnum, Integer> userIds, Evaluate info, String type, Integer subId, Integer areaId, boolean evaluateRankFlag, Integer customId) {
        insertEvaluateScore(evaluateId, starLevel, userIds, info, type,
                subId, areaId, evaluateRankFlag, customId, null);
    }

    private void insertEvaluateScore(Integer evaluateId, int starLevel, HashMap<EvaluateJobEnum, Integer> userIds, Evaluate info, String type, Integer subId, Integer areaId,
                                     boolean evaluateRankFlag, Integer customId, Integer versionNum) {
        for (Map.Entry<EvaluateJobEnum, Integer> entry : userIds.entrySet()) {
            insertEvaluateScoreInternel(evaluateId, starLevel, entry.getValue(), entry.getKey(), info, type, subId, areaId, evaluateRankFlag, customId, null, versionNum);
        }
    }

    // 插入得分记录
    private void insertEvaluateScoreInternel(Integer evaluateId, int starLevel, Integer ch999Id, EvaluateJobEnum job, Evaluate info, String type, Integer subId, Integer areaId, boolean evaluateRankFlag, Integer customId, Integer score2, Integer versionNum) {
        try {
            List<EvaluateCh999UserInfoBO> uList = new ArrayList<>();
            if (areaId == 0) {
                areaId = subMapper.getAreaId(subId);
            }
            //根据subId,评价类型,员工id,岗位类别去找这个记录，如果存在，则更新，如果不存在，那么插入
            Integer exist = 0;
            if (info.getEvaluateType() == 1 || info.getEvaluateType() == 2) {
                exist = evaluateMapper.getId(job.getCode(), subId);
            }
            if (job == EvaluateJobEnum.JISHUSERVICE) {
                exist = evaluateMapper.getID(job.getCode(), subId, ch999Id, type);
            }
            //客评系数 工作人员下单时，若客户存在历史投诉，服务完成后：该客户给予五星好评，则该好评按照两个五星好评进行计算 低评和原来一样不做特殊处理
            int kpXishu = 1;
            int kpXishuType = 0;
            if (starLevel == 5) {
                Integer single = evaluateMapper.getSingle(evaluateId);
                if (single != null) {
                    kpXishuType = 1;
                    kpXishu = 2;
                }
                if (kpXishu == 1) {
                    //钻级会员 5星评价算2个
                    Integer uObj = evaluateMapper.getUObj(evaluateId);
                    if (uObj != null) {
                        kpXishuType = 2;
                        kpXishu = 2;
                    }
                }
                if (kpXishu == 1) {
                    //贴膜售后五星好评算2个
                    if (info.getEvaluateType().equals(EvaluateTypeEnum.FileAfterSale.getCode())) {
                        kpXishuType = 3;
                        kpXishu = 2;
                    }
                }
                if (kpXishu == 1) {
                    //年包售后五星
                    if (info.getEvaluateType().equals(EvaluateTypeEnum.Online.getCode()) || info.getEvaluateType().equals(EvaluateTypeEnum.Offline.getCode())) {
                        Integer nObj = evaluateMapper.getNObj(info.getSubId());
                        if (nObj != null) {
                            kpXishuType = 4;
                            kpXishu = 2;
                        }
                    }
                }
            }
            if (job == EvaluateJobEnum.ONLINESERICES && subId.toString().length() == 6) {
                messagePushCloud.pushQyWeiXinMessage("1109", null, null,
                        "EvaluateScorePushOnlineServices: starLevel->" + starLevel + ",evaluateRankFlag->" + evaluateRankFlag + ",evaluateId->" + evaluateId,
                        null, null);
            }
            boolean evaluateRankFlag1 = true;

            if (evaluateRankFlag && starLevel == 4 && StringUtils.isNotEmpty(info.getTagIdStr())) {
                //正面标签的四星评价自动无效
                Integer exists = evaluateTagRecordMapper.exists(evaluateId, ch999Id, job.getCode());
                if (exists != 0) {
                    evaluateRankFlag1 = false;
                }

            }
            boolean evaluateRankFlag2 = true;
            Integer exists = 0;
            if (evaluateRankFlag && score2 == 4 && StringUtils.isNotEmpty(info.getTagIdStr())) {
                //正面标签的四星评价自动无效
                exists = evaluateTagRecordMapper.exists(evaluateId, ch999Id, job.getCode());
                if (exists != 0) {
                    evaluateRankFlag1 = false;
                }
            }
            boolean pushFlag = evaluateRankFlag;
            DateTimeFormatter dtf3 = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate data = LocalDate.now();
            String strDate3 = dtf3.format(data);
            String cacheKey = "DayEvaluateCount_" + strDate3 + "_" + customId + "_" + ch999Id;

            // evaluateType  13（小件售后）//，14（贴膜售后）
            if (info.getEvaluateType() == 13) {
                pushFlag = false;
            }
            int evaluateCount = 0;
            if (evaluateRankFlag && pushFlag && ch999Id > 0) {
                evaluateCount = (int) redisTemplate.opsForValue().get(cacheKey);
            }
            evaluateCount++;
            Date currentTime = new Date();
            //从一个 Instant和区域ID获得 LocalDateTime实例
            LocalDateTime localDateTime = LocalDateTime.ofInstant(currentTime.toInstant(), ZoneId.systemDefault());
            //获取第第二天零点时刻的实例
            LocalDateTime toromorrowTime = LocalDateTime.ofInstant(currentTime.toInstant(), ZoneId.systemDefault())
                    .plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
            //ChronoUnit日期枚举类,between方法计算两个时间对象之间的时间量
            long seconds = ChronoUnit.MINUTES.between(localDateTime, toromorrowTime);
            redisTemplate.opsForValue().set(cacheKey, evaluateCount, seconds);
            if (evaluateCount > 3) {
                evaluateRankFlag = false;
                evaluateRankFlag1 = false;
                evaluateRankFlag2 = false;
                pushFlag = false;
                data = LocalDate.now();
                String process = "系统" + dtf3.format(data) + "当天会员id:" + customId + "对工号" + ch999Id + "的员工第" + evaluateCount + "次评价，自动设置为无效评价";
                evaluateMapper.updateEvaluate(process, evaluateId);
            }

            int evaluateScoreId = 0;
            EvaluateScore evaluateScore = new EvaluateScore();
            if (exist != 0) {
                evaluateScore.setEvaluateId(evaluateId);
                evaluateScore.setRelateCh999Id(ch999Id);
                evaluateScore.setScore(starLevel);
                evaluateScore.setJob(job.getCode());
                evaluateScore.setSubId(subId);
                evaluateScore.setType(info.getEvaluateType());
                evaluateScore.setAreaid(areaId);
                evaluateScore.setWuxiao(evaluateRankFlag && evaluateRankFlag1);
                evaluateScore.setWuxiao2(score2 != null && (!evaluateRankFlag || !evaluateRankFlag2));
                evaluateScore.setKpXishu(kpXishu);
                evaluateScore.setKpXishuType(kpXishuType);
                evaluateScore.setScore2(score2 != null ? score2 : 0);
                evaluateScoreMapper.updateEvaluate(evaluateScore, job.getCode(), type, subId);
                EvaluateScore relateCh999IdById = evaluateScoreMapper.getRelateCh999Id(evaluateScore, job.getCode(), type, subId);
                if (job == EvaluateJobEnum.JISHUSERVICE) {
                    evaluateScoreMapper.updateEvaluateByCh999Id(evaluateScore, job.getCode(), type, subId, ch999Id);
                    relateCh999IdById = evaluateScoreMapper.getRelateCh999IdById(evaluateScore, job.getCode(), type, subId, ch999Id);
                }
                if (relateCh999IdById != null) {
                    pushFlag = pushFlag && relateCh999IdById.getRelateCh999Id() > 0;
                    ch999Id = relateCh999IdById.getRelateCh999Id();
                    evaluateScoreId = relateCh999IdById.getId();
                }
            } else {
                boolean save = evaluateScoreService.save(evaluateScore);
                EvaluateInvalidRecord evaluateInvalidRecord = new EvaluateInvalidRecord();
                evaluateInvalidRecord.setScoreId(evaluateScoreId);
                evaluateInvalidRecord.setDayCount(evaluateCount);
                evaluateInvalidRecord.setKinds(1);
                if (!save) {
                    pushFlag = false;
                }
                if (evaluateScoreId > 0 && evaluateCount > 3) {
                    evaluateInvalidRecordService.save(evaluateInvalidRecord);
                }
                List<EvaluateCh999UserModelBO> evaluateCh999UserModel = new ArrayList<>();
                EvaluateCh999UserModelBO e = new EvaluateCh999UserModelBO();
                e.setCh999_id(ch999Id);
                e.setMobileCount(1);
                e.setEvaluatePoint(BigDecimal.valueOf(starLevel));
                e.setEvaluateJob(job);
                evaluateCh999UserModel.add(e);
                updateEvaluateCh999Info(evaluateCh999UserModel);
                if (job == EvaluateJobEnum.ONLINESERICES && subId.toString().length() == 6) {
                    messagePushCloud.pushQyWeiXinMessage("1109", null, null,
                            "EvaluateScorePushOnlineServices: pushFlag->" + pushFlag + ",starLevel->" + starLevel + ",evaluateRankFlag->" + evaluateRankFlag + ",evaluateId->" + evaluateId,
                            null, null);
                }
                if (pushFlag && starLevel > 0 && evaluateRankFlag) {
                    JSONObject jsonData = new JSONObject();
                    JSONObject object = new JSONObject();
                    object.put("ch999Id", ch999Id);
                    object.put("score", starLevel);
                    object.put("kpXishu", kpXishu);
                    jsonData.put("act", "ManagementEvaluateNotifyPush");
                    jsonData.put("data", object);

                    if (job == EvaluateJobEnum.ONLINESERICES && subId.toString().length() == 6) {
                        messagePushCloud.pushQyWeiXinMessage("1109", null, null,
                                "EvaluateScorePushOnlineData" + jsonData,
                                null, null);
                    }
                    String redisCacheKey = "EvaluateScoreCacheKey_" + evaluateId + "_" + ch999Id;
                    Boolean aBoolean = redisTemplate.hasKey(redisCacheKey);
                    if (aBoolean) {
                        int time = (int) (1 + Math.random() * 9) * 300;
                        amqpTemplate.convertAndSend(RabbitMqConfig.QUEUE_TOPIC_DELAYQUEUE, jsonData, message -> {
                            message.getMessageProperties().setExpiration(time + "");
                            return message;
                        });

                    } else {
                        amqpTemplate.convertAndSend(RabbitMqConfig.QUEUE_TOPIC_DELAYQUEUE, jsonData);
                    }
                    redisTemplate.opsForValue().set(redisCacheKey, 1, 1);

                    //region 工作人员通知
                    String message = "";
                    String jobName = "服务";
                    switch (job) {
                        case YANJI:
                            jobName = "验机";
                            break;
                        case WEIXIUGONGCHENGSHI:
                            jobName = "维修";
                            break;
                        case SHOUHOUKEFU:
                            jobName = "接件";
                            break;
                    }
                    List<EvaluateUserRankBO> ranks = getEvaluateUserRankBy(Collections.singletonList(ch999Id));
                    if (ranks.size() > 0) {
                        EvaluateUserRankBO rankItem = ranks.get(0);
                        Integer count = rankItem.getCount();
                        Integer haoPing = rankItem.getHaoping();
                        Integer chaPing = rankItem.getChaping();
                        Integer rank = rankItem.getRank();
                        //客户建议
                        String comment = "";
                        if (StringUtils.isNotEmpty(info.getContent())) {
                            comment = "客户建议：" + info.getContent().replace("\n", "");
                        }
                        if (job == EvaluateJobEnum.ONLINESERICES) {
                            String subtxt = "";
                            if ("L".equals(type)) {
                                subtxt = "你" + jobName + "的订单号：" + subId + "，";
                            }

                            if (starLevel > 3) {
                                message = "厉害了!" + subId + "收到" + starLevel + "分评价，目前本月累计好评" + haoPing + "个，差评" + chaPing + "个，本月门店排名:" + rank + "名 。" + comment + "请继续保持，go go go！";
                            } else {
                                message = "加油哦!" + subId + "收到" + starLevel + "分评价，目前本月累计好评" + haoPing + "个，差评" + chaPing + "个，本月门店排名:" + rank + "名 。" + comment + "请调整状态，努力追赶！";
                            }
                        } else {
                            switch (type.toUpperCase()) {
                                case "C":
                                    if (starLevel > 3) {
                                        message = "厉害了!收到" + starLevel + "分评价，目前本月累计好评" + haoPing + "个，差评" + chaPing + "个，本月门店排名:" + rank + "名 。" + comment + "请继续保持，go go go！";
                                    } else {
                                        message = "加油哦!收到" + starLevel + "分评价，目前本月累计好评" + haoPing + "个，差评" + chaPing + "个，本月门店排名:" + rank + "名 。" + comment + "请调整状态，努力追赶！";
                                    }
                                    break;
                                case "R":
                                case "S":
                                    if (starLevel >= 3) {
                                        message = "厉害了!你" + jobName + "的订单号" + subId + "收到" + starLevel + "分评价，目前本月累计好评" + haoPing + "个，差评" + chaPing + "个，本月门店排名:" + rank + "名 。" + comment + "请继续保持，go go go！";
                                    } else {
                                        message = "加油哦！!你" + jobName + "的订单号" + subId + "收到" + starLevel + "分评价，目前本月累计好评" + haoPing + "个，差评" + chaPing + "个，本月门店排名:" + rank + "名 。" + comment + "请调整状态，努力追赶！";
                                    }
                                    break;
                                default:
                                    if (starLevel > 3) {
                                        message = "厉害了!你" + jobName + "的订单号" + subId + "收到" + starLevel + "分评价，目前本月累计好评" + haoPing + "个，差评" + chaPing + "个，本月门店排名:" + rank + "名 。" + comment + "请继续保持，go go go！";
                                    } else {
                                        message = "加油哦！!你" + jobName + "的订单号" + subId + "收到" + starLevel + "分评价，目前本月累计好评" + haoPing + "个，差评" + chaPing + "个，本月门店排名:" + rank + "名 。" + comment + "请调整状态，努力追赶！";
                                    }
                                    break;
                            }
                        }
                        if (StringUtils.isNotEmpty(message)) {
                            message += "<a href=\"https://moa.9ji.com/mevaluate/evaluateinfo?id=" + evaluateId + "\">查看评价</a>";
                            messagePushCloud.pushQyWeiXinMessage(ch999Id.toString(), "0", "系统通知", message, "https://moa.9ji.com/mevaluate/evaluateinfo?id=", "");
                        }
                    }
                }
            }
        } catch (Exception e) {
            messagePushCloud.pushQyWeiXinMessage("1109,6817", null, null,
                    "InsertEvaluateScoreInternelException" + e.getMessage(),
                    null, null);
            log.error(e.getMessage());
        }
    }

    private List<EvaluateUserRankBO> getEvaluateUserRankBy(List<Integer> ch999Id) {
        Date currentTime = new Date();
        //从一个 Instant和区域ID获得 LocalDateTime实例
        LocalDateTime localDateTime = LocalDateTime.ofInstant(currentTime.toInstant(), ZoneId.systemDefault());
        //获取第第二天零点时刻的实例
        LocalDateTime toromorrowTime = LocalDateTime.ofInstant(currentTime.toInstant(), ZoneId.systemDefault())
                .plusMonths(1).withHour(0).withMinute(0).withSecond(-1).withNano(0);
        List<EvaluateUserBO> dateList = evaluateScoreMapper.evaluateCountQuery(localDateTime, toromorrowTime, ch999Id);
        List<EvaluateUserBO> rankList = new ArrayList<>();
        List<EvaluateUserRankBO> returnList = new ArrayList<>();
        Map<String, List<EvaluateUserBO>> map = dateList.stream().collect(Collectors.groupingBy(item -> item.getCh999id() + "," + item.getAreaid()));
        map.forEach((key, value) -> {
            String[] strArr = key.split(",");
            Integer rank = value.get(0).getRank();
            EvaluateUserBO e = new EvaluateUserBO().setAreaid(Integer.parseInt(strArr[1])).setCh999id(Integer.parseInt(strArr[0])).setRank(rank);
            rankList.add(e);
            Integer haoPing = value.stream().map(EvaluateUserBO::getHaoping).reduce(Integer::sum).orElse(0);
            Integer count = value.stream().map(EvaluateUserBO::getCount).reduce(Integer::sum).orElse(0);
            Integer totalScore = value.stream().map(EvaluateUserBO::getTotalScore).reduce(Integer::sum).orElse(0);
            EvaluateUserRankBO evaluateUserRankBO = new EvaluateUserRankBO().setCh999id(Integer.parseInt(strArr[0]))
                    .setHaoping(haoPing).setCount(count).setTotalScore(totalScore).setRank(rank != null ? rank : 0);
        });
        return returnList;
    }

    public void updateEvaluateCh999Info(List<EvaluateCh999UserModelBO> info) {
        for (EvaluateCh999UserModelBO m : info) {
            if (m.getCh999_id() == 0) {
                continue;
            }
            String ch999_id = ch999EvaluateInfoMapper.getById(m.getCh999_id(), m.getEvaluateJob().getCode());
            Ch999EvaluateInfo ch999EvaluateInfo = new Ch999EvaluateInfo();
            ch999EvaluateInfo.setCh999Id(m.getCh999_id());
            ch999EvaluateInfo.setEvaluateCount(m.getEvaluateCount());
            ch999EvaluateInfo.setEvaluatePoint(m.getEvaluatePoint());
            ch999EvaluateInfo.setMobileCount(m.getMobileCount());
            ch999EvaluateInfo.setPriceCount(m.getPriceCount());
            ch999EvaluateInfo.setType(m.getEvaluateJob().getCode());
            if (ch999_id == null) {
                ch999EvaluateInfoService.save(ch999EvaluateInfo);
            } else {
                ch999EvaluateInfoMapper.updateByIdAndType(ch999EvaluateInfo, m.getCh999_id(), m.getEvaluateJob().getCode());
            }
        }


    }

    //根据单号获取订单相关人员
    private Integer getEvaluateTagRelateCh999Id(Integer subId, EvaluateJobEnum job, String type) {
        try {
            if (job == EvaluateJobEnum.ZUJISELLER || job == EvaluateJobEnum.ZUJIBACK) {
                return getZujiCh999idByJob(subId, job);
            }
            String uobj = "";
            Integer result = 0;
            switch (job) {
                case XIAOSHOU:
                    uobj = basketMapper.getSeller(subId);
                    break;
                case SHOUYIN:
                    uobj = shouyingMapper.getInuser(subId);
                    break;
                case JISHU:
                    uobj = basketMapper.getInuser(subId);
                    break;
                case YANJI:
                    uobj = subMapper.getSubTrader(subId);
                    break;
                case XIANSHANGKEFU:
                    uobj = basketMapper.getSellerV2(subId);
                    break;
                case WULIUPEISONG:
                    return getWuLiuPeiSongCh999Id(subId);
                case SHOUHOUKEFU:
                    uobj = shouhouMapper.getInUser(subId);
                    break;
                case WEIXIUGONGCHENGSHI:
                    uobj = shouyingMapper.getWeiXiuRen(subId);
                    break;
                case JISHUSERVICE:
                    return getJiShuServiceCh999Id(subId, type);
                case CALLSERVICE:
                    uobj = callCenterServiceEvalRecordMapper.getCh99Id(subId);
                    break;
                case ONLINESERICES:
                    if ("L".equals(type.toUpperCase())) {
                        uobj = recoverMarketsubinfoMapper.getOnlineSericesUsername(subId);
                        break;
                    } else {
                        return getOnlineServicesUserid(subId);
                    }
                case SMALLPROCONNECTOR:
                case SCREENPROTECTORCONNECTOR:
                    uobj = smallproMapper.getInUser(subId);
                    break;
                case RECYCLER:
                    uobj = recoverSubMapper.getInUser(subId);
                    break;
                case LIANGPINCHOOSE:
                    uobj = recoverMarketsubinfoMapper.getOnlineSericesUsername(subId);
                    break;
                case FOLLOWUPPERSON:
                    uobj = tousuMapper.getProcessUser(subId);
                    break;
                case XIANXIAALL:
                    result = ch999UserMapper.getCh999UerId(subId);
                    break;
                case SHOUHOUALL:
                    result = ch999UserMapper.getUerId(subId);
                    break;
                case INTERVIEW:
                    result = doorVisitRecordMapper.getCh999UerId(subId);
                    break;
                case DEVELOPER:
                    result = applyPrizeMapper.getCh999UerId(subId);
                    break;
                case PRODUCTMANAGER:
                    result = tApprovallogMapper.getCh999UerId(subId);
                    break;
                default:
                    break;
            }
            R<Ch999UserVo> userVoR = userInfoClient.getCh999UserByUserName(uobj);
            if (userVoR.getCode() == Result.SUCCESS) {
                Ch999UserVo userVo = userVoR.getData();
                return userVo == null ? 0 : userVo.getCh999Id();
            }

        } catch (Exception e) {
            log.error(e.getMessage());
            return 0;
        }
        return 0;
    }

    //获取在线客服 员工
    private Integer getOnlineServicesUserid(Integer subId) {
        Integer userid = 0;
        if (subId < 2000000) {
            MsgCenterQueue msgCenterQueue = msgCenterQueueMapper.getMsgCenterQueueMapper(subId);
            if (msgCenterQueue != null) {
                if (msgCenterQueue.getToUserID() != 0) {
                    userid = msgCenterQueue.getToUserID();
                } else {
                    userid = msgCenterQueueMapper.getToUserID(msgCenterQueue.getFromUserID(), subId);
                }
            }
        } else {
            String url = mUrlSource.getBasicUrl() + "web/api/oa/getStaffIdByQueueId/v1?queueId==" + subId;
            String jsonStr = HttpClientUtil.get(url);
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            // 请求成功
            if (jsonObject.getIntValue("code") == 0) {
                return jsonObject.getInteger("data");
            }
        }
        return userid;
    }

    //获取技术服务的员工id，订单类型不同，查询方式也不同
    private Integer getJiShuServiceCh999Id(Integer subId, String type) {
        try {
            //如果类型是V，那么是订单，subId是订单Id
            if ("V".equals(type)) {
                //根据订单id找到软件接见这个小伙
                String userName = msoftMapper.getInUser(subId);
                if (StringUtils.isEmpty(userName)) {
                    return 0;
                } else {
                    R<Ch999UserVo> userVoR = userInfoClient.getCh999UserByUserName(userName);
                    if (userVoR.getCode() == ResultCode.SUCCESS) {
                        Ch999UserVo ch999UserVo = userVoR.getData();
                        return ch999UserVo == null ? 0 : ch999UserVo.getCh999Id();
                    }
                    return 0;
                }
            } else if ("R".equals(type)) {
                String userName = msoftMapper.getInUserV2(subId);
                if (StringUtils.isNotEmpty(userName)) {
                    R<Ch999UserVo> userVoR = userInfoClient.getCh999UserByUserName(userName);
                    if (userVoR.getCode() == ResultCode.SUCCESS) {
                        Ch999UserVo ch999UserVo = userVoR.getData();
                        return ch999UserVo == null ? 0 : ch999UserVo.getCh999Id();
                    }
                    return 0;
                } else {
                    return 0;
                }
            } else {
                return 0;
            }
        } catch (Exception e) {
            return 0;
        }
    }

    //获取物流配送人员的id
    private Integer getWuLiuPeiSongCh999Id(Integer subId) {
        try {
            String paiJianRen = wuliuMapper.getPaiJianRen(subId);
            if (StringUtils.isNotEmpty(paiJianRen)) {
                R<Ch999UserVo> ch999UserR = userInfoClient.getCh999UserByUserName(paiJianRen);
                if (ch999UserR.getCode() == ResultCode.SUCCESS) {
                    Ch999UserVo ch999User = ch999UserR.getData();
                    return ch999User == null ? 0 : ch999User.getCh999Id();
                }
                return 0;
            } else {
                String obj = subMapper.getTrader(subId);
                if (StringUtils.isNotEmpty(obj)) {
                    R<Ch999UserVo> ch999UserR = userInfoClient.getCh999UserByUserName(paiJianRen);
                    if (ch999UserR.getCode() == ResultCode.SUCCESS) {
                        Ch999UserVo ch999User = ch999UserR.getData();
                        return ch999User == null ? 0 : ch999User.getCh999Id();
                    }
                    return 0;
                } else {
                    return 0;
                }
            }
        } catch (Exception e) {
            return 0;
        }
    }

    //获取租机的服务员工
    private Integer getZujiCh999idByJob(Integer subId, EvaluateJobEnum job) {
        Integer type = EvaluateTypeEnum.ZujiGet.getCode();
        if (job == EvaluateJobEnum.ZUJIBACK) {
            type = EvaluateTypeEnum.ZujiBack.getCode();
        }
        String url = mUrlSource.getBasicUrl() + "/rent/api/rentOrder/getStaffByOrderIdAndType?orderId=" + subId + "&type=" + type;
        String jsonStr = HttpClientUtil.get(url);
        JSONObject jsonObject = JSON.parseObject(jsonStr);
        // 请求成功
        if (jsonObject.getIntValue("code") == 0) {
            return jsonObject.getInteger("data");
        }
        return 0;
    }

    // 动态问题记录
    private void insertEvaluateQuestionAnswer(Integer evaluateId, List<Evaluate.QuestionAnswer> answers) {
        if (answers == null || !(answers.size() > 0)) {
            return;
        }
        for (Evaluate.QuestionAnswer q : answers
        ) {
            EvaluateQuestionAnswer e = new EvaluateQuestionAnswer();
            e.setEvaluateid(evaluateId);
            e.setCh999id(q.getCh999id());
            e.setQuestionid(q.getQuestionid());
            e.setAnswer(q.getAnswer());
            evaluateQuestionAnswerService.save(e);
            //连续10条(问题答案为是)的好评自动恢复,以后不再提问
            List<EvaluateQuestionAnswer> list = evaluateQuestionAnswerMapper.cancelQuestAnswer(q.getCh999id());
            int size = (int) list.stream().filter(item -> "是".equals(item.getAnswer())).count();
            if (list.size() == 10) {
                if (size == 10) {
                    evaluateQuestionAnswerMapper.updateQuestionUser(q.getCh999id());
                }
            }
        }
    }

    /// 插入标签记录
    private void insertEvaluateTagRecord(Integer id, List<EvaluateTag> tags, HashMap<EvaluateJobEnum, Integer> userIds) {
        for (EvaluateTag tag : tags) {
            EvaluateTagRecord evaluateTagRecord = new EvaluateTagRecord();
            evaluateTagRecord.setTagId(id);
            evaluateTagRecord.setTagId(tag.getId());
            evaluateTagRecord.setRelateCh999Id(userIds.getOrDefault(
                    EnumUtil.getEnumByMessage(EvaluateJobEnum.class, tag.getJob()), 0));
            evaluateTagRecordService.save(evaluateTagRecord);
            evaluateTagMapper.updateE(tag.getId());
        }
    }

    //获取标签关联员工（新）
    private HashMap<EvaluateJobEnum, Integer> getEvaluateTagRelateCh999IdsNew(Integer subId, EvaluateTypeEnum typeEnum) {
        HashMap<EvaluateJobEnum, Integer> result = new HashMap<>();
        switch (typeEnum) {
            case Online:
                result.put(EvaluateJobEnum.XIANSHANGALL, getEvaluateTagRelateCh999Id(subId, EvaluateJobEnum.XIANSHANGKEFU, ""));
                result.put(EvaluateJobEnum.WULIUPEISONG, getEvaluateTagRelateCh999Id(subId, EvaluateJobEnum.WULIUPEISONG, ""));
                return result;
            case Offline:
                result.put(EvaluateJobEnum.XIAOSHOU, getEvaluateTagRelateCh999Id(subId, EvaluateJobEnum.XIAOSHOU, ""));
                result.put(EvaluateJobEnum.SHOUYIN, getEvaluateTagRelateCh999Id(subId, EvaluateJobEnum.SHOUYIN, ""));
                result.put(EvaluateJobEnum.JISHU, getEvaluateTagRelateCh999Id(subId, EvaluateJobEnum.JISHU, ""));
                result.put(EvaluateJobEnum.YANJI, getEvaluateTagRelateCh999Id(subId, EvaluateJobEnum.YANJI, ""));
                return result;
            case Shouhou:
                result.put(EvaluateJobEnum.SHOUHOUKEFU, getEvaluateTagRelateCh999Id(subId, EvaluateJobEnum.SHOUHOUKEFU, ""));
                result.put(EvaluateJobEnum.WEIXIUGONGCHENGSHI, getEvaluateTagRelateCh999Id(subId, EvaluateJobEnum.WEIXIUGONGCHENGSHI, ""));
                return result;
            case Electrombile:
                break;
            default:
                throw new IllegalArgumentException("参数类型不匹配EvaluateTypeEnum:" + typeEnum);
        }
        result.put(EvaluateJobEnum.NONE, 0);
        return result;
    }

    //插入客评数据(新)
    private Integer insertEvaluateNew(Evaluate info, List<EvaluateTag> tags, String type, int sourceFrom, boolean isInvalid, Integer versionNum) {
        try {
            String[] jobStarLevels = info.getJobStarLevel().split(",");
            List<Integer> scoreList = new ArrayList<>();
            List<Integer> majorScoreList = new ArrayList<>();
            for (String jobStarLevel : jobStarLevels
            ) {
                Integer star = 0;
                Integer majorScore = 0;
                String[] keyValue = jobStarLevel.split(":");
                if (keyValue.length >= 2 && keyValue[0].matches("^[0-9]*$") && keyValue[1].matches("^[0-9]*$")) {
                    scoreList.add(star);
                    if (keyValue.length == 3 && keyValue[1].matches("^[0-9]*$")) {
                        majorScoreList.add(majorScore);
                    }
                }
            }
            //计算平均分
            float avgStar = 0f;

            if (scoreList.size() <= 0) {
                avgStar = 0;
            } else {
                if (majorScoreList.size() <= 0) {
                    BigDecimal b = BigDecimal.valueOf(scoreList.stream().mapToDouble(Integer::intValue).average().getAsDouble());
                    avgStar = b.setScale(1, BigDecimal.ROUND_HALF_UP).floatValue();
                } else {
                    List<Integer> allScoreList = new ArrayList<>();
                    allScoreList.addAll(scoreList);
                    allScoreList.addAll(majorScoreList);
                    BigDecimal b = BigDecimal.valueOf(allScoreList.stream().mapToDouble(Integer::intValue).average().getAsDouble());
                    avgStar = b.setScale(1, BigDecimal.ROUND_HALF_UP).floatValue();
                }
            }
            //每个都是三星以上，无负面标签的无需处理
            Integer status;
            List<Integer> collect = scoreList.stream().filter(integer -> integer > 3).collect(Collectors.toList());
            // EvaluateTagTypeEnum : Positive(1, "正面")
            List<EvaluateTag> evaluateTagStream = tags.stream().filter(e -> Integer.valueOf(1).equals(e.getTagType())).collect(Collectors.toList());
            if (collect.size() > 0 && evaluateTagStream.size() > 0) {
                status = EvaluateStatusEnum.EvaluateStatusWXCL.getCode();
            } else {
                status = EvaluateStatusEnum.EvaluateStatusWCL.getCode();
            }
            String content = "";
            if (StringUtils.isNotEmpty(info.getContent())) {
                content = info.getContent().length() > 499 ? info.getContent().substring(0, 499) : info.getContent();
            }
            String dianzhang = bbsxpUsersMapper.getDianZhang(info.getAreaId());
            LocalDateTime addtime = LocalDateTime.now();
            LocalDateTime huanyuantimeout;
            DayOfWeek week = addtime.getDayOfWeek();
            if (week == DayOfWeek.SATURDAY || week == DayOfWeek.SUNDAY) {
                if (addtime.getHour() >= 9 && addtime.getHour() <= 18) {
                    huanyuantimeout = addtime.plusDays(2).plusSeconds(-1);
                } else if (addtime.getHour() > 18 && (addtime.getHour() < 21 || (addtime.getHour() == 21 && addtime.getMinute() <= 30))) {
                    huanyuantimeout = addtime.plusDays(3).plusHours(12);
                } else {
                    huanyuantimeout = addtime.plusDays(3).plusHours(18);
                }
            } else {
                if (addtime.getHour() >= 9 && addtime.getHour() <= 18) {
                    huanyuantimeout = addtime.plusDays(1).plusSeconds(-1);
                } else if (addtime.getHour() > 18 && (addtime.getHour() < 21 || (addtime.getHour() == 21 && addtime.getMinute() <= 30))) {
                    huanyuantimeout = addtime.plusDays(1).plusHours(12);
                } else {
                    huanyuantimeout = addtime.plusDays(1).plusHours(18);
                }
            }
            info.setContent(content);
            info.setEvaluateStatus(status);
            info.setAvgStarLevel(avgStar);
            info.setSourceFrom(sourceFrom);
            info.setDianzhang(dianzhang);
            info.setHuanyuantimeout(huanyuantimeout);
            info.setIsInvalid(isInvalid);
            info.setVersionNum(versionNum);
            boolean isSave = evaluateService.save(info);

            //如果类型是软件接件，那么记录评价id和软件接见id的关系
            if ("R".equals(type) && isSave) {
                insertEvaluateIdAndMSoftId(info.getId(), info.getSubId());
            }
            if ("C".equals(type) && isSave) {
                markServiceEvaluted(info.getSubId());
            }

            return info.getId();

        } catch (Exception e) {
            log.error(e.getMessage());
            return 0;
        }
    }

    private void markServiceEvaluted(Integer subId) {
        CallCenterServiceEvalRecord call = callCenterServiceEvalRecordService.getCallCenterServiceEvalRecord(subId);
        call.setScored(true);
        callCenterServiceEvalRecordService.updateById(call);
    }

    //记录评价id和软件接见id的关系
    private void insertEvaluateIdAndMSoftId(Integer evaluateId, Integer msoftId) {
        EvaluateIdAndMsoftId evaluateIdAndMsoftId = new EvaluateIdAndMsoftId();
        evaluateIdAndMsoftId.setEvaluateId(evaluateId);
        evaluateIdAndMsoftId.setMsoftId(msoftId);
        Msoft msoft = msoftService.getById(msoftId);
        msoft.setEvaluateid((long) evaluateId);
        msoftService.updateById(msoft);
        evaluateIdAndMsoftIdService.save(evaluateIdAndMsoftId);

    }

    //修改评价内容和推荐指数内容
    private boolean upEvaluateContentAndCommentedRemark(int evaluateId, String content, String commendedRemark, Integer commendedScore) {
        String process = "系统" + LocalDateTime.now() + toString() + "客户追评:" + content;
        if (StringUtils.isEmpty(content)) {
            process = "";
        }
        Evaluate evaluate = evaluateService.getById(evaluateId);
        evaluate.setContent(content);
        evaluate.setCommendedRemark(commendedRemark);
        if (evaluate.getProcess() == null) {
            evaluate.setProcess("" + process);
        } else {
            evaluate.setProcess(process);
        }
        if (commendedScore != null) {
            evaluate.setCommendedScore(commendedScore - 1);
        }
        return evaluateService.updateById(evaluate);
    }

    //插入网站推荐分标签记录
    private void insertRecommendationWebsiteTagRecord(int evaluateId, List<EvaluateTag> tags) {
        EvaluateTagRecord evaluateTagRecord = new EvaluateTagRecord();
        evaluateTagRecord.setEvaluateId(evaluateId);
        evaluateTagRecord.setRelateCh999Id(null);
        evaluateTagRecord.setIsOrderTag(true);
        for (EvaluateTag e : tags) {
            if (e.getClickCount() == null) {
                e.setClickCount(e.getClickCount() + 1);
            }
            evaluateTagRecord.setTagId(e.getId());
            evaluateTagService.updateById(e);
            evaluateTagRecordService.save(evaluateTagRecord);
        }
    }

    //解析标签Id
    private List<Integer> tryParseEvaluateTagIds(String tagIdStr) {
        List<Integer> result = new ArrayList<>();
        String[] idList = tagIdStr.split(",");
        for (String id : idList) {
            if (id.matches("^[0-9]*$")) {
                int tagId = Integer.parseInt(id);
                result.add(tagId);
            }
        }

        return result;
    }

    //校验评价参数
    private void checkEvaluate(Evaluate info) {
        if (info.getUserId() == 0) {
            log.error("会员会员id有误");
        }
        if (info.getAreaId() == 0) {
            log.error("门店ID有误");
        }
        if (info.getSubId() == 0) {
            log.error("订单有误");
        }
        if (info.getEvaluateType() == 0) {
            log.error("评价类型有误");
        }
    }

    //根据标签Id获取标签数据
    private List<EvaluateTag> getEvaluateTags(List<Integer> tagIds) {
        if (tagIds.size() == 0) {
            return new ArrayList<>();
        }
        return evaluateMapper.getEvaluateTags(tagIds);
    }

    @Override
    public R<Page<EvaluateListVO>> getEvaluateList(EvaluateListQuery req, Map<Integer, String> areaMap,boolean isExport) {
        Page<EvaluateListVO> page = new Page<>(req.getCurrent(), req.getSize());
        // 当前登录用户
        OaUserBO oaUserBo = currentRequestComponent.getCurrentStaffId();
        EvaluateListExQuery exQuery = new EvaluateListExQuery();
        R r = handleEvaluateListReq(req, exQuery, oaUserBo, isExport);
        // 唯一标识：单号、客评id
        boolean uniqueIdent = Objects.nonNull(req.getSearchKey())
                && StringUtils.isNotEmpty(req.getSearchValue())
                && Arrays.asList(1, 5).contains(req.getSearchKey());
        if (uniqueIdent) {
            EvaluateListQuery newReq = new EvaluateListQuery();
            newReq.setSearchKey(req.getSearchKey());
            newReq.setSearchValue(req.getSearchValue());
            if (ActiveProfileJudgeUtil.isJiuJiEnvironment() && Objects.equals(req.getSearchKey(), 5) && org.apache.commons.lang3.StringUtils.isNotBlank(req.getSearchValue())) {
                long searchKeySplitSize = Arrays.stream(org.apache.commons.lang3.StringUtils.split(req.getSearchValue(), ",")).count();
                if (searchKeySplitSize > 1) {
                    newReq.setEvaluateIdStr(req.getSearchValue());
                }
            }
            req = newReq;
        } else {
            BusinessUtil.checkDataViewScope(RoleTermModuleEnum.STORE_OPERATION, oaUserBo.getToken(),
                    req.getStartTime(), req.getEndTime(), req::setStartTime, req::setEndTime);
            if (Objects.isNull(req.getTimeType())) {
                req.setTimeType(EvaluateTimeTypeEnum.EVALUATE_TIME.getCode());
            }
        }

        if (ResultCode.SUCCESS != r.getCode()) {
            return R.error(r.getUserMsg());
        }

        getEvaluateListPage(page, req, areaMap, exQuery);
        return R.success(page);
    }

    @Override
    public R<Page<EvaluateListVO>> getEvaluateStatisticsList(String evaluateIdsKey, Integer page, Integer size) {
        Page<EvaluateListVO> pageVO = new Page<EvaluateListVO>(page, size);
        pageVO.setDesc("e.id");
        if (StringUtils.isEmpty(evaluateIdsKey)) {
            return R.success(pageVO);
        }
        Set<Integer> evaluateIds = (Set<Integer>)redisTemplate.opsForValue().get(evaluateIdsKey);
        if (CollectionUtil.isEmpty(evaluateIds)) {
            return R.success(pageVO);
        }
        pageVO = baseMapper.getEvaluateStatisticsList(pageVO, evaluateIds, jiujiSystemProperties.getOfficeName());
        List<EvaluateListVO> records = pageVO.getRecords();
        if (CollectionUtil.isEmpty(records)) {
            return R.success(pageVO);
        }
        Map<Integer, String> areaMap = new HashMap<>();
        handleSubListQuery(records, areaMap, false);
        return R.success(pageVO);
    }

    private void getEvaluateListPage(Page<EvaluateListVO> page, EvaluateListQuery req, Map<Integer, String> areaMap, EvaluateListExQuery exQuery) {
        handlerQuery(req);
        OaUserBO oaUserBO = userComponent.getOaUserBO(true);
        Integer authorizeId = null;
        if (oaUserBO != null) {
            authorizeId = oaUserBO.getAuthorizeId();
        }
        // 查询总条数
        Long evaluateListTotal = baseMapper.getEvaluateListTotal(req, exQuery, jiujiSystemProperties.getOfficeName(), authorizeId);
        if (evaluateListTotal != null && evaluateListTotal > 0) {
            // 查询记录
            List<EvaluateListVO> evaluateList = baseMapper.getEvaluateList(req, exQuery, jiujiSystemProperties.getOfficeName(), authorizeId);
            handleSubListQuery(evaluateList, areaMap, Boolean.TRUE.equals(req.getExportData()));

            page.setTotal(evaluateListTotal);
            page.setRecords(evaluateList);
        }
    }

    private void handlerQuery(EvaluateListQuery req) {
        // 输出不包含税务模式
        if ("jiuji".equals(SpringContext.getActiveProfile())) {
            //不为空时c#统一对地区选项做了筛选，java端针对地区为空时税务模式调整即可
            if (CollectionUtils.isEmpty(req.getAreaIds())) {
                //没有地区条件下根据当前电脑证书归属门店的“总公司主体”、“纳税主体”情况进行来源门店的匹配
                OaUserBO userBO = currentRequestComponent.getCurrentStaffId();
                //开启税务模式
                if (userBO != null && userBO.getIsTaxModel() != null && userBO.getIsTaxModel()) {
                    AreaFindVo findVo = new AreaFindVo();
                    findVo.setIsTaxModel(userBO.getIsTaxModel());
                    findVo.setTaxCompanyId(userBO.getTaxCompanyId());
                    List<Integer> integerList = storeAreaInfoCloud.idsByCondition(findVo).getData();
                    if (CollectionUtils.isEmpty(integerList)) {
                        return ;
                    }
                    req.setAreaIds(integerList);
                }
            }
        }
        req.setIsJiuji(XtenantEnum.isJiujiXtenant());

        // 建议字数做特殊处理
        if (req.getAreaSuggestionLength() != null) {
            req.setAreaSuggestionLength(req.getAreaSuggestionLength() * 2);
        }
        if (req.getPersonSuggestionLength() != null) {
            req.setPersonSuggestionLength(req.getPersonSuggestionLength() * 2);
        }
        if (req.getSuggestionKey() != null && req.getSuggestionKey().size() != 0) {
            if (req.getSuggestionKey().contains(1) && req.getPersonSuggestionLength() == null && StringUtils.isEmpty(req.getPersonSuggestion())) {
                req.setPersonSuggestionLength(1);
            }
            if (req.getSuggestionKey().contains(2) && req.getAreaSuggestionLength() == null && StringUtils.isEmpty(req.getAreaSuggestion())) {
                req.setAreaSuggestionLength(1);
            }
        }
    }

    /**
     * 整理req 一些复杂的条件处理转换成sql需要的查询条件
     * @param req
     * @param exQuery
     * @return
     */
    private R handleEvaluateListReq(EvaluateListQuery req, EvaluateListExQuery exQuery,
                                    OaUserBO oaUserBo, boolean isExport) {
        if (isExport) {
            req.setCurrent(null);
            req.setSize(null);
            req.setExportData(true);
        } else {
            req.setExportData(false);
            if (CommonUtil.isNullOrZero(req.getCurrent())) {
                req.setCurrent(1);
            }
            if (CommonUtil.isNullOrZero(req.getSize())) {
                req.setSize(20);
            }
        }
        if (Objects.isNull(req.getTimeType())
                && Objects.nonNull(req.getStartTime())
                && Objects.nonNull(req.getEndTime())) {
            req.setTimeType(EvaluateTimeTypeEnum.EVALUATE_TIME.getCode());
        }

        if (req.getEvaluateTagType() == null) {
            req.setEvaluateTagType(Lists.newArrayList());
        }
        if (Boolean.TRUE.equals(req.getEvaluateCountFlag()) && XtenantEnum.isJiujiXtenant()) {
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(req.getEvaluateIdsList())) {
                if (req.getEvaluateIdsList().size() > 5000) {
                    throw new CustomizeException("查询数据过多，请精确查询条件后再进行查询");
                }
                req.setEvaluateIdStr(org.apache.commons.lang3.StringUtils.join(req.getEvaluateIdsList(), ","));
            } else {
                req.setEvaluateIdStr("0");
            }
        }
        if (ActiveProfileJudgeUtil.isJiuJiEnvironment()) {
            List<Integer> taxCompanyIdList = getTaxModelFromConfig(oaUserBo.getToken());
            req.setTaxModel(CollectionUtils.isNotEmpty(taxCompanyIdList));
            req.setTaxCompanyIdList(taxCompanyIdList);
        }
        return handle(req, exQuery);
    }

    private R handle(EvaluateListQuery req, EvaluateListExQuery exQuery) {
/*        if (req.getEndTime() != null) {
            req.setEndTime(req.getEndTime().plus(1, ChronoUnit.DAYS));
        }*/
        // 处理评价来源枚举，将app换成ANDROID(2, "andriod"),IOS(3, "ios")
        List<Integer> evaluateSourceFromList = req.getEvaluateSourceFromList();
        if (CollectionUtil.isNotEmpty(evaluateSourceFromList)) {
            boolean contains = evaluateSourceFromList.contains(EvaluateSourceFromEnum.APP.getCode());
            if (contains) {
                evaluateSourceFromList.removeIf(item -> EvaluateSourceFromEnum.APP.getCode().equals(item));
                evaluateSourceFromList.add(2);
                evaluateSourceFromList.add(3);
            }
        }
        // 根据req departType 和departId 获取areaIds
        List<Integer> areaIds = null;
        if (StringUtils.isNotEmpty(req.getDepartId()) && req.getDepartType() != null) {
            if (req.getDepartType() == 1) {
                // 按门店
                areaIds = Arrays.stream(req.getDepartId().split(",")).map(Integer::valueOf).collect(Collectors.toList());
            } else if (req.getDepartType() == 2 || req.getDepartType() == 3) {
                // 2按小区 3按大区
                R<List<AreaInfo>> areaListR =  areaInfoClient.listAll();
                if (ResultCode.SUCCESS != areaListR.getCode() || CollectionUtils.isEmpty(areaListR.getData())) {
                    return R.error("门店信息获取出错!");
                }
                areaIds = areaListR.getData().stream().filter(a -> (a.getXtenant() != null && a.getXtenant().equals(req.getXtenant()))
                                && a.getDepartId().equals(req.getDepartId()))
                        .map(AreaInfo::getId).collect(Collectors.toList());
            }
        }
        exQuery.setExAreaIds(areaIds);
        // 处理searchKey searchValue 返回false说明查询结果肯定为空。
        if (!handleSearchKeyValue(req, exQuery)) {
            return R.error("未查询到数据");
        }

        // 处理staffName
        if (StringUtils.isNotEmpty(req.getStaffName())) {
            Integer staffCh999Id = transUserId(req.getStaffName());
            if (staffCh999Id == 0) {
                return R.error("未查询到数据");
            }
            exQuery.setStaffCh999Id(staffCh999Id);
        }
        return R.success("");
    }

    private List<Integer> getTaxModelFromConfig(String token) {
        R<List<Integer>> taxResult = areaInfoCloud.listTaxCompanyIdByToken(token);
        if (CommonUtils.isRequestSuccess(taxResult)) {
            return taxResult.getData();
        }
        return Collections.emptyList();
    }


    /**
     * C# 子查询处理对照说明
     * Tags, JobTags JobAndStarLevel 导出excel设置色彩用
     * Questions详情页用， RecommendationWebsiteTags 推荐网站标签
     * ScoreTags 客评列表服务和专业标签，重构不需要  只需要查询出jobList 客评列表 专业分
     * @param records
     */
    private void handleSubListQuery(List<EvaluateListVO> records, Map<Integer, String> areaMap, boolean isExport) {
        List<Integer> evaluateIds = records.stream().map(EvaluateListVO::getId).collect(Collectors.toList());
        if (evaluateIds.size() > 5000) {
            throw new CustomizeException("查询数据过多，请精确查询条件后再进行查询");
        }
        boolean isJiuji = XtenantEnum.isJiujiXtenant();
        List<Integer> userIds = records.stream().map(EvaluateListVO::getUserId).filter(Objects::nonNull).collect(Collectors.toList());
        String userIdStr = org.apache.commons.lang3.StringUtils.join(userIds, ",");
        List<UserBO> userList = bbsxpUsersService.getUserList(userIdStr);
        Map<Integer, Integer> growthMap = userList.stream().collect(Collectors.toMap(UserBO::getUserid, UserBO::getGrowth));
        String evaluateIdStr = org.apache.commons.lang3.StringUtils.join(evaluateIds, ",");
        List<EvaluateJobScoreVO> scoreList = baseMapper.getEvaluateJobScoreByEvaluateIdStr(evaluateIdStr, jiujiSystemProperties.getOfficeName());
        Map<Integer, List<EvaluateJobScoreVO>> evaMap = scoreList.stream().collect(Collectors.groupingBy(EvaluateJobScoreVO::getEvaluateId));
        // 当‘验机、技术服务’这两个岗位是同一个员工时，只显示验机岗位即可
        // 导出不排除
        if (isJiuji && !isExport) {
            scoreList.removeIf(eJobScore -> {
                List<EvaluateJobScoreVO> scoreVOList = evaMap.getOrDefault(eJobScore.getEvaluateId(), new ArrayList<>());
                return EvaluateJobEnum.JISHUSERVICE.getCode().equals(eJobScore.getJob())
                        && scoreVOList.stream().anyMatch(score -> EvaluateJobEnum.YANJI.getCode().equals(score.getJob())
                        && score.getUserId().equals(eJobScore.getUserId()));
            });
        }
        Map<Integer, List<EvaluateJobScoreVO>> scoreMap = scoreList.stream().map(item ->
                item.setJobName(EnumUtil.getMessageByCode(EvaluateJobEnum.class, item.getJob())))
                .collect(Collectors.groupingBy(EvaluateJobScoreVO::getEvaluateId));
        String evaluateScoreIdStr = org.apache.commons.lang3.StringUtils.join(scoreList.stream().map(EvaluateJobScoreVO::getId).collect(Collectors.toList()), ",");
        List<EvaluateTagRecordNameVO> tagList = baseMapper.getEvaluateTargetListByEvaluateIdStr(evaluateIdStr);
        List<EvaluateNonFiveStarDeductBO> nonFiveStarDeductList = new ArrayList<>();
        if (StringUtils.isNotEmpty(evaluateScoreIdStr)) {
            nonFiveStarDeductList = baseMapper.getEvaluateNonFiveStarDeductByEvaluateIdStr(evaluateScoreIdStr);
        }
        Map<Integer, Integer> nonFiveStarDeductMap = nonFiveStarDeductList.stream().collect(Collectors.toMap(EvaluateNonFiveStarDeductBO::getId, EvaluateNonFiveStarDeductBO::getFen));
        List<EvaluateDepart> evaluateDeparts = baseMapper.departInfos(evaluateIdStr);
        List<EvaluateRewardBO> evaluateRewardList = baseMapper.getEvaluateReward(jiujiSystemProperties.getOfficeName(), evaluateIdStr);
        Map<Integer, EvaluateRewardBO> evaluateRewardMap = evaluateRewardList.stream().collect(Collectors.toMap(EvaluateRewardBO::getEvaluateId, Function.identity()));
        Map<Integer, List<EvaluateDepart>> listMap = evaluateDeparts.stream()
                .collect(Collectors.groupingBy(it -> it.getEvaluateId()));
        Map<Integer, List<EvaluateCategoryRelation>> relationMaps =new HashMap<>();
        if(CollectionUtil.isNotEmpty(evaluateDeparts)){
            List<EvaluateCategoryRelation> relations=baseMapper.cateInfos(evaluateDeparts.stream().map(EvaluateDepart::getId).collect(Collectors.toList()));
            relationMaps=relations.stream().collect(Collectors.groupingBy(it->it.getEvaluateId()));
        }
        Map<Integer,String> departCache=new HashMap<>();
        Map<Integer, List<EvaluateCategoryRelation>> finalRelationMaps = relationMaps;
        List<Integer> tsIds = records.stream().filter(eva -> EvaluateTypeEnum.COMPLAINT.getCode().equals(eva.getEvaluateType())).map(EvaluateListVO::getSubId).collect(Collectors.toList());
        Map<Integer, Integer> tsAreaMap = new HashMap<>();
        if (ActiveProfileJudgeUtil.isJiuJiEnvironment() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(tsIds)) {
            List<TousuAreaBO> tousuAreaInfos = touSuMapper.getTousuAreaInfos(tsIds);
            tsAreaMap = tousuAreaInfos.stream().collect(Collectors.toMap(TousuAreaBO::getTousuId, TousuAreaBO::getAreaId));
        }
        EvaluateRewardBO defaultEvaluateReward = new EvaluateRewardBO(0, 0, BigDecimal.ZERO);
        Map<Integer, Integer> finalTsAreaMap = tsAreaMap;
        records.forEach(r -> {
            if(listMap.containsKey(r.getId())){
                List<EvaluateDepart> departs = listMap.get(r.getId());
                List<String> strings = departs.stream().map(it -> {
                    if (departCache.containsKey(it.getDepartId())) {
                        return departCache.get(it.getDepartId());
                    }
                    R<DepartInfoVO> byDepartId = departInfoClient.getByDepartId(it.getDepartId());
                    departCache.put(it.getDepartId(), byDepartId.getData().getName());
                    return byDepartId.getData().getName();
                }).collect(Collectors.toList());
                r.setEvaluateDepartNames(org.apache.commons.lang3.StringUtils.join(strings,","));
                r.setEvaluateDepartNameList(strings);
            }
            if(finalRelationMaps.containsKey(r.getId())){
                List<String> strings = finalRelationMaps.get(r.getId()).stream().map(it -> it.getCateName())
                        .collect(Collectors.toList());
                r.setCateNames(org.apache.commons.lang3.StringUtils.join(strings,","));
            }
            // 评价类型
            r.setEvaluateTypeName(EnumUtil.getMessageByCode(EvaluateTypeEnum.class, r.getEvaluateType()));
            // 评价状态
            r.setEvaluateStatusName(EnumUtil.getMessageByCode(EvaluateStatusEnum.class, r.getEvaluateStatus()));
            // 来源
            r.setSourceFromName(EnumUtil.getMessageByCode(EvaluateSourceFromEnum.class, r.getSourceFrom()));
            // 会员级别
            r.setUserClassName(EnumUtil.getMessageByCode(EUserClassNewEnum.class, r.getUserClass()));
            //处理状态
            if (StringUtils.isEmpty(r.getIsSolvingProblem())){
                r.setIsSolvingProblem("-");
            } else if(Objects.equals(r.getIsSolvingProblem(),"0")){
                r.setIsSolvingProblem("未解决");
            }else{
                r.setIsSolvingProblem("已解决");
            }
            r.setMemberStarLevel(userComponent.getUserStarLevel(growthMap.getOrDefault(r.getUserId(), null)));
            if (ActiveProfileJudgeUtil.isJiuJiEnvironment() && EvaluateTypeEnum.COMPLAINT.getCode().equals(r.getEvaluateType())) {
                if (Objects.nonNull(finalTsAreaMap.get(r.getSubId()))) {
                    r.setAreaId(finalTsAreaMap.get(r.getSubId()));
                } else {
                    r.setAreaId(22);
                }
            }
            if (ActiveProfileJudgeUtil.isJiuJiEnvironment() && EvaluateTypeEnum.COMPLAINT.getCode().equals(r.getEvaluateType())) {
                r.setComplaintUrl(oaUrl + "/staticpc/#/complaint/detail/" + r.getSubId());
                r.setFavourableText("否");
                r.setIsIllegal(Boolean.FALSE);
            }
            Integer rewardPoints = evaluateRewardMap.getOrDefault(r.getId(), defaultEvaluateReward).getRewardPoints();
            BigDecimal rewardReaPacket = evaluateRewardMap.getOrDefault(r.getId(), defaultEvaluateReward).getRewardReaPacket();
            r.setRewardPoints(Objects.nonNull(rewardPoints) ? rewardPoints : defaultEvaluateReward.getRewardPoints());
            r.setRewardReaPacket(Objects.nonNull(rewardReaPacket) ? rewardReaPacket : defaultEvaluateReward.getRewardReaPacket());
            if (r.getAreaId() != null) {
                if (areaMap.containsKey(r.getAreaId())) {
                    r.setArea(areaMap.get(r.getAreaId()));
                } else {
                    R<AreaInfo> areaR = areaInfoClient.getAreaInfoById(r.getAreaId());
                    if (ResultCode.SUCCESS == areaR.getCode() && areaR.getData() != null) {
                        r.setArea(areaR.getData().getArea());
                        areaMap.put(r.getAreaId(), areaR.getData().getArea());
                    }
                }
            }
            r.setJobList(scoreMap.get(r.getId()));
            // 非五星扣分
            List<EvaluateJobScoreVO> subScoreList = scoreMap.getOrDefault(r.getId(), new ArrayList<>());
            r.setNonFiveStarDeduct(subScoreList.stream()
                    .map(score -> BigDecimal.valueOf(nonFiveStarDeductMap.getOrDefault(score.getId(), 0)))
                    .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, RoundingMode.HALF_DOWN));
            // setNonFiveStarDeduct(r);
            // TODO 下面注释的代码有bug
//            if (CollectionUtils.isNotEmpty(scoreMap.get(r.getId()))) {
//                Map<Integer, Integer> jobAndStarLevel = new HashMap<>();
//                scoreMap.get(r.getId()).forEach(s -> jobAndStarLevel.put(s.getJob(), s.getScore()));
//                r.setJobAndStarLevel(jobAndStarLevel);
//            }
            if (CollectionUtils.isNotEmpty(tagList)) {
                StringBuilder tags = new StringBuilder();
                StringBuilder jobTags = new StringBuilder();
                // 门店评价标签
                List<EvaluateScoreTagVO> areaTags = new ArrayList<>();
                List<EvaluateScoreTagVO> scoreTags = new ArrayList<>();
                tagList.forEach(t -> {
                    if (r.getId().equals(t.getEvaluateId()) && Boolean.FALSE.equals(t.getIsOrderTag())) {
                        scoreTags.add(new EvaluateScoreTagVO().setName(t.getName()).setTagType(t.getTagType()).setJob(t.getJob()).setScoreKinds(t.getScoreKind()));
                        tags.append(t.getName()).append("、");
                        if (t.getJob() != null && t.getJob() > 0) {
                            jobTags.append(t.getName()).append("、");
                        }
                    }
                    if (r.getId().equals(t.getEvaluateId()) && AREA_EVALUATE_TAG_TYPE.equals(t.getEvaluateType())) {
                        areaTags.add(new EvaluateScoreTagVO().setName(t.getName()).setTagType(t.getTagType()).setJob(t.getJob()).setScoreKinds(t.getScoreKind()));
                    }
                });
                if (tags.length() > 0) {
                    tags.deleteCharAt(tags.length() - 1);
                    r.setTags(tags.toString());
                }
                if (jobTags.length() > 0) {
                    jobTags.deleteCharAt(jobTags.length() - 1);
                    r.setJobTags(jobTags.toString());
                }
                if (CollectionUtils.isNotEmpty(areaTags)) {
                    r.setAreaTags(areaTags);
                }

                r.setScoreTags(scoreTags);

                // 商品平均分
                Integer evaluateType = r.getEvaluateType();
                List<Integer> basketIds = new ArrayList<>();

                // 新机评价商品平均分
                if (EvaluateTypeEnum.Online.getCode().equals(evaluateType) || EvaluateTypeEnum.Offline.getCode().equals(evaluateType)) {
                    basketIds = baseMapper.getBasketsOfNew(r.getSubId());
                    if (basketIds != null && basketIds.size() != 0) {
                        EvaluateProductCommentVO evaluateProductCommentVO = new EvaluateProductCommentVO();
                        evaluateProductCommentVO.setBasketIds(basketIds);
                        OaUserBO currentStaffId = abstractCurrentRequestComponent.getCurrentStaffId();
                        Integer xTenant = currentStaffId == null ? (int)Namespaces.get() : currentStaffId.getXTenant();
                        // com.jiuji.oa.oacore.oaorder.client.vo.Result<List<ProductCommentAggBO>> evaluateProductCommentInfo = evaluateProductClient.getEvaluateProductCommentInfo(xTenant, evaluateProductCommentVO);
                        //if (evaluateProductCommentInfo.getCode() == 0 && evaluateProductCommentInfo.getData() != null && evaluateProductCommentInfo.getData().size() != 0) {
                        //    double avg = evaluateProductCommentInfo.getData().stream().mapToInt(e -> e.getReviews().getStar()).average().orElse(0);
                        //    r.setProductScore(Double.valueOf(0).equals(avg) ? null : avg);
                        //}
                    }
                }

                // 良品评价商品平均分
               if (EvaluateTypeEnum.Liangpin.getCode().equals(evaluateType)) {
                   String url = "";
                   R<String> urlR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.WEB_URL, (int) Namespaces.get());
                   if (CommonUtils.isRequestSuccess(urlR)) {
                       url = urlR.getData();
                   }
                   url = url + "/hsapi/vueapi/getCommentList?subId={subId}&commentCategory=1";

                   String object = restTemplate.getForObject(url, String.class, r.getSubId());
                   LiangPinCommentRes liangPinCommentRes = JSONUtil.toBean(object, LiangPinCommentRes.class);
                   Double avg = liangPinCommentRes.getData().stream().mapToInt(e -> e.getStar()).average().orElse(0);
                   r.setProductScore(Double.valueOf(0).equals(avg) ? null : avg);

               }
               // 备用机商品评价
                if (EvaluateTypeEnum.STANDBY.getCode().equals(evaluateType) && r.getStandbyId() != null) {
                    // -1代表勾选的未借用备用机
                    Integer standbyScore = Optional.ofNullable(r.getStandbyScore()).orElse(0);
                    r.setProductScore(Double.valueOf(standbyScore == -1 ? 0 : standbyScore));
                }
                log.info(basketIds.toString());
            }
        });
    }

    /**
     * 处理searchkey和searchValue
     * @param req
     * @param exQuery
     * @return
     */
    private boolean handleSearchKeyValue(EvaluateListQuery req, EvaluateListExQuery exQuery) {
        exQuery.setTrimSearchValue(StrUtil.trim(req.getSearchValue()));
        if (req.getSearchKey() != null && StringUtils.isNotEmpty(exQuery.getTrimSearchValue())) {
            exQuery.setIsIntSearchValue(NUM_PATTERN.matcher(exQuery.getTrimSearchValue()).matches());
            // sarchKey为2是电话号码
            if (Boolean.TRUE.equals(exQuery.getIsIntSearchValue()) && !Objects.equals(req.getSearchKey(), 2)) {
                exQuery.setIntSearchValue(Integer.parseInt(exQuery.getTrimSearchValue()));
            } else {
                exQuery.setIntSearchValue(null);
            }
            if (req.getSearchKey() == 7) {
                Integer ch999Id = transUserId(exQuery.getTrimSearchValue());
                if (ch999Id == 0) {
                    return false;
                } else {
                    exQuery.setCh999Id(ch999Id);
                }
            }
            if (req.getSearchKey() == 9 || req.getSearchKey() == 10) {
                List<Integer> ppids = new ArrayList<>();
                String[] strArray = exQuery.getTrimSearchValue().split(",");
                for(String str : strArray) {
                    if (NUM_PATTERN.matcher(str).matches()) {
                        ppids.add(Integer.parseInt(str));
                    } else {
                        return false;
                    }
                }
                exQuery.setPpids(ppids);
            }
        }
        return true;
    }

    private Integer transUserId(String value) {
        if (NUM_PATTERN.matcher(value).matches()) {
            return Integer.parseInt(value);
        } else {
            R<Ch999UserVo> userVoR = userInfoClient.getCh999UserByUserName(value);
            if (userVoR.getCode() == Result.SUCCESS && userVoR.getData() != null) {
                Ch999UserVo userVo = userVoR.getData();
                return userVo.getCh999Id();
            } else {
                return 0;
            }
        }
    }

    /**
     * 设置非五星扣分
     *
     * @param evaluate 评价列表数据
     */
    private void setNonFiveStarDeduct(EvaluateListVO evaluate) {
        evaluate.setNonFiveStarDeduct(BigDecimal.ZERO);
        if (InnerProfileJudgeUtil.isSaas()
                || CollectionUtils.isEmpty(evaluate.getJobList())) {
            return;
        }
        // 扣分规则，1 星，2 星，3 星
        int[] deductRule;
        // 钻石会员等级
        List<Integer> diamondUserClassList = Arrays.asList(EUserClassNewEnum.EUserClassNew_5.getCode(),
                EUserClassNewEnum.EUserClassNew_6.getCode());
        if (Objects.nonNull(evaluate.getUserClass())
                && diamondUserClassList.contains(evaluate.getUserClass())) {
            deductRule = new int[]{200, 150, 100};
        } else {
            deductRule = new int[]{100, 80, 50};
        }
        evaluate.setUserClassName(EnumUtil.getMessageByCode(EUserClassNewEnum.class, evaluate.getUserClass()));
        BigDecimal nonFiveStarDeduct = evaluate.getJobList().stream()
                .map(job -> {
                    Integer score = job.getScore();
                    if (Objects.isNull(score)) {
                        return BigDecimal.ZERO;
                    }
                    switch (score) {
                        case 1:
                        case 2:
                        case 3:
                            return BigDecimal.valueOf(deductRule[score - 1]);
                        default:
                            break;
                    }
                    return BigDecimal.ZERO;
                })
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        evaluate.setNonFiveStarDeduct(nonFiveStarDeduct);
    }

    @Override
    @DS("oanew")
    public R<List<EvaluateJobScoreVO>> getStaffScoreRedpack(Integer evaluateId) {
        if (evaluateId == null) {
            return R.error("参数不合法");
        }
        List<EvaluateJobScoreVO> evaluateJobScoreVOS = baseMapper.getEvaluateJobScoreByEvaluateId(evaluateId, jiujiSystemProperties.getOfficeName());
        if (CollectionUtils.isEmpty(evaluateJobScoreVOS)) {
            return R.success(new ArrayList<>());
        }
        evaluateJobScoreVOS.forEach(item -> {
            item.setJobName(EnumUtil.getMessageByCode(EvaluateJobEnum.class, item.getJob()));
        });
        return R.success(evaluateJobScoreVOS);
    }

    @Override
    @DS("oanew")
    public R addStaffScoreRedpack(OaUserBO oaUserBO, EvaluateRedPackAddReq req) {
        if (!oaUserBO.getRank().contains("kp")) {
            return R.error("你没有发放客评积分红包的权限");
        }
        List<EvaluateRedPackSubReq> subList = ActiveProfileJudgeUtil.isJiuJiEnvironment() ?
                req.getSubList().stream().filter(redPackReq -> Objects.nonNull(redPackReq.getPoints()) && redPackReq.getPoints() > 0).collect(Collectors.toList()) : req.getSubList();
        subList.forEach(redPackReq -> {
            if (ActiveProfileJudgeUtil.isJiuJiEnvironment()) {
                if (redPackReq.getPoints() > 500) {
                    throw new CustomizeException("输入金额不能大于500，请重新输入");
                }
            } else {
                if (Objects.isNull(redPackReq.getKind())) {
                    throw new CustomizeException("请选择发放类型");
                }
            }
        });
        List<Integer> scoreIds = subList.stream().map(EvaluateRedPackSubReq::getId).collect(Collectors.toList());
        List<EvaluateJobScoreVO> evaluateJobScoreList = baseMapper.getEvaluateJobScoreByScoreIds(scoreIds, jiujiSystemProperties.getOfficeName());
        List<EvaluateJobScoreVO> list = evaluateJobScoreList.stream().filter(h -> h.getFen() == null).collect(Collectors.toList());
        List<EvaluateRedPackSubReq> reqEvaluateList = subList.stream().filter(h->{
            long count = list.stream().filter(s -> s.getId().equals(h.getId())).count();
            return count != 0;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return R.error("该岗位已经发过积分，请勿重复发放！");
        }
        if (list.stream().anyMatch(o -> o.getFen() != null)) {
            return R.error("已存在客评积分红包记录，不能重复发放");
        }
        Map<Integer, EvaluateJobScoreVO> map = list.stream().collect(Collectors.toMap(EvaluateJobScoreVO::getId, k->k, (k1, k2)->k1));
        // 保存进积分
        List<AddUserFenReq> reqList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        Evaluate evaluate = ((EvaluateService)AopContext.currentProxy()).getEvaluate(evaluateJobScoreList.get(0).getEvaluateId());
        for(EvaluateRedPackSubReq k : reqEvaluateList) {
            EvaluateJobScoreVO score = map.get(k.getId());
            if (score == null) {
                return R.error("积分不能为空");
            }
            RedPackCommentEnum redPackCommentEnumByCode = RedPackCommentEnum.getRedPackCommentEnumByCode(k.getKind());
            Integer points;
            if (redPackCommentEnumByCode == null && !ActiveProfileJudgeUtil.isJiuJiEnvironment()) {
                continue;
            }
            if (ActiveProfileJudgeUtil.isJiuJiEnvironment()) {
                points = k.getPoints();
            } else {
                assert redPackCommentEnumByCode != null;
                points = redPackCommentEnumByCode.getFen();
            }
            reqList.add(new AddUserFenReq().setUserId(score.getUserId()).setRemark(k.getRemark())
                    .setFen(points).setInUser(oaUserBO.getUserName()).setBumenId(score.getId())
                    .setKind(k.getKind()).setJifenType(StaffJifenTypeEnum.KPJFHB.getCode()).setScore(score.getScore()).setUserName(score.getUserName()));
            // 发积分添加进程（xxx（登录人姓名）给岗位【xxx】xxx （人员姓名）发了200积分，备注：xxxxxx）
            sb.append(String.format("%s给岗位【%s】%s发了%s积分，备注：%s；", oaUserBO.getUserName(), EnumUtil.getMessageByCode(EvaluateJobEnum.class, score.getJob()),
                    score.getUserName(), points, k.getRemark()));
        }
        staffScoreRedpackService.insertBatchUserFen(reqList);
        // 保存发送记录及微信通知
        staffScoreRedpackService.insertBatchRedPack(reqList, oaUserBO);
        // 更新进程
        evaluate.setProcess((evaluate.getProcess() == null ? "" : evaluate.getProcess()) + String.format("%s|%s|%s<br>",
                oaUserBO.getUserName(), LocalDateTime.now().format(PROCESS_TIME_FORMAT), sb));
        ((EvaluateService)AopContext.currentProxy()).updateEvaluate(evaluate);
        return R.success("发送成功!");
    }

    @Override
    public R exportExcel(EvaluateListQuery req, HttpServletResponse response) {
        List<String> columns = req.getColumns();
        if (columns == null || columns.size() < 1) {
            throw new RuntimeException("请选择要导出的列");
        }

        TreeMap<Integer, String> treeMap = new TreeMap<>();
        List<String> only9jiColumns = Lists.newArrayList(
                ChooseColumnsEnum.REWARD_POINTS.getEn(),
                ChooseColumnsEnum.REWARD_REA_PACKET.getEn(),
                ChooseColumnsEnum.OVER_FIVE_FLAG_TEXT.getEn(),
                ChooseColumnsEnum.POINTS_FLAG_TEXT.getEn(),
                ChooseColumnsEnum.RED_PACKET_FLAG_TEXT.getEn());
        columns.forEach(column ->{
            if (XtenantEnum.isJiujiXtenant() && (ChooseColumnsEnum.AREA_SCORE.getEn().equals(column) || ChooseColumnsEnum.AREA_TAG.getEn().equals(column))) {
                return;
            }
            if (!XtenantEnum.isJiujiXtenant() && only9jiColumns.contains(column)) {
                return;
            }
            treeMap.put(ChooseColumnsEnum.getRankByEn(column), column);
        });
        Collection<String> columnsBySort = treeMap.values();

        R<Page<EvaluateListVO>> r = getEvaluateList(req, new HashMap<>(128),true);
        if (ResultCode.SUCCESS != r.getCode()) {
            return R.error(r.getUserMsg());
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
        List<EvaluateJobScoreVO> jobList;
        List<List<Object>> cells = new ArrayList<>();
        if (r.getData() != null && r.getData().getRecords() != null) {
            for (EvaluateListVO m : r.getData().getRecords()) {
                jobList = m.getJobList();
                //不为空才处理
                if (CollectionUtils.isNotEmpty(jobList)) {
                    jobList.forEach(job -> {
                        if(CollectionUtils.isNotEmpty(m.getEvaluateDepartNameList())&&columnsBySort.contains(ChooseColumnsEnum.DEPART.getEn())){
                            m.getEvaluateDepartNameList().forEach(departName->{
                                ArrayList<Object> cell = new ArrayList<>();
                                if (columnsBySort.contains(ChooseColumnsEnum.ID.getEn())) {
                                    if (m.getId() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getId());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.AREA.getEn())) {
                                    if (m.getArea() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getArea());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.TYPE.getEn())) {
                                    if (m.getEvaluateType() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(EvaluateTypeEnum.getEvaluateTypeMessage(m.getEvaluateType()));
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.GUIDE.getEn())) {
                                    if (m.getIsGuide() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add((m.getIsGuide()) ? "是" : "否");
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.SUB.getEn())) {
                                    if (m.getSubId() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getSubId().toString());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.TIME.getEn())) {
                                    if (m.getDTime() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getDTime().format(df));
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.CLASS.getEn())) {
                                    if (m.getUserClassName() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getUserClassName());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.SERVICE_STAFF.getEn())) {
                                    if (m.getUserClassName() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(String.format("%s：%s(%s)",job.getJobName(),job.getUserName(),job.getUserId()));
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.SERVICE_SCORE.getEn())) {
                                    if (job.getScore() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(String.format("%s", job.getScore()));
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.SERVICE_TAG.getEn())) {
                                    if (m.getJobTags() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getJobTags());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.AREA_SCORE.getEn())) {
                                    if (m.getAreaScore() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getAreaScore());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.AREA_TAG.getEn())) {
                                    if (m.getAreaTags() != null && m.getAreaTags().size() > 0) {
                                        List<String> tagList = m.getAreaTags().stream().map(EvaluateScoreTagVO::getName).collect(Collectors.toList());
                                        String tagListStr = CollectionUtil.join(tagList, "、");
                                        cell.add(tagListStr);
                                    }else {
                                        cell.add("-");
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.PRODUCT_SCORE.getEn())) {
                                    if (m.getProductScore() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getProductScore());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.COMMENDED_SCORE.getEn())) {
                                    if (m.getCommendedScore() == null || m.getCommendedScore() == -1) {
                                        cell.add("-");
                                    } else {
                                        cell.add(m.getCommendedScore().toString());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.CONTENT.getEn())) {
                                    if (m.getContent() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getContent());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.AREA_REMARK.getEn())) {
                                    if (m.getAreaRemark() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getAreaRemark());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.EVALUATE_TAG.getEn())) {
                                    if (m.getEvaluateTag() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(EvaluateTagTypeEnum.getMessageByCode(m.getEvaluateTag()));
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.SOURCE.getEn())) {
                                    if (m.getSourceFromName() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getSourceFromName());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.ACTION.getEn())) {
                                    cell.add("-");
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.ILLEGAL.getEn())) {
                                    if (m.getIsIllegal() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add((m.getIsIllegal()) ? "违规" : "正常");
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.SHOW.getEn())) {
                                    if (m.getIsShow() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add((m.getIsShow()) ? "是" : "否");
                                    }
                                }
                                cell.add(departName);
                                if (columnsBySort.contains(ChooseColumnsEnum.REASON.getEn())) {
                                    if (m.getCateNames() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getCateNames());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.PROCESSUSER.getEn())) {
                                    if (m.getProcessUser() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getProcessUser());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.FAVOURABLE_TEXT.getEn())) {
                                    if (org.apache.commons.lang3.StringUtils.isEmpty(m.getFavourableText())) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getFavourableText());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.NON_FIVE_STAR_POINT.getEn())) {
                                    if (m.getNonFiveStarDeduct() == null) {
                                        cell.add("-");
                                    } else {
                                        cell.add(m.getNonFiveStarDeduct());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.REWARD_POINTS.getEn())) {
                                    if (Objects.isNull(m.getRewardPoints())) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getRewardPoints());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.REWARD_REA_PACKET.getEn())) {
                                    if (Objects.isNull(m.getRewardReaPacket())) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getRewardReaPacket());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.OVER_FIVE_FLAG_TEXT.getEn())) {
                                    if (org.apache.commons.lang3.StringUtils.isEmpty(m.getOverFiveFlagText())) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getOverFiveFlagText());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.POINTS_FLAG_TEXT.getEn())) {
                                    if (org.apache.commons.lang3.StringUtils.isEmpty(m.getPointsFlagText())) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getPointsFlagText());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.RED_PACKET_FLAG_TEXT.getEn())) {
                                    if (org.apache.commons.lang3.StringUtils.isEmpty(m.getRedPacketFlagText())) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getRedPacketFlagText());
                                    }
                                }
                                cells.add(cell);
                            });
                        }else{
                            ArrayList<Object> cell = new ArrayList<>();
                            if (columnsBySort.contains(ChooseColumnsEnum.ID.getEn())) {
                                if (m.getId() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getId());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.AREA.getEn())) {
                                if (m.getArea() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getArea());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.TYPE.getEn())) {
                                if (m.getEvaluateType() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(EvaluateTypeEnum.getEvaluateTypeMessage(m.getEvaluateType()));
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.GUIDE.getEn())) {
                                if (m.getIsGuide() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add((m.getIsGuide()) ? "是" : "否");
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.SUB.getEn())) {
                                if (m.getSubId() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getSubId().toString());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.TIME.getEn())) {
                                if (m.getDTime() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getDTime().format(df));
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.CLASS.getEn())) {
                                if (m.getUserClassName() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getUserClassName());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.SERVICE_STAFF.getEn())) {
                                if (m.getUserClassName() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(String.format("%s：%s(%s)",job.getJobName(),job.getUserName(),job.getUserId()));
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.SERVICE_SCORE.getEn())) {
                                if (job.getScore() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(String.format("%s", job.getScore()));
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.SERVICE_TAG.getEn())) {
                                if (m.getJobTags() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getJobTags());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.AREA_SCORE.getEn())) {
                                if (m.getAreaScore() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getAreaScore());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.AREA_TAG.getEn())) {
                                if (m.getAreaTags() != null && m.getAreaTags().size() > 0) {
                                    List<String> tagList = m.getAreaTags().stream().map(EvaluateScoreTagVO::getName).collect(Collectors.toList());
                                    String tagListStr = CollectionUtil.join(tagList, "、");
                                    cell.add(tagListStr);
                                }else {
                                    cell.add("-");
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.PRODUCT_SCORE.getEn())) {
                                if (m.getProductScore() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getProductScore());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.COMMENDED_SCORE.getEn())) {
                                if (m.getCommendedScore() == null || m.getCommendedScore() == -1) {
                                    cell.add("-");
                                } else {
                                    cell.add(m.getCommendedScore().toString());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.CONTENT.getEn())) {
                                if (m.getContent() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getContent());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.AREA_REMARK.getEn())) {
                                if (m.getAreaRemark() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getAreaRemark());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.EVALUATE_TAG.getEn())) {
                                if (m.getEvaluateTag() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(EvaluateTagTypeEnum.getMessageByCode(m.getEvaluateTag()));
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.SOURCE.getEn())) {
                                if (m.getSourceFromName() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getSourceFromName());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.ACTION.getEn())) {
                                cell.add("-");
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.ILLEGAL.getEn())) {
                                if (m.getIsIllegal() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add((m.getIsIllegal()) ? "违规" : "正常");
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.SHOW.getEn())) {
                                if (m.getIsShow() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add((m.getIsShow()) ? "是" : "否");
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.DEPART.getEn())) {
                                if (m.getEvaluateDepartNames() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getEvaluateDepartNames());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.REASON.getEn())) {
                                if (m.getCateNames() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getCateNames());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.PROCESSUSER.getEn())) {
                                if (m.getProcessUser() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getProcessUser());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.FAVOURABLE_TEXT.getEn())) {
                                if (org.apache.commons.lang3.StringUtils.isEmpty(m.getFavourableText())) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getFavourableText());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.NON_FIVE_STAR_POINT.getEn())) {
                                if (m.getNonFiveStarDeduct() == null) {
                                    cell.add("-");
                                } else {
                                    cell.add(m.getNonFiveStarDeduct());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.REWARD_POINTS.getEn())) {
                                if (Objects.isNull(m.getRewardPoints())) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getRewardPoints());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.REWARD_REA_PACKET.getEn())) {
                                if (Objects.isNull(m.getRewardReaPacket())) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getRewardReaPacket());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.OVER_FIVE_FLAG_TEXT.getEn())) {
                                if (org.apache.commons.lang3.StringUtils.isEmpty(m.getOverFiveFlagText())) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getOverFiveFlagText());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.POINTS_FLAG_TEXT.getEn())) {
                                if (org.apache.commons.lang3.StringUtils.isEmpty(m.getPointsFlagText())) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getPointsFlagText());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.RED_PACKET_FLAG_TEXT.getEn())) {
                                if (org.apache.commons.lang3.StringUtils.isEmpty(m.getRedPacketFlagText())) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getRedPacketFlagText());
                                }
                            }
                            cells.add(cell);
                        }
                    });
                } else {
                    {
                        if(CollectionUtils.isNotEmpty(m.getEvaluateDepartNameList())&&columnsBySort.contains(ChooseColumnsEnum.DEPART.getEn())){
                            m.getEvaluateDepartNameList().forEach(departName->{
                                ArrayList<Object> cell = new ArrayList<>();
                                if (columnsBySort.contains(ChooseColumnsEnum.ID.getEn())) {
                                    if (m.getId() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getId());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.AREA.getEn())) {
                                    if (m.getArea() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getArea());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.TYPE.getEn())) {
                                    if (m.getEvaluateType() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(EvaluateTypeEnum.getEvaluateTypeMessage(m.getEvaluateType()));
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.GUIDE.getEn())) {
                                    if (m.getIsGuide() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add((m.getIsGuide()) ? "是" : "否");
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.SUB.getEn())) {
                                    if (m.getSubId() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getSubId().toString());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.TIME.getEn())) {
                                    if (m.getDTime() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getDTime().format(df));
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.CLASS.getEn())) {
                                    if (m.getUserClassName() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getUserClassName());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.SERVICE_STAFF.getEn())) {
                                    cell.add("-");
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.SERVICE_SCORE.getEn())) {
                                    cell.add("-");
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.SERVICE_TAG.getEn())) {
                                    if (m.getJobTags() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getJobTags());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.AREA_SCORE.getEn())) {
                                    if (m.getAreaScore() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getAreaScore());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.AREA_TAG.getEn())) {
                                    if (m.getAreaTags() != null && m.getAreaTags().size() > 0) {
                                        List<String> tagList = m.getAreaTags().stream().map(EvaluateScoreTagVO::getName).collect(Collectors.toList());
                                        String tagListStr = CollectionUtil.join(tagList, "、");
                                        cell.add(tagListStr);
                                    }else {
                                        cell.add("-");
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.PRODUCT_SCORE.getEn())) {
                                    if (m.getProductScore() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getProductScore());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.COMMENDED_SCORE.getEn())) {
                                    if (m.getCommendedScore() == null || m.getCommendedScore() == -1) {
                                        cell.add("-");
                                    } else {
                                        cell.add(m.getCommendedScore().toString());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.CONTENT.getEn())) {
                                    if (m.getContent() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getContent());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.AREA_REMARK.getEn())) {
                                    if (m.getAreaRemark() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getAreaRemark());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.EVALUATE_TAG.getEn())) {
                                    if (m.getEvaluateTag() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(EvaluateTagTypeEnum.getMessageByCode(m.getEvaluateTag()));
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.SOURCE.getEn())) {
                                    if (m.getSourceFromName() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getSourceFromName());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.ACTION.getEn())) {
                                    cell.add("-");
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.ILLEGAL.getEn())) {
                                    if (m.getIsIllegal() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add((m.getIsIllegal()) ? "违规" : "正常");
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.SHOW.getEn())) {
                                    if (m.getIsShow() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add((m.getIsShow()) ? "是" : "否");
                                    }
                                }
                                cell.add(departName);
                                if (columnsBySort.contains(ChooseColumnsEnum.REASON.getEn())) {
                                    if (m.getCateNames() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getCateNames());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.PROCESSUSER.getEn())) {
                                    if (m.getProcessUser() == null) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getProcessUser());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.FAVOURABLE_TEXT.getEn())) {
                                    if (org.apache.commons.lang3.StringUtils.isEmpty(m.getFavourableText())) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getFavourableText());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.NON_FIVE_STAR_POINT.getEn())) {
                                    if (m.getNonFiveStarDeduct() == null) {
                                        cell.add("-");
                                    } else {
                                        cell.add(m.getNonFiveStarDeduct());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.REWARD_POINTS.getEn())) {
                                    if (Objects.isNull(m.getRewardPoints())) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getRewardPoints());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.REWARD_REA_PACKET.getEn())) {
                                    if (Objects.isNull(m.getRewardReaPacket())) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getRewardReaPacket());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.OVER_FIVE_FLAG_TEXT.getEn())) {
                                    if (org.apache.commons.lang3.StringUtils.isEmpty(m.getOverFiveFlagText())) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getOverFiveFlagText());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.POINTS_FLAG_TEXT.getEn())) {
                                    if (org.apache.commons.lang3.StringUtils.isEmpty(m.getPointsFlagText())) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getPointsFlagText());
                                    }
                                }
                                if (columnsBySort.contains(ChooseColumnsEnum.RED_PACKET_FLAG_TEXT.getEn())) {
                                    if (org.apache.commons.lang3.StringUtils.isEmpty(m.getRedPacketFlagText())) {
                                        cell.add("-");
                                    }else {
                                        cell.add(m.getRedPacketFlagText());
                                    }
                                }
                                cells.add(cell);
                            });
                        }else{
                            ArrayList<Object> cell = new ArrayList<>();
                            if (columnsBySort.contains(ChooseColumnsEnum.ID.getEn())) {
                                if (m.getId() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getId());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.AREA.getEn())) {
                                if (m.getArea() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getArea());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.TYPE.getEn())) {
                                if (m.getEvaluateType() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(EvaluateTypeEnum.getEvaluateTypeMessage(m.getEvaluateType()));
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.GUIDE.getEn())) {
                                if (m.getIsGuide() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add((m.getIsGuide()) ? "是" : "否");
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.SUB.getEn())) {
                                if (m.getSubId() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getSubId().toString());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.TIME.getEn())) {
                                if (m.getDTime() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getDTime().format(df));
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.CLASS.getEn())) {
                                if (m.getUserClassName() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getUserClassName());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.SERVICE_STAFF.getEn())) {
                                cell.add("-");
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.SERVICE_SCORE.getEn())) {
                                cell.add("-");
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.SERVICE_TAG.getEn())) {
                                if (m.getJobTags() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getJobTags());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.AREA_SCORE.getEn())) {
                                if (m.getAreaScore() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getAreaScore());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.AREA_TAG.getEn())) {
                                if (m.getAreaTags() != null && m.getAreaTags().size() > 0) {
                                    List<String> tagList = m.getAreaTags().stream().map(EvaluateScoreTagVO::getName).collect(Collectors.toList());
                                    String tagListStr = CollectionUtil.join(tagList, "、");
                                    cell.add(tagListStr);
                                }else {
                                    cell.add("-");
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.PRODUCT_SCORE.getEn())) {
                                if (m.getProductScore() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getProductScore());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.COMMENDED_SCORE.getEn())) {
                                if (m.getCommendedScore() == null || m.getCommendedScore() == -1) {
                                    cell.add("-");
                                } else {
                                    cell.add(m.getCommendedScore().toString());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.CONTENT.getEn())) {
                                if (m.getContent() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getContent());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.AREA_REMARK.getEn())) {
                                if (m.getAreaRemark() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getAreaRemark());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.EVALUATE_TAG.getEn())) {
                                if (m.getEvaluateTag() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(EvaluateTagTypeEnum.getMessageByCode(m.getEvaluateTag()));
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.SOURCE.getEn())) {
                                if (m.getSourceFromName() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getSourceFromName());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.ACTION.getEn())) {
                                cell.add("-");
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.ILLEGAL.getEn())) {
                                if (m.getIsIllegal() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add((m.getIsIllegal()) ? "违规" : "正常");
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.SHOW.getEn())) {
                                if (m.getIsShow() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add((m.getIsShow()) ? "是" : "否");
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.DEPART.getEn())) {
                                if (m.getEvaluateDepartNames() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getEvaluateDepartNames());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.REASON.getEn())) {
                                if (m.getCateNames() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getCateNames());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.PROCESSUSER.getEn())) {
                                if (m.getProcessUser() == null) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getProcessUser());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.FAVOURABLE_TEXT.getEn())) {
                                if (org.apache.commons.lang3.StringUtils.isEmpty(m.getFavourableText())) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getFavourableText());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.NON_FIVE_STAR_POINT.getEn())) {
                                if (m.getNonFiveStarDeduct() == null) {
                                    cell.add("-");
                                } else {
                                    cell.add(m.getNonFiveStarDeduct());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.REWARD_POINTS.getEn())) {
                                if (Objects.isNull(m.getRewardPoints())) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getRewardPoints());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.REWARD_REA_PACKET.getEn())) {
                                if (Objects.isNull(m.getRewardReaPacket())) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getRewardReaPacket());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.OVER_FIVE_FLAG_TEXT.getEn())) {
                                if (org.apache.commons.lang3.StringUtils.isEmpty(m.getOverFiveFlagText())) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getOverFiveFlagText());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.POINTS_FLAG_TEXT.getEn())) {
                                if (org.apache.commons.lang3.StringUtils.isEmpty(m.getPointsFlagText())) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getPointsFlagText());
                                }
                            }
                            if (columnsBySort.contains(ChooseColumnsEnum.RED_PACKET_FLAG_TEXT.getEn())) {
                                if (org.apache.commons.lang3.StringUtils.isEmpty(m.getRedPacketFlagText())) {
                                    cell.add("-");
                                }else {
                                    cell.add(m.getRedPacketFlagText());
                                }
                            }
                            cells.add(cell);
                        }
                    }
                }
            };
        }
        String fileName = "客评列表数据导出-"+ LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMddHHmm")) +".xls";
        try {
            ExcelUtils.exportByEasyExcel(fileName, cells,response,columnsBySort);
        } catch (Exception e) {
            log.error("客评列表数据导出失败：{}", e.getMessage());
            return R.error("导出失败" + e.getMessage());
        }
        return R.success("导出成功");
    }

    @Override
    public R<List<EvaluateRedPackVO>> getEvaluateRedPackData(EvaluateRedPackReq req) {
        return R.success(baseMapper.getEvaluateRedPackList(req, jiujiSystemProperties.getOfficeName()));
    }

    @Override
    public R exportRedpackExcel(EvaluateRedPackReq req, HttpServletResponse response) {
        R<List<EvaluateRedPackVO>> r = getEvaluateRedPackData(req);
        if (ResultCode.SUCCESS != r.getCode()) {
            return R.error("数据获取失败");
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        ArrayList<String> titles = new ArrayList<>(Arrays.asList("客评号", "客评时间", "类别", "大区", "小区", "地区", "岗位", "工号", "员工", "发放额度",
                "发放人", "发放备注"));
        List<List<Object>> datas = r.getData().stream().map(m -> {
            List<Object> cells = new ArrayList<>();
            cells.add(m.getEvaluateId());
            cells.add(m.getDTime().format(df));
            cells.add(EvaluateTypeEnum.getEvaluateTypeMessage(m.getEvaluateType()));
            cells.add(m.getDName2());
            cells.add(m.getDName1());
            cells.add(m.getArea());
            cells.add(EvaluateJobEnum.getEvaluateJobMessage(m.getJob()));
            cells.add(m.getUserId());
            cells.add(m.getUserName());
            cells.add(m.getFen());
            cells.add(m.getInUser());
            cells.add(m.getRemark());
            return cells;
        }).collect(Collectors.toList());
        String fileName = "积分激励数据导出-"+ LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMddHHmm")) +".xls";
        try {
            ExcelUtils.export(response, datas, titles, null,
                    fileName, 1);
        } catch (IOException e) {
            log.error("积分激励数据导出：{}", e.getMessage());
            return R.error("积分激励数据导出失败" + e.getMessage());
        }
        return R.success("积分激励数据导出成功");
    }

    @Override
    public R<EvaluateDetailVO> getEvaluateDetail(Integer evaluateId) {
        // 获取客评基础信息
        R<EvaluateDetailVO> r = evaluateClient.getEvaluateDetailById(evaluateId);
        // 设置心声社区信息
        // 设置客评打分 根据evaluateId获取EvaluateScore
        // 计算平均分等信息
        // 如果是售后单 设置订单手机号
        return R.success("test", r.getData());
    }

    @Override
    @DS("oanew")
    public R<Boolean> sendMa(SendMaReq req) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        Evaluate evaluate = ((EvaluateService)AopContext.currentProxy()).getEvaluate(req.getEvaluateId());
        if (evaluate == null) {
            return R.error("客户评价不存在");
        }
//        if (EvaluateStatusEnum.EvaluateStatusYHC.getCode().equals(evaluate.getEvaluateStatus())) {
//            return R.error("客评已完成！");
//        }
        if (evaluate.getUserId() == null || evaluate.getUserId() == 0) {
            return R.error("订单客户信息记录有误");
        }
        BbsxpUsers bbsxpUsers =
                bbsxpUsersMapper.getInfoByUserId(new LambdaUpdateWrapper<BbsxpUsers>().eq(BbsxpUsers::getId,
                        evaluate.getUserId()));
        if (bbsxpUsers == null) {
            return R.error("订单客户信息记录有误");
        }
/*        Sub sub = subService.getSub(evaluate.getSubId());
        if (sub == null) {
            return R.error("没有对应的订单");
        }
        R<AreaInfo> areaRes = areaInfoClient.getAreaInfoById(sub.getAreaid());
        if (ResultCode.SUCCESS != areaRes.getCode() || areaRes.getData() == null) {
            return R.error("门店查询失败");
        }
        if (!Boolean.TRUE.equals(areaRes.getData().getIsSend()) ||
                !JIUJI_PRINT_NAME.equals(areaRes.getData().getPrintName())) {
            return R.error("所在门店不符合要求");
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(sub.getSubMobile()) && (evaluate.getUserId() == null || evaluate.getUserId() == 0)) {
            return R.error("订单客户信息记录有误");
        }*/
        NumberCardAddReq addReq = new NumberCardAddReq();
        String gname = "评价订单号(" + evaluate.getSubId() + ")";
        addReq.setGname(gname);
        addReq.setCount(1);
        addReq.setTotal(req.getPrice());
        addReq.setStarttime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        addReq.setEndtime(req.getEndTime() + "");
        addReq.setLimit1("3");
        addReq.setLimit2("0");
        addReq.setLimitprice(req.getPrice().toString());
        addReq.setIsdjq("1");
        addReq.setUserid(evaluate.getUserId());
        String code = numberCardService.getRunNumber(addReq, "系统", oaUserBO.getXTenant());
        if (StringUtils.isEmpty(code)) {
            String userMsg = CommonUtil.consumeLocalThreadMessage("userMsg");
            return R.error(Optional.ofNullable(userMsg).orElse("生成代金券失败"));
        }

        code = code.replace(",", "");

        String content = String.format("发送优惠码：%s，金额：%s，结束日期：%s", code.substring(0, 2) + "**" + code.substring(code.length() - 2), req.getPrice(), req.getEndTime());
/*        if (req.getIsWx() && req.getIsSms()) {
            content += "（短信 微信通知）";
        } else if (req.getIsSms()) {
            content += "（短信通知）";
        } else if (req.getIsWx()) {
            content += "（微信通知）";
        }*/

        evaluate.setProcess((evaluate.getProcess() == null ? "" : evaluate.getProcess()) + String.format("%s|%s|%s<br>",
                oaUserBO.getUserName(), LocalDateTime.now().format(PROCESS_TIME_FORMAT), content));
        ((EvaluateService)AopContext.currentProxy()).updateEvaluate(evaluate);
        // 目前评价发送优惠券只有九机有，文案先先死，后续输出有再调
        if (org.apache.commons.lang3.StringUtils.isNotBlank(bbsxpUsers.getMobile()) && Boolean.TRUE.equals(req.getIsSms())) {
            String msg = String.format("尊敬的顾客您好，因员工给您提供的服务违背九机网“一个手机、一个朋友”的经营理念，给您带来的不快深表歉意，" +
                            "特向您赠送一张%s元代金券[%s]，有效期至%s，可至九机app个人中心-优惠券查看具体使用条件，望您继续监督我们的服务，祝您生活愉快。",
                    req.getPrice().toString(), code, req.getEndTime());
            String url = smsProperties.getUrl();
            CommonUtil.sendSms(bbsxpUsers.getMobile(), msg, "9", "系统", url);
        }

        // 给评价人员推送APP消息（仅九机）
        if (Boolean.TRUE.equals(req.getIsApp()) && (evaluate.getUserId() != null && evaluate.getUserId() != 0) && InnerProfileJudgeUtil.isJiuJi()) {
            int xtenant = 0;
            if (evaluate.getUserId() != null && evaluate.getUserId() != 0) {
                xtenant = tousuAreaMapper.getXtenant(evaluate.getUserId());
            }

            XtenantSubject xtenantSubject = touSuService.getXtenantSubject(xtenant);
            // 推送点击详情跳转优惠码详情
            String url = xtenantSubject.getMUrl() + "/member/coupon/detail?coupon=" + code + "&codeTitle=评价专享优惠";
            String msg = String.format("尊敬的顾客您好，因员工给您提供的服务违背九机网“一个手机、一个朋友”理念，给您带来的不快深表歉意，" +
                            "特向您赠送%s元代金券[%s]，有效期至%s，可至九机app个人中心——我的优惠劵中查看详情，预祝您购物愉快。", req.getPrice().toString(), code, req.getEndTime());
           // String title = String.format("尊敬的顾客您好，九机对本次给您带来的不佳体验深表歉意，特向您赠送一张%s元代金券", req.getPrice());

            // 商城消息中心
            UserMsg userMsg = new UserMsg().setUserID(evaluate.getUserId())
                    .setTitle("评价专享优惠")
                    .setContent(msg)
                    .setLink(url)
                    .setKindID(12)
                    .setAddTime(new Date());
            HttpRequest.post(WEB_APP_MSG_URL).body(JSON.toJSONString(userMsg)).execute().body();

            // 极光推送
            Map<String, Object> params = new HashMap<String, Object>(16){
                private static final long serialVersionUID = -6571332082347964462L;
                {
                    //  必填，应用名称，和App上报的注册信息保持一致； oa就是oa，商城是web
                    put("appName", "web");
                    // 必填，推送消息的标题，长度不能超过20字符，中英文均算一个字符；
                    put("title", "评价专享优惠");
                    // 必填，推送消息的内容，建议长度不超过50字符，中英文均算一个字符，超过50个字符的，将自动转成前47个字符加上...的形式推送；
                    put("content", msg);
                    // 可选，推送消息的业务附加消息，格式必须为json；
                    put("extra", JSON.toJSONString(new HashMap<String, String>(){
                        private static final long serialVersionUID = -4705437559568650230L;
                        {
                            put("value", "");
                            put("type", "3");
                        }
                    }));
                    // 可选，当使用别名推送消息时，指定该字段，最多100个alias；推送会员id
                    put("alias", Collections.singletonList(evaluate.getUserId()));
                }
            };
            HttpRequest.post(WEB_APP_PUSH_URL).body(JSON.toJSONString(params)).execute().body();
        }
        return R.success("代金券发送成功!", true);
    }

    @Override
    @DS("officeWrite")
    public R<Boolean> updateWuxiao(WuXiaoReq req, String userName) {
        if (req.getScoreKinds() != 1 && req.getScoreKinds() != 2) {
            return R.error(ERROR_MSG_WUXIAO_FENSHU);
        }
        Evaluate evaluate = getById(req.getEvaluateId());
        if (evaluate == null) {
            return R.error("客户评价不存在");
        }
        EvaluateScore scoreJob = evaluateScoreService.lambdaQuery()
                .eq(EvaluateScore::getEvaluateId, req.getEvaluateId())
                .eq(EvaluateScore::getJob, req.getJobId())
                .eq(EvaluateScore::getRelateCh999Id, req.getUserId())
                .one();
        if (scoreJob == null) {
            return R.error("无效的岗位");
        }
        if ((req.getScoreKinds() == 1 && scoreJob.getWuxiao()) || (req.getScoreKinds() != 1 && scoreJob.getWuxiao2())) {
            return R.error("无效客评不能再转无效");
        }
        // 查询员工服务和专业的评价申诉
        List<Integer> appealStatusList = new ArrayList<>();
        List<EvaluateScore> scoreJobList = evaluateScoreService.lambdaQuery()
                .eq(EvaluateScore::getEvaluateId, req.getEvaluateId()).list();
        scoreJobList.forEach(item -> {
            Integer appealStatus1 = item.getAppealStatus1() != null ? item.getAppealStatus1() : 0;
            Integer appealStatus2 = item.getAppealStatus2() != null ? item.getAppealStatus2() : 0;
            appealStatusList.add(appealStatus1);
            appealStatusList.add(appealStatus2);
        });
        // 设置客评状态
        this.setEvaluateTag(appealStatusList,evaluate);

        String scoreKindName = req.getScoreKinds() == 1 ? "服务分" : "专业分";
        String process = String.format("岗位【%s】%s改为无效", EnumUtil.getMessageByCode(EvaluateJobEnum.class, req.getJobId()), scoreKindName);
        evaluate.setProcess((evaluate.getProcess() == null ? "" : evaluate.getProcess()) + String.format("%s|%s|%s<br>",
                userName, LocalDateTime.now().format(PROCESS_TIME_FORMAT), process));
        if (req.getScoreKinds() == 1) {
            scoreJob.setWuxiao(true);
            scoreJob.setAppealStatus1(EvaluateAppealStatusEnum.CHECKED.getCode());
        } else {
            scoreJob.setWuxiao2(true);
            scoreJob.setAppealStatus2(EvaluateAppealStatusEnum.CHECKED.getCode());
        }
        String moaUrl = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.MOA_URL, (int) Namespaces.get()).getData();
        try {
            // 九机低评不扣积分
            if (req.getScoreKinds() == 1 && !ActiveProfileJudgeUtil.isJiuJiEnvironment()) {
                Integer fen = staffScoreRedpackService.getFenByBumenId(scoreJob.getId(), StaffJifenTypeEnum.PJKF.getCode(), false);
                if (fen != null && fen < 0) {
                    String comment = "单号：" + scoreJob.getSubId() + ERROR_MSG_WUXIAO_DIPING + Math.abs(fen);
                    AddUserFenReq addUserFenReq = new AddUserFenReq().setUserId(req.getUserId()).setRemark(comment)
                            .setFen(Math.abs(fen)).setInUser("系统").setBumenId(scoreJob.getId())
                            .setJifenType(StaffJifenTypeEnum.PJKFTH.getCode()).setScore(Math.abs(fen));
                    Integer insertCount = staffScoreRedpackService.insertUserFen(addUserFenReq);
                    evaluate.setProcess((evaluate.getProcess() == null ? "" : evaluate.getProcess()) + String.format("%s|%s|%s<br>",
                            "系统", LocalDateTime.now().format(PROCESS_TIME_FORMAT), comment));
                    if (insertCount > 0) {
                        String msg = "您因" + comment;
                        String link = StrUtil.format(EVALUATE_URL, moaUrl, evaluate.getId());
                        msgPushCloud.simpleSend(String.valueOf(req.getUserId()), msg, link, OaMesTypeEnum.SYSTEM.getCode(), Math.toIntExact(Namespaces.get()));
                        //smsService.sendOaMsg(msg, link, String.valueOf(req.getUserId()), "0");
                    }
                }
            }
            updateById(evaluate);
            evaluateScoreService.updateById(scoreJob);
        } catch (Exception e) {
            log.error("客评设置无效，退回积分异常,评价进程信息：{},堆栈：", evaluate.getProcess(), e);
            throw new CustomizeException("客评设置无效，退回积分异常", ResultCode.SERVER_ERROR);
        }
        return R.success("操作成功", true);
    }

    private void setEvaluateTag(List<Integer> appealStatusList , Evaluate evaluate){
        long count = appealStatusList.stream().filter(item -> item != 0 && item != 3).count();
        // 判断门店是否也是城市经理审核
        int areaAppealStatus = evaluate.getAreaAppealStatus()!=null?evaluate.getAreaAppealStatus():0;
        if (count == 1L && (areaAppealStatus == 0 || areaAppealStatus == 3)) {
            evaluate.setEvaluateTag(null);
        } else {
            long isCityManagerCheck = appealStatusList.stream().filter(item -> item == 1).count();
            if (isCityManagerCheck != 0 || areaAppealStatus == 1) {
                // 服务人员有城市经理审核显示城市经理审核
                evaluate.setEvaluateTag(EvaluateTagTypeEnum.CityManagerCheck.getCode());
            } else {
                // 服务人员有客评组审核显示申诉中
                evaluate.setEvaluateTag(EvaluateTagTypeEnum.Shensu.getCode());
            }
        }
    }

    private static final int SCORE_KIND_1 = 1;
    private static final int SCORE_KIND_2 = 2;
    /**
     * 使用 tcc 模式 重新编排了原来的代码逻辑
     * @param req 请求参数
     * @param userName 用户名
     * @return
     * @throws Exception 执行出错时会发出企业微信告警，并把该异常抛出
     */
    @Override
    @DS("officeWrite")
    public R<Boolean> updateWuxiaoWithTcc(WuXiaoReq req, String userName) throws Exception {
        if (req.getScoreKinds() != SCORE_KIND_1 && req.getScoreKinds() != SCORE_KIND_2) {
            return R.error(ERROR_MSG_WUXIAO_FENSHU);
        }
        Evaluate evaluate = getById(req.getEvaluateId());
        if (evaluate == null) {
            return R.error("客户评价不存在");
        }
        EvaluateScore scoreJob = evaluateScoreService.lambdaQuery()
                .eq(EvaluateScore::getEvaluateId, req.getEvaluateId())
                .eq(EvaluateScore::getJob, req.getJobId())
                .eq(EvaluateScore::getRelateCh999Id, req.getUserId())
                .one();
        if (scoreJob == null) {
            return R.error("无效的岗位");
        }
        boolean isWuxiao = (req.getScoreKinds() == SCORE_KIND_1 && scoreJob.getWuxiao())
                || (req.getScoreKinds() != SCORE_KIND_1 && scoreJob.getWuxiao2());
        if (isWuxiao) {
            return R.error("无效客评不能再转无效");
        }

        // 查询员工服务和专业的评价申诉
        List<Integer> appealStatusList = new ArrayList<>();
        List<EvaluateScore> scoreJobList = evaluateScoreService.lambdaQuery()
                .eq(EvaluateScore::getEvaluateId, req.getEvaluateId()).list();
        scoreJobList.forEach(item -> {
            Integer appealStatus1 = item.getAppealStatus1() != null ? item.getAppealStatus1() : 0;
            Integer appealStatus2 = item.getAppealStatus2() != null ? item.getAppealStatus2() : 0;
            appealStatusList.add(appealStatus1);
            appealStatusList.add(appealStatus2);
        });
        // 设置客评状态
        this.setEvaluateTag(appealStatusList,evaluate);

        try {
            return ((EvaluateServiceImpl) AopContext.currentProxy()).doUpdateWuxiao(req, userName, evaluate, scoreJob);
        } catch (Exception e) {
            //切换配置，并发出告警
            String localIp = NetUtil.getLocalIp();
            StringBuilder alertContent = new StringBuilder(256);
            alertContent.append("order-service : ").append(localIp)
                    .append(" \n EvaluateServiceImpl : updateWuxiao")
                    .append(" \n exception : ").append(e.getClass().getName())
                    .append(':').append(e.getMessage())
                    .append(" \n action : disable seata : true");
            seataAlerter.alert("seata模式错误", alertContent.toString());
            throw e;
        }
    }

    /**
     * GlobalTransactional 开启 seata 分布式事务
     * */
    @DS("officeWrite")
    @GlobalTransactional
    public R<Boolean> doUpdateWuxiao(WuXiaoReq req, String userName, Evaluate evaluate, EvaluateScore scoreJob) throws Exception {
        Evaluate beforeUpdateEvaluate = evaluate;
        Evaluate updateEvaluate = new Evaluate();
        BeanUtils.copyProperties(beforeUpdateEvaluate, updateEvaluate);

        EvaluateScore beforeUpdateScore = scoreJob;
        EvaluateScore updateScore = new EvaluateScore();
        BeanUtils.copyProperties(beforeUpdateScore, updateScore);

        String processContentUpdate = beforeUpdateEvaluate.getProcess();
        if (processContentUpdate == null) {
            processContentUpdate = "";
        }
        String scoreKindName = req.getScoreKinds() == 1 ? "服务分" : "专业分";
        String process = String.format("岗位【%s】%s改为无效", EnumUtil.getMessageByCode(EvaluateJobEnum.class, req.getJobId()), scoreKindName);
        process = String.format("%s|%s|%s<br>",
                userName, LocalDateTime.now().format(PROCESS_TIME_FORMAT), process);
        processContentUpdate = new StringBuffer(1024)
                .append(processContentUpdate)
                .append(process).toString();

        if (req.getScoreKinds() == SCORE_KIND_1) {
            updateScore.setWuxiao(true);
            updateScore.setAppealStatus1(EvaluateAppealStatusEnum.CHECKED.getCode());
        } else {
            updateScore.setWuxiao2(true);
            updateScore.setAppealStatus2(EvaluateAppealStatusEnum.CHECKED.getCode());
        }
        String moaUrl = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.MOA_URL, (int) Namespaces.get()).getData();
        // 九机低评不扣积分
        if (req.getScoreKinds() == SCORE_KIND_1 && !ActiveProfileJudgeUtil.isJiuJiEnvironment()) {
            Integer fen = staffScoreRedpackService.getFenByBumenId(scoreJob.getId(), StaffJifenTypeEnum.PJKF.getCode(), false);
            if (fen != null && fen < 0) {
                String comment = "单号：" + scoreJob.getSubId() + ERROR_MSG_WUXIAO_DIPING + Math.abs(fen);
                AddUserFenReq addUserFenReq = new AddUserFenReq().setUserId(req.getUserId()).setRemark(comment)
                        .setFen(Math.abs(fen)).setInUser("系统").setBumenId(scoreJob.getId())
                        .setJifenType(StaffJifenTypeEnum.PJKFTH.getCode()).setScore(Math.abs(fen));

                comment = String.format("%s|%s|%s<br>", "系统", LocalDateTime.now().format(PROCESS_TIME_FORMAT), comment);
                processContentUpdate = new StringBuffer(1024)
                        .append(processContentUpdate)
                        .append(comment)
                        .toString();

                // 新插入记录的主键id
                Integer fenId = tccStaffScoreRedpackAction.prepared(addUserFenReq);
                if (fenId != null && fenId > 0) {
                    String msg = "您因" + comment;
                    String link = StrUtil.format(EVALUATE_URL, moaUrl,evaluate.getId());
                    msgPushCloud.simpleSend(String.valueOf(req.getUserId()), msg, link, OaMesTypeEnum.SYSTEM.getCode(), Math.toIntExact(Namespaces.get()));
                    //smsService.sendOaMsg(msg, link, String.valueOf(req.getUserId()), "0");
                }
            }
        }
        updateEvaluate.setProcess(processContentUpdate);
        tccEvaluateUpdateAction.prepared(beforeUpdateEvaluate, updateEvaluate, beforeUpdateScore, updateScore);
        return R.success("操作成功", true);
    }

    @Override
    @DS("officeWrite")
    public R<Boolean> updateWuxiaoToYouxiao(WuXiaoReq req, String userName) {
        if (req.getScoreKinds() != 1 && req.getScoreKinds() != 2) {
            return R.error(ERROR_MSG_WUXIAO_FENSHU);
        }
        Evaluate evaluate = getById(req.getEvaluateId());
        if (evaluate == null) {
            return R.error("客户评价不存在");
        }
        EvaluateScore scoreJob = evaluateScoreService.lambdaQuery()
                .eq(EvaluateScore::getEvaluateId, req.getEvaluateId())
                .eq(EvaluateScore::getJob, req.getJobId())
                .eq(EvaluateScore::getRelateCh999Id, req.getUserId())
                .one();
        if (scoreJob == null) {
            return R.error("无效的岗位");
        }
        if ((req.getScoreKinds() == 1 && !scoreJob.getWuxiao()) || (req.getScoreKinds() != 1 && !scoreJob.getWuxiao2())) {
            return R.error("有效客评不能再转有效");
        }

        String scoreKindName = req.getScoreKinds() == 1 ? "服务分" : "专业分";
        String process = String.format("岗位【%s】%s无效改为有效", EnumUtil.getMessageByCode(EvaluateJobEnum.class, req.getJobId()), scoreKindName);
        evaluate.setProcess((evaluate.getProcess() == null ? "" : evaluate.getProcess()) + String.format("%s|%s|%s<br>",
                userName, LocalDateTime.now().format(PROCESS_TIME_FORMAT), process));
        if (req.getScoreKinds() == 1) {
            scoreJob.setWuxiao(false);
            scoreJob.setAppealStatus1(EvaluateAppealStatusEnum.NONE.getCode());
        } else {
            scoreJob.setWuxiao2(false);
            scoreJob.setAppealStatus2(EvaluateAppealStatusEnum.NONE.getCode());
        }
        try {
            // 九机低评不扣积分
            if (req.getScoreKinds() == 1  && !ActiveProfileJudgeUtil.isJiuJiEnvironment()) {
                FenAndYanyinVO fenAndYanyin = staffScoreRedpackService.getFenAndYanyinByBumenId(scoreJob.getId(), StaffJifenTypeEnum.PJKF.getCode(), false);
                Integer fen = Objects.isNull(fenAndYanyin) ? null : fenAndYanyin.getFen();
                String yanyin = Objects.isNull(fenAndYanyin) ? org.apache.commons.lang3.StringUtils.EMPTY : fenAndYanyin.getYanyin();
                if (fen != null && fen < 0) {
                    String comment = "单号：" + scoreJob.getSubId() + "低评判定有效，扣除积分：" + Math.abs(fen);
                    AddUserFenReq addUserFenReq = new AddUserFenReq().setUserId(req.getUserId()).setRemark(StrUtil.isBlank(yanyin) ? comment : yanyin)
                            .setFen(fen).setInUser("系统").setBumenId(scoreJob.getId())
                            .setJifenType(StaffJifenTypeEnum.PJKF.getCode()).setScore(fen);
                    Integer insertCount = staffScoreRedpackService.insertUserFen(addUserFenReq);
                    evaluate.setProcess((evaluate.getProcess() == null ? "" : evaluate.getProcess()) + String.format("%s|%s|%s<br>",
                            "系统", LocalDateTime.now().format(PROCESS_TIME_FORMAT), comment));
                    if (insertCount > 0) {
                        String msg = "您因" + comment;
                        smsService.sendOaMsg(msg,"",String.valueOf(req.getUserId()),"0");
                    }
                }
            }
            updateById(evaluate);
            evaluateScoreService.updateById(scoreJob);
        } catch (Exception e) {
            log.error("客评设置无效，无效转有效扣除积分异常,评价进程信息：{},堆栈：", evaluate.getProcess(), e);
            throw new CustomizeException("客评设置无效，无效转有效扣除积分异常", ResultCode.SERVER_ERROR);
        }
        return R.success("操作成功", true);
    }

    @Override
    @DS("officeWrite")
    public R<Boolean> changeEvaluateUser(ChangeEvaluateUserReq req, String userName) {
        R<Ch999UserVo> userRes = userInfoClient.getCh999UserByUserName(req.getChangeUser());
        if (ResultCode.SUCCESS != userRes.getCode()) {
            return R.error("获取员工信息失败。");
        }
        Pattern digitPattern = Pattern.compile("^[\\d]*$");
        if (userRes.getData() == null && digitPattern.matcher(req.getChangeUser()).matches()) {
            userRes = userInfoClient.getCh999UserByUserId(Integer.parseInt(req.getChangeUser()));
            if (ResultCode.SUCCESS != userRes.getCode()) {
                return R.error("获取员工信息失败。");
            }
        }
        Ch999UserVo user = userRes.getData();
        if (user == null) {
            return R.error("评价人不存在");
        }
        Evaluate evaluate = getById(req.getEvaluateId());
        if (evaluate == null) {
            return R.error("客户评价不存在");
        }
        // 投诉新加了处理人，可以多个，岗位是相同的，换成id查
        EvaluateScore scoreJob;
        if (Objects.nonNull(req.getScoreId()) && req.getScoreId() != 0) {
            scoreJob = evaluateScoreService.lambdaQuery().eq(EvaluateScore::getId, req.getScoreId()).one();
        } else {
            scoreJob = evaluateScoreService.lambdaQuery()
                    .eq(EvaluateScore::getEvaluateId, req.getEvaluateId()).eq(EvaluateScore::getJob, req.getJobId()).one();
        }
        if (scoreJob == null) {
            return R.error("无效的岗位");
        }
        if (user.getCh999Id().equals(scoreJob.getRelateCh999Id())) {
            return R.error("更改的评价人和原评价人一样，请确认是否正确!");
        }
        Integer oldUserId = scoreJob.getRelateCh999Id();
        scoreJob.setRelateCh999Id(user.getCh999Id());
        String process = "评价岗位人员修改为" + user.getCh999Name();
        evaluate.setProcess((evaluate.getProcess() == null ? "" : evaluate.getProcess()) + String.format("%s|%s|%s<br>",
                userName, LocalDateTime.now().format(PROCESS_TIME_FORMAT), process));
        FenAndYanyinVO fenAndYanyinVO = staffScoreRedpackService.getFenAndYanyinByBumenId(scoreJob.getId(), StaffJifenTypeEnum.PJKF.getCode(), false);
        Integer fen = fenAndYanyinVO.getFen();
        String yanyin = fenAndYanyinVO.getYanyin();
        String moaUrl = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.MOA_URL, (int) Namespaces.get()).getData();
        // 九机低评不扣积分
        if (fen != null && !ActiveProfileJudgeUtil.isJiuJiEnvironment()) {
            Integer existFen = staffScoreRedpackService.getFenByBumenId(scoreJob.getId(), StaffJifenTypeEnum.PJKFTH.getCode(), true);
            if (existFen == null) {
                String comment = "单号：" + scoreJob.getSubId() + ERROR_MSG_WUXIAO_DIPING + Math.abs(fen);
                AddUserFenReq addUserFenReq = new AddUserFenReq().setUserId(oldUserId).setRemark(StrUtil.isBlank(yanyin) ? comment : yanyin)
                        .setFen(Math.abs(fen)).setInUser("系统").setBumenId(scoreJob.getId())
                        .setJifenType(StaffJifenTypeEnum.PJKFTH.getCode()).setScore(Math.abs(fen));
                Integer insertCount = staffScoreRedpackService.insertUserFen(addUserFenReq);
                evaluate.setProcess((evaluate.getProcess() == null ? "" : evaluate.getProcess()) + String.format("%s|%s|%s<br>",
                        "系统", LocalDateTime.now().format(PROCESS_TIME_FORMAT), comment));
                if (insertCount > 0) {
                    String msg = "您因" + comment;
                    String link = StrUtil.format(EVALUATE_URL, moaUrl,evaluate.getId());
                    // 暂时先注释调，现在推送调企业微信有问题
//                    messagePushCloud.pushQyWeiXinMessage(oldUserId + "", "0", "系统", msg, "", "");
                    msgPushCloud.simpleSend(String.valueOf(oldUserId), msg, link, OaMesTypeEnum.SYSTEM.getCode(), Math.toIntExact(Namespaces.get()));
                    //smsService.sendOaMsg(msg,link,String.valueOf(oldUserId),"0");
                }

                // 变更后的人扣除积分
                String newComment = "单号：" + scoreJob.getSubId() + "低评有效，扣除积分：" + Math.abs(fen);
                AddUserFenReq newAddUserFenReq = new AddUserFenReq().setUserId(user.getCh999Id()).setRemark(newComment)
                        .setFen(fen).setInUser("系统").setBumenId(scoreJob.getId())
                        .setJifenType(StaffJifenTypeEnum.PJKF.getCode()).setScore(fen);
                Integer newInsertCount = staffScoreRedpackService.insertUserFen(newAddUserFenReq);
                evaluate.setProcess((evaluate.getProcess() == null ? "" : evaluate.getProcess()) + String.format("%s|%s|%s<br>",
                        "系统", LocalDateTime.now().format(PROCESS_TIME_FORMAT), newComment));
                if (newInsertCount > 0) {
                    String msg = "您因" + comment;
                    smsService.sendOaMsg(msg,"",String.valueOf(user.getCh999Id()),"0");
                }
            }
        }
        updateById(evaluate);
        evaluateScoreService.updateById(scoreJob);
        return R.success("操作成功", true);
    }

    @Override
    @DS("office")
    public Evaluate getEvaluate(Integer evaluateId) {
        return this.getById(evaluateId);
    }

    @Override
    @DS("officeWrite")
    public void updateEvaluate(Evaluate evaluate) {
        this.updateById(evaluate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EvaluateRes saveEvaluate(EvaluateReq evaluateReq) {
        Evaluate evaluate = new Evaluate();
        evaluate.setSubId(evaluateReq.getSubId());
        evaluate.setUserId(evaluateReq.getUserId());
        evaluate.setStarLevel(evaluateReq.getStarLevel() != null ? evaluateReq.getStarLevel() : 0);
        evaluate.setContent(evaluateReq.getContent());
        //未处理状态
        evaluate.setEvaluateStatus(2);
        //评价类型
        evaluate.setEvaluateType(EvaluateTypeEnum.LOGISTICS.getCode());
        evaluate.setAreaId(evaluateReq.getAreaId());
        evaluate.setSourceFrom(evaluateReq.getSourceFrom());
        //默认不匿名
        evaluate.setIsAnon(evaluateReq.getIsAnon() != null ? evaluateReq.getIsAnon() : false);
        evaluate.setDtime(LocalDateTime.now());
        evaluate.setHuanyuantimeout(LocalDateTime.now());
        evaluate.setIsShow(true);
        evaluate.setVersionNum(1);
        this.save(evaluate);
        EvaluateScore evaluateScore = new EvaluateScore();
        evaluateScore.setEvaluateId(evaluate.getId());
        evaluateScore.setRelateCh999Id(evaluateReq.getRelateCh999Id());
        evaluateScore.setScore(evaluateReq.getScore());
        evaluateScore.setJob(evaluateReq.getJob());
        evaluateScore.setSubId(evaluateReq.getSubId());
        evaluateScore.setType(evaluateReq.getType());
        evaluateScore.setUserid(evaluateReq.getUserId());
        evaluateScore.setDtime(LocalDate.parse(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));
        evaluateScore.setAreaid(evaluateReq.getAreaId());
        evaluateScore.setScore2(evaluateReq.getScore2());
        evaluateScoreService.save(evaluateScore);
        List<Integer> serviceEvaluateTag = evaluateReq.getServiceEvaluateTag();
        List<Integer> specialtyEvaluateTag = evaluateReq.getSpecialtyEvaluateTag();

        if (serviceEvaluateTag != null && specialtyEvaluateTag != null) {
            serviceEvaluateTag.addAll(specialtyEvaluateTag);
        }
        if (specialtyEvaluateTag !=null){
            serviceEvaluateTag=specialtyEvaluateTag;
        }
        ArrayList<EvaluateTagRecord> tagRecords = new ArrayList<>();
        if (serviceEvaluateTag != null) {
            serviceEvaluateTag.forEach(item -> {
                EvaluateTagRecord evaluateTagRecord = new EvaluateTagRecord();
                evaluateTagRecord.setEvaluateId(evaluate.getId());
                evaluateTagRecord.setTagId(item);
                evaluateTagRecord.setRelateCh999Id(evaluateReq.getRelateCh999Id());
                //默认不是订单标签
                evaluateTagRecord.setIsOrderTag(evaluateReq.getIsOrderTag() != null ? evaluateReq.getIsOrderTag() : false);
                tagRecords.add(evaluateTagRecord);
            });
        }
        evaluateTagRecordService.saveBatchNoId(tagRecords);
        EvaluateRes evaluateRes = new EvaluateRes();
        evaluateRes.setEvaluateId(evaluate.getId());
        evaluateRes.setEvaluateScoreId(evaluateScore.getId());
        evaluateRes.setAreaId(evaluateReq.getAreaId());
        evaluateRes.setScore(evaluateReq.getScore());
        evaluateRes.setScore2(evaluateReq.getScore2());
        evaluateRes.setSubId(evaluateReq.getSubId());
        evaluateRes.setUserId(evaluateReq.getUserId());
        evaluateRes.setContent(evaluateReq.getContent());
        evaluateRes.setJob(evaluateReq.getJob());
        evaluateRes.setServiceEvaluateTag(evaluateReq.getServiceEvaluateTag());
        evaluateRes.setSpecialtyEvaluateTag(evaluateReq.getSpecialtyEvaluateTag());
        evaluateRes.setType(evaluateReq.getType());
        evaluateRes.setSourceFrom(evaluateReq.getSourceFrom());
        evaluateRes.setRelateCh999Id(evaluateReq.getRelateCh999Id());
        return evaluateRes;
    }

    @Override
    @DS("officeWrite")
    public void addEvaluateDepart(EvaluateDepart evaluateDepart) {
        OaUserBO oaUser = abstractCurrentRequestComponent.getCurrentStaffId();
        //if (oaUser == null) {
        //    return "没有登录";
        //}

        //获取大区
        R<Integer> departTypeIdR = departInfoClient.getDepartTypeId(evaluateDepart.getDepartId(), DataTypeEnum.L_COMMUNITY.getCode());
        if (evaluateDepart.getDepartId() != null && departTypeIdR.getCode() == ResultCode.SUCCESS) {
            evaluateDepart.setDepartArea(departTypeIdR.getData());
        }
        if (oaUser != null) {
            evaluateDepart.setCreateUser(oaUser.getUserName());
        }
        evaluateDepart.setCreateTime(LocalDateTime.now());
        this.evaluateDepartService.save(evaluateDepart);
        if (CollectionUtils.isNotEmpty(evaluateDepart.getCategoryIds())) {
            for (Integer cateId : evaluateDepart.getCategoryIds()) {
                EvaluateCategoryRelation evaluateCategoryRelation = new EvaluateCategoryRelation();
                evaluateCategoryRelation.setCateId(cateId);
                evaluateCategoryRelation.setCreateTime(LocalDateTime.now());
                if (oaUser != null) {
                    evaluateCategoryRelation.setCreateUser(oaUser.getUserName());
                }
                evaluateCategoryRelation.setEvaluateId(evaluateDepart.getEvaluateId());
                evaluateCategoryRelation.setDepartId(evaluateDepart.getId());
                evaluateCategoryRelationService.save(evaluateCategoryRelation);
            }
        }
    }

    @Override
    @DS("officeWrite")
    public void modifyEvaluateDepart(EvaluateDepart evaluateDepart) {
        OaUserBO oaUser = abstractCurrentRequestComponent.getCurrentStaffId();
        //if (oaUser == null) {
        //    return "没有登录";
        //}

        //获取大区
        R<Integer> departTypeIdR = departInfoClient.getDepartTypeId(evaluateDepart.getDepartId(), DataTypeEnum.L_COMMUNITY.getCode());
        if (evaluateDepart.getDepartId() != null && departTypeIdR.getCode() == ResultCode.SUCCESS) {
            evaluateDepart.setDepartArea(departTypeIdR.getData());
        }
        if (oaUser != null) {
            evaluateDepart.setCreateUser(oaUser.getUserName());
        }
        evaluateDepart.setCreateTime(LocalDateTime.now());
        this.evaluateDepartService.updateById(evaluateDepart);
        if (CollectionUtils.isNotEmpty(evaluateDepart.getCategoryIds())) {
            evaluateCategoryRelationService.remove(new QueryWrapper<EvaluateCategoryRelation>().eq("departId",evaluateDepart.getId()));
            for (Integer cateId : evaluateDepart.getCategoryIds()) {
                EvaluateCategoryRelation evaluateCategoryRelation = new EvaluateCategoryRelation();
                evaluateCategoryRelation.setCateId(cateId);
                evaluateCategoryRelation.setCreateTime(LocalDateTime.now());
                if (oaUser != null) {
                    evaluateCategoryRelation.setCreateUser(oaUser.getUserName());
                }
                evaluateCategoryRelation.setEvaluateId(evaluateDepart.getEvaluateId());
                evaluateCategoryRelation.setDepartId(evaluateDepart.getId());
                evaluateCategoryRelationService.save(evaluateCategoryRelation);
            }
        }
    }

    @Override
    @DS("officeWrite")
    public void deleteEvaluateDepart(EvaluateDepart evaluateDepart) {

        this.evaluateDepartService.removeById(evaluateDepart.getId());
    }

    @Override
    @DS("office")
    public EvaluateEndInfo getEvaluateEndInfo(Long evaluateId) {
        EvaluateEndInfo evaluateEndInfo = new EvaluateEndInfo();
        List<EvaluateDepartInfo> departs = evaluateMapper.selectDepartInfo(evaluateId);
        List<TousuCategory> list = tousuAreaService.listTousuCategoryByKind(2);
        Set<Integer> parentSet = list.stream().map(TousuCategory::getParentId).collect(Collectors.toSet());
        Set<Integer> idSet = list.stream().map(TousuCategory::getId).collect(Collectors.toSet());
        parentSet.removeAll(idSet);
        parentSet.remove(0);
        if(!parentSet.isEmpty()){
            List<TousuCategory> categories = tousuAreaMapper.getTousuCategoryByIds(parentSet);
            list.addAll(categories);
        }
        Map<Integer, TousuCategory> categoryMap = list.stream().collect(Collectors.toMap(it -> it.getId(), Function.identity()));
        if (CollectionUtils.isNotEmpty(departs)) {
            List<Integer> EvaluateDepartIds = departs.stream().map(EvaluateDepartInfo::getId).collect(Collectors.toList());
//     根据投诉Id查找所有投诉原因
            List<EvaluateCategoryRelation> relations = evaluateCategoryRelationService
                    .list(new QueryWrapper<EvaluateCategoryRelation>().in("departId", EvaluateDepartIds));
            if (CollectionUtils.isNotEmpty(relations)) {
                Map<Integer, List<EvaluateCategoryRelation>> listMap = relations.stream()
                        .collect(Collectors.groupingBy(it -> it.getDepartId()));
                for (EvaluateDepartInfo info : departs) {
                    if (listMap.containsKey(info.getId())) {
                        List<Integer> cateIds = listMap.get(info.getId()).stream().map(EvaluateCategoryRelation::getCateId)
                                .collect(Collectors.toList());
                        info.setEvaluateTypes(cateIds);
                        info.setEvaluateTypeNames(TousuCateUtils.getCateLine(info.getEvaluateTypes(),categoryMap));
                    }
                    touSuDepartSetName(info,departInfoClient);
            
                }
            }
            evaluateEndInfo.setDepartInfos(departs);
        }
        return evaluateEndInfo;
    }

    @Override
    public EvaluateShouhouBO getShouhouEvaluate() {
        EvaluateShouhouBO evaluateShouhou = null;
        // 先从缓存中获取
        String redisKey = RedisKeyConstant.EVALUATE_SHOUHOU_KEY;
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(redisKey))) {
            String redisValue = stringRedisTemplate.opsForValue().get(redisKey);
            if (StringUtils.isNotEmpty(redisValue)) {
                try {
                    evaluateShouhou = JsonUtils.fromJson(redisValue, EvaluateShouhouBO.class);
                } catch (SerializationException e) {
                    log.error("缓存中的售后评价解析异常，{}", e.getMessage(), e);
                }
            }
        }
        if (Objects.nonNull(evaluateShouhou)) {
            return evaluateShouhou;
        }
        // 查询售后评价统计
        String shouhouEvaluateStr = this.baseMapper.getShouhouEvaluate();
        // 构建返回对象
        evaluateShouhou = new EvaluateShouhouBO();
        String[] statisticsArray = shouhouEvaluateStr.split(",");
        if (statisticsArray.length == 2) {
            // 评价量
            BigDecimal evaluateCount = new BigDecimal(statisticsArray[0]);
            evaluateShouhou.setEvaluateCount(Integer.parseInt(statisticsArray[0]));
            // 好评量
            BigDecimal goodCount = new BigDecimal(statisticsArray[1]);
            evaluateShouhou.setPraiseRate(BigDecimal.ZERO);
            if (evaluateCount.compareTo(BigDecimal.ZERO) > 0) {
                evaluateShouhou.setPraiseRate(goodCount.divide(evaluateCount, IntConstant.FOUR, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100))
                        .setScale(IntConstant.TWO, RoundingMode.HALF_UP));
            }
        }
        // 查询累计服务人次
        Integer cumulativeService = this.baseMapper.getCumulativeService();
        evaluateShouhou.setCumulativeService(cumulativeService);
        // 设置1天的缓存
        String redisValue = null;
        try {
            redisValue = JsonUtils.toJson(evaluateShouhou);
        } catch (SerializationException e) {
            log.error("售后评价to json异常，{}", e.getMessage(), e);
        }
        if (StringUtils.isNotEmpty(redisValue)) {
            stringRedisTemplate.opsForValue().set(redisKey, redisValue, 1, TimeUnit.DAYS);
        }
        return evaluateShouhou;
    }

    @Override
    public R<HighOpinionRes> getOrderHighOpinion(Integer memberId, Long subId, String type, Boolean ignoreEvaluated) {
        HighOpinionRes res = new HighOpinionRes();
        R<HighOpinionRes> errorResult = R.error("暂无评价");
        errorResult.setData(res);
        // 转换下订单类型
        List<Integer> evaluateTypeList = WebSubEvaluateTypeEnum.getEvaluateType(type);
        if (subId == null || memberId == null || CollectionUtils.isEmpty(evaluateTypeList)) {
            return errorResult;
        }
        List<Evaluate> evaluates = this.list(new LambdaQueryWrapper<Evaluate>().eq(Evaluate::getSubId,subId).in(Evaluate::getEvaluateType, evaluateTypeList).orderByDesc(Evaluate::getId));
        // 没有评价过 也返回数据，只排除收评价对象影响的字段 都置为null
        if (Boolean.TRUE.equals(ignoreEvaluated) && CollectionUtils.isEmpty(evaluates)) {
            evaluates.add(new Evaluate());
        }
        if (CollectionUtils.isNotEmpty(evaluates)){
            Evaluate evaluate = evaluates.get(0);
            res.setMemberId(memberId);
            res.setOverFiveFlg(evaluate.getOverFiveFlg());
            res.setEvaluateId(evaluate.getId());
            res.setAreaId(evaluate.getAreaId());
            Map<Integer, HighOpinionRes.StaffMessage> staffCommentMap = null;
            // 获取会员可用积分
            BbsxpUsers user = bbsxpUsersService.getAllByUserId(memberId);
            if (user != null){
                res.setMemberPoints(user.getPoints());
            }
            // 默认从评论表获取
            // 如果提交过五星好评 则从投诉（表扬）侧获取覆盖
            List<ZeRenRen> tousuRenList;
            List<EvaluateScore> evaluateScoreList;
            if (Boolean.TRUE.equals(ignoreEvaluated) && Objects.isNull(evaluate.getId())) {
                evaluateScoreList = evaluateScoreService.list(new LambdaQueryWrapper<EvaluateScore>().eq(EvaluateScore::getSubId, subId));
            } else {
                evaluateScoreList = evaluateScoreService.list(new LambdaQueryWrapper<EvaluateScore>().eq(EvaluateScore::getEvaluateId,evaluate.getId()));
            }
            if (CollectionUtils.isNotEmpty(evaluateScoreList)){
                staffCommentMap = evaluateScoreList.stream().map(i ->{
                    HighOpinionRes.StaffMessage staffMessage = new HighOpinionRes.StaffMessage();
                    staffMessage.setStaffId(i.getRelateCh999Id());
                    staffMessage.setJob(i.getJob());
                    staffMessage.setJobName(TousuCateUtils.getJobName(i.getJob()));
//                    staffMessage.setEvaluateContent(evaluate.getContent());
                    staffMessage.setRewardCount(0);
                    staffMessage.setCashRewardCount(0);
                    staffMessage.setRewardMemberList(Collections.emptyList());
                    staffMessage.setRewardInfo(HighOpinionRes.RewardInfo.builder().rewardId(i.getId()).canReward(1).isReward(0).canCashReward(1).isCashReward(0).build());
                    return staffMessage;
                }).collect(Collectors.toMap(HighOpinionRes.StaffMessage::getStaffId, o -> o,(o1,o2) -> o1));

                if (Objects.equals(res.getOverFiveFlg(),1) && CollectionUtils.isNotEmpty(tousuRenList = touSuService.getCommentPraise(evaluate.getId()))){
                    for (ZeRenRen r : tousuRenList){
                        HighOpinionRes.StaffMessage staff = staffCommentMap.get(r.getStaffId());
                        if (staff != null){
                            staff.setEvaluateContent(r.getTsContent());
//                            staff.getRewardInfo().setIsReward(1);//打赏过 此逻辑不严谨 暂不返回
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(staffCommentMap)){
                // 员工信息
                List<StaffPraiseBO> staffMessage = ch999UserMapper.getStaffSimpleMessage(jiujiSystemProperties.getOfficeName(),staffCommentMap.keySet());
                if (CollectionUtils.isNotEmpty(staffMessage)){
                    for (StaffPraiseBO t : staffMessage){
                        HighOpinionRes.StaffMessage staff = staffCommentMap.get(t.getStaffId());
                        if(staff != null){
                            staff.setStaffName(t.getStaffName());
                            staff.setStaffHeadImg(t.getStaffHeadImg());
                            staff.setAreaName(t.getAreaName());
                            if (!Objects.equals(t.getIszaizhi(),1)){
                                staff.getRewardInfo().setCanReward(0);
                                staff.getRewardInfo().setMsg("该员工暂时不可打赏");
                                staff.getRewardInfo().setCanCashReward(0);
                                staff.getRewardInfo().setCashRewardedMsg("该员工暂时不可打赏");
                                staff.setCashRewardCount(1); // 这里因为app已经提交审核的原因 这里需要写个假的数据来兼容app的逻辑
                                staff.setCashRewardedMsg(staff.getRewardInfo().getCashRewardedMsg());
                            }else{
                                // 金额只能打赏一次
                                staff.setCashRewardCount(ch999UserMapper.countStaffCashReward(jiujiSystemProperties.getOfficeName(),staff.getStaffId(),res.getEvaluateId()));
                                if (staff.getCashRewardCount() > 0){
                                    staff.getRewardInfo().setCanCashReward(0);
                                    staff.getRewardInfo().setCashRewardedMsg("您已打赏过该员工哦~");
                                    staff.setCashRewardCount(1); // 这里因为app已经提交审核的原因 这里需要写个假的数据来兼容app的逻辑
                                    staff.setCashRewardedMsg(staff.getRewardInfo().getCashRewardedMsg());
                                }
                            }
                            if (org.apache.commons.lang3.StringUtils.isBlank(t.getStaffHeadImg())) {
                                t.setStaffHeadImg( "https://img.9xun.com/newstatic/14058/0389acb912057643.png");
                            }
                        }
                    }
                }

                // 打赏信息
                    for (Map.Entry<Integer, HighOpinionRes.StaffMessage> entry : staffCommentMap.entrySet()){
                        HighOpinionRes.StaffMessage staff = entry.getValue();
                        staff.setRewardCount(ch999UserMapper.countStaffPraiseRecord(jiujiSystemProperties.getOfficeName(),staff.getStaffId(),null));
                        if (staff.getRewardCount() > 0) {
                            List<StaffPraiseBO> praiseRecord = ch999UserMapper.getStaffPraiseRecord(jiujiSystemProperties.getOfficeName(), 0, 3, staff.getStaffId());
                            if (CollectionUtils.isNotEmpty(praiseRecord)) {
                                // 头像处理
                                praiseRecord.forEach(i -> {
                                    if (org.apache.commons.lang3.StringUtils.isNotBlank(i.getHeadImg())
                                            && !i.getHeadImg().startsWith("http")
                                            && i.getHeadImg().contains("newstatic")) {
                                        i.setHeadImg(imageProperties.getSelectImgUrl() + "/" + i.getHeadImg());
                                    }
                                });
                                staff.setRewardMemberList(praiseRecord.stream().map(i -> {
                                    HighOpinionRes.RewardCustomInfo customInfo = new HighOpinionRes.RewardCustomInfo();
                                    BeanUtils.copyProperties(i, customInfo);
                                    return customInfo;
                                }).collect(Collectors.toList()));
                            }
                        }
                    }
                }
                res.setStaffMessages(Objects.nonNull(staffCommentMap) ? new ArrayList<>(staffCommentMap.values()) : Lists.newArrayList());
                // 如果第一个员工没有评论 设置一个默认值
                if (CollectionUtils.isNotEmpty(res.getStaffMessages()) && org.apache.commons.lang3.StringUtils.isBlank(res.getStaffMessages().get(0).getEvaluateContent())){
                    res.getStaffMessages().get(0).setEvaluateContent(evaluate.getContent());
                }
            return R.success(res);
        }
        return errorResult;
    }

    @Override
    public R<HighOpinionRes> getOrderHighOpinionToCV2(Integer memberId, Long subId, String type, Boolean ignoreEvaluated, Integer ch999Id){
        HighOpinionRes res = new HighOpinionRes();
        R<HighOpinionRes> errorResult = R.error("暂无评价");
        errorResult.setData(res);
        // 转换下订单类型
        List<Integer> evaluateTypeList = WebSubEvaluateTypeEnum.getEvaluateType(type);
        if (subId == null || memberId == null || CollectionUtils.isEmpty(evaluateTypeList)) {
            return errorResult;
        }
        List<Evaluate> evaluates = this.list(new LambdaQueryWrapper<Evaluate>().eq(Evaluate::getSubId,subId).in(Evaluate::getEvaluateType, evaluateTypeList).orderByDesc(Evaluate::getId));
        // 没有评价过 也返回数据，只排除收评价对象影响的字段 都置为null
        if (Boolean.TRUE.equals(ignoreEvaluated) && CollectionUtils.isEmpty(evaluates)) {
            evaluates.add(new Evaluate());
        }
        if (CollectionUtils.isNotEmpty(evaluates)){
            Evaluate evaluate = evaluates.get(0);
            res.setMemberId(memberId);
            res.setOverFiveFlg(evaluate.getOverFiveFlg());
            res.setEvaluateId(evaluate.getId());
            res.setAreaId(evaluate.getAreaId());
            Map<Integer, HighOpinionRes.StaffMessage> staffCommentMap = null;
            // 获取会员可用积分
            BbsxpUsers user = bbsxpUsersService.getAllByUserId(memberId);
            if (user != null){
                res.setMemberPoints(user.getPoints());
            }
            // 默认从评论表获取
            // 如果提交过五星好评 则从投诉（表扬）侧获取覆盖
            List<ZeRenRen> tousuRenList;
            List<EvaluateScore> evaluateScoreList;
            if (Boolean.TRUE.equals(ignoreEvaluated) && Objects.isNull(evaluate.getId())) {
                evaluateScoreList = evaluateScoreService.list(new LambdaQueryWrapper<EvaluateScore>().eq(EvaluateScore::getSubId, subId));
            } else {
                evaluateScoreList = evaluateScoreService.list(new LambdaQueryWrapper<EvaluateScore>().eq(EvaluateScore::getEvaluateId,evaluate.getId()));
            }
            // 过滤当前ch99Id的数据
            if(null != ch999Id && CollUtil.isNotEmpty(evaluateScoreList)){
                evaluateScoreList = evaluateScoreList.stream().filter(e -> e.getRelateCh999Id().equals(ch999Id)).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(evaluateScoreList)){
                staffCommentMap = evaluateScoreList.stream().map(i ->{
                    HighOpinionRes.StaffMessage staffMessage = new HighOpinionRes.StaffMessage();
                    staffMessage.setStaffId(i.getRelateCh999Id());
                    staffMessage.setJob(i.getJob());
                    staffMessage.setJobName(TousuCateUtils.getJobName(i.getJob()));
//                    staffMessage.setEvaluateContent(evaluate.getContent());
                    staffMessage.setRewardCount(0);
                    staffMessage.setCashRewardCount(0);
                    staffMessage.setRewardMemberList(Collections.emptyList());
                    staffMessage.setRewardInfo(HighOpinionRes.RewardInfo.builder().rewardId(i.getId()).canReward(1).isReward(0).canCashReward(1).isCashReward(0).build());
                    return staffMessage;
                }).collect(Collectors.toMap(HighOpinionRes.StaffMessage::getStaffId, o -> o,(o1,o2) -> o1));

                if (Objects.equals(res.getOverFiveFlg(),1) && CollectionUtils.isNotEmpty(tousuRenList = touSuService.getCommentPraise(evaluate.getId()))){
                    for (ZeRenRen r : tousuRenList){
                        HighOpinionRes.StaffMessage staff = staffCommentMap.get(r.getStaffId());
                        if (staff != null){
                            staff.setEvaluateContent(r.getTsContent());
//                            staff.getRewardInfo().setIsReward(1);//打赏过 此逻辑不严谨 暂不返回
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(staffCommentMap)){
                // 员工信息
                List<StaffPraiseBO> staffMessage = ch999UserMapper.getStaffSimpleMessage(jiujiSystemProperties.getOfficeName(),staffCommentMap.keySet());
                if (CollectionUtils.isNotEmpty(staffMessage)){
                    for (StaffPraiseBO t : staffMessage){
                        HighOpinionRes.StaffMessage staff = staffCommentMap.get(t.getStaffId());
                        if(staff != null){
                            staff.setStaffName(t.getStaffName());
                            staff.setStaffHeadImg(t.getStaffHeadImg());
                            staff.setAreaName(t.getAreaName());
                            if (!Objects.equals(t.getIszaizhi(),1)){
                                staff.getRewardInfo().setCanReward(0);
                                staff.getRewardInfo().setMsg("该员工暂时不可打赏");
                                staff.getRewardInfo().setCanCashReward(0);
                                staff.getRewardInfo().setCashRewardedMsg("该员工暂时不可打赏");
                                staff.setCashRewardCount(1); // 这里因为app已经提交审核的原因 这里需要写个假的数据来兼容app的逻辑
                                staff.setCashRewardedMsg(staff.getRewardInfo().getCashRewardedMsg());
                            }else{

                                staff.setCashRewardCount(ch999UserMapper.countStaffCashReward(jiujiSystemProperties.getOfficeName(),staff.getStaffId(),res.getEvaluateId()));
//                                // 不限制打赏次数 注释一下代码
//                                if (staff.getCashRewardCount() > 0){
//                                    staff.getRewardInfo().setCanCashReward(0);
//                                    staff.getRewardInfo().setCashRewardedMsg("您已打赏过该员工哦~");
//                                    staff.setCashRewardCount(1); // 这里因为app已经提交审核的原因 这里需要写个假的数据来兼容app的逻辑
//                                    staff.setCashRewardedMsg(staff.getRewardInfo().getCashRewardedMsg());
//                                }
                            }
                            if (org.apache.commons.lang3.StringUtils.isBlank(t.getStaffHeadImg())) {
                                t.setStaffHeadImg( "https://img.9xun.com/newstatic/14058/0389acb912057643.png");
                            }
                        }
                    }
                }

                // 打赏信息
                for (Map.Entry<Integer, HighOpinionRes.StaffMessage> entry : staffCommentMap.entrySet()){
                    HighOpinionRes.StaffMessage staff = entry.getValue();
                    staff.setRewardCount(ch999UserMapper.countStaffPraiseRecord(jiujiSystemProperties.getOfficeName(),staff.getStaffId(),null));
                    if (staff.getRewardCount() > 0) {
                        List<StaffPraiseBO> praiseRecord = ch999UserMapper.getStaffPraiseRecord(jiujiSystemProperties.getOfficeName(), 0, 3, staff.getStaffId());
                        if (CollectionUtils.isNotEmpty(praiseRecord)) {
                            // 头像处理
                            praiseRecord.forEach(i -> {
                                if (org.apache.commons.lang3.StringUtils.isNotBlank(i.getHeadImg())
                                        && !i.getHeadImg().startsWith("http")
                                        && i.getHeadImg().contains("newstatic")) {
                                    i.setHeadImg(imageProperties.getSelectImgUrl() + "/" + i.getHeadImg());
                                }
                            });
                            staff.setRewardMemberList(praiseRecord.stream().map(i -> {
                                HighOpinionRes.RewardCustomInfo customInfo = new HighOpinionRes.RewardCustomInfo();
                                BeanUtils.copyProperties(i, customInfo);
                                return customInfo;
                            }).collect(Collectors.toList()));
                        }
                    }
                }
            }
            res.setStaffMessages(Objects.nonNull(staffCommentMap) ? new ArrayList<>(staffCommentMap.values()) : Lists.newArrayList());
            // 如果第一个员工没有评论 设置一个默认值
            if (CollectionUtils.isNotEmpty(res.getStaffMessages()) && org.apache.commons.lang3.StringUtils.isBlank(res.getStaffMessages().get(0).getEvaluateContent())){
                res.getStaffMessages().get(0).setEvaluateContent(evaluate.getContent());
            }
            return R.success(res);
        }
        return errorResult;
    }

    @Override
    public R<HighOpinionRes> getOrderHighOpinionToC(Integer memberId,Integer ch999Id){
        HighOpinionRes res = new HighOpinionRes();
        R<HighOpinionRes> errorResult = R.error("暂无信息");
        errorResult.setData(res);

        if (ch999Id == null || memberId == null) {
            return errorResult;
        }
        res.setMemberId(memberId);
        // 获取会员可用积分
        BbsxpUsers user = bbsxpUsersService.getAllByUserId(memberId);
        if (user != null){
            res.setMemberPoints(user.getPoints());
        }

        HighOpinionRes.StaffMessage staffMessage = new HighOpinionRes.StaffMessage();
        staffMessage.setStaffId(ch999Id);
        staffMessage.setRewardCount(0);
        staffMessage.setCashRewardCount(0);
        staffMessage.setRewardMemberList(Collections.emptyList());
        // 注意这个业务Id - rewardId
        staffMessage.setRewardInfo(HighOpinionRes.RewardInfo.builder().rewardId(ch999Id).canReward(1).isReward(0).canCashReward(1).isCashReward(0).build());

        // 员工信息
        Set<Integer> staffIdList  = new HashSet<>();
        staffIdList.add(ch999Id);
        List<StaffPraiseBO> staffMessageList = ch999UserMapper.getStaffSimpleMessage(jiujiSystemProperties.getOfficeName(), staffIdList);
        if(CollUtil.isEmpty(staffMessageList)){
            errorResult.setMsg("未查询到员工信息！");
            return errorResult;
        }

        HighOpinionRes.StaffMessage staff = staffMessage;
        StaffPraiseBO t = staffMessageList.get(0);
        staff.setStaffName(t.getStaffName());
        staff.setStaffHeadImg(t.getStaffHeadImg());
        staff.setAreaName(t.getAreaName());
        staff.setCashRewardCount(1); // 这里因为app已经提交审核的原因 这里需要写个假的数据来兼容app的逻辑
        if (!Objects.equals(t.getIszaizhi(),1)){
            staff.getRewardInfo().setCanReward(0);
            staff.getRewardInfo().setMsg("该员工暂时不可打赏");
            staff.getRewardInfo().setCanCashReward(0);
            staff.getRewardInfo().setCashRewardedMsg("该员工暂时不可打赏");
            staff.setCashRewardedMsg(staff.getRewardInfo().getCashRewardedMsg());
        }else{
            // 打赏次数
            staff.setCashRewardCount(ch999UserMapper.countStaffCashReward(jiujiSystemProperties.getOfficeName(),staff.getStaffId(),res.getEvaluateId()));
            // 不限制打赏次数
            staff.getRewardInfo().setCanCashReward(1);
            staff.getRewardInfo().setCanReward(1);
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(t.getStaffHeadImg())) {
            t.setStaffHeadImg( "https://img.9xun.com/newstatic/14058/0389acb912057643.png");
        }

        // 打赏数据
        staff.setRewardCount(ch999UserMapper.countStaffPraiseRecord(jiujiSystemProperties.getOfficeName(),staff.getStaffId(),null));
        if (staff.getRewardCount() > 0) {
            List<StaffPraiseBO> praiseRecord = ch999UserMapper.getStaffPraiseRecord(jiujiSystemProperties.getOfficeName(), 0, 3, staff.getStaffId());
            if (CollectionUtils.isNotEmpty(praiseRecord)) {
                // 头像处理
                praiseRecord.forEach(i -> {
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(i.getHeadImg())
                            && !i.getHeadImg().startsWith("http")
                            && i.getHeadImg().contains("newstatic")) {
                        i.setHeadImg(imageProperties.getSelectImgUrl() + "/" + i.getHeadImg());
                    }
                });
                staff.setRewardMemberList(praiseRecord.stream().map(i -> {
                    HighOpinionRes.RewardCustomInfo customInfo = new HighOpinionRes.RewardCustomInfo();
                    BeanUtils.copyProperties(i, customInfo);
                    return customInfo;
                }).collect(Collectors.toList()));
            }
        }

        res.setStaffMessages(Lists.newArrayList(staffMessage));

        return R.success(res);
    }

    @Override
    public R<String> HighOpinionIgnore(Long subId, String type) {
        List<Integer> evaluateTypeList = WebSubEvaluateTypeEnum.getEvaluateType(type);
        if (subId == null || subId == 0L || CollectionUtils.isEmpty(evaluateTypeList)) {
            return R.error("参数异常");
        }

        List<Evaluate> evaluates = this.list(new LambdaQueryWrapper<Evaluate>().eq(Evaluate::getSubId,subId).in(Evaluate::getEvaluateType, evaluateTypeList).orderByDesc(Evaluate::getId));
        if (CollectionUtils.isEmpty(evaluates)){
            return R.error("评论不存在");
        }
        Evaluate evaluate = evaluates.get(0);

        if (Objects.equals(evaluate.getOverFiveFlg(),1)){
            return R.error("评论已经好评过");
        }
        if (Objects.equals(evaluate.getOverFiveFlg(),2)){
            return R.success("操作成功");
        }
        this.update(new LambdaUpdateWrapper<Evaluate>().eq(Evaluate::getId,evaluate.getId()).set(Evaluate::getOverFiveFlg,2));
        return R.success("操作成功");
    }

    @Override
    public R<List<EvaluateRewardInfo>> getStaffRewardList(Integer current, Integer size, Integer staffId) {
        Integer startRows = null;
        if (current != null && size != null){
            startRows = (current - 1) * size;
        }else {
            size = null;
        }
        List<StaffPraiseBO> praiseRecord = ch999UserMapper.getStaffPraiseRecord(jiujiSystemProperties.getOfficeName(),startRows,size,staffId);
        if (CollectionUtils.isNotEmpty(praiseRecord)){
            // 头像处理&&根据打赏时间排序
            praiseRecord.forEach(i -> {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(i.getHeadImg())
                        && !i.getHeadImg().startsWith("http")
                        && i.getHeadImg().contains("newstatic")){
                    i.setHeadImg(imageProperties.getSelectImgUrl() + "/" + i.getHeadImg());
                }
            });
            return R.success(praiseRecord.stream().map(i -> {
                EvaluateRewardInfo info = new EvaluateRewardInfo();
                BeanUtils.copyProperties(i,info);
                info.setRewardTime(i.getRewardTime().toLocalDate());
                return info;
            }).collect(Collectors.toList()));
        }
        return R.success(Collections.emptyList());
    }

    @Override
    public R<HighOpinionRes.StaffMessage> getStaffRewardSimpleMsg(Integer staffId) {
        // 打赏信息
        HighOpinionRes.StaffMessage staff = new  HighOpinionRes.StaffMessage();
        staff.setStaffId(staffId);
        staff.setRewardCount(ch999UserMapper.countStaffPraiseRecord(jiujiSystemProperties.getOfficeName(),staffId,null));
        if (staff.getRewardCount() > 0) {
//            staff.setCashRewardCount(ch999UserMapper.countStaffPraiseRecord(jiujiSystemProperties.getOfficeName(),staffId,2));
            List<StaffPraiseBO> praiseRecord = ch999UserMapper.getStaffPraiseRecord(jiujiSystemProperties.getOfficeName(), 0, 3, staffId);
            if (CollectionUtils.isNotEmpty(praiseRecord)) {
                // 头像处理
                praiseRecord.forEach(i -> {
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(i.getHeadImg())
                            && !i.getHeadImg().startsWith("http")
                            && i.getHeadImg().contains("newstatic")) {
                        i.setHeadImg(imageProperties.getSelectImgUrl() + "/" + i.getHeadImg());
                    }
                });
                staff.setRewardMemberList(praiseRecord.stream().map(i -> {
                    HighOpinionRes.RewardCustomInfo customInfo = new HighOpinionRes.RewardCustomInfo();
                    BeanUtils.copyProperties(i, customInfo);
                    return customInfo;
                }).collect(Collectors.toList()));
            }
        }
        return R.success(staff);
    }

    /*private void handleCateName(EvaluateDepartInfo info, Map<Integer, TousuCategory> categoryMap) {
        List<Integer> evaluateTypes = info.getEvaluateTypes();
        //key root-second
        Map<String,List<Integer>> names=new HashMap<>();
        for(Integer id:evaluateTypes){
            TousuCategory tousuCategory = categoryMap.get(id);
            TousuCategory second = categoryMap.get(tousuCategory.getParentId());
            TousuCategory root = categoryMap.get(second.getParentId());

            String key=root.getId()+"-"+second.getId();
            List<Integer> cateIds=names.get(key);
            if(cateIds==null){
                cateIds=new ArrayList<>();
                cateIds.add(id);
                names.put(key,cateIds);
            }else {
                cateIds.add(id);
            }
        }
        String format="【{0}-{1}】";
        List<String> list=new ArrayList<>(names.size());
        names.entrySet().forEach(entry->{
            String[] strings = entry.getKey().split("-");
            String header= MessageFormat.format(format,categoryMap.get(Integer.valueOf(strings[0])).getName(),categoryMap.get(Integer.valueOf(strings[1])).getName());
            String lines = org.apache.commons.lang3.StringUtils.join(entry.getValue().stream().map(it->categoryMap.get(Integer.valueOf(it)).getName()).collect(
                    Collectors.toList()), ",");
            list.add(header+lines);
        });
        info.setEvaluateTypeNames(list);
    }*/

    public void touSuDepartSetName(EvaluateDepartInfo departInfo, DepartInfoClient departInfoClient) {


        if (departInfo.getDepartIdArea() != null) {
            R<DepartInfoVO> byDepartCode = departInfoClient.getByDepartId(departInfo.getDepartIdArea());
            if (byDepartCode.getCode() == ResultCode.SUCCESS && byDepartCode.getData() != null) {
                DepartInfoVO data = byDepartCode.getData();
                //责任归属区域名
                departInfo.setDepartAreaName(data.getName());
            }
        }
        if (departInfo.getDepartId() != null) {
            R<DepartInfoVO> byDepartCode = departInfoClient.getByDepartId(departInfo.getDepartId());
            if (byDepartCode.getCode() == ResultCode.SUCCESS && byDepartCode.getData() != null) {
                DepartInfoVO data = byDepartCode.getData();
                //部门名称
                departInfo.setDepartment(data.getName());
                R<Integer> departTypeIdR = departInfoClient.getDepartTypeId(departInfo.getDepartId(), DataTypeEnum.CENTER.getCode());
                //获取部门所属中心Id
                if (departTypeIdR.getCode() == ResultCode.SUCCESS) {
                    departInfo.setDepartmentCentId(departTypeIdR.getData());
                }

            }
        }

        if (departInfo.getDepartmentCentId() != null) {
            R<DepartInfoVO> byDepartCode = departInfoClient.getByDepartId(departInfo.getDepartmentCentId());
            if (byDepartCode.getCode() == ResultCode.SUCCESS && byDepartCode.getData() != null) {
                DepartInfoVO data = byDepartCode.getData();
                //所属中心
                departInfo.setDepartmentCentName(data.getName());
            }
        }

    }

    @Override
    public EvaluateOrderInfoRes getCompleteOrderInfoWithinHours(Integer userId) {
        EvaluateOrderInfoRes evaluateOrderInfoRes = new EvaluateOrderInfoRes();
        // 内部员工不用弹窗
        String phoneForCh999User = evaluateMapper.checkPhoneForCh999User(userId);
        if (StringUtils.isNotEmpty(phoneForCh999User)) {
            return evaluateOrderInfoRes;
        }
        List<Integer> tieMoCidList = categoryService.tieMoCids();
        List<Integer> finalTieMoCidList = categoryService.selectCategoryChildrenByCid(tieMoCidList);
        if (CollectionUtils.isEmpty(finalTieMoCidList)) {
            finalTieMoCidList = Lists.newArrayList(0);
        }
        LocalDateTime beforeTime = LocalDateTime.now().minusDays(1);
        LocalDateTime afterTime = LocalDateTime.now().minusMinutes(30);
        List<EvaluateOrderBO> orderInfoWithinHours = evaluateMapper.getOrderInfoWithinHours(userId, beforeTime, afterTime, org.apache.commons.lang3.StringUtils.join(finalTieMoCidList, ","));
        if (CollectionUtils.isEmpty(orderInfoWithinHours)) {
            return evaluateOrderInfoRes;
        }
        List<Integer> subIds = orderInfoWithinHours.stream().map(EvaluateOrderBO::getSubId).collect(Collectors.toList());
        // 查询没有评价的订单
        List<EvaluateOrderBO> notEvaluateOrderInfo = evaluateMapper.getNotEvaluateOrderInfo(subIds);
        List<EvaluateOrderBO> evaluateOrderInfoMain = evaluateMapper.getEvaluateOrderInfoMain(subIds);
        // 过滤出最近完成的
        List<EvaluateOrderBO> finalOrderInfoList = orderInfoWithinHours.stream().filter(orderInfo -> {
            List<Integer> evaluateType = WebSubEvaluateTypeEnum.getEvaluateType(orderInfo.getSubType());
            List<Integer> notEvaluateSubIds = notEvaluateOrderInfo.stream()
                    .filter(it -> evaluateType.contains(it.getEvaluateType()))
                    .map(EvaluateOrderBO::getSubId).distinct().collect(Collectors.toList());
            List<Integer> notEvaluateSubIdsMain = evaluateOrderInfoMain.stream()
                    .filter(it -> evaluateType.contains(it.getEvaluateType()))
                    .map(EvaluateOrderBO::getSubId).distinct().collect(Collectors.toList());
            return notEvaluateSubIds.contains(orderInfo.getSubId()) && !notEvaluateSubIdsMain.contains(orderInfo.getSubId());
        }).sorted(Comparator.comparing(EvaluateOrderBO::getTradeDate).reversed()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(finalOrderInfoList)) {
            EvaluateOrderBO evaluateOrderBO = finalOrderInfoList.get(0);

            evaluateOrderInfoRes.setSubType(evaluateOrderBO.getSubType());
            evaluateOrderInfoRes.setSubId(evaluateOrderBO.getSubId());
        }
        return evaluateOrderInfoRes;
    }

    @Override
    public Boolean productScoreToEvaluateScore(Integer evaluateId) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if (Objects.isNull(oaUserBO)) {
            throw new CustomizeException("登录信息获取失败");
        }
        if (!oaUserBO.getRank().contains("gjkp")) {
            throw new CustomizeException("无权处理");
        }
        Evaluate evaluate = this.getById(evaluateId);
        if (Objects.isNull(evaluate)) {
            throw new CustomizeException("评价查询异常");
        }
        List<EvaluateScore> evaluateScoreList = evaluateScoreService.getByEvaluateId(evaluate.getId());
        if (CollectionUtils.isEmpty(evaluateScoreList)) {
            throw new CustomizeException("服务员工查询异常");
        }
        Integer evaluateType = evaluate.getEvaluateType();
        Double productScore = null;
        if (EvaluateTypeEnum.Online.getCode().equals(evaluateType) || EvaluateTypeEnum.Offline.getCode().equals(evaluateType)) {
            List<Integer> basketIds = baseMapper.getBasketsOfNew(evaluate.getSubId());
            if (basketIds != null && basketIds.size() != 0) {
                EvaluateProductCommentVO evaluateProductCommentVO = new EvaluateProductCommentVO();
                evaluateProductCommentVO.setBasketIds(basketIds);
                OaUserBO currentStaffId = abstractCurrentRequestComponent.getCurrentStaffId();
                int xTenant = currentStaffId == null ? (int)Namespaces.get() : currentStaffId.getXTenant();
                com.jiuji.oa.oacore.oaorder.client.vo.Result<List<ProductCommentAggBO>> evaluateProductCommentInfo = evaluateProductClient.getEvaluateProductCommentInfo(xTenant, evaluateProductCommentVO);
                if (evaluateProductCommentInfo.getCode() == 0 && evaluateProductCommentInfo.getData() != null && evaluateProductCommentInfo.getData().size() != 0) {
                    productScore = evaluateProductCommentInfo.getData().stream().mapToInt(e -> e.getReviews().getStar()).average().orElse(0);
                }
            }
        }
        // 良品评价商品平均分
        if (EvaluateTypeEnum.Liangpin.getCode().equals(evaluateType)) {
            String url = "";
            R<String> urlR = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.WEB_URL, (int) Namespaces.get());
            if (CommonUtils.isRequestSuccess(urlR)) {
                url = urlR.getData();
            }
            url = url + "/hsapi/vueapi/getCommentList?subId={subId}&commentCategory=1";
            String object = restTemplate.getForObject(url, String.class, evaluate.getSubId());
            LiangPinCommentRes liangPinCommentRes = JSONUtil.toBean(object, LiangPinCommentRes.class);
            productScore = liangPinCommentRes.getData().stream().mapToInt(LiangPinCommentVO::getStar).average().orElse(0);
        }
        // 备用机商品评价
        if (EvaluateTypeEnum.STANDBY.getCode().equals(evaluateType) && evaluate.getStandbyId() != null) {
            // -1代表勾选的未借用备用机
            Integer standbyScore = Optional.ofNullable(evaluate.getStandbyScore()).orElse(0);
            productScore = (double) (standbyScore == -1 ? 0 : standbyScore);
        }
        if (Objects.isNull(productScore) || Objects.equals(productScore, 0D)) {
            throw new CustomizeException("商品分为空，不能把商品分转员工评价分");
        }
        Integer finalProductScore = Math.toIntExact(Math.round(productScore));
        List<Integer> evaluateScoreIds = evaluateScoreList.stream().map(EvaluateScore::getScore).collect(Collectors.toList());
        List<Integer> staffIds = evaluateScoreList.stream().map(EvaluateScore::getRelateCh999Id).distinct().collect(Collectors.toList());
        List<Ch999User> ch999UsersById = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(staffIds)) {
            ch999UsersById = ch999UserService.getCh999UsersById(staffIds);
        }
        Map<Integer, String> staffNameMap = ch999UsersById.stream().collect(Collectors.toMap(Ch999User::getCh999Id, Ch999User::getCh999Name, (c1, c2) -> c2));
        LambdaUpdateWrapper<EvaluateScore> updateWrapper = Wrappers.<EvaluateScore>lambdaUpdate()
                .set(EvaluateScore::getScore, finalProductScore)
                .in(EvaluateScore::getId, evaluateScoreIds);
        boolean update = evaluateScoreService.update(updateWrapper);
        List<String> logStrList = evaluateScoreList.stream()
                .map(staff -> String.format("%s的评价分由%s修改为%s", staffNameMap.getOrDefault(staff.getRelateCh999Id(), ""), staff.getScore(), finalProductScore))
                .collect(Collectors.toList());
        String comment = "商品评价分转员工评价分，" + org.apache.commons.lang3.StringUtils.join(logStrList, "，");
        evaluate.setProcess((evaluate.getProcess() == null ? "" : evaluate.getProcess()) + String.format("%s|%s|%s<br>",
                oaUserBO.getUserName(), LocalDateTime.now().format(PROCESS_TIME_FORMAT), comment));
        LambdaUpdateWrapper<Evaluate> evaluateUw = Wrappers.<Evaluate>lambdaUpdate()
                .set(Evaluate::getProcess, evaluate.getProcess())
                .in(Evaluate::getId, evaluate.getId());
        this.update(evaluateUw);
        return update;
    }
}
