package com.jiuji.oa.oacore.thirdplatform.order.service;

import com.jiuji.oa.oacore.thirdplatform.order.bo.PreparationMealCompleteBO;
import com.jiuji.oa.oacore.thirdplatform.order.bo.SelectMeiTuanOrderDetailBO;
import com.jiuji.oa.oacore.thirdplatform.order.bo.ShowMeiTuanOrderDetailBO;
import com.jiuji.oa.oacore.thirdplatform.order.vo.HandleInvoiceReq;
import com.jiuji.oa.oacore.thirdplatform.order.vo.NationalSupplementUpSnReq;
import com.jiuji.oa.oacore.thirdplatform.order.vo.NationalSupplementUpSnRes;
import com.jiuji.oa.oacore.thirdplatform.order.vo.SelectInvoiceReq;
import com.jiuji.tc.common.vo.R;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.Map;


/**
 * <AUTHOR>
 */
public interface MeiTuanService {



    /**
     * 批量补SN
     * @param req
     * @return
     */
    NationalSupplementUpSnRes nationalSupplementUpSn(NationalSupplementUpSnReq req);

    /**
     * 美团发票查询
     * @param req
     * @return
     */
    String selectInvoice(SelectInvoiceReq req);
    /**
     * 美团发票处理
     * @param req
     * @return
     */
    NationalSupplementUpSnRes handleInvoice(HandleInvoiceReq req);

    /**
     * 同步OA门店营业时间倒美团开放门店
     * @param areaId
     */
    void synchronizationBusinessHours(String areaId);

    /**
     * 同步OA门店营业时间倒美团开放门店
     * @param areaId
     */
    void synchronizationBusinessHoursV2(String areaId);

    /**
     * 获取美团订单信息
     * @param detailBO
     * @return
     */
    ShowMeiTuanOrderDetailBO getOrderDetail(SelectMeiTuanOrderDetailBO detailBO);


    /**
     * 拣选完成之后修改美团订单状态
     * @param preparationMealCompleteBO
     * @return
     */
    R<String> pickingCompleted(PreparationMealCompleteBO preparationMealCompleteBO);


    /**
     * 美团同步订单状态到九机
     * @param parms
     * @return
     */
    R deliverySynchronization(Map<String, Object> parms);

    void invoiceCall(Map<String, Object> parms);

    /**
     * 通过门店id进行门店营业时间的同步
     * @param areaId
     */
    void synchronizationAreaHour(String areaId);

    void synchronizationAreaHourV2(String areaId);

    R modifyOrderInfoSynchronization(Map<String, Object> params);

}
