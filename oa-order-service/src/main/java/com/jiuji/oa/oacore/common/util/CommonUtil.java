package com.jiuji.oa.oacore.common.util;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.LRUCache;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.google.common.collect.Lists;
import com.jiuji.oa.oacore.common.bo.ZnSendConnBo;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.constant.RequestAttrKeys;
import com.jiuji.oa.oacore.common.constant.UrlConstant;
import com.jiuji.oa.oacore.common.enums.*;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.oaorder.enums.EUserClassNewEnum;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import jodd.typeconverter.Convert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: gengjiaping
 * @date: 2019/11/28
 */
@Slf4j
@Component
public class CommonUtil {
    private static final Integer JIU_JI_MAX_XTENANT = 1000;
    private static Pattern INTEGER_DOT_REGEX = Pattern.compile("^(\\d+,)*\\d+$");
    // 地球平均半径
    private static final double EARTH_RADIUS = 6378137;
    /**方便原方法没有相应返回值,但需要增加返回值在方法之间传递,为了防止内存泄露,消息有时长限制*/
    private static final ThreadLocal<LRUCache<String, Object>> CHANNEL_MESSAGE = ThreadLocal.withInitial(()-> CacheUtil.newLRUCache(10,60*60000));

    public static boolean containIgnoreCase(List<String> list, String compareStr) {
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        for (String s : list) {
            if (StringUtils.equalsIgnoreCase(s, compareStr)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 使用java正则表达式去掉多余的.与0
     *
     * @param s
     * @return string
     */
    public static String replace(String s) {
        if (null != s && s.indexOf(".") > 0) {
            s = s.replaceAll("0+?$", "");//去掉多余的0
            s = s.replaceAll("[.]$", "");//如最后一位是.则去掉
        }
        return s;
    }

    /**
     * 替换非法字符
     *
     * @return
     */
    public static String replaces(String str) {
        if (null == str) {
            return "";
        }
        String temp = str;
        str = str.toLowerCase().replace("exec ", "");
        str = str.toLowerCase().replace("insert ", "");
        str = str.toLowerCase().replace("mid ", "");
        str = str.toLowerCase().replace("update ", "");
        str = str.toLowerCase().replace("select ", "");
        str = str.toLowerCase().replace("delete ", "");
        str = str.toLowerCase().replace("drop ", "");
        str = str.toLowerCase().replace("script", "");
        str = str.toLowerCase().replace("union ", "＋");
        str = str.toLowerCase().replace("or ", "＋");
        str = str.toLowerCase().replace("truncate ", "");
        str = str.toLowerCase().replace("confirm ", "");
        str = str.toLowerCase().replace("prompt ", "");
        str = str.toLowerCase().replace("alter ", "");
        str = str.toLowerCase().replace("create ", "");
        str = str.toLowerCase().replace("into ", "");
        str = str.toLowerCase().replace("database ", "");
        if (str.trim().equals(temp.toLowerCase().trim())) {
            return temp.trim();
        } else {
            return str.trim();
        }
    }

    public static void pushOaAppAndWeiXing(String message, Integer ch999Id, Integer msgType) {
        try {
            //消息推送
            Map<String, String> params = new HashMap<>(16);
            params.put("content", message);
            params.put("ch999ids", ch999Id.toString());
            params.put("msgType", msgType.toString());
            HttpClientUtil.post(UrlConstant.OA_SEND_MSG_URL, params);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    /**
     * 把以逗号分隔的字符串转为List<Integer>
     * @param ids
     * @return
     */
    public static List<Integer> toIntList(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return new LinkedList<>();
        }
        return Arrays.asList(ids.split(","))
                .stream()
                .filter(StringUtils::isNotEmpty)
                .filter(CommonUtil::isNumer)
                .map(Integer::valueOf).collect(Collectors.toList());
    }

    /**
     * 把以逗号分隔的字符串转为List<Integer>
     * @param ids
     * @return
     */
    public static List<String> toStrList(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return new LinkedList<>();
        }
        return Arrays.asList(ids.split(","));
    }

    /**
     * 是否是数字
     *
     * @param str
     * @return
     */
    public static boolean isNumer(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        } else {
            for (int index = 0; index < str.length(); ++index) {
                if (!Character.isDigit(str.charAt(index))) {
                    return false;
                }
            }

            return true;
        }
    }
    public static int getSmsChannelByXtenant(Integer xtenant, Integer eSmsChannelType) {
        eSmsChannelType = eSmsChannelType == null ? ESmsChannelTypeEnum.YZMTD.getCode() : eSmsChannelType;

        Integer result = JiujiSmsChannelEnum.JIUJI_SMS_CHANNEL_JIUJI.getCode();

        if (eSmsChannelType.equals(ESmsChannelTypeEnum.YZMTD.getCode())) {
            if (xtenant.equals(JiujiTenantEnum.JIUJI_TENANT_JIUJI.getCode())) {
                result = JiujiSmsChannelEnum.JIUJI_SMS_CHANNEL_JIUJI.getCode();
            } else if (xtenant.equals(JiujiTenantEnum.JIUJI_TENANT_YAYA.getCode())) {
                result = JiujiSmsChannelEnum.JIUJI_SMS_CHANNEL_YAYA.getCode();
            } else if (xtenant.equals(JiujiTenantEnum.JIUJI_TENANT_HUAWEI.getCode())) {
                result = JiujiSmsChannelEnum.JIUJI_SMS_CHANNEL_HUAWEI.getCode();
            } else {
                result = JiujiSmsChannelEnum.JIUJI_SMS_CHANNEL_JIUJI.getCode();
            }

        } else if (eSmsChannelType.equals(ESmsChannelTypeEnum.YXTD.getCode())) {
            if (xtenant.equals(JiujiTenantEnum.JIUJI_TENANT_JIUJI.getCode())) {
                result = JiujiSmsChannelEnum.JIUJI_SMS_CHANNEL_JIUJI_MARKETING.getCode();
            } else if (xtenant.equals(JiujiTenantEnum.JIUJI_TENANT_YAYA.getCode())) {
                result = JiujiSmsChannelEnum.JIUJI_SMS_CHANNEL_YAYA_MARKETING.getCode();
            } else if (xtenant.equals(JiujiTenantEnum.JIUJI_TENANT_HUAWEI.getCode())) {
                result = JiujiSmsChannelEnum.JIUJI_SMS_CHANNEL_HUAWEI.getCode();
            } else {
                result = JiujiSmsChannelEnum.JIUJI_SMS_CHANNEL_JIUJI_MARKETING.getCode();
            }
        }

        return result;
    }

    // 根据租户 获取 M站，PC站，回收站任意地址
    public static String getUrlByXtenant(Integer xtenant, Integer urlType) {
        try {
            if (EXtenantEnum.TYPE1.getCode().equals(xtenant)) {
                if (urlType == 1) {
                    return JiujiJumpUrlEnum.JIUJI_JUMP_URL_JIUJI_M_URL.getMessage();
                } else if (urlType == 2) {
                    return JiujiJumpUrlEnum.JIUJI_JUMP_URL_JIUJI_WEB_URL.getMessage();
                } else if (urlType == 3) {
                    return JiujiJumpUrlEnum.JIUJI_JUMP_URL_JIUJI_HS_URL.getMessage();
                } else {
                    return JiujiJumpUrlEnum.JIUJI_JUMP_URL_JIUJI_M_URL.getMessage();
                }

            } else if (EXtenantEnum.TYPE2.getCode().equals(xtenant)) {
                if (urlType == 5) {
                    return JiujiJumpUrlEnum.JIUJI_JUMP_URL_YAYA_M_URL.getMessage();
                } else if (urlType == 6) {
                    return JiujiJumpUrlEnum.JIUJI_JUMP_URL_YAYA_WEB_URL.getMessage();
                } else if (urlType == 7) {
                    return JiujiJumpUrlEnum.JIUJI_JUMP_URL_YAYA_HS_URL.getMessage();
                } else {
                    return JiujiJumpUrlEnum.JIUJI_JUMP_URL_YAYA_M_URL.getMessage();
                }
            } else if (EXtenantEnum.TYPE4.getCode().equals(xtenant)) {
                return "https://huawei.ch999.com.cn";
            } else {
                return "";
            }
        } catch (Exception ex) {
            return "";
        }
    }

    /**
     * 根据两点间经纬度坐标（double值），计算两点间距离，单位为米
     *
     * @param lng1 A点的经度
     * @param lat1 A点的纬度
     * @param lng2 B点的经度
     * @param lat2 B点的纬度
     * @return 距离值（单位：米）
     */
    public static double getDistance(double lng1, double lat1, double lng2, double lat2) {
        double radLat1 = rad(lat1);
        double radLat2 = rad(lat2);
        double a = radLat1 - radLat2;
        double b = rad(lng1) - rad(lng2);
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2)
                * Math.pow(Math.sin(b / 2), 2)));
        s = s * EARTH_RADIUS;
        s = Math.round(s * 10000) / 10000.0;
        return s;
    }
    // 把经纬度转为度（°）
    private static double rad(double d) {
        return d * Math.PI / 180.0;
    }
    public static String getShortUrl(Long xtenant, String fullUrl, String des) {
        Map<String, String> params = new HashMap<>();
        params.put("xtenant", String.valueOf(xtenant));
        params.put("description", des);
        params.put("fullUrl", fullUrl);
        String resultJson = HttpClientUtil.post(UrlConstant.SHORT_URL, params);
        R<String> shortUrlRet = JSON.parseObject(resultJson, new TypeReference<R<String>>() {
        });
        if (shortUrlRet.getCode() == ResultCode.SUCCESS && StringUtils.isNotEmpty(shortUrlRet.getData())) {
            fullUrl = shortUrlRet.getData();
        }
        return fullUrl;
    }

    public static R<Boolean> sendZnMsg(ZnSendConnBo con, String url) {
        if (con == null) {
            return R.error("发送消息内容不能为空");
        }
        try {
            Map<String, String> param = new HashMap<>();
            param.put("title", con.getTitle());
            //kind: 1=>活动，4=》其他
            param.put("kind", String.valueOf(con.getKind()));
            param.put("PlatForm", con.getPlatForm());
            if (StringUtils.isNotEmpty(con.getLink())) {
                param.put("link", con.getLink());
            }
            if (StringUtils.isNotEmpty(con.getAppLink())) {
                param.put("AppLink", con.getAppLink());
            }
            if (StringUtils.isNotEmpty(con.getHdimg())) {
                param.put("img", con.getHdimg());
            }
            if (StringUtils.isNotEmpty(con.getEndTime())) {
                param.put("endtime", con.getEndTime());
            }
            param.put("content", con.getContent());
            param.put("ExtraData", con.getExtraData());
            if (8 == con.getKind()) {
                param.put("userid", String.valueOf(0));
                String json = HttpClientUtil.post(url, param);
                if (json != null && "1".equals(json)) {
                    return R.success("发送成功", true);
                }
            } else {
                int nowCount = 0;
                StringBuilder sendUserIds = new StringBuilder();
                if (StringUtils.isNotEmpty(con.getSmsnumber())) {
                    String[] smsArr = con.getSmsnumber().split(",");
                    for (int i = 0; i < smsArr.length; i++) {
                        sendUserIds.append(smsArr[i].replace("\r", "").replace("\n", ""));
                        if (i < smsArr.length - 1) {
                            sendUserIds.append(",");
                        }
                        nowCount++;
                        if (nowCount % 2000 == 0 || nowCount == smsArr.length) {
                            param.put("userid", sendUserIds.toString());
                            String json = HttpClientUtil.post(url, param);
                            sendUserIds = new StringBuilder();
                            if (json != null && "1".equals(json)) {
                                return R.success("发送成功", true);
                            } else {
                                return R.error("发送失败");
                            }

                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("推送站内消息失败：{}", e.getMessage());
        }
        return R.error("发送失败");
    }

    public static boolean startNewUserClass() {
        return LocalDateTime.now().isBefore(LocalDateTime.of(2018, 10, 18, 16, 0, 0));
    }

    public static String getUserType(int userClass) {
        String re = "";
        if (startNewUserClass()) {
            switch (userClass) {
                case 0:
                    re = EUserClassNewEnum.EUserClassNew_1.getMessage();
                    break;
                case 1:
                    re = EUserClassNewEnum.EUserClassNew_2.getMessage();
                    break;
                case 2:
                    re = EUserClassNewEnum.EUserClassNew_3.getMessage();
                    break;
                case 3:
                    re = EUserClassNewEnum.EUserClassNew_4.getMessage();
                    break;
                default:
                    break;
            }
        } else {
            switch (userClass) {
                case 0:
                    re = EUserClassNewEnum.EUserClassNew_0.getMessage();
                    break;
                case 1:
                    re = EUserClassNewEnum.EUserClassNew_1.getMessage();
                    break;
                case 2:
                    re = EUserClassNewEnum.EUserClassNew_2.getMessage();
                    break;
                case 3:
                    re = EUserClassNewEnum.EUserClassNew_3.getMessage();
                    break;
                case 5:
                    re = EUserClassNewEnum.EUserClassNew_5.getMessage();
                    break;
                case 6:
                    re = EUserClassNewEnum.EUserClassNew_6.getMessage();
                    break;
                default:
                    break;
            }
        }
        return re;
    }

    //发送短信
    public static R<Boolean> sendSms(String phone, String content, String sendServer, String sender, String url) {
        try {
            if (!ObjectUtils.allNotNull(sendServer)) {
                return R.error(ResultCode.PARAM_ERROR, "没有短信通道");
            }
            DateFormat format1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String sendtime = format1.format(new Date());
            Map<String, String> param = new HashMap<>();
            param.put("phones", phone);
            param.put("content", content);
            param.put("sendtime", sendtime);
            param.put("server", sendServer);
            param.put("sender", sender);
            String smsRet = HttpClientUtil.post(url, param);
            if (!org.apache.commons.lang3.StringUtils.isBlank(smsRet) && smsRet.contains("True")) {
                return R.success("发送成功", true);
            }
        } catch (Exception e) {
            log.error("短信发送失败,error={}", e.getMessage());
        }
        return R.error(ResultCode.RETURN_ERROR, "短信发送失败");
    }

    /**
     * 把以逗号分隔的ids字符串，转为list，因为用的比较频繁，所以抽一个工具类出来
     */
    public static List<Integer> covertIdStr(String ids) {
        if (StringUtils.isBlank(ids)) {
            //空的时候返回一个空的List，避免NPE异常
            return new ArrayList<>();
        }
        return Arrays.stream(StringUtils.split(ids.replace("，", ",").replace("[", "").replace("]", ""), ","))
                .map(StringUtils::trim)//trim一下
                .filter(StringUtils::isNumeric)//过滤掉不能转成整数数的串
                .filter(e -> e.length() < 10)
                .map(Integer::valueOf)
                .collect(Collectors.toList());
    }

    public static Boolean isNullOrZero(Integer data) {
        return data == null || data.equals(0);
    }
    public static boolean isNotNullZero(Long data) {
        return data != null && data != 0L;
    }
    public static boolean isNotNullZero(Integer data) {
        return data != null && data != 0L;
    }
    public static boolean isNullOrZero(Long data) {
        return data == null || data == 0L;
    }

    //类似微信接口的签名生成方法
    public static String createSign(Map<String, Object> params) {
        StringBuilder sb = new StringBuilder();
        // 将参数以参数名的字典升序排序
        Map<String, Object> sortParams = new TreeMap<>(params);
        // 遍历排序的字典,并拼接"key=value"格式
        for (Map.Entry<String, Object> entry : sortParams.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue().toString().trim();
            if (!StringUtils.isEmpty(value)) {
                sb.append("&").append(key).append("=").append(value);
            }
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(0);
        }
        //将签名使用MD5加密并全部字母变为大写
        log.info("当前签名字符串为：" + sb.toString());
        return SecureUtil.md5(sb.toString()).toUpperCase();
    }

    public static boolean matchIntWithDotDelimiter(String str){
        return INTEGER_DOT_REGEX.matcher(str).matches();
    }

    /**
     * 发布当前线程的渠道消息
     * @param key
     * @param object
     */
    public static void publishLocalThreadMessage(String key,Object object){
        CHANNEL_MESSAGE.get().put(key,object);
    }

    /**
     * 消费渠道中传递的消息
     * @param key
     * @param <T>
     * @return
     */
    public static <T> T consumeLocalThreadMessage(String key){
        LRUCache<String, Object> cache = CHANNEL_MESSAGE.get();
        Object value = cache.get(key);
        cache.remove(key);
        return (T)value;
    }
    public static boolean isJiuJiXtenant(Integer xtenant) {
        return xtenant < JIU_JI_MAX_XTENANT;
    }



    /**
     * 自动查询历史库(没有查到再查)
     * @param queryFun
     * @param <T>
     * @return
     */
    public static <T> T autoQueryHist(Supplier<T> queryFun){
        return autoQueryHist(queryFun,null, (Long) null);
    }

    /**
     *  自动查询历史库
     * @param queryFun
     * @param tableInfoEnum
     * @param tableId 识别参数唯一id
     * @param types 对应类型值, 可以多个
     * @return
     * @param <T>
     */
    public static <T> T autoQueryHist(Supplier<T> queryFun, MTableInfoEnum tableInfoEnum, Integer tableId, Collection types){
        return autoQueryHist(queryFun, tableInfoEnum, Convert.toLong(tableId), ObjectUtil.defaultIfNull(types, Collections.emptyList()).toArray());
    }

    /**
     *  自动查询历史库
     * @param queryFun
     * @param tableInfoEnum
     * @param tableId 识别参数唯一id
     * @param types 对应类型值, 可以多个
     * @return
     * @param <T>
     */
    public static <T> T autoQueryHist(Supplier<T> queryFun, MTableInfoEnum tableInfoEnum, Integer tableId, Object ... types){
        return autoQueryHist(queryFun, tableInfoEnum, Convert.toLong(tableId), types);
    }

    /**
     * 自动查询历史库(没有查到再查)
     * @param queryFun
     * @param tableInfoEnum
     * @param tableId
     * @param types  对应类型值, 可以多个
     * @return
     */
    public static <T> T autoQueryHist(Supplier<T> queryFun, MTableInfoEnum tableInfoEnum, Long tableId, Object ... types){
        Supplier<T> queryHistFun = () -> MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS, () -> queryFun.get());
        if(tableId != null && XtenantEnum.isJiujiXtenant()){
            if(tableInfoEnum == null){
                throw new CustomizeException("主键不为空, 表信息枚举不能为空");
            }
            boolean isHistory = tableInfoEnum.isHistory(tableId,types);
            if(isHistory){
                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "表[{}]{}为: {},类型:{}, 从历史库查询", tableInfoEnum.getCode(),
                        tableInfoEnum.getMmColumnName(), tableId, types);
                //历史库存查询
                return queryHistFun.get();
            }else{
                //实时库查询
                return queryFun.get();
            }
        }
        T t = queryFun.get();
        if(XtenantEnum.isJiujiXtenant() && ObjectUtil.isEmpty(t)){
            return queryHistFun.get();
        }
        return t;
    }


    /**
     * 自动写入历史库(为null 或者false 自动写入)
     * @param queryFun
     * @param <T>
     * @return
     */
    public static <T> T autoWriteHist(Supplier<T> queryFun){
        T t = queryFun.get();
        if(XtenantEnum.isJiujiXtenant() && (ObjectUtil.isEmpty(t) || Boolean.FALSE.equals(t))){
            AtomicReference<T> result = new AtomicReference<>();
            MultipleTransaction.build()
                    .execute(DataSourceConstants.OA_NEW_HIS_WRITE, () -> result.set(queryFun.get()))
                    .commit();
            return result.get();
        }
        return t;
    }


    /**
     * 同时查实时库和历史库
     * @param queryFun
     * @param <T>
     * @return
     */
    public static <T> Collection<T> autoQueryMergeHist(Supplier<Collection<T>> queryFun){
        Collection<T> t = queryFun.get();
        if(XtenantEnum.isJiujiXtenant()){
            return Stream.concat(t.stream(),MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS, () -> queryFun.get()).stream())
                    .collect(Collectors.toList());
        }
        return t;
    }
    /**
     * mybatis 判断字段是否为空或者等于某个值
     * @param column
     * @param value
     * @return
     * @param <T>
     */
    public static <T> Function<LambdaQueryWrapper<T>, LambdaQueryWrapper<T>> isNullOrEq(SFunction<T, ?> column, Object value){
        return cnd -> cnd.eq(column, value).or().isNull(column);
    }

    public static boolean closeBaoZunEsApi(){
        return LocalDateTime.now().isAfter(LocalDateTime.of(2024, 06, 24, 23, 59, 59));
    }

    /**
     * 对比两个列表，返回需要新增、更新和删除的数据
     *
     * @param listOld        listOld   旧数据
     * @param listNew        listNew 新数据
     * @param keyExtractor 用于提取对象唯一标识的函数
     * @param <T>          列表元素类型
     * @param <K>          唯一标识类型
     * @return 对比结果
     */
//    public static <T, K> CompareResult<T> compareLists(List<T> listOld, List<T> listNew, Function<T, K> keyExtractor) {
//        if(CollUtil.isEmpty(listNew)){
//            return new CompareResult<>(Lists.newArrayList(), Lists.newArrayList(), listOld);
//        }
//        if(CollUtil.isEmpty(listOld)){
//            return new CompareResult<>(listNew, Lists.newArrayList(), Lists.newArrayList());
//        }
//        // 将 listA 和 listB 转换为 Map，方便快速查找 [ 过滤掉 key 为空的元素]
//        Map<K, T> mapA = listOld.stream()
//                .filter(config -> keyExtractor.apply(config) != null)
//                .collect(Collectors.toMap(keyExtractor, config -> config));
//        Map<K, T> mapB = listNew.stream()
//                .filter(config -> keyExtractor.apply(config) != null)
//                .collect(Collectors.toMap(keyExtractor, config -> config));
//
//        // 1. 找出需要新增的数据（listB 中存在但 listA 中不存在）
//        List<T> toAdd = listNew.stream()
//                .filter(config -> !mapA.containsKey(keyExtractor.apply(config)))
//                .collect(Collectors.toList());
//
//        // 2. 找出需要更新的数据（listA 和 listB 中都存在，但对象内容不同）
//        List<T> toUpdate = listNew.stream()
//                .filter(e -> mapA.containsKey(keyExtractor.apply(e)))
//                        .filter(config -> !config.equals(mapA.get(keyExtractor.apply(config))))
//                        .collect(Collectors.toList());
//
//        // 3. 找出需要删除的数据（listA 中存在但 listB 中不存在）
//        List<T> toDelete = listOld.stream()
//                .filter(config -> !mapB.containsKey(keyExtractor.apply(config)))
//                .collect(Collectors.toList());
//
//        return new CompareResult<>(toAdd, toUpdate, toDelete);
//    }

    /**
     * 对比两个列表，返回需要新增、更新和删除的数据
     *
     * @param listOld       旧数据
     * @param listNew       新数据
     * @param keyExtractor  用于提取对象唯一标识的函数
     * @param fieldNames    需要排除的字段名
     * @param <T>           列表元素类型
     * @param <K>           唯一标识类型
     * @return 对比结果
     */
    public static <T, K> CompareResult<T> compareLists(List<T> listOld, List<T> listNew, Function<T, K> keyExtractor, String... fieldNames) {
        if (listNew == null || listNew.isEmpty()) {
            return new CompareResult<>(Collections.emptyList(), Collections.emptyList(), listOld);
        }
        if (listOld == null || listOld.isEmpty()) {
            return new CompareResult<>(listNew, Collections.emptyList(), Collections.emptyList());
        }

        // 将 listOld 和 listNew 转换为 Map，方便快速查找 [过滤掉 key 为空的元素]
        Map<K, T> mapA = listOld.stream()
                .filter(config -> keyExtractor.apply(config) != null)
                .collect(Collectors.toMap(keyExtractor, config -> config));
        Map<K, T> mapB = listNew.stream()
                .filter(config -> keyExtractor.apply(config) != null)
                .collect(Collectors.toMap(keyExtractor, config -> config));

        // 1. 找出需要新增的数据（listNew 中存在但 listOld 中不存在）
        List<T> toAdd = listNew.stream()
                .filter(config -> !mapA.containsKey(keyExtractor.apply(config)))
                .collect(Collectors.toList());

        // 2. 找出需要更新的数据（listOld 和 listNew 中都存在，但对象内容不同）
        List<T> toUpdate = listNew.stream()
                .filter(e -> mapA.containsKey(keyExtractor.apply(e)))
                .filter(config -> !isEqualExcludingFields(config, mapA.get(keyExtractor.apply(config)), fieldNames))
                .collect(Collectors.toList());

        // 3. 找出需要删除的数据（listOld 中存在但 listNew 中不存在）
        List<T> toDelete = listOld.stream()
                .filter(config -> !mapB.containsKey(keyExtractor.apply(config)))
                .collect(Collectors.toList());

        return new CompareResult<>(toAdd, toUpdate, toDelete);
    }

    /**
     * 比较两个对象是否相等，排除指定字段
     *
     * @param obj1       对象1
     * @param obj2       对象2
     * @param fieldNames 需要排除的字段名
     * @param <T>        对象类型
     * @return 是否相等
     */
    private static <T> boolean isEqualExcludingFields(T obj1, T obj2, String... fieldNames) {
        if (obj1 == null || obj2 == null) {
            return obj1 == obj2;
        }

        // 获取所有字段
        Field[] fields = obj1.getClass().getDeclaredFields();
        Set<String> excludedFields = new HashSet<>(Arrays.asList(fieldNames));

        try {
            for (Field field : fields) {
                field.setAccessible(true); // 允许访问私有字段
                String fieldName = field.getName();

                // 如果字段在排除列表中，则跳过比较
                if (excludedFields.contains(fieldName)) {
                    continue;
                }

                // 获取字段值并比较
                Object value1 = field.get(obj1);
                Object value2 = field.get(obj2);
                // 特殊处理 BigDecimal
                if (value1 instanceof BigDecimal && value2 instanceof BigDecimal) {
                    if (((BigDecimal) value1).compareTo((BigDecimal) value2) != 0) {
                        return false;
                    }
                }
                // 其他情况用 Objects.equals
                else if (!Objects.equals(value1, value2)) {
                    return false;
                }
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException("Failed to compare objects", e);
        }

        return true;
    }

    /**
     * 对比结果封装类
     *
     * @param <T> 列表元素类型
     */
    public static class CompareResult<T> {
        private final List<T> toAdd;    // 需要新增的数据
        private final List<T> toUpdate; // 需要更新的数据
        private final List<T> toDelete; // 需要删除的数据

        public CompareResult(List<T> toAdd, List<T> toUpdate, List<T> toDelete) {
            this.toAdd = toAdd;
            this.toUpdate = toUpdate;
            this.toDelete = toDelete;
        }

        public List<T> getToAdd() {
            return toAdd;
        }

        public List<T> getToUpdate() {
            return toUpdate;
        }

        public List<T> getToDelete() {
            return toDelete;
        }
    }

    /**
     * 保留两位小数 ： 1000.00
     * @param amount
     * @return
     */
    public static String formatBigDecimal(BigDecimal amount){
        if(null != amount){
            return String.format("%.2f", amount);
        }else {
            return "0.00";
        }
    }
    /**
     *
     * @param amount
     * @return
     */
    public static BigDecimal setZero(BigDecimal amount){
        if(null == amount){
            return BigDecimal.ZERO;
        }else {
            return amount;
        }
    }

    /**
     * 校验字符串是否符合时间格式
     */
    public static boolean isValidFormat(String dateTimeStr) {
        try {
            DateUtil.parseLocalDateTime(dateTimeStr, DatePattern.NORM_DATETIME_PATTERN);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
