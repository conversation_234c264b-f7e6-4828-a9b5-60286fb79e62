package com.jiuji.oa.oacore.yearcardtransfer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.oacore.yearcardtransfer.entity.AreaGuestBookAccountLog;
import com.jiuji.oa.oacore.yearcardtransfer.entity.YearPackageTransferLogPo;

import java.util.List;

public interface AreaGuestBookAccountLogService extends IService<AreaGuestBookAccountLog> {
    /**
     *
     *
     * @param content 操作内容
     * @param inUser 操作人
     * @return 是否记录成功
     */
    /**
     * 快速记录操作日志
     * @param inUser
     * @param contentFormat
     * @param params
     * @return
     */
    boolean saveLog(Integer businessId, String inUser, String contentFormat, String... params);

    List<AreaGuestBookAccountLog> listByBusinessId(Integer businessId);
}
