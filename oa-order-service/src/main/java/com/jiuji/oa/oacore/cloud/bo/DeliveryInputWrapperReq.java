package com.jiuji.oa.oacore.cloud.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/9/23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryInputWrapperReq {
    /**
     * oa 推送接收人类别
     */
    private DeliveryInfoVO input;

    @Data
    public static class DeliveryInfoVO {
        private Long subId;
        private String expectTime;
        private Integer cityId;
        private String address;
        private String detailAddress;
        private String mobile;
        private String subUserName;
        private String addressGps;
    }
}
