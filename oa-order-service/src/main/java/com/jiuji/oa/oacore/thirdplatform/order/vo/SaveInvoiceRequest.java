package com.jiuji.oa.oacore.thirdplatform.order.vo;

import com.sankuai.meituan.shangou.open.sdk.domain.SystemParam;
import com.sankuai.meituan.shangou.open.sdk.request.SgOpenRequest;
import lombok.Data;

@Data
public class SaveInvoiceRequest extends SgOpenRequest {

    private Long order_id;

    private String app_poi_code;

    private String spu_data;

    private String invoice_urls;

    private Integer is_red_invoice;


    public SaveInvoiceRequest(SystemParam systemParam) {
        super("/api/v1/ecommerce/invoice/subsidy/recognize/save", "POST", systemParam);
    }
}