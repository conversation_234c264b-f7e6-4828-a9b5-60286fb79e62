package com.jiuji.oa.oacore.oaorder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * description: <订单状态枚举类>
 * translation: <Order status enumeration class>
 *
 * <AUTHOR>
 * @date 16:11 2019/11/13
 * @since 1.0.0
 **/
@Getter
@AllArgsConstructor
public enum SubCheckEnum implements CodeMessageEnumInterface {

    /**
     * 订单状态 编码-信息
     */
    SUB_CHECK_NULL(null, ""),
    SUB_CHECK_UNCONFIRMED(0, "未确认"),
    SUB_CHECK_CONFIRMED(1, "已确认"),
    SUB_CHECK_OUT_OF_STOCK(2, "已出库"),
    SUB_CHECK_COMPLETED(3, "已完成"),
    SUB_CHECK_DELETED(4, "已删除"),
    SUB_CHECK_WAITING_CONFIRMATION(5, "等待确认"),
    SUB_CHECK_ARREARS(6, "欠款"),
    SUB_CHECK_TO_BE_PROCESSED(7, "待处理"),
    SUB_CHECK_UNSUBSCRIBE(8, "退订"),
    SUB_CHECK_REFUND(9, "退款");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 编码对应信息
     */
    private String message;

    public static String valueOfByCode(Integer code) {
        for (SubCheckEnum enumConstant : SubCheckEnum.class.getEnumConstants()) {
            if (Objects.equals(enumConstant.getCode(), code)) {
                return enumConstant.getMessage();
            }
        }
        return null;
    }
    public static Integer getCodeByMessage(String message) {
        for (SubCheckEnum enumConstant : SubCheckEnum.class.getEnumConstants()) {
            if (Objects.equals(enumConstant.getMessage(), message)) {
                return enumConstant.getCode();
            }
        }
        return null;
    }

    public static SubCheckEnum getEnumByCode(Integer code) {
        for (SubCheckEnum enumConstant : SubCheckEnum.class.getEnumConstants()) {
            if (Objects.equals(enumConstant.getCode(), code)) {
                return enumConstant;
            }
        }
        return null;
    }
}
