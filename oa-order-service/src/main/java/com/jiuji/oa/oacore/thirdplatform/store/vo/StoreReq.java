package com.jiuji.oa.oacore.thirdplatform.store.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.jiuji.tc.utils.fastjson.TrimDeSerializeCodec;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jiuji.oa.oacore.common.util.FieldModified;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 17:54
 * @Description
 */
@Data
@ApiModel(value = "更新门店实体")
public class StoreReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "编号")
    @NotNull(message = "门店ID不能为空")
    private Integer id;

    /**
     * 平台门店编码
     */
    @ApiModelProperty(value = "平台门店编码")
    @JSONField(deserializeUsing = TrimDeSerializeCodec.class)
    private String storeCode;
    /**
     * 本地门店编码
     */
    @ApiModelProperty(value = "本地门店编码")
    @JSONField(deserializeUsing = TrimDeSerializeCodec.class)
    private String areaCode;

    /**
     * 平台编码（JD-京东;MT-美团）
     */
    @ApiModelProperty(value = "平台编码（JD-京东;MT-美团）")
    private String platCode;

    /**
     * 管理门店的信息
     */
    @ApiModelProperty(value = "关联门店的信息")
    private String associatedStores;

    /**
     * 启用状态
     */
    private Boolean associatedStoresFlag;

    /**
     * 大件 供应商id
     */

    private Integer largeChannelId;

    /**
     * 小件 供应商id
     */

    private Integer smallChannelId;

    /**
     * 配送方式
     */
    private Integer deliveryType;
}
