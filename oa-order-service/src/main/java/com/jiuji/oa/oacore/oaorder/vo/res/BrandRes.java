package com.jiuji.oa.oacore.oaorder.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: gengjiaping
 * @date: 2020/3/6
 */
@ApiModel
@Data
public class BrandRes {

    @ApiModelProperty(value = "分类id")
    private Integer cid;

    @ApiModelProperty(value = "分类名字")
    private String cname;

    @JSONField(name = "value")
    @ApiModelProperty(value = "品牌id")
    private Integer id;

    @JSONField(name = "label")
    @ApiModelProperty(value = "品牌名称")
    private String name;
}
