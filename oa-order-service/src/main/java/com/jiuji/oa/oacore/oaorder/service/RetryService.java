package com.jiuji.oa.oacore.oaorder.service;

import com.doudian.open.exception.DoudianOpException;
import com.sankuai.meituan.shangou.open.sdk.exception.SgOpenException;

import java.io.IOException;
import java.util.function.Supplier;

/**
 * 重试服务类
 * <AUTHOR>
 * @since 2023/1/29 17:56
 */
public interface RetryService {

    /**
     * feign 调用触发重试
     * @param <T>
     * @param retryFun
     * @return
     */
     <T> T retryByFeignRetryableException(Supplier<T> retryFun);

    /**
     * 重试通过网络异常
     * @param <T>
     * @param retryFun
     * @return
     */
    <T> T retryByMeituanException(RetryMeituanFun<T> retryFun) throws SgOpenException, IOException, DoudianOpException;

    /**
     * 重试保存异常
     * @param retryFun
     * @return
     * @param <T>
     * @throws SgOpenException
     * @throws IOException
     */
    <T> T retrySaveException(Supplier<T> retryFun);

    interface RetryMeituanFun<T>{
        T get() throws SgOpenException, IOException, DoudianOpException;
    }
}
