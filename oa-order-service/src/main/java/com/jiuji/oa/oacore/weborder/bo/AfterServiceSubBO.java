package com.jiuji.oa.oacore.weborder.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @since 2020/4/12
 */
@Data
public class AfterServiceSubBO {
    private Integer state;
    private Integer subId;
    private LocalDateTime addTime;
    private LocalDateTime finishTime;
    /**
     * 历史库拆分  所以做sql拆分
     * evaluate 表的数据
     *  (case when e.id is null then 0 else 1 end) as ispj
     * left join office.dbo.evaluate e with (nolock) on (e.subid = s.id and e.EvaluateType = 3)
     */
    private Boolean ispj;
    private Boolean isquji;
    private Integer shouyingLock;
    /**
     * 历史库拆分  所以做sql拆分
     * 预约单
     */
    private Integer kind;
    /**
     * 历史库拆分  所以做sql拆分
     * 预约单
     */
    private Integer stype;
    private Integer areaId;
    private String problem;
    /**
     * 历史库拆分  所以做sql拆分
     * 预约单
     */
    private String yuyuePpids;

    private Integer ppid;
    private String productName;
    private String productColor;
    private BigDecimal price;
    private String imei;
    @ApiModelProperty(value = "是否回购机 1是，0不是")
    private Integer ishuishou;
    @ApiModelProperty(value = "对应basket_id")
    private Integer basketId;
    private BigDecimal yifum;
    private Integer userid;

    private BigDecimal feiyong;

    private String imageUrl;

    private Integer relationSubId;

    private Integer isSoft;

    private Integer shouhouSubId;

    private Integer newMachineBasketId;
    private Integer newMachineBasketType;

    private Integer yuyueId;
    /**
     * 是否删除
     */
    private Boolean isDel;
}
