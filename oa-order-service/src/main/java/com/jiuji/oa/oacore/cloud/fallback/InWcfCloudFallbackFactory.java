package com.jiuji.oa.oacore.cloud.fallback;

import com.alibaba.fastjson.JSON;
import com.jiuji.oa.oacore.cloud.InWcfCloud;
import com.jiuji.oa.oacore.cloud.bo.DeliveryInputWrapperReq;
import com.jiuji.oa.oacore.cloud.bo.WXSmsReceiverReq;
import com.jiuji.tc.common.vo.R;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class InWcfCloudFallbackFactory implements FallbackFactory<InWcfCloud> {

    @Override
    public InWcfCloud create(Throwable throwable) {
        log.error("调用c# api接口失败！", throwable);
        return new InWcfCloud() {
            @Override
            public String getGotifyReceiver(WXSmsReceiverReq req) {
                log.warn("查询推送人员失败，请求地址：/oaApi.svc/rest/GetGotifyReceiver ,请求参数：{}", req);
                R<String> error = R.error("调用c# api接口失败！");
                return JSON.toJSONString(error);
            }

            @Override
            public String editThirdPlatformOrder(DeliveryInputWrapperReq req) {
                log.warn("查询推送人员失败，请求地址：/oaApi.svc/rest/EditThirdPlatformOrder ,请求参数：{}", req);
                R<String> error = R.error("调用c# api接口失败！");
                return JSON.toJSONString(error);
            }
        };
    }
}
