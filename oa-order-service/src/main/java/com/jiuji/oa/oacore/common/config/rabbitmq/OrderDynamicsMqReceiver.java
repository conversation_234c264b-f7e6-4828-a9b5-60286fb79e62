package com.jiuji.oa.oacore.common.config.rabbitmq;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ch999.common.util.tenant.Namespaces;
import com.ch999.common.util.utils.Exceptions;
import com.jiuji.oa.oacore.common.bo.OaMqData;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.E02CallbackBodyBO;
import com.jiuji.oa.oacore.thirdplatform.baozun.service.BzTenantClientService;
import com.jiuji.oa.oacore.thirdplatform.order.service.MeiTuanService;
import com.jiuji.oa.oacore.weborder.bo.MqSubDynamicsBO;
import com.jiuji.oa.oacore.weborder.service.SubDynamicsService;
import com.jiuji.tc.utils.common.TraceIdUtil;
import com.jiuji.tc.utils.enums.order.SubDynamicsBusinessNodeEnum;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * 订单动态mq消费者
 *
 * <AUTHOR> Jianhong
 * @date 2022-10-11
 */
@Slf4j
@Component
public class OrderDynamicsMqReceiver {

    private static final String ERROR_INFO = "MQ 异常信息：{}";

    /**
     * 订单动态同步 act
     */
    private static final String ACT_ORDER_DYNAMICS_ASYNC = "orderDynamicsAsync";

    @Resource
    private SubDynamicsService subDynamicsService;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private BzTenantClientService bzTenantClientService;

    @Resource
    private MeiTuanService meiTuanService;

    @RabbitHandler
    @RabbitListener(queuesToDeclare = @Queue(RabbitMqConfig.QUEUE_ORDER_DYNAMICS_MESSAGE), containerFactory = "oaAsyncManualListenerContainerFactory")
    public void process(Message message, Channel channel) {
        try {
            String messageContent = new String(message.getBody());
            log.warn("Receiver from {} message:{}", RabbitMqConfig.QUEUE_ORDER_DYNAMICS_MESSAGE, messageContent);
            OaMqData<MqSubDynamicsBO> oaMqData = JSON.parseObject(messageContent, new TypeReference<OaMqData<MqSubDynamicsBO>>() {});
            if (Objects.isNull(oaMqData)) {

                return;
            }
            // 设置oa传的xtenant
            Integer oaXtenant = oaMqData.getXtenant();
            if (Objects.nonNull(oaXtenant)) {
                Namespaces.set(oaXtenant);
            }
            String act = oaMqData.getAct();
            if (ACT_ORDER_DYNAMICS_ASYNC.equals(act)) {
                MqSubDynamicsBO data = oaMqData.getData();
                // 消费消息
                subDynamicsService.addSubDynamicsByMq(data);
                // 回调
                dynamicsByMqCallback(data);
                return;
            }
            log.warn("unknown act :{}, data:{}", act, JSON.toJSONString(oaMqData));
        } catch (Exception e) {
            log.error("{} consume message exception:{}", RabbitMqConfig.QUEUE_ORDER_DYNAMICS_MESSAGE, e.getMessage(), e);
        } finally {
            try {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } catch (IOException e) {
                log.error("queue:{} ack error:{}", RabbitMqConfig.QUEUE_ORDER_DYNAMICS_MESSAGE, e.getMessage(), e);
            }
        }
    }

    private void dynamicsByMqCallback(MqSubDynamicsBO mqSubDynamicsBo) {
        if (Objects.isNull(mqSubDynamicsBo)
                || Objects.isNull(mqSubDynamicsBo.getBusinessNode())) {

            return;
        }
        Integer businessNode = mqSubDynamicsBo.getBusinessNode();
        SubDynamicsBusinessNodeEnum businessNodeEnum = SubDynamicsBusinessNodeEnum.getBusinessNodeByCode(businessNode);
        if (Objects.isNull(businessNodeEnum)) {

            return;
        }
        switch (businessNodeEnum) {
            case MKC_DIAOBO_DELIVERY:
            case ORDER_ASSOCIATED_DIAOBO:
            case SECOND_GOODS_DIAOBO_DELIVERY:
            case SECOND_HAND_ORDER_ASSOCIATED_DIAOBO:
            case LOGISTICS_PICKUP:
            case LOGISTICS_DELIVERY:
            case LOGISTICS_ARRIVED:
            case SECOND_GOODS_DIAOBO_RECEIVED:
            case EXPRESS_ARRIVED:
            case EXPRESS_DELIVERING:
            case ORDER_STOCK_OUT:
            case SECOND_HAND_ORDER_STOCK_OUT:
            case DIAOBO_DELIVERY:
            case MKC_DIAOBO_RECEIVED:
            case DIAOBO_RECEIVED:
                rabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_ORDER_DYNAMICS_CALLBACK_MESSAGE, JSON.toJSONString(mqSubDynamicsBo));
                break;
            default:
        }

    }


    @RabbitHandler
    @RabbitListener(queuesToDeclare = @Queue(RabbitMqConfig.BAOZUN_PURCHASE_INSTOCK),  containerFactory = "oaListenerContainerFactory")
    public void e02(Message message, Channel channel) {
        MDC.put(TraceIdUtil.TRACE_ID_KEY, TraceIdUtil.getTraceId());
        try {
            log.info(message.toString());
            String msg = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("从rabbitmq获取宝尊采购入库消息：{}", msg);
            if (StringUtils.isEmpty(msg)) {
                log.error("从rabbitmq获取宝尊采购入库消息为空！");
                return;
            }
            E02CallbackBodyBO vo = JSONUtil.toBean(msg, E02CallbackBodyBO.class);
            bzTenantClientService.e02(vo);
        } catch (Exception e) {
            log.error(ERROR_INFO, Exceptions.getStackTraceAsString(e));
        }finally {
            MDC.remove(TraceIdUtil.TRACE_ID_KEY);
        }
    }
//
//    @RabbitHandler
//    @RabbitListener(queuesToDeclare = @Queue(RabbitMqConfig.MEITUAN_PRINT_PICK),  containerFactory = "oaListenerContainerFactory")
//    public void meituanPrintPick(Message message, Channel channel) {
//        log.info(message.toString());
//        try {
//            String msg = new String(message.getBody(), StandardCharsets.UTF_8);
//            log.info("从rabbitmq获取美团打印小票消息：{}", msg);
//            if (StringUtils.isEmpty(msg)) {
//                log.error("从rabbitmq获取美团打印小票消息为空！");
//                return;
//            }
//            Thread.sleep(120000);
//            PreparationMealCompleteBO vo = JSONUtil.toBean(msg, PreparationMealCompleteBO.class);
//            meiTuanService.pickingCompleted(vo);
//        } catch (Exception e) {
//            log.error(ERROR_INFO, Exceptions.getStackTraceAsString(e));
//        }
//    }


}
