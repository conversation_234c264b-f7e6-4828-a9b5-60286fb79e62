package com.jiuji.oa.oacore.thirdplatform.tuangou.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import com.jiuji.oa.oacore.apollo.ApolloEntity;
import com.jiuji.oa.oacore.common.constant.RedisKeyConstant;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.thirdplatform.common.enums.PlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.TuangouPlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.common.util.JsonUtils;
import com.jiuji.oa.oacore.thirdplatform.order.entity.MtPaymentLog;
import com.jiuji.oa.oacore.thirdplatform.order.service.MtPaymentLogService;
import com.jiuji.oa.oacore.thirdplatform.store.entity.Store;
import com.jiuji.oa.oacore.thirdplatform.store.service.StoreService;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.TenantService;
import com.jiuji.oa.oacore.thirdplatform.tenant.vo.TenantStoreReq;
import com.jiuji.oa.oacore.thirdplatform.tuangou.dto.TenantStoreDto;
import com.jiuji.oa.oacore.thirdplatform.tuangou.enums.MeituanTuangouBusinessEnum;
import com.jiuji.oa.oacore.thirdplatform.tuangou.service.MeituanTuangouService;
import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.MeituanTuangouToken;
import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.req.*;
import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.res.*;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.meituan.sdk.DefaultMeituanClient;
import com.meituan.sdk.MeituanClient;
import com.meituan.sdk.MeituanResponse;
import com.meituan.sdk.auth.MeituanTokenData;
import com.meituan.sdk.auth.MeituanTokenResponse;
import com.meituan.sdk.internal.exceptions.MtSdkException;
import com.meituan.sdk.internal.utils.SignerUtil;
import com.meituan.sdk.model.ddzh.common.migrateSession.MigrateSessionRequest;
import com.meituan.sdk.model.ddzh.common.migrateSession.MigrateSessionResponse;
import com.meituan.sdk.model.ddzh.common.pageQuerySession.PageQuerySessionRequest;
import com.meituan.sdk.model.ddzh.common.pageQuerySession.PageQuerySessionResponse;
import com.meituan.sdk.model.ddzh.common.pageQuerySessionTokenMapping.PageQuerySessionTokenMappingRequest;
import com.meituan.sdk.model.ddzh.common.pageQuerySessionTokenMapping.PageQuerySessionTokenMappingResponse;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptConsume.ReceiptConsumeResult;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptConsume.RpaymentDetail;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptConsume.TuangouReceiptConsumeRequest;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptConsume.TuangouReceiptConsumeResponse;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptPrepare.PaymentDetailSub;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptPrepare.TuangouReceiptPrepareRequest;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptPrepare.TuangouReceiptPrepareResponse;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptReverseconsume.TuangouReceiptReverseconsumeRequest;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptReverseconsume.TuangouReceiptReverseconsumeResponse;
import com.meituan.sdk.model.ddzhkh.auth.queryPoiMapping.PoiInfoTO;
import com.meituan.sdk.model.ddzhkh.auth.queryPoiMapping.QueryPoiMappingRequest;
import com.meituan.sdk.model.ddzhkh.auth.queryPoiMapping.QueryPoiMappingResponse;
import com.meituan.sdk.model.ddzhkh.dingdan.orderQueryInfo.OrderQueryInfoRequest;
import com.meituan.sdk.model.ddzhkh.dingdan.orderQueryInfo.OrderQueryInfoResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 抖音生活服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class MeituanTuangouServiceImpl implements MeituanTuangouService {
    @Resource
    private TenantService tenantService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private MtPaymentLogService mtPaymentLogService;
    @Resource
    private ApolloEntity apolloEntity;
    @Resource
    private StoreService storeService;


    @Override
    public boolean isMyPlatfrom(String platfrom) {
        return Boolean.TRUE.equals(apolloEntity.getMeituanTuangouSwitch()) && TuangouPlatfromEnum.MTTG.getPlatfromCode().equals(platfrom);
    }

    @Override
    public String certificatePrepare (CertificatePrepareReq req) {
        MeituanTuangouToken token = getTokenByCache(req.getAreaId(), MeituanTuangouBusinessEnum.BUSINESS_58.getCode());
        MeituanClient meituanClient = DefaultMeituanClient.builder(Convert.toLong(token.getAppKey()), token.getAppSecret()).build();
        TuangouReceiptPrepareRequest request = new TuangouReceiptPrepareRequest();
        request.setReceiptCode(req.getCode());
        String appAuthToken = token.getAccessToken();
        MeituanResponse<TuangouReceiptPrepareResponse> response = null;
        try {
            response = meituanClient.invokeApi(request, appAuthToken);
            log.info("调用美团新验券校验接口,request={}，response={}", JsonUtils.toJson(request), JsonUtils.toJson(response));
        } catch (MtSdkException e) {
            RRExceptionHandler.logError("调用美团验券校验接口异常", request, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
        if (response == null) {
            throw new CustomizeException("调用美团验券校验接口异常，请稍后再试");
        }
        TuangouDataRes<CertificatePrepareData> res = new TuangouDataRes<>();
        CertificatePrepareData certificatePrepare = new CertificatePrepareData();
        certificatePrepare.setErrorCode(response.isSuccess() ? 0 : 1);
        certificatePrepare.setDescription(response.getMsg());
        if (response.isSuccess()) {
            certificatePrepare.setErrorCode(0);
            CertificateData certificateData = toCertificateData(response.getData());
            certificatePrepare.setCertificates(Collections.singletonList(certificateData));
            certificatePrepare.setVerifyToken(UUID.randomUUID().toString());
        }
        res.setData(certificatePrepare);
        return JsonUtils.toJson(res);
    }

    private CertificateData toCertificateData(TuangouReceiptPrepareResponse data) {
        if (Objects.isNull(data)) {
            return null;
        }
        CertificateData certificateData = new CertificateData();
        certificateData.setCount(data.getCount());
        certificateData.setExpireTime(data.getReceiptEndDate());
        certificateData.setEncryptedCode(data.getReceiptCode());
        /**
         * 类型说明：
         * 2：抵用券
         * 5：积分
         * 6：立减
         * 8：商户抵用券
         * 10：C端美团支付
         * 12：优惠代码
         * 15：美团立减
         * 17：商家立减
         * 18：美团商家立减
         * 21：次卡
         * 22：打折卡
         * 23：B端美团支付
         * 24：全渠道会员券
         * 25：pos支付
         * 26：线下认款平台
         * 28：商家折上折
         * 29：美团分销支付
         */
        List<PaymentDetailSub> paymentDetail = data.getPaymentDetail();
        //8，17，18，22，24，28表示商家优惠；10，23，25，26，29表示用户支付；其余为平台优惠
        List<Integer> merchantType = Arrays.asList(8, 17, 18, 22, 24, 28);
        List<Integer> payType = Arrays.asList(10,23,25,26,29);

        Long merchantTicketAmount = paymentDetail.stream().filter(v -> merchantType.contains(v.getAmountType()))
                .map(v -> Convert.toLong(new BigDecimal(v.getAmount()).multiply(new BigDecimal(NumberConstant.ONE_HUNDRED)).longValue()))
                .reduce(Long::sum).orElse(0L);
        Long payAmount = BigDecimal.valueOf(ObjectUtil.defaultIfNull(data.getDealPrice(), 0D))
                .multiply(new BigDecimal(NumberConstant.ONE_HUNDRED)).longValue();
        CertificateAmount certificateAmount = new CertificateAmount();
        certificateAmount.setPayAmount(payAmount);
        certificateAmount.setMerchantTicketAmount(merchantTicketAmount);
        certificateAmount.setCouponPayAmount(payAmount);
        certificateData.setAmount(certificateAmount);

        CertificateSku certificateSku = new CertificateSku();
        certificateSku.setSkuId(Convert.toStr(data.getDealGroupId()));
        certificateSku.setAccountId(data.getMobile());
        certificateSku.setTitle(data.getDealTitle());
        certificateSku.setMarketPrice(Convert.toLong(ObjectUtil.defaultIfNull(data.getDealMarketPrice(), 0D) * NumberConstant.ONE_HUNDRED));
        certificateData.setSku(certificateSku);
        return certificateData;
    }

    /**
     * 保存支付明细
     * @param receiptConsumeResponse
     */
    @Override
    public void savePayment(TuangouReceiptConsumeResponse receiptConsumeResponse) {
        try {
            List<ReceiptConsumeResult> data = receiptConsumeResponse.getResult();
            //只有九机触发
            if(CollectionUtils.isNotEmpty(data) && XtenantEnum.isJiujiXtenant()){
                List<MtPaymentLog> MtPaymentLogList = new ArrayList<>();
                for (ReceiptConsumeResult datum : data) {
                    List<RpaymentDetail> paymentDetail = datum.getPaymentDetail();
                    if(CollectionUtils.isNotEmpty(paymentDetail)){
                        paymentDetail.forEach(v -> {
                            MtPaymentLog mtPaymentLog = new MtPaymentLog();
                            mtPaymentLog.setOrderId(datum.getOrderId());
                            mtPaymentLog.setPaymentDetailId(v.getPaymentDetailId());
                            mtPaymentLog.setAmount(Convert.toBigDecimal(v.getAmount()));
                            mtPaymentLog.setReceiptCode(datum.getReceiptCode());
                            mtPaymentLog.setAmountType(Convert.toLong(v.getAmountType()));
                            MtPaymentLogList.add(mtPaymentLog);
                        });
                    }
                }
                mtPaymentLogService.saveBatchByOrderId(MtPaymentLogList);
            }
        } catch (Exception e) {
            RRExceptionHandler.logError("美团支付订单记录明细", receiptConsumeResponse, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
    }

    @Override
    public String certificateVerify (CertificateVerifyReq req) {
        if (CollectionUtils.isEmpty(req.getEncryptedCodes())) {
            throw new CustomizeException("核销券码不能为空");
        }
        MeituanTuangouToken token = getTokenByCache(req.getAreaId(), MeituanTuangouBusinessEnum.BUSINESS_58.getCode());
        MeituanClient meituanClient = DefaultMeituanClient.builder(Convert.toLong(token.getAppKey()), token.getAppSecret()).build();
        TuangouReceiptConsumeRequest request = new TuangouReceiptConsumeRequest();
        request.setReceiptCode(req.getEncryptedCodes().get(0));
        request.setCount(Optional.ofNullable(req.getCount()).orElse(1));
        request.setRequestId(req.getVerifyToken());
        request.setAppShopAccountName(req.getAppShopAccountname());
        request.setAppShopAccount(req.getAppShopAccount());

        String appAuthToken = token.getAccessToken();
        MeituanResponse<TuangouReceiptConsumeResponse> response = null;
        try {
            response = meituanClient.invokeApi(request, appAuthToken);
            log.info("调用美团新验券接口,request={}，response={}", JsonUtils.toJson(request), JsonUtils.toJson(response));
        } catch (MtSdkException e) {
            RRExceptionHandler.logError("调用美团验券接口异常", request, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
        if (response == null) {
            throw new CustomizeException("调用美团验券接口异常，请稍后再试");
        }
        //记录支付记录
        savePayment(response.getData());
        CertificateVerifyData certificateVerifyData = new CertificateVerifyData();
        certificateVerifyData.setErrorCode(response.isSuccess() ? 0 : 1);
        certificateVerifyData.setDescription(response.getMsg());
        if (response.isSuccess() && response.getData() != null && CollectionUtils.isNotEmpty(response.getData().getResult())) {
            certificateVerifyData.setErrorCode(0);
            List<CertificateVerifyResultData> verifyResults = response.getData().getResult().stream()
                    .map(v -> LambdaBuild.create(CertificateVerifyResultData.class)
                            .set(CertificateVerifyResultData::setResult, 0)
                            .set(CertificateVerifyResultData::setOrderId, v.getOrderId())
                            .set(CertificateVerifyResultData::setOriginCode, v.getReceiptCode())
                            .set(CertificateVerifyResultData::setCertificateId, v.getReceiptCode())
                            .set(CertificateVerifyResultData::setVerifyId, Convert.toStr(v.getDealId()))
                            .build()).collect(Collectors.toList());
            certificateVerifyData.setVerifyResults(verifyResults);
        }
        TuangouDataRes<CertificateVerifyData> res = new TuangouDataRes<>();
        res.setData(certificateVerifyData);
        return JsonUtils.toJson(res);
    }

    @Override
    public String certificateCancel (CertificateCancelReq req) {
        MeituanTuangouToken token = getTokenByCache(req.getAreaId(), MeituanTuangouBusinessEnum.BUSINESS_58.getCode());
        MeituanClient meituanClient = DefaultMeituanClient.builder(Convert.toLong(token.getAppKey()), token.getAppSecret()).build();
        TuangouReceiptReverseconsumeRequest tuangouReceiptReverseconsumeRequest = new TuangouReceiptReverseconsumeRequest();
        tuangouReceiptReverseconsumeRequest.setAppShopAccount(req.getAppShopAccount());
        tuangouReceiptReverseconsumeRequest.setAppShopAccountName(req.getAppShopAccountname());
        tuangouReceiptReverseconsumeRequest.setReceiptCode(req.getCertificateId());
        tuangouReceiptReverseconsumeRequest.setDealId(req.getVerifyId());

        String appAuthToken = token.getAccessToken();
        MeituanResponse<TuangouReceiptReverseconsumeResponse> response = null;
        try {
            response = meituanClient.invokeApi(tuangouReceiptReverseconsumeRequest, appAuthToken);
            log.info("调用美团新撤销验券接口,request={}，response={}", JsonUtils.toJson(tuangouReceiptReverseconsumeRequest), JsonUtils.toJson(response));
        } catch (MtSdkException e) {
            RRExceptionHandler.logError("调用美团撤销验券接口异常", tuangouReceiptReverseconsumeRequest, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
        if (response == null) {
            throw new CustomizeException("调用美团撤销验券接口异常，请稍后再试");
        }
        CertificateCancelData certificateCancelData = new CertificateCancelData();
        certificateCancelData.setErrorCode(response.isSuccess() ? 0 : 1);
        if (response.isSuccess() && response.getData() != null && CollectionUtils.isNotEmpty(response.getData().getResult())) {
            certificateCancelData.setErrorCode(0);
        }
        certificateCancelData.setDescription(response.getMsg());
        TuangouDataRes<CertificateCancelData> res = new TuangouDataRes<>();
        res.setData(certificateCancelData);
        return JsonUtils.toJson(res);
    }

    @Override
    public String certificateGet (CertificateGetReq req) {
        return null;
    }

    @Override
    public String certificateQuery (CertificateQueryReq req) {
        MeituanTuangouToken token = getTokenByCache(req.getAreaId(), MeituanTuangouBusinessEnum.BUSINESS_59.getCode());
        MeituanClient meituanClient = DefaultMeituanClient.builder(Convert.toLong(token.getAppKey()), token.getAppSecret()).build();
        OrderQueryInfoRequest orderQueryInfoRequest = new OrderQueryInfoRequest();
        //orderQueryInfoRequest.setOrderId("string");
        orderQueryInfoRequest.setType(Arrays.asList(1,2));
        orderQueryInfoRequest.setUnifiedOrderId(req.getOrderId());
        String appAuthToken = token.getAccessToken();
        MeituanResponse<OrderQueryInfoResponse> response = null;
        try {
            response = meituanClient.invokeApi(orderQueryInfoRequest, appAuthToken);
            log.info("调用美团新订单及券码状态查询,request={}，response={}", JsonUtils.toJson(orderQueryInfoRequest), JsonUtils.toJson(response));
        } catch (MtSdkException e) {
            RRExceptionHandler.logError("调用美团订单及券码状态查询接口异常", orderQueryInfoRequest, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
        if (response == null) {
            throw new CustomizeException("调用美团订单及券码状态查询异常，请稍后再试");
        }
        CertificateQueryData certificateQueryData = new CertificateQueryData();
        //只可撤销当天核销且未超过10分钟的团购券
        certificateQueryData.setCancelLimitTime(NumberConstant.TEN);
        certificateQueryData.setErrorCode(response.isSuccess() ? 0 : 1);
        certificateQueryData.setDescription(response.getMsg());
        if (Objects.nonNull(response.getData())) {
            certificateQueryData.setErrorCode(0);
            List<CertificateInfoData> certificates = response.getData().getReceiptList().stream().map(v -> LambdaBuild.create(CertificateInfoData.class)
                    .set(CertificateInfoData::setCode, v.getReceiptCode())
                    .set(CertificateInfoData::setEncryptedCode, v.getReceiptCode())
                    .set(CertificateInfoData::setStatus, toTuangouStatus(v.getStatus()))
                    .build()).collect(Collectors.toList());
            certificateQueryData.setCertificates(certificates);
        }
        TuangouDataRes<CertificateQueryData> res = new TuangouDataRes<>();
        res.setData(certificateQueryData);
        return JsonUtils.toJson(res);
    }

    private Integer toTuangouStatus(Integer status) {
        Integer res = status;
        switch (status) {
            case 3:
                res = 7;
                break;
            case 4:
            case 6:
                res = 4;
                break;
            default:
                break;
        }
        return res;
    }

    /**
     * 授权后查询缓存token
     * @param appKey
     * @param authCode
     * @param businessId
     * @return
     */
    @Override
    public String meituanAuthCallBack (String appKey, String authCode, Integer businessId, String state) {
        Tenant tenant = tenantService.getTenantByPlatCodeAndAppkeyV2(PlatfromEnum.MTTG.name(), appKey);
        if (Objects.isNull(tenant)) {
            throw new CustomizeException("未查询到美团团购配置信息");
        }
        String token = "";
        try {
            MeituanClient meituanClient = DefaultMeituanClient.builder(Convert.toLong(tenant.getAppKey()), tenant.getAppSecret()).build();
            MeituanTokenResponse authToken = meituanClient.getOAuthToken(businessId, authCode);
            if (authToken.isSuccess()) {
                MeituanTokenData authTokenData = authToken.getData();
                MeituanTuangouToken tuangouToken = new MeituanTuangouToken();
                tuangouToken.setAppKey(tenant.getAppKey());
                tuangouToken.setAppSecret(tenant.getAppSecret());
                tuangouToken.setAccessToken(authTokenData.getAccessToken());
                tuangouToken.setRefreshToken(authTokenData.getRefreshToken());
                tuangouToken.setBid(authTokenData.getOpBizCode());
                tuangouToken.setExpiresIn(Convert.toLong(authTokenData.getExpireIn()));
                tuangouToken.setScope(authTokenData.getScope());
                tuangouToken.setOpBizCode(authTokenData.getOpBizCode());
                tuangouToken.setOpBizName(authTokenData.getOpBizName());
                tuangouToken.setExpiresTime(LocalDateTime.now().plusSeconds(tuangouToken.getExpiresIn()).minusSeconds(NumberConstant.SIXTY));
                String tokenKey = StrUtil.format(RedisKeyConstant.MEITUAN_TUANGOU_TOKEN,appKey,businessId);
                if (MeituanTuangouBusinessEnum.BUSINESS_58.getCode().equals(businessId)) {
                    Store store = storeService.getStoreByopPoiId(tenant.getTenantCode(), tenant.getPlatCode(), authTokenData.getOpBizCode());
                    if (store != null) {
                        tokenKey = StrUtil.format(RedisKeyConstant.MEITUAN_TUANGOU_TOKEN1,appKey,businessId, store.getAreaId());
                    } else {
                        tokenKey = StrUtil.format(RedisKeyConstant.MEITUAN_TUANGOU_TOKEN1,appKey,businessId, state);
                        log.warn("美团混淆门店未查询到门店信息,appKey={}，tuangouToken：{}", appKey,JsonUtils.toJson(tuangouToken));
                        RRExceptionHandler.logError("美团混淆门店未查询到门店信息", authTokenData, null, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
                    }
                }
                stringRedisTemplate.opsForValue().set(tokenKey, JsonUtils.toJson(tuangouToken), NumberConstant.THIRTY_ONE, TimeUnit.DAYS);
                token = tuangouToken.getAccessToken();
            }
        } catch (MtSdkException e) {
            RRExceptionHandler.logError("调用美团获取授权token接口异常", authCode, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
        return token;
    }

    /**
     * 获取授权链接
     *
     * @param areaId
     * @return
     */
    @Override
    public String meituanAuthUrl(Integer areaId, Integer businessId) {
        TenantStoreReq tenantStoreReq = TenantStoreReq.builder().platCode(PlatfromEnum.MTTG.name()).areaId(areaId).appType(1).build();
        TenantStoreDto tenantStore = tenantService.getTenantStore(tenantStoreReq);
        String signKey = tenantStore.getAppSecret();
        String developerId = tenantStore.getAppKey();
        businessId = CommonUtil.isNullOrZero(businessId) ? MeituanTuangouBusinessEnum.BUSINESS_58.getCode() : businessId;

        Map<String,String> params = new HashMap<>();
        params.put("developerId",developerId);
        params.put("businessId",Convert.toStr(businessId));
        params.put("timestamp", Convert.toStr(DateUtil.currentSeconds()));
        params.put("charset",CharsetUtil.UTF_8);
        //params.put("scope",MeituanTuangouBusinessEnum.BUSINESS_58.getCode().toString())
        //oa门店
        params.put("state",Convert.toStr(areaId));
        //美团门店
        //params.put("poiId",tenantStore.getStoreCode());
        //params.put("poiName",tenantStore.getStoreName());
        Map<String, Object> urlParams = new HashMap<>(params);
        try {
            String sign = SignerUtil.getSign(signKey, params);
            urlParams.put("sign", sign);
        } catch (MtSdkException e) {
            throw new CustomizeException(StrUtil.format("获取授权链接异常"), e);
        }
        return HttpUtil.urlWithForm("https://open-erp.meituan.com/general/auth", urlParams, CharsetUtil.CHARSET_UTF_8, false);
    }

    @Override
    public Boolean meituanRefreshToken(String appKey) {
        Tenant tenant = tenantService.getTenantByPlatCodeAndAppkey(PlatfromEnum.MTTG.name(), appKey);
        if (Objects.isNull(tenant)) {
            throw new CustomizeException("未查询到美团团购配置信息");
        }
        //刷新到店综合(客户)token
        Integer businessId = MeituanTuangouBusinessEnum.BUSINESS_59.getCode();
        String tokenKey = StrUtil.format(RedisKeyConstant.MEITUAN_TUANGOU_TOKEN,appKey,businessId);
        String tokenStr = stringRedisTemplate.opsForValue().get(tokenKey);
        if (StringUtils.isNotBlank(tokenStr)) {
            MeituanTuangouToken meituanTuangouToken = JsonUtils.fromJson(tokenStr, MeituanTuangouToken.class);
            if (Objects.nonNull(meituanTuangouToken.getExpiresTime()) && LocalDateTime.now().isAfter(meituanTuangouToken.getExpiresTime().minusDays(2))) {
                meituanTuangouToken = refreshToken(meituanTuangouToken,businessId,1);
                //设置过期时间
                meituanTuangouToken.setExpiresTime(LocalDateTime.now().plusSeconds(meituanTuangouToken.getExpiresIn()).minusSeconds(NumberConstant.SIXTY));
                stringRedisTemplate.opsForValue().set(tokenKey, JsonUtils.toJson(meituanTuangouToken), NumberConstant.THIRTY_ONE, TimeUnit.DAYS);
            }
        }
        //刷新到店综合token
        businessId = MeituanTuangouBusinessEnum.BUSINESS_58.getCode();
        List<Store> storeList = storeService.lambdaQuery().eq(Store::getTenantCode, tenant.getTenantCode())
                .eq(Store::getPlatCode, tenant.getPlatCode()).list();
        for (Store store : storeList) {
            tokenKey = StrUtil.format(RedisKeyConstant.MEITUAN_TUANGOU_TOKEN1,appKey,businessId,store.getAreaId());
            tokenStr = stringRedisTemplate.opsForValue().get(tokenKey);
            if (StringUtils.isBlank(tokenStr)) {
                continue;
            }
            MeituanTuangouToken meituanTuangouToken = JsonUtils.fromJson(tokenStr, MeituanTuangouToken.class);
            if (Objects.nonNull(meituanTuangouToken.getExpiresTime()) && LocalDateTime.now().isAfter(meituanTuangouToken.getExpiresTime().minusDays(2))) {
                meituanTuangouToken = refreshToken(meituanTuangouToken,businessId,store.getAreaId());
                //设置过期时间
                meituanTuangouToken.setExpiresTime(LocalDateTime.now().plusSeconds(meituanTuangouToken.getExpiresIn()).minusSeconds(NumberConstant.SIXTY));
                stringRedisTemplate.opsForValue().set(tokenKey, JsonUtils.toJson(meituanTuangouToken), NumberConstant.THIRTY_ONE, TimeUnit.DAYS);
            }
        }
        return true;
    }

    /**
     * 从缓存查询token
     * @return
     */
    public MeituanTuangouToken getTokenByCache(Integer areaId, Integer businessId) {
        TenantStoreReq tenantStoreReq = TenantStoreReq.builder().platCode(PlatfromEnum.MTTG.name()).areaId(areaId).appType(1).build();
        TenantStoreDto tenantStore = tenantService.getTenantStore(tenantStoreReq);
        if (Objects.isNull(tenantStore)) {
            throw new CustomizeException(StrUtil.format("商户信息或门店{}配置不存在或未启用", areaId));
        }
        String appKey = tenantStore.getAppKey();

        String tokenKey = StrUtil.format(RedisKeyConstant.MEITUAN_TUANGOU_TOKEN,appKey,businessId);
        String tokenStr = "";
        if (MeituanTuangouBusinessEnum.BUSINESS_58.getCode().equals(businessId)) {
            tokenKey = StrUtil.format(RedisKeyConstant.MEITUAN_TUANGOU_TOKEN1,appKey,businessId,areaId);
            tokenStr = stringRedisTemplate.opsForValue().get(tokenKey);
            if (StringUtils.isBlank(tokenStr)) {
                //默认使用使用k100
                tokenKey = StrUtil.format(RedisKeyConstant.MEITUAN_TUANGOU_TOKEN1,appKey,businessId,825);
            }
        }
        if (StringUtils.isBlank(tokenStr)) {
            tokenStr = stringRedisTemplate.opsForValue().get(tokenKey);
        }
        if (StringUtils.isBlank(tokenStr)) {
            throw new CustomizeException("美团团购配置页面重新授权！");
        }
        MeituanTuangouToken meituanTuangouToken = JsonUtils.fromJson(tokenStr, MeituanTuangouToken.class);
        if (Objects.nonNull(meituanTuangouToken.getExpiresTime()) && LocalDateTime.now().isAfter(meituanTuangouToken.getExpiresTime())) {
            meituanTuangouToken = refreshToken(meituanTuangouToken,businessId,areaId);
            //设置过期时间
            meituanTuangouToken.setExpiresTime(LocalDateTime.now().plusSeconds(meituanTuangouToken.getExpiresIn()).minusSeconds(NumberConstant.SIXTY));
            stringRedisTemplate.opsForValue().set(tokenKey, JsonUtils.toJson(meituanTuangouToken), NumberConstant.THIRTY_ONE, TimeUnit.DAYS);
        }
        return meituanTuangouToken;
    }

    /**
     * 刷新token
     * @param meituanTuangouToken
     * @return
     */
    public MeituanTuangouToken refreshToken (MeituanTuangouToken meituanTuangouToken,Integer businessId,Integer areaId) {
        try {
            MeituanClient meituanClient = DefaultMeituanClient.builder(Convert.toLong(meituanTuangouToken.getAppKey()), meituanTuangouToken.getAppSecret()).build();
            MeituanTokenResponse authToken = meituanClient.refreshToken(businessId, meituanTuangouToken.getRefreshToken());
            if (authToken.isSuccess()) {
                MeituanTokenData authTokenData = authToken.getData();
                meituanTuangouToken.setAccessToken(authTokenData.getAccessToken());
                meituanTuangouToken.setRefreshToken(authTokenData.getRefreshToken());
                meituanTuangouToken.setBid(authTokenData.getOpBizCode());
                meituanTuangouToken.setExpiresIn(Convert.toLong(authTokenData.getExpireIn()));
                meituanTuangouToken.setScope(authTokenData.getScope());
                meituanTuangouToken.setOpBizCode(authTokenData.getOpBizCode());
                meituanTuangouToken.setOpBizName(authTokenData.getOpBizName());
                meituanTuangouToken.setExpiresTime(LocalDateTime.now().plusSeconds(meituanTuangouToken.getExpiresIn()).minusSeconds(NumberConstant.SIXTY));
                String tokenKey = StrUtil.format(RedisKeyConstant.MEITUAN_TUANGOU_TOKEN,meituanTuangouToken.getAppKey(),businessId);
                if (MeituanTuangouBusinessEnum.BUSINESS_58.getCode().equals(businessId)) {
                    tokenKey = StrUtil.format(RedisKeyConstant.MEITUAN_TUANGOU_TOKEN1,meituanTuangouToken.getAppKey(),businessId,areaId);
                }
                stringRedisTemplate.opsForValue().set(tokenKey, JsonUtils.toJson(meituanTuangouToken), NumberConstant.THIRTY_ONE, TimeUnit.DAYS);
            }
        } catch (MtSdkException e) {
            RRExceptionHandler.logError("调用美团团购刷新token接口异常", meituanTuangouToken.getRefreshToken(), e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            throw new CustomizeException("appkey:"+meituanTuangouToken.getAppKey()+"刷新美团团购token失败");
        }
        return meituanTuangouToken;
    }

    @Override
    public void queryPoiMapping (String appKey) {
        Tenant tenant = tenantService.getTenantByPlatCodeAndAppkeyV2(PlatfromEnum.MTTG.name(), appKey);
        if (Objects.isNull(tenant)) {
            throw new CustomizeException("未查询到美团团购配置信息");
        }
        MeituanTuangouToken token = getTokenByCache(1, MeituanTuangouBusinessEnum.BUSINESS_59.getCode());
        MeituanClient meituanClient = DefaultMeituanClient.builder(Convert.toLong(token.getAppKey()), token.getAppSecret()).build();
        //查询没有绑定混淆码的门店
        List<Store> storeList = storeService.lambdaQuery().eq(Store::getTenantCode, tenant.getTenantCode())
                .eq(Store::getPlatCode, tenant.getPlatCode()).isNull(Store::getOpPoiId).orderByDesc(Store::getId).list();
        if (CollUtil.isEmpty(storeList)) {
            return;
        }
        Map<String, Store> storeMap = storeList.stream().collect(Collectors.toMap(Store::getStoreCode, Function.identity(), (v1, v2) -> v1));
        List<Long> poiIds = storeList.stream().map(v -> Convert.toLong(v.getStoreCode())).filter(Objects::nonNull).limit(NumberConstant.ONE_HUNDRED)
                .collect(Collectors.toList());
        String appAuthToken = token.getAccessToken();
        QueryPoiMappingRequest queryPoiMappingRequest = new QueryPoiMappingRequest();
        queryPoiMappingRequest.setPoiIds(poiIds);
        try {
            MeituanResponse<QueryPoiMappingResponse> response = meituanClient.invokeApi(queryPoiMappingRequest, appAuthToken);
            if (response != null && response.isSuccess() && CollUtil.isNotEmpty(response.getData().getResult())) {
                List<PoiInfoTO> poiInfoList = response.getData().getResult();
                for (PoiInfoTO poiInfo : poiInfoList) {
                    Store store = storeMap.get(Convert.toStr(poiInfo.getPoiId()));
                    String opPoiId = poiInfo.getOpPoiId();
                    if (store != null && StrUtil.isNotBlank(opPoiId)) {
                        store.setOpPoiId(opPoiId);
                        storeService.updateOpPoiId(store);
                    }
                }
            }
        } catch (MtSdkException e) {
            RRExceptionHandler.logError("调用美团查询对应客户门店ID映射关系", queryPoiMappingRequest, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
    }

    @Override
    @SneakyThrows
    public R<PageQuerySessionResponse> querySession(String appKey,String bjxAppKey) {
        Tenant tenant = tenantService.getTenantByPlatCodeAndAppkeyV2(PlatfromEnum.MTTG.name(), appKey);
        if (Objects.isNull(tenant)) {
            throw new CustomizeException("未查询到美团团购配置信息");
        }
        MeituanClient meituanClient = DefaultMeituanClient.builder(Convert.toLong(tenant.getAppKey()), tenant.getAppSecret()).build();
        PageQuerySessionRequest pageQuerySessionRequest = new PageQuerySessionRequest();

        pageQuerySessionRequest.setAppKey(bjxAppKey);
        pageQuerySessionRequest.setLastIndexId(1);
        pageQuerySessionRequest.setPageSize(1);

        MeituanResponse<PageQuerySessionResponse> response = meituanClient.invokeApi(pageQuerySessionRequest);
        log.info("查询原北极星有效session，参数：{}，结果：{}",JsonUtils.toJson(pageQuerySessionRequest),JsonUtils.toJson(response));
        if (response.isSuccess()) {
            return R.success(response.getData());
        } else {
            return R.error(response.getMsg());
        }
    }

    @Override
    @SneakyThrows
    public R<MigrateSessionResponse> migrateSession(String appKey, String bjxAppKey, String session) {
        Tenant tenant = tenantService.getTenantByPlatCodeAndAppkeyV2(PlatfromEnum.MTTG.name(), appKey);
        if (Objects.isNull(tenant)) {
            throw new CustomizeException("未查询到美团团购配置信息");
        }
        MeituanClient meituanClient = DefaultMeituanClient.builder(Convert.toLong(tenant.getAppKey()), tenant.getAppSecret()).build();
        MigrateSessionRequest migrateSessionRequest = new MigrateSessionRequest();
        migrateSessionRequest.setSession(session);
        migrateSessionRequest.setAppKey(bjxAppKey);
        MeituanResponse<MigrateSessionResponse> response = meituanClient.invokeApi(migrateSessionRequest);
        log.info("迁移原北极星session，参数：{}，结果：{}",JsonUtils.toJson(migrateSessionRequest),JsonUtils.toJson(response));
        if (response.isSuccess()) {
            return R.success(response.getData());
        } else {
            return R.error(response.getMsg());
        }
    }

    @Override
    @SneakyThrows
    public R<String> handleSessionTokenMapping(String appKey, String session) {
        Tenant tenant = tenantService.getTenantByPlatCodeAndAppkeyV2(PlatfromEnum.MTTG.name(), appKey);
        if (Objects.isNull(tenant)) {
            throw new CustomizeException("未查询到美团团购配置信息");
        }
        MeituanClient meituanClient = DefaultMeituanClient.builder(Convert.toLong(tenant.getAppKey()), tenant.getAppSecret()).build();
        PageQuerySessionTokenMappingRequest pageQuerySessionTokenMappingRequest = new PageQuerySessionTokenMappingRequest();
        pageQuerySessionTokenMappingRequest.setOffset(0);
        pageQuerySessionTokenMappingRequest.setLimit(10);
        pageQuerySessionTokenMappingRequest.setSession(session);
        MeituanResponse<PageQuerySessionTokenMappingResponse> response = meituanClient.invokeApi(pageQuerySessionTokenMappingRequest);
        log.info("查询已迁移原北极星session和合作中心token的映射关系，参数：{}，结果：{}",JsonUtils.toJson(pageQuerySessionTokenMappingRequest),JsonUtils.toJson(response));
        if (response.isSuccess()) {
            response.getData();
        } else {
            return R.error(response.getMsg());
        }
        return R.error("成功");
    }
}
