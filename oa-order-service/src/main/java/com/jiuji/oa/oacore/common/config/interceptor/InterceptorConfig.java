package com.jiuji.oa.oacore.common.config.interceptor;

import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.cloud.oaapi.service.AuthenticationCloud;
import com.jiuji.cloud.oaapi.vo.response.UserInfoAuthCloudVO;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.config.xtenant.MultitenancyInterceptor;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.interceptor.TraceIdInterceptor;
import com.jiuji.tc.utils.token.RemoteAnyTokenInterceptor;
import com.jiuji.tc.utils.token.TokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

/**
 * @author: gengjiaping
 * @date: 2019/11/6
 */
@Configuration
@Slf4j
public class InterceptorConfig implements WebMvcConfigurer {

    @Resource
    private OaTokenInterceptor oaTokenInterceptor;

    @Resource
    private SignInterceptor signInterceptor;

    @Resource
    private RemoteAnyTokenInterceptor remoteAnyTokenInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //多租户拦截器
        registry.addInterceptor(new MultitenancyInterceptor());

        List<String> anyTokenList = Arrays.asList(
            "/InApi/webOrder/getElectronicTicket/v1",
            "/InApi/webOrder/updateSubComment/v1",
            "/InApi/webOrder/updateTicket/v1",
            "/InApi/webOrder/list",
            "/InApi/webOrder/getOrderPendingPayment/*",
            "/InApi/webOrder/listLiangpin",
            "/InApi/webOrder/listRecover",
            "/InApi/webOrder/listAfterServiceRepair",
            "/InApi/webOrder/listReservation",
            "/InApi/webOrder/count",
            "/InApi/webOrder/changeSubPpriceid",
            "/InApi/webOrder/getOrderByTypeAndUserId**",
            "/inApi/oaOrder/userOrders/**",
            "/year-package-transfer/**"
        );

        registry.addInterceptor(oaTokenInterceptor).addPathPatterns("/api/**")
                //美团相关拦截配置
                .addPathPatterns("/admin/api/tenant/**")
                .excludePathPatterns("/api/SnapUp/getSet/v1")
                .addPathPatterns("/admin/api/store/**")
                .addPathPatterns("/admin/api/stock/**")
                .addPathPatterns("/admin/api/productConfig/**")
                .addPathPatterns("/admin/api/meituanJdWorkLog/**")
                .excludePathPatterns("/api/dataCompare/downloadRemoteFile")
                .excludePathPatterns("/api/dataCompare/download_cache_file/**")
                .excludePathPatterns("/admin/api/stock/syncToMtAndJdTiming")
                .excludePathPatterns("/admin/api/stock/syncToDouDian")
                .excludePathPatterns("/admin/api/stock/syncToMtStock/v1")
                .excludePathPatterns("/api/hwGoldSeed/**")
                .excludePathPatterns("/api/recoverBasket/**")
                .excludePathPatterns("/api/productinfo/**")
                .excludePathPatterns("/api/cibLogs/**")
                .excludePathPatterns("/api/wuliu/**")
                .excludePathPatterns("/api/mcsIMei/getSignature")
                .excludePathPatterns("/api/iqiyi/**")
                .excludePathPatterns("/api/sub/checkOrderUser")
                .excludePathPatterns("/api/sub/addRemind")
                .excludePathPatterns("/inApi/**")
                .excludePathPatterns("/electronicTicket/**")
                .excludePathPatterns("/api/minFile/upload/v1")
                .excludePathPatterns("/api/sub/getMyOrderNew")
                .excludePathPatterns("/api/wcf/getOrderBaseInfo")
                .excludePathPatterns("/api/sub/getSubIdAndRecoverId")
                .excludePathPatterns("/api/sub/existsProcessingOrderInfo")
                .excludePathPatterns("/api/sub/getAfterSaleQuantity")
                .excludePathPatterns("/api/salary/filterStoreOrder/**")
                .excludePathPatterns("/api/oaorder/evaluate/queryEvaluateListV2")
                .excludePathPatterns("/api/sys/sysConfig/getDeployInfo")
                .excludePathPatterns("/api/salary/calculate")
                .excludePathPatterns("/api/salary/app/calculate")
                .excludePathPatterns("/api/salary/task/auto-calculate")
                .excludePathPatterns("/api/salary/task/taskCalculateSendMsg")
                .excludePathPatterns("/api/salary/task/calculate")
                .excludePathPatterns("/api/salary/updateAreaIds")
                .excludePathPatterns("/api/diy_order/export/v1")
                .excludePathPatterns("/api/sub/getTaxSubInfo")
                .excludePathPatterns("/api/sub/listTaxValidBasket")
                .excludePathPatterns("/api/sub/taxValidAnswer")
                .excludePathPatterns("/api/sub/getHQLargeScreenOrderCount")
                //获取重复数据接口
                .excludePathPatterns("/api/operator/account/getRepeatAccount")
                .excludePathPatterns("/api/oaOrder/recommend/get/v1")
                .excludePathPatterns("/api/product/label/initialize")
                .excludePathPatterns("/api/jiuxun/care/receive/**")
                .excludePathPatterns("/api/jiuxun/care/sync-yading/v1")
                .excludePathPatterns("/api/jiuxun/care/register/cron/task/**")
                .excludePathPatterns("/api/jiuxun/care/reload/cron/task/**")
                .excludePathPatterns("/api/jiuxun/care/add/yading-service/v1")
                .excludePathPatterns("/api/debt-sub/message-push/v1")
                .excludePathPatterns("/api/apollo/**")
                .excludePathPatterns("/api/debt-sub/message-push/v1")
                //抖音团购核销接口
                .excludePathPatterns("/api/douyin/certificate/**")
                //团购核销接口
                .excludePathPatterns("/api/tuangou/certificate/**")
                //快手团购核销回调
                .excludePathPatterns("/api/kuaishou/v1")
                //第三方订单接口
                .excludePathPatterns("/api/thirdPlatform/order/**")
                //定时更新运营商业务提成规则状态
                .excludePathPatterns("/api/operator/commission/handleStatus/v1")
                .excludePathPatterns("/api/salary/updateOrderKind")
                // 会员信息查询接口[对外提供的查询接口]
                .excludePathPatterns("/external/queryUser/V1")
                //汇机保回调接口
                .excludePathPatterns("/api/HuiJiController/receive/huijb/v1")
                .excludePathPatterns("/api/HuiJiController/deleteHuiJiBao/v1")
                .excludePathPatterns("/api/operator/activityConfiguration/getActivityConfigurationByChannelIdAndPpId")
                .excludePathPatterns("/api/recommendedMainConfig/closeService/v1")
                .excludePathPatterns("/api/recommendedMainConfig/clearPriceCloseService/v1")
                .excludePathPatterns("/admin/api/meituan/member/createMeiTuanMember/v1")
                .excludePathPatterns("/admin/api/meituan/member/selectMeiTuanMember/v1")
                .excludePathPatterns("/admin/api/stock/e02-xxl")
                .excludePathPatterns("/api/salary/updateOrderKind")
                // 投诉保存
                .excludePathPatterns("/inApi/touSu/highOpinion")
                //网站套餐查询
                .excludePathPatterns("/api/recommendedMainConfig/recommendedSelectNoToken/v1")
                //淘宝获取token信息
                .excludePathPatterns("/api/third/taobao/**")
                .excludePathPatterns("/admin/api/order/getThirdOrderInfo")
                .excludePathPatterns("/admin/api/meituan/member/memberCancellation/v1")

                .excludePathPatterns("/doudian/client/createDouYinMember/v1")
                .excludePathPatterns("/doudian/client/cancelDouYinMember/v1")
                .excludePathPatterns("/doudian/client/updateDouYinMember/v1")
                //同步淘宝抖音门店营业信息
                .excludePathPatterns("/admin/api/store/synchronizationBusinessHours/v1")
                .excludePathPatterns("/api/nationalSubsidyOrder/v1/isNationalSubsidy")
                //同步淘宝抖音门店营业信息
                .excludePathPatterns("/admin/api/store/synchronizationBusinessHours/v1")
                .excludePathPatterns("/admin/api/productConfig/listProductConfigs/v1")
                .excludePathPatterns("/InApi/webOrder/getPointsBySubId/v1")
                .excludePathPatterns("/api/nationalSubsidyOrder/v1/gb-attachment-download/**")
                .excludePathPatterns("/api/operator/config/selectTripartitePaymentId")
                .excludePathPatterns("/admin/api/meituan/nationalSupplementUpSn/v1")
                .excludePathPatterns("/admin/api/meituan/invoiceCall/v1")
                .excludePathPatterns(anyTokenList)
        ;

        registry.addInterceptor(signInterceptor).addPathPatterns("/open/**");
        //日志中增加调用链id
        registry.addInterceptor(new TraceIdInterceptor()).addPathPatterns("/**");
        if (!anyTokenList.isEmpty()){
            registry.addInterceptor(remoteAnyTokenInterceptor).addPathPatterns(anyTokenList);
        }
    }

    @Bean
    public RemoteAnyTokenInterceptor remoteAnyTokenInterceptor() {
        Function<HttpServletRequest, Object> oaFun = request -> SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId();
        Function<HttpServletRequest, Object> webFun = request -> {
            String webToken = TokenUtil.getWebToken(request);
            if(webToken == null) {
                return null;
            }
            R<UserInfoAuthCloudVO> authR = SpringUtil.getBean(AuthenticationCloud.class).authenticationV2(webToken);
            if(authR.isSuccess()){
                return authR.getData();
            }else{
                log.warn("调用授权接口返回异常: {}", authR.getUserMsg());
            }
            return null;
        };
        return new RemoteAnyTokenInterceptor(Arrays.asList(oaFun, webFun));
    }
}
