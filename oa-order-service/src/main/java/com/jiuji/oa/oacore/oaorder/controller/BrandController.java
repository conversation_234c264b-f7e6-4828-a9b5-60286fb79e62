package com.jiuji.oa.oacore.oaorder.controller;


import com.jiuji.oa.oacore.oaorder.service.BrandService;
import com.jiuji.oa.oacore.oaorder.vo.res.BrandRes;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-06
 */
@Api(tags = "品牌：品牌接口")
@RestController
@RequestMapping("/api/oaorder/brand")
public class BrandController {

    @Autowired
    private BrandService brandService;

    @ApiOperation(value = "通过分类id获取品牌列表接口", httpMethod = "POST")
    @PostMapping("/getBrandsByCid/v1")
    public R<List<BrandRes>> getBrandsByCid(@RequestBody List<Integer> cids){
        return R.success(brandService.getBrandsByCid(cids));
    }
    @ApiOperation(value = "通过分类id获取品牌列表接口", httpMethod = "POST")
    @PostMapping("/getCidAndBrandsMap/v1")
    public R<Map<Integer,List<BrandRes>>> getCidAndBrandsMap(@RequestBody List<Integer> cids){
        return R.success(brandService.getCidAndBrandsMap(cids));
    }

}

