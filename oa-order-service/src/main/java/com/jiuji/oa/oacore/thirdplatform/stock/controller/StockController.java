package com.jiuji.oa.oacore.thirdplatform.stock.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.jiuji.oa.oacore.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.resubmitAspect.Resubmit;
import com.jiuji.oa.oacore.common.util.SaasManagerUtils;
import com.jiuji.oa.oacore.common.util.SpringContextUtil;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.BzOrderResult;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.DecryptBodyBO;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.E01RequestBodyBO;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.E02CallbackBodyBO;
import com.jiuji.oa.oacore.thirdplatform.baozun.common.enums.BzResultCode;
import com.jiuji.oa.oacore.thirdplatform.baozun.service.BzTenantClientService;
import com.jiuji.oa.oacore.thirdplatform.common.ThirdPlatformCommonConst;
import com.jiuji.oa.oacore.thirdplatform.common.enums.CommodityModelEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.LogTypeEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.PlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.doudian.service.DefaultDouDianService;
import com.jiuji.oa.oacore.thirdplatform.oplog.po.MeituanJdWorkLog;
import com.jiuji.oa.oacore.thirdplatform.oplog.service.MeituanJdWorkLogService;
import com.jiuji.oa.oacore.thirdplatform.stock.bo.StockSyncReq;
import com.jiuji.oa.oacore.thirdplatform.stock.enums.IncrementTypeEnum;
import com.jiuji.oa.oacore.thirdplatform.stock.service.StockService;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.JdTenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.JdTenantService;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.TraceIdUtil;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.LocalDateTime;
import org.joda.time.Seconds;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * 库存管理Controller
 *
 * <AUTHOR>
 * @date 2021-5-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/admin/api/stock")
@Slf4j
@Api(value = "stock", tags = "库存同步管理")
public class StockController {
    @Autowired
    private StockService stockService;
    @Autowired
    private RestTemplate restTemplate;
    @Resource
    private MeituanJdWorkLogService meituanJdWorkLogService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private DefaultDouDianService defaultDouDianService;
    @Resource
    private JdTenantService jdTenantService;
    private final static int ERROR_CODE=49999;

    @Autowired
    private BzTenantClientService bzTenantClientService;

    @Resource
    private RabbitTemplate rabbitTemplate;
    /**
     * 库存同步接口
     * 防重复提交处理,逻辑为租户为九机 并且 platCode是MT 的库存同步接口进行锁定
     * @param platCode
     * @return
     */
    @PostMapping("/syncToMeituan")
    @Resubmit(delay = 3600, strategy = {Resubmit.JUDGE_STRATEGY}, parameter = {"#platCode=='{\"platCode\":\"MT\"}'","#platCode"})
    public R syncToMtAndJd(@RequestBody String platCode) {
        String code = JSON.parseObject(platCode).getString("platCode");
        if (ThirdPlatformCommonConst.THIRD_PLAT_JD.equalsIgnoreCase(code)) {
            //评估京东到家库存同步涉及面太广，暂时先用C#的接口。
            String url = SaasManagerUtils.getMoaUrl() + "/jdapi/InitBatchUpdateStock";
            //添加日志操作
            MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.STOCK_SYN.getMessage(), "库存同步操作......同步连接："+url,
                    abstractCurrentRequestComponent.getCurrentStaffId().getUserName(), LogTypeEnum.STOCK_SYN.getCode(), PlatfromEnum.JD.getCode(),"京东到家");
            //开启事务 切换写库
            MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> meituanJdWorkLogService.save(structure)).commit();
            return restTemplate.getForObject(url, R.class);
        }
        return R.success(stockService.syncToMeiTuan());
    }


    /**
     * 库存同步接口
     * 防重复提交处理,逻辑为租户为九机 并且 platCode是MT 的库存同步接口进行锁定
     * @return
     */
    @ApiOperation("美团&京东库存同步接口")
    @PostMapping("/syncToMeituanV1")
    @RepeatSubmitCheck(expression = "#{classFullName}:#{methodSignName}:#{req.platCode}:#{req.tenantCode}", message = "正在同步中, 请耐心等待")
    public R syncToMtAndJdV1(@RequestBody StockSyncReq req) {
        String traceId = MDC.get(TraceIdUtil.TRACE_ID_KEY);
        String code = req.getPlatCode();
        //判断不是九机
        boolean isNotJiuJi = !XtenantEnum.isJiujiXtenant(XtenantEnum.getXtenant());
        StringBuilder urlOa = new StringBuilder("/jdapi/InitBatchUpdateStock");
        if(isNotJiuJi && ThirdPlatformCommonConst.THIRD_PLAT_JD.equals(code)){
            String tenantCode = req.getTenantCode();
            List<JdTenant> list = jdTenantService.lambdaQuery().in(JdTenant::getTenantCode, tenantCode).list();
            if(CollectionUtils.isNotEmpty(list)){
                JdTenant jdTenant = list.get(0);
                Integer commodityModel = Optional.ofNullable(jdTenant.getCommodityModel()).orElse(CommodityModelEnum.TWO.getCode());
                if(commodityModel.equals(CommodityModelEnum.TWO.getCode())){
                    R<Object> error = R.error("当前商品匹配模式为京东SKU编码，不支持同步库存。如需调整请到商户配置界面修改。");
                    error.setCode(ERROR_CODE);
                    return error;
                } else {
                    urlOa.append("?tenant_code=").append(tenantCode);
                }
            }
        }
        if (ThirdPlatformCommonConst.THIRD_PLAT_JD.equalsIgnoreCase(code)) {
            //评估京东到家库存同步涉及面太广，暂时先用C#的接口。
            String url = SaasManagerUtils.getMoaUrl() + urlOa;
            //添加日志操作
            MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.STOCK_SYN.getMessage(), "库存同步操作......同步连接："+url,
                    abstractCurrentRequestComponent.getCurrentStaffId().getUserName(), LogTypeEnum.STOCK_SYN.getCode(), PlatfromEnum.JD.getCode(),"京东到家");
            //开启事务 切换写库
            MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> meituanJdWorkLogService.save(structure)).commit();
            CompletableFuture.runAsync(SpringContextUtil.runnableWithContext(() -> {
                restTemplate.getForObject(url, R.class);
            }));
            return R.success("正在同步，请耐心等待！");
        }
        CompletableFuture.runAsync(SpringContextUtil.runnableWithContext(() -> {
            stockService.syncToMeiTuanV1(Dict.create().set("platCode", req.getPlatCode()).set("tenantCode", req.getTenantCode())
                    .set("ppids", req.getPpids()).set("incrementType", CollUtil.isNotEmpty(req.getPpids()) ? IncrementTypeEnum.ONE.getCode() : IncrementTypeEnum.All.getCode()));
        }));
        return R.success("正在同步，请耐心等待！");
    }


    /**
     * 库存同步接口
     * 防重复提交处理,逻辑为租户为九机 并且 platCode是MT 的库存同步接口进行锁定

     * @return
     */
    @ApiOperation("抖音库存同步接口")
    @GetMapping("/syncToDouDian")
    @RepeatSubmitCheck(expression = "#{classFullName}:#{methodSignName}:#{tenantCode}", message = "正在同步，请耐心等待！")
    public R syncToDouDian(@RequestParam(value = "tenantCode", required = false) String tenantCode) {
        String traceId = MDC.get(TraceIdUtil.TRACE_ID_KEY);
        // 获取门店列表信息
        stockService.syncToMeiTuanV1(Dict.create().set("platCode", PlatfromEnum.DY.name())
                .set("tenantCode", tenantCode).set("incrementType", IncrementTypeEnum.All.getCode()));
        return R.success("正在同步，请耐心等待！").put(TraceIdUtil.TRACE_ID_KEY, traceId);
    }

    /**
     * 美团库存定时全量同步
     * param ppids
     *
     * @return
     */
    @ApiOperation("美团定时同步接口")
    @PostMapping("/syncToMtAndJdTiming")
    @RepeatSubmitCheck()
    public R<String> syncToMtAndJdTiming(@RequestBody(required = false) Dict param) {
        Dict syncParam = param;
        if (syncParam == null){
            syncParam = Dict.create().set("incrementType", IncrementTypeEnum.All.getCode());
        }
        log.warn("美团定时同步接口,参数: {}",JSON.toJSONString(syncParam));
        stockService.syncToMeiTuanV1(syncParam);
        return R.success(MDC.get(TraceIdUtil.TRACE_ID_KEY));
    }




    /**
     * 美团定时增量同步接口
     *
     */
    @ApiOperation("美团定时增量同步接口")
    @GetMapping("/syncToMtStock/v1")
    @RepeatSubmitCheck()
    public R<?> syncToMtStock (@RequestParam(value = "syncTime") Integer syncTime,
                               @RequestParam(value = "platCode", required = false, defaultValue = "MT") String platCode,
                               @RequestParam(value = "syncType", required = false, defaultValue = "all") String syncType
    ) {
        String uuid = MDC.get(TraceIdUtil.TRACE_ID_KEY);
        LocalDateTime start = LocalDateTime.now();
        log.warn("{}库存同步开始：{}同步开始时间:{}", platCode,uuid, start);
        stockService.syncToMtStock(platCode,syncTime, syncType);
        LocalDateTime end = LocalDateTime.now();
        Seconds seconds = Seconds.secondsBetween(start, end);
        int diff = seconds.getSeconds();
        log.warn("{}库存同步结束：{},同步开始结束:{},时间差为:{}", platCode,uuid, end,diff+"秒");
        return R.success(MDC.get(TraceIdUtil.TRACE_ID_KEY));
    }
    /**
     * 同步美团的所有门店 临时批量接口(用完删除)
     * @param area area
     * @return
     */
    @GetMapping("/syncStore")
    public Boolean syncStore(@RequestParam List<String> area) {
        return stockService.syncStore(area);
    }



    /**
     * e01采购入库 content-type: application/text
     * @param body 解密后内容
     * @param decryptBody 平台相关信息
     * @return
     */
    @PostMapping("/e01")
    @ApiOperation("e01采购入库")
    public BzOrderResult e01(@RequestBody String body, @RequestParam(required = false) DecryptBodyBO decryptBody) {
        if(StrUtil.isBlank(body)){
            return BzOrderResult.error(BzResultCode.MISSING_PARAMETERS_1006,"采购单信息内容不能为空!");
        }
        E01RequestBodyBO e01RequestBodyBO = JSON.parseObject(body, E01RequestBodyBO.class);
        if (Objects.isNull(decryptBody)) {
            decryptBody = new DecryptBodyBO();
            decryptBody.setTenantId(2);
        }
        return bzTenantClientService.generateCaigouSub(e01RequestBodyBO,decryptBody);
    }


    /**
     * e02入库反馈
     * @return
     */
    @PostMapping("e02")
    @ApiOperation("e02入库反馈")
    public R<String> e02(@RequestBody E02CallbackBodyBO vo){
//        rabbitTemplate.convertAndSend(RabbitMqConfig.BAOZUN_PURCHASE_INSTOCK, JSON.toJSONString(vo));
        bzTenantClientService.e02(vo);
        return R.success("");
    }


    /**
     * e02入库反馈失败补偿
     * @return
     */
    @GetMapping("e02-xxl")
    @ApiOperation("e02入库反馈失败补偿")
    public R<String> e02Xxl(@RequestParam Integer fkTenantId){
        bzTenantClientService.e02Xxl(fkTenantId);
        return R.success("");
    }
}
