/*
 *     Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.oacore.tousu.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.oacore.common.req.AttachmentsReq;
import com.jiuji.oa.oacore.tousu.po.Attachments;
import com.jiuji.oa.oacore.tousu.res.AttachmentsRes;

import java.util.List;

/**
 * ${comments}Service
 *
 * <AUTHOR> code generator
 * @date 2020-11-18 11:16:01
 */
public interface AttachmentsService extends IService<Attachments> {

    /**
     * 转换附件的http地址
     * @param host
     * @param e
     * @return
     */
    String concatHttpUrl(String host, Attachments e);

    /**
     *
     * @param linkId
     * @param type
     * @return
     */
    List<Attachments> getAttachmentsByLinkId(Integer linkId, Integer type);

    List<Attachments> getAttachmentsByLinkIds(List<Integer> linkIds, Integer type);

    /**
     * 根据附件id批量查询附件
     *
     * @param attachIds 附件id列表
     * @return
     */
    List<AttachmentsRes> listTousuAttachmentsByIds(List<Integer> attachIds);

    /**
     * 保存附件
     * @param files
     * @param linkId
     * @param type
     * @param userId
     * @param kind
     * @return
     */
    String saveAttachemnts(List<AttachmentsReq> files, Integer linkId, Integer type, Integer userId, Integer kind);
}
