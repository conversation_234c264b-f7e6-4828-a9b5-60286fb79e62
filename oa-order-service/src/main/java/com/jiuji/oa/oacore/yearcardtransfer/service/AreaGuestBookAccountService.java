package com.jiuji.oa.oacore.yearcardtransfer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.yearcardtransfer.entity.AreaGuestBookAccount;
import com.jiuji.oa.oacore.yearcardtransfer.entity.AreaGuestBookAccountLog;
import com.jiuji.oa.oacore.yearcardtransfer.vo.AreaGuestBookAccountQuery;
import com.jiuji.oa.oacore.yearcardtransfer.vo.AreaGuestBookAccountVO;

/**
 * <AUTHOR>
 */
public interface AreaGuestBookAccountService extends IService<AreaGuestBookAccount> {

    IPage<AreaGuestBookAccountVO> page(AreaGuestBookAccountQuery req);

    /**
     * 查询日志
     * @param configId
     * @return
     */
//    IPage<AreaGuestBookAccountLog> pageLog(Integer configId, Integer currentPage, Integer pageSize);

    AreaGuestBookAccountVO getDetail(Integer configId);


    boolean updateEnableStatus(Integer configId, Boolean enableStatus, OaUserBO oaUserBO);

    boolean addOrUpdate(AreaGuestBookAccountVO areaGuestBookAccount, OaUserBO oaUserBO);

    /**
     * 所属门店是否存在
     * @param showAreaId
     * @return
     */
    boolean checkShowAreaId(Integer showAreaId);
}
