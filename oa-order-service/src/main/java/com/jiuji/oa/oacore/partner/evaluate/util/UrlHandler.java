package com.jiuji.oa.oacore.partner.evaluate.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021/7/28
 */
@Slf4j
@Component
public class UrlHandler {

    public static final String XSERVICENAME = "?xservicename=oa-orderservice";

    private static final String DEV_PROFILE = "dev";
    private static final String LOCAL_PROFILE = "local";
    private static final String TEST_PROFILE = "jiujitest";
    private static final String SAAS_10050 = "10050";

    public static String getCurrentUrl() {
        String url = DEV_PROFILE.equals(SpringContext.getActiveProfile())
                || LOCAL_PROFILE.equals(SpringContext.getActiveProfile())
                || TEST_PROFILE.equals(SpringContext.getActiveProfile())
                || SAAS_10050.equals(SpringContext.getActiveProfile())
                ? "https://moa.dev.9ji.com/cloudapi_nc/orderservice" : "https://moa.9ji.com/cloudapi_nc/orderservice";
        log.info("当前运行环境为{} ，合作伙伴请求地址{}", SpringContext.getActiveProfile(), url);
        return url;
    }
}
