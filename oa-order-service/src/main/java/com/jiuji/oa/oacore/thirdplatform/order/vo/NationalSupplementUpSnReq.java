package com.jiuji.oa.oacore.thirdplatform.order.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class NationalSupplementUpSnReq {

    /**
     * 美团订单
     */
    @NotBlank(message = "订单ID不能为空")
    private String order_id;


    /**
     *
     * 操作类型，0-新增，1-修改，默认为0
     */
    @NotNull(message = "操作类型不能为空")
    private Integer op_type;

    /**
     * sn
     */
    @NotNull(message = "sn不能为空")
    private String sn_code;

    /**
     * imei  使用逗号拼接
     */
    @NotBlank(message = "串号不能为空")
    private String imei_code_list;

    /**
     *
     */
    @NotNull(message = "ppid不能为空")
    private Integer ppid;


    /**
     * 商品映射
     */
    private List<SpuData> spuDataList;

}
