package com.jiuji.oa.oacore.weborder.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jiuji.cloud.after.enums.ProductTypeEnum;
import com.ch999.common.util.utils.XtenantJudgeUtil;
import com.jiuji.cloud.stock.service.OaStockCloud;
import com.jiuji.cloud.stock.vo.response.PriceProtectionByMkcIdsRes;
import com.jiuji.oa.oacore.assistantOrderInfo.enums.SmallProStatsEnum;
import com.jiuji.oa.oacore.assistantOrderInfo.enums.SmallProStatsEnum;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.config.properties.JiujiSystemProperties;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.enums.EvaluateTypeEnum;
import com.jiuji.oa.oacore.common.enums.OaMesTypeEnum;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.common.res.PageRes;
import com.jiuji.oa.oacore.common.util.AreaUtil;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.common.util.SpringContextUtil;
import com.jiuji.oa.oacore.goldseed.service.AreaInfoService;
import com.jiuji.oa.oacore.mapstruct.CommonStructMapper;
import com.jiuji.oa.oacore.oaorder.bo.ProductIsMobileInfoBO;
import com.jiuji.oa.oacore.oaorder.cloud.service.RentCloud;
import com.jiuji.oa.oacore.oaorder.cloud.service.vo.RentOrderVO;
import com.jiuji.oa.oacore.oaorder.dao.OrderDetailMapper;
import com.jiuji.oa.oacore.oaorder.dao.SubMapper;
import com.jiuji.oa.oacore.oaorder.document.MkcLogsNewDocument;
import com.jiuji.oa.oacore.oaorder.document.SubLogsNewDocument;
import com.jiuji.oa.oacore.oaorder.enums.BasketTypeEnum;
import com.jiuji.oa.oacore.oaorder.enums.EvaluateJobEnum;
import com.jiuji.oa.oacore.oaorder.enums.SubCheckEnum;
import com.jiuji.oa.oacore.oaorder.po.Basket;
import com.jiuji.oa.oacore.oaorder.po.ProductMkc;
import com.jiuji.oa.oacore.oaorder.po.RecoverMarketinfo;
import com.jiuji.oa.oacore.oaorder.po.Sub;
import com.jiuji.oa.oacore.oaorder.res.TaxPiAoBO;
import com.jiuji.oa.oacore.oaorder.service.*;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.oacore.recover.res.SalfGoodsNewMpicsRes;
import com.jiuji.oa.oacore.recover.service.UsedGoodsService;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.oacore.weborder.bo.*;
import com.jiuji.oa.oacore.weborder.chainOfResponsibility.AppointmentFormOrderHandler;
import com.jiuji.oa.oacore.weborder.chainOfResponsibility.GoodQualityOrderHandler;
import com.jiuji.oa.oacore.weborder.chainOfResponsibility.NewMachineOrderHandler;
import com.jiuji.oa.oacore.weborder.chainOfResponsibility.RecyclingOrderHandler;
import com.jiuji.oa.oacore.weborder.dao.WebOrderMapper;
import com.jiuji.oa.oacore.weborder.enums.AssistStatusEnum;
import com.jiuji.oa.oacore.weborder.enums.InvoiceType;
import com.jiuji.oa.oacore.weborder.enums.OrderTypeTag;
import com.jiuji.oa.oacore.weborder.enums.PayTypeEnum;
import com.jiuji.oa.oacore.weborder.req.*;
import com.jiuji.oa.oacore.weborder.res.*;
import com.jiuji.oa.oacore.weborder.service.WebOrderService;
import com.jiuji.oa.oacore.weborder.vo.*;
import com.jiuji.oa.oacore.weborder.vo.ImOrderVO;
import com.jiuji.oa.oacore.weborder.vo.req.CheckStockInfoReqVO;
import com.jiuji.oa.oacore.weborder.vo.req.MyClientReq;
import com.jiuji.oa.oacore.weborder.vo.req.OaStockInfoReqVO;
import com.jiuji.oa.oacore.weborder.vo.req.StockInfoReqVO;
import com.jiuji.oa.oacore.weborder.vo.req.WhetherToBuyReq;
import com.jiuji.oa.oacore.weborder.vo.res.*;
import com.jiuji.oa.oacore.weborder.vo.res.LogisticsTransferInfoVO;
import com.jiuji.oa.oacore.weborder.wrapper.WebOrderWrapper;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.LambdaCaseWhen;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.IntConstant;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import com.jiuji.tc.utils.xtenant.Namespaces;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description
 * @since 2020/4/9
 */
@Service
@Slf4j
public class WebOrderServiceImpl implements WebOrderService {
    //异常订单，在该key下，通过hashSet的方式进行存储
    private static final String exceptionSubKey = "ExceptionSubRedisKey";
    private static final String exceptionLiangpinSubKey = "ExceptionLpRecoverSubRedisKey";
    private static final int EXCLUSIVE_CUSTOMER_SERVICE_COUNT = 4;

    /**
     * 商品id 暂时固定，之后有需求可提取
     */
    private static final int COMMODITY_ID = 47113;

    private static final List<EvaluateTypeEnum> xinjiTypeEnums = Arrays.asList(
            EvaluateTypeEnum.Online,
            EvaluateTypeEnum.Offline);
    private static final Pattern numberPattern = Pattern.compile("[\\d]");
    @Value("${jiuji.weborder.enableHis:false}")
    public Boolean enableHis = false;
    @Resource
    private WebOrderMapper webOrderMapper;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    protected StringRedisTemplate stringRedisTemplate;
    @Resource
    private OrderDetailMapper orderDetailMapper;
    @Autowired
    private JiujiSystemProperties jiujiSystemProperties;
    @Autowired
    private UserInfoClient userInfoClient;
    @Resource
    private SubMapper subMapper;
    @Autowired
    private AreaInfoService areaInfoService;

    @Qualifier("asyncServiceExecutor")
    @Autowired
    private Executor executor;
    @Autowired
    private ProductMkcService productMkcService;
    @Autowired
    private MkcLogsNewService mkcLogsNewService;
    @Autowired
    private SubLogsNewLogService subLogsNewLogService;
    @Autowired
    private BasketService basketService;
    @Autowired
    private SubService subService;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private AbstractCurrentRequestComponent userInfoService;
    @Resource
    private RecoverMarketinfoService recoverMarketinfoService;
    @Resource
    private RentCloud rentCloud;
    @CreateCache(name = "WebOrderService.cacheMap.count:", expire = 2, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.REMOTE)
    private Cache<String, Integer> countCache;
    private static final int GOODPRODUCT=2;
    @Resource
    private AppointmentFormOrderHandler appointmentFormOrderHandler;
    @Resource
    private GoodQualityOrderHandler goodQualityOrderHandler;
    @Resource
    private NewMachineOrderHandler newMachineOrderHandler;
    @Resource
    private RecyclingOrderHandler recyclingOrderHandler;
    @Resource
    private SmsService smsService;
    @Resource
    private ProductinfoService productinfoService;
    @Resource
    private OaStockCloud stockCloud;
    @Resource
    private CommonStructMapper commonStructMapper;

    @Override
    public R<List<PendingPaymentVO>> selectOrderPendingPaymentV2(PendingPaymentReq req) {
        //责任连构建  新机>良品>回收>预约维修
        newMachineOrderHandler.setNext(goodQualityOrderHandler)
                .setNext(recyclingOrderHandler)
                .setNext(appointmentFormOrderHandler);
        List<PendingPaymentVO> list = new ArrayList<>();
        return newMachineOrderHandler.handleRequest(req,list);
    }

    @Override
    public R<Boolean> judgeGoodAccessories(JudgeGoodAccessoriesBO judgeGoodAccessoriesBO) {
        Integer subId = judgeGoodAccessoriesBO.getSubId();
        Integer type = judgeGoodAccessoriesBO.getType();
        List<Integer> list;
        if(GOODPRODUCT==type){
            //判断良品是否已经付款
            RecoverMarketinfo recoverMarketinfo = Optional.ofNullable(recoverMarketinfoService.getById(subId)).orElse(new RecoverMarketinfo());
            Double yifuM = Optional.ofNullable(recoverMarketinfo.getYifuM()).orElse(0.00);
            if(yifuM>0.00){
                return R.success(Boolean.FALSE);
            }
            list = webOrderMapper.selectGoodAccessoriesGoodProduct(subId);
        } else {
            //判断新机是否已经付款
            Sub sub = Optional.ofNullable(subService.getById(subId)).orElse(new Sub());
            Double yifuM = Optional.ofNullable(sub.getYifuM()).orElse(0.00);
            if(yifuM>0.00){
                return R.success(Boolean.FALSE);
            }
            list = webOrderMapper.selectGoodAccessories(subId);
        }
        if(CollectionUtils.isEmpty(list)){
            return R.success(Boolean.FALSE);
        }
        return R.success(Boolean.TRUE);
    }

    @Override
    public R<ExchangeCountVO> getExchangeCount(Integer memberId) {
        List<ExchangeCountBO> exchangeCountBOS = webOrderMapper.getExchangeCount(memberId);

        //列表转map
        Map<Integer,Integer> exchangeCountMap = exchangeCountBOS.stream().collect(Collectors.toMap(ExchangeCountBO::getType, ExchangeCountBO::getCount, (key1, key2) -> key2));

        //通过类型作为key值获取各类型商品退换货次数
        ExchangeCountVO exchangeCountVO = new ExchangeCountVO();
        exchangeCountVO.setXjCount(exchangeCountMap.get(ExchangeCountBO.XJANDYPCOUNTTYPE) - exchangeCountMap.get(ExchangeCountBO.YPCOUNTTYPE));
        exchangeCountVO.setYpCount(exchangeCountMap.get(ExchangeCountBO.YPCOUNTTYPE));
        exchangeCountVO.setLpCount(exchangeCountMap.get(ExchangeCountBO.LPCOUNTTYPE));
        return R.success(exchangeCountVO);
    }

    @Override
    public R<List<OrderStateRes>> getOrderStatusByList(List<Integer> orderIdList) {
        List<OrderStateRes> orderStateRes = CommonUtils.bigDataInQuery(orderIdList,webOrderMapper::getOrderStatusByList);
        return R.success(orderStateRes);
    }

    @Override
    public R<Integer> getSubIdByBasketIdV1(Integer basketId) {
        Integer subId = webOrderMapper.getSubIdByBasketId(basketId);
        if (Objects.isNull(subId)) {
            subId = IntConstant.ZERO;
        }
        return R.success(subId);
    }

    /**
     * 查询库存信息
     *
     * @param req
     * @return
     */
    @Override
    public R<List<StockInfoResVO>> getOaStockTimelyByPpids(StockInfoReqVO req) {
        if (CollectionUtils.isEmpty(req.getPpids())) {
            return R.success(new ArrayList<>());
        }
        List<ProductIsMobileInfoBO> productInfoList = productinfoService.getProductIsmobileByPpids(req.getPpids());
        List<Integer> mppids = productInfoList.stream().filter(v -> Objects.equals(1, v.getIsmobile())).map(ProductIsMobileInfoBO::getPpid).collect(Collectors.toList());
        List<Integer> pjPpids = productInfoList.stream().filter(v -> !Objects.equals(1, v.getIsmobile())).map(ProductIsMobileInfoBO::getPpid).collect(Collectors.toList());
        List<StockCountVO> stockCountList = new ArrayList<>();
        stockCountList.addAll(productinfoService.getProductMkcCountByPpids(mppids,req));
        stockCountList.addAll(productinfoService.getProductKcCountByPpids(pjPpids,req));
        Map<Integer, List<StockCountVO>> stockCountMap= stockCountList.stream().collect(Collectors.groupingBy(StockCountVO::getPpriceid));
        List<StockInfoResVO> stockInfoResList = req.getPpids().stream().map(v -> LambdaBuild.create(StockInfoResVO.class).set(StockInfoResVO::setPpid, v)
                .set(StockInfoResVO::setCounts, stockCountMap.getOrDefault(v, Collections.emptyList()))
                .set(StockInfoResVO::setType, "1").build()).collect(Collectors.toList());
        log.info("查询oa实时库存req={},result={}", JSONUtil.toJsonStr(req), JSONUtil.toJsonStr(stockInfoResList));
        return R.success(stockInfoResList);
    }

    @Override
    public R<List<WebStockInfoResVO>> getStockByPpids(WebStockInfoReqVO req) {
        if (CollectionUtils.isEmpty(req.getPpids())) {
            return R.success(new ArrayList<>());
        }
        AreaInfo areainfo = areaInfoService.getAreaInfoById(Optional.ofNullable(req.getAreaId()).orElse(0));
        req.setXtenant(Optional.ofNullable(areainfo).map(AreaInfo::getXtenant).orElse(0));

        List<ProductIsMobileInfoBO> productInfoList = productinfoService.getProductIsmobileByPpids(req.getPpids());
        List<Integer> mppids = productInfoList.stream().filter(v -> Objects.equals(1, v.getIsmobile())).map(ProductIsMobileInfoBO::getPpid).collect(Collectors.toList());
        List<Integer> pjPpids = productInfoList.stream().filter(v -> !Objects.equals(1, v.getIsmobile())).map(ProductIsMobileInfoBO::getPpid).collect(Collectors.toList());
        req.setPpids(mppids);
        List<WebStockInfoResVO> stockList = new ArrayList<>(productinfoService.getProductMkcStockByPpids(req));
        req.setPpids(pjPpids);
        stockList.addAll(productinfoService.getProductKcStockByPpids(req));
        return R.success(stockList);
    }

    @Override
    public R<List<DiaoboStockInfoResVO>> getDiaoboByPpidAndToArea(DiaoboStockInfoReqVO req) {
        return R.success(productinfoService.getDiaoboByPpidAndToArea(req));
    }

    /**
     * 查询商品成本
     *
     * @param req
     * @return
     */
    @Override
    public R<List<WebStockPriceResVO>> getInPriceByPpid(WebStockPriceReqVO req) {
        List<WebStockPriceResVO> list = new ArrayList<>();
        AreaInfo areainfo = areaInfoService.getAreaInfoById(Optional.ofNullable(req.getAreaId()).orElse(0));
        req.setAuthorizeId(Optional.ofNullable(areainfo).map(AreaInfo::getAuthorizeId).orElse(null));
        req.setXtenant(Optional.ofNullable(areainfo).map(AreaInfo::getXtenant).orElse(0));
        if (Objects.equals(1, req.getIsMoble())) {
            req.setType(1);
            list.addAll(productinfoService.getInPriceByPpid(req));
            req.setType(2);
            list.addAll(productinfoService.getInPriceByPpid(req));
            list = list.stream().distinct().collect(Collectors.toList());
            if(CollUtil.isNotEmpty(list)){
                List<Integer> mkcIds = list.stream().map(WebStockPriceResVO::getMkcId).collect(Collectors.toList());
                R<List<PriceProtectionByMkcIdsRes>> listR = stockCloud.queryPriceByMkcIds(mkcIds);
                log.warn("获取mkcId价格返回结果：{}传入参数：{}",listR,mkcIds);
                if (listR != null && listR.isSuccess() && listR.getData() != null && CollUtil.isNotEmpty(listR.getData())) {
                    // 创建mkcId到cashbackAmount的映射
                    Map<Integer, BigDecimal> mkcIdToCashbackMap = listR.getData().stream()
                            .filter(v -> ObjectUtil.isNotNull(v.getMkcId()))
                            .collect(Collectors.toMap(
                                PriceProtectionByMkcIdsRes::getMkcId,
                                    v -> Optional.ofNullable(v.getCashbackAmount()).orElse(BigDecimal.ZERO),
                                (v1, v2) -> v2
                            ));
                    if(MapUtil.isNotEmpty(mkcIdToCashbackMap)){
                        // 遍历list，根据mkcId设置soPrice
                        list.forEach(item -> {
                            item.setSoPrice(mkcIdToCashbackMap.getOrDefault(item.getMkcId(),BigDecimal.ZERO));
                        });
                    }
                }
            }


        } else {
            req.setType(3);
            list.addAll(productinfoService.getInPriceByPpid(req));
            //查询商品成本
            if (CollUtil.isEmpty(list)) {
                list.addAll(productinfoService.getProductInPriceByPpid(req));
            }
        }
        return R.success(list);
    }

    /**
     * 校验商品库存信息
     *
     * @param req
     * @return
     */
    @Override
    public R<List<CheckStockInfoResVO>> checkProductStock(CheckStockInfoReqVO req) {
        List<CheckStockInfoResVO> resultList = new ArrayList<>();
        if (CollUtil.isEmpty(req.getPpids()) || CollUtil.isEmpty(req.getAreaIds())) {
            return R.error("ppids或areaIds不能为空");
        }
        List<ProductIsMobileInfoBO> productInfoList = productinfoService.getProductIsmobileByPpids(req.getPpids());
        List<Integer> mppids = productInfoList.stream().filter(v -> Objects.equals(1, v.getIsmobile())).map(ProductIsMobileInfoBO::getPpid).collect(Collectors.toList());
        List<Integer> pjPpids = productInfoList.stream().filter(v -> !Objects.equals(1, v.getIsmobile())).map(ProductIsMobileInfoBO::getPpid).collect(Collectors.toList());
        String areaIds = CollUtil.join(req.getAreaIds(), ",");
        if (CollUtil.isNotEmpty(mppids)) {
            String ppids = CollUtil.join(mppids, ",");
            resultList.addAll(productinfoService.getMkcStockByPpidsAndAreaIds(ppids, areaIds));
        }
        if (CollUtil.isNotEmpty(pjPpids)) {
            String ppids = CollUtil.join(pjPpids, ",");
            resultList.addAll(productinfoService.getKcStockByPpidsAndAreaIds(ppids, areaIds));
        }
        return R.success(resultList);
    }

    /**
     * 查询赠品库存
     *
     * @param req
     * @return
     */
    @Override
    public R<List<GiftStockResVO>> getGiftStockByAreaIds(GiftStockReqVO req) {
        if(CollUtil.isEmpty(req.getAreaIds())) {
            return R.success(Collections.emptyList());
        }
        return R.success(productinfoService.getGiftStockByAreaIds(req));
    }

    @Override
    public R<List<StockInfoResVO>> getOaStockTimely(OaStockInfoReqVO req) {
        List<AreaInfo> areaInfoList = areaInfoService.getAreaInfoList();
        if (CollUtil.isEmpty(areaInfoList)) {
            log.warn("查询区域库存信息接口，门店信息为空req={}", req);
            RRExceptionHandler.logError("查询区域库存信息接口，门店信息为空", req, null, smsService::sendOaMsgTo9JiMan);
            return R.success(new ArrayList<>());
        }
        //过滤区域门店信息
        List<Integer> areaIds = areaInfoList.parallelStream()
                .filter(AreaInfo::getIsPass)
                .filter(v -> Objects.equals(req.getXtenantId(), v.getXtenant()))
                .filter(v -> AreaUtil.checkAreaInclude(v.getCityId(), req.getCityIds()))
                .map(AreaInfo::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(areaIds)) {
            return R.success(new ArrayList<>());
        }
        StockInfoReqVO stockInfoReq = new StockInfoReqVO();
        stockInfoReq.setPpids(req.getPpids());
        stockInfoReq.setAreaIds(areaIds);
        return getOaStockTimelyByPpids(stockInfoReq);
    }

    /**
     * 分页count 第一页直接查询,第二页开始直接从缓存获取,减少资源损耗
     *
     * @param isHis
     * @param searchId
     * @param paramStatus
     * @return
     */
    public Integer count(WebOrderQueryReq req, Boolean isHis, Integer searchId, List<Integer> paramStatus) {
        Integer userId = req.getUserId();
        Integer xtenant = req.getXtenant();
        String tag = req.getTagType();
        String keyWord = req.getKeyWord();
        Boolean distribution = req.getDistribution();
        int page = req.getCurrent();
        Integer orderLinkFlag = req.getOrderLinkFlag();
        Integer queryHistoryOrder = req.getQueryHistoryOrder();
        Integer hideOrder = req.getHideOrder();
        List<Integer> status = DecideUtil.iif(CollUtil.isEmpty(paramStatus), null,paramStatus);
        String countCacheKey = MessageFormat.format("{0}/{1}/{2}/{3}/{4}/{5}/{6}/{7}/{8}", userId, xtenant
                , tag, keyWord, isHis, searchId, distribution
                , DecideUtil.iif(CollUtil.isEmpty(status),()-> null,()-> status.stream().map(String::valueOf)
                        .collect(Collectors.joining(",", "[", "]")))
                , queryHistoryOrder,hideOrder
        );
        countCacheKey += "/"+req.getDelCollect();
        if (!isHis && page < 2) {
            //主表第一页,每次都删除缓存,使重新加载
            countCache.remove(countCacheKey);
        }
        return countCache.computeIfAbsent(countCacheKey, key -> {
            if (isHis) {
                return webOrderMapper.countNormalHis(userId, xtenant, tag, keyWord, isHis, searchId, distribution, status,orderLinkFlag, queryHistoryOrder,hideOrder,req);
            } else {
                return webOrderMapper.countNormal(userId, xtenant, tag, keyWord, isHis, searchId, distribution, status,orderLinkFlag,queryHistoryOrder,hideOrder,req);
            }
        });
    }

    /**
     * 良品无理由退款查询
     * @param userId
     * @return
     */
    @Override
    public Integer queryRetreatCount(Integer userId) {
        log.warn("良品无理由退款次数查询传入参数：{}",userId);
        Integer count = Optional.ofNullable(webOrderMapper.selectRetreatCount(userId)).orElse(Integer.MAX_VALUE);
        log.warn("良品无理由退款次数查询传结果：{}",count);
        return count ;
    }

    @Override
    @Deprecated
    public PageRes<XinjiSubVO> pageNormal(WebOrderQueryReq req) {
        PageRes<XinjiSubVO> res = new PageRes<>(req.getCurrent(), req.getSize());
        Integer startRows = (req.getCurrent() - 1) * req.getSize();
        List<Integer> status = CommonUtil.covertIdStr(req.getStatus());
        Boolean displayInvoice = Optional.ofNullable(req.getDisplayInvoice()).orElse(Boolean.FALSE);
        log.info("start");
        Integer count = webOrderMapper.countNormal(req.getUserId(), req.getXtenant(), req.getTagType(),
                req.getKeyWord(), false, getSearchId(req.getKeyWord()), req.getDistribution(), CollectionUtils.isEmpty(status) ? null : status,req.getOrderLinkFlag(), req.getQueryHistoryOrder(), req.getHideOrder(),req);
        log.info("count:{}", count);
        Integer countHis = 0;
        if (req.getXtenant() < 1000) {
            countHis = webOrderMapper.countNormalHis(req.getUserId(), req.getXtenant(), req.getTagType(),
                    req.getKeyWord(), true, getSearchId(req.getKeyWord()), req.getDistribution(), CollectionUtils.isEmpty(status) ? null : status,req.getOrderLinkFlag(), req.getQueryHistoryOrder(), req.getHideOrder(), req);
        }
        log.info("countHis:{}", countHis);
        Integer total = count + countHis;
        res.setTotal(total);

        if (startRows > total) {
            res.setRecords(new ArrayList<>());
            return res;
        }

        List<XinjiSubBO> xinJiList = new ArrayList<>();
        List<Integer> allSubId = new ArrayList<>();
        List<XinjiProductBO> allXinJiProduct = new ArrayList<>();

        if (startRows < count) {
            List<XinjiSubBO> list = webOrderMapper.listNormal(req.getUserId(),req.getXtenant(),
                    req.getTagType(), req.getKeyWord(), getSearchId(req.getKeyWord()), false,
                    startRows, req.getSize(), req.getDistribution(), CollectionUtils.isEmpty(status) ? null : status, req.getOrderLinkFlag(), req.getQueryHistoryOrder(),req.getHideOrder(),req);
            log.info("list:{}", list.size());
            if (CollectionUtils.isNotEmpty(list)) {
                xinJiList.addAll(list);
                List<Integer> listSubId = list.stream()
                        .map(XinjiSubBO::getSubId).collect(Collectors.toList());
                allSubId.addAll(listSubId);
                List<XinjiProductBO> productBOList = webOrderMapper.listProductNormal(listSubId);
                productBOList = productBOList.stream().distinct().collect(Collectors.toList());
                allXinJiProduct.addAll(productBOList);
            }
        }

        if ((startRows + req.getSize() > count) && req.getXtenant() < 1000) {
            Integer startRowsHis = Integer.max(startRows - count, 0);
            Integer size = req.getSize() - xinJiList.size();

            List<XinjiSubBO> listHis = webOrderMapper.listNormalHis(req.getUserId(), req.getXtenant(),
                    req.getTagType(), req.getKeyWord(), getSearchId(req.getKeyWord()), true,
                    startRowsHis, size, req.getDistribution(), DecideUtil.iif(CollectionUtils.isEmpty(status) , null , status),
                    req.getOrderLinkFlag(),req.getQueryHistoryOrder(),req.getHideOrder(),req);
            log.info("listHis:{}", listHis.size());
            if (CollectionUtils.isNotEmpty(listHis)) {
                xinJiList.addAll(listHis);
                List<Integer> listSubId = listHis.stream()
                        .map(XinjiSubBO::getSubId).collect(Collectors.toList());
                allSubId.addAll(listSubId);
                List<XinjiProductBO> productBOList = webOrderMapper.listProductNormalHis(listSubId);
                allXinJiProduct.removeAll(productBOList);
                allXinJiProduct.addAll(productBOList);
            }
        }
        if (CollectionUtils.isEmpty(xinJiList)) {
            res.setRecords(new ArrayList<>());
            return res;
        }
        //todo 并行
        Map<Integer, AreaInfo> areaInfoMap = areaInfoService.getAreaInfoMap();

        List<Integer> evaluatedSubId = webOrderMapper.checkEvaluatedInType(allSubId, xinjiTypeEnums,
                jiujiSystemProperties.getOfficeName());

        List<Integer> allRecoverSubId = xinJiList.stream()
                .filter(tmp -> tmp.getRecoverSubId() != null && tmp.getRecoverSubId() > 0)
                .map(XinjiSubBO::getRecoverSubId)
                .collect(Collectors.toList());

        List<HuiShouSubBO> huiShouSubBOS = new ArrayList<>();
        List<HuiShouProductBO> huiShouProductBOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(allRecoverSubId)) {
            //todo 并行
            huiShouSubBOS = webOrderMapper.listHuishouByRecoverSubId(allRecoverSubId, Collections.emptyList(), 1);
            huiShouProductBOS = webOrderMapper.listHuishouProductByRecoverSubId(allRecoverSubId);
        }

        Map<Integer, List<XinjiProductBO>> xinJiProductMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(allXinJiProduct)) {
            xinJiProductMap = allXinJiProduct.stream().collect(Collectors.groupingBy(XinjiProductBO::getSubId));
        }
        Map<Integer, List<HuiShouSubBO>> huiShouSubMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(huiShouSubBOS)) {
            huiShouSubMap = huiShouSubBOS.stream()
                    .collect(Collectors.groupingBy(HuiShouSubBO::getRecoverSubId));
        }
        Map<Integer, List<HuiShouProductBO>> huiShouProductMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(huiShouProductBOS)) {
            huiShouProductMap = huiShouProductBOS.stream()
                    .collect(Collectors.groupingBy(HuiShouProductBO::getRecoverSubId));
        }

        //todo 并行
        // 根据订单查询发票
        List<TaxPiAoBO> taxPiAos = orderDetailMapper.getTaxPiAos(allSubId, InvoiceType.Normal.getCode());
        Map<Integer, TaxPiAoBO> taxPiAoMap = taxPiAos.stream().collect(
                Collectors.toMap(TaxPiAoBO::getSubId, o -> o, (v1, v2) -> v1));

        // 根据订单查询专属客服
        //todo 并行
        Map<Integer, List<Ch999UserServiceVO>> exclusiveCustomerServiceMap = getExclusiveCustomerService(allSubId,
                EXCLUSIVE_CUSTOMER_SERVICE_COUNT);

        List<XinjiSubVO> xinJiSubVOList = new ArrayList<>();
        for (XinjiSubBO tmp : xinJiList) {
            Boolean isException = checkException(exceptionSubKey, tmp.getSubId().toString());
            xinJiSubVOList.add(WebOrderWrapper.toXinjiSubVO(tmp
                    , areaInfoMap, evaluatedSubId, isException
                    , xinJiProductMap, huiShouSubMap, huiShouProductMap
                    , taxPiAoMap, exclusiveCustomerServiceMap, Collections.emptyMap()));
        }
        res.setRecords(xinJiSubVOList);
        return res;
    }

    /**
     * 进行数据的解析
     * @param pageResR
     * @return
     */
    private PendingPaymentVO createPendingPaymentVO(R<PageRes<XinjiSubVO>> pageResR,String tagType){
        int code = pageResR.getCode();
        if(ResultCode.SUCCESS==code){
            PageRes<XinjiSubVO> data = Optional.ofNullable(pageResR.getData()).orElse(new PageRes<>());
            List<XinjiSubVO> records = data.getRecords();
            if(CollectionUtils.isNotEmpty(records)){
                XinjiSubVO xinjiSubVO = records.get(0);
                List<XinjiProductVO> productList = xinjiSubVO.getProductList();
                if(CollectionUtils.isNotEmpty(productList)){
                    //获取价格最高的商品
                    PendingPaymentVO pendingPaymentVO = new PendingPaymentVO();
                    productList.stream().max(Comparator.comparing(XinjiProductVO::getPrice)).ifPresent(item->{
                        pendingPaymentVO.setPpid(item.getPpid());
                        pendingPaymentVO.setBasketId(item.getBasketId());

                    });
                    pendingPaymentVO.setOrderId(xinjiSubVO.getSubId());
                    pendingPaymentVO.setSubCheck(xinjiSubVO.getSubCheck());
                    pendingPaymentVO.setTagType(tagType);
                    return pendingPaymentVO;
                }
            }
        }
        return null;
    }

    @Override
    public R<PendingPaymentVO> selectOrderPendingPayment(PendingPaymentReq req){
        Integer userId = req.getUserId();
        if(ObjectUtil.isNull(userId)){
            return R.error("userId不能为空");
        }
        Integer xtenant = req.getXtenant();
        if(ObjectUtil.isNull(xtenant)){
            return R.error("xtenant不能为空");
        }
        //构建查询的参数
        WebOrderQueryReq webOrderQueryReq = new WebOrderQueryReq();
        webOrderQueryReq.setXtenant(req.getXtenant());
        webOrderQueryReq.setUserId(req.getUserId());
        webOrderQueryReq.setQueryStaff(Boolean.FALSE);
        webOrderQueryReq.setQueryHistoryOrder(NumberConstant.TWO);
        webOrderQueryReq.setTagType(OrderTypeTag.Normal_Wait_Pay.getCode());
        //查询待付款的订单数据
        log.warn("构建查询待付款的订单参数：{}", JSONUtil.toJsonStr(webOrderQueryReq));
        R<PageRes<XinjiSubVO>> pageResR = pageNormalV2(webOrderQueryReq);
        log.warn("查询待付款的订单结果：{}", JSONUtil.toJsonStr(pageResR));
        PendingPaymentVO normalWaitPay = createPendingPaymentVO(pageResR,OrderTypeTag.Normal_Wait_Pay.getCode());
        if(ObjectUtil.isNotNull(normalWaitPay)){
            return R.success(normalWaitPay);
        }
        //如果待付款的数据查不出来那就查询待收货的订单数据
        webOrderQueryReq.setTagType(OrderTypeTag.Normal_Wait_Take.getCode());
        log.warn("构建查询待收货的订单参数：{}", JSONUtil.toJsonStr(webOrderQueryReq));
        R<PageRes<XinjiSubVO>> normalWaitTake = pageNormalV2(webOrderQueryReq);
        log.warn("查询待收货的订单结果：{}", JSONUtil.toJsonStr(normalWaitTake));
        return R.success(createPendingPaymentVO(normalWaitTake,OrderTypeTag.Normal_Wait_Take.getCode()));

    }
    @Override
    public R<PageRes<XinjiSubVO>> pageNormalV2(WebOrderQueryReq req) {
        PageRes<XinjiSubVO> res = new PageRes<>(req.getCurrent(), req.getSize());
        Integer startRows = req.getStartRows();
        if(startRows == 0 && WebOrderQueryReq.isNotTotal(req.getQueryHistoryOrder())){
            //重置history 的查询状态 首页的查询必须为 NO_TOTAL_ON_TIME_ORDER
            req.setQueryHistoryOrder(WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ON_TIME_ORDER.getCode());
        }
        List<Integer> status = CommonUtil.covertIdStr(req.getStatus());
        req.setHideOrder(Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.HIDE_ORDER_CONFIG))
                .filter(R::isSuccess).map(R::getData).filter(StrUtil::isNotBlank).map(Convert::toInt).orElse(0));
        log.info("start");
        WebOrderServiceImpl webOrderService = (WebOrderServiceImpl) AopContext.currentProxy();
        //异步主表计数
        CompletableFuture<Integer> countFuture = CompletableFuture.supplyAsync(() ->{
            Integer count = 0;
            if (WebOrderQueryReq.isTotal(req.getQueryHistoryOrder())
                    || ObjectUtil.equal(req.getQueryHistoryOrder(),WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ONLY_HISTORY_ORDER.getCode())){
                count = webOrderService.count(req, false, getSearchId(req.getKeyWord()), status);
            }
            return count;
        }, executor);
        //异步历史表计数
        CompletableFuture<Integer> countHisFuture = CompletableFuture.supplyAsync(() -> {
            Integer countHis = 0;
            boolean isNeedQueryNormalHis = CommonUtils.isJiuJiXtenant(ObjectUtil.defaultIfNull(req.getXtenant(), 0))
                    && OrderTypeTag.needQueryNormalHis(req.getTagType())
                    && ObjectUtil.equal(req.getQueryHistoryOrder(),WebOrderQueryReq.HistoryOrderEnum.HISTORY_ORDER.getCode());
            if (isNeedQueryNormalHis) {
                countHis = webOrderService.count(req, true, getSearchId(req.getKeyWord()),status);
            }
            return countHis;
        }, executor);

        Integer count = countFuture.join();
        Integer countHis = countHisFuture.join();
        log.info("count:{}", count);
        log.info("countHis:{}", countHis);
        Integer total = count + countHis;
        res.setTotal(total);
        R<PageRes<XinjiSubVO>> success = R.success(null);
        String queryHistoryOrder = WebOrderService.QUERY_HISTORY_ORDER;
        //默认返回原值
        success.put(queryHistoryOrder,req.getQueryHistoryOrder());
        if (startRows > total && WebOrderQueryReq.isTotal(req.getQueryHistoryOrder())
                || ObjectUtil.equal(WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_NO_DATA.getCode(),req.getQueryHistoryOrder())) {
            res.setRecords(new ArrayList<>());
            success.setData(res);
            return success;
        }

        //异步查询主表记录
        CompletableFuture<List<XinjiSubBO>> normalListFuture = CompletableFuture.supplyAsync(() -> {
            if (startRows < count || ObjectUtil.equal(req.getQueryHistoryOrder(),WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ON_TIME_ORDER.getCode())) {
                List<XinjiSubBO> normalList = webOrderMapper.listNormal(req.getUserId(), req.getXtenant(),
                        req.getTagType(), req.getKeyWord(), getSearchId(req.getKeyWord()), false,
                        startRows, req.getSize(), req.getDistribution(), DecideUtil.iif(CollectionUtils.isEmpty(status) , null , status),
                        req.getOrderLinkFlag(),req.getQueryHistoryOrder(),req.getHideOrder(),req);
                log.info("normalList:{}", normalList.size());
                return normalList;
            }
            return Collections.emptyList();
        }, executor);
        //异步查询历史表记录
        CompletableFuture<List<XinjiSubBO>> hisListFuture = CompletableFuture.supplyAsync(() -> {
            Integer tempCount = count;
            boolean isNotTotal = WebOrderQueryReq.isNotTotal(req.getQueryHistoryOrder());
            boolean isQueryHist = req.getXtenant() < 1000;
            Integer startRowsHis;
            Integer size;
            if(isNotTotal && count <= 0){
                tempCount = normalListFuture.join().size();
                startRowsHis = 0;
                size = req.getSize()-tempCount;
                isQueryHist = isQueryHist && tempCount < req.getSize();
            }else{
                isQueryHist = isQueryHist
                        && ((WebOrderQueryReq.isTotal(req.getQueryHistoryOrder()) && countHis > 0 && startRows < tempCount + countHis)
                        || WebOrderQueryReq.isNotTotal(req.getQueryHistoryOrder()))
                        && (startRows + req.getSize() > tempCount);
                startRowsHis = Integer.max(startRows - tempCount, 0);
                double normalRemainderPage = Math.ceil(tempCount * 1.0 / req.getSize());
                size = normalRemainderPage == req.getCurrent() ? req.getSize() - tempCount % req.getSize() : req.getSize();
            }

            List<XinjiSubBO> listHis = Collections.emptyList();
            if (isQueryHist) {
                listHis = webOrderMapper.listNormalHis(req.getUserId(), req.getXtenant(),
                        req.getTagType(), req.getKeyWord(), getSearchId(req.getKeyWord()), true,
                        startRowsHis, size, req.getDistribution(), DecideUtil.iif(CollectionUtils.isEmpty(status) , null , status),
                        req.getOrderLinkFlag(),req.getQueryHistoryOrder(),req.getHideOrder(),req);
                log.info("listHis:{}", listHis.size());
            }

            if(isNotTotal){
                if(CollUtil.isNotEmpty(listHis) && normalListFuture.join().size()+listHis.size() >= req.getSize()){
                    success.put(queryHistoryOrder,WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ONLY_HISTORY_ORDER.getCode());
                }else if(isQueryHist){
                    success.put(queryHistoryOrder,WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_NO_DATA.getCode());
                }else{
                    success.put(queryHistoryOrder,WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ON_TIME_ORDER.getCode());
                }
            }

            return listHis;
        }, executor);
        List<XinjiSubBO> normalList = normalListFuture.join();
        List<XinjiSubBO> xinJiList = new ArrayList<>();
        List<Integer> allSubId = new ArrayList<>();
        //异步查询主表产品信息
        CompletableFuture<List<XinjiProductBO>> normalProductFuture = CompletableFuture.completedFuture(Collections.emptyList());
        if (CollectionUtils.isNotEmpty(normalList)) {
            xinJiList.addAll(normalList);
            List<Integer> listSubId = normalList.stream()
                    .map(XinjiSubBO::getSubId).collect(Collectors.toList());
            allSubId.addAll(listSubId);
            normalProductFuture = CompletableFuture.supplyAsync(() -> {
                List<XinjiProductBO> productBOList = webOrderMapper.listProductNormal(listSubId);
                productBOList = productBOList.stream().distinct().collect(Collectors.toList());
                return productBOList;
            }, executor);
        }
        List<XinjiSubBO> hisList = hisListFuture.join();
        //异步查询历史表产品信息
        CompletableFuture<List<XinjiProductBO>> hisProductFuture = CompletableFuture.completedFuture(Collections.emptyList());
        if (CollectionUtils.isNotEmpty(hisList)) {
            xinJiList.addAll(hisList);
            List<Integer> listSubId = hisList.stream()
                    .map(XinjiSubBO::getSubId).collect(Collectors.toList());
            allSubId.addAll(listSubId);
            hisProductFuture = CompletableFuture.supplyAsync(() -> {
                List<XinjiProductBO> productBOList = webOrderMapper.listProductNormalHis(listSubId);
                return productBOList;
            }, executor);
        }

        if (CollectionUtils.isEmpty(xinJiList)) {
            res.setRecords(new ArrayList<>());
            success.setData(res);
            return success;
        }
        //并行获取区域信息
        CompletableFuture<Map<Integer, AreaInfo>> areaInfoMapFuture = CompletableFuture.supplyAsync(() -> areaInfoService.getAreaInfoMap(), executor);
        //并行获取已评价的订单id
        CompletableFuture<List<Integer>> evaluatedSubIdFuture = CompletableFuture.supplyAsync(() -> webOrderMapper.checkEvaluatedInType(allSubId, xinjiTypeEnums,
                jiujiSystemProperties.getOfficeName()), executor);

        CompletableFuture<Map<Integer, List<HuiShouSubBO>>> huiShouSubMapFuture = CompletableFuture.completedFuture(Collections.emptyMap());
        CompletableFuture<Map<Integer, List<HuiShouProductBO>>> huiShouProductMapFuture = CompletableFuture.completedFuture(Collections.emptyMap());
        if (CollectionUtils.isNotEmpty(allSubId)) {
            //并行查询回收订单
            huiShouSubMapFuture = CompletableFuture.supplyAsync(() -> {
                List<HuiShouSubBO> huiShouSubBOS = webOrderMapper.listHuishouByRecoverSubId(Collections.emptyList(), allSubId, 1);
                if (CollectionUtils.isNotEmpty(huiShouSubBOS)) {
                    return huiShouSubBOS.stream().sorted(Comparator.comparing(HuiShouSubBO::getAddTime, Comparator.nullsLast(Comparator.reverseOrder())))
                            .collect(Collectors.groupingBy(HuiShouSubBO::getSubId));
                }
                return Collections.emptyMap();
            }, executor);
            //并行查询回收产品信息
            huiShouProductMapFuture = huiShouSubMapFuture.thenApplyAsync(huiShouSubList -> {
                if (huiShouSubList.isEmpty()) {
                    return Collections.emptyMap();
                }
                Set<Integer> allRecoverSubId = huiShouSubList.entrySet().stream().flatMap(entry -> entry.getValue().stream())
                        .map(HuiShouSubBO::getRecoverSubId).collect(Collectors.toSet());
                List<HuiShouProductBO> huiShouProductBOS = webOrderMapper.listHuishouProductByRecoverSubId(allRecoverSubId);
                if (CollectionUtils.isNotEmpty(huiShouProductBOS)) {
                    return huiShouProductBOS.stream()
                            .collect(Collectors.groupingBy(HuiShouProductBO::getRecoverSubId));
                } else {
                    return Collections.emptyMap();
                }
            }, executor);
        }

        // 异步根据订单查询发票
        CompletableFuture<Map<Integer, TaxPiAoBO>> taxPiAoMapFuture = CompletableFuture.supplyAsync(() -> {
            List<TaxPiAoBO> taxPiAos = orderDetailMapper.getTaxPiAos(allSubId, InvoiceType.Normal.getCode());
            Map<Integer, TaxPiAoBO> taxPiAoMap = taxPiAos.stream().collect(
                    Collectors.toMap(TaxPiAoBO::getSubId, o -> o, (v1, v2) -> v1));
            return taxPiAoMap;
        }, executor);
        // 异步根据订单查询专属客服
        CompletableFuture<Map<Integer, List<Ch999UserServiceVO>>> exclusiveCustomerServiceMapFuture = CompletableFuture.supplyAsync(() -> {
            Map<Integer, List<Ch999UserServiceVO>> exclusiveCustomerServiceMap = webOrderService.getExclusiveCustomerService(allSubId,
                    EXCLUSIVE_CUSTOMER_SERVICE_COUNT);
            return exclusiveCustomerServiceMap;
        }, executor);
        //异步查询是否为良品配件
        CompletableFuture<Map<Integer, BindSubInfoBo>> newSubIdAndLpSubIdMapFuture = CompletableFuture.supplyAsync(() -> {
            Map<Integer, BindSubInfoBo> newSubIdAndLpSubIdMap = CommonUtil.autoQueryMergeHist(() -> webOrderMapper.listBindLpPjSubInfo(allSubId, BusinessTypeEnum.SALE_ORDER))
                    .stream().collect(Collectors.toMap(lp->lp.getSubId(), lp->lp,(lp1, lp2)->lp1));
            return newSubIdAndLpSubIdMap;
        }, executor);

        //主表产品追加
        List<XinjiProductBO> productBOList = normalProductFuture.join();
        List<XinjiProductBO> allXinJiProduct = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(productBOList)) {
            allXinJiProduct.addAll(productBOList);
        }
        //历史产品追加
        List<XinjiProductBO> hisProductBOList = hisProductFuture.join();
        if (CollectionUtils.isNotEmpty(hisProductBOList)) {
            allXinJiProduct.removeAll(hisProductBOList);
            allXinJiProduct.addAll(hisProductBOList);
        }
        Map<Integer, List<XinjiProductBO>> xinJiProductMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(allXinJiProduct)) {
            xinJiProductMap = allXinJiProduct.stream().collect(Collectors.groupingBy(XinjiProductBO::getSubId));
        }
        List<XinjiSubVO> xinJiSubVOList = new ArrayList<>();
        Map<Integer, AreaInfo> areaInfoMap = areaInfoMapFuture.join();
        List<Integer> evaluatedSubId = evaluatedSubIdFuture.join();
        //订单号为键
        Map<Integer, List<HuiShouSubBO>> huiShouSubMap = huiShouSubMapFuture.join();
        Map<Integer, List<HuiShouProductBO>> huiShouProductMap = huiShouProductMapFuture.join();
        Map<Integer, TaxPiAoBO> taxPiAoMap = taxPiAoMapFuture.join();
        Map<Integer, List<Ch999UserServiceVO>> exclusiveCustomerServiceMap = exclusiveCustomerServiceMapFuture.join();
        Map<Integer, BindSubInfoBo> newSubIdAndLpSubIdMap = newSubIdAndLpSubIdMapFuture.join();
        for (XinjiSubBO tmp : xinJiList) {
            Boolean isException = checkException(exceptionSubKey, tmp.getSubId().toString());
            xinJiSubVOList.add(WebOrderWrapper.toXinjiSubVO(tmp
                    , areaInfoMap, evaluatedSubId, isException
                    , xinJiProductMap, huiShouSubMap, huiShouProductMap
                    , taxPiAoMap, exclusiveCustomerServiceMap,newSubIdAndLpSubIdMap));
        }
        res.setRecords(xinJiSubVOList);
        success.setData(res);
        return success;
    }
//    public PageRes<XinjiSubVO> pageNormal(WebOrderQueryReq req) {
//        PageRes<XinjiSubVO> res = new PageRes<>(req.getCurrent(), req.getSize());
//        Integer startRows = (req.getCurrent() - 1) * req.getSize();
//
//        log.info("start");
//        Integer count = webOrderMapper.countNormal(req.getUserId(), req.getXtenant(), req.getTagType(),
//                req.getKeyWord(), false, getSearchId(req.getKeyWord()));
//        log.info("count:{}", count);
//        Integer countHis = webOrderMapper.countNormalHis(req.getUserId(), req.getXtenant(), req.getTagType(),
//                req.getKeyWord(), true, getSearchId(req.getKeyWord()));
//        log.info("countHis:{}", countHis);
//        Integer total = count + countHis;
//        res.setTotal(total);
//
//        if (startRows > total) {
//            res.setRecords(new ArrayList<>());
//            return res;
//        }
//
//        List<XinjiSubBO> xinJiList = new ArrayList<>();
//        List<Integer> allSubId = new ArrayList<>();
//        List<XinjiProductBO> allXinJiProduct = new ArrayList<>();
//
//        if (startRows < count) {
//            List<XinjiSubBO> list = webOrderMapper.listNormal(req.getUserId(), req.getXtenant(),
//                    req.getTagType(), req.getKeyWord(), getSearchId(req.getKeyWord()), false,
//                    startRows, req.getSize());
//            log.info("list:{}", list.size());
//            if (CollectionUtils.isNotEmpty(list)) {
//                xinJiList.addAll(list);
//                List<Integer> listSubId = list.stream()
//                        .map(XinjiSubBO::getSubId).collect(Collectors.toList());
//                allSubId.addAll(listSubId);
//                List<XinjiProductBO> productBOList = webOrderMapper.listProductNormal(listSubId);
//                productBOList = productBOList.stream().distinct().collect(Collectors.toList());
//                allXinJiProduct.addAll(productBOList);
//            }
//        }
//
//        if (startRows + req.getSize() > count) {
//            Integer startRowsHis = Integer.max(startRows - count, 0);
//            Integer size = req.getSize() - xinJiList.size();
//
//            List<XinjiSubBO> listHis = webOrderMapper.listNormalHis(req.getUserId(), req.getXtenant(),
//                    req.getTagType(), req.getKeyWord(), getSearchId(req.getKeyWord()), true,
//                    startRowsHis, size);
//            log.info("listHis:{}", listHis.size());
//            if (CollectionUtils.isNotEmpty(listHis)) {
//                xinJiList.addAll(listHis);
//                List<Integer> listSubId = listHis.stream()
//                        .map(XinjiSubBO::getSubId).collect(Collectors.toList());
//                allSubId.addAll(listSubId);
//                List<XinjiProductBO> productBOList = webOrderMapper.listProductNormalHis(listSubId);
//                allXinJiProduct.addAll(productBOList);
//            }
//        }
//
//        if (CollectionUtils.isEmpty(xinJiList)) {
//            res.setRecords(new ArrayList<>());
//            return res;
//        }
//        //todo 并行
//        Map<Integer, AreaInfo> areaInfoMap = areaInfoService.getAreaInfoMap();
//
//        List<Integer> evaluatedSubId = webOrderMapper.checkEvaluatedInType(allSubId, xinjiTypeEnums,
//                jiujiSystemProperties.getOfficeName());
//
//        List<Integer> allRecoverSubId = xinJiList.stream()
//                .filter(tmp -> tmp.getRecoverSubId() != null && tmp.getRecoverSubId() > 0)
//                .map(XinjiSubBO::getRecoverSubId)
//                .collect(Collectors.toList());
//
//        List<HuiShouSubBO> huiShouSubBOS = new ArrayList<>();
//        List<HuiShouProductBO> huiShouProductBOS = new ArrayList<>();
//
//        if (CollectionUtils.isNotEmpty(allRecoverSubId)) {
//            //todo 并行
//            huiShouSubBOS = webOrderMapper.listHuishouByRecoverSubId(allRecoverSubId);
//            huiShouProductBOS = webOrderMapper.listHuishouProductByRecoverSubId(allRecoverSubId);
//        }
//
//        Map<Integer, List<XinjiProductBO>> xinJiProductMap = new HashMap<>();
//        if (CollectionUtils.isNotEmpty(allXinJiProduct)) {
//            xinJiProductMap = allXinJiProduct.stream().collect(Collectors.groupingBy(XinjiProductBO::getSubId));
//        }
//        Map<Integer, List<HuiShouSubBO>> huiShouSubMap = new HashMap<>();
//        if (CollectionUtils.isNotEmpty(huiShouSubBOS)) {
//            huiShouSubMap = huiShouSubBOS.stream()
//                    .collect(Collectors.groupingBy(HuiShouSubBO::getRecoverSubId));
//        }
//        Map<Integer, List<HuiShouProductBO>> huiShouProductMap = new HashMap<>();
//        if (CollectionUtils.isNotEmpty(huiShouProductBOS)) {
//            huiShouProductMap = huiShouProductBOS.stream()
//                    .collect(Collectors.groupingBy(HuiShouProductBO::getRecoverSubId));
//        }
//
//        // 根据订单查询发票
//        CompletableFuture<Map<Integer, TaxPiAoBO>> taxPiAoMap = CompletableFuture.supplyAsync(() ->
//                        orderDetailMapper.getTaxPiAos(allSubId, InvoiceType.Normal.getCode())
//                                .stream().collect(Collectors.toMap(TaxPiAoBO::getSubId, o -> o, (v1, v2) -> v1))
//                , executor);
//
//        // 根据订单查询专属客服
//        CompletableFuture<Map<Integer, List<Ch999UserServiceVO>>> exclusiveCustomerServiceMap =
//                CompletableFuture.supplyAsync(() -> getExclusiveCustomerService(allSubId,
//                        EXCLUSIVE_CUSTOMER_SERVICE_COUNT), executor);
//
//
//        List<XinjiSubVO> xinJiSubVOList = Collections.synchronizedList(new ArrayList<>());
//        Map<Integer, List<XinjiProductBO>> finalXinJiProductMap = new ConcurrentHashMap<>(xinJiProductMap);
//        Map<Integer, List<HuiShouSubBO>> finalHuiShouSubMap = new ConcurrentHashMap<>(huiShouSubMap);
//        Map<Integer, List<HuiShouProductBO>> finalHuiShouProductMap = new ConcurrentHashMap<>(huiShouProductMap);
//        xinJiList.stream().map(tmp ->
//                CompletableFuture.runAsync(() -> {
//                    Boolean isException = checkException(exceptionSubKey, tmp.getSubId().toString());
//                    xinJiSubVOList.add(WebOrderWrapper.toXinjiSubVO(tmp
//                            , areaInfoMap, evaluatedSubId, isException
//                            , finalXinJiProductMap, finalHuiShouSubMap, finalHuiShouProductMap
//                            , taxPiAoMap.join(), exclusiveCustomerServiceMap.join()));
//                }, executor)).forEach(CompletableFuture::join);
//        res.setRecords(xinJiSubVOList);
//
//        return res;
//    }

    @Override
    public PageRes<LiangpinSubVO> pageLiangpin(WebOrderQueryReq req) {
        PageRes<LiangpinSubVO> res = new PageRes<>(req.getCurrent(), req.getSize());
        Integer startRows = (req.getCurrent() - 1) * req.getSize();
        boolean isNotTotal = WebOrderQueryReq.isNotTotal(req.getQueryHistoryOrder());
        if(startRows == 0 && isNotTotal){
            //重置history 的查询状态 首页的查询必须为 NO_TOTAL_ON_TIME_ORDER
            req.setQueryHistoryOrder(WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ON_TIME_ORDER.getCode());
        }
        Boolean displayInvoice = Optional.ofNullable(req.getDisplayInvoice()).orElse(Boolean.FALSE);
        List<Integer> status = CommonUtil.covertIdStr(req.getStatus());
        log.info("start");
        Supplier<Integer> countFun = () -> webOrderMapper.countLiangpin(req.getUserId(),displayInvoice, req.getXtenant(), req.getTagType(),
                req.getKeyWord(), getSearchId(req.getKeyWord()), CollectionUtils.isEmpty(status) ? null : status, req);
        //主表计数
        Integer count = Optional.of(WebOrderQueryReq.isTotal(req.getQueryHistoryOrder())
                || ObjectUtil.equal(req.getQueryHistoryOrder(),WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ONLY_HISTORY_ORDER.getCode()))
                .filter(Boolean::booleanValue)
                .map(isNeedCount -> countFun.get())
                .orElse(0);
        //历史表计数
        boolean isJiuJiXtenant = CommonUtils.isJiuJiXtenant(ObjectUtil.defaultIfNull(req.getXtenant(), 0));
        Integer countHis = Optional.of(isJiuJiXtenant
                && OrderTypeTag.needQueryNormalHis(req.getTagType())
                && ObjectUtil.equal(req.getQueryHistoryOrder(),WebOrderQueryReq.HistoryOrderEnum.HISTORY_ORDER.getCode()))
                        .map(isNeedQueryNormalHis -> MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS, () -> countFun.get()))
                .orElse(0);
        log.warn("count:{}, countHis:{}", count, countHis);
        Integer total = count + countHis;
        res.setTotal(total);
        String queryHistoryOrder = WebOrderService.QUERY_HISTORY_ORDER;
        //默认返回原值
        SpringContextUtil.getRequest().ifPresent(request -> request.setAttribute(queryHistoryOrder,req.getQueryHistoryOrder()));
        if (startRows > total && WebOrderQueryReq.isTotal(req.getQueryHistoryOrder())
                || ObjectUtil.equal(WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_NO_DATA.getCode(),req.getQueryHistoryOrder())) {
        res.setRecords(new ArrayList<>());
            return res;
        }

        List<LiangpinSubBO> list = new ArrayList<>();
        List<LiangpinProductBO> productList = new ArrayList<>();
        Map<Integer, List<LiangpinProductBO>> productMap = new HashMap<>();

        BiFunction<Integer, Integer, List<LiangpinSubBO>> listLiangpinFun =(startRow, size) -> webOrderMapper.listLiangpin(
                req.getUserId(),displayInvoice, req.getXtenant(), req.getTagType(), req.getKeyWord(), getSearchId(req.getKeyWord()),
                startRow, size, CollectionUtils.isEmpty(status) ? null : status, req);

        list = new LinkedList<>();
        List<LiangpinSubBO> normalList = Collections.emptyList();
        //查询主表记录
        if (startRows < count || ObjectUtil.equal(req.getQueryHistoryOrder(),WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ON_TIME_ORDER.getCode())) {
            normalList = listLiangpinFun.apply(startRows, req.getSize());
            log.warn("normalList:{}", normalList.size());
            list.addAll(normalList);
        }
        // region 查询历史表记录
        Integer onTimeCount = count;
        boolean isQueryHist = isJiuJiXtenant;
        Integer startRowsHis;
        Integer sizeHis;
        if(isNotTotal && count <= 0){
            onTimeCount = normalList.size();
            startRowsHis = 0;
            sizeHis = req.getSize()-onTimeCount;
            isQueryHist = isQueryHist && onTimeCount < req.getSize();
        }else{
            isQueryHist = isQueryHist
                    && ((WebOrderQueryReq.isTotal(req.getQueryHistoryOrder()) && countHis > 0 && startRows < onTimeCount + countHis)
                    || isNotTotal)
                    && (startRows + req.getSize() > onTimeCount);
            startRowsHis = Integer.max(startRows - onTimeCount, 0);
            double normalRemainderPage = Math.ceil(onTimeCount * 1.0 / req.getSize());
            sizeHis = normalRemainderPage == req.getCurrent() ? req.getSize() - onTimeCount % req.getSize() : req.getSize();
        }

        List<LiangpinSubBO> listHis = Collections.emptyList();
        if (isQueryHist) {
            listHis = MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS, () -> listLiangpinFun.apply(startRowsHis, sizeHis));
            log.warn("listHis:{}", listHis.size());
            list.addAll(listHis);
        }

        if(isNotTotal){
            if(CollUtil.isNotEmpty(listHis) && normalList.size()+listHis.size() >= req.getSize()){
                SpringContextUtil.getRequest().ifPresent(request -> request.setAttribute(queryHistoryOrder,WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ONLY_HISTORY_ORDER.getCode()));
            }else if(isQueryHist){
                SpringContextUtil.getRequest().ifPresent(request -> request.setAttribute(queryHistoryOrder,WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_NO_DATA.getCode()));
            }else{
                SpringContextUtil.getRequest().ifPresent(request -> request.setAttribute(queryHistoryOrder,WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ON_TIME_ORDER.getCode()));
            }
        }
        // endregion

        if (CollectionUtils.isEmpty(list)) {
            res.setRecords(new ArrayList<>());
            return res;
        }
        List<Integer> allNormalSubId = normalList.stream().map(LiangpinSubBO::getSubId).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(allNormalSubId)){
            productList.addAll(webOrderMapper.listProductLiangpin(allNormalSubId));
        }
        List<Integer> allHisSubId = listHis.stream().map(LiangpinSubBO::getSubId).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(allHisSubId)){
            productList.addAll(MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS,()-> webOrderMapper.listProductLiangpin(allHisSubId)));
        }
        if (CollectionUtils.isNotEmpty(productList)) {
            productMap = productList.stream()
                    .collect(Collectors.groupingBy(LiangpinProductBO::getSubId));
        }

        R<List<AreaInfo>> areInfoAllR = areaInfoClient.listAll();
        List<AreaInfo> areaInfoAll = new ArrayList<>();
        if (areInfoAllR.getCode() == 0) {
            areaInfoAll = areInfoAllR.getData();
        }
        Collection<Integer> allSubIds = CollUtil.union(allHisSubId, allNormalSubId);
        List<Integer> evaluatedSubId = webOrderMapper.checkEvaluatedByType(allSubIds, EvaluateTypeEnum.Liangpin,
                jiujiSystemProperties.getOfficeName());

        List<HuiShouSubBO> huiShouSubBOS = new ArrayList<>();
        List<HuiShouProductBO> huishouProductBOS = new ArrayList<>();
        Map<Integer, List<HuiShouSubBO>> huishouSubMap = new HashMap<>();
        Map<Integer, List<HuiShouProductBO>> huishouProductMap = new HashMap<>();
        if(CollUtil.isNotEmpty(allNormalSubId)){
            huiShouSubBOS.addAll(webOrderMapper.listHuishouByRecoverSubId(Collections.emptyList(), allNormalSubId, 2));
        }
        if(CollUtil.isNotEmpty(allHisSubId)){
            huiShouSubBOS.addAll(MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS,()->
                    webOrderMapper.listHuishouByRecoverSubId(Collections.emptyList(), allHisSubId, 2)));
        }
        if (CollectionUtils.isNotEmpty(huiShouSubBOS)) {
            List<Integer> allRecoverSubId = huiShouSubBOS.stream().map(HuiShouSubBO::getRecoverSubId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(huiShouSubBOS)) {
                huishouSubMap = huiShouSubBOS.stream()
                        .collect(Collectors.groupingBy(HuiShouSubBO::getSubId));
                huishouProductBOS = webOrderMapper.listHuishouProductByRecoverSubId(allRecoverSubId);
            }
            if (CollectionUtils.isNotEmpty(huishouProductBOS)) {
                huishouProductMap = huishouProductBOS.stream()
                        .collect(Collectors.groupingBy(HuiShouProductBO::getRecoverSubId));
            }
        }
        // 根据订单查询发票
        List<TaxPiAoBO> taxPiAos = orderDetailMapper.getTaxPiAos(allSubIds, InvoiceType.SecondHand.getCode());
        Map<Integer, TaxPiAoBO> taxPiAoMap = taxPiAos.stream().collect(
                Collectors.toMap(TaxPiAoBO::getSubId, o -> o, (v1, v2) -> v1));
        // 异步根据订单查询专属客服
        WebOrderServiceImpl webOrderService = (WebOrderServiceImpl) AopContext.currentProxy();
        CompletableFuture<Map<Integer, List<Ch999UserServiceVO>>> exclusiveLiangPinCustomerServiceMapFuture = CompletableFuture.supplyAsync(() -> {
            Map<Integer, List<Ch999UserServiceVO>> exclusiveLiangPinCustomerServiceMap = webOrderService.getLiangPinExclusiveCustomerService(allSubIds,EXCLUSIVE_CUSTOMER_SERVICE_COUNT);
            return exclusiveLiangPinCustomerServiceMap;
        }, executor);
        //异步查询是否为良品配件
        CompletableFuture<Map<Integer, BindSubInfoBo>> bindSubInfoMapFuture = CompletableFuture.supplyAsync(() -> {
            Map<Integer, BindSubInfoBo> bindSubInfoMap = CollUtil.newHashMap(allSubIds.size());
            if(CollUtil.isNotEmpty(allNormalSubId)){
                bindSubInfoMap.putAll(webOrderMapper.listBindLpPjSubInfo(allNormalSubId, BusinessTypeEnum.LP_ORDER).stream()
                        .collect(Collectors.toMap(lp->lp.getBindSubId(), lp->lp,(lp1, lp2)->lp1)));
            }
            if(CollUtil.isNotEmpty(allHisSubId)){
                bindSubInfoMap.putAll(MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS,() ->
                                webOrderMapper.listBindLpPjSubInfo(allHisSubId, BusinessTypeEnum.LP_ORDER)).stream()
                        .collect(Collectors.toMap(lp->lp.getBindSubId(), lp->lp,(lp1, lp2)->lp1)));
            }
            return bindSubInfoMap;
        }, executor);

        List<LiangpinSubVO> subVOList = new ArrayList<>();
        Map<Integer, List<Ch999UserServiceVO>> exclusiveLiangPinCustomerServiceMap = exclusiveLiangPinCustomerServiceMapFuture.join();
        Map<Integer, BindSubInfoBo> bindSubInfoMap = bindSubInfoMapFuture.join();
        for (LiangpinSubBO subBo : list) {
            Boolean isException = checkException(exceptionLiangpinSubKey, subBo.getSubId().toString());
            subVOList.add(WebOrderWrapper.toLiangpinSubVO(subBo
                    , areaInfoAll, evaluatedSubId, isException
                    , productMap, huishouSubMap, taxPiAoMap, huishouProductMap,exclusiveLiangPinCustomerServiceMap
                    ,bindSubInfoMap));
        }
        res.setRecords(subVOList);
        return res;
    }

    @Override
    public PageRes<RecoverSubVO> pageRecover(WebOrderQueryReq req) {
        PageRes<RecoverSubVO> res = new PageRes<>(req.getCurrent(), req.getSize());
        Integer startRows = (req.getCurrent() - 1) * req.getSize();
        boolean isNotTotal = WebOrderQueryReq.isNotTotal(req.getQueryHistoryOrder());

        if(startRows == 0 && isNotTotal){
            //重置history 的查询状态 首页的查询必须为 NO_TOTAL_ON_TIME_ORDER
            req.setQueryHistoryOrder(WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ON_TIME_ORDER.getCode());
        }
        List<Integer> status = CommonUtil.covertIdStr(req.getStatus());
        log.info("start");
        Supplier<Integer> countFun = () -> webOrderMapper.countRecover(req.getUserId(), req.getXtenant(), req.getTagType(),
                req.getKeyWord(), getSearchId(req.getKeyWord()), CollectionUtils.isEmpty(status) ? null : status, req);
        //主表计数
        Integer count = Optional.of(WebOrderQueryReq.isTotal(req.getQueryHistoryOrder())
                        || ObjectUtil.equal(req.getQueryHistoryOrder(),WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ONLY_HISTORY_ORDER.getCode()))
                .filter(Boolean::booleanValue)
                .map(isNeedCount -> countFun.get())
                .orElse(0);
        //历史表计数
        boolean isJiuJiXtenant = CommonUtils.isJiuJiXtenant(ObjectUtil.defaultIfNull(req.getXtenant(), 0));
        Integer countHis = Optional.of(isJiuJiXtenant
                        && OrderTypeTag.needQueryNormalHis(req.getTagType())
                        && ObjectUtil.equal(req.getQueryHistoryOrder(),WebOrderQueryReq.HistoryOrderEnum.HISTORY_ORDER.getCode()))
                .map(isNeedQueryNormalHis -> MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS, () -> countFun.get()))
                .orElse(0);
        log.warn("count:{}, countHis:{}", count, countHis);
        Integer total = count + countHis;
        res.setTotal(total);
        String queryHistoryOrder = WebOrderService.QUERY_HISTORY_ORDER;
        //默认返回原值
        SpringContextUtil.getRequest().ifPresent(request -> request.setAttribute(queryHistoryOrder,req.getQueryHistoryOrder()));
        if (startRows > total && WebOrderQueryReq.isTotal(req.getQueryHistoryOrder())
                || ObjectUtil.equal(WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_NO_DATA.getCode(),req.getQueryHistoryOrder())) {
            res.setRecords(new ArrayList<>());
            return res;
        }
        List<HuiShouSubBO> list = new LinkedList<>();
        //List<Integer> allSubId = new ArrayList<>();
        List<HuiShouProductBO> huishouProductBOS = new ArrayList<>();

        BiFunction<Integer, Integer, List<HuiShouSubBO>> listRecoverFun =(startRow, size) -> webOrderMapper.listRecover(
                req.getUserId(), req.getXtenant(), req.getTagType(), req.getKeyWord(), getSearchId(req.getKeyWord()),
                startRow, req.getSize(), CollectionUtils.isEmpty(status) ? null : status, req);


        // 主库记录
        List<HuiShouSubBO> normalList = Collections.emptyList();
        if (startRows < count || ObjectUtil.equal(req.getQueryHistoryOrder(),WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ON_TIME_ORDER.getCode())) {
            normalList = listRecoverFun.apply(startRows, req.getSize());
            log.warn("normalList:{}", normalList.size());
            list.addAll(normalList);
        }

        // region 查询历史表记录
        Integer onTimeCount = count;
        boolean isQueryHist = isJiuJiXtenant;
        Integer startRowsHis;
        Integer sizeHis;
        if(isNotTotal && count <= 0){
            onTimeCount = normalList.size();
            startRowsHis = 0;
            sizeHis = req.getSize()-onTimeCount;
            isQueryHist = isQueryHist && onTimeCount < req.getSize();
        }else{
            isQueryHist = isQueryHist
                    && ((WebOrderQueryReq.isTotal(req.getQueryHistoryOrder()) && countHis > 0 && startRows < onTimeCount + countHis)
                    || isNotTotal)
                    && (startRows + req.getSize() > onTimeCount);
            startRowsHis = Integer.max(startRows - onTimeCount, 0);
            double normalRemainderPage = Math.ceil(onTimeCount * 1.0 / req.getSize());
            sizeHis = normalRemainderPage == req.getCurrent() ? req.getSize() - onTimeCount % req.getSize() : req.getSize();
        }

        List<HuiShouSubBO> listHis = Collections.emptyList();
        if (isQueryHist) {
            listHis = MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS, () -> listRecoverFun.apply(startRowsHis, sizeHis));
            log.warn("listHis:{}", listHis.size());
            list.addAll(listHis);
        }

        if(isNotTotal){
            if(CollUtil.isNotEmpty(listHis) && normalList.size()+listHis.size() >= req.getSize()){
                SpringContextUtil.getRequest().ifPresent(request -> request.setAttribute(queryHistoryOrder,WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ONLY_HISTORY_ORDER.getCode()));
            }else if(isQueryHist){
                SpringContextUtil.getRequest().ifPresent(request -> request.setAttribute(queryHistoryOrder,WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_NO_DATA.getCode()));
            }else{
                SpringContextUtil.getRequest().ifPresent(request -> request.setAttribute(queryHistoryOrder,WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ON_TIME_ORDER.getCode()));
            }
        }
        // endregion

        if (CollectionUtils.isEmpty(list)) {
            res.setRecords(new ArrayList<>());
            return res;
        }

        List<Integer> allNormalSubId = normalList.stream().map(HuiShouSubBO::getRecoverSubId).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(allNormalSubId)){
            huishouProductBOS.addAll(webOrderMapper.listHuishouProductByRecoverSubId(allNormalSubId));
        }
        List<Integer> allHisSubId = listHis.stream().map(HuiShouSubBO::getRecoverSubId).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(allHisSubId)){
            huishouProductBOS.addAll(MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS,()->webOrderMapper.listHuishouProductByRecoverSubId(allHisSubId)));
        }


        List<Integer> allSubId = list.stream().map(HuiShouSubBO::getRecoverSubId)
                .collect(Collectors.toList());
//        huishouProductBOS = webOrderMapper.listHuishouProductByRecoverSubId(allSubId);
        if (CollectionUtils.isNotEmpty(huishouProductBOS)) {
            List<HuiShouProductVO> staffInfoList = webOrderMapper.listStaffInfoByMobile(huishouProductBOS.stream().map(item -> item.getMobile()).collect(Collectors.toList()));
            huishouProductBOS = huishouProductBOS.stream().map(t -> {
                HuiShouProductVO staffInfo = staffInfoList.stream().filter(e -> Objects.equals(e.getMobile(), t.getMobile())).findFirst().orElse(null);
                if (staffInfo != null) {
                    t.setIsStaffOrder(Boolean.TRUE);
                    t.setStaffId(staffInfo.getStaffId());
                    t.setStaffName(staffInfo.getStaffName());
                }
                t.setIsAssistSale(Objects.equals(t.getKinds(), 1) && CommonUtil.isNullOrZero(t.getIshouhou()));
                if (Objects.equals(t.getKinds(), 1) && CommonUtil.isNullOrZero(t.getIshouhou())) {
                    t.setAssistStatusText(EnumUtil.getMessageByCode(AssistStatusEnum.class, t.getAssistStatus()));
                }

                return t;
            }).collect(Collectors.toList());
        }

        List<Integer> evaluatedSubId = webOrderMapper.checkEvaluatedByType(allSubId, EvaluateTypeEnum.Recycle,
                jiujiSystemProperties.getOfficeName());
        Map<Integer, List<HuiShouProductBO>> huishouProductMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(huishouProductBOS)) {
            huishouProductMap = huishouProductBOS.stream()
                    .collect(Collectors.groupingBy(HuiShouProductBO::getRecoverSubId));
        }
        R<List<AreaInfo>> areInfoAllR = areaInfoClient.listAll();
        List<AreaInfo> areaInfoAll = new ArrayList<>();
        if (areInfoAllR.getCode() == 0) {
            areaInfoAll = areInfoAllR.getData();
        }

        List<RecoverSubVO> subVOList = new ArrayList<>();
        for (HuiShouSubBO subBo : list) {
            subVOList.add(WebOrderWrapper.toRecoverSubVO(subBo
                    , areaInfoAll, evaluatedSubId
                    , huishouProductMap));
        }
        res.setRecords(subVOList);
        return res;
    }

    @Override
    public R<PageRes<AfterServiceSubVO>> pageAfterAndSmallServiceRepair(WebAfterQueryReq req) {
        // 查询售后数据(sql按时间倒序)
        R<PageRes<AfterServiceSubVO>> afterR = this.pageAfterServiceRepair(req);
        // 查询小件数据(sql按时间倒序)
        R<PageRes<AfterServiceSubSmallBO>> smallR = this.pageAfterServiceRepairSmall(req);

        PageRes<AfterServiceSubVO> res = new PageRes<>(req.getCurrent(), req.getSize());
        R<PageRes<AfterServiceSubVO>> result = R.success(res);
        //设置分页需要携带的信息
        Map<String, Object> exData = result.getExData();
        if(exData == null){
            exData = new HashMap<>();
            result.setExData(exData);
        }
        if(afterR.getExData() != null){
            exData.putAll(afterR.getExData());
        }
        if(smallR.getExData() != null){
            exData.putAll(smallR.getExData());
        }
        List<AfterServiceSubVO> resultRecords = getAfterServiceSubs(req, afterR, smallR, result);
        res.setRecords(resultRecords);
        return result;
    }

    private @NotNull List<AfterServiceSubVO> getAfterServiceSubs(WebAfterQueryReq req, R<PageRes<AfterServiceSubVO>> afterR,
                                                                 R<PageRes<AfterServiceSubSmallBO>> smallR, R<PageRes<AfterServiceSubVO>> result) {
        // 获取列表数据
        List<AfterServiceSubVO> afterList;
        List<AfterServiceSubSmallBO> smallList;
        if(afterR.getData() != null && afterR.getData().getRecords() != null){
            afterList = afterR.getData().getRecords();
        }else{
            afterList = Collections.emptyList();
        }
        if(smallR.getData() != null && smallR.getData().getRecords() != null){
            smallList = smallR.getData().getRecords();
        }else{
            smallList = Collections.emptyList();
        }
        // 取时间较大前size条记录
        int ai = 0;
        int si = 0;
        List<AfterServiceSubVO> resultRecords = new LinkedList<>();
        // 分别从afterList和smallList里面获取数据, 每次取addTime和inDate比较, 取较大时间的数据, 直到取到size条数据为止
        while((ai < afterList.size() || si < smallList.size()) && resultRecords.size() < req.getSize()){
            AfterServiceSubVO after = null;
            AfterServiceSubSmallBO small = null;
            if(ai < afterList.size()){
                after = afterList.get(ai);
            }
            if(si < smallList.size()){
                small = smallList.get(si);
            }
           AfterServiceSubVO r;
            if (small == null || after != null && after.getAddTime().isAfter(small.getInDate())) {
                r = after;
                ai++;
            } else {
                // 时间相等的时候, 优先取小件商品
                r = commonStructMapper.toAfterServiceSubVO(small);
                r.setTroubleDesc(WebOrderWrapper.formatTroubleDesc(small.getProblem(), null, 1));
                r.setStateName(SmallProStatsEnum.getMessageByCode(small.getState()));
                List<Integer> cids = small.getProductList().stream().map(AfterServiceProductVO::getCid).collect(Collectors.toList());
                r.setPjKinds(getPJKinds(cids));
                si++;
            }
            resultRecords.add(r);
        }

        if(ai < afterList.size()){
            // 当前页数据没取完, 不能定位到新的状态码
            result.put(WebAfterQueryReq.AFTER_PAGE_TYPE_KEY, req.getAfterPageType());
        }
        if(si < smallList.size()){
            // 当前页数据没取完, 不能定位到新的状态码
            result.put(WebAfterQueryReq.SMALL_PAGE_TYPE_KEY, req.getSmallPageType());
        }

        result.put(WebAfterQueryReq.AFTER_START_OFFSET_KEY, req.getAfterStartOffset() + ai);
        result.put(WebAfterQueryReq.SMALL_START_OFFSET_KEY, req.getSmallStartOffset() + si);
        return resultRecords;
    }

    public String getPJKinds(List<Integer> cids) {
        List<Integer> tieMoCidList = SpringUtil.getBean(CategoryService.class).tieMoCids();
        List<Integer> tieMoCids = SpringUtil.getBean(CategoryService.class).selectCategoryChildrenByCid(tieMoCidList);
        if (CollUtil.isNotEmpty(tieMoCids) && CollUtil.isNotEmpty(cids)) {
            if (new HashSet<>(tieMoCids).containsAll(cids)) {
                return "KT";
            }
        }
        return "KX";
    }

    @Override
    public R<PageRes<AfterServiceSubSmallBO>> pageAfterServiceRepairSmall(WebAfterQueryReq req){
        PageRes<AfterServiceSubSmallBO> res = new PageRes<>(req.getCurrent(), req.getSize());
        res.setRecords(Collections.emptyList());
        if (ObjectUtil.defaultIfNull(req.getCurrent(), 1) == 1 && req.isNotTotal()) {
            //重置查询状态 首页的查询必须为 NO_TOTAL_ON_TIME_ORDER
            req.setSmallPageType(WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ON_TIME_ORDER.getCode());
            req.setSmallStartOffset(0);
        }
        Integer startRows = req.getStartRows("small");
        log.warn("小件单列表start：{}",startRows);
        R<PageRes<AfterServiceSubSmallBO>> result = R.success(res);
        //默认返回原值
        result.put(WebAfterQueryReq.SMALL_PAGE_TYPE_KEY,req.getSmallPageType());
        if(WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_NO_DATA.getCode().equals(req.getSmallPageType())){
            // 已经没有数据了, 不再进行查询
            return result;
        }
        List<AfterServiceSubSmallBO> list = new ArrayList<>();
        //小件没有 未处理 的状态
        if(!OrderTypeTag.AfterServiceRepair_Not_Repair.getCode().equals(req.getTagType())){
            list = webOrderMapper.listSmallProAfterServiceSubV2(req.getUserId(), req.getXtenant(), req.getTagType(),
                    startRows, req.getSize(), jiujiSystemProperties.getOfficeName(), req);
        }

        //没有记录了
        if (req.isNotTotal() && list.size() < req.getSize()) {
            result.put(WebAfterQueryReq.SMALL_PAGE_TYPE_KEY, WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_NO_DATA.getCode());
        }

        res.setRecords(list);

        return result;
    }

    @Override
    public R<PageRes<AfterServiceSubVO>> pageAfterServiceRepair(WebAfterQueryReq req) {
        PageRes<AfterServiceSubVO> res = new PageRes<>(req.getCurrent(), req.getSize());
        res.setRecords(Collections.emptyList());
        if (ObjectUtil.defaultIfNull(req.getCurrent(), 1) == 1 && req.isNotTotal()) {
            //重置history 的查询状态 首页的查询必须为 NO_TOTAL_ON_TIME_ORDER
            req.setAfterPageType(WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ON_TIME_ORDER.getCode());
            req.setAfterStartOffset(0);
            //首页重置历史标记位
            req.setAfterHisStartOffset(-1);
        }
        Integer startRows = req.getStartRows("after");
        log.warn("维修单列表start：{}", startRows);

        R<PageRes<AfterServiceSubVO>> result = R.success(res);
        //默认返回原值
        result.put(WebAfterQueryReq.AFTER_PAGE_TYPE_KEY,req.getAfterPageType());
         result.put(WebAfterQueryReq.AFTER_HIS_START_OFFSET_KEY,req.getAfterHisStartOffset());
        if(WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_NO_DATA.getCode().equals(req.getAfterPageType())){
            // 已经没有数据了, 不再进行查询
            return result;
        }

        List<AfterServiceSubBO> timeInfoList;
        if(!WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ONLY_HISTORY_ORDER.getCode().equals(req.getAfterPageType())){
            // 非仅查询历史库, 都要查询实时库
            timeInfoList = webOrderMapper.listAfterServiceSub(req.getUserId(), req.getXtenant(), req.getTagType(), startRows,
                    req.getSize(),req);
        }else{
            timeInfoList = Collections.emptyList();
        }

        List<AfterServiceSubBO> hisInfoList;
        if (XtenantEnum.isJiujiXtenant()
                // 排除不查询历史库的状态
                && !(WebOrderQueryReq.HistoryOrderEnum.ON_TIME_ORDER.getCode().equals(req.getAfterPageType())
                // 不计算总量实时库查询, 数量满页的时候, 无需查询历史库
                || WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ON_TIME_ORDER.getCode().equals(req.getAfterPageType())
                && timeInfoList.size() >= req.getSize())) {
            int hisStartRows = startRows;
            int hisSize = req.getSize();
            if (req.isNotTotal() && req.getAfterHisStartOffset() < 0) {
                req.setAfterHisStartOffset(startRows + timeInfoList.size());
                hisSize = req.getSize() - timeInfoList.size();
                //记录历史库的offset到结果集
                result.put(WebAfterQueryReq.AFTER_PAGE_TYPE_KEY, WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_ONLY_HISTORY_ORDER.getCode());
                result.put(WebAfterQueryReq.AFTER_HIS_START_OFFSET_KEY, req.getAfterHisStartOffset());
            }
            hisInfoList = webOrderMapper.listAfterServiceSubHis(req.getUserId(), req.getXtenant(), req.getTagType(), hisStartRows, hisSize, req);

        } else {
            hisInfoList = Collections.emptyList();
        }

        List<AfterServiceSubBO> list = Stream.concat(timeInfoList.stream(), hisInfoList.stream()).collect(Collectors.toList());

        //没有记录了
        if (req.isNotTotal() && list.size() < req.getSize()) {
            result.put(WebAfterQueryReq.AFTER_PAGE_TYPE_KEY, WebOrderQueryReq.HistoryOrderEnum.NO_TOTAL_NO_DATA.getCode());
        }
        if(CollUtil.isNotEmpty(list)) {
            List<Integer> shouhouIds = list.stream().map(AfterServiceSubBO::getSubId).collect(Collectors.toList());
            //查询是否评价
            List<AfterServiceSubPjBO> afterServiceSubPjList = webOrderMapper.listAfterSubPj(shouhouIds);
            Map<Integer, AfterServiceSubPjBO> subPjMap = afterServiceSubPjList.stream().collect(Collectors.toMap(AfterServiceSubPjBO::getSubId, Function.identity(), (v1, v2) -> v1));
            //查询预约单
            List<Integer> yuyueIds = list.stream().map(AfterServiceSubBO::getYuyueId).filter(Objects::nonNull).collect(Collectors.toList());
            Map<Integer, AfterServiceSubYuyueBO> yuyueMap = new HashMap<>();
            if (CollUtil.isNotEmpty(yuyueIds)) {
                List<AfterServiceSubYuyueBO> yuyueList = webOrderMapper.listAfterSubYuyue(yuyueIds);
                yuyueMap.putAll(yuyueList.stream().collect(Collectors.toMap(AfterServiceSubYuyueBO::getYuyueId, Function.identity(), (v1, v2) -> v1)));
            }
            list.forEach(sub -> {
                AfterServiceSubPjBO subPj = subPjMap.get(sub.getSubId());
                sub.setIspj(subPj != null);
                AfterServiceSubYuyueBO afterServiceSubYuyue = yuyueMap.get(sub.getYuyueId());
                if (afterServiceSubYuyue != null) {
                    sub.setYuyuePpids(afterServiceSubYuyue.getYuyuePPids());
                    sub.setStype(afterServiceSubYuyue.getStype());
                    sub.setKind(afterServiceSubYuyue.getKind());
                }
            });
        }

        Map<Integer, AreaInfo> areaInfoMap = areaInfoService.getAreaInfoMap();

        List<AfterServiceSubVO> subVOList = new ArrayList<>();
        for (AfterServiceSubBO subBO : list) {
            subVOList.add(WebOrderWrapper.toAfterServiceSubVO(subBO, areaInfoMap));
        }

        //批量查询优品信息
        List<Integer> newMachineBasketIds = subVOList.stream().filter(sv -> ProductTypeEnum.NEW_MACHINE.getCode().equals(sv.getProductType()))
                .map(AfterServiceSubVO::getBasketId).filter(basketId -> ObjectUtil.defaultIfNull(basketId, 0) > 0)
                .collect(Collectors.toList());
        if(!newMachineBasketIds.isEmpty()){
            List<Integer> youPingBasketIds = CommonUtils.bigDataInQuery(newMachineBasketIds, ids -> SpringUtil.getBean(BasketService.class)
                    .lambdaQuery().in(Basket::getBasketId, ids)
                    .eq(Basket::getType, BasketTypeEnum.BASKET_TYPE_DEFECT_MACHINE.getCode()).select(Basket::getBasketId)
                    .list()).stream().map(Basket::getBasketId).collect(Collectors.toList());
            youPingBasketIds.forEach(basketId -> subVOList.stream()
                    .filter(sv -> ObjectUtil.equals(basketId, sv.getBasketId()))
                    .forEach(sv -> sv.setProductType(ProductTypeEnum.EXCELLENT_PRODUCT.getCode())));
        }

        res.setRecords(subVOList);
        return result;
    }


    @Override
    public PageRes<ReservationSubVO> pageReservation(WebOrderQueryReq req) {
        PageRes<ReservationSubVO> res = new PageRes<>(req.getCurrent(), req.getSize());
        Integer startRows = (req.getCurrent() - 1) * req.getSize();
        log.info("start");
        req.setIsJiuJi(Convert.toInt(XtenantJudgeUtil.isJiujiMore()));
        Integer total = webOrderMapper.countReservation(req.getUserId(), req.getXtenant(),
                req.getTagType(), req.getKeyWord(), getSearchId(req.getKeyWord()), req);
        res.setTotal(total);
        if (startRows > total) {
            res.setRecords(new ArrayList<>());
            return res;
        }
        List<ReservationSubBO> list = webOrderMapper.listReservationSub(
                req.getUserId(), req.getXtenant(), req.getTagType(), req.getKeyWord(), getSearchId(req.getKeyWord()),
                startRows, req.getSize(), req);
        if (CollectionUtils.isEmpty(list)) {
            res.setRecords(new ArrayList<>());
            return res;
        }

        List<Integer> shIdList = list.stream().map(ReservationSubBO::getShId)
                .filter(tmp -> tmp != null && tmp > 0)
                .collect(Collectors.toList());
        List<Integer> shEva = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(shIdList)) {
            shEva = webOrderMapper.checkEvaluatedByType(shIdList, EvaluateTypeEnum.Shouhou,
                    jiujiSystemProperties.getOfficeName());
        }

        List<ReservationSubVO> subVOList = new ArrayList<>();
        for (ReservationSubBO subBO : list) {
            if(XtenantJudgeUtil.isJiujiMore()){
                subBO.setState(subBO.getJiuState());
            }
            subVOList.add(WebOrderWrapper.toReservationSubVO(subBO, shEva));
        }
        res.setRecords(subVOList);
        return res;
    }

    @Override
    public OrderCountVO getOrderCount(WebOrderCountReq req) {
        Map<String, Integer> countMap = new HashMap<>();
        Integer hideOrder = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.HIDE_ORDER_CONFIG))
                .filter(R::isSuccess).map(R::getData).filter(StrUtil::isNotBlank).map(Convert::toInt).orElse(0);
        for (String tagStr : req.getQueryTag()) {
            OrderTypeTag tag = null;
            for (OrderTypeTag value : OrderTypeTag.values()) {
                if (value.getCode().equals(tagStr)) {
                    tag = value;
                }
            }
            if (tag == null) {
                continue;
            }
            switch (tag.getOrderType()) {
                case Normal:
                    Integer count = webOrderMapper.countNormal(req.getUserId(), req.getXtenant(),
                            tag.getCode(), null, false, null, req.getDistribution(), null,req.getOrderLinkFlag(), null, hideOrder,req);
                    Integer countHis = webOrderMapper.countNormalHis(req.getUserId(), req.getXtenant(),
                            tag.getCode(), null, true, null, req.getDistribution(), null,req.getOrderLinkFlag(), null, hideOrder, req);
                    countMap.put(tag.getCode(), count + countHis);
                    break;
                case SecondHand:
                    countMap.put(tag.getCode(),
                            webOrderMapper.countLiangpin(req.getUserId(),null, req.getXtenant()
                                    , tag.getCode(), null, null, null, req));
                    break;
                case Recover:
                    countMap.put(tag.getCode(),
                            webOrderMapper.countRecover(req.getUserId(), req.getXtenant()
                                    , tag.getCode(), null, null, null, req));
                    break;
                case AfterServiceRepair:
                    LambdaCaseWhen<Boolean, Integer> caseWhen = LambdaCaseWhen.lambdaCase(Objects.equals(req.getKind(), NumberConstant.ONE));
                    OrderTypeTag finalTag = tag;
                    Integer afterCount = caseWhen.when(Boolean.TRUE,() ->{
                                Integer total = Optional.ofNullable(webOrderMapper.countAfterService(req.getUserId(), req.getXtenant(), finalTag.getCode(), req)).orElse(NumberConstant.ZERO);
                                if(XtenantJudgeUtil.isJiujiMore()){
                                    Integer totalHis = Optional.ofNullable(webOrderMapper.countAfterServiceHis(req.getUserId(), req.getXtenant(), finalTag.getCode(), req)).orElse(NumberConstant.ZERO);
                                    return total+totalHis;
                                } else {
                                    return total;
                                }
                            })
                            .endElse(() -> webOrderMapper.countSmallProAfterService(req.getUserId(), req.getXtenant(), finalTag.getCode(), jiujiSystemProperties.getOfficeName()));
                    countMap.put(tag.getCode(),afterCount);
                    break;
                case AfterServiceReservation:
                    req.setIsJiuJi(Convert.toInt(XtenantJudgeUtil.isJiujiMore()));
                    countMap.put(tag.getCode(),
                            webOrderMapper.countReservation(req.getUserId(), req.getXtenant()
                                    , tag.getCode(), null, null, req));
                    break;
                default:
            }
        }
        OrderCountVO vo = new OrderCountVO();
        vo.setCountMap(countMap);
        return vo;
    }

    @Override
    public List<OrderStatusCheckVO> checkOrderStatusNormal(WebOrderCheckReq req) {
        List<XinjiSubBO> bos = webOrderMapper.listNormalStatusBySubId(req.getSubIdList());
        if (bos.size() < req.getSubIdList().size()) {
            List<XinjiSubBO> bosHis = webOrderMapper.listNormalStatusBySubIdHis(req.getSubIdList());
            if (CollectionUtils.isNotEmpty(bosHis)) {
                bos.addAll(bosHis);
            }
        }
        if (CollectionUtils.isEmpty(bos)) {
            return new ArrayList<>();
        }
        List<Integer> realSubId = bos.stream().map(XinjiSubBO::getSubId).collect(Collectors.toList());
        List<Integer> delSubIdList = webOrderMapper.listDelCollectBySubIdAndType(realSubId, 1);

        List<OrderStatusCheckVO> checkVOS = new ArrayList<>();
        for (XinjiSubBO bo : bos) {
            OrderStatusCheckVO vo = new OrderStatusCheckVO();
            vo.setSubId(bo.getSubId())
                    .setSubCheck(bo.getSubCheck())
                    .setSubCheckName(com.jiuji.oa.oacore.common.util.EnumUtil.getMessageByCode(SubCheckEnum.class, bo.getSubCheck()))
                    .setIsDel(CollectionUtils.isNotEmpty(delSubIdList) && delSubIdList.contains(bo.getSubId()))
                    .setIsPay(bo.getYingfuM() != null && bo.getYifuM() != null && bo.getYingfuM().compareTo(bo.getYifuM()) == 0);
            checkVOS.add(vo);
        }
        return checkVOS;
    }

    private Boolean checkException(String key, String subId) {
        return stringRedisTemplate.opsForHash().hasKey(key, subId);
    }

    private Integer getSearchId(String keyWord) {
        if (StringUtils.isBlank(keyWord)) {
            return null;
        }
        if (!StringUtils.isNumeric(keyWord)) {
            return null;
        }
        if (!NumberUtil.isInteger(keyWord)) {
            return null;
        }
        return Integer.valueOf(keyWord);
    }

    @Override
    public Map<Integer, List<Ch999UserServiceVO>> getExclusiveCustomerService(List<Integer> subIds, Integer count) {
        if (CollectionUtils.isEmpty(subIds)) {
            return new HashMap<>();
        }

        List<Ch999UserServiceVO> exclusiveCustomerServices = webOrderMapper.listExclusiveCustomerService(subIds, jiujiSystemProperties.getOfficeName());
        if (CollectionUtils.isEmpty(exclusiveCustomerServices)) {
            return new HashMap<>();
        }
        return filterExclusiveCustomerService(exclusiveCustomerServices,count);
    }

    @Override
    public Map<Integer, List<Ch999UserServiceVO>> getLiangPinExclusiveCustomerService(Collection<Integer> subIds, Integer count) {
        if (CollectionUtils.isEmpty(subIds)) {
            return new HashMap<>();
        }

        List<Ch999UserServiceVO> exclusiveCustomerServices = webOrderMapper.listLiangPinExclusiveCustomerService(subIds, jiujiSystemProperties.getOfficeName());
        if (CollectionUtils.isEmpty(exclusiveCustomerServices)) {
            return new HashMap<>();
        }
        return filterExclusiveCustomerService(exclusiveCustomerServices,count);
    }


    private Map<Integer, List<Ch999UserServiceVO>> filterExclusiveCustomerService(List<Ch999UserServiceVO> exclusiveCustomerServices, Integer count) {

        Map<Integer, Ch999UserVo> ch999UserMap = new HashMap<>();
        List<Integer> ch999Ids = exclusiveCustomerServices.stream().map(Ch999UserServiceVO::getCh999Id).collect(Collectors.toList());
        R<List<Ch999UserVo>> ch999UsersR = userInfoClient.listCh999UserInfo(ch999Ids);
        if (ch999UsersR.getCode() == ResultCode.SUCCESS) {
            List<Ch999UserVo> ch999Users = ch999UsersR.getData();
            ch999UserMap = ch999Users.stream().collect(
                    Collectors.toMap(Ch999UserVo::getCh999Id, Function.identity(), (v1, v2) -> v1));
        }

        Map<Integer, Ch999UserVo> finalCh999UserMap = ch999UserMap;
        Map<Integer, List<Ch999UserServiceVO>> exclusiveCustomerServicesMap = exclusiveCustomerServices.stream()
                .peek(item -> {
                    Ch999UserVo ch999UserVo = finalCh999UserMap.getOrDefault(item.getCh999Id(), new Ch999UserVo());
                    String ch999Name = numberPattern.matcher(
                            Optional.ofNullable(ch999UserVo.getCh999Name()).orElse("")).replaceAll("");
                    item.setCh999Id(ch999UserVo.getCh999Id()).setAvatar(ch999UserVo.getAvatar()).setCh999Name(ch999Name)
                            .setJobName(com.jiuji.tc.utils.enums.EnumUtil.getMessageByCode(EvaluateJobEnum.class, item.getJob()));
                })
                .filter(item -> item.getCh999Id() != null)
                .filter(distinctByKey(item -> item.getSubId() + item.getCh999Name() + item.getJobName()))
                .collect(Collectors.groupingBy(Ch999UserServiceVO::getSubId));
        for (Map.Entry<Integer, List<Ch999UserServiceVO>> m : exclusiveCustomerServicesMap.entrySet()) {
            if (m.getValue().size() > count) {
                exclusiveCustomerServicesMap.put(m.getKey(), m.getValue().subList(0, count));
            }
        }
        return exclusiveCustomerServicesMap;
    }

    @Override
    public List<Ch999UserServiceVO> listExclusiveCustomerServiceByUserId(Integer userId, int count, Integer xtenant) {
        List<Ch999UserServiceVO> ch999UserServiceVOS = listExclusiveCustomerServiceByUserId(userId, count, xtenant, 1, new ArrayList<>());
        Map<Integer, Ch999UserVo> ch999UserMap = new HashMap<>();
        List<Integer> ch999Ids = ch999UserServiceVOS.stream().map(Ch999UserServiceVO::getCh999Id).collect(Collectors.toList());
        R<List<Ch999UserVo>> ch999UsersR = userInfoClient.listCh999UserInfo(ch999Ids);
        if (ch999UsersR.getCode() == ResultCode.SUCCESS) {
            List<Ch999UserVo> ch999Users = ch999UsersR.getData();
            ch999UserMap = ch999Users.stream().collect(
                    Collectors.toMap(Ch999UserVo::getCh999Id, Function.identity(), (v1, v2) -> v1));
        }
        if (CollectionUtils.isNotEmpty(ch999UserServiceVOS)) {
            Map<Integer, Ch999UserVo> finalCh999UserMap = ch999UserMap;
            ch999UserServiceVOS = ch999UserServiceVOS.stream()
                    .sorted(Comparator.comparingInt(Ch999UserServiceVO::getSubId).reversed())
                    .peek(item -> {
                        Ch999UserVo ch999UserVo = finalCh999UserMap.getOrDefault(item.getCh999Id(), new Ch999UserVo());
                        String ch999Name = numberPattern.matcher(
                                Optional.ofNullable(ch999UserVo.getCh999Name()).orElse("")).replaceAll("");
                        item.setCh999Id(ch999UserVo.getCh999Id())
                                .setAvatar(ch999UserVo.getAvatar())
                                .setCh999Name(ch999Name)
                                .setJobName(com.jiuji.tc.utils.enums.EnumUtil.getMessageByCode(EvaluateJobEnum.class, item.getJob()));
                    })
                    .filter(item -> item.getCh999Id() != null)
                    .collect(Collectors.toList());
        }
        return ch999UserServiceVOS;
    }

    @Override
    public List<Ch999UserServiceVO> listExclusiveCustomerServiceByUserId(Integer userId, int count, Integer xtenant, long current, List<Ch999UserServiceVO> list) {
        // 每次查询50个订单
        int size = 50;
        Integer total = webOrderMapper.countExclusiveCustomerOrder(userId, xtenant, jiujiSystemProperties.getOfficeName());
        List<Integer> subIds = webOrderMapper.listExclusiveCustomerOrder(userId, xtenant, jiujiSystemProperties.getOfficeName(), current, size);
        // 查询完所有订单或查询够指定的专属客服数量时返回
        if (current * size > (total + size) || list.size() >= count) {
            if (list.size() > count) {
                return list.subList(0, count);
            }
            return list;
        }
        if (CollectionUtils.isEmpty(subIds)) {
            return new ArrayList<>();
        }
        List<Ch999UserServiceVO> exclusiveCustomerServices = webOrderMapper.listExclusiveCustomerService(subIds, jiujiSystemProperties.getOfficeName());
        if (CollectionUtils.isNotEmpty(exclusiveCustomerServices)) {
            list.addAll(exclusiveCustomerServices);
        }
        list = list.stream().filter(distinctByKey(Ch999UserServiceVO::getCh999Id)).collect(Collectors.toList());
        current++;
        return listExclusiveCustomerServiceByUserId(userId, count, xtenant, current, list);
    }

    private <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    @Override
    public int countNotEvaluatedOrder(Integer userId) {
        return webOrderMapper.countNotEvaluatedOrder(userId, jiujiSystemProperties.getOfficeName());
    }

    @Override
    public List<SaleOrderVO> getSaleOrders(List<Integer> productIds, Double pay) {
        if (CollectionUtils.isEmpty(productIds)) {
            return new ArrayList<>();
        }
        return webOrderMapper.getSaleOrders(productIds, pay);
    }

    @Override
    public List<SaleOrderVO> getSaleOrdersV2(List<Integer> productIds, Double pay, String subDate) {

        if (CollectionUtils.isEmpty(productIds)) {
            return new ArrayList<>();
        }
        return webOrderMapper.getSaleOrdersV2(productIds, pay, subDate);
    }

    @Override
    public List<SaleOrderKcGroupInfoVO> getSaleOrderKcGroupInfo(List<Integer> basketIds, Integer level, Integer wxBind) {
        if (CollectionUtils.isEmpty(basketIds)) {
            return new ArrayList<>();
        }

        List<SaleOrderKcGroupInfoVO> resultList = new ArrayList<>();
        // basketId 分组
        List<List<Integer>> basketIdListList = ListUtils.partition(basketIds, 500);
        for (List<Integer> basketIdList : basketIdListList) {
            List<SaleOrderKcGroupInfoVO> saleOrderKcGroupInfo = webOrderMapper.getSaleOrderKcGroupInfo(basketIdList, level, wxBind);
            resultList.addAll(saleOrderKcGroupInfo);
        }
        return resultList;
    }

    @Override
    public InventoryPresetVO getInventoryPreset(Integer basketId) {
        return webOrderMapper.getInventoryPreset(basketId);
    }

    @Override
    @DS("smallpro_write")
    @Transactional(rollbackFor = Exception.class)
    public R<String> changeSubPpriceid(Integer userId, Integer basketId, Integer ppriceid, BigDecimal price) {
        List<ProductInfoBO> list = webOrderMapper.listProductInfoByBasketId(basketId, userId);
        if (CollectionUtils.isEmpty(list)) {
            return R.error("无效订单！");
        }
        ProductInfoBO productInfoBO = list.get(0);
        String preSaleProductIdsStr = stringRedisTemplate.opsForValue().get("PreSaleProductIds");
        List<Integer> preSaleProductIds = new ArrayList<>();
        if (StringUtils.isNotBlank(preSaleProductIdsStr)) {
            preSaleProductIds = Arrays.stream(preSaleProductIdsStr.split(",")).map(item -> {
                try {
                    return Integer.valueOf(item);
                } catch (NumberFormatException e) {
                    log.error("productId转换异常，productId：{}", item, e);
                    return 0;
                }
            }).filter(id -> !id.equals(0)).collect(Collectors.toList());
            List<Integer> productIds = list.stream().map(ProductInfoBO::getProductId).collect(Collectors.toList());
            if (!preSaleProductIds.containsAll(productIds)) {
                return R.error("此商品不能更换！");
            }
        }
        if (price == null || price.compareTo(BigDecimal.ZERO) < 1) {
            return R.error("无效金额！");
        }
        ProductInfoBO newProductInfo = webOrderMapper.getProductInfo(ppriceid);
        if (newProductInfo == null) {
            return R.error("无效商品：" + ppriceid);
        }
        if (!preSaleProductIds.contains(newProductInfo.getProductId())) {
            return R.error("此商品不能更换！");
        }
        if (productInfoBO.getPpriceid().equals(ppriceid)) {
            return R.error("商品一致无需更换！");
        }

        try {
            Basket basket = basketService.getById(basketId);
            basket.setPrice(price).setPrice1(price).setPpriceid(Long.valueOf(ppriceid));
            basketService.updateBasket(basket);

            List<Integer> mkcIds = list.stream()
                    .filter(item -> !Integer.valueOf(0).equals(item.getMkcId())).map(ProductInfoBO::getMkcId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(mkcIds)) {
                Collection<ProductMkc> productMkcs = productMkcService.listByIds(mkcIds);
                List<Integer> cashTransferIds = Arrays.asList(2, 3, 10);
                List<Integer> cancelIds = Arrays.asList(0, 4, 7);
                productMkcs.forEach(productMkc -> {
                    Integer kcCheck = productMkc.getKcCheck();
                    // 转现
                    if (cashTransferIds.contains(kcCheck)) {
                        MkcLogsNewDocument mkcLog = new MkcLogsNewDocument();
                        MkcLogsNewDocument.Conts conts = new MkcLogsNewDocument.Conts();
                        conts.setComment("客户更换ppriceid，转现操作，转来自：<a href='/addOrder/editOrder?basketid=" + productMkc.getBasketId() + "&showDel=1' target='_blank' >" + productMkc.getBasketId() + "</a>")
                                .setDTime(LocalDateTime.now())
                                .setInUser("系统")
                                .setType(true);
                        mkcLog.setMkcId(Long.valueOf(productMkc.getId())).setCons(Collections.singletonList(conts));
                        mkcLogsNewService.saveMkcLogsNew(mkcLog);
                        productMkc.setBasketId(null);
                    } else if (cancelIds.contains(kcCheck)) {
                        // 取消备货
                        productMkc.setKcCheck(4).setBasketId(null);
                        MkcLogsNewDocument mkcLog = new MkcLogsNewDocument();
                        MkcLogsNewDocument.Conts conts = new MkcLogsNewDocument.Conts();
                        conts.setComment("客户更换ppriceid，删除操作！")
                                .setDTime(LocalDateTime.now())
                                .setInUser("系统")
                                .setType(false);
                        mkcLog.setMkcId(Long.valueOf(productMkc.getId())).setCons(Collections.singletonList(conts));
                        mkcLogsNewService.saveMkcLogsNew(mkcLog);
                    }
                });
                productMkcService.updateProductMkcs(productMkcs);
            }

            // 提交未备货
            productMkcService.productMkc(ppriceid, "系统", 0, "", productInfoBO.getAreaId(),
                    basketId, productInfoBO.getBasketCount(), productInfoBO.getCostPrice(), null, null,
                    null, null, null, null, null);

            String comment = "客户自主修改订单规格版本，商品由" + productInfoBO.getProductName() + " "
                    + productInfoBO.getProductColor() + "修改为"
                    + newProductInfo.getProductName() + " " + newProductInfo.getProductColor();
            SubLogsNewDocument subLogsNewDocument = new SubLogsNewDocument();
            SubLogsNewDocument.Conts conts = new SubLogsNewDocument.Conts();
            conts.setComment(comment).setDTime(LocalDateTime.now()).setInUser("客户").setShowType(true).setType(1);
            subLogsNewDocument.setConts(Collections.singletonList(conts));
            subLogsNewDocument.setId(Long.valueOf(productInfoBO.getSubId()));
            subLogsNewLogService.saveSubLogsNewLog(subLogsNewDocument);

            // 修改订单金额
            ((WebOrderService) AopContext.currentProxy()).updateOrderPrice(productInfoBO.getSubId());
        } catch (Exception e) {
            log.error("更换ppriceid出错，", e);
        } finally {
            subService.updateNetSub(productInfoBO.getSubId());
        }
        return R.success("更换成功！");
    }

    @Override
    public void updateOrderPrice(Integer subId) {
        BigDecimal price = webOrderMapper.getOrderPrice(subId);
        Sub sub = subService.getById(subId);
        List<Integer> status = Arrays.asList(0, 1, 2, 4, 5, 6);
        if (sub != null && status.contains(sub.getSubCheck())) {
            sub.setYingfuM(price);
            subService.updateSub(sub);
        }
    }

    @Override
    public List<ImOrderVO> getImNewOrders(List<Integer> subIds, Integer userId) {
        List<ImOrderVO> result = webOrderMapper.getImNewOrders(subIds, userId);
        return null;
    }

    @Override
    public List<ImOrderVO> getImAfterServiceOrders(List<Integer> subIds, Integer userId) {
        return null;
    }

    @Override
    public List<ImOrderVO> getImRecoverOrders(List<Integer> subIds, Integer userId) {
        return null;
    }

    @Override
    public List<ImOrderVO> getImLiangPinOrders(List<Integer> subIds, Integer userId) {
        return null;
    }

    @Override
    public List<ImOrderVO> listOrderProductInfo(String type, List<Integer> subIds, Integer userId, Integer kind) {
        List<ImOrderVO> result = new ArrayList<>();
        Map<Integer, ImOrderVO> resultMap;
        String typeName = null;
        String url;
        try {
            R<String> wcfR = sysConfigClient.getValueByCode(SysConfigConstant.M_URL);
            if (wcfR.getCode() != ResultCode.SUCCESS || com.baomidou.mybatisplus.core.toolkit.StringUtils.isEmpty(wcfR.getData())) {
                String message = "获取M_URL域名失败结果为"+JSONUtil.toJsonStr(wcfR);
                smsService.sendOaMsgTo9Ji(message, "13495", OaMesTypeEnum.YCTZ.getCode().toString());
                url = "https://m.9ji.com";
            } else {
                url = wcfR.getData();
            }

        }catch (Exception e){
            url="https://m.9ji.com";
            log.error("获取M_URL域名结果异常",e);
            smsService.sendOaMsgTo9Ji("获取M_URL域名结果异常"+e.getMessage(), "13495", OaMesTypeEnum.YCTZ.getCode().toString());
        }

        switch (type) {
            case "mine":
                // 新机
                typeName = "新机";
                result = webOrderMapper.listMineProductInfoBySubIdAndUserId(subIds, userId);
                break;
            case "repair":
                // 维修（售后）
                typeName = "维修单";
                result = webOrderMapper.listRepairProductInfoBySubIdAndUserId(subIds, userId, NumberConstant.ONE);
                break;
            case "repairSmall":
                // 维修（小件）
                typeName = "小件接件单";
                result = webOrderMapper.listRepairProductInfoBySubIdAndUserId(subIds, userId, NumberConstant.TWO);
                break;
            case "recover":
                // 回收
                typeName = "回收";
                result = webOrderMapper.listRecoverProductInfoBySubIdAndUserId(subIds, userId);
                break;
            case "secondhand":
                // 良品
                typeName = "良品";
                result = webOrderMapper.listSecondHandProductInfoBySubIdAndUserId(subIds, userId);
                //查询良品主图
                if (XtenantJudgeUtil.isJiujiMore() && CollUtil.isNotEmpty(result)) {
                    List<Integer> mkcIds = result.stream().filter(v -> StrUtil.isBlank(v.getImagePath())).map(ImOrderVO::getMkcId).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(mkcIds)) {
                        List<SalfGoodsNewMpicsRes> newMpicsList = SpringUtil.getBean(UsedGoodsService.class).getNewMpicsByMkcIds(mkcIds);
                        Map<Integer, String> newMpicsMap = newMpicsList.stream().collect(Collectors.toMap(SalfGoodsNewMpicsRes::getMkcId, SalfGoodsNewMpicsRes::getNewMpics, (v1, v2) -> v1));
                        result.forEach(v -> {
                            String newMpics = newMpicsMap.get(v.getMkcId());
                            if (StrUtil.isNotBlank(newMpics)) {
                                v.setImagePath(StrUtil.subBefore(newMpics, ",", false));
                            }
                        });
                    }
                }
                break;
            case "rent":
                // 租机
                typeName = "租机";
                if(XtenantJudgeUtil.isJiujiMore()){
                    ImOrderVO imOrderVO = new ImOrderVO();
                    imOrderVO.setPrice(BigDecimal.ZERO);
                    imOrderVO.setImagePath("https://img2.ch999img.com/newstatic/2372/032e104ed693b13d.png");
                    imOrderVO.setName("xxxxxx");
                    if(CollectionUtils.isNotEmpty(subIds)){
                        imOrderVO.setId(subIds.get(0));
                    }
                    result.add(imOrderVO);
                } else {
                    R<List<RentOrderVO>> rendSub = rentCloud.getRentSub(Optional.ofNullable(userId).orElseThrow(() -> new CustomizeException("userId不能为空")).toString());
                    if (rendSub.getCode() == ResultCode.SUCCESS && CollectionUtils.isNotEmpty(rendSub.getData())) {
                        List<RentOrderVO> dataList = rendSub.getData();
                        if (CollectionUtils.isNotEmpty(subIds)) {
                            dataList.removeIf(data -> !subIds.contains(Math.toIntExact(data.getSub().getSubId())));
                        }
                        for (RentOrderVO item : dataList) {
                            ImOrderVO imOrderVO = new ImOrderVO();
                            imOrderVO.setId((int) item.getSub().getSubId());
                            imOrderVO.setPrice(BigDecimal.valueOf(item.getSub().getPayablePrice()));
                            imOrderVO.setTime(item.getSub().getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                            imOrderVO.createRentUrl(imOrderVO.getId(),url);
                            if (CollUtil.isNotEmpty(item.getProductList())) {
                                imOrderVO.setImagePath(item.getProductList().get(0).getPicUrl());
                                imOrderVO.setName(item.getProductList().get(0).getProductName());
                            }
                            result.add(imOrderVO);
                        }
                    }
                }


                break;
            case "logistics":
                url = getOaurl();
                typeName = "物流单";
                //查询物流单订货调拨
                List<WuliuDiaoboSubBO> wuliuDiaoboSubList = webOrderMapper.listWuliuDiaoboBySubId(subIds);
                Map<Integer, List<WuliuDiaoboSubBO>> wuliuDiaoboSubMap = wuliuDiaoboSubList.stream().collect(Collectors.groupingBy(WuliuDiaoboSubBO::getWuliuId));
                if (CollUtil.isNotEmpty(wuliuDiaoboSubMap)) {
                    Map<Integer, AreaInfo> areaInfoMap = areaInfoService.getAreaInfoMap();
                    for (Map.Entry<Integer, List<WuliuDiaoboSubBO>> entry : wuliuDiaoboSubMap.entrySet()) {
                        List<WuliuDiaoboSubBO> WuliuDiaoboList = entry.getValue();
                        Optional<WuliuDiaoboSubBO> diaoboSub = WuliuDiaoboList.stream().findFirst();
                        if (diaoboSub.isPresent()) {
                            WuliuDiaoboSubBO wuliuDiaoboSub = diaoboSub.get();

                            String diaoboId = WuliuDiaoboList.stream().map(v -> "0".equals(v.getDiaoboId()) ? "" : v.getDiaoboId())
                                    .filter(StringUtils::isNotBlank)
                                    .collect(Collectors.joining(","));
                            String orderId = WuliuDiaoboList.stream().map(v -> "0".equals(v.getOrderId()) ? "" : v.getOrderId())
                                    .filter(StringUtils::isNotBlank)
                                    .collect(Collectors.joining(","));
                            ImOrderVO imOrderVO = new ImOrderVO();
                            imOrderVO.setId(wuliuDiaoboSub.getWuliuId());
                            imOrderVO.setPrice(BigDecimal.ZERO);
                            imOrderVO.setTime(wuliuDiaoboSub.getDtime());
                            imOrderVO.createLogisticsUrl(imOrderVO.getId(),url);
                            //物流单默认写死图片
                            imOrderVO.setImagePath("51575/0d4efdee8f535a49.png");
                            LogisticsTransferInfoVO transferInfo = LambdaBuild.create(LogisticsTransferInfoVO.class)
                                    .set(LogisticsTransferInfoVO::setOrderId, orderId)
                                    .set(LogisticsTransferInfoVO::setTransferId, diaoboId)
                                    .set(LogisticsTransferInfoVO::setSendArea, Optional.ofNullable(areaInfoMap.get(wuliuDiaoboSub.getSendAreaId())).map(AreaInfo::getArea).orElse(""))
                                    .set(LogisticsTransferInfoVO::setRecieveArea, Optional.ofNullable(areaInfoMap.get(wuliuDiaoboSub.getReceiveAreaId())).map(AreaInfo::getArea).orElse(""))
                                    .build();
                            imOrderVO.setLogisticsInfo(transferInfo);
                            result.add(imOrderVO);
                        }
                    }
                }
            case "diaobo":
                url = getOaurl();
                typeName = "调拨单";
                List<DiaoboSubBO> diaoboSubList = webOrderMapper.listDiaoboBySubId(subIds);
                Map<Integer, List<DiaoboSubBO>> diaoboSubSubMap = diaoboSubList.stream().collect(Collectors.groupingBy(DiaoboSubBO::getSubId));
                if (CollUtil.isNotEmpty(diaoboSubSubMap)) {
                    Map<Integer, AreaInfo> areaInfoMap = areaInfoService.getAreaInfoMap();
                    for (Map.Entry<Integer, List<DiaoboSubBO>> entry : diaoboSubSubMap.entrySet()) {
                        List<DiaoboSubBO> diaoboList = entry.getValue();
                        Optional<DiaoboSubBO> diaoboSub = diaoboList.stream().findFirst();
                        if (diaoboSub.isPresent()) {
                            DiaoboSubBO wuliuDiaoboSub = diaoboSub.get();
                            ImOrderVO imOrderVO = new ImOrderVO();
                            imOrderVO.setId(wuliuDiaoboSub.getSubId());
                            imOrderVO.setPrice(BigDecimal.ZERO);
                            imOrderVO.setTime(wuliuDiaoboSub.getDtime());
                            imOrderVO.createDiaoboUrl(imOrderVO.getId(),url);
                            //默认写死图片
                            imOrderVO.setImagePath("57203/1590e799b36f8491.png");
                            DiaoboInfoVO diaoboInfo = LambdaBuild.create(DiaoboInfoVO.class)
                                    .set(DiaoboInfoVO::setTransferId, wuliuDiaoboSub.getSubId())
                                    .set(DiaoboInfoVO::setTitle, wuliuDiaoboSub.getTitle())
                                    .set(DiaoboInfoVO::setSendArea, Optional.ofNullable(areaInfoMap.get(wuliuDiaoboSub.getFromAreaId())).map(AreaInfo::getArea).orElse(""))
                                    .set(DiaoboInfoVO::setRecieveArea, Optional.ofNullable(areaInfoMap.get(wuliuDiaoboSub.getToAreaId())).map(AreaInfo::getArea).orElse(""))
                                    .build();
                            imOrderVO.setDiaoboInfo(diaoboInfo);
                            result.add(imOrderVO);
                        }
                    }
                }
                break;
            default:
                break;
        }
        // subId通过转map去重，保留商品价格最高的记录
        resultMap = result.stream().collect(Collectors.toMap(ImOrderVO::getId, Function.identity(),
                (key1, key2) -> key1.getMemberPrice().compareTo(key2.getMemberPrice()) > 0 ? key1 : key2));
        result = new ArrayList<>(resultMap.values());
        setOrderProductImg(type, result,url);
        for (ImOrderVO item : result) {
            // 设置订单类型
            ImOrderVO.OrderName order = new ImOrderVO.OrderName(type, typeName);
            item.setOderName(order);
            // 租机接口在主站 图片url不用再处理
            if (!"rent".equals(type)) {
//                item.setImagePath(imgPrefix + item.getImagePath());
                item.setImagePath(this.getProductImgUrl(item.getImagePath()));
            }
        }
        //处理回收获取不到商品图片
        if (XtenantJudgeUtil.isJiujiMore() && "recover".equals(type)) {
            List<Integer> goodsIds = result.stream().filter(v -> StrUtil.isBlank(v.getImagePath())).map(ImOrderVO::getGoodsId).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(goodsIds)) {
                List<SalfGoodsNewMpicsRes> newMpicsList = SpringUtil.getBean(UsedGoodsService.class).getRecyclePjtGoodsByGoodsIds(goodsIds);
                Map<Integer, String> newMpicsMap = newMpicsList.stream().collect(Collectors.toMap(SalfGoodsNewMpicsRes::getMkcId, SalfGoodsNewMpicsRes::getNewMpics, (v1, v2) -> v1));
                result.forEach(v -> {
                    String newMpics = newMpicsMap.get(v.getGoodsId());
                    if (StrUtil.isNotBlank(newMpics)) {
                        v.setImagePath(newMpics);
                    }
                });
            }
        }
        return result;
    }

    /**
     * 获取oa域名
     * @return
     */
    private String getOaurl() {
        String oaurl;
        try {
            R<String> wcfR = sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL);
            if (wcfR.getCode() != ResultCode.SUCCESS || com.baomidou.mybatisplus.core.toolkit.StringUtils.isEmpty(wcfR.getData())) {
                String message = "获取M_URL域名失败结果为"+JSONUtil.toJsonStr(wcfR);
                smsService.sendOaMsgTo9Ji(message, "13495", OaMesTypeEnum.YCTZ.getCode().toString());
                oaurl = "https://moa.9ji.com";
            } else {
                oaurl = wcfR.getData();
            }

        }catch (Exception e){
            oaurl="https://moa.9ji.com";
            log.error("获取M_URL域名结果异常",e);
            smsService.sendOaMsgTo9Ji("获取M_URL域名结果异常"+e.getMessage(), "13495", OaMesTypeEnum.YCTZ.getCode().toString());
        }
        return oaurl;
    }


    /**
     * 根据订单类型设置订单信息详情的链接
     *
     * @param type   订单类型
     * @param result 订单信息详情列表
     */
    public void setOrderProductImg(String type, List<ImOrderVO> result,String url) {
        if (result != null) {
            for (ImOrderVO item : result) {
                switch (type) {
                    case "mine":
                        // 新机
                        item.createNewUrl(item.getId(),url);
                        break;
                    case "repair":
                        // 维修（售后）
                        item.createMainRepairUrl(item.getId(),url);
                        break;
                    case "repairSmall":
                        // 售后小件
                        item.createRepairSmallUrl(item.getId(),url);
                        break;
                    case "recover":
                        // 回收
                        item.createRecoverUrl(item.getId(),url);
                        break;
                    case "secondhand":
                        // 良品
                        item.createSecondUrl(item.getId(),url);
                        break;
                    case "rent":
                        if(XtenantJudgeUtil.isJiujiMore()){
                            item.setLink("");
                        } else {
                            item.createRentUrl(item.getId(),url);
                        }

                        break;
                    case "logistics":
                        item.createLogisticsUrl(item.getId(),url);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    private String getProductImgUrl(String picUrl) {

        if (StringUtils.isEmpty(picUrl)) {
            return null;
        }
        picUrl = StringUtils.stripEnd(picUrl, "/");
        String pa = "^\\d{1,9}/.*";
        Pattern pattern = Pattern.compile(pa);
        String url = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.IMG_URL, userInfoService.getCurrentXtenant()).getData();
        String baseUrl = url;
        if (pattern.matcher(picUrl).matches()) {
            url += "/newstatic/" + picUrl;
        } else {
            url += "/pic/product/440x440/" + picUrl;
        }
        if (StrUtil.startWith(picUrl,"newstatic")) {
            url = baseUrl + "/" + picUrl;
        }
        return url;
    }

    /**
     * 根据传入号码和租户编号，品牌id，时间段，查询当前用户是否存在购买记录
     * @param whetherToBuyReq 号码和租户编号，品牌id，时间段
     * @return
     */
    @Override
    public List<WhetherToBuyReq.Res> getWhetherToBuy(WhetherToBuyReq whetherToBuyReq) {
        return Optional.ofNullable(whetherToBuyReq.getUserIds())
                .filter(CollectionUtil::isNotEmpty)
                .map(userIds->{
                    List<WhetherToBuyReq.Res> whetherToBuy = webOrderMapper.getWhetherToBuy(whetherToBuyReq);
                    return userIds.stream().map(userId->new WhetherToBuyReq.Res().setUserId(userId)
                            .setBuy(whetherToBuy.stream().anyMatch(wb->wb.getUserId().equals(userId))))
                            .collect(Collectors.toList());
                }).orElse(Collections.emptyList());
    }

    /**
     * 根据时间和会员id查询 对应的状态
     * @param myClientReq myClientReq
     * @param flag 表示 false为未完成 true为已完成
     * @return
     */
    @Override
    public List<Integer> getMyClientComplete(MyClientReq myClientReq,Boolean flag) {
        //当会员id为空，返回空数组
        if (myClientReq.getUserId().isEmpty()){
            return new ArrayList<>();
        }

        //一次in 50条数据
        List<Integer> userIdList = myClientReq.getUserId();
        List<Integer> myClientNotComplete = CommonUtils.bigDataInQuery(NumberConstant.FIFTY,userIdList,ids ->{
            MyClientReq myClientReq1 = new MyClientReq();
            BeanUtils.copyProperties(myClientReq, myClientReq1);
            myClientReq1.setUserId(ids);
            return webOrderMapper.getMyClientComplete(myClientReq1,flag);
        });
        myClientReq.setUserId(userIdList);
        return myClientNotComplete;
    }


    /**
     * 商品ID （commodityId），有购买完成的客户（根据时间筛选）
     * @param myClientReq myClientReq
     * @return
     */
    @Override
    public List<Integer> getMyClientPurchaseComplete(MyClientReq myClientReq) {
        //当会员id为空，返回空数组
        if (myClientReq.getUserId().isEmpty()){
            return new ArrayList<>();
        }

        //一次in 50条数据
        List<Integer> userIdList = myClientReq.getUserId();
        List<Integer> myClientNotComplete = CommonUtils.bigDataInQuery(NumberConstant.FIFTY,userIdList,ids ->{
            MyClientReq myClientReq1 = new MyClientReq();
            BeanUtils.copyProperties(myClientReq, myClientReq1);
            myClientReq1.setUserId(ids);
            return webOrderMapper.getMyClientPurchaseComplete(myClientReq1,COMMODITY_ID);
        });
        myClientReq.setUserId(userIdList);
        return myClientNotComplete;
    }

    /**
     * 筛选近X天【已完成】状态的订单中包含电子烟商品
     * @param myClientReq myClientReq
     * @return
     */
    @Override
    public List<Integer> getMyClientIsCompleteByCigarette(MyClientReq myClientReq) {
        //当会员id为空，返回空数组
        if (myClientReq.getUserId().isEmpty()){
            return new ArrayList<>();
        }
        //查询云雾商品分类 为空返回空数组
        R<String> valueByCode = sysConfigClient.getValueByCode(SysConfigConstant.FOG_FEN_CID);
        if (valueByCode.getCode() != ResultCode.SUCCESS && StringUtils.isEmpty(valueByCode.getData())) {
            return new ArrayList<>();
        }
        //过滤云雾会员的value
        List<Integer> valueList = Stream.of(valueByCode.getData().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        //一次in 50条数据
        List<Integer> userIdList = myClientReq.getUserId();
        List<Integer> myClientNotComplete = CommonUtils.bigDataInQuery(NumberConstant.FIFTY,userIdList,ids ->{
            MyClientReq myClientReq1 = new MyClientReq();
            BeanUtils.copyProperties(myClientReq, myClientReq1);
            myClientReq1.setUserId(ids);
            return webOrderMapper.getMyClientIsCompleteByCigarette(myClientReq1,valueList);
        });
        myClientReq.setUserId(userIdList);
        return myClientNotComplete;
    }

    /**
     * 根据mkcIdList查询库存位置
     * @param mkcIdList mkcIdList
     * @return
     */
    @Override
    public List<GoodProductStockVO> getGoodProductStockByMkcList(List<Integer> mkcIdList) {
        if (CollUtil.isEmpty(mkcIdList)){
            return new ArrayList<>();
        }
        return CommonUtils.bigDataInQuery(NumberConstant.FIFTY, mkcIdList, ids -> webOrderMapper.getGoodProductStockByMkcList(ids));
    }


    /**
     *
     * @param outTradeNo
     * @return
     */
    @Override
    public R<OrderInfoRes> getOrderByWxNo(String outTradeNo) {
        if (StrUtil.isBlank(outTradeNo)) {
            return R.error("订单号不能为空");
        }

        // 获取订单基本信息
        OrderByWxNoVO orderInfo = webOrderMapper.getAlipayInfoById(outTradeNo);
        if (orderInfo == null) {
            return R.error("该订单不存在！");
        }

        // 获取订单项列表
        List<OrderByWxNoVO> orderByWxNoDTOList;
        if (StrUtil.isBlank(orderInfo.getOrderNum())) {
            orderByWxNoDTOList = webOrderMapper.getPayItemInfoById(orderInfo.getId());
        } else {
            orderByWxNoDTOList = Collections.singletonList(orderInfo);
        }

        // 按支付类型分组获取订单号
        Map<Integer, List<String>> payTypeToOrderNums = new HashMap<>();
        payTypeToOrderNums.put(PayTypeEnum.ORDER.getCode(), new ArrayList<>());
        payTypeToOrderNums.put(PayTypeEnum.GOOD_PRODUCT.getCode(), new ArrayList<>());
        payTypeToOrderNums.put(PayTypeEnum.AFTER_SERVICE.getCode(), new ArrayList<>());
        payTypeToOrderNums.put(PayTypeEnum.SMALL_PRODUCT_AFTER_SERVICE.getCode(), new ArrayList<>());

        // 按支付类型分组订单号
        orderByWxNoDTOList.forEach(order -> {
            if (StrUtil.isNotBlank(order.getOrderNum()) && payTypeToOrderNums.containsKey(order.getPayType())) {
                payTypeToOrderNums.get(order.getPayType()).add(order.getOrderNum());
            }
        });

        // 获取所有订单项
        List<OrderItemVO> orderItemList = new ArrayList<>();

        // 使用统一方法处理不同类型订单
        if (CollUtil.isNotEmpty(payTypeToOrderNums.get(PayTypeEnum.ORDER.getCode()))) {
            orderItemList.addAll(webOrderMapper.getSubOrderInfoBySubIds(payTypeToOrderNums.get(PayTypeEnum.ORDER.getCode())));
        }
        if (CollUtil.isNotEmpty(payTypeToOrderNums.get(PayTypeEnum.GOOD_PRODUCT.getCode()))) {
            orderItemList.addAll(webOrderMapper.getGoodSubOrderInfoBySubIds(payTypeToOrderNums.get(PayTypeEnum.GOOD_PRODUCT.getCode())));
        }
        if (CollUtil.isNotEmpty(payTypeToOrderNums.get(PayTypeEnum.AFTER_SERVICE.getCode()))) {
            orderItemList.addAll(webOrderMapper.getRepairSubOrderInfoBySubIds(payTypeToOrderNums.get(PayTypeEnum.AFTER_SERVICE.getCode())));
        }
        if (CollUtil.isNotEmpty(payTypeToOrderNums.get(PayTypeEnum.SMALL_PRODUCT_AFTER_SERVICE.getCode()))) {
            orderItemList.addAll(webOrderMapper.getSmallSubOrderInfoBySubIds(payTypeToOrderNums.get(PayTypeEnum.SMALL_PRODUCT_AFTER_SERVICE.getCode())));
        }

        if (CollUtil.isEmpty(orderItemList)) {
            return R.error("该订单不存在");
        }

        // 获取域名配置
        String webUrl = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.WEB_URL))
                .filter(R::isSuccess)
                .map(R::getData)
                .orElseThrow(() -> new CustomizeException("域名获取失败，ORG服务异常！"));

        // 处理商品图片
        if (CollUtil.isNotEmpty(orderItemList)) {
            // 收集有效的ppriceId
            List<Long> ppidList = orderItemList.stream()
                    .map(OrderItemVO::getPpriceId)
                    .filter(StrUtil::isNotBlank)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());

            // 批量获取商品图片
            if (CollUtil.isNotEmpty(ppidList)) {
                String url = StrUtil.format("{}/web/api/products/batchGetPackPic/v1?t={}", webUrl, System.currentTimeMillis());
                String result = HttpRequest.post(url)
                        .header("Content-Type", "application/json")
                        .body(JSONUtil.toJsonStr(ppidList))
                        .execute()
                        .body();

                // 解析图片结果并设置到订单项
                JSONObject json = JSONUtil.parseObj(result);
                JSONArray dataArr = json.getJSONArray("data");
                Map<String, String> ppidToPic = new HashMap<>();

                for (int i = 0; i < dataArr.size(); i++) {
                    JSONObject obj = dataArr.getJSONObject(i);
                    String ppid = String.valueOf(obj.getLong("ppid"));
                    JSONArray pics = obj.getJSONArray("pics");
                    if (pics != null && !pics.isEmpty()) {
                        ppidToPic.put(ppid, pics.getStr(0));
                    }
                }

                // 设置图片和支付类型名称
                orderItemList.forEach(item -> {
                    if (item.getPpriceId() != null) {
                        String imageUrl = ppidToPic.get(item.getPpriceId());
                        if (imageUrl != null) {
                            item.setImagePath(imageUrl);
                        }
                    }
                    item.setPayTypeName(EnumUtil.getMessageByCode(PayTypeEnum.class, item.getPayType()));
                });
            }

            // 处理良品主图（九机特有）
            List<Integer> mkcIds = orderItemList.stream()
                    .filter(v -> StrUtil.isBlank(v.getImagePath()))
                    .map(OrderItemVO::getMkcId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(mkcIds)) {
                List<SalfGoodsNewMpicsRes> newMpicsList = SpringUtil.getBean(UsedGoodsService.class).getNewMpicsByMkcIds(mkcIds);
                Map<Integer, String> newMpicsMap = newMpicsList.stream()
                        .collect(Collectors.toMap(SalfGoodsNewMpicsRes::getMkcId, SalfGoodsNewMpicsRes::getNewMpics, (v1, v2) -> v1));

                orderItemList.forEach(v -> {
                    if (v.getMkcId() != null) {
                        String newMpics = newMpicsMap.get(v.getMkcId());
                        if (StrUtil.isNotBlank(newMpics)) {
                            v.setImagePath(StrUtil.subBefore(newMpics, ",", false));
                        }
                    }
                });
            }
        }
        // 构建返回结果
        OrderInfoRes orderInfoRes = new OrderInfoRes();
        List<OrderInfoRes.OrderItem> orderItems = new ArrayList<>();

        // 按支付类型分组处理订单项
        if (CollUtil.isNotEmpty(orderItemList)) {
            orderItemList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(
                            item -> Optional.ofNullable(item.getPayType()).orElse(0),
                            Collectors.toList()
                    ))
                    .forEach((payType, items) -> {
                        if (CollUtil.isNotEmpty(items)) {
                            // 创建订单组
                            OrderInfoRes.OrderItem orderItem = new OrderInfoRes.OrderItem();

                            // 获取第一个项目的基本信息作为订单组信息
                            OrderItemVO firstItem = items.get(0);
                            orderItem.setOrderType(String.valueOf(payType));
                            orderItem.setName(StrUtil.emptyToDefault(firstItem.getName(), ""));
                            orderItem.setId(StrUtil.emptyToDefault(firstItem.getId(), ""));
                            orderItem.setPrice(StrUtil.emptyToDefault(firstItem.getPrice(), "0"));
                            orderItem.setTime(StrUtil.emptyToDefault(firstItem.getTime(), ""));
                            orderItem.setImagePath(StrUtil.emptyToDefault(firstItem.getImagePath(), ""));

                            // 设置该类型下的所有商品项
                            List<OrderInfoRes.ProductItem> productItems = items.stream()
                                    .filter(Objects::nonNull)
                                    .map(item -> {
                                        OrderInfoRes.ProductItem productItem = new OrderInfoRes.ProductItem();
                                        try {
                                            BeanUtils.copyProperties(item, productItem);
                                        } catch (Exception e) {
                                            log.error("复制属性失败", e);
                                        }
                                        return productItem;
                                    })
                                    .collect(Collectors.toList());

                            orderItem.setProductItemList(productItems);

                            // 将订单组添加到结果列表
                            orderItems.add(orderItem);
                        }
                    });
        }

        orderInfoRes.setList(orderItems);
        return R.success(orderInfoRes);
    }
}
