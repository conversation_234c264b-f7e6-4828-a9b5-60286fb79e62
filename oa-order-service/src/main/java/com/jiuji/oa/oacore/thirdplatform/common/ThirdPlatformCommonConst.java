package com.jiuji.oa.oacore.thirdplatform.common;

/**
 * 第三方订单平台常量
 * <AUTHOR>
 */
public class ThirdPlatformCommonConst {

    private ThirdPlatformCommonConst(){
    }

    /**
     * 美团库存同步地址
     */
    public static final String MEITUAN_STOCK_URL = "https://waimaiopen.meituan.com/api/v1/retail/sku/stock";

    /**
     * 第三方平台编码：美团
     */
    public static final String THIRD_PLAT_MT = "MT";

    /**
     * 第三方平台编码：抖音
     */
    public static final String THIRD_PLAT_DY = "DY";
    /**
     * 第三方平台编码：小米
     */
    public static final String THIRD_PLAT_MI = "MI";

    /**
     * 聚合小米所有租户
     */
    public static final String AGGREGATION_MI_TENANT_CODE = "ALL_MI";

    /**
     * 第三方平台编码：抖音团购
     */
    public static final String THIRD_PLAT_DYTG = "DYTG";

    /**
     * 第三方平台 抖音小时达
     */
    public static final String THIRD_PLAT_DYXSD = "DYXSD";




    public static final String THIRD_PLATTG = "TG";

    public static final String THIRD_PLAT_TB = "TB";

    /**
     * 第三方平台编码：京东
     */
    public static final String THIRD_PLAT_JD = "JD";

    public static final String STRING_NULL = "null";

    public static final String STRING_UNDEFINED = "undefined";

    /**
     * 订单操作人
     */
    public static final String ORDER_OP_USER_INTERNET = "网络";

    public static final String ORDER_OP_USER_SYSTEM = "系统";

    /**
     * 订单支付方式：在线支付
     */
    public static final Integer ORDER_PAY_TYPE_ONLINE = 1;

    /**
     * 订单支付方式：货到付款
     */
    public static final Integer ORDER_PAY_TYPE_ARRIVE = 2;

    /**
     * 配送方式：到店自取
     */
    public static final Integer DELIVERY_TYPE_SELF = 1;

    /**
     * 配送方式：快递运输
     */
    public static final Integer DELIVERY_TYPE_EXPRESS_TRANSPORT = 4;

    /**
     * 配送方式：九机快送
     */
    public static final Integer DELIVERY_TYPE_EXPRESS = 2;

    public static final Long MIN_MILL = 1000L;

    public static final long HOUR = 3600 * 1000L;

    public static final Long OFFSET = 600L;

    public static final String EN_CODING_UTF8 = "UTF-8";

    public static final String OA_API_SUBMIT_ORDER_EX = "/oaApi.svc/rest/SubmitOrderEx";

    public static final String OA_API_SUBMIT_ORDER_ZHKEY = "/recover.svc/rest/submitOrderByZhkey";

    /**
     * 良品支付接口
     */
    public static final String OA_API_SUBMIT_ORDER_SUBPAY = "/recover.svc/rest/subPay";

    public static final String OA_API_SUB_PAY = "/oaApi.svc/rest/subPay";

    public static final String OA_API_SUB_REFUND = "/oaApi.svc/rest/AutoReturnOrderMeituan";

    /**
     * 良品订单退订接口
     */
    public static final String OA_API_SUBMIT_REFUND = "/recover.svc/rest/autoReturnOrder";

    /**
     * 优惠码使用
     */
    public static final String OA_API_YOUHUIMA_USE = "/recover.svc/rest/youhuimaUse";

    /**
     * 相加
     */
    public static final int SYNC_TYPE_ADD = 1;
    /**
     * 相减
     */
    public static final int SYNC_TYPE_SUBTRACT = 2;
    /**
     * 相乘
     */
    public static final int SYNC_TYPE_MULTI = 3;
    /**
     * 固定值
     */
    public static final int SYNC_TYPE_FIX = 4;

    public static final Integer COMMON_INTEGER_TRUE = 1;

    public static final String COMMON_STRING_ONE = "1";

    public static final String COMMON_STRING_TRUE = "true";

    /**抖音消息门店id字段*/
    public static final String SHOP_ID = "shop_id";

    public static final Long TEN_THOUSAND = 10000L;

/**
     * 订单标识：云南国补订单
     */
    public static final Integer ORDER_TAG_YUNAN_GOVERNMENT_SUBSIDY = 10027;


    /**美团订单加密手机号正则*/
    public static final String MEITUAN_ENCRYPT_MOBILE_REGEX = "手机号\\s*([0-9*]+)";
}
