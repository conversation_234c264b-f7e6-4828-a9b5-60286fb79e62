package com.jiuji.oa.oacore.oaorder.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.oaorder.po.ProductMark;
import com.jiuji.oa.oacore.oaorder.vo.req.NationalSubsidyOrderDetailReq;
import com.jiuji.oa.oacore.oaorder.vo.res.EfficiencyRes;
import com.jiuji.oa.oacore.oaorder.vo.res.NationalSubsidyEfficiencyCode;
import com.jiuji.oa.oacore.oaorder.vo.res.NationalSubsidyOrderDetailRes;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface NationalSubsidyOrderMapper {

    /**
     * 查询国补订单明细列表
     */
    Page<NationalSubsidyOrderDetailRes> selectNationalSubsidyOrderList(Page<NationalSubsidyOrderDetailRes> page, @Param("req") NationalSubsidyOrderDetailReq req);

    List<NationalSubsidyOrderDetailRes> selectNationalSubsidyInvoiceList(@Param("orderNoList") List<Integer> orderNoList);

    List<NationalSubsidyOrderDetailRes> selectNationalSubsidyCashierList(@Param("orderNoList") List<Integer> orderNoList);

    //查询国补收银方式
    List<NationalSubsidyOrderDetailRes> getThreePartiesPaymentType(@Param("orderNoList") List<Integer> orderNoList);

    Integer isNationalSubsidy(@Param("subId") Integer subId);

    List<NationalSubsidyOrderDetailRes> nationalSubShouyinOther(@Param("subIds") List<Integer> subIds);

    List<ProductMark> getTagStatistics();

    /**
     * 查询国补订单 身份证号
     * @param subIdList
     * @return
     */
    List<NationalSubsidyOrderDetailRes> getIdCard(@Param("subIdList") List<Integer> subIdList);
    @DS(DataSourceConstants.WEB999)
    List<EfficiencyRes> getEfficiencyByTGovernmentCategory(@Param("xtenant") Integer xtenant);

    List<EfficiencyRes> getEfficiencyByTaxCodeOldNew();

    List<EfficiencyRes> getEfficiencyByBasketExtend();

    List<NationalSubsidyOrderDetailRes> getSubAddress(@Param("subIdList") List<Integer> subIdList);

    /**
     * 查询国补订单 国补收银时录入的69码
     * @param subIdList
     * @return
     */
    List<NationalSubsidyEfficiencyCode> getEfficiencyCode(@Param("subIdList") List<Integer> subIdList);
}
