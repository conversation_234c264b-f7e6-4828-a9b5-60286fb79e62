package com.jiuji.oa.oacore.thirdplatform.douyinlife.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.oa.oacore.common.constant.RedisKeyConstant;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.thirdplatform.common.enums.PlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.common.util.JsonUtils;
import com.jiuji.oa.oacore.thirdplatform.douyinlife.constant.DouyinLifeConstant;
import com.jiuji.oa.oacore.thirdplatform.douyinlife.service.DouyinLifeService;
import com.jiuji.oa.oacore.thirdplatform.douyinlife.utils.DouyinLifeUtil;
import com.jiuji.oa.oacore.thirdplatform.douyinlife.vo.DouyinLifeBase;
import com.jiuji.oa.oacore.thirdplatform.douyinlife.vo.DouyinLifeClientToken;
import com.jiuji.oa.oacore.thirdplatform.douyinlife.vo.DouyinLifeClientTokenReq;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.TenantService;
import com.jiuji.oa.oacore.thirdplatform.tenant.vo.TenantStoreReq;
import com.jiuji.oa.oacore.thirdplatform.tuangou.dto.TenantStoreDto;
import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.MeituanTuangouToken;
import com.jiuji.tc.utils.constants.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 抖音生活服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class DouyinLifeServiceImpl implements DouyinLifeService {
    @Resource
    private TenantService tenantService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public DouyinLifeClientToken getAccessToken(Integer areaId) {
        TenantStoreReq tenantStoreReq = TenantStoreReq.builder().platCode(PlatfromEnum.DYTG.name()).areaId(areaId).build();
        TenantStoreDto tenant = tenantService.getTenantStore(tenantStoreReq);
        return SpringUtil.getBean(DouyinLifeService.class).getAccessToken(tenant.getAppKey(), tenant.getAppSecret());
    }

    @Override
    public DouyinLifeClientToken getAccessToken(String appKey, String appSecret) {
        String tokenKey = RedisKeyConstant.DOUYIN_TUANGOU_TOKEN + appKey;
        String tokenStr = stringRedisTemplate.opsForValue().get(tokenKey);
        if (StringUtils.isNotBlank(tokenStr)) {
            MeituanTuangouToken cacheToken = JsonUtils.fromJson(tokenStr, MeituanTuangouToken.class);
            DouyinLifeClientToken token = new DouyinLifeClientToken();
            token.setAccessToken(cacheToken.getAccessToken());
            token.setExpiresIn(cacheToken.getExpiresIn());
            return token;
        }

        DouyinLifeClientTokenReq req = new DouyinLifeClientTokenReq(appKey, appSecret);
        String result = DouyinLifeUtil.post(DouyinLifeConstant.BASE_URL + DouyinLifeConstant.CLIENT_TOKEN_URL, JsonUtils.toJson(req));
        if (StringUtils.isBlank(result)) {
            throw new CustomizeException(StrUtil.format("通过抖音接口获取token发生{}异常", result));
        }
        DouyinLifeBase<DouyinLifeClientToken> douyinLifeClientTokenBase = JsonUtils.fromJson(result, new com.fasterxml.jackson.core.type.TypeReference<DouyinLifeBase<DouyinLifeClientToken>>() {
        });
        DouyinLifeClientToken token = douyinLifeClientTokenBase.getData();
        if (Objects.nonNull(token)) {
            if (StringUtils.isBlank(token.getAccessToken())) {
                throw new CustomizeException(StrUtil.format("通过抖音接口获取token发生异常error_code:{},message{}", token.getErrorCode(), token.getDescription()));
            }
        } else {
            throw new CustomizeException(StrUtil.format("获取token发生异常"));
        }
        //缓存token
        MeituanTuangouToken tuangouToken = new MeituanTuangouToken();
        tuangouToken.setAppKey(appKey);
        tuangouToken.setAppSecret(appSecret);
        tuangouToken.setAccessToken(token.getAccessToken());
        tuangouToken.setExpiresIn(token.getExpiresIn());
        tuangouToken.setExpiresTime(LocalDateTime.now().plusSeconds(tuangouToken.getExpiresIn()).minusSeconds(NumberConstant.SIXTY));
        stringRedisTemplate.opsForValue().set(tokenKey, JsonUtils.toJson(tuangouToken), NumberConstant.FOUR, TimeUnit.MINUTES);
        return token;
    }
}
