package com.jiuji.oa.oacore.thirdplatform.baozun.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.jiuji.oa.oacore.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.oaorder.service.ProductinfoService;
import com.jiuji.oa.oacore.thirdplatform.baozun.common.constants.CommonConstants;
import com.jiuji.oa.oacore.thirdplatform.baozun.common.enums.BzWorkLogEnum;
import com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenant;
import com.jiuji.oa.oacore.thirdplatform.baozun.po.BzTenantVariants;
import com.jiuji.oa.oacore.thirdplatform.baozun.po.BzWorkLog;
import com.jiuji.oa.oacore.thirdplatform.baozun.service.BzTenantService;
import com.jiuji.oa.oacore.thirdplatform.baozun.service.BzTenantVariantsService;
import com.jiuji.oa.oacore.thirdplatform.baozun.service.BzWorkLogService;
import com.jiuji.oa.oacore.thirdplatform.baozun.vo.req.BzTenantVariantsSearchReq;
import com.jiuji.oa.oacore.thirdplatform.baozun.vo.res.BzTenantVariantsRes;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.EnumVO;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jodd.util.StringPool;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/9/4 19:29
 * @Description
 */

@RestController
@RequestMapping("/api/baozun/variants")
@Api(tags = "宝尊商品详细信息")
public class BzTenantVariantsController {
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private BzTenantVariantsService bzTenantVariantsService;
    @Resource
    private BzTenantService bzTenantService;
    @Resource
    private BzWorkLogService bzWorkLogService;
    @Resource
    private ProductinfoService productinfoService;

    @PostMapping("/listByPage")
    @ApiOperation("商品关系配置查询")
    public R<Page<BzTenantVariantsRes>> queryList(@Validated @RequestBody BzTenantVariantsSearchReq req) {
        if (Boolean.TRUE.equals(CommonUtil.isNullOrZero(req.getTenantId()))) {
            return R.error("参数不正确，商户ID不能为空。");
        }
        R<Page<BzTenantVariantsRes>> result = R.success(bzTenantVariantsService.listByPage(req));
        if (Boolean.FALSE.equals(CommonUtil.isNullOrZero(req.getTenantId()))) {
            BzTenant bzTenant = bzTenantService.getById(req.getTenantId());
            if (Objects.nonNull(bzTenant)) {
                result.put("tenantCode", bzTenant.getTenantCode());
                result.put("tenantName", bzTenant.getTenantName());
                result.put("tenantId", bzTenant.getId());
            }
        }
        return result;
    }


    /**
     *  配置信息修改
     * @param id
     * @param fieldName
     * @param paramPpid
     * @param newValue
     * @return
     */
    @PostMapping("/update")
    @ApiOperation("商品信息更新")
    public R<Boolean> update(@RequestParam("id") String id,
                             @RequestParam(value = "fieldName", defaultValue = "ppriceid", required = false) String fieldName,
                             @RequestParam(value = "ppid", required = false) Integer paramPpid,
                             @RequestParam(value = "newValue", required = false) String newValue
    ) {
        BzTenantVariants oldBzTenantVariants = bzTenantVariantsService.getById(id);
        BzTenantVariants bzTenantVariantsById = bzTenantVariantsService.getOne(new LambdaQueryWrapper<BzTenantVariants>()
                .eq(BzTenantVariants::getBrandSkuCode, id));

        BzTenantVariants.FieldNameEnum fieldNameEnum = EnumUtil.getEnumByCode(BzTenantVariants.FieldNameEnum.class, fieldName);

        if(fieldNameEnum == null){
            throw new CustomizeException("不支持该字段名称更新");
        }
        Integer ppid;
        if(paramPpid == null && BzTenantVariants.FieldNameEnum.PPRICEID.equals(fieldNameEnum) && StrUtil.isNotBlank(newValue)){
            ppid = Convert.toInt(newValue);
        }else{
            ppid = paramPpid;
        }

        if(ppid != null) {
            List<Integer> list = bzTenantVariantsService.lambdaQuery()
                    .eq(BzTenantVariants::getPpriceid, ppid).list()
                    .stream().map(BzTenantVariants::getPpriceid).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                throw new CustomizeException("SKU不允许重复录入：" + StringUtils.join(list, StringPool.COMMA));
            }
        }
        //构建操作日志
        BzWorkLog bzWorkLog = new BzWorkLog();
        bzWorkLog.setWorkName("更新商品配置");
        bzWorkLog.setCreateUser(abstractCurrentRequestComponent.getCurrentStaffId().getUserName());
        bzWorkLog.setIsDel(Boolean.FALSE);
        bzWorkLog.setLogType(BzWorkLogEnum.TENANT_PRODUCT.getCode());
        bzWorkLog.setCoreBusinessId(oldBzTenantVariants.getFkTenantId());
        //数据对比工具
        //List<String> fieldModifiedLog = WorkLogUtil.getFieldModifiedLog(BzTenantVariants.class, bzTenantVariantsById, bzTenantVariants);
        String workContent;
        String logFormat = "平台SKU编码：{}，{}由{}修改为{}";
        LambdaUpdateChainWrapper<BzTenantVariants> lambdaUpdate = bzTenantVariantsService.lambdaUpdate().eq(BzTenantVariants::getId,id);
        switch(fieldNameEnum){
            case PPRICEID:
                workContent = StrUtil.format(logFormat, oldBzTenantVariants.getBrandSkuCode(), fieldNameEnum.getMessage(),
                        oldBzTenantVariants.getPpriceid(), ppid);
                lambdaUpdate.set(BzTenantVariants::getPpriceid,ppid)
                    .set(!Optional.ofNullable(bzTenantVariantsById).map(BzTenantVariants::getPpriceid).isPresent(),
                            BzTenantVariants::getIsEnabled,Boolean.TRUE);
                break;
            case MARKET_CLASSIFICATION:
                workContent = StrUtil.format(logFormat, oldBzTenantVariants.getBrandSkuCode(), fieldNameEnum.getMessage(),
                                        oldBzTenantVariants.getMarketClassification(), newValue);
                lambdaUpdate.set(BzTenantVariants::getMarketClassification,newValue);
                break;
            default:
                throw new CustomizeException("该字段未实现更新逻辑");
        }

        bzWorkLog.setWorkContent(workContent);
        //开启事务 切换写库
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
            bzWorkLogService.save(bzWorkLog);
            lambdaUpdate.update();
        }).commit();
        return R.success(CommonConstants.SUCCESS_TIPS);
    }


    /**
     * 配置信息修改
     *
     * @return Boolean
     */
    @PostMapping("/nppPrice/update")
    @ApiOperation("商品nppPrice信息更新")
    public R<Boolean> updateNppPrice(@RequestParam("id") String id,
                                     @RequestParam("nppPrice") String nppPriceString) {

        String strReg = "^[0-9]+(\\.[0-9]{1,4})?$";
        BigDecimal nppPrice;
        if (StringUtils.isNotEmpty(nppPriceString)) {
            if (!nppPriceString.matches(strReg)) {
                throw new CustomizeException("金额格式不符合,请填写正数金额，最多保留4位小数" );
            } else {
                nppPrice = Convert.toBigDecimal(nppPriceString);
            }
        } else {
            nppPrice = null;
        }
//        BzTenantVariants bzTenantVariantsById = bzTenantVariantsService.getById(id);
        BzTenantVariants bzTenantVariantsById = bzTenantVariantsService.getOne(new LambdaQueryWrapper<BzTenantVariants>().eq(BzTenantVariants::getBrandSkuCode, id));
        BzTenantVariants bzTenantVariants = new BzTenantVariants();
//
//        BzTenantVariants one = bzTenantVariantsService.lambdaQuery()
//                .eq(BzTenantVariants::getId, id).one();
//        if (Objects.isNull(one) || Objects.isNull(one.getPpriceid())) {
//            throw new CustomizeException("未配置ppid：" + one.getVariantCode());
//        }
//        Productinfo productinfo = productinfoService.getProductinfoByPpid(Arrays.asList(one.getPpriceid())).stream()
//                .findFirst().orElse(null);
//        if (Objects.isNull(productinfo)) {
//            throw new CustomizeException("找不到所配置的ppid：" + one.getPpriceid());
//        }
//        if (Objects.nonNull(nppPrice) && productinfo.getIsmobile1() && nppPrice.compareTo(BigDecimal.ZERO) < 0) {
//            throw new CustomizeException("大件sku仅可输入大于等于0的数字");
//        }

        //构建操作日志
        BzTenantVariants one = bzTenantVariantsService.getById(id);
        BzWorkLog bzWorkLog = new BzWorkLog();
        bzWorkLog.setWorkName("更新商品配置");
        bzWorkLog.setCreateUser(abstractCurrentRequestComponent.getCurrentStaffId().getUserName());
        bzWorkLog.setIsDel(Boolean.FALSE);
        bzWorkLog.setLogType(BzWorkLogEnum.TENANT_PRODUCT.getCode());
        bzWorkLog.setCoreBusinessId(one.getFkTenantId());
        bzTenantVariants.setBrandSkuCode(id);
        bzTenantVariants.setNppPrice(nppPrice);
        //数据对比工具
        //List<String> fieldModifiedLog = WorkLogUtil.getFieldModifiedLog(BzTenantVariants.class, bzTenantVariantsById, bzTenantVariants);
        bzWorkLog.setWorkContent(StrUtil.format("平台SKU编码：{}，NPP采购价由{}修改为{}",one.getBrandSkuCode(),one.getNppPrice(),nppPrice));
        //开启事务 切换写库
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
            bzWorkLogService.save(bzWorkLog);
            bzTenantVariantsService.update(new LambdaUpdateWrapper<BzTenantVariants>().eq(BzTenantVariants::getId,id)
                    .set(BzTenantVariants::getNppPrice,nppPrice).set(!Optional.ofNullable(bzTenantVariantsById).map(BzTenantVariants::getNppPrice)
                            .isPresent(),BzTenantVariants::getIsEnabled,Boolean.TRUE));
//            bzTenantVariantsService.updateById(bzTenantVariants);
        }).commit();
        return R.success(CommonConstants.SUCCESS_TIPS);
    }

    /**
     * 配置信息修改
     *
     * @return Boolean
     */
    @PostMapping("/ecppPrice/update")
    @ApiOperation("商品ecppPrice信息更新")
    public R<Boolean> updateEcppPrice(@RequestParam("id") String id,
                                     @RequestParam("ecppPrice") String ecppPriceString) {
        String strReg = "^[0-9]+(\\.[0-9]{1,4})?$";
        BigDecimal ecppPrice;
        if (StringUtils.isNotEmpty(ecppPriceString)) {
            if (!ecppPriceString.matches(strReg)) {
                throw new CustomizeException("金额格式不符合,请填写正数金额，最多保留4位小数" );
            } else {
                ecppPrice = Convert.toBigDecimal(ecppPriceString);
            }
        } else {
            ecppPrice = null;
        }
        BzTenantVariants bzTenantVariantsById = bzTenantVariantsService.getOne(new LambdaQueryWrapper<BzTenantVariants>().eq(BzTenantVariants::getBrandSkuCode, id));
        BzTenantVariants bzTenantVariants = new BzTenantVariants();

//        BzTenantVariants one = bzTenantVariantsService.lambdaQuery()
//                .eq(BzTenantVariants::getId, id).one();
//        if (Objects.isNull(one) || Objects.isNull(one.getPpriceid())) {
//            throw new CustomizeException("未配置ppid：" + one.getVariantCode());
//        }
//        Productinfo productinfo = productinfoService.getProductinfoByPpid(Arrays.asList(one.getPpriceid())).stream()
//                .findFirst().orElse(null);
//        if (Objects.isNull(productinfo)) {
//            throw new CustomizeException("找不到所配置的ppid：" + one.getPpriceid());
//        }
//        if (Objects.nonNull(ecppPrice) && productinfo.getIsmobile1() && ecppPrice.compareTo(BigDecimal.ZERO) < 0) {
//            throw new CustomizeException("大件sku仅可输入大于等于0的数字");
//        }

        //构建操作日志
        BzTenantVariants one = bzTenantVariantsService.getById(id);
        BzWorkLog bzWorkLog = new BzWorkLog();
        bzWorkLog.setWorkName("更新商品配置");
        bzWorkLog.setCreateUser(abstractCurrentRequestComponent.getCurrentStaffId().getUserName());
        bzWorkLog.setIsDel(Boolean.FALSE);
        bzWorkLog.setLogType(BzWorkLogEnum.TENANT_PRODUCT.getCode());
        bzWorkLog.setCoreBusinessId(one.getFkTenantId());
        bzTenantVariants.setBrandSkuCode(id);
        bzTenantVariants.setEcppPrice(ecppPrice);
        //数据对比工具
        //List<String> fieldModifiedLog = WorkLogUtil.getFieldModifiedLog(BzTenantVariants.class, bzTenantVariantsById, bzTenantVariants);
        bzWorkLog.setWorkContent(StrUtil.format("平台SKU编码：{}，NPP采购价由{}修改为{}",one.getBrandSkuCode(),one.getEcppPrice(),ecppPrice));
        //开启事务 切换写库
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
            bzWorkLogService.save(bzWorkLog);
            bzTenantVariantsService.update(new LambdaUpdateWrapper<BzTenantVariants>().eq(BzTenantVariants::getId,id)
                    .set(BzTenantVariants::getEcppPrice,ecppPrice).set(!Optional.ofNullable(bzTenantVariantsById).map(BzTenantVariants::getEcppPrice)
                            .isPresent(),BzTenantVariants::getIsEnabled,Boolean.TRUE));
//            bzTenantVariantsService.updateById(bzTenantVariants);
        }).commit();
        return R.success(CommonConstants.SUCCESS_TIPS);
    }

    /**
     * 校验导入excel
     * @param file
     * @return
     */
    @PostMapping("/importExcel/v1")
    @ResponseBody
    @RepeatSubmitCheck(expression = "#{packageFullName}:#{methodSignName}",message = "有用户正在使用此功能导入数据，请稍后再尝试，以免冲突")
    public R<Map<String,Object>> checkExcel(MultipartFile file,Integer importType){
        return R.success(bzTenantVariantsService.checkAndImportExcel(file,importType));
    }

    /**
     * 切换启用状态
     *
     * @param enabled 启用状态
     * @return Boolean
     */
    @PostMapping("/switchEnable")
    @ApiOperation("切换启用状态")
    public R<Boolean> switchEnable(@RequestParam("id") String id,
                             @RequestParam("enabled") Boolean enabled) {
        if (enabled == null) {
            R.error("必须传入启用状态");
        }
        BzTenantVariants tenantVariants = bzTenantVariantsService.getById(id);
        if (Objects.isNull(tenantVariants)) {
            return R.error("商品数据不存在");
        }
        //构建操作日志
        BzWorkLog bzWorkLog = new BzWorkLog();
        bzWorkLog.setWorkName("更新配置");
        bzWorkLog.setCreateUser(abstractCurrentRequestComponent.getCurrentStaffId().getUserName());
        bzWorkLog.setIsDel(Boolean.FALSE);
        bzWorkLog.setLogType(BzWorkLogEnum.TENANT_PRODUCT.getCode());
        bzWorkLog.setCoreBusinessId(tenantVariants.getFkTenantId());

        bzWorkLog.setWorkContent(StrUtil.format("id: {}的同步状态由{}修改为{}",id,tenantVariants.getIsEnabled(),enabled));
        //开启事务 切换写库
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
            bzWorkLogService.save(bzWorkLog);
            boolean isUp = bzTenantVariantsService.update(new LambdaUpdateWrapper<BzTenantVariants>()
                    .set(BzTenantVariants::getIsEnabled, enabled).eq(BzTenantVariants::getId, id)
                    .and(cnd -> cnd.eq(BzTenantVariants::getIsEnabled, !Boolean.TRUE.equals(enabled))
                            .or().isNull(Boolean.TRUE.equals(enabled),BzTenantVariants::getIsEnabled)));
            if(!isUp){
                throw new CustomizeException("状态已被更新,不要重复操作");
            }
        }).commit();
        return R.success(Boolean.TRUE);
    }

    @GetMapping("/getEnums")
    public R<Map<String, List<EnumVO>>> getEnums(){
        return bzTenantVariantsService.getEnums();
    }
}
