/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.oacore.thirdplatform.stock.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.oacore.thirdplatform.stock.bo.ChangeStock;
import com.jiuji.oa.oacore.thirdplatform.stock.entity.StockSync;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存同步mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface StockSyncMapper extends BaseMapper<StockSync> {


    /**
     * 批量保存
     * @param subList
     */
    @DS("smallpro_write")
    void saveBatch(List<StockSync> subList);

    /**
     * 查询变动库存
     *
     * @param platCode
     * @param syncTime
     * @param syncType
     * @return
     */
    @DS("smallpro_write")
    List<ChangeStock> selectChangeStock(@Param("platCode") String platCode, @Param(value = "syncTime") LocalDateTime syncTime, @Param("syncType") String syncType);

    /**
     * 更新同步成功记录
     * @param successIds
     */
    @DS("smallpro_write")
    void updateSyncSuccess(List<Integer> successIds);

    /**
     * 更新同步失败记录
     * @param failIds
     */
    @DS("smallpro_write")
    void updateSyncFail(List<Integer> failIds);

    /**
     * 删除15天之前的数据
     * @param failIds
     */
    void delSyncByFifteenDay(LocalDateTime failIds);

    List<Integer> getRarePpriceIdList(@Param("allPpids") List<Integer> allPpids);

}
