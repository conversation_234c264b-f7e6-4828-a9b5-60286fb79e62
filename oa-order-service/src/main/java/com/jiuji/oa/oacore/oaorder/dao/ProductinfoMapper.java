package com.jiuji.oa.oacore.oaorder.dao;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.oacore.brand.dji.vo.ProductInfoVO;
import com.jiuji.oa.oacore.oaorder.bo.ProductIsMobileInfoBO;
import com.jiuji.oa.oacore.oaorder.bo.ProductSimpleBO;
import com.jiuji.oa.oacore.oaorder.po.Productinfo;
import com.jiuji.oa.oacore.oaorder.res.ProductInfoSimpleVo;
import com.jiuji.oa.oacore.oaorder.res.SearchProductInfoVO;
import com.jiuji.oa.oacore.promocode.bo.CommonSwitchBO;
import com.jiuji.oa.oacore.weborder.req.DiaoboStockInfoReqVO;
import com.jiuji.oa.oacore.weborder.req.GiftStockReqVO;
import com.jiuji.oa.oacore.weborder.req.WebStockInfoReqVO;
import com.jiuji.oa.oacore.weborder.req.WebStockPriceReqVO;
import com.jiuji.oa.oacore.weborder.res.DiaoboStockInfoResVO;
import com.jiuji.oa.oacore.weborder.res.GiftStockResVO;
import com.jiuji.oa.oacore.weborder.res.WebStockInfoResVO;
import com.jiuji.oa.oacore.weborder.res.WebStockPriceResVO;
import com.jiuji.oa.oacore.weborder.vo.res.CheckStockInfoResVO;
import com.jiuji.oa.oacore.weborder.vo.req.StockInfoReqVO;
import com.jiuji.oa.oacore.weborder.vo.res.StockCountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-06
 */
@Mapper
public interface ProductinfoMapper extends BaseMapper<Productinfo> {
    ProductInfoSimpleVo getProductInfoByImei(@Param("imei") String imei);

    @SqlParser(filter = true)
    List<Integer> selectPpidByProductId(@Param("productId") Integer productId);

    /**
     * 通过ppid获取pid
     *
     * @param ppids
     * @return
     */
    List<ProductSimpleBO> getProductMapByPpids(@Param("ppids") List<Integer> ppids);


    List<ProductSimpleBO> getProductMapByProductIds(@Param("productIds") List<Integer> productIds);

    List<CommonSwitchBO> getNameByIds(@Param("ids") List<Integer> limitIdsStr);

    List<Integer> getProducId(@Param("ids") List<Integer> split);

    List<Integer> getProductpriceId(@Param("ids") List<Integer> split);

    List<SearchProductInfoVO> getTop5SmallProductByNameLike(@Param("name") String name);

    List<SearchProductInfoVO> getTop5SmallProductByIdOrLike(@Param("id") Integer id);

    List<ProductInfoVO> listProductInfoByBrandId(@Param("brandId") Integer brandId);

    List<SearchProductInfoVO> getProductNamesByPpids(@Param("ids") List<Integer> ids);

    List<Productinfo> getProductListByPpids(@Param("ppids") List<Integer> ppids);

    List<SearchProductInfoVO> listProductNamesOnlyByMkcId(@Param("mkcIds") List<Integer> mkcIds);

    List<SearchProductInfoVO> getProductNamesByMkcId(@Param("mkcIds") List<Integer> mkcIds, @Param("isToBasketIdNull") boolean isToBasketIdNull);

    List<ProductIsMobileInfoBO> getProductIsmobileByPpids(@Param("ppids") List<Integer> ppids);

    List<StockCountVO> getProductMkcCountByPpids(@Param("ppids") List<Integer> ppids, @Param("req") StockInfoReqVO req);

    List<StockCountVO> getProductKcCountByPpids(@Param("ppids") List<Integer> ppids, @Param("req") StockInfoReqVO req);

    /**
     * 查询大件库存
     * @param req
     * @return
     */
    List<WebStockInfoResVO> getProductMkcStockByPpids(@Param("req") WebStockInfoReqVO req);

    /**
     * 查询小件库存
     * @param req
     * @return
     */
    List<WebStockInfoResVO> getProductKcStockByPpids(@Param("req") WebStockInfoReqVO req);

    List<DiaoboStockInfoResVO> getDiaoboByPpidAndToArea(@Param("req") DiaoboStockInfoReqVO req);

    List<WebStockPriceResVO> getInPriceByPpid(@Param("req") WebStockPriceReqVO req);

    /**
     * 查询大件库存数量
     * @param ppids
     * @param areaIds
     * @return
     */
    List<CheckStockInfoResVO> getMkcStockByPpidsAndAreaIds(@Param("ppids") String ppids,
                                                           @Param("areaIds") String areaIds);

    /**
     * 查询小件库存数量
     * @param ppids
     * @param areaIds
     * @return
     */
    List<CheckStockInfoResVO> getKcStockByPpidsAndAreaIds(@Param("ppids") String ppids,
                                                          @Param("areaIds") String areaIds);

    /**
     * 获取赠品库存
     * @param req
     * @return
     */
    List<GiftStockResVO> getGiftStockByAreaIds(@Param("req") GiftStockReqVO req);

    List<WebStockPriceResVO> getProductInPriceByPpid(@Param("req") WebStockPriceReqVO req);
}
