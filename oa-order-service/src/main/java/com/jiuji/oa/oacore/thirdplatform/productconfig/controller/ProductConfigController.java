package com.jiuji.oa.oacore.thirdplatform.productconfig.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.common.util.ValidatorUtil;
import com.jiuji.oa.oacore.common.util.WorkLogUtil;
import com.jiuji.oa.oacore.oaorder.res.SearchProductInfoVO;
import com.jiuji.oa.oacore.oaorder.service.ProductinfoService;
import com.jiuji.oa.oacore.thirdplatform.common.ThirdPlatformCommonConst;
import com.jiuji.oa.oacore.thirdplatform.common.enums.LogTypeEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.PlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.oplog.po.MeituanJdWorkLog;
import com.jiuji.oa.oacore.thirdplatform.oplog.service.MeituanJdWorkLogService;
import com.jiuji.oa.oacore.thirdplatform.productconfig.bo.*;
import com.jiuji.oa.oacore.thirdplatform.productconfig.entity.JdProductConfig;
import com.jiuji.oa.oacore.thirdplatform.productconfig.entity.ProductConfig;
import com.jiuji.oa.oacore.thirdplatform.productconfig.mapper.ProductConfigMapper;
import com.jiuji.oa.oacore.thirdplatform.productconfig.service.JdProductConfigService;
import com.jiuji.oa.oacore.thirdplatform.productconfig.service.ProductConfigService;
import com.jiuji.oa.oacore.thirdplatform.productconfig.vo.*;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.JdTenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.JdTenantService;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.TenantService;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.annotation.LogRecordAround;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.enums.EnumVO;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jodd.typeconverter.Convert;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 产品关系配置Controller
 *
 * <AUTHOR>
 * @date 2021-5-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/admin/api/productConfig")
@Api(value = "productConfig", tags = "美团&京东-商品配置接口")
@Slf4j
public class ProductConfigController {

    @Autowired
    private final ProductConfigService productConfigService;

    @Resource
    private final JdProductConfigService jdProductConfigService;

    @Autowired
    private final TenantService tenantService;

    @Resource
    private final JdTenantService jdTenantService;

    @Autowired
    private final ProductinfoService productinfoService;
    @Resource
    private final MeituanJdWorkLogService meituanJdWorkLogService;
    @Resource
    private final AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private final ProductConfigMapper productConfigMapper;


    @PostMapping("/listByPage")
    @ApiOperation("商品关系配置查询")
    public R queryList(ProductConfigSearchBO search) {
        //验证搜索类型不为空 && 搜索类型为本地skuid 搜索值不为空 搜索字段不包含数字
        if (search.getSearchOptions() != null && search.getSearchOptions() == ProductConfigSearchBO.SEARCH_OPTIONS_PPID
                && StringUtils.isNotBlank(search.getSearchValue()) && !StringUtils.isNumeric(search.getSearchValue())) {
            return R.error("请填写正确的ID");
        }
        //默认页码设置
        if (search.getCurrent() == null) {
            search.setCurrent(1);
        }
        if (search.getSize() == null) {
            search.setSize(NumberConstant.TEN);
        }
        //判断是否是京东到家的类型 忽略大小写判断
        if (ThirdPlatformCommonConst.THIRD_PLAT_JD.equalsIgnoreCase(search.getPlatCode())) {
            R<Page<ProductConfigVO>> result = R.success(jdProductConfigService.listByPage(search));
            //查询下商户信息
            if (null != search.getTenantid()) {
                JdTenant tenant = jdTenantService.getById(search.getTenantid());
                if (null != tenant) {
                    result.put("tenantCode", tenant.getTenantCode());
                    result.put("tenantName", tenant.getTenantName());
                    result.put("tenantid", tenant.getId());
                }
            }
            return result;
        }
        R<Page<ProductConfigVO>> result = R.success(productConfigService.listByPage(search));
        //查询下商户信息
        if (null != search.getTenantid()) {
            Tenant tenant = tenantService.getById(search.getTenantid());
            if (null != tenant) {
                result.put("tenantCode", tenant.getTenantCode());
                result.put("tenantName", tenant.getTenantName());
                result.put("tenantid", tenant.getId());
            }
        }
        return result;
    }

    @PostMapping("/listByPage/v2")
    @ApiOperation("商品关系配置查询")
    public R queryListV2(ProductConfigSearchBOV2 search) {
        //验证搜索类型不为空 && 搜索类型为本地skuid 搜索值不为空 搜索字段不包含数字
        if (search.getSearchOptions() != null && search.getSearchOptions() == ProductConfigSearchBO.SEARCH_OPTIONS_PPID
                && StringUtils.isNotBlank(search.getSearchValue()) && !StringUtils.isNumeric(search.getSearchValue())) {
            return R.error("请填写正确的ID");
        }
        //默认页码设置
        if (search.getCurrent() == null) {
            search.setCurrent(1);
        }
        if (search.getSize() == null) {
            search.setSize(NumberConstant.TEN);
        }
        R<Page<ProductConfigVOV2>> result = R.success(productConfigService.listByPageV2(search));
        //查询下商户信息
        if (null != search.getTenantid()) {
            Tenant tenant = tenantService.getById(search.getTenantid());
            if (null != tenant) {
                result.put("tenantCode", tenant.getTenantCode());
                result.put("tenantName", tenant.getTenantName());
                result.put("tenantid", tenant.getId());
            }
        }
        return result;
    }


    /**
     * 提供给neo进行商品同步
     * @param req
     * @return
     */
    @PostMapping("/listProductConfigs/v1")
    public R<Page<ListProductConfigsRes>>  listProductConfigs(@RequestBody @Validated ListProductConfigsReq req) {
        return R.success(productConfigService.listProductConfigs(req)) ;
    }


    @PostMapping("/listJdProductTyingConfigByPage/v1")
    @ApiOperation("京东商品搭售配置查询")
    public R listJdProductTyingConfigByPage(@RequestBody ProductConfigSearchBO search) {
        //验证搜索类型不为空 && 搜索类型为本地skuid 搜索值不为空 搜索字段不包含数字
        if (search.getSearchOptions() != null && search.getSearchOptions() == ProductConfigSearchBO.SEARCH_OPTIONS_PPID
                && StringUtils.isNotBlank(search.getSearchValue()) && !StringUtils.isNumeric(search.getSearchValue())) {
            return R.error("请填写正确的ID");
        }
        R<Page<JdProductTyingConfigPageVO>> result = R.success(jdProductConfigService.listJdProductTyingConfigByPage(search));
        return result;
    }

    /**
     * 添加或更新商品搭售关系配置
     *
     * @param productConfigBO
     * @return
     */
    @PostMapping("/jdProductTyingConfig/saveOrUpdate/v1")
    @ApiOperation("添加或更新商品搭售关系配置")
    public R saveOrUpdateJdProductTyingConfig(@RequestBody JdProductTyingConfigVO productConfigBO) {
        return jdProductConfigService.saveOrUpdateJdProductTyingConfig(productConfigBO);
    }


    /**
     * 添加或更新商品搭售关系配置
     *
     * @param productConfigBO
     * @return
     */
    @PostMapping("/jdProductTyingConfig/saveOrUpdateBatch/v1")
    @ApiOperation("添加或更新商品搭售关系配置")
    public R saveOrUpdateJdProductTyingConfigBatch(@RequestBody List<JdProductTyingConfigVO> productConfigBO) {
        return jdProductConfigService.saveOrUpdateJdProductTyingConfigBatch(productConfigBO);
    }
    /**
     * 添加或更新商品搭售关系配置
     *
     * @param productConfigBO
     * @return
     */
    @PostMapping("/jdProductTyingConfig/delete/v1")
    @ApiOperation("添加或更新商品搭售关系配置")
    public R deleteJdProductTyingConfig(@RequestBody JdProductTyingConfigVO productConfigBO) {
        return jdProductConfigService.deleteJdProductTyingConfig(productConfigBO);
    }


    /**
     * 添加商品关系配置
     *
     * @param productConfigBO
     * @return
     */
    @PostMapping("/save")
    @ApiOperation("增加商品关系配置")
    public R add(@RequestBody ProductConfigAddBO productConfigBO) {
        ValidatorUtil.validateEntity(productConfigBO);
        if (ThirdPlatformCommonConst.THIRD_PLAT_JD.equalsIgnoreCase(productConfigBO.getPlatCode())) {
            JdTenant jdTenant = jdTenantService.getOneTenantByJd(productConfigBO.getTenantCode());
            if (null == jdTenant) {
                return R.error("商户不存在！");
            }
            return jdProductConfigService.saveData(productConfigBO);
        }
        String tenantCode = productConfigBO.getTenantCode();
        //如果是聚合小米那就不需要进行商户校验
        Tenant tenant = tenantService.getOneTenantBy(tenantCode, productConfigBO.getPlatCode());
        if (!ThirdPlatformCommonConst.AGGREGATION_MI_TENANT_CODE.equals(tenantCode) && null == tenant) {
            return R.error("商户不存在！");
        }
        return productConfigService.saveData(productConfigBO);
    }

    /**
     * 添加商品关系配置
     *
     * @param productConfigBO
     * @return
     */
    @PostMapping("/saveOrUpdate/v1")
    @ApiOperation("增加商品关系配置")
    public R<String> saveOrUpdateConfig(@RequestBody ProductConfigAddBOV2 productConfigBO) {
        ValidatorUtil.validateEntity(productConfigBO);
        Tenant tenant = tenantService.getOneTenantBy(productConfigBO.getTenantCode(), productConfigBO.getPlatCode());
        if (!ThirdPlatformCommonConst.AGGREGATION_MI_TENANT_CODE.equals(productConfigBO.getTenantCode()) && null == tenant) {
            return R.error("商户不存在！");
        }
        return productConfigService.saveOrUpdateConfig(productConfigBO);
    }


    /**
     * 抖音商品解锁和删除
     * @param req
     * @return
     */
    @LogRecordAround(value ="抖音商品解锁和删除")
    @PostMapping("/dyUnlockAndDel")
    public R<String> dyUnlockAndDel(@RequestBody @Validated DyUnlockAndDelReq req) {
        List<Integer> mkcIdList = req.getMkcIdList();
        if(CollUtil.isEmpty(mkcIdList)){
            return R.error("良品库存不能为空");
        }
        List<LpByMkcIdVo> lpByMkcIdVos = SpringUtil.getBean(ProductConfigMapper.class).getIdLpByMkcId(mkcIdList);
        if(CollUtil.isEmpty(lpByMkcIdVos)){
            return R.error("良品库存配置为空");
        }
        List<Integer> collect = lpByMkcIdVos.stream().map(LpByMkcIdVo::getMkcId).collect(Collectors.toList());
        // 计算差集，找出mkcIdList中有但collect中没有的ID
        List<Integer> diffList = mkcIdList.stream()
                .filter(id -> !collect.contains(id))
                .collect(Collectors.toList());
        if(CollUtil.isNotEmpty(diffList)){
            return R.error("以下良品库存ID未找到对应配置：" + diffList);
        }
        // 收集删除失败的错误信息
        Map<Integer, String> errorMap = new HashMap<>();
        lpByMkcIdVos.forEach(lpByMkcIdVo -> {
            try {
                R deleteResult = delete(lpByMkcIdVo.getId(), ThirdPlatformCommonConst.THIRD_PLAT_DY);
                if(!deleteResult.isSuccess()){
                    errorMap.put(lpByMkcIdVo.getMkcId(), deleteResult.getMsg());
                }
            } catch (Exception e){
                errorMap.put(lpByMkcIdVo.getMkcId(), "程序异常"+e.getMessage());
                log.error("抖音商品解锁和删除异常：{}", lpByMkcIdVo.getMkcId()+e.getMessage(), e);
            }
        });
        if(CollUtil.isNotEmpty(errorMap)){
            String msg = "部分删除失败：" + errorMap;
            RRExceptionHandler.logError("抖音商品解锁和删除异常", msg, null, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            return R.error(msg);
        }
        return R.success("删除成功");
    }


    /**
     * 删除商品关系配置
     *
     * @param id
     * @return
     */
    @DS("smallpro_write")
    @PostMapping("/delete")
    @ApiOperation("删除商品关系配置")
    public R delete(Integer id, String platCode) {
        //校验是否有订单，有订单不允许删除
        if (null == id) {
            return R.error("参数错误！");
        }
        //判断是否是京东到家的订单类型
        if (ThirdPlatformCommonConst.THIRD_PLAT_JD.equalsIgnoreCase(platCode)) {
            JdProductConfig byId = jdProductConfigService.getById(id);
            MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.PRODUCT_CONFIG_DELETE.getMessage(),
                    MessageFormat.format("商品序号为：{0}, 商家SKU编码:{1},商品本地SKU:{2},", id, byId.getSkuId(), Convert.toString(byId.getPpriceid())),
                    abstractCurrentRequestComponent.getCurrentStaffId().getUserName(), LogTypeEnum.PRODUCT_CONFIG_DELETE.getCode(), PlatfromEnum.JD.getCode(), byId.getTenantCode());
            //开启事务 切换写库
            MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
                meituanJdWorkLogService.save(structure);
                jdProductConfigService.removeById(id);
            }).commit();
            return R.success("删除成功！");
        }
        ProductConfig byId = productConfigService.getById(id);
        Integer platFrom = PlatfromEnum.getCodeByName(byId.getPlatCode());
        MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.PRODUCT_CONFIG_DELETE.getMessage(),
                MessageFormat.format("商品序号为：{0}, 商家SKU编码:{1},商品本地SKU:{2},", id, byId.getSkuId(), Convert.toString(byId.getPpriceid())),
                abstractCurrentRequestComponent.getCurrentStaffId().getUserName(), LogTypeEnum.PRODUCT_CONFIG_DELETE.getCode(),platFrom , byId.getTenantCode());
        //开启事务 切换写库
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
            meituanJdWorkLogService.save(structure);
            productConfigService.removeById(id);
            if(CommonUtil.isNotNullZero(byId.getType()) && Boolean.TRUE.equals(byId.getLibraryLock())){
                //当库存还在锁定时删除商品关系，进行自动解锁库存操作
                productConfigMapper.lockStockBatch(Collections.singletonList(byId.getMkcId()),NumberConstant.ZERO, null);
                //删除的时候主站不自动上架
                //productConfigService.updateGisGroundByMkcId(Collections.singletonList(byId.getMkcId()), GisgroundEnum.ON_SALE.getCode());
            }
        }).commit();
        return R.success("操作成功！");
    }

    /**
     * 根据本地ppid查询商品名称
     *
     * @param ppids
     * @return
     */
    @PostMapping("/getProductNames")
    @ApiOperation("根据本地ppid查询商品名称")
    public R getProductNames(String ppids) {
        if (StringUtils.isBlank(ppids)) {
            return R.error("参数不能为空！");
        }
        List<SearchProductInfoVO> productList = productinfoService.getProductNamesByPpids(ppids);
        return R.success(productList);
    }

    /**
     * 根据mckId查询商品名称
     *
     * @param mkcId
     * @return
     */
    @PostMapping("/getProductNamesByMkcId")
    @ApiOperation("根据mckId查询商品名称")
    public R getProductNamesByMkcId(String mkcId) {
        if (StringUtils.isBlank(mkcId)) {
            return R.error("参数不能为空！");
        }
        List<SearchProductInfoVO> productList = productinfoService.getProductNamesByMkcId(mkcId);
        return R.success(productList);
    }

    /**
     * 更新商品关系配置
     *
     * @param editBo editBo
     * @return
     */
    @DS("smallpro_write")
    @PostMapping("/update")
    @ApiOperation("更新商品关系配置")
    public R update(@RequestBody ProductConfigEditBO editBo) {
        if (ThirdPlatformCommonConst.THIRD_PLAT_JD.equalsIgnoreCase(editBo.getPlatCode())) {
            JdProductConfig jdPc = jdProductConfigService.getById(editBo.getId());
            if (null == jdPc) {
                return R.error(String.format("数据不存在：ID[%s]", editBo.getId()));
            }
            JdProductConfig newPc = new JdProductConfig();
            BeanUtils.copyProperties(jdPc, newPc);
            String value = editBo.getEditValue();
            boolean editFlag = true;
            try {
                switch (editBo.getEditField()) {
                    case ProductConfigEditBO.PRICE_SPLIT:
                        newPc.setPriceSplit(Double.valueOf(value));
                        break;
                    case ProductConfigEditBO.SYNC_FIRST:
                        boolean syncFirst = stringToBoolean(value);
                        newPc.setSyncFirst(syncFirst);
                        break;
                    case ProductConfigEditBO.SYNC_LIMIT:
                        newPc.setSyncLimit(Integer.parseInt(value));
                        break;
                    case ProductConfigEditBO.SYNC_OFF:
                        boolean syncOff = stringToBoolean(value);
                        newPc.setSyncOff(syncOff);
                        break;
                    case ProductConfigEditBO.GIFT_OFF:
                        boolean giftOff = stringToBoolean(value);
                        newPc.setGiftOff(giftOff);
                        break;
                    case ProductConfigEditBO.PLATFORM_COST:
                        BigDecimal platformCost = Convert.toBigDecimal(value);
                        newPc.setPlatformCost(platformCost);
                        break;
                    case ProductConfigEditBO.SYNC_RATIO:
                        newPc.setSyncRatio(Double.valueOf(value));
                        break;
                    case ProductConfigEditBO.SYNC_TYPE:
                        newPc.setSyncType(Integer.parseInt(value));
                        break;
                    default:
                        editFlag = false;
                        break;
                }
            } catch (Exception e) {
                return R.error(String.format("参数错误：key(%s),value(%s)", editBo.getEditField(), value));
            }
            if (editFlag) {
                String logPrefix = "商家sku编码：" + jdPc.getSkuId() + "，本地商品sku：" + jdPc.getPpriceid()+",";
                //无字段更新时不写库
                boolean flag = false;
                //数据对比工具
                List<String> fieldModifiedLog = WorkLogUtil.getFieldModifiedLog(JdProductConfig.class, jdPc, newPc);
                List<String> newFieldModifiedLog = new ArrayList<>();
                //添加日志操作
                if (CollectionUtils.isEmpty(fieldModifiedLog)){
                    flag = true;
                    fieldModifiedLog.add(logPrefix + "无字段更新！");
                }else {
                    for (String s : fieldModifiedLog) {
                        newFieldModifiedLog.add( logPrefix + s);
                    }
                }
                MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.PRODUCT_CONFIG_UPDATE.getMessage(), newFieldModifiedLog.toString(),
                        abstractCurrentRequestComponent.getCurrentStaffId().getUserName(), LogTypeEnum.PRODUCT_CONFIG_UPDATE.getCode(), PlatfromEnum.JD.getCode(),newPc.getTenantCode());
                //开启事务 切换写库
                if (flag){
                    return R.success("操作成功");
                }
                MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
                    meituanJdWorkLogService.save(structure);
                    jdProductConfigService.updateById(newPc);
                }).commit();
            }
            return R.success("操作成功");
        }
        ProductConfig pc = productConfigService.getById(editBo.getId());
        if (null == pc) {
            return R.error(String.format("数据不存在：ID[%s]", editBo.getId()));
        }
        ProductConfig newpc = new ProductConfig();
        BeanUtils.copyProperties(pc, newpc);

        String value = editBo.getEditValue();
        boolean editFlag = true;
        try {
            switch (editBo.getEditField()) {
                case ProductConfigEditBO.PRICE_SPLIT:
                    newpc.setPriceSplit(Double.valueOf(value));
                    break;
                case ProductConfigEditBO.SYNC_FIRST:
                    boolean syncFirst = stringToBoolean(value);
                    newpc.setSyncFirst(syncFirst);
                    break;
                case ProductConfigEditBO.SYNC_LIMIT:
                    newpc.setSyncLimit(Integer.parseInt(value));
                    break;
                case ProductConfigEditBO.SYNC_OFF:
                    boolean syncOff = stringToBoolean(value);
                    newpc.setSyncOff(syncOff);
                    break;
                case ProductConfigEditBO.GIFT_OFF:
                    boolean giftOff = stringToBoolean(value);
                    newpc.setGiftOff(giftOff);
                    break;
                case ProductConfigEditBO.PLATFORM_COST:
                    BigDecimal platformCost = Convert.toBigDecimal(value);
                    newpc.setPlatformCost(platformCost);
                    break;
                case ProductConfigEditBO.SYNC_RATIO:
                    newpc.setSyncRatio(Double.valueOf(value));
                    break;
                case ProductConfigEditBO.SYNC_TYPE:
                    newpc.setSyncType(Integer.parseInt(value));
                    break;
                case ProductConfigEditBO.LABEL:
                    newpc.setLabel(Convert.toInteger(value));
                    break;
                default:
                    editFlag = false;
                    break;
            }
        } catch (Exception e) {
            return R.error(String.format("参数错误：key(%s),value(%s)", editBo.getEditField(), value));
        }
        if (editFlag) {

            String logPrefix = "商家sku编码：" + pc.getSkuId() + "，本地商品sku：" + pc.getPpriceid()+",";

            //无字段更新时不写库
            boolean flag = false;
            //数据对比工具
            List<String> fieldModifiedLog = WorkLogUtil.getFieldModifiedLog(ProductConfig.class, pc, newpc);
            List<String> newFieldModifiedLog = new ArrayList<>();
            //添加日志操作
            if (CollectionUtils.isEmpty(fieldModifiedLog)) {
                flag = true;
                fieldModifiedLog.add("无字段更新！");
            } else {
                for (String s : fieldModifiedLog) {
                    newFieldModifiedLog.add( logPrefix + s);
                }
            }
            Integer platFrom = PlatfromEnum.getCodeByName(editBo.getPlatCode());
            if (ThirdPlatformCommonConst.THIRD_PLAT_DY.equalsIgnoreCase(editBo.getPlatCode())) {
                platFrom = PlatfromEnum.DY.getCode();
            }
            MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.PRODUCT_CONFIG_UPDATE.getMessage(), newFieldModifiedLog.toString(),
                    abstractCurrentRequestComponent.getCurrentStaffId().getUserName(), LogTypeEnum.PRODUCT_CONFIG_UPDATE.getCode(), platFrom,newpc.getTenantCode());
            //开启事务 切换写库
            if (flag){
                return R.success("操作成功");
            }
            MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
                meituanJdWorkLogService.save(structure);
                productConfigService.updateById(newpc);
            }).commit();
        }
        return R.success("操作成功");
    }


    /**
     * 抖音良品更新配置 (抖音专用)
     */
    @PostMapping("/updateByDouDian")
    @ApiOperation("更新商品关系配置")
    public R<Boolean> updateByDouDian(@RequestBody ProductConfigEditByDouDianBO editBo) {
        return productConfigService.updateByDouDian(editBo);
    }


    /**
     * 导入商品配置数据
     *
     * @return
     */
    @PostMapping(value = "/uploadData")
    @ApiOperation(value = "导入商品配置数据", httpMethod = "POST")
    public R<Boolean> uploadData(MultipartFile file, String platCode) {
        //判断是否是京东到家的商品配置
        if (ThirdPlatformCommonConst.THIRD_PLAT_JD.equalsIgnoreCase(platCode)) {
            return jdProductConfigService.uploadData(file);
        }
        return productConfigService.uploadData(file,platCode);
    }

    /**
     * 导入商品配置数据
     *
     * @return
     */
    @PostMapping(value = "/syncCost")
    @ApiOperation(value = "导入商品配置数据", httpMethod = "POST")
    public R<Boolean> syncCost(@RequestBody ProductConfigCostReq req) {
        //判断是否是京东到家的商品配置
        if (ThirdPlatformCommonConst.THIRD_PLAT_JD.equalsIgnoreCase(req.getPlatCode())) {
            return R.success(jdProductConfigService.syncCost(req));
        }
        return R.success(productConfigService.syncCost(req));
    }



    /**
     * 导入商品配置数据
     *
     * @return
     */
    @PostMapping(value = "/uploadByDouDianData")
    @ApiOperation(value = "导入商品配置数据(抖音良品)", httpMethod = "POST")
    public R<Boolean> uploadByDouDianData(MultipartFile file, @RequestParam("platCode") String platCode, @RequestParam("tenantid") Integer tenantId) {
        if (ThirdPlatformCommonConst.THIRD_PLAT_DY.equalsIgnoreCase(platCode)) {
            return productConfigService.uploadByDouDianData(file, tenantId);
        }
        return R.error("导入类型错误");
    }


    /**
     * 清空商品配置数据
     *
     * @return
     */
    @GetMapping(value = "/clearData")
    @ApiOperation(value = "清空商户商品配置", httpMethod = "GET")
    public R<Boolean> clearData(String platCode, String tenantCode) {
        //判断是否是京东到家的商品配置
        if (ThirdPlatformCommonConst.THIRD_PLAT_JD.equalsIgnoreCase(platCode)) {
            return jdProductConfigService.clearData(tenantCode);
        }
        return productConfigService.clearData(tenantCode, platCode);
    }


    /**
     * 清空商品配置数据
     *
     * @return
     */
    @PostMapping(value = "/clearData/v2")
    @ApiOperation(value = "清空商户商品配置", httpMethod = "GET")
    public R<Boolean> clearData(@RequestBody ProductConfigClearReq req) {
        String platCode = req.getPlatCode();
        //判断是否是京东到家的商品配置
        if (ThirdPlatformCommonConst.THIRD_PLAT_JD.equalsIgnoreCase(platCode)) {
            return jdProductConfigService.clearDataV2(req);
        }
        return productConfigService.clearDataV2(req);
    }

    /**
     * String转boolean
     *
     * @param inputString inputString
     * @return
     */
    private boolean stringToBoolean(String inputString) {
        if (StringUtils.isBlank(inputString)) {
            return false;
        }
        inputString = StringUtils.lowerCase(inputString);
        return StringUtils.equals(ThirdPlatformCommonConst.COMMON_STRING_ONE, inputString) || StringUtils.equals(ThirdPlatformCommonConst.COMMON_STRING_TRUE, inputString);
    }



    @PostMapping("/exportExcel")
    @ApiOperation("导出订单配置信息")
    public R exportExcel(@RequestBody ProductConfigSearchBO req, HttpServletResponse response) {
        //判断是否是京东到家的类型 忽略大小写判断
        if (ThirdPlatformCommonConst.THIRD_PLAT_JD.equalsIgnoreCase(req.getPlatCode())) {
            return jdProductConfigService.exportExcel(req, response);
        }
       return productConfigService.exportExcel(req, response);
    }

    @PostMapping("/exportExcel/v2")
    @ApiOperation("导出订单配置信息")
    public R exportExcelV2(@RequestBody ProductConfigSearchBOV2 req, HttpServletResponse response) {
        return productConfigService.exportExcelV2(req, response);
    }

    /**
     * 美团绑定导入
     */

    /**
     * 导入商品配置数据
     *
     * @return
     */
    @PostMapping(value = "/importPoiCodeSpu")
    @ApiOperation(value = "导入门店关联商品", httpMethod = "POST")
    public R<Integer> importPoiCodeSpu(MultipartFile file) {
       return productConfigService.importPoiCodeSpu(file);
    }

    @GetMapping(value = "/getEnums")
    @ApiOperation(value = "获取商品的选项信息", httpMethod = "GET")
    public R<Map<String, List<EnumVO>>> getEnums(){
        return productConfigService.getEnums();
    }

}
