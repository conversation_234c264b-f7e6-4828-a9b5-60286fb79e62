package com.jiuji.oa.oacore.thirdplatform.annotation;


/**
 * 日志添加的类型
 *
 * <AUTHOR>
 */
public final class AddLogKind {

    /**
     * 主站调用stock接口修改条码
     */
    public static final String MEITUAN_PICKING_COMPLETE = "美团订单拣选完成";
    public static final String MEITUAN_ORDER_DETAIL = "美团订单详情";
    public static final String DELIVERY_STATUS_SYNCHRONIZATION = "美团订单快递信息同步";
    public static final String MODIFY_ORDER_INFO_SYNCHRONIZATION = "美团推送订单信息修改消息";
    public static final String CREATE_MEITUAN_MEMBER = "美团会员创建";
    public static final String SELECT_MEITUAN_MEMBER = "美团会员查询";
    public static final String ORDER_CHECK_UPDATE = "九机订单修改状态";
    public static final String ORDER_REFUND_DETAIL = "美团获取订单退款记录";
    public static final String BATCH_PULL_PHONE_NUMBER = "拉取用户真实手机号";
    public static final String GET_ORDER_PRIVACY_PHONE = "隐私号失效时获取新的隐私号";
    public static final String SYNCHRONIZATION_BUSINESS_HOURS = "同步门店营业时间到美团平台";
    public static final String SYNCHRONIZATION_PLATFORM_COST = "同步更新SKU价格";
    public static final String ORDER_MI_SYNCHRONIZATION = "小米订单同步";
    public static final String HUIJI_INSURANCE_CALLBACK= "汇机保回调接口";
    public static final String SYNCHRONIZATION_BUSINESS_HOURS_V1 = "同步门店营业时间到三方(TB/JD)平台";
    public static final String DOU_DIAN_STATUS_SYNCHRONIZATION= "抖音小时达订单配送信息同步";
    public static final String ORDER_LOGISTICS_SYNCHRONIZATION= "三方订单物流信息同步";
    public static final String DOU_DIAN_LOGISTICS_ADD= "抖音小时达订单发货";
    public static final String DOU_DIAN_LOGISTICS_EDIT= "抖音小时达订单更新物流信息";
    public static final String INVOICE_CALL= "美团发票回调";


    private AddLogKind() {
        throw new IllegalStateException("AddLogKind class");
    }
}
