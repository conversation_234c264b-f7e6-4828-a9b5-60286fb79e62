package com.jiuji.oa.oacore.thirdplatform.doudian.message.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.doudian.open.api.product_detail.ProductDetailRequest;
import com.doudian.open.api.product_detail.ProductDetailResponse;
import com.doudian.open.api.product_detail.data.ProductDetailData;
import com.doudian.open.api.product_detail.data.SpecPricesItem;
import com.doudian.open.core.msg.DoudianOpMsgParamRecord;
import com.doudian.open.msg.product_change.param.ProductChangeParam;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.enums.OaMesTypeEnum;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.oaorder.po.RecoverMarketsubinfo;
import com.jiuji.oa.oacore.oaorder.res.SearchProductInfoVO;
import com.jiuji.oa.oacore.oaorder.service.ProductinfoService;
import com.jiuji.oa.oacore.sys.service.WXSmsReceiverService;
import com.jiuji.oa.oacore.thirdplatform.baozun.enums.DoudianTagEnum;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.DoudianFactory;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.MyAccessToken;
import com.jiuji.oa.oacore.thirdplatform.doudian.enums.OuterProductIdEnum;
import com.jiuji.oa.oacore.thirdplatform.doudian.enums.ProductChangeEventEnum;
import com.jiuji.oa.oacore.thirdplatform.doudian.message.service.ProductChangeMessageService;
import com.jiuji.oa.oacore.thirdplatform.oplog.service.MeituanJdWorkLogService;
import com.jiuji.oa.oacore.thirdplatform.productconfig.bo.ProductConfigAddBO;
import com.jiuji.oa.oacore.thirdplatform.productconfig.bo.ProductConfigEditByDouDianBO;
import com.jiuji.oa.oacore.thirdplatform.productconfig.entity.ProductConfig;
import com.jiuji.oa.oacore.thirdplatform.productconfig.service.ProductConfigService;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCodeEnum;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.sms.SmsReceiverClassfyEnum;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 抖音消息处理的主接口
 * <AUTHOR>
 * @since 2022/3/20 20:55
 */
@Service
@Slf4j
public class ProductChangeMessageServiceImpl extends ParentMessageServiceImpl<DoudianOpMsgParamRecord<ProductChangeParam>> implements ProductChangeMessageService {
    @Autowired
    MeituanJdWorkLogService meituanJdWorkLogService;
    @Resource
    private DoudianFactory doudianFactory;
    @Resource
    private ProductConfigService productConfigService;
    @Resource(name = "prodAbstractCurrentRequestComponent")
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Resource
    @Lazy
    private ProductChangeMessageService productChangeMessageService;

    @Override
    protected R protectedHandleMessage(DoudianOpMsgParamRecord<ProductChangeParam> paramRecord) throws Exception{
        //获取到抖音推送过来的商品信息
        ProductChangeParam productChangeParam = paramRecord.getData();
        ProductChangeEventEnum eventEnum = EnumUtil.getEnumByCode(ProductChangeEventEnum.class, productChangeParam.getEvent());
        if(eventEnum == null){
            return R.error(StrUtil.format("事件[{}]没有对应的枚举类", productChangeParam.getEvent()));
        }
        R result = R.success(null);
        result.setUserMsg(null);
        // 以下处理都是sku级别的商品
        Long shopId = productChangeParam.getShopId();
        switch (eventEnum){
            case PRODUCT_APPROVED:
            case PRODUCT_SUBMITTED_FOR_REVIEW:
                //获取订单详情区分良品还是新机
                ProductDetailResponse detailResp = doudianFactory.executeRequest(new ProductDetailRequest(), shopId,
                        param -> param.setProductId(Convert.toStr(productChangeParam.getProductId())));
                if (!detailResp.isSuccess()){
                    throw new CustomizeException(detailResp.getMsg());
                }
                ProductDetailData detailData = detailResp.getData();
                List<SpecPricesItem> specPrices = ObjectUtil.defaultIfNull(detailData.getSpecPrices(), Collections.emptyList());
                // 获取全区推送人
                Collection<Integer> ch999Ids = getNoticeCh999Ids();
                Collection<SearchProductInfoVO> allProductNamesByMkcId = new LinkedList<>();
                //根据mkcId 查询ppid
                List<SearchProductInfoVO> productNamesByMkcId = Optional.of(specPrices).filter(CollUtil::isNotEmpty)
                        .map(spPrices -> spPrices.stream()
                                .map(specPrice -> {
                                    String outerProductId = specPrice.getCode();
                                    OuterProductIdEnum outerProductIdEnum = OuterProductIdEnum.getById(outerProductId);
                                    switch (outerProductIdEnum){
                                        case LP_ORDER:
                                            return outerProductIdEnum.getRealId(outerProductId);
                                        default:
                                            return null;
                                    }
                                }).filter(Objects::nonNull).collect(Collectors.toList()))
                        .filter(CollUtil::isNotEmpty)
                        .map(mkcIdList -> {
                            MultipleTransaction.query(DataSourceConstants.SMALLPRO_WRITE,
                                    () -> SpringUtil.getBean(ProductinfoService.class).listProductNamesOnlyByMkcId(mkcIdList))
                                    .forEach(allProductNamesByMkcId::add);
                            return MultipleTransaction.query(DataSourceConstants.SMALLPRO_WRITE,
                                    () -> SpringUtil.getBean(ProductinfoService.class).getProductNamesByMkcId(mkcIdList.stream().map(Object::toString)
                                            .collect(Collectors.joining(",")), false));
                        }).orElse(Collections.emptyList());
                Map<Integer, RecoverMarketsubinfo> outDouYinBasketMap = CollUtil.newHashMap(productNamesByMkcId.size());
                StringJoiner outErrMsgJoiner = new StringJoiner(jodd.util.StringPool.SLASH);
                productConfigService.handleDouYinBasket(productNamesByMkcId, outDouYinBasketMap, outErrMsgJoiner);
                for (SpecPricesItem specPrice : specPrices) {
                    String outerProductId = specPrice.getCode();
                    OuterProductIdEnum outerProductIdEnum = OuterProductIdEnum.getById(outerProductId);
                    Integer realId = outerProductIdEnum.getRealId(outerProductId);
                    //获取不到真实的单号
                    if(realId == null){
                        log.warn("商品[{}]本地商品信息[{}]解析异常", productChangeParam.getProductId(), outerProductId);
                        continue;
                    }
                    switch (outerProductIdEnum){
                        case LP_ORDER:
                            if (allProductNamesByMkcId.stream().allMatch(pnm -> ObjectUtil.notEqual(pnm.getId(), realId))){
                                //非我们的库存商品,不进行同步
                                log.warn("商品[{}]本地商品信息[{}]非九机良品", productChangeParam.getProductId(), outerProductId);
                                continue;
                            }
                            if(allProductNamesByMkcId.stream().filter(apn -> Objects.equals(apn.getId(), realId)).findFirst()
                                    .filter(apn -> apn.getToBasketId() == null).isPresent()){
                                log.warn("商品[{}]本地商品信息[{}]未进行订单锁定", productChangeParam.getProductId(), outerProductId);
                                continue;
                            }
                            //当前商品已卖完不需要同步
                            if(productNamesByMkcId.stream().allMatch(pnm -> ObjectUtil.notEqual(pnm.getId(), realId)) && ObjectUtil.defaultIfNull(specPrice.getStockNum(), 0L) <=0){
                                log.warn("商品[{}]良品mkcId[{}]已卖完不需要同步", productChangeParam.getProductId(), outerProductId);
                                continue;
                            }
                            //保存或更新良品配置信息
                            R r = productChangeMessageService.saveOrUpdateLpConfig(shopId, detailData, realId, specPrice, ch999Ids);
                            if(!r.isSuccess()){
                                r.setCode(ResultCodeEnum.SERVER_ERROR.getCode());
                            }
                            r.setUserMsg(StrUtil.format("mkcId[{}]同步结果: {}", realId, r.getUserMsg()));
                            break;
                        default:
                            //不需要处理
                            break;
                    }
                }
                break;
            default:
                break;
        }
        result.setUserMsg(ObjectUtil.defaultIfBlank(result.getUserMsg(), "不用执行商品同步"));
        return result;
    }

    @Override
    public Set<Integer> getNoticeCh999Ids() {
        return getNoticeCh999Ids(SmsReceiverClassfyEnum.DOU_DIAN_PRODUCT_SYSNC);
    }

    @Override
    public Set<Integer> getNoticeCh999Ids(SmsReceiverClassfyEnum classfyEnum) {
        return SpringUtil.getBean(WXSmsReceiverService.class)
                .getByClassify(classfyEnum.getCode())
                .map(sr -> StrUtil.splitTrim(sr.getCh999ids(), StringPool.COMMA).stream().map(Convert::toInt).collect(Collectors.toSet()))
                .orElse(Collections.emptySet());
    }

    @Override
    public R<Boolean> saveOrUpdateLpConfig(Long shopId, ProductDetailData detailData, Integer mkcId, SpecPricesItem specPrice, Collection<Integer> ch999Ids) {
        String productCode = Convert.toStr(detailData.getProductId());
        String skuId = specPrice.getCode();
        String priceSplit = Convert.toStr(ObjectUtil.defaultIfNull(detailData.getDiscountPrice(), 0L) / 100.00);
        OaUserBO oaUser = LambdaBuild.create(new OaUserBO()).set(OaUserBO::setUserName, "系统").build();
        Optional<ProductConfig> configOpt = productConfigService.lambdaQuery().eq(ProductConfig::getMkcId, mkcId)
                .orderByDesc(ProductConfig::getId).list().stream().findFirst();
        R<Boolean> r = saveOrUpdateLpConfig(shopId, mkcId, productCode, skuId, priceSplit, oaUser, configOpt);
        Map<String, Object> exData = ObjectUtil.defaultIfNull(r.getExData(), Collections.emptyMap());
        Integer lockNum = Convert.toInt(exData.get("lockNum"), 0);
        String workName = configOpt.map(c -> "更新").orElse("新增");
        //异常记录对应参数
        if(!r.isSuccess()){
            log.warn("{} mkcId: {}, 异常: {},shopId: {} detailData: {}, mkcId: {} specPrice: {}", workName, mkcId, r.getUserMsg(),shopId,
                    JSON.toJSONString(detailData), mkcId, specPrice);
        }
        SmsService smsService = SpringUtil.getBean(SmsService.class);
        if (!ch999Ids.isEmpty()){
            if(!r.isSuccess()){
                //发送oa消息通知相关人员
                smsService.sendOaMsg(StrUtil.format("抖音上架同步商品{}[{}]异常不能锁定，为避免超卖或交易损失，请及时核对，mkcid：{}", workName, r.getUserMsg(), mkcId),
                        "", CollUtil.join(ch999Ids, StringPool.COMMA), OaMesTypeEnum.DDTZ);
            }else if(lockNum < 1){
                //锁定失败发送消息通知相关人员
                smsService.sendOaMsg(StrUtil.format("抖音上架同步商品{}状态异常不能锁定，为避免超卖或交易损失，请及时核对，mkcid：{}", workName, mkcId),
                        "", CollUtil.join(ch999Ids, StringPool.COMMA), OaMesTypeEnum.DDTZ);
            }
        }
        return r;
    }

    @Override
    public R<Boolean> saveOrUpdateLpConfig(Long shopId, Integer mkcId, String productCode, String skuId, String priceSplit, OaUserBO oaUser, Optional<ProductConfig> configOpt) {
        R<Boolean> r = configOpt.map(config -> new ProductConfigEditByDouDianBO().setId(config.getId()).setLibraryLock(config.getLibraryLock())
                .setPlatCode(config.getPlatCode()).setPriceSplit(priceSplit)
                .setProductCode(productCode).setSkuId(skuId))
                //存在进行更新
                .map(pcedd -> currentRequestComponent.invokeWithUser(XtenantEnum.getXtenant(), oaUser, ou-> productConfigService.updateByDouDian(pcedd)))
                //不存在进行新增
                .orElseGet(() -> {
                    MyAccessToken myAccessToken = doudianFactory.getMyAccessToken(shopId);
                    Tenant tenant = myAccessToken.getTenant();
                    ProductConfigAddBO addProductConfig = new ProductConfigAddBO()
                            .setPlatCode(tenant.getPlatCode()).setTenantCode(tenant.getTenantCode())
                            .setProducts(Collections.singletonList(new ProductConfigAddBO.ProductConfigItemBO()
                                    .setMkcId(mkcId).setProductCode(productCode).setSkuId(skuId)));
                    return currentRequestComponent.invokeWithUser(XtenantEnum.getXtenant(), oaUser, ou-> productConfigService.saveDataByMkcId(addProductConfig));
                });
        return r;
    }


    @Override
    public DoudianTagEnum acceptTag() {
        return DoudianTagEnum.DOUDIAN_PRODUCT_CHANGE_TAG;
    }

}
