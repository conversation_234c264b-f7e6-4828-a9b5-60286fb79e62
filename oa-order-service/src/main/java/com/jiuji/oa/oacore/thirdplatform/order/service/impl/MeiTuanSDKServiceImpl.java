package com.jiuji.oa.oacore.thirdplatform.order.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.thirdplatform.annotation.AddLogKind;
import com.jiuji.oa.oacore.thirdplatform.annotation.AddOrderLog;
import com.jiuji.oa.oacore.thirdplatform.common.ThirdPlatformCommonConst;
import com.jiuji.oa.oacore.thirdplatform.order.bo.PreparationMealCompleteBO;
import com.jiuji.oa.oacore.thirdplatform.order.bo.SelectMeiTuanOrderDetailBO;
import com.jiuji.oa.oacore.thirdplatform.order.entity.Order;
import com.jiuji.oa.oacore.thirdplatform.order.service.MeiTuanSDKService;
import com.jiuji.oa.oacore.thirdplatform.order.service.OrderService;
import com.jiuji.oa.oacore.thirdplatform.order.vo.*;
import com.jiuji.oa.oacore.thirdplatform.order.vo.meituan.EcommerceOrderGetOrderPrivacyPhone;
import com.jiuji.oa.oacore.thirdplatform.order.vo.meituan.EcommerceOrderGetOrderPrivacyPhone;
import com.jiuji.oa.oacore.thirdplatform.order.vo.NationalSupplementUpSnReq;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.TenantService;
import com.jiuji.tc.utils.common.annotation.LogRecordAround;
import com.sankuai.meituan.shangou.open.sdk.domain.SystemParam;
import com.sankuai.meituan.shangou.open.sdk.dto.WmAppPoiDto;
import com.sankuai.meituan.shangou.open.sdk.exception.SgOpenException;
import com.sankuai.meituan.shangou.open.sdk.request.*;
import com.sankuai.meituan.shangou.open.sdk.response.SgOpenResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
public class MeiTuanSDKServiceImpl implements MeiTuanSDKService {
    @Resource
    private TenantService tenantService;

    @Resource
    private OrderService orderService;

    /**
     * 获取美团订单详情
     * @param detailBO
     * @return
     * @throws IOException
     * @throws SgOpenException
     */
    @Override
    @AddOrderLog(type = AddLogKind.MEITUAN_ORDER_DETAIL,returnType = SgOpenResponse.class)
    public SgOpenResponse getMeiTuanSgOpenResponse(SelectMeiTuanOrderDetailBO detailBO) throws IOException, SgOpenException {
        SystemParam systemParam = createSystemParam(detailBO.getOrderId());
        OrderGetOrderDetailRequest orderGetOrderDetailRequest = new OrderGetOrderDetailRequest(systemParam);
        orderGetOrderDetailRequest.setOrder_id(detailBO.getOrderId());
        orderGetOrderDetailRequest.setIs_mt_logistics(detailBO.getIsMtLogistics());
        return orderGetOrderDetailRequest.doRequest();
    }

    /**
     * 拣选完成之后同步美团
     * @param preparationMealCompleteBO
     * @return
     * @throws IOException
     * @throws SgOpenException
     */
    @Override
    @AddOrderLog(type = AddLogKind.MEITUAN_PICKING_COMPLETE,returnType = SgOpenResponse.class)
    public SgOpenResponse preparationMealComplete(PreparationMealCompleteBO preparationMealCompleteBO) throws IOException, SgOpenException {
        SystemParam systemParam = createSystemParam(preparationMealCompleteBO.getOrderId());
        OrderPreparationMealCompleteRequest request = new OrderPreparationMealCompleteRequest(systemParam);
        request.setOrder_id(preparationMealCompleteBO.getOrderId());
        return request.doRequest();
    }


    /**
     * 从美团获取取消原因
     * @param orderId
     * @return
     * @throws IOException
     * @throws SgOpenException
     */
    @Override
    @AddOrderLog(type = AddLogKind.ORDER_REFUND_DETAIL,returnType = SgOpenResponse.class)
    public SgOpenResponse cancelReason(String orderId) throws IOException, SgOpenException {
        SystemParam systemParam = createSystemParam(orderId);
        EcommerceOrderGetOrderRefundDetailRequest request = new EcommerceOrderGetOrderRefundDetailRequest(systemParam);
        request.setWm_order_id_view(Long.parseLong(orderId));
        return request.doRequest();
    }

    /**
     * 拉取号码
     * @param orderId
     * @return
     * @throws IOException
     * @throws SgOpenException
     */
    @Override
    @AddOrderLog(type = AddLogKind.BATCH_PULL_PHONE_NUMBER,returnType = SgOpenResponse.class)
    public SgOpenResponse batchPullPhoneNumber(String orderId) throws IOException, SgOpenException {
        SystemParam systemParam = createSystemParam(orderId);
        OrderBatchPullPhoneNumberRequest request = new OrderBatchPullPhoneNumberRequest(systemParam);
        request.setOffset(0);
        request.setLimit(1000);
        return request.doRequest();
    }

    /**
     * 从美团获取取消原因
     * @param orderId
     * @return
     * @throws IOException
     * @throws SgOpenException
     */
    @Override
    @AddOrderLog(type = AddLogKind.GET_ORDER_PRIVACY_PHONE,returnType = SgOpenResponse.class)
    public SgOpenResponse getOrderPrivacyPhone(String orderId) throws IOException, SgOpenException {
        SystemParam systemParam = createSystemParam(orderId);
        EcommerceOrderGetOrderPrivacyPhone request = new EcommerceOrderGetOrderPrivacyPhone(systemParam);
        request.setOrder_id(Convert.toLong(orderId));
        return request.doRequest();
    }

    /**
     * 同步门店营业时间到美团
     * @param createMeiTuanSystem
     * @return
     */
    @Override
    @AddOrderLog(type = AddLogKind.SYNCHRONIZATION_BUSINESS_HOURS,returnType = SgOpenResponse.class)
    public SgOpenResponse synchronizationBusinessHours(CreateMeiTuanSystem createMeiTuanSystem) throws IOException, SgOpenException {
        Tenant oneTenantBy = tenantService.getOneTenantBy("6900", ThirdPlatformCommonConst.THIRD_PLAT_MT);
        SystemParam systemParam = new SystemParam(oneTenantBy.getAppKey(), oneTenantBy.getAppSecret());
        PoiSaveRequest poiSaveRequest = new PoiSaveRequest(systemParam);
        WmAppPoiDto poiInfoDto = new WmAppPoiDto();
        poiInfoDto.setShipping_time(createMeiTuanSystem.getShippingTime());
        poiInfoDto.setApp_poi_code(createMeiTuanSystem.getStoreCode());
        poiInfoDto.setOpen_level(createMeiTuanSystem.getOpenLevel());
        poiSaveRequest.setPoi(poiInfoDto);
        return poiSaveRequest.doRequest();
    }


    @LogRecordAround(value = "美团国补订单查询信息")
    @Override
    public SgOpenResponse orderIdentificationGetRequest(String orderId) throws IOException, SgOpenException {
        SystemParam systemParam = SpringUtil.getBean(MeiTuanSDKService.class).createSystemParam(orderId);
        OrderIdentificationGetRequest orderIdentificationGetRequest = new OrderIdentificationGetRequest(systemParam);
        orderIdentificationGetRequest.setOrder_id(orderId);
        return orderIdentificationGetRequest.doRequest();
    }


    @LogRecordAround(value = "美团发票查询")
    @Override
    public SgOpenResponse queryTipsRequest(SelectInvoiceReq req) throws IOException, SgOpenException {
        SystemParam systemParam = SpringUtil.getBean(MeiTuanSDKService.class).createSystemParam(Convert.toStr(req.getOrder_id()));
        QueryTipsRequest queryTipsRequest = new QueryTipsRequest(systemParam);
        queryTipsRequest.setOrder_id(req.getOrder_id());
        queryTipsRequest.setApp_poi_code(req.getApp_poi_code());
        return queryTipsRequest.doRequest();
    }



    @LogRecordAround(value = "美团发票上传")
    @Override
    public SgOpenResponse saveInvoiceRequest(HandleInvoiceReq req) throws IOException, SgOpenException {
        SystemParam systemParam = SpringUtil.getBean(MeiTuanSDKService.class).createSystemParam(Convert.toStr(req.getOrder_id()));
        SaveInvoiceRequest saveInvoiceRequest = new SaveInvoiceRequest(systemParam);
        BeanUtil.copyProperties(req,saveInvoiceRequest);
        saveInvoiceRequest.setSpu_data(JSONUtil.toJsonStr(req.getSpuDataList()));
        return saveInvoiceRequest.doRequest();
    }


    @LogRecordAround(value = "美团国补订单上传信息")
    @Override
    public SgOpenResponse orderIdentificationSaveRequest(NationalSupplementUpSnReq req) throws IOException, SgOpenException {
        SystemParam systemParam = SpringUtil.getBean(MeiTuanSDKService.class).createSystemParam(req.getOrder_id());
        OrderIdentificationSaveRequest orderIdentificationGetRequest = new OrderIdentificationSaveRequest(systemParam);
        BeanUtil.copyProperties(req,orderIdentificationGetRequest);
        orderIdentificationGetRequest.setSpu_data(JSONUtil.toJsonStr(req.getSpuDataList()));
        return orderIdentificationGetRequest.doRequest();
    }

    /**
     * 获取美团sdk主体
     * @param orderId
     * @return
     */
    @Override
    public SystemParam createSystemParam(String orderId) {
        List<Order> list = orderService.lambdaQuery().eq(Order::getOrderId, orderId).or().eq(Order::getSubId,orderId).list();
        if(CollectionUtils.isEmpty(list)){
            throw new CustomizeException("美团订单查询为空，订单号:"+orderId);
        }
        //根据商户编码获取对应的 AppId  AppSecret
        Tenant oneTenantBy = Optional.ofNullable(tenantService.getOneTenantBy(list.get(0).getTenantCode(), ThirdPlatformCommonConst.THIRD_PLAT_MT))
                .orElseThrow(() -> new CustomizeException("美团数据库相关查询为空"));
        String meiTuanAppId = Optional.ofNullable(oneTenantBy.getTenantCode()).orElseThrow(() -> new CustomizeException("美团商户编码为空"));
        String meiTuanAppSecret = Optional.ofNullable(oneTenantBy.getAppSecret()).orElseThrow(() -> new CustomizeException("美团appSecret为空"));
        return new SystemParam(meiTuanAppId, meiTuanAppSecret);
    }
}
