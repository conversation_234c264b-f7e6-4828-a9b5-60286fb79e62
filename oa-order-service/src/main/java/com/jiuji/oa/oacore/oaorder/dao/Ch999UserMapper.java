package com.jiuji.oa.oacore.oaorder.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.oacore.oaorder.bo.Ch999UserBasicBO;
import com.jiuji.oa.oacore.oaorder.po.Ch999User;
import com.jiuji.oa.oacore.salary.bo.dto.SalaryBalanceInfoDto;
import com.jiuji.oa.oacore.tousu.bo.StaffPraiseBO;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 员工 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-03
 */
@Mapper
public interface Ch999UserMapper extends BaseMapper<Ch999User> {

    /**
     * 获取后台登录用户
     * @return 后台登录用户
     */
    List<Ch999User> getCh999Users(@Param("officeName") String officeName);

    Integer getCh999UerId(Integer subId);

    Integer getUerId(Integer subId);

    /**
     * 九机获取提成计算时该使用的xtenant
     *
     * @param ch999Id
     * @param month
     * @return
     */
    SalaryBalanceInfoDto getXtenantOfSalary(@Param("ch999Id") Integer ch999Id, @Param("month") String month, @Param("isJiuJi") boolean isJiuJi);

    /**
     * 根据工号获取员工基础信息
     * @param ch999Id
     * @return
     */
    Ch999UserBasicBO getCh999UserBasicBO(@Param("ch999Id") Integer ch999Id);

    /**
     * 根据ch99id获取用户名 包括离职的
     * @param ids
     * @return
     */
    @DS("oanew")
    List<Ch999User> getCh999UsersById(@Param("ids") List<Integer> ids);
    /**
     * 获取门店总人数
     * @return 总人数
     */
    @DS("oanew")
    Integer getUserCountByAreaId(@Param("areaId") Integer areaId);

    Ch999User getCh999UsersByAreaId(@Param("areaId") Integer areaId);

    /**
     * 获取门店员工列表信息
     * @param areaId
     * @return
     */
    List<Ch999User> listCh999UsersByAreaId(@Param("areaId") Integer areaId);

    /**
     * 生成投诉评价短链
     *
     * @param tsId
     * @return
     */
    String getComplaintEvaluateShortLink(@Param("tsId") Integer tsId);

    /**
     * 获取微信用户openId
     *
     * @param userId
     * @return
     */
    String getWxOpenIdByUserId(@Param("userId") Integer userId, @Param("wxId") Integer wxId);

    String selectPwdByCh999Id(@Param("userId")Integer userId);


    List<Integer> selectStoreManagerAndManager(@Param("userId") Integer userId);

    @DS("oanew")
    List<StaffPraiseBO> getStaffSimpleMessage(@Param("officeDbName")String officeDbName,@Param("staffIdList") Set<Integer> staffIdList);

    @DS("oanew")
    Integer countStaffPraiseRecord(@Param("officeDbName")String officeDbName,@Param("staffId") Integer staffId,@Param("rewardType")Integer rewardType);


    @DS("oanew")
    Integer countStaffCashReward(@Param("officeDbName")String officeDbName,@Param("staffId") Integer staffId,@Param("evaluateId") Integer evaluateId);

    @DS("oanew")
    List<StaffPraiseBO> getStaffPraiseRecord(@Param("officeDbName")String officeDbName,@Param("startRows")Integer startRows,@Param("size")Integer size,@Param("staffId") Integer staffId);

}
