package com.jiuji.oa.oacore.thirdplatform.order.vo.meituan;

import com.sankuai.meituan.shangou.open.sdk.domain.SystemParam;
import com.sankuai.meituan.shangou.open.sdk.request.SgOpenRequest;

public class EcommerceOrderGetOrderPrivacyPhone extends SgOpenRequest {
    private Long order_id;

    public EcommerceOrderGetOrderPrivacyPhone(SystemParam systemParam) {
        super("/api/v1/ecommerce/order/getOrderPrivacyPhone" +
                "", "GET", systemParam);
    }

    public void setOrder_id(Long order_id) {
        this.order_id = order_id;
    }

    public Long getOrder_id() {
        return this.order_id;
    }

}
