package com.jiuji.oa.oacore.oaorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.oaorder.dao.SysConfigMapper;
import com.jiuji.oa.oacore.oaorder.service.OaSysConfigService;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.sysconfig.vo.SysConfigVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * OA系统配置
 * <AUTHOR>
 */
@Service
@Slf4j
public class OaSysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfigVo> implements OaSysConfigService {

    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;

    @Resource
    private SysConfigClient sysConfigClient;
    @Override
    public List<SysConfigVo> getListByAuthorizeIdAndAreaId(Integer authorizeId, Integer areaId) {
        List<SysConfigVo> list = baseMapper.getListByAuthorizeId(authorizeId);
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return list.stream().filter(obj-> StringUtils.isBlank(obj.getAreaids())
                || CollectionUtils.isEmpty(CommonUtil.covertIdStr(obj.getAreaids()))
                || CommonUtil.covertIdStr(obj.getAreaids()).contains(areaId)).collect(Collectors.toList());
    }

    @Override
    public List<SysConfigVo> getListByAuthorizeId(Integer authorizeId) {
        return baseMapper.getListByAuthorizeId(authorizeId);
    }

    @DS("smallpro_write")
    @Override
    public String getListByCode(Integer code) {
        String url="";
        List<SysConfigVo> listByCodeAndXtenant = baseMapper.getListByCodeAndXtenant(code);
        if(CollectionUtils.isNotEmpty(listByCodeAndXtenant)){
            SysConfigVo sysConfigVo = listByCodeAndXtenant.get(0);
            url=sysConfigVo.getValue();
        }
        return url;
    }

    @Override
    public List<SysConfigVo> getPayWay() {
        List<SysConfigVo> list = baseMapper.getPayWay();
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return list;
    }

    @DS(DataSourceConstants.SMALLPRO_WRITE)
    @Override
    public boolean updateValueByCode(Integer code, String value) {

        return this.baseMapper.updateValueByCode(code, value);
    }

    /**
     * 查询sysconfig
     *
     * @param code
     * @return
     */
    @Override
    public List<SysConfigVo> getListByCoode(Integer code) {
        return this.baseMapper.getListByCoode(code);
    }

    @Override
    public String getValueByCodeAndXtenant(Integer code, Integer xTenant) {
        R<String> codeR = sysConfigClient.getValueByCodeAndXtenant(code, xTenant);
        if(!codeR.isSuccess()){
            log.warn("获取配置接口异常, 参数: code: {} xtenant: {}, 结果: {}", code, xTenant, JSON.toJSONString(codeR));
            return null;
        }
        return codeR.getData();
    }
}
