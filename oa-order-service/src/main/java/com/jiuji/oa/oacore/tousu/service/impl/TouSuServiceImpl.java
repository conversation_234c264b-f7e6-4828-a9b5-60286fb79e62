package com.jiuji.oa.oacore.tousu.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.vo.Result;
import com.jiuji.oa.nc.MessagePushCloud;
import com.jiuji.oa.oacore.cloud.IMCloud;
import com.jiuji.oa.oacore.common.bo.ZnSendConnBo;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.config.properties.ImageProperties;
import com.jiuji.oa.oacore.common.config.properties.JiujiSystemProperties;
import com.jiuji.oa.oacore.common.config.properties.SmsProperties;
import com.jiuji.oa.oacore.common.constant.UrlConstant;
import com.jiuji.oa.oacore.common.enums.EStatsEnum;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.res.PageRes;
import com.jiuji.oa.oacore.common.source.InwcfSource;
import com.jiuji.oa.oacore.common.source.MoaUrlSource;
import com.jiuji.oa.oacore.common.source.appIndex.AppIndexWwwUrlSource;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.oaorder.bo.ColorBO;
import com.jiuji.oa.oacore.oaorder.po.Ch999User;
import com.jiuji.oa.oacore.oaorder.service.Ch999UserService;
import com.jiuji.oa.oacore.oaorder.service.OaSysConfigService;
import com.jiuji.oa.oacore.tousu.bo.TouSuProcessBO;
import com.jiuji.oa.oacore.tousu.bo.Weixin;
import com.jiuji.oa.oacore.tousu.bo.ZeRenRen;
import com.jiuji.oa.oacore.tousu.constant.ComplainConstant;
import com.jiuji.oa.oacore.tousu.dao.TouSuMapper;
import com.jiuji.oa.oacore.tousu.dao.TousuAreaMapper;
import com.jiuji.oa.oacore.tousu.enums.TouSuTypeEnum;
import com.jiuji.oa.oacore.tousu.po.*;
import com.jiuji.oa.oacore.tousu.res.TouSuProcess;
import com.jiuji.oa.oacore.tousu.res.*;
import com.jiuji.oa.oacore.tousu.service.ReciversService;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.oacore.tousu.service.TouSuService;
import com.jiuji.oa.oacore.tousu.service.TsProcessService;
import com.jiuji.oa.oacore.tousu.vo.res.AddTouSuResult;
import com.jiuji.oa.oacore.tousu.vo.res.ComplaintPvPage;
import com.jiuji.oa.oacore.tousu.vo.res.ComplaintPvRes;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.departinfo.client.DepartInfoClient;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.sysconfig.vo.SysConfigVo;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Description 添加投诉
 * <AUTHOR>
 * @Date 2020/8/27 14:15
 * @See .net:oa999Services.userServices.addTouSu
 */
@Slf4j
@Service
@DS("office")
public class TouSuServiceImpl extends ServiceImpl<TouSuMapper, TouSuModel> implements TouSuService {
    @Resource
    private SmsProperties smsProperties;

    @Autowired(required = false)
    private TouSuMapper touSuMapper;
    @Autowired(required = false)
    private MoaUrlSource moaUrlSource;
    @Autowired
    private ReciversService reciversService;
    @Autowired
    private JiujiSystemProperties jiujiSystemProperties;
    @Autowired
    private ImageProperties imageProperties;
    @Resource
    private TousuAreaMapper tousuAreaMapper;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Autowired
    private UserInfoClient userInfoClient;
    @Resource
    private IMCloud imCloud;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private MessagePushCloud messagePushCloud;
    @Resource
    private AppIndexWwwUrlSource wwwUrlSource;
    @Resource
    private InwcfSource inwcfSource;
    @Autowired
    private SmsService smsService;
    @Autowired
    private DepartInfoClient departInfoClient;
    @Resource
    private Ch999UserService ch999UserService;
    @Resource
    private TsProcessService tsProcessService;
    @Resource
    private AbstractCurrentRequestComponent requestComponent;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private OaSysConfigService oaSysConfigService;

    @Override
    public AddTouSuResult addTouSu(TouSuModel ts) {
        AddTouSuResult info = new AddTouSuResult();
        //经常以获取奖励目的发起投诉，屏蔽
        List<Integer> userIds = Arrays.asList(1532849, 2934090, 1455034, 958396, 3453496);
        if ("13547937664".equals(ts.getMobile()) || "18314533122".equals(ts.getMobile()) ||
                "13978642368".equals(ts.getMobile()) || userIds.contains(ts.getUserId())) {
            info.setStats(1);
            info.setResult("添加成功");
            return info;
        }
        if (ts.getUserId() != null && ts.getUserId() != 0) {
            Integer touSuId = touSuMapper.getTouSuId(ts.getUserId());
            if (touSuId != null && touSuId > 0) {
                info.setStats(2);
                info.setResult(String.valueOf(touSuId));
                return info;
            }
        }
        ts.setContent(ts.getContent().replace("<", "").replace(">", ""));
        int tsCount = touSuMapper.getTouSuCount(ts.getMobile(), ts.getContent());
        if (tsCount > 0) {
            info.setStats(0);
            info.setResult("您已经提交过相同投诉，无需重复提交！");
            return info;
        }
        ts.setHuanyuantimeout(getTimeout(1));
        ts.setDealTimeout(getTimeout(2));
        ts.setAreaId(areaInfoClient.getAreaInfoByArea(ts.getArea()).getData().getId());
        Integer touSuId = touSuMapper.addTouSu(ts);
        if (touSuId == 0) {
            info.setStats(0);
            info.setResult("添加失败");
        } else {
            String userClassName = "";
            if (ts.getUserId() != null && ts.getUserId() > 0) {
                int userClassType = touSuMapper.getUserClass(ts.getUserId());
                userClassName = CommonUtil.getUserType(userClassType);
            }
            if (!ts.getContent().equals("开发中心测试")) {
                String areaName = touSuMapper.getAreaListByArea(ts.getArea()).get(0).getArea_name();
                String msg = "会员名称:" + ts.getWriter() + " \n电话:" + ts.getMobile() + (!userClassName.isEmpty() ? "（" + userClassName + "）" : "") + "\n店面：" + areaName
                        + "\n编号：<a href='" + moaUrlSource.getBasicUrl() + "/mtousu/detail?id=" + touSuId + "'>" + touSuId + "</a>\n投诉时间:"
                        + DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm").format(LocalDateTime.now()) + "\n投诉内容：" +
                        (ts.getContent().length() > 300 ? ts.getContent().substring(0, 300) : ts.getContent()) + "";
                List<Receiver> smsReceiverList = touSuMapper.smsReceiverList("1");
                String areaTemp = ts.getArea().replace("a", "");
                List<String> byDepartId = touSuMapper.getDepartIdList(areaTemp);
                List<AreaInfoModel> areaList = touSuMapper.loadAllAreaInfo();
                List<ReceiverCache> userids = reciversService.GetReceiverIds(smsReceiverList, "1", 0, byDepartId, smsReceiverList.get(0).getAreaids(), areaList);
                List<Integer> user = touSuMapper.getCh999_user();
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < userids.size(); i++) {
                    if (userids.get(i).getCh999_id() != 0) {
                        if (i == userids.size() - 1) {
                            sb.append(userids.get(i).getCh999_id());
                        } else {
                            sb.append(userids.get(i).getCh999_id()).append(',');
                        }
                    }
                }
                for (int i = 0; i < user.size(); i++) {
                    if (user.get(i) != 0) {
                        if (i == user.size() - 1) {
                            sb.append(user.get(i));
                        } else {
                            sb.append(user.get(i)).append(',');
                        }
                    }
                }
                //TODO 测试阶段暂时标题暂时写死，等上线的时候在改
                if (StringUtils.isNotEmpty(sb.toString())) {
                    messagePushCloud.pushQyWeiXinMessageAsync(sb.toString(), "8", "开发中心测试", msg, null, null);
                    log.error("TouSuService.addTouSu:发送消息：消息接受者：userids:" + sb.toString());
                    messagePushCloud.pushOaMessageAsync(sb.toString(), "开发中心测试", "https://moa.9ji.com/mtousu/detail?id=" + ts.getId());
                    log.error("TouSuService.addTouSu:发送消息：消息接受者：userids:" + sb.toString());
                }
            }
            info.setStats(1);
            info.setResult("添加成功");
        }
        return info;
    }


    // 获取处理超时时间和 已还原超时时间
    // <param name="kind">1 已还原超时时间， 2 处理超时时间</param>
    public LocalDateTime getTimeout(int kind) {
        LocalDateTime date = LocalDateTime.now().plusDays(1).plusSeconds(-1);
        LocalDateTime addTime = LocalDateTime.now();
        if (kind == 1) {//已还原超时时间
            DayOfWeek week = addTime.getDayOfWeek();
            if (week == DayOfWeek.SATURDAY || week == DayOfWeek.SUNDAY) {
                if (addTime.getHour() > 11 && (addTime.getHour() < 21 || (addTime.getHour() == 21 && addTime.getMinute() <= 30))) {
                    date = addTime.plusMinutes(50);
                } else {
                    date = addTime.plusDays(1).plusHours(11).plusMinutes(50);
                }
            } else {
                if (addTime.getHour() >= 9 && (addTime.getHour() < 21 || (addTime.getHour() == 21 && addTime.getMinute() <= 30))) {
                    date = addTime.plusMinutes(50);
                } else {
                    date = addTime.plusDays(1).plusHours(11).plusMinutes(50);
                }
                LocalDateTime mt = addTime.plusDays(1).plusHours(11);
                if (week == DayOfWeek.FRIDAY &&
                        (addTime.getHour() > 21 || (addTime.getHour() == 21 && addTime.getMinute() > 30)) && addTime.isBefore(mt)) {
                    date = mt.plusMinutes(50);
                }

            }
        } else if (kind == 2) {
            //处理超时时间
            if (addTime.getHour() >= 9 && (addTime.getHour() < 21 || (addTime.getHour() == 21 && addTime.getMinute() <= 30))) {
                date = addTime.plusHours(60);
            } else {
                date = addTime.plusDays(1).plusHours(57); //次日9点加48小时
            }
        }
        return date;
    }

    @Override
    public PageRes<TouSuModelRes> touSuList(long userid, int curPage, int rows) {
        IPage<TouSuModelRes> page = new Page<>(curPage, rows);

        IPage<TouSuModelRes> touSuResIPage = touSuMapper.listTouSuRes(page, jiujiSystemProperties.getOfficeName(), userid);
        PageRes<TouSuModelRes> pageRes = new PageRes<>();
        List<TouSuModelRes> records = touSuResIPage.getRecords();


        pageRes.setRecords(records);
        pageRes.setCurrent((int) touSuResIPage.getCurrent());
        pageRes.setSize((int) touSuResIPage.getSize());
        pageRes.setTotal((int) touSuResIPage.getTotal());
        pageRes.setPages((int) touSuResIPage.getPages());

        List<TouSuModelRes> list = pageRes.getRecords();
        List<Integer> ids = list.stream().map(TouSuModelRes::getId).collect(Collectors.toList());
        List<TouSuProcess> userIds = touSuMapper.getUserIds(jiujiSystemProperties.getOfficeName(), ids);
        if (list.size() > 0) {
            for (TouSuModelRes t : list) {
                EStatsEnum[] statsEnums = EStatsEnum.values();
                for (EStatsEnum e : statsEnums) {
                    if (t.getStates_() == e.getCode()) {
                        t.setStatsName(e.getMessage());
                    }
                }
                List<TouSuProcess> collect = userIds.stream().filter(item -> item.getTsID() == t.getId()).sorted(Comparator.comparing(TouSuProcess::getIntime).reversed()).collect(Collectors.toList());
                for (TouSuProcess touSuProcess : collect
                ) {
                    touSuProcess.setOpUser(touSuProcess.getTsID() + "");
                }
                t.setProcessLog(collect);
                R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(t.getAreaid());
                if (ResultCode.SUCCESS == areaInfoR.getCode()) {
                    AreaInfo areaInfo = areaInfoR.getData();
                    t.setArea(areaInfo.getArea());
                    t.setAreaName(areaInfo.getAreaName());
                }
            }
            pageRes.setRecords(list);
        }
        return pageRes;
    }

    @Override
    public TouSuDetail touSuDetail(long userid, int id) {
        TouSuDetail info = new TouSuDetail();
        TouSuModelRes touSuModel = touSuMapper.getTouSuModelByUserIdAndId(jiujiSystemProperties.getOfficeName(), userid, id);
        TouSuResult result = new TouSuResult();
        if (touSuModel != null) {
            result.setStats(1);
            info.setResult(result);
            touSuModel.setUserclassName(CommonUtil.getUserType(touSuModel.getUserclass()));
            touSuModel.setTouSuTypeDes(new ArrayList<>());

            EStatsEnum[] statsEnums = EStatsEnum.values();
            for (EStatsEnum e : statsEnums) {
                if (touSuModel.getStates_() == e.getCode()) {
                    touSuModel.setStatsName(e.getMessage());
                }
            }

            if (StringUtils.isNotEmpty(touSuModel.getTouSuTypeIds())) {
                TouSuTypeEnum[] touSuTypeEnums = TouSuTypeEnum.values();
                for (TouSuTypeEnum touSuTypeEnum : touSuTypeEnums) {
                    if (touSuModel.getTouSuTypeIds().equals(touSuTypeEnum.getCode() + "")) {
                        touSuModel.getTouSuTypeDes().add(touSuTypeEnum.getMessage());
                    }
                }
            } else {
                touSuModel.setTouSuTypeIds("");
            }
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(touSuModel.getAreaid());
            if (ResultCode.SUCCESS == areaInfoR.getCode()) {
                AreaInfo areaInfo = areaInfoR.getData();
                touSuModel.setArea(areaInfo.getArea());
                touSuModel.setAreaName(areaInfo.getAreaName());
            }

            touSuModel.setProcessLog(new ArrayList<>());
            List<TouSuProcess> touSuProcessList = touSuMapper.getTouSuProcessBytsID(jiujiSystemProperties.getOfficeName(), touSuModel.getId());
            for (TouSuProcess touSuProcess : touSuProcessList) {
                touSuProcess.setAttachments(new ArrayList<>());
                touSuProcess.setOpUser(touSuProcess.getCh999_id() + "");
                String attachFiles = touSuProcess.getAttachFiles();
                List<Integer> ids;
                String tousupic = touSuModel.getTousupic();
                if (StringUtils.isNotEmpty(attachFiles) || StringUtils.isNotEmpty(tousupic)) {
                    String[] picIds = attachFiles.trim().split(",");
                    ids = Arrays.stream(picIds).mapToInt(Integer::parseInt).filter(p -> p > 0).boxed().collect(Collectors.toList());
                    List<AttachmentsRes> attachmentsResList = touSuMapper.getTouSuPicByIds(ids);
                    for (AttachmentsRes pic : attachmentsResList) {
                        pic.setFilepath(imageProperties.getSelectImgUrl() + "/newstatic/" + pic.getFid().replace(",", "/"));
                    }
                    touSuProcess.setAttachments(attachmentsResList);
                    touSuModel.setAttachments(attachmentsResList);
                }
                touSuModel.getProcessLog().add(touSuProcess);
            }
            List<TouSuZeRenRen> suZeRenRenList = touSuMapper.getTouSuZeRenRenById(jiujiSystemProperties.getOfficeName(), id);

            for (TouSuZeRenRen touSuZeRenRen : suZeRenRenList
            ) {
                R<AreaInfo> areaInfo = areaInfoClient.getAreaInfoById(touSuZeRenRen.getArea1id());
                if (ResultCode.SUCCESS == areaInfo.getCode()) {
                    AreaInfo areaInf = areaInfo.getData();

                    touSuZeRenRen.setArea_name(areaInf.getAreaName());
                }
            }
            touSuModel.setTouSuZeRenRens(suZeRenRenList);
            info.setDetail(touSuModel);
        } else {
            result.setStats(0);
            result.setResult("查无相关记录");
            info.setResult(result);
        }
        return info;
    }

    @Override
    public TouSuResult touSuAddQuestion(long userid, int id, String content, String attachFiles) {
        TouSuResult result = new TouSuResult();
        if (StringUtils.isEmpty(content)) {
            result.setStats(0);
            result.setResult("内容不能为空");
        }
        //屏蔽客户投诉追问
        if (userid == 5140556) {
            result.setStats(1);
            return result;
        }
        ProcessUser processUser = touSuMapper.getProcessUser(userid, id);
        if (processUser != null) {
            if (processUser.getStates_() == 3) {
                result.setStats(0);
                result.setResult("仅处理中的投诉可以追问");
                return result;
            }
            TouSuProcessBO touSuProcess = new TouSuProcessBO();
            touSuProcess.setTsId(id);
            touSuProcess.setOpUser("系统");
            touSuProcess.setShow(true);
            touSuProcess.setDsc(content + "<span style='color:red;'>*客户追问</span>");
            touSuProcess.setAttachFiles(StringUtils.isEmpty(attachFiles) ? "" : attachFiles);
            touSuProcess.setCate(1);
            touSuProcess.setCateContent(content);
            addTsProcess(touSuProcess, null);
            result.setStats(1);
            notifyTousuQuestion(id, content, processUser.getProcessUser());
        } else {
            result.setStats(0);
            result.setResult("查无相关记录");
        }
        return result;
    }

    //客户追问时，推送给客评组所有成员
    @DS("officeWrite")
    private void notifyTousuQuestion(int tousuId, String content, String processUser) {
        List<Integer> userIds = getCh999Ids();
        String userId = userIds.toString();
        userId = userId.replaceAll("(?:\\[|null|\\]| +)", "");
        if (userIds != null && userIds.size() > 0) {
            String url = "https://moa.9ji.com/mtousu/detail/" + tousuId + "";
            String idContent = "<a href='https://moa.9ji.com/mtousu/detail/" + tousuId + "" + "'>【" + tousuId + "" + "】</a>";
            String msg = "投诉ID" + idContent + "客户追问：【" + content + "】";
            if (StringUtils.isNotEmpty(processUser)) {
                msg = "投诉ID" + idContent + "客户追问：【" + content + "】" + processUser + "【投诉跟进人：" + processUser + "】";
            }
            messagePushCloud.pushOaMessagePost(userId + "", msg, url);
        }
    }

    @Cached(name = "CACHE_KEPINGZU",
            expire = 24, timeUnit = TimeUnit.HOURS, cacheType = CacheType.BOTH)
    public List<Integer> getCh999Ids() {
        return touSuMapper.getCh999_id();
    }

    // 添加投诉处理进度
    @Override
    public int addTsProcess(TouSuProcessBO process, TouSuModel model) {
//        if (process.getShow() != null && process.getShow() && model != null) {
//            //网站显示
//            //添加的进程会同时推送给客户的微信和站内信（如未绑定微信则只推站内信）
//            pushMsg(process.getTsId());
//        }
        if (process.getNotice() != null && process.getNotice() && StringUtils.isNotEmpty(process.getNoticeUser())) {
            String noticeType = "";
            if (process.getWeixin() != null && process.getWeixin()) {
                sendWx(process, model);
                noticeType += " 微信 ";
            }
            if (process.getTongzhi() != null && process.getTongzhi()) {
                sendEmail(process, model);
                noticeType += " 内部邮件 ";
            }
            if (process.getDuanxin() != null && process.getDuanxin()) {
                sendMessage(process, model);
                noticeType += " 短信 ";
            }
            if (StringUtils.isNotEmpty(noticeType)) {
                noticeType = "(" + noticeType + "已对接通知：" + process.getNoticeUser() + ")";
            }
            String dsc = process.getDsc();
            process.setDsc(dsc += noticeType);
        }
        if (StringUtils.isEmpty(process.getAttachFiles())) {
            process.setAttachFiles("");
        }
        TsProcess tsProcess = new TsProcess();
        BeanUtils.copyProperties(process, tsProcess);
        tsProcess.setIsShow(process.getShow());
        tsProcess.setIntime(LocalDateTime.now());
        tsProcessService.saveProcess(tsProcess);
//        touSuMapper.saveTouSuProcess(process);
        return tsProcess.getId();
    }

    @Override
    public ComplaintPvPage getComplaintPV(String url, Long current, Long size) {
        R<ComplaintPvPage> complaintPvPageResult;
        String result = null;
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("purl", url);
            params.put("pageNum", current);
            params.put("pageSize", size);
            result = HttpRequest
                    .post(ComplainConstant.COMPLAIN_VIEW_URL)
                    .header("xservicename", "data-api")
                    .body(JSON.toJSONString(params), "application/json")
                    .execute().body();

            complaintPvPageResult = JSON.parseObject(result, new TypeReference<R<ComplaintPvPage>>() {
            });

            if (complaintPvPageResult.getCode() != Result.SUCCESS && complaintPvPageResult.getData() == null) {
                return new ComplaintPvPage();
            }

        } catch (Exception e) {
            log.error("查询投诉记录失败，result：{}", e.getMessage(), result);
            throw new CustomizeException("查询投诉记录失败！");
        }

        ComplaintPvPage data = complaintPvPageResult.getData();

        if (CollectionUtils.isNotEmpty(data.getRecordList())) {
            List<Integer> userIds = data.getRecordList().stream().map(ComplaintPvRes::getUserId).distinct().collect(Collectors.toList());
            List<Ch999User> ch999Users = ch999UserService.getCh999UsersById(userIds);
            if (CollectionUtils.isNotEmpty(ch999Users)) {
                Map<Integer, Ch999User> map = ch999Users.stream().collect(Collectors.toMap(Ch999User::getCh999Id, Function.identity(), (v1, v2) -> v1));
                data.getRecordList().stream().forEach((ComplaintPvRes res) -> {
                    res.setUserName(map.containsKey(res.getUserId()) ? map.get(res.getUserId()).getCh999Name() : "");
                });
            }
        }

        return data;
    }

    @Override
    @DS("officeWrite")
    public boolean updateTousuToShowWeb(Integer tsId) {
        TouSuModel touSuModel = getById(tsId);
        if (Objects.isNull(touSuModel)
                || Boolean.TRUE.equals(touSuModel.getShowWeb())) {

            return false;
        }
        touSuModel.setShowWeb(true);
        return updateById(touSuModel);
    }

    @Override
    @DS("officeWrite")
    public boolean updateTsDealUser(Integer tsId) {
        TouSuModel touSuModel = getById(tsId);
        if (Objects.isNull(touSuModel)) {
            return false;
        }
        LambdaUpdateWrapper<TouSuModel> lambdaUpdate = Wrappers.<TouSuModel>lambdaUpdate()
                .set(TouSuModel::getDealUser, org.apache.commons.lang.StringUtils.EMPTY)
                .eq(TouSuModel::getId, touSuModel.getId());
        return this.update(lambdaUpdate);
    }

    @Override
    public List<ZeRenRen> getCommentPraise(Integer evaluateId) {
        return this.getBaseMapper().getCommentPraise(evaluateId);
    }

    public void sendMessage(TouSuProcessBO process, TouSuModel model) {
        try {
            R<Ch999UserVo> userR = userInfoClient.getCh999UserByUserName(process.getNoticeUser());
            if (userR.getCode() == ResultCode.SUCCESS && userR.getData() != null) {
                String mobile = userR.getData().getMobile();
                String memberName = StringUtils.isEmpty(model.getMemberName()) ? model.getMobile() : model.getMemberName();
                if (mobile.length() == 11 && mobile.startsWith("1")) {
                    String dsc = process.getDsc().replace("<span style='color:red;'>", "").replace("</span>", "");
                    String content = "投诉ID：" + model.getId() + ",会员：" + memberName + ",联系号码：" + model.getMobile() + "(" + (dsc.length() > 300 ? dsc.substring(0, 300) : dsc) + ") 【" + process.getOpUser() + "】";
                    Integer xtenant = 0;
                    if (model.getUserId() != null && model.getUserId() != 0) {
                        xtenant = tousuAreaMapper.getXtenant(model.getUserId());
                    }
                    smsService.sendSms(mobile, xtenant, content, LocalDateTime.now().toString(), "系统");
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }
    // 发送邮件

    public void sendEmail(TouSuProcessBO process, TouSuModel model) {
        try {
            String content = process.getDsc().replace("<span style='color:red;'>", "").replace("</span>", "");
            String memberName = StringUtils.isEmpty(model.getMemberName()) ? model.getMobile() : model.getMemberName();
            String emailContent = "投诉ID：" + model.getId()
                        + " ，会员：" + memberName + "，联系号码：" + model.getMobile() + "(" + content + ") (" + process.getOpUser() + ")";
                String emailTitle = "投诉ID：" + model.getId() + "，" + (content.length() > 30 ? content.substring(0, 30) :content);
                smsService.sendEmail(process.getNoticeUser(), emailTitle, emailContent);
//            ZnSendConnBo znSendConnBo = new ZnSendConnBo();
//            znSendConnBo.setKind(12);
//            znSendConnBo.setSmsnumber(process.getCh999Id() + "");
//            znSendConnBo.setTitle("投诉处理通知");
//            znSendConnBo.setContent(emailContent);
//            znSendConnBo.setLink(url);
//                smsService.sendZnMsg(znSendConnBo);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }
    // 发送微信

    public void sendWx(TouSuProcessBO process, TouSuModel model) {
        String memberName = StringUtils.isEmpty(model.getMemberName()) ? model.getMobile() : model.getMemberName();
        Integer ch999Id = process.getCh999Id();
        String dsc = process.getDsc().replace("<span style='color:red;'>", "").replace("</span>", "");
        String content = "投诉ID：" + model.getId() + ",会员：" + memberName+ ",联系号码：" + model.getMobile() + "(" + (dsc.length() > 300 ? dsc.substring(0, 300) : process.getDsc()) + ") 【" + process.getOpUser() + "】";
        String link = moaUrlSource.getBasicUrl() + "/new/#/operation/complaint/links?conplaintid=" + model.getId();
        smsService.sendOaAppAndWeiXing(content, ch999Id + "", 9, link);
    }
    // 根据平台 租户 获取基本信息

    public XtenantSubject getXtenantSubject(Integer xtenant) {
        XtenantSubject xtenantSubject = new XtenantSubject();
        xtenantSubject.setSmsChannel(CommonUtil.getSmsChannelByXtenant(xtenant, 1) + "");
        xtenantSubject.setSmsChannelMarketing(CommonUtil.getSmsChannelByXtenant(xtenant, 2) + "");
        xtenantSubject.setMUrl(CommonUtil.getUrlByXtenant(xtenant, 1));
        xtenantSubject.setWebUrl(CommonUtil.getUrlByXtenant(xtenant, 2));
        xtenantSubject.setHsUrl(CommonUtil.getUrlByXtenant(xtenant, 3));
        return xtenantSubject;
    }

    // 投诉进度推送
    public void pushMsg(int tousuId) {
        User user = touSuMapper.getUserById(jiujiSystemProperties.getOfficeName(), tousuId);
        if (user != null) {
            byte xtenant = user.getXtenant();
            XtenantSubject xtenantSubject = getXtenantSubject((int) xtenant);
            String url = xtenantSubject.getMUrl() + "/member/complaint/mine/" + tousuId;
            String title = "投诉处理通知";
            String msg = "您的投诉有了新的处理进程，点击查看";
            Weixin weixin = touSuMapper.getMyWx(user.getUserid());
            ZnSendConnBo znSendConnBo = new ZnSendConnBo();
            znSendConnBo.setKind(12);
            znSendConnBo.setSmsnumber(user.getUserid() + "");
            znSendConnBo.setTitle(title);
            znSendConnBo.setContent(msg);
            znSendConnBo.setLink(url);
            if (weixin != null) {
                String openid = weixin.getOpenid();
                if (StringUtils.isNotEmpty(openid)) {
                    DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    String datetime = LocalDateTime.now().format(df);
                    sendMsgTousu(openid, url, title, tousuId + "", msg, datetime, 0, "");
                } else if (StringUtils.isNotEmpty(user.getMobile())) {
                    smsService.sendSms(user.getMobile(), msg, smsService.getShortUrl((long) xtenant, url, ""), 0, "系统");
                }
            }
            smsService.sendZnMsg(znSendConnBo);
        }
    }

    public String sendMsgTousu(String openid, String url, String first, String keyword1, String keyword2, String datetime, Integer type, String remark) {
        type = type == null ? 0 : type;
        String color;
        switch (type) {
            case 1:
                color = "#ff0000";
                break;
            case 2:
                color = "#00ff00";
                break;
            default:
                color = "#173177";
                break;
        }
        String tempId = "3GNB40NYrY-3yA9hLgniYTuVIzO2VPdh9mk4thKThC0";
        String result = "";
        String topcolor = "#666";
        if (StringUtils.isNotEmpty(tempId)) {
            JSONObject request = new JSONObject();
            request.put("first", new ColorBO(first, topcolor));
            request.put("keyword1", new ColorBO(keyword1, color));
            request.put("keyword2", new ColorBO(keyword2, topcolor));
            request.put("keyword3", new ColorBO(datetime, topcolor));
            request.put("remark", new ColorBO(remark, topcolor));
            result = sendTemplateMsg(openid, tempId, url, request, "#0048a3");
        }
        return result;
    }

    public String sendTemplateMsg(String openid, String templateid, String url, JSONObject data, String topcolor) {
        if (StringUtils.isEmpty(topcolor)) {
            topcolor = "#0048a3";
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("touser", openid);
        map.put("template_id", templateid);
        map.put("url", url);
        map.put("topcolor", topcolor);
        map.put("data", data);
        String accessToken = smsService.getAccessToken();
        String jsonStr = JSONObject.toJSONString(map);
        String sendMsgUrl = String.format(UrlConstant.SEND_MSG, accessToken);
        return HttpUtil.post(sendMsgUrl, jsonStr);
    }

    @Override
    public R<Boolean> modifyBusinessFlowChart(String image) {
        R<List<SysConfigVo>> sysConfigsResult = sysConfigClient.getSysConfigList(Collections.singletonList(SysConfigConstant.TOUSU_BUSINESS_FLOW_CHART), null);
        if (Objects.isNull(sysConfigsResult) || !sysConfigsResult.isSuccess()) {

            return R.error("修改失败，请联系管理员");
        }
        List<SysConfigVo> configs = sysConfigsResult.getData();
        if (CollectionUtils.isEmpty(configs)) {
            // 没有配置，新增一个
            SysConfigVo sysConfigVo = new SysConfigVo();
            sysConfigVo.setName("投诉管理-业务流程图");
            sysConfigVo.setCode(SysConfigConstant.TOUSU_BUSINESS_FLOW_CHART);
            sysConfigVo.setDsc("oa使用，用于替换投诉详情的业务流程图，可在投诉管理页编辑");
            sysConfigVo.setValue(image);
            R<Boolean> saveResult = sysConfigClient.saveConfig(sysConfigVo);
            if (Objects.nonNull(saveResult) && saveResult.isSuccess()) {
                return R.success("修改成功");
            }
            return R.error("修改失败，请联系管理员");
        }
        boolean updateResult = oaSysConfigService.updateValueByCode(SysConfigConstant.TOUSU_BUSINESS_FLOW_CHART, image);
        if (updateResult) {
            return R.success("修改成功", true);
        }
        return R.error("修改失败，请联系管理员");
    }

    @Override
    public TouSuModel getComplainById(Integer id) {
        return this.getById(id);
    }


}
