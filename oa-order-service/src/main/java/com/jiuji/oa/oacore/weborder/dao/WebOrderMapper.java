package com.jiuji.oa.oacore.weborder.dao;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.jiuji.oa.oacore.common.enums.EvaluateTypeEnum;
import com.jiuji.oa.oacore.weborder.bo.*;
import com.jiuji.oa.oacore.weborder.req.IWebOrderQuery;
import com.jiuji.oa.oacore.weborder.req.WebAfterQueryReq;
import com.jiuji.oa.oacore.weborder.vo.*;
import com.jiuji.oa.oacore.weborder.vo.req.MyClientReq;
import com.jiuji.oa.oacore.weborder.vo.req.WhetherToBuyReq;
import com.jiuji.oa.oacore.weborder.vo.res.OrderStateRes;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @since 2020/4/9
 */
@Mapper
public interface WebOrderMapper {


    Integer selectRetreatCount(@Param("userId") Integer userId);

    //新机-订单量-新需求加了状态筛选,其他地方也加了参数
    Integer countNormal(@Param("userId") Integer userId, @Param("xtenant") Integer xtenant
            , @Param("tag") String tag, @Param("keyWord") String keyWord, @Param("isHis") Boolean isHis,
                        @Param("searchId") Integer searchId, @Param("distribution") Boolean distribution, @Param("status") List<Integer> status
            , @Param("orderLinkFlag") Integer orderLinkFlag, @Param("queryHistoryOrder") Integer queryHistoryOrder, @Param("hideOrder") Integer hideOrder
            , @Param("req") IWebOrderQuery req);

    //新机-订单量-历史订单，缓存6分钟-新需求加了状态筛选,防止其他地方有问题重新加了个方法
    @DS("oanew_his")
    Integer countNormalHis(@Param("userId") Integer userId, @Param("xtenant") Integer xtenant
            , @Param("tag") String tag, @Param("keyWord") String keyWord, @Param("isHis") Boolean isHis,
                           @Param("searchId") Integer searchId, @Param("distribution") Boolean distribution, @Param("status") List<Integer> status
            , @Param("orderLinkFlag") Integer orderLinkFlag, @Param("queryHistoryOrder") Integer queryHistoryOrder, @Param("hideOrder") Integer hideOrder
            , @Param("req") IWebOrderQuery req);

    //新机-订单详情
    List<XinjiSubBO> listNormal(@Param("userId") Integer userId
            , @Param("xtenant") Integer xtenant, @Param("tag") String tag
            , @Param("keyWord") String keyWord, @Param("searchId") Integer searchId, @Param("isHis") Boolean isHis
            , @Param("startRows") Integer startRows, @Param("size") Integer size, @Param("distribution") Boolean distribution
            , @Param("status") List<Integer> status, @Param("orderLinkFlag") Integer orderLinkFlag
            , @Param("queryHistoryOrder") Integer queryHistoryOrder, @Param("hideOrder") Integer hideOrder
            , @Param("req") IWebOrderQuery req);

    //新机-订单详情-历史库
    @DS("oanew_his")
    @Cached(name = "orderservice:WebOrderMapper:listNormalHis", expire = 1, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.LOCAL)
    List<XinjiSubBO> listNormalHis(@Param("userId") Integer userId
            , @Param("xtenant") Integer xtenant, @Param("tag") String tag
            , @Param("keyWord") String keyWord, @Param("searchId") Integer searchId, @Param("isHis") Boolean isHis
            , @Param("startRows") Integer startRows, @Param("size") Integer size, @Param("distribution") Boolean distribution
            , @Param("status") List<Integer> status, @Param("orderLinkFlag") Integer orderLinkFlag
            , @Param("queryHistoryOrder") Integer queryHistoryOrder, @Param("hideOrder") Integer hideOrder
            , @Param("req") IWebOrderQuery req);

    //新机-订单状态详情
    List<XinjiSubBO> listNormalStatusBySubId(@Param("subIds") List<Integer> subIds);

    //新机-订单状态详情-历史库
    @DS("oanew_his")
    List<XinjiSubBO> listNormalStatusBySubIdHis(@Param("subIds") List<Integer> subIds);

    List<Integer> listDelCollectBySubIdAndType(@Param("subIds") List<Integer> subIds, @Param("type") Integer type);

    //新机-订单 - 商品详情
    List<XinjiProductBO> listProductNormal(@Param("subIds") List<Integer> subIds);

    //新机-订单 - 商品详情-历史库
    @DS("oanew_his")
    List<XinjiProductBO> listProductNormalHis(@Param("subIds") List<Integer> subIds);

    //获取已评价的订单id
    List<Integer> checkEvaluatedInType(@Param("subIds") List<Integer> subIds,
                                       @Param("typeList") List<EvaluateTypeEnum> typeList,
                                       @Param("officeName") String officeName);

    List<Integer> checkEvaluatedByType(@Param("subIds") Collection<Integer> subIds, @Param("type") EvaluateTypeEnum type,
                                       @Param("officeName") String officeName);

    //获取回收订单
    List<HuiShouSubBO> listHuishouByRecoverSubId(@Param("recoverSubIds") List<Integer> recoverSubIds, @Param("allSubIds") List<Integer> allSubIds, @Param("subIdoType") Integer subIdoType);

    // 获取回收订单详情
    List<HuiShouProductBO> listHuishouProductByRecoverSubId(@Param("recoverSubIds") Collection<Integer> recoverSubIds);

    //获取 员工服务
    List<Ch999UserServiceBO> listCh999UserServiceBO(@Param("subIds") List<Integer> subIds,
                                                    @Param("officeName") String officeName);


    //良品订单部分
    Integer countLiangpin(@Param("userId") Integer userId, @Param("displayInvoice") Boolean displayInvoice
            , @Param("xtenant") Integer xtenant, @Param("tag") String tag
            , @Param("keyWord") String keyWord, @Param("searchId") Integer searchId,@Param("status")List<Integer> status
            , @Param("req") IWebOrderQuery req);

    List<LiangpinSubBO> listLiangpin(@Param("userId") Integer userId,@Param("displayInvoice") Boolean displayInvoice
            , @Param("xtenant") Integer xtenant, @Param("tag") String tag
            , @Param("keyWord") String keyWord, @Param("searchId") Integer searchId
            , @Param("startRows") Integer startRows, @Param("size") Integer size,@Param("status")List<Integer> status
            , @Param("req") IWebOrderQuery req);

    List<LiangpinProductBO> listProductLiangpin(@Param("subIds") List<Integer> subIds);



    Integer countRecover(@Param("userId") Integer userId
            , @Param("xtenant") Integer xtenant, @Param("tag") String tag
            , @Param("keyWord") String keyWord, @Param("searchId") Integer searchId,@Param("status")List<Integer> status
            , @Param("req") IWebOrderQuery req);

    List<HuiShouSubBO> listRecover(@Param("userId") Integer userId
            , @Param("xtenant") Integer xtenant, @Param("tag") String tag
            , @Param("keyWord") String keyWord, @Param("searchId") Integer searchId
            , @Param("startRows") Integer startRows, @Param("size") Integer size,@Param("status")List<Integer> status
            , @Param("req") IWebOrderQuery req);

    Integer countAfterService(@Param("userId") Integer userId,
                              @Param("xtenant") Integer xtenant,
                              @Param("tag") String tag

            , @Param("req") IWebOrderQuery req);

    @DS("oanew_his")
    Integer countAfterServiceHis(@Param("userId") Integer userId,
                              @Param("xtenant") Integer xtenant,
                              @Param("tag") String tag
            , @Param("req") IWebOrderQuery req);

    List<AfterServiceSubBO> listAfterServiceSub(@Param("userId") Integer userId,
                                                @Param("xtenant") Integer xtenant,
                                                @Param("tag") String tag,
                                                @Param("startRows") Integer startRows,
                                                @Param("size") Integer size
            , @Param("req") WebAfterQueryReq req);

    @DS("oanew_his")
    List<AfterServiceSubBO> listAfterServiceSubHis(@Param("userId") Integer userId,
                                                @Param("xtenant") Integer xtenant,
                                                @Param("tag") String tag,
                                                @Param("startRows") Integer startRows,
                                                @Param("size") Integer size
            , @Param("req") WebAfterQueryReq req);

    @DS("office")
    List<AfterServiceSubPjBO> listAfterSubPj(@Param("shouhouIds") List<Integer> shouhouIds);

    List<AfterServiceSubYuyueBO> listAfterSubYuyue(@Param("yuyueIds") List<Integer> yuyueIds);

    /**
     *
     * @param userId
     * @param xtenant
     * @param tag
     * @param officeName
     * @return
     */
    Integer countSmallProAfterService(@Param("userId") Integer userId,
                              @Param("xtenant") Integer xtenant,
                              @Param("tag") String tag,
                              @Param("officeName") String officeName);

    /**
     *
     * @param userId
     * @param xtenant
     * @param tag
     * @param startRows
     * @param size
     * @param officeName
     * @return
     */
    List<AfterServiceSubSmallBO> listSmallProAfterServiceSub(@Param("userId") Integer userId,
                                                @Param("xtenant") Integer xtenant,
                                                @Param("tag") String tag,
                                                @Param("startRows") Integer startRows,
                                                @Param("size") Integer size,
                                                @Param("officeName") String officeName,
                                                @Param("req") WebAfterQueryReq req);

    /**
     *
     * @param userId
     * @param xtenant
     * @param tag
     * @param startRows
     * @param size
     * @param officeName
     * @return
     */
    List<AfterServiceSubSmallBO> listSmallProAfterServiceSubV2(@Param("userId") Integer userId,
                                                             @Param("xtenant") Integer xtenant,
                                                             @Param("tag") String tag,
                                                             @Param("startRows") Integer startRows,
                                                             @Param("size") Integer size,
                                                             @Param("officeName") String officeName,
                                                             @Param("req") WebAfterQueryReq req);


    Integer countReservation(@Param("userId") Integer userId
            , @Param("xtenant") Integer xtenant, @Param("tag") String tag
            , @Param("keyWord") String keyWord, @Param("searchId") Integer searchId
            , @Param("req") IWebOrderQuery req);

    List<ReservationSubBO> listReservationSub(@Param("userId") Integer userId
            , @Param("xtenant") Integer xtenant, @Param("tag") String tag
            , @Param("keyWord") String keyWord, @Param("searchId") Integer searchId
            , @Param("startRows") Integer startRows, @Param("size") Integer size
            , @Param("req") IWebOrderQuery req);

    /**
     * 查询订单相关联的所有专属客服
     *
     * @param subIds 订单id
     * @return 专属客服
     */
    List<Ch999UserServiceVO> listExclusiveCustomerService(@Param("subIds") List<Integer> subIds,@Param("officeName") String officeName);

    /**
     * 根据用户id查询专属客服相关订单
     *
     * @param userId  用户id
     * @param current 页码
     * @param size    页容量
     * @return 专属客服相关订单
     */
    List<Integer> listExclusiveCustomerOrder(@Param("userId") Integer userId,
                                             @Param("xtenant") Integer xtenant,
                                             @Param("officeName") String officeName,
                                             @Param("current") long current,
                                             @Param("size") int size);

    /**
     * 查询专属客服相关订单数量
     *
     * @param userId     用户id
     * @param xtenant    租户
     * @param officeName office数据库名
     * @return 订单数量
     */
    @Cached(name = "weborder.WebOrderMapper.countExclusiveCustomerOrder", expire = 1, timeUnit = TimeUnit.MINUTES)
    Integer countExclusiveCustomerOrder(@Param("userId") Integer userId,
                                        @Param("xtenant") Integer xtenant,
                                        @Param("officeName") String officeName);

    /**
     * 根据用户id查询未评价订单数量
     *
     * @param userId 用户id
     * @return 未评价订单数量
     */
    int countNotEvaluatedOrder(@Param("userId") Integer userId, @Param("officeName") String officeName);

    /**
     * 查询p30订金预定订单
     *
     * @param productIds 商品id
     * @param pay        订金金额
     * @return 订单
     */
    List<SaleOrderVO> getSaleOrders(@Param("productIds") List<Integer> productIds, @Param("pay") Double pay);

    /**
     * 查询p30订金预定订单 V2
     *
     * @param productIds 商品id
     * @param pay        订金金额
     * @return 订单
     */
    List<SaleOrderVO> getSaleOrdersV2(@Param("productIds") List<Integer> productIds, @Param("pay") Double pay,
                                      @Param("subDate")String subDate);


    /**
     * 获取新机库存预设分组信息
     *
     * @param basketIds
     * @param level     1-是 2 不是
     * @param wxBind    1 绑定 2 未绑定
     * @return
     */
    List<SaleOrderKcGroupInfoVO> getSaleOrderKcGroupInfo(@Param("basketIds") List<Integer> basketIds, @Param("level") Integer level, @Param("wxBind") Integer wxBind);

    /**
     * 根据订单明细id获取库存预设
     *
     * @param basketId 订单明细id
     * @return 库存预设
     */
    InventoryPresetVO getInventoryPreset(@Param("basketId") Integer basketId);

    /**
     * 根据订单明细id查询订单商品信息
     *
     * @param basketId 订单明细id
     * @return 订单商品信息
     */
    List<ProductInfoBO> listProductInfoByBasketId(@Param("basketId") Integer basketId,
                                                  @Param("userId") Integer userId);

    /**
     * 根据ppriceid查询商品信息
     *
     * @param ppriceid ppid
     * @return 商品信息
     */
    ProductInfoBO getProductInfo(@Param("ppriceid") Integer ppriceid);

    /**
     * 获取订单总金额
     *
     * @param subId 订单id
     * @return 订单总金额
     */
    BigDecimal getOrderPrice(@Param("subId") Integer subId);

    List<ImOrderVO> getImNewOrders(@Param("subIds") List<Integer> subIds, @Param("userId") Integer userId);

    /**
     * 根据订单 ids 和用户 id 查询新机订单商品信息
     *
     * @param subIds subIds
     * @param userId userId
     * @return List<ImOrderVO>
     */
    List<ImOrderVO> listMineProductInfoBySubIdAndUserId(@Param("subIds") List<Integer> subIds, @Param("userId") Integer userId);

    /**
     * 根据订单 ids 和用户 id 查询维修订单商品信息
     *
     * @param subIds subIds
     * @param userId userId
     * @return List<ImOrderVO>
     */
    List<ImOrderVO> listRepairProductInfoBySubIdAndUserId(@Param("subIds") List<Integer> subIds, @Param("userId") Integer userId, @Param("kind") Integer kind);

    /**
     * 根据订单 ids 和用户 id 查询回收订单商品信息
     *
     * @param subIds subIds
     * @param userId userId
     * @return List<ImOrderVO>
     */
    List<ImOrderVO> listRecoverProductInfoBySubIdAndUserId(@Param("subIds") List<Integer> subIds, @Param("userId") Integer userId);

    /**
     * 根据订单 ids 和用户 id 查询良品订单商品信息
     *
     * @param subIds subIds
     * @param userId userId
     * @return List<ImOrderVO>
     */
    List<ImOrderVO> listSecondHandProductInfoBySubIdAndUserId(@Param("subIds") List<Integer> subIds, @Param("userId") Integer userId);

    /**
     * 获取九机员工信息
     * @param mobileList
     * @return
     */
    List<HuiShouProductVO> listStaffInfoByMobile(@Param("mobileList") List<String> mobileList);

    /**
     * 根据传入号码和租户编号，品牌id，时间段，查询当前用户是否存在购买记录
     * @param whetherToBuyReq 号码和租户编号，品牌id，时间段
     * @return
     */
    List<WhetherToBuyReq.Res> getWhetherToBuy(@Param("whetherToBuyReq") WhetherToBuyReq whetherToBuyReq);


    /**
     * 根据时间和会员id查询 对应的状态
     * @param myClientReq myClientReq
     * @param flag 表示 false为未完成 true为已完成
     * @return 客户编号
     */
    List<Integer> getMyClientComplete(@Param("myClientReq") MyClientReq myClientReq,@Param("flag") Boolean flag );


    /**
     * 商品ID （commodityId），有购买完成的客户（根据时间筛选）
     * @param myClientReq myClientReq
     * @param commodityId 商品id
     * @return 客户编号
     */
    List<Integer> getMyClientPurchaseComplete(@Param("myClientReq") MyClientReq myClientReq,@Param("commodityId") Integer commodityId);

    /**
     * 筛选近X天【已完成】状态的订单中包含电子烟商品
     * @param myClientReq myClientReq
     * @param valueList 云雾商品分类
     * @return 客户编号
     */
    List<Integer> getMyClientIsCompleteByCigarette(@Param("myClientReq") MyClientReq myClientReq,@Param("valueList") List<Integer> valueList);

    /**
     * 根据mkcIdList查询库存位置
     * @param mkcIdList mkcIdList
     * @return
     */
    List<GoodProductStockVO> getGoodProductStockByMkcList(@Param("mkcIdList") List<Integer> mkcIdList);

    List<Ch999UserServiceVO> listLiangPinExclusiveCustomerService(@Param("subIds") Collection<Integer> subIds, @Param("officeName") String officeName);

    /**
     * 获取绑定的良品配件订单信息
     * @param subIds
     * @return
     */
    List<BindSubInfoBo> listBindLpPjSubInfo(@Param("subIds") Collection<Integer> subIds, @Param("businessTypeEnum") BusinessTypeEnum businessTypeEnum);


    /**
     * 查询是否为良品配件补贴
     * @param subId
     * @return
     */
    List<Integer> selectGoodAccessories(@Param("subId")Integer subId);


    /**
     * 查询是否为良品配件补贴
     * @param subId
     * @return
     */
    List<Integer> selectGoodAccessoriesGoodProduct(@Param("subId")Integer subId);

    /**
     * 通过会员id查询退换机次数
     * @param memberId 会员id
     * @return
     */
    List<ExchangeCountBO> getExchangeCount(@Param("memberId")Integer memberId);

    /**
     * 通过订单id列表获取订单详情
     * @param orderIdList
     * @return
     */
    List<OrderStateRes> getOrderStatusByList(@Param("orderIdList")List<Integer> orderIdList);

    /**
     * 根据basketId查询subId
     *
     * @param basketId 订单商品明细id
     * @return subId
     */
    Integer getSubIdByBasketId(@Param("basketId") Integer basketId);

    /**
     * 根据subId查询物流单订货调拨信息
     * @param subIds
     * @return
     */
    List<WuliuDiaoboSubBO> listWuliuDiaoboBySubId(@Param("subIds") List<Integer> subIds);

    /**
     * 查询调拨单信息
     * @param subIds
     * @return
     */
    List<DiaoboSubBO> listDiaoboBySubId(@Param("subIds") List<Integer> subIds);

    OrderByWxNoVO getAlipayInfoById(@Param("outTradeNo") String outTradeNo);

    List<OrderByWxNoVO> getPayItemInfoById(@Param("outTradeNo") Integer outTradeNo);

    List<OrderItemVO> getSubOrderInfoBySubIds(@Param("subIds") List<String> subIds);

    List<OrderItemVO> getGoodSubOrderInfoBySubIds(@Param("subIds") List<String> goodSubIds);

    List<OrderItemVO> getRepairSubOrderInfoBySubIds(@Param("subIds") List<String> repairSubIds);

    List<OrderItemVO> getSmallSubOrderInfoBySubIds(@Param("subIds") List<String> smallSubIds);
}
