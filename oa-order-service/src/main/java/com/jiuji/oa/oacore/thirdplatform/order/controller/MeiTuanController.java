package com.jiuji.oa.oacore.thirdplatform.order.controller;


import com.jiuji.oa.oacore.thirdplatform.annotation.AddLogKind;
import com.jiuji.oa.oacore.thirdplatform.order.bo.*;
import com.jiuji.oa.oacore.thirdplatform.order.service.MeiTuanService;
import com.jiuji.oa.oacore.thirdplatform.order.service.OrderService;
import com.jiuji.oa.oacore.thirdplatform.order.service.impl.UpdateSubCheck;
import com.jiuji.oa.oacore.thirdplatform.order.utils.ParamUtils;
import com.jiuji.oa.oacore.thirdplatform.order.vo.HandleInvoiceReq;
import com.jiuji.oa.oacore.thirdplatform.order.vo.NationalSupplementUpSnReq;
import com.jiuji.oa.oacore.thirdplatform.order.vo.NationalSupplementUpSnRes;
import com.jiuji.oa.oacore.thirdplatform.order.vo.SelectInvoiceReq;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.annotation.CheckHeaderSign;
import com.jiuji.tc.utils.common.annotation.LogRecordAround;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.CheckForSigned;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/admin/api/meituan")
public class MeiTuanController {

    @Resource
    private MeiTuanService meiTuanService;

    @Resource
    private UpdateSubCheck updateSubCheck;
    @Resource
    private SmsService smsService;
    @Resource
    private OrderService orderService;

    @Resource
    private ParamUtils paramUtils;


    /**
     * 美团订单详情查询
     *
     * @param detailBO
     * @return
     */
    @PostMapping("/getOrderDetail/v1")
    public R<ShowMeiTuanOrderDetailBO> getOrderDetail(@RequestBody @Valid SelectMeiTuanOrderDetailBO detailBO) {
        ShowMeiTuanOrderDetailBO orderDetail = meiTuanService.getOrderDetail(detailBO);
        return R.success(orderDetail);
    }


    /**
     * 美团国补订单 上传 sn
     * @param detailBO
     * @return
     */
    @CheckHeaderSign(value = "美团国补订单")
    @LogRecordAround(value = "美团国补订单")
    @PostMapping("/nationalSupplementUpSn/v1")
    public R<String> nationalSupplementUpSn(@RequestBody @Valid NationalSupplementUpSnReq req) {
        NationalSupplementUpSnRes nationalSupplementUpSnRes = meiTuanService.nationalSupplementUpSn(req);
        if(Optional.ofNullable(nationalSupplementUpSnRes.getSuccess()).orElse(Boolean.FALSE)){
            return R.success("成功");
        }
        return R.error(nationalSupplementUpSnRes.getMessage());
    }



    /**
     * 美团国补订单 上传 sn
     * @param detailBO
     * @return
     */
    @CheckHeaderSign(value = "美团发票查询")
    @LogRecordAround(value = "美团发票OA查询")
    @PostMapping("/selectInvoice/v1")
    public R<String> selectInvoice(@RequestBody @Valid SelectInvoiceReq req) {
        return R.success("查询成功",meiTuanService.selectInvoice(req));
    }


    /**
     * 美团国补订单 上传 sn
     * @param detailBO
     * @return
     */
    @CheckHeaderSign(value = "美团发票上传")
    @LogRecordAround(value = "美团发票OA上传")
    @PostMapping("/handleInvoice/v1")
    public R<String> handleInvoice(@RequestBody @Valid HandleInvoiceReq req) {
        NationalSupplementUpSnRes nationalSupplementUpSnRes = meiTuanService.handleInvoice(req);
        if(Optional.ofNullable(nationalSupplementUpSnRes.getSuccess()).orElse(Boolean.FALSE)){
            return R.success("成功");
        }
        return R.error(nationalSupplementUpSnRes.getMessage());
    }


    /**
     * 美团订单详情查询
     *
     * @param areaId
     * @return
     */
    @GetMapping("/synchronizationBusinessHours/v1")
    public R<String> synchronizationBusinessHours(@RequestParam String areaId) {
        meiTuanService.synchronizationAreaHour(areaId);
        return R.success("操作结束");
    }

    /**
     * 美团订单详情查询
     *
     * @param areaId
     * @return
     */
    @GetMapping("/synchronizationBusinessHours/v2")
    public R<String> synchronizationBusinessHoursV2(@RequestParam String areaId) {
        meiTuanService.synchronizationAreaHourV2(areaId);
        return R.success("操作结束");
    }


    /**
     * 拣选完成之后同步美团
     *
     * @param preparationMealCompleteBO
     * @return
     */
    @PostMapping("/preparationMealComplete/v1")
    public R<String> preparationMealComplete(@RequestBody @Valid PreparationMealCompleteBO preparationMealCompleteBO) {
        return meiTuanService.pickingCompleted(preparationMealCompleteBO);
    }


    /**
     * 从美团获取取消原因
     *
     * @param orderId
     * @return
     */
    @GetMapping("/getCancelReason/v1")
    public R<String> getCancelReason(@RequestParam(name = "orderId") String orderId) {
        return R.success(orderService.getCancelReason(orderId));
    }


    /**
     * 美团配送状态同步九机订单状态
     *
     * @param
     * @return
     */
    @PostMapping("/deliveryStatusSynchronization/v1")
    public R deliveryStatusSynchronization(HttpServletRequest request) {
        return meiTuanService.deliverySynchronization(paramUtils.getParams(request, AddLogKind.DELIVERY_STATUS_SYNCHRONIZATION));
    }


    /**
     * 美团发票上传回调
     * @param
     * @return
     */
    @LogRecordAround(value = AddLogKind.INVOICE_CALL)
    @PostMapping("/invoiceCall/v1")
    public R invoiceCall(HttpServletRequest request) {
        Map<String, Object> params = paramUtils.getParams(request, AddLogKind.INVOICE_CALL);
        meiTuanService.invoiceCall(params);
        return R.success("成功");
    }
    /**
     * 美团配送状态同步九机订单状态(提供给测试使用)
     *
     * @param
     * @return
     */
    @PostMapping("/deliveryStatusSynchronization/test/v1")
    public R deliveryStatusSynchronizationTest(@RequestBody MeiTuanTestBO meiTuanTestBO) {
        //判断只有九机才走该逻辑
        if (XtenantEnum.isJiujiXtenant(XtenantEnum.getXtenant())) {
            Map<String, Object> params = new HashMap<>(4);
            params.put("logistics_status", meiTuanTestBO.getLogisticsStatus());
            params.put("dispatcher_name", meiTuanTestBO.getDispatcherName());
            params.put("order_id", meiTuanTestBO.getOrderId());
            params.put("dispatcher_mobile", meiTuanTestBO.getDispatcherMobile());
            return meiTuanService.deliverySynchronization(params);
        } else {
            return new R(OrderRes.RESULT_OK);
        }
    }


    /**
     * 美团推送订单信息修改消息
     *
     * @param
     * @return
     */
    @PostMapping("/modifyOrderInfoSynchronization/v1")
    public R modifyOrderInfoSynchronization(HttpServletRequest request) {
        //判断只有九机才走该逻辑
        if (XtenantEnum.isJiujiXtenant(XtenantEnum.getXtenant())) {
            return meiTuanService.modifyOrderInfoSynchronization(paramUtils.getParams(request, AddLogKind.MODIFY_ORDER_INFO_SYNCHRONIZATION));
        } else {
            return new R(OrderRes.RESULT_OK);
        }

    }

    /**
     * 美团推送订单信息修改消息(提供给测试使用)
     *
     * @param
     * @return
     */
    @PostMapping("/modifyOrderInfoSynchronization/test/v1")
    public R modifyOrderInfoSynchronization(@RequestBody MeiTuanTestBO meiTuanTestBO) {
        //判断只有九机才走该逻辑
        if (XtenantEnum.isJiujiXtenant(XtenantEnum.getXtenant())) {
            Map<String, Object> params = new HashMap<>(4);
            params.put("order_id", meiTuanTestBO.getOrderId());
            return meiTuanService.modifyOrderInfoSynchronization(params);
        } else {
            return new R(OrderRes.RESULT_OK);
        }
    }



    /**
     * 修改订单状态接口
     *
     * @param
     * @return
     */
    @PostMapping("/updateOrder/v1")
    public R<Boolean> updateOrder(@RequestBody UpdateSubCheckBO updateSubCheckBO, HttpServletRequest request) {
        String header = request.getHeader("south");
        if (!"18008733159".equals(header)) {
            return R.error("密钥错误");
        }
        return updateSubCheck.updateSubCheck(updateSubCheckBO);
    }
}
