package com.jiuji.oa.oacore.yearcardtransfer.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 苹果GB账号配置日志表
 * <AUTHOR>
 */
@Data
@TableName("area_guestbook_account_log")
public class AreaGuestBookAccountLog {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 业务ID
     */
    @TableField(value = "business_id", fill = FieldFill.INSERT)
    private Integer businessId;

    /**
     * 操作内容
     */
    @TableField("content")
    private String content;

    /**
     * 操作时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 操作人
     */
    @TableField("in_user")
    private String inUser;
}
