package com.jiuji.oa.oacore.common.config.rabbitmq;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ch999.common.util.tenant.Namespaces;
import com.ch999.common.util.utils.Exceptions;
import com.jiuji.oa.oacore.common.bo.OaMqData;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.thirdplatform.common.ThirdPlatformCommonConst;
import com.jiuji.oa.oacore.thirdplatform.order.bo.EcommerceOrderParam;
import com.jiuji.oa.oacore.thirdplatform.order.bo.LossOrderMessagePushParam;
import com.jiuji.oa.oacore.thirdplatform.order.service.OrderService;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.tc.utils.common.TraceIdUtil;
import com.rabbitmq.client.Channel;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Objects;


/**
 * <AUTHOR>
 * @Date 11:38
 * @Description
 */
@Slf4j
@Component
public class OrderServiceMqReceiver {
    @Resource
    private OrderService orderService;

    @RabbitHandler
    @RabbitListener(queuesToDeclare = @Queue("orderServiceReceive"), containerFactory = "oaAsyncManualListenerContainerFactory")
    public void process(Message message, Channel channel) throws IOException {
        String msgStr = null;
        try {
            MDC.put(TraceIdUtil.TRACE_ID_KEY, TraceIdUtil.getTraceId());
            msgStr = new String(message.getBody());
            log.warn("Receiver from orderServiceReceive:{}", msgStr);
            OaMqData<JSONObject> oaMqData = JSON.parseObject(msgStr, new TypeReference<OaMqData<JSONObject>>() {
            });
            if (oaMqData != null) {
                log.warn("oaMqData:{}", oaMqData);
                // 设置oa传的xtenant
                Integer oaXtenant = oaMqData.getXtenant();
                if (Objects.nonNull(oaXtenant)) {
                    Namespaces.set(oaXtenant);
                }
                String act = oaMqData.getAct();
                if ("ecommerceOrderLogisticsSync".equals(act)) {
                    try {
                        //美团类型订单的状态
                        OaMqData<EcommerceOrderParam> oaMqData1 = JSON.parseObject(msgStr, new TypeReference<OaMqData<EcommerceOrderParam>>() {
                        });
                        orderService.ecommerceOrderLogisticsSync(oaMqData1.getData());
                    } catch (Exception exception) {
                        log.error("json 消费失败，json:{}, e:{}，", msgStr, Exceptions.getStackTraceAsString(exception));
                        //出现异常通知  做报警通知 并且act 以免阻塞
                        String noticMsg = "美团出库消费失败！" + oaXtenant + "消费消息为:" + msgStr;
                        log.error(noticMsg);
                    }
                    log.warn("unknownact :{}", JSON.toJSONString(oaMqData));
                }
            }
        } catch (Exception e) {
            log.error("msgerror", e);
            log.warn("msgerror-drop:{}", msgStr);
        } finally {
            MDC.remove(TraceIdUtil.TRACE_ID_KEY);
            //TODO 先暂时去掉 测试环境怕其他消费者消费掉
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);

        }
    }

    /**
     * 亏损订单提醒
     *
     * @param message
     * @param channel
     * @throws IOException
     */
    @RabbitHandler
    @RabbitListener(queuesToDeclare = @Queue(name = RabbitMqConfig.QUEUE_LOSS_ORDER_MESSAGE_PUSH, admins = "oaAsyncRabbitAdmin"),
            containerFactory = "oaAsyncListenerContainerFactory", admin = "oaAsyncRabbitAdmin")
    @SneakyThrows
    @Profile(value = {"jiuji", "dev"})
    public void lossOrderMessagePush(Message message, Channel channel) throws IOException {
        String msgStr = null;
        try {
            MDC.put(TraceIdUtil.TRACE_ID_KEY, TraceIdUtil.getTraceId());
            //解析为对象
            String msg = new String(message.getBody());
            try {
                LossOrderMessagePushParam data = JSONUtil.toBean(msg, LossOrderMessagePushParam.class);
                log.info(StrUtil.format("消费亏损订单消息, 平台：{}, 单号: {}, 服务费: {}", data.getPlatform(), data.getSubId(), data.getFee()));
                if (Objects.equals(data.getPlatform(), ThirdPlatformCommonConst.THIRD_PLAT_MT)
                        || Objects.equals(data.getPlatform(), ThirdPlatformCommonConst.THIRD_PLAT_JD)) {
                    orderService.lossOrderMessagePush(data);
                }
            } catch (Exception exception) {
                log.error("json 消费失败，json:{}, e:{}，", msgStr, Exceptions.getStackTraceAsString(exception));
                //出现异常通知  做报警通知 并且act 以免阻塞
                String noticMsg = "亏损订单提醒消费失败！消费消息为:" + msgStr;
                log.error(noticMsg);
            }

        }catch (Exception e){
            RRExceptionHandler.logError("MQ调用亏损订单提醒消费接口失败", msgStr, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        } finally {
            MDC.remove(TraceIdUtil.TRACE_ID_KEY);
        }
    }
}
