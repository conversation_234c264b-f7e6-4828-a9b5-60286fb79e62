package com.jiuji.oa.oacore.cloud;

import com.jiuji.oa.oacore.cloud.bo.DeliveryInputWrapperReq;
import com.jiuji.oa.oacore.cloud.bo.WXSmsReceiverReq;
import com.jiuji.oa.oacore.cloud.fallback.InWcfCloudFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * inwcf 相关接口
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@FeignClient(url ="${jiuji.sys.inWcf}", name = "InWcfApiCloud", fallbackFactory = InWcfCloudFallbackFactory.class)
public interface InWcfCloud {


    /**
     * oa 获取通知人
     * @return
     */
    @RequestMapping(value = "/oaApi.svc/rest/GetGotifyReceiver", method = RequestMethod.POST, headers = {"Accept=application/json"})
    String getGotifyReceiver(@RequestBody WXSmsReceiverReq req);

    /**
     * 编辑第三方订单信息
     * @return
     */
    @RequestMapping(value = "/oaApi.svc/rest/EditThirdPlatformOrder", method = RequestMethod.POST, headers = {"Accept=application/json"})
    String editThirdPlatformOrder(@RequestBody DeliveryInputWrapperReq req);


}
