package com.jiuji.oa.oacore.tousu.service;

import com.jiuji.oa.oacore.common.res.PageRes;
import com.jiuji.oa.oacore.tousu.bo.TouSuProcessBO;
import com.jiuji.oa.oacore.tousu.bo.ZeRenRen;
import com.jiuji.oa.oacore.tousu.po.TouSuModel;
import com.jiuji.oa.oacore.tousu.res.TouSuDetail;
import com.jiuji.oa.oacore.tousu.res.TouSuModelRes;
import com.jiuji.oa.oacore.tousu.res.TouSuResult;
import com.jiuji.oa.oacore.tousu.vo.res.AddTouSuResult;
import com.jiuji.oa.oacore.tousu.vo.res.ComplaintPvPage;
import com.jiuji.tc.common.vo.R;

import java.util.List;

/**
 * @Description 投诉类
 * <AUTHOR>
 * @Date 2020/8/27 13:57
 */
public interface TouSuService {
    AddTouSuResult addTouSu(TouSuModel ts);
    PageRes<TouSuModelRes> touSuList(long userid, int curPage, int rows);

    TouSuDetail touSuDetail(long userid, int id);

    TouSuResult touSuAddQuestion(long userid, int id, String content, String attachFiles);

    int addTsProcess(TouSuProcessBO process, TouSuModel model);

    ComplaintPvPage getComplaintPV(String url, Long current, Long size);

    /**
     * 修改投诉管理 业务流程图
     *
     * @param image 业务流程图
     * @return
     */
    R<Boolean> modifyBusinessFlowChart(String image);

    /**
     * 根据id获取投诉信息
     * @param id 投诉id
     * @return
     */
    TouSuModel getComplainById(Integer id);

    /**
     * 更新投诉为网站可见
     *
     * @param tsId 投诉id
     * @return boolean
     */
    boolean updateTousuToShowWeb(Integer tsId);

    /**
     * 更新处理人
     *
     * @param tsId 投诉id
     * @return boolean
     */
    boolean updateTsDealUser(Integer tsId);

    /**
     * 根据评价id查询对应好评人员
     */
    List<ZeRenRen> getCommentPraise(Integer evaluateId);
}
