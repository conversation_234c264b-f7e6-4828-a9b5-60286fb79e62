package com.jiuji.oa.oacore.salary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.ch999.common.util.utils.DateTimeUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jiuji.oa.nc.CustomSalaryReportCloud;
import com.jiuji.oa.nc.MessagePushCloud;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.config.properties.JiujiSystemProperties;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.constant.RedisKeyConstant;
import com.jiuji.oa.oacore.common.enums.JiujiTenantEnum;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.util.ActiveProfileJudgeUtil;
import com.jiuji.oa.oacore.common.util.DateTimeFormatterUtil;
import com.jiuji.oa.oacore.common.util.LocalDateTimeUtils;
import com.jiuji.oa.oacore.common.util.profileutil.InnerProfileJudgeUtil;
import com.jiuji.oa.oacore.csharp.cloud.CsharpInWcfCloud;
import com.jiuji.oa.oacore.csharp.vo.res.OrderTypeRes;
import com.jiuji.oa.oacore.oaorder.bo.Ch999UserBasicBO;
import com.jiuji.oa.oacore.oaorder.dao.Ch999UserMapper;
import com.jiuji.oa.oacore.oaorder.enums.RecoverTradeWayEnum;
import com.jiuji.oa.oacore.partner.evaluate.util.SpringContext;
import com.jiuji.oa.oacore.salary.bo.dto.*;
import com.jiuji.oa.oacore.salary.common.constant.SalaryConstant;
import com.jiuji.oa.oacore.salary.dao.SalaryDao;
import com.jiuji.oa.oacore.salary.dao.SalaryOperatorOrderDao;
import com.jiuji.oa.oacore.salary.dao.SalaryRecoverDao;
import com.jiuji.oa.oacore.salary.dao.SalaryResDao;
import com.jiuji.oa.oacore.salary.entity.vo.RetrunOrderEx;
import com.jiuji.oa.oacore.salary.entity.vo.TaskCalculateVo;
import com.jiuji.oa.oacore.salary.entity.vo.req.SalaryCalculateReq;
import com.jiuji.oa.oacore.salary.entity.vo.res.*;
import com.jiuji.oa.oacore.salary.enums.*;
import com.jiuji.oa.oacore.salary.service.SalaryService;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.operation.statistics.CustomSalaryReportReq;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.sysconfig.vo.SysConfigNameValueVO;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserSalaryVo;
import com.jiuji.oa.orginfo.userinfo.vo.ZhiWuVO;
import com.jiuji.oa.salary.*;
import com.jiuji.oa.salary.enums.ReturnOrderVTypeEnum;
import com.jiuji.oa.train.client.AchievementMedalClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.BasketTypeEnum;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.EnumVO;
import com.jiuji.tc.utils.enums.coupon.SubSubTypeEnum;
import com.jiuji.tc.utils.xtenant.Namespaces;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/2/22
 * @description 自定义薪酬获取对应租户枚举数据接口实现
 */
@Service
@Slf4j
public class SalaryServiceImpl implements SalaryService {
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private SalaryDao salaryDao;
    @Resource
    private SalaryRecoverDao salaryRecoverDao;
    @Resource
    private SalaryOperatorOrderDao salaryOperatorOrderDao;
    @Resource
    private SalaryResDao salaryResDao;
    @Resource
    private UserInfoClient userInfoClient;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private AchievementMedalClient achievementMedalClient;
    @Resource
    private JiujiSystemProperties jiujiSystemProperties;
    @Resource
    private Ch999UserMapper ch999UserMapper;
    @Resource
    private CustomSalaryReportCloud customSalaryReportCloud;
    @Qualifier("asyncServiceExecutor")
    @Autowired
    private Executor executor;

    //配置ObjectMapper对象
    private static final ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
    private final static String DATE_FORMATTER = "yyyy-MM-dd HH:mm:ss";

    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private RedisTemplate<String, Integer> redisTemplate;
    @Resource
    private CsharpInWcfCloud csharpInWcfCloud;
    @Value("${autoCalculateSalary.xtenant}")
    private Integer tenantId;

    @Resource
    private MessagePushCloud messagePushCloud;

    //没有带培的员工类别
    private static final List<Integer> noDaiPeiUserTypes = Arrays.asList(2, 5, 6, 7, 8, 9, 10, 11, 14, 18, 19, 20);

    @Override
    public RecycleEnumsRes getRecycleEnums() {
        Integer xtenant = (int) Namespaces.get();
        if (xtenant == null) {
            return null;
        }
        // 回收订单类型
        R<List<SysConfigNameValueVO>> orderVTypeListR = sysConfigClient.getNameAndValueByCodeAndXtenant(SysConfigConstant.ORDER_TYPE, xtenant);
        List<SysConfigNameValueVO> orderVTypeList = CommonUtils.isRequestSuccess(orderVTypeListR) ?
                orderVTypeListR.getData().stream().sorted(Comparator.comparing(SysConfigNameValueVO::getValue)).collect(Collectors.toList()) : null;
        // 回收支付方式
        R<List<SysConfigNameValueVO>> recyclePayListR = sysConfigClient.getNameAndValueByCodeAndXtenant(SysConfigConstant.RECYCLE_PAY_METHOD, xtenant);
        List<SysConfigNameValueVO> recyclePayList = CommonUtils.isRequestSuccess(recyclePayListR) ?
                recyclePayListR.getData().stream().sorted(Comparator.comparing(SysConfigNameValueVO::getValue)).collect(Collectors.toList()) : null;
        // 回收交易方式(即回收派送方式)
        List<SysConfigNameValueVO> orderDeliveryList = new ArrayList<>();
        for (RecoverTradeWayEnum value : RecoverTradeWayEnum.values()) {
            SysConfigNameValueVO sysConfigNameValueVO = new SysConfigNameValueVO();
            sysConfigNameValueVO.setValue(value.getCode().toString());
            sysConfigNameValueVO.setName(value.getMessage());
            orderDeliveryList.add(sysConfigNameValueVO);
        }
        return new RecycleEnumsRes()
                .setOrderVTypeList(orderVTypeList)
                .setPayMethodList(recyclePayList)
                .setOrderDeliveryList(orderDeliveryList);
    }

    @Override
    public MarketingEnumsRes getMarketingEnums() {
        Integer xtenant = (int) Namespaces.get();

        // 销售支付方式，下标0:网上支付,1:三方支付,2:分期付款
        List<SysConfigNameValueVO> marketingOnlineMethod;
        if (xtenant == 0) {
            // 如果是九机则只有建行分期
            marketingOnlineMethod = new ArrayList<>();
            marketingOnlineMethod.add(new SysConfigNameValueVO().setValue("0").setName("建行分期"));
        } else {
            marketingOnlineMethod = getMarketingOnlineMethod();
        }
        R<List<SysConfigNameValueVO>> installmentsR = sysConfigClient.getNameAndValueByCodeAndXtenant(SysConfigConstant.INSTALLMENTS_PAYMENT_METHOD, xtenant);
        List<SysConfigNameValueVO> installments = CommonUtils.isRequestSuccess(installmentsR) ?
                installmentsR.getData().stream().sorted(Comparator.comparing(SysConfigNameValueVO::getValue)).collect(Collectors.toList()) : null;
        // 代金券就是销售的三方支付
        R<List<SysConfigNameValueVO>> threePartPayListR = sysConfigClient.getNameAndValueByCodeAndXtenant(SysConfigConstant.VOUCHER_PAYMENT_METHOD, xtenant);
        List<SysConfigNameValueVO> threePartPayList = CommonUtils.isRequestSuccess(threePartPayListR) ?
                threePartPayListR.getData().stream().sorted(Comparator.comparing(SysConfigNameValueVO::getValue)).collect(Collectors.toList()) : null;
        List<SysConfigNameValueVO> payMethodList = new ArrayList<>(marketingOnlineMethod);
        Optional.ofNullable(installments).ifPresent(payMethodList::addAll);
        Optional.ofNullable(threePartPayList).ifPresent(payMethodList::addAll);

        // 销售派送方式
        List<SysConfigNameValueVO> orderDeliveryList = salaryDao.getOrderDeliveryList();
        // 销售订单类别
        List<EnumVO> orderDetailType = EnumUtil.toEnumVOList(BasketTypeEnum.class);
        // 销售订单类型
        List<EnumVO> orderVTypeList = EnumUtil.toEnumVOList(SubSubTypeEnum.class);
        // 请求c#的接口获取订单类型和订单类别
        R<OrderTypeRes> orderTypeResult = csharpInWcfCloud.getOrderTypes();
        if (CommonUtils.isRequestSuccess(orderTypeResult)) {
            OrderTypeRes typeRes = orderTypeResult.getData();
            if (CollectionUtil.isNotEmpty(typeRes.getOrderDetailType())) {
                orderDetailType = typeRes.getOrderDetailType()
                        .stream()
                        .map(item -> {
                            EnumVO enumVo = new EnumVO();
                            enumVo.setLabel(item.getLabel());
                            enumVo.setValue(item.getValue());
                            return enumVo;
                        })
                        .collect(Collectors.toList());
            }
            if (CollectionUtil.isNotEmpty(typeRes.getOrderType())) {
                orderVTypeList = typeRes.getOrderType()
                        .stream()
                        .map(item -> {
                            EnumVO enumVo = new EnumVO();
                            enumVo.setLabel(item.getLabel());
                            enumVo.setValue(item.getValue());
                            return enumVo;
                        })
                        .collect(Collectors.toList());
            }
        }
        return new MarketingEnumsRes()
                .setOrderVTypeList(orderVTypeList)
                .setPayMethodList(payMethodList.stream().map(item -> new EnumVO().setValue(item.getValue()).setLabel(item.getName())).collect(Collectors.toList()))
                .setOrderDeliveryList(orderDeliveryList.stream().map(item -> new EnumVO().setValue(item.getValue()).setLabel(item.getName())).collect(Collectors.toList()))
                .setOrderDetailType(orderDetailType);
    }

    @Override
    public AfterSalesEnumsRes getAfterSalesEnums() {
        Integer xtenant = (int) Namespaces.get();
        // 通过门店信息获取printname
        R<AreaInfo> areaInfo = areaInfoClient.getAreaInfoById(abstractCurrentRequestComponent.getCurrentStaffId()
                .getAreaId());
        String printName = CommonUtils.isRequestSuccess(areaInfo) ? areaInfo.getData().getPrintName() : " ";
        // 售后维修状态
        List<EnumVO> orderRepairStatus = EnumUtil.toEnumVOList(OrderRepairStatusEnum.class);
        // 售后保修状态
        List<EnumVO> orderWarrantyStatus = EnumUtil.toEnumVOList(OrderWarrantyStatusEnum.class);
        // 售后九机服务
        List<EnumVO> orderServiceType = EnumUtil.toEnumVOList(OrderServiceTypeEnum.class);
        EnumVO serviceType = new EnumVO();
        serviceType.setLabel(JiujiTenantEnum.JIUJI_TENANT_JIUJI.getMessage().equals(printName) ? "非九机服务" : "非" + printName + "服务");
        serviceType.setValue("3");
        orderServiceType.add(serviceType);
        // 售后维修等级
        List<EnumVO> orderRepairRank = EnumUtil.toEnumVOList(OrderRepairRankEnum.class);
        // 售后维修方式
        List<EnumVO> orderRepairType = EnumUtil.toEnumVOList(OrderRepairTypeEnum.class);

        orderRepairType.get(0).setLabel(printName + orderRepairType.get(0).getLabel());

        // 售后维修机器类型
        List<EnumVO> orderRepairProductType = EnumUtil.toEnumVOList(OrderRepairProductTypeEnum.class);
        // 售后订单来源

        OrderSourceRes orderSourceResNormal = new OrderSourceRes();
        orderSourceResNormal.setLabel("正常接件");
        orderSourceResNormal.setValue(1);
        OrderSourceRes orderSourceResYuyue = new OrderSourceRes();
        orderSourceResYuyue.setLabel("预约接件");
        orderSourceResYuyue.setValue(0);
        orderSourceResYuyue.setChildren(EnumUtil.toEnumVOList(OrderSourceReserveEnum.class));
        return new AfterSalesEnumsRes()
                .setOrderRepairStatus(orderRepairStatus)
                .setOrderWarrantyStatus(orderWarrantyStatus)
                .setOrderServiceType(orderServiceType)
                .setOrderRepairRank(orderRepairRank)
                .setOrderRepairType(orderRepairType)
                .setOrderRepairProductType(orderRepairProductType)
                .setOrderSource(Arrays.asList(orderSourceResNormal, orderSourceResYuyue));
    }

    @Override
    public OperatorEnumsRes getOperatorEnums() {
        // 运营商类型
        List<SysConfigNameValueVO> operatorKind = salaryDao.getOperatorKind();
        return new OperatorEnumsRes().setOperatorKind(operatorKind);
    }

    @Override
    public List<SaleOrder> listSaleOrder(Integer ch999Id, LocalDateTime startDate, LocalDateTime endDate, List<Integer> mkcIds, List<Integer> oldMkcIds, boolean isJiuJi) {
        return salaryDao.listSaleOrder(ch999Id, startDate, endDate, mkcIds, oldMkcIds, null, isJiuJi);
    }

    @Override
    public List<RetrunOrderEx> listReturnOrder(Integer ch999Id, LocalDateTime startDate, LocalDateTime endDate) {
        boolean b = endDate.isBefore(LocalDateTime.of(LocalDate.of(2023, 5, 1), LocalTime.MIN));
        return b ? salaryDao.listReturnOrder(ch999Id, startDate, endDate) : salaryRecoverDao.listReturnOrder(ch999Id, startDate, endDate, SpringContext.isJiuJiEnvironment());
    }

    @Override
    public List<AfterOrder> listAfterOrder(Integer ch999Id, LocalDateTime startDate, LocalDateTime endDate) {
        return salaryDao.listAfterOrder(ch999Id, startDate, endDate);
    }

    @Override
    public SalaryConfigTotalLogRes calculateCommission(SalaryCalculateReq salaryCalculateReq, Map<Integer, ZhiWuVO> zhiWuVOMap, Integer xtenant) {
        try {
            return calculateCommissionV2(salaryCalculateReq, zhiWuVOMap, xtenant);
        } catch (Exception e) {
            log.error("自定义薪酬提成获取异常：{},{}", e.getMessage(), salaryCalculateReq.getCh999Id(), e);
        }
        return new SalaryConfigTotalLogRes();
    }

    public SalaryConfigTotalLogRes callSalary(SalaryCalculateReq salaryCalculateReq) {
        return callSalaryV1(salaryCalculateReq).getT1();
    }

    public Tuple2<SalaryConfigTotalLogRes, UserReq> callSalaryV1(SalaryCalculateReq salaryCalculateReq) {
        InspireSalaryReq inspireSalaryReq = getCalculateSalaryData(salaryCalculateReq);
        String url = InnerProfileJudgeUtil.isJiuJiTest() ? "https://moa.dev.9ji.com/salary_service/api/salaryConfig/getInspireSalary" : "https://moa.9ji.com/salary_service/api/salaryConfig/getInspireSalary";
        //String url = "localhost:11018/salary_service/api/salaryConfig/getInspireSalary";
        if (salaryCalculateReq.getTrackingProcess()) {
            inspireSalaryReq.setSalaryId(salaryCalculateReq.getSalaryId());
            inspireSalaryReq.setSubId(salaryCalculateReq.getSubId());
            inspireSalaryReq.setPpId(salaryCalculateReq.getPpId());
        }
        String domain = StringUtils.isEmpty(salaryCalculateReq.getDomain()) ? inspireSalaryReq.getDomain() : salaryCalculateReq.getDomain();
        JSONObject jsonObject = JSON.parseObject(HttpRequest.post(url)
                .header("Authorization", salaryCalculateReq.getToken())
                .header("x-tenant-scope", domain)
                .body(JSON.toJSONString(inspireSalaryReq), "application/json")
                .execute().body());
        return Tuples.of(JSON.parseObject(jsonObject.get("data").toString(), SalaryConfigTotalLogRes.class), inspireSalaryReq.getUserReq());
    }

    private void getMckIds(List<Integer> mckIds, List<Integer> oldMkcIds, String ch999Name) {
        if (StringUtils.isEmpty(ch999Name)) {
            return;
        }
        String url = jiujiSystemProperties.getWeb() + "/web/api/bargain/getStaffBargain/v1?staffName=" + ch999Name;
        JSONObject jsonObject = JSON.parseObject(HttpRequest.get(url).execute().body());
        if (Integer.parseInt(jsonObject.get("code").toString()) == 0) {
            JSONObject dataJson = JSONObject.parseObject(jsonObject.get("data").toString());
            mckIds = JSONObject.parseArray(dataJson.get("bigList").toString(), Integer.class);
            oldMkcIds = JSONObject.parseArray(dataJson.get("displayList").toString(), Integer.class);
        }
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    @Cached(name = "oaOrder.service.SalaryRecoverDao.getCaiWuApplySubDtoList", expire = 10, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.REMOTE, key = "#startDate+'-'+#endDate")
    public List<CaiWuApplySubDto> getCaiWuApplySubDtoList(LocalDateTime startDate, LocalDateTime endDate) {
        //自定义薪酬销售类（新机、良品）计算提成时，如果订单关联了财务支出单，在计算提成时剔除对应订单，不计算提成
        List<CaiWuApplySubDto> reaCaiWuApplySubDtoList = new ArrayList<>();

        List<CaiWuApplySubDto> caiWuApplySubDtoList = salaryRecoverDao.getCaiWuApplySubDtoList(startDate, endDate.plusMonths(2));
        if (CollUtil.isEmpty(caiWuApplySubDtoList)) {
            return reaCaiWuApplySubDtoList;
        }
        caiWuApplySubDtoList.forEach(dto -> {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(dto.getSubIdStr()) && dto.getSubIdStr().contains(",")) {
                Arrays.asList(dto.getSubIdStr().split(",")).forEach(item -> {
                    String subStr = org.apache.commons.lang3.StringUtils.deleteWhitespace(item);
                    if (org.apache.commons.lang3.StringUtils.isNumeric(subStr)) {
                        reaCaiWuApplySubDtoList.add(new CaiWuApplySubDto().setType(dto.getType()).setSubId(Integer.parseInt(subStr)));
                    }
                });
            } else {
                String subStr = org.apache.commons.lang3.StringUtils.deleteWhitespace(dto.getSubIdStr());
                if (org.apache.commons.lang3.StringUtils.isNumeric(subStr)) {
                    reaCaiWuApplySubDtoList.add(new CaiWuApplySubDto().setType(dto.getType()).setSubId(Integer.parseInt(subStr)));
                }
            }
        });
        return reaCaiWuApplySubDtoList;
    }

    @Override
    public SalaryConfigTotalLogRes calculateCommissionV2(SalaryCalculateReq salaryCalculateReq, Map<Integer, ZhiWuVO> zhiWuVOMap, Integer xtenant) {
        if (zhiWuVOMap == null) {
            R<List<ZhiWuVO>> listR = userInfoClient.listSimpleZhiWuInfo();
            zhiWuVOMap = listR.getData().stream().collect(Collectors.toMap(ZhiWuVO::getId, Function.identity()));
        }

        Tuple2<SalaryConfigTotalLogRes, UserReq> tuple2 = callSalaryV1(salaryCalculateReq.setTrackingProcess(false));
        SalaryConfigTotalLogRes res = tuple2.getT1();

        List<Long> salaryIdList = res.getSalaryConfigLogRes().stream().map(SalaryConfigLogRes::getSalaryId).collect(Collectors.toList());
        HashSet<Long> salaryIdSet = new HashSet<>(salaryIdList);
        if (salaryIdList.size() != salaryIdSet.size()) {
            log.error("自定义薪酬提成获取异常：{}", salaryIdList.stream().map(Objects::toString).collect(Collectors.joining(",")));
            return res;
        }

        // 导入的赛马
        List<RacingDataDto> racingList = salaryDao.listRacing(salaryCalculateReq.getStartDate().toLocalDate(),
                salaryCalculateReq.getEndDate().toLocalDate(), salaryCalculateReq.getCh999Id());
        res.getSalaryConfigLogRes().addAll(racingList.stream().map(p -> {
            SalaryConfigLogRes salaryConfigLogRes = new SalaryConfigLogRes(p.getAmount(), p.getSalaryName(),
                    p.getSalaryCategoryId(), p.getSalaryCategoryName(), p.getSalaryChildCategoryId(), p.getSalaryChildCategoryName());
            salaryConfigLogRes.setCh999Id(salaryCalculateReq.getCh999Id());
            salaryConfigLogRes.setSalaryId(0L);
            salaryConfigLogRes.setSalaryType(2);
            salaryConfigLogRes.setSalaryKinds("导入");
            salaryConfigLogRes.setSalaryMode("导入");
            return salaryConfigLogRes;
        }).collect(Collectors.toList()));
        res.setTotalMoney(res.getTotalMoney().add(racingList.stream().map(RacingDataDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));

        if (!ActiveProfileJudgeUtil.isJiuJiEnvironment()) {
            return res;
        }
        try {
            res = daiPeiHandler(tuple2.getT2(), res, salaryCalculateReq, zhiWuVOMap);
        } catch (Exception e) {
            log.error("获取带培信息异常", e);
        }

        return res;
    }

    @Override
    public List<RecoverDiffNewDto> getRecoverDiffList(List<Integer> subIds) {
        return salaryRecoverDao.getRecoverDiffNewDto(subIds, SpringContext.isJiuJiEnvironment());
    }

    public SalaryConfigTotalLogRes daiPeiHandler(UserReq userReq, SalaryConfigTotalLogRes res, SalaryCalculateReq salaryCalculateReq, Map<Integer, ZhiWuVO> zhiWuVOMap) {
        //是否应该有带培提成，八大区下才有
        if (Boolean.FALSE.equals(salaryResDao.hasDaiPeiSalary(userReq.getAreaId()))) {
            return res;
        }
        // 带培
        String formatYm = salaryCalculateReq.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMM"));
        List<DaiPeiInfoDto> daiPeiInfoDtoList = salaryResDao.getDaiPeiInfoDtoList(userReq.getCh999Id(), formatYm,
                salaryCalculateReq.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                salaryCalculateReq.getEndDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        // 没有带培信息直接返回，有带培信息才有下面的逻辑
        if (CollUtil.isEmpty(daiPeiInfoDtoList)) {
            return res;
        }
        // 没勋章直接返回
        if (userReq.getMedalsMap() == null
                || !userReq.getMedalsMap().stream().flatMap(map -> map.entrySet().stream()).map(Map.Entry::getValue).collect(Collectors.toList()).contains("前端带培达人")) {
            return res;
        }
        DateTimeFormatter ymFmt = DateTimeFormatter.ofPattern("yyyyMM");
        // 有勋章的话看是不是前端的店长、副店长、主管，是的话带培一个人固定给300元提成。如果不是的话，按比例算带培奖金，带培一人按被带培人提成80%计算，带培两人按各自被带培人的60%计算
        ZhiWuVO zhiWuVO = zhiWuVOMap.get(Integer.parseInt(userReq.getPosition()));
        if (zhiWuVO != null && zhiWuVO.getZwType() == 1 && Arrays.asList("店长", "副店长", "主管", "门店主管").contains(zhiWuVO.getName())) {
            List<SalaryConfigLogRes> daiPeiList = daiPeiInfoDtoList.stream()
                    // 员工入职当月+入职次月，每月发放300元
                    .filter(p -> p.getInDate() != null
                            && (Objects.equals(formatYm, p.getInDate().format(ymFmt))
                            || Objects.equals(formatYm, p.getInDate().plusMonths(1).format(ymFmt))))
                    .map(p -> {
                        SalaryConfigLogRes salaryConfigLogRes = new SalaryConfigLogRes();
                        salaryConfigLogRes.setCh999Id(p.getCh999Id());
                        salaryConfigLogRes.setCh999Name(p.getCh999Name());
                        salaryConfigLogRes.setDaiPeiId(p.getDaiPeiId());
                        salaryConfigLogRes.setAmount(new BigDecimal("300"));
                        salaryConfigLogRes.setSalaryName("带培");
                        salaryConfigLogRes.setSalaryId(0L);
                        salaryConfigLogRes.setSalaryType(4);
                        salaryConfigLogRes.setSalaryKinds("带培");
                        salaryConfigLogRes.setSalaryMode("带培");
                        return salaryConfigLogRes;
                    }).collect(Collectors.toList());
            res.getSalaryConfigLogRes().addAll(daiPeiList);
            res.setTotalMoney(res.getTotalMoney().add(daiPeiList.stream().map(SalaryConfigLogRes::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
            res.setFixDaiPei(true);
            return res;
        }
        // callSalary
        daiPeiInfoDtoList = daiPeiInfoDtoList.stream().filter(daiPeiInfoDto -> daiPeiInfoDto.getInDateDiffStart() <= 50 && daiPeiInfoDto.getInDateDiffEnd() >= 0).collect(Collectors.toList());
        BigDecimal rate = new BigDecimal("0.6");
        daiPeiInfoDtoList.forEach(daiPeiInfoDto -> {
            SalaryCalculateReq daiPeiReq = new SalaryCalculateReq();
            daiPeiReq.setCh999Id(daiPeiInfoDto.getCh999Id()).setAreaId(salaryCalculateReq.getAreaId())
                    .setStartDate(salaryCalculateReq.getStartDate()).setEndDate(salaryCalculateReq.getEndDate())
                    .setIsDaiPei(true).setInDate(daiPeiInfoDto.getInDate());
            SalaryConfigTotalLogRes daiPeiRes = callSalary(daiPeiReq);
            if (daiPeiRes != null) {
                List<SalaryConfigLogRes> daiPeiList = daiPeiRes.getSalaryConfigLogRes().stream()
                        .peek(item -> {
                            item.setAmount(item.getAmount().multiply(rate).setScale(2, RoundingMode.HALF_UP));
                            item.setCh999Id(daiPeiInfoDto.getCh999Id());
                            item.setCh999Name(daiPeiInfoDto.getCh999Name());
                            item.setDaiPeiId(daiPeiInfoDto.getDaiPeiId());
                        }).collect(Collectors.toList());
                res.getSalaryConfigLogRes().addAll(daiPeiList);
                res.setTotalMoney(res.getTotalMoney().add(daiPeiList.stream().map(SalaryConfigLogRes::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)).setScale(2, RoundingMode.HALF_UP));
            }
        });
        return res;
    }

    private List<SaleOrder> saleOrdersGetOrderPayType(List<SaleOrder> saleOrders, LocalDateTime startDate, LocalDateTime endDate) {
        if (CollectionUtils.isEmpty(saleOrders)) {
            return new ArrayList<>(0);
        }
        if (SpringContext.isJiuJiEnvironment()) {
            List<CaiWuApplySubDto> reaCaiWuApplySubDtoList = getCaiWuApplySubDtoList(startDate, endDate);
            if (CollUtil.isNotEmpty(reaCaiWuApplySubDtoList)) {
                Map<Integer, List<CaiWuApplySubDto>> listMap = reaCaiWuApplySubDtoList.stream().collect(Collectors.groupingBy(CaiWuApplySubDto::getType));
                saleOrders = saleOrders.stream().filter(order -> {
                    if (order.getOrderType().equals("1-1") || order.getOrderType().equals("1-3")) {
                        return !listMap.getOrDefault(0, new ArrayList<>()).stream().map(CaiWuApplySubDto::getSubId).collect(Collectors.toList()).contains(order.getSubId());
                    } else if (order.getOrderType().equals("1-2")) {
                        return !listMap.getOrDefault(1, new ArrayList<>()).stream().map(CaiWuApplySubDto::getSubId).collect(Collectors.toList()).contains(order.getSubId());
                    }
                    return true;
                }).collect(Collectors.toList());
            }
        }

        // 新机单和一手优品单
        List<Integer> newAndBestSaleOrders = new ArrayList<>();
        // 良品单
        List<Integer> goodSaleOrders = new ArrayList<>();
        saleOrders.forEach(item -> {
            if (item.getOrderType().equals("1-1") || item.getOrderType().equals("1-3")) {
                newAndBestSaleOrders.add(item.getSubId());
            } else if (item.getOrderType().equals("1-2")) {
                goodSaleOrders.add(item.getSubId());
            }
        });

        Map<Integer, List<SaleOrderPayType>> newAndBestMap = new HashMap<>();
        Map<Integer, List<SaleOrderPayType>> goodMap = new HashMap<>();
        Map<Integer, List<ShouYinSubPayDto>> shouYinSubPayMap = new HashMap<>();
        Map<Integer, List<NationalSubCouponDto>> nationalSubCouponMap = new HashMap<>();

        CompletableFuture<Void> newAndBestMapC = null;
        CompletableFuture<Void> goodMapC = null;
        if (!CollectionUtils.isEmpty(newAndBestSaleOrders)) {
            newAndBestMapC = CompletableFuture
                    .supplyAsync(() -> newAndBestGroupQuery(newAndBestSaleOrders))
                    .thenApplyAsync(newAndBestList -> newAndBestList.stream().collect(Collectors.groupingBy(SaleOrderPayType::getSubId)))
                    .thenAcceptAsync(newAndBestMap::putAll);
        }
        if (!CollectionUtils.isEmpty(goodSaleOrders)) {
            goodMapC = CompletableFuture
                    .supplyAsync(() -> goodGroupQuery(goodSaleOrders))
                    .thenApplyAsync(goodList -> goodList.stream().collect(Collectors.groupingBy(SaleOrderPayType::getSubId)))
                    .thenAcceptAsync(goodMap::putAll);
        }
        List<SaleOrder> finalSaleOrders = saleOrders;
        CompletableFuture<Void> shouYinSubPayC = CompletableFuture
                .supplyAsync(() -> getShouYinSubPay(finalSaleOrders.stream().map(BusinessDataCommon::getSubId).collect(Collectors.toList())))
                .thenApplyAsync(shouYinSubPayDtoList -> shouYinSubPayDtoList.stream().collect(Collectors.groupingBy(ShouYinSubPayDto::getSubId)))
                .thenAcceptAsync(shouYinSubPayMap::putAll);

        if (!SpringContext.isJiuJiEnvironment()) {
            CompletableFuture<Void> nationalSubCouponMapC = CompletableFuture
                    .supplyAsync(() -> salaryDao.getNationalSubCouponDto(startDate, endDate))
                    .thenApplyAsync(nationalSubCouponDtoList -> nationalSubCouponDtoList.stream().collect(Collectors.groupingBy(NationalSubCouponDto::getSubId)))
                    .thenAcceptAsync(nationalSubCouponMap::putAll);;
            nationalSubCouponMapC.join();
        }

        if (newAndBestMapC != null) {
            newAndBestMapC.join();
        }
        if (goodMapC != null) {
            goodMapC.join();
        }
        if (shouYinSubPayC != null) {
            shouYinSubPayC.join();
        }

        for (SaleOrder item : saleOrders) {
            if (!CollectionUtils.isEmpty(newAndBestSaleOrders) && (item.getOrderType().equals("1-1") || item.getOrderType().equals("1-3"))) {
                List<SaleOrderPayType> SaleOrderPayTypeList = newAndBestMap.get(item.getSubId());
                if (!CollectionUtils.isEmpty(SaleOrderPayTypeList)) {
                    item.setOrderPayType(SaleOrderPayTypeList.stream().map(SaleOrderPayType::getOrderPayType).collect(Collectors.joining(",")));
                }
            } else if (!CollectionUtils.isEmpty(goodSaleOrders) && item.getOrderType().equals("1-2")) {
                List<SaleOrderPayType> SaleOrderPayTypeList = goodMap.get(item.getSubId());
                if (!CollectionUtils.isEmpty(SaleOrderPayTypeList)) {
                    item.setOrderPayType(SaleOrderPayTypeList.stream().map(SaleOrderPayType::getOrderPayType).collect(Collectors.joining(",")));
                }
            }
            List<ShouYinSubPayDto> shouYinSubPayDtoList = shouYinSubPayMap.get(item.getSubId());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(shouYinSubPayDtoList)) {
                List<String> shouYinSubPayList = new ArrayList<>();
                shouYinSubPayDtoList.forEach(shouYinSubPayDto -> shouYinSubPayList.addAll(Arrays.asList(shouYinSubPayDto.getSubPay01(), shouYinSubPayDto.getSubPay02(), shouYinSubPayDto.getSubPay03(),
                        shouYinSubPayDto.getSubPay06(), shouYinSubPayDto.getSubPay07(), shouYinSubPayDto.getSubPay08())));
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(shouYinSubPayList)) {
                    String subPay = shouYinSubPayList.stream().filter(Objects::nonNull).collect(Collectors.joining(","));
                    item.setOrderPayType(org.apache.commons.lang3.StringUtils.isNotEmpty(item.getOrderPayType()) ? item.getOrderPayType() + "," + subPay : subPay);
                }
            }
            if (!SpringContext.isJiuJiEnvironment() && nationalSubCouponMap.containsKey(item.getSubId())) {
                List<NationalSubCouponDto> nationalSubCouponDto = nationalSubCouponMap.get(item.getSubId());
                BigDecimal allYouhuiPrice = nationalSubCouponDto.stream()
                        .map(NationalSubCouponDto::getYouhuiPrice)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                item.setTrueProfit(item.getTrueProfit().add(allYouhuiPrice));
                item.setTrueProfit1(item.getTrueProfit1().add(allYouhuiPrice));
                item.setTrueProfit2(item.getTrueProfit2().add(allYouhuiPrice));
                item.setTrueProfit3(item.getTrueProfit3().add(allYouhuiPrice));
            }
        }
        return saleOrders;
    }

    private List<SaleOrderPayType> newAndBestGroupQuery(List<Integer> newAndBestSaleOrders) {
        newAndBestSaleOrders = newAndBestSaleOrders.stream().distinct().collect(Collectors.toList());
        //不要修改分页值，否则报错。传入的请求具有过多的参数。该服务器支持最多+2100+个参数。请减少参数的数目，然后重新发送该请求。
        int size = 699;
        if (newAndBestSaleOrders.size() <= size) {
            return salaryDao.newAndBestList(newAndBestSaleOrders);
        }
        List<List<Integer>> list = CollUtil.split(newAndBestSaleOrders, size);
        List<SaleOrderPayType> resultList = new ArrayList<>();
        list.forEach(item -> resultList.addAll(salaryDao.newAndBestList(item)));
        return resultList;
    }

    private List<SaleOrderPayType> goodGroupQuery(List<Integer> goodSaleOrders) {
        goodSaleOrders = goodSaleOrders.stream().distinct().collect(Collectors.toList());
        if (goodSaleOrders.size() <= 2000) {
            return salaryDao.goodList(goodSaleOrders);
        }
        List<List<Integer>> list = CollUtil.split(goodSaleOrders, 2000);
        List<SaleOrderPayType> resultList = new ArrayList<>();
        list.forEach(item -> resultList.addAll(salaryDao.goodList(item)));
        return resultList;
    }

    private List<ShouYinSubPayDto> getShouYinSubPay(List<Integer> subIds) {
        List<ShouYinSubPayDto> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(subIds)) {
            return resultList;
        }
        subIds = subIds.stream().distinct().collect(Collectors.toList());
        if (subIds.size() <= 2000) {
            return salaryDao.getShouYinSubPay(subIds);
        }
        List<List<Integer>> list = CollUtil.split(subIds, 2000);
        list.forEach(item -> resultList.addAll(salaryDao.getShouYinSubPay(item)));
        return resultList;
    }

    @Override
    public R<List<SaleOrder>> calculateSaleCommission(CalculateSaleReq calculateSaleReq) {
        Integer xtenant = (int) Namespaces.get();
        List<SaleOrder> saleOrderList;
        if (calculateSaleReq == null || CollectionUtils.isEmpty(calculateSaleReq.getAreaId())
                || StringUtils.isEmpty(calculateSaleReq.getOrderType())) {
            return R.error("type 参数错误");
        }
        //是否开启考核毛利
        Integer isOpenCheck = salaryDao.getCheckProfit(xtenant);
        switch (calculateSaleReq.getOrderType()) {
            //新机单
            case "1-1":
                saleOrderList = salaryDao.listSaleOrderNew(calculateSaleReq, isOpenCheck);
                break;
            //良品单
            case "1-2":
                saleOrderList = salaryDao.listSaleOrderGoodProduct(calculateSaleReq, isOpenCheck, SpringContext.isJiuJiEnvironment());
                break;
            //一手优品
            case "1-3":
                saleOrderList = salaryDao.listSaleOrderBestProduct(calculateSaleReq, isOpenCheck);
                break;
            default:
                return R.error("type 参数错误");
        }
        //过滤排除的ppId
        if (!CollectionUtils.isEmpty(calculateSaleReq.getExcludePpId()) && !CollectionUtils.isEmpty(saleOrderList)) {
            saleOrderList.stream()
                    .filter(item -> !calculateSaleReq.getExcludePpId().contains(item.getPpId()))
                    .collect(Collectors.toList());
        }
        // 销售数据设置支付方式
        saleOrdersGetOrderPayType(saleOrderList, calculateSaleReq.getStartDate(), calculateSaleReq.getEndDate());
        // 根据请求体支付方式过滤数据
        saleOrderList = CollectionUtils.isEmpty(calculateSaleReq.getOrderPayType())
                ? saleOrderList
                : saleOrderList.stream().filter(item -> {
            if (!StringUtils.isEmpty(item.getOrderPayType())) {
                List<String> orderPayType = Arrays.asList(item.getOrderPayType().split(","));
                if (Collections.disjoint(orderPayType, calculateSaleReq.getOrderPayType())) {
                    return true;
                }
            }
            return false;
        }).collect(Collectors.toList());
        return R.success(saleOrderList);
    }

    @Override
    public R<List<ReturnOrder>> calculateReturnCommission(CalculateReturnReq calculateReturnReq) {
        List<ReturnOrder> returnOrderList;
        if (calculateReturnReq == null || CollectionUtils.isEmpty(calculateReturnReq.getAreaId())
                || StringUtils.isEmpty(calculateReturnReq.getOrderType())) {
            return R.error("type 参数错误");
        }
        boolean b = calculateReturnReq.getEndDate().isBefore(LocalDateTime.of(LocalDate.of(2023, 5, 1), LocalTime.MIN));
        switch (calculateReturnReq.getOrderType()) {
            //回收单
            case "2-1":
                returnOrderList = salaryDao.listReturnOrderReturn(calculateReturnReq, SpringContext.isJiuJiEnvironment());
                returnOrderList = getRecoverDiffNewDto(returnOrderList);
                break;
            //帮卖单
            case "2-2":
                returnOrderList = salaryDao.listReturnOrderHelpSell(calculateReturnReq, SpringContext.isJiuJiEnvironment());
                break;
            //回收毛利
            case "2-3":
                if (b) {
                    returnOrderList = salaryDao.listReturnOrderProfit(calculateReturnReq, SpringContext.isJiuJiEnvironment());
                } else {
                    returnOrderList = salaryRecoverDao.listReturnOrderProfit(calculateReturnReq, SpringContext.isJiuJiEnvironment());
                    returnOrderList = getRecoverDiffNewDto(returnOrderList);
                    returnOrderList = listReturnOrderSettlementProfit(returnOrderList, null, calculateReturnReq.getAreaId(), calculateReturnReq.getStartDate(), calculateReturnReq.getEndDate());
                }
                break;
            default:
                return R.error("type 参数错误");
        }
        if (!CollectionUtils.isEmpty(calculateReturnReq.getExcludePpId()) && !CollectionUtils.isEmpty(returnOrderList)) {
            returnOrderList.stream()
                    .filter(item -> !calculateReturnReq.getExcludePpId().contains(item.getPpId()))
                    .collect(Collectors.toList());
        }
        return R.success(returnOrderList);
    }

    @Override
    public R<List<AfterOrder>> calculateAfterCommission(CalculateAfterReq calculateAfterReq) {
        List<AfterOrder> afterOrderList;
        if (calculateAfterReq == null || CollectionUtils.isEmpty(calculateAfterReq.getAreaId())
                || StringUtils.isEmpty(calculateAfterReq.getOrderType())) {
            return R.error("type 参数错误");
        }
        switch (calculateAfterReq.getOrderType()) {
            //软件在保
            case "3-1":
                afterOrderList = salaryDao.listAfterOrderSoftUnderWarranty(calculateAfterReq);
                break;
            //软件不在保
            case "3-2":
                afterOrderList = salaryDao.listAfterOrderNotSoftUnderWarranty(calculateAfterReq);
                break;
            //硬件接件
            case "3-3":
                afterOrderList = salaryDao.listAfterOrderHardFit(calculateAfterReq);
                break;
            default:
                return R.error("type 参数错误");
        }
        if (!CollectionUtils.isEmpty(calculateAfterReq.getExcludePpId()) && !CollectionUtils.isEmpty(afterOrderList)) {
            afterOrderList.stream()
                    .filter(item -> !calculateAfterReq.getExcludePpId().contains(item.getPpId()))
                    .collect(Collectors.toList());
        }
        return R.success(afterOrderList);
    }

    @Override
    public R<List<OperatorOrder>> calculateOperatorCommission(CalculateOperatorReq calculateOperatorReq) {
        List<OperatorOrder> operatorOrderList;
        if (calculateOperatorReq == null || CollectionUtils.isEmpty(calculateOperatorReq.getAreaId())
                || StringUtils.isEmpty(calculateOperatorReq.getOrderType())) {
            return R.error("type 参数错误");
        }
        operatorOrderList = SpringContext.isJiuJiEnvironment() ? salaryOperatorOrderDao.listOperatorOrderOperator(calculateOperatorReq)
                : salaryDao.listOperatorOrderOperator(calculateOperatorReq);
        if (!CollectionUtils.isEmpty(calculateOperatorReq.getExcludePpId()) && !CollectionUtils.isEmpty(operatorOrderList)) {
            operatorOrderList = operatorOrderList.stream()
                    .filter(item -> !calculateOperatorReq.getExcludePpId().contains(item.getPpId()))
                    .collect(Collectors.toList());
        }
        return R.success(operatorOrderList);
    }

    @Override
    public R<List<BrandRes.BrandDetailRes>> getBrands() {
        return R.success(salaryDao.getBrands());
    }

    @Override
    public SpecificSalaryAppRes appCalculate(SalaryCalculateReq salaryCalculateReq) {
        SpecificSalaryAppRes specificSalaryAppRes = new SpecificSalaryAppRes().setSaleData(new LinkedList<>()).setTotalMoney(BigDecimal.ZERO);
        final SalaryConfigTotalLogRes salaryConfigTotalLogRes = calculateCommission(salaryCalculateReq, null, null);
        if (salaryConfigTotalLogRes.getSalaryConfigLogRes() == null) {
            return specificSalaryAppRes;
        }
        // 设置总金额
        specificSalaryAppRes.setTotalMoney(salaryConfigTotalLogRes.getTotalMoney());
        // 设置大类信息
        getSpecificSalaryAppRes(specificSalaryAppRes, salaryConfigTotalLogRes.getSalaryConfigLogRes().stream().filter(item -> item.getDaiPeiId() == null).collect(Collectors.groupingBy(SalaryConfigLogRes::getSalaryCategoryId)));
        getSpecificSalaryAppRes(specificSalaryAppRes, true, salaryConfigTotalLogRes.getFixDaiPei(), salaryConfigTotalLogRes.getSalaryConfigLogRes().stream().filter(item -> item.getDaiPeiId() != null).collect(Collectors.toList()));

        if (InnerProfileJudgeUtil.isJiuJi() || InnerProfileJudgeUtil.isJiuJiTest()) {
            //获取其他信息
            String appCalculateUrl = "/App/GetUserCommission?startDate={0}&endDate={1}";
            String url = MessageFormat.format(appCalculateUrl,
                    salaryCalculateReq.getStartDate().format(DateTimeFormatter.ofPattern(DateTimeFormatterUtil.YMD_STR)),
                    salaryCalculateReq.getEndDate().format(DateTimeFormatter.ofPattern(DateTimeFormatterUtil.YMD_STR)));
            String body = HttpRequest.get(jiujiSystemProperties.getMoa() + url).header("Authorization", salaryCalculateReq.getToken()).execute().body();
            specificSalaryAppRes.getSaleData().addAll(getBigTypeDataList(JSON.parseObject(body)));
        }
        return specificSalaryAppRes;
    }

    void getSpecificSalaryAppRes(SpecificSalaryAppRes specificSalaryAppRes, Map<Integer, List<SalaryConfigLogRes>> bigCollection) {
        if (CollectionUtils.isEmpty(bigCollection)) {
            return;
        }
        for (Map.Entry<Integer, List<SalaryConfigLogRes>> bigItem : bigCollection.entrySet()) {
            List<SalaryConfigLogRes> value = bigItem.getValue();
            getSpecificSalaryAppRes(specificSalaryAppRes, false, false, value);
        }
    }

    void getSpecificSalaryAppRes(SpecificSalaryAppRes specificSalaryAppRes, boolean isDaiPei, Boolean fixDaiPei, List<SalaryConfigLogRes> bigCollection) {
        if (CollectionUtils.isEmpty(bigCollection)) {
            return;
        }
        // 大类信息转换
        SpecificSalaryAppRes.BigTypeData bigTypeData = new SpecificSalaryAppRes.BigTypeData()
                .setCategoryName(isDaiPei ? "带培提成" : bigCollection.get(0).getSalaryCategoryName()).setChildren(new LinkedList<>());

        Map<Integer, List<SalaryConfigLogRes>> childrenCollection = bigCollection.stream().collect(Collectors.groupingBy(SalaryConfigLogRes::getSalaryChildCategoryId));
        // 子类信息转换置入大类信息实体
        for (Map.Entry<Integer, List<SalaryConfigLogRes>> childrenItem : childrenCollection.entrySet()) {
            List<SalaryConfigLogRes> subValue = childrenItem.getValue();
            SpecificSalaryAppRes.SubclassData subclassData = new SpecificSalaryAppRes.SubclassData();
            subclassData.setAmount(subValue.stream().map(SalaryConfigLogRes::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                    .setChildCategoryName(isDaiPei ? "带培" : subValue.get(0).getSalaryChildCategoryName()).setChildren(new LinkedList<>());
            // 具体信息转换置入子类信息实体
            for (SalaryConfigLogRes config : subValue) {
                SpecificSalaryAppRes.SpecificInfo specificInfo = new SpecificSalaryAppRes.SpecificInfo()
                        .setSalaryMode(config.getSalaryMode()).setAmount(config.getAmount())
                        .setCardinalNumber(fixDaiPei ? config.getAmount() : config.getCardinalNumber())
                        .setSalaryName(isDaiPei ? config.getSalaryName() + "-" + config.getCh999Name() + "(" + config.getCh999Id() + ")" : config.getSalaryName())
                        .setCategoryName(fixDaiPei ? "带培" : config.getSalaryCategoryName())
                        .setSalaryKinds(config.getSalaryKinds()).setContent(new LinkedList<>());

                if (subValue.stream().anyMatch(item -> item.getSalaryType() != 3)) {

                    specificInfo.setHeader();
                    // 设置具体的提成明细
                    for (int i = 0; config.getBusinessDataResList() != null && i < config.getBusinessDataResList().size(); i++) {
                        SalaryConfigLogRes.BusinessDataRes item = config.getBusinessDataResList().get(i);
                        List<String> temp = new LinkedList<>();
                        temp.add(String.valueOf(i + 1));
                        temp.add(item.getSubId() == null ? "" : item.getSubId().toString());
                        temp.add(item.getPpId() == null ? "" : item.getPpId().toString());
                        temp.add(item.getTradeDate() == null ? "" : DateTimeUtils.formatDate(item.getTradeDate(), DATE_FORMATTER));
                        temp.add(item.getReturnDate() == null ? "" : DateTimeUtils.formatDate(item.getReturnDate(), DATE_FORMATTER));
                        specificInfo.getContent().add(temp);
                    }
                }
                subclassData.getChildren().add(specificInfo);
            }
            bigTypeData.getChildren().add(subclassData);
        }
        specificSalaryAppRes.getSaleData().add(bigTypeData);
    }

    @Override
    public List<SalaryRemarkRes> getSalaryRemarks(LocalDate startTime, LocalDate endTime) {
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        DateTimeFormatter fmtYmd = DateTimeFormatter.ofPattern(DateTimeUtils.DATE_FORMAT);
        String url = InnerProfileJudgeUtil.isJiuJiTest() ? "https://moa.dev.9ji.com/salary_service/api/salaryConfig/getRemarks" : "https://moa.9ji.com/salary_service/api/salaryConfig/getRemarks";
        url = MessageFormat.format(url + "?startTime={0}&endTime={1}&xtenant={2}", startTime.format(fmtYmd) + " 00:00:00", endTime.format(fmtYmd) + " 23:59:59", Namespaces.get());
        JSONObject jsonObject = JSON.parseObject(HttpRequest.get(url)
                .header("Authorization", request.getHeader(SalaryConstant.AUTHORIZATION))
                .execute().body());
        if (jsonObject.get("data") == null) {
            return new ArrayList<>();
        }
        return JSONObject.parseArray(jsonObject.get("data").toString(), SalaryRemarkRes.class);
    }

    public ArrayList<SpecificSalaryAppRes.BigTypeData> getBigTypeDataList(JSONObject jsonObject) {
        /**
         * /App/GetUserCommission?startDate=2022-03-01&endDate=2022-03-31，带token
         * "TargetRatio": 1.0114, 目标完成率
         *         "DepartRatio": 1, 部门系数
         *         "AreaRatio": 1.1,门店绩效系数
         *         "InStoreRatio": 1, 驻店系数
         *         "OrganizationalRatio": 1, 编制系数
         *         "OtherCommissionAmount": 57.90其他提成
         */
        ArrayList<SpecificSalaryAppRes.BigTypeData> bigTypeData = new ArrayList<>();
        bigTypeData.add(new SpecificSalaryAppRes.BigTypeData()
                .setCategoryName("目标完成率").setRate(Objects.isNull(jsonObject.get("TargetRatio")) ? "1" : jsonObject.get("TargetRatio").toString()).setChildren(new LinkedList<>()));
        bigTypeData.add(new SpecificSalaryAppRes.BigTypeData()
                .setCategoryName("部门系数").setRate(Objects.isNull(jsonObject.get("DepartRatio")) ? "1" : jsonObject.get("DepartRatio").toString()).setChildren(new LinkedList<>()));
        bigTypeData.add(new SpecificSalaryAppRes.BigTypeData()
                .setCategoryName("门店绩效系数").setRate(Objects.isNull(jsonObject.get("AreaRatio")) ? "1" : jsonObject.get("AreaRatio").toString()).setChildren(new LinkedList<>()));
        bigTypeData.add(new SpecificSalaryAppRes.BigTypeData()
                .setCategoryName("驻店系数").setRate(Objects.isNull(jsonObject.get("InStoreRatio")) ? "1" : jsonObject.get("InStoreRatio").toString()).setChildren(new LinkedList<>()));
        bigTypeData.add(new SpecificSalaryAppRes.BigTypeData()
                .setCategoryName("编制系数").setRate(Objects.isNull(jsonObject.get("OrganizationalRatio")) ? "1" : jsonObject.get("OrganizationalRatio").toString()).setChildren(new LinkedList<>()));
        bigTypeData.add(new SpecificSalaryAppRes.BigTypeData()
                .setCategoryName("其他提成").setRate(Objects.isNull(jsonObject.get("OtherCommissionAmount")) ? "0" : jsonObject.get("OtherCommissionAmount").toString()).setChildren(new LinkedList<>()));
        return bigTypeData;
    }

    @Override
    public InspireSalaryReq getCalculateSalaryData(SalaryCalculateReq salaryCalculateReq) {
        InspireSalaryReq inspireSalaryReq = new InspireSalaryReq();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("自定义薪酬查询数据");
        try {
            // 个人以及门店基础信息
            //如果是九机安照c#的原逻辑去查询xtenant和areaInfo，如果是输出去Namespaces里面取xtenant
            //调整输出获取areaId逻辑
            SalaryBalanceInfoDto salaryBalanceInfoDto = ch999UserMapper.getXtenantOfSalary(salaryCalculateReq.getCh999Id(),
                    salaryCalculateReq.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMM")),
                    ActiveProfileJudgeUtil.isJiuJiEnvironment());
            salaryCalculateReq.setAreaId(salaryBalanceInfoDto.getAreaId());
            int xtenant;
            //如果是互联通，取新的逻辑，隔离
            if (ActiveProfileJudgeUtil.isJiuJiEnvironment() || InnerProfileJudgeUtil.isSassByTenantId("10053") || InnerProfileJudgeUtil.isSassByTenantId("10058")) {
                xtenant = salaryBalanceInfoDto.getXtenant();
            } else if (ActiveProfileJudgeUtil.isZlfEnvironment()) {
                xtenant = 1000;
            } else {
                //取主租户
                xtenant = Integer.parseInt(InnerProfileJudgeUtil.getPropertyByName("jiuji.xtenant"));
            }
            UserReq userReq = getUserInfoByCh999Id(salaryCalculateReq, xtenant, salaryBalanceInfoDto);
            inspireSalaryReq.setUserReq(userReq);
            inspireSalaryReq.setPlatform(XtenantEnum.isJiujiXtenant(xtenant) ? 2 : 0);

            LocalDateTime startDate = salaryCalculateReq.getStartDate().with(TemporalAdjusters.firstDayOfMonth());
            LocalDateTime endDate = salaryCalculateReq.getStartDate().with(TemporalAdjusters.lastDayOfMonth());
            //查询完成率跟机率差异率等不管前面条件进来是什么都默认查询整月的
            LocalDateTime startDateRate = LocalDateTime.of(startDate.getYear(), startDate.getMonth(), startDate.getDayOfMonth(), 0, 0, 0);
            LocalDateTime endDateRate = LocalDateTime.of(endDate.getYear(), endDate.getMonth(), endDate.getDayOfMonth(), 23, 59, 59);
            // 个人完成率情况
            CompletableFuture<Void> userCompletionRateC = CompletableFuture.supplyAsync(() ->
                            this.mapUserCompletionRate(startDateRate, endDateRate)
                                    .getOrDefault(salaryCalculateReq.getCh999Id(), new UserCompletionRate()), executor)
                    .thenAccept(inspireSalaryReq::setUserCompletionRate);
            // 个人差异率情况
            CompletableFuture<Void> recoverCheckUserDiffRateC = CompletableFuture.supplyAsync(() ->
                            this.mapRecoverCheckUserDiffRate(startDateRate, endDateRate)
                                    .getOrDefault(salaryCalculateReq.getCh999Id(), new RecoverCheckUserDiffRate()), executor)
                    .thenAccept(inspireSalaryReq::setRecoverCheckUserDiffRate);
            // 门店完成率情况
            CompletableFuture<Void> areaCompletionRateC = CompletableFuture.supplyAsync(() ->
                            this.mapAreaCompletionRate(startDateRate, endDateRate)
                                    .getOrDefault(userReq.getAreaId(), new AreaCompletionRate()), executor)
                    .thenAccept(inspireSalaryReq::setAreaCompletionRate);

            // 个人回收差异率
            CompletableFuture<Void> diffUserRateC = CompletableFuture.supplyAsync(() ->
                                    Optional.ofNullable(salaryRecoverDao.getRecoverDiffUserRateNewDto(startDateRate, endDateRate, userReq.getCh999Id(), SpringContext.isJiuJiEnvironment()))
                                            .orElseGet(ArrayList::new)
                                            .stream().filter(item -> Objects.equals(item.getCh999Id(), userReq.getCh999Id())).collect(Collectors.toList()),
                            executor)
                    .thenAccept(inspireSalaryReq::setDiffUserRateNewDtoList);
            //门店回收差异率
            CompletableFuture<Void> diffAreaRateC = CompletableFuture.supplyAsync(() ->
                                    salaryRecoverDao.getRecoverDiffAreaRateNewDto(startDateRate, endDateRate, SpringContext.isJiuJiEnvironment())
                                            .stream().filter(item -> Objects.equals(item.getAreaId(), userReq.getAreaId())).findFirst().orElseGet(RecoverDiffAreaRateNewDto::new),
                            executor)
                    .thenAccept(inspireSalaryReq::setDiffAreaRateNewDto);

            // 销售数据
            List<Integer> mckIds = new ArrayList<>();
            List<Integer> oldMkcIds = new ArrayList<>();
            try {
                getMckIds(mckIds, oldMkcIds, userReq.getCh999Name());
            } catch (Exception ex) {
                log.error("获取一手优品信息失败,继续执行提成逻辑!");
            }
            Integer finalTenant = xtenant;
            //销售数据
            CompletableFuture<Void> saleOrders = CompletableFuture.supplyAsync(() -> salaryDao.getCheckProfit(finalTenant), executor)
                    .thenApplyAsync(isOpenCheck ->
                            salaryDao.listSaleOrder(salaryCalculateReq.getCh999Id(), salaryCalculateReq.getStartDate(), salaryCalculateReq.getEndDate(), mckIds, oldMkcIds, isOpenCheck, SpringContext.isJiuJiEnvironment()), executor)
                    .thenApplyAsync(item -> this.saleOrdersGetOrderPayType(item, salaryCalculateReq.getStartDate(), salaryCalculateReq.getEndDate()), executor)
                    .thenAccept(inspireSalaryReq::setSaleOrder);
            if (SpringContext.isJiuJiEnvironment()) {
                saleOrders.thenApplyAsync(item -> salaryDao.getSalesTaskVO(salaryCalculateReq.getStartDate().format(LocalDateTimeUtils.YEAR_MONTH_FORMATTER), userReq.getCh999Id(), userReq.getAreaId()), executor)
                        .thenAccept(inspireSalaryReq::setSalesTaskVO);
            }
            // 回收数据
            //2023-05-01之前使用老逻辑，否则使用新逻辑
            boolean b = salaryCalculateReq.getEndDate().isBefore(LocalDateTime.of(LocalDate.of(2023, 5, 1), LocalTime.MIN));
            CompletableFuture<Void> returnOrders;
            if (b) {
                returnOrders = CompletableFuture.supplyAsync(() -> salaryDao.listReturnOrder(
                                salaryCalculateReq.getCh999Id(),
                                salaryCalculateReq.getStartDate(),
                                salaryCalculateReq.getEndDate()), executor
                        )
                        .thenApplyAsync(this::transformReturnOrderVType, executor)
                        .thenAccept(inspireSalaryReq::setReturnOrder);
            } else {
                returnOrders = CompletableFuture.supplyAsync(() -> salaryRecoverDao.listReturnOrder(
                                salaryCalculateReq.getCh999Id(),
                                salaryCalculateReq.getStartDate(),
                                salaryCalculateReq.getEndDate(), SpringContext.isJiuJiEnvironment()), executor
                        )
                        .thenApplyAsync(this::transformReturnOrderVType, executor)
                        .thenApplyAsync(this::getRecoverDiffNewDto)
                        .thenApplyAsync(item -> this.listReturnOrderSettlementProfit(
                                item,
                                salaryCalculateReq.getCh999Id(),
                                null,
                                salaryCalculateReq.getStartDate(),
                                salaryCalculateReq.getEndDate()), executor)
                        .thenAccept(inspireSalaryReq::setReturnOrder);
            }

            // 售后数据
            CompletableFuture<Void> afterOrders = CompletableFuture.supplyAsync(() ->
                            salaryDao.listAfterOrder(salaryCalculateReq.getCh999Id(), salaryCalculateReq.getStartDate(), salaryCalculateReq.getEndDate()), executor)
                    .thenAccept(inspireSalaryReq::setAfterOrder);
            // 运营数据
            CompletableFuture<Void> operatorOrders = CompletableFuture.supplyAsync(() ->
                                    SpringContext.isJiuJiEnvironment()
                                            ? salaryOperatorOrderDao.listOperatorOrder(salaryCalculateReq.getCh999Id(), salaryCalculateReq.getStartDate(), salaryCalculateReq.getEndDate())
                                            : salaryDao.listOperatorOrder(salaryCalculateReq.getCh999Id(), salaryCalculateReq.getStartDate(), salaryCalculateReq.getEndDate())
                            , executor)
                    .thenAccept(inspireSalaryReq::setOperatorOrder);
            //售后真实毛利
            CompletableFuture<Void> afterOrderTrueProfitVos = CompletableFuture.supplyAsync(() ->
                            salaryDao.getAfterOrderTrueProfitVo(salaryCalculateReq.getCh999Id(), salaryCalculateReq.getStartDate(), salaryCalculateReq.getEndDate()), executor)
                    .thenAccept(inspireSalaryReq::setAfterOrderTrueProfitVos);

            // 等待各个异步任务完成才提交请求
            CompletableFuture<Void> all = CompletableFuture.allOf(userCompletionRateC,
                    recoverCheckUserDiffRateC,
                    areaCompletionRateC,
                    saleOrders,
                    returnOrders,
                    afterOrders,
                    operatorOrders,
                    afterOrderTrueProfitVos,
                    diffUserRateC,
                    diffAreaRateC);
            inspireSalaryReq.setToken(salaryCalculateReq.getToken());
            inspireSalaryReq.setDomain(jiujiSystemProperties.getMoa());
            inspireSalaryReq.setTrackingProcess(salaryCalculateReq.getTrackingProcess());
            all.join();
        } catch (Exception e) {
            log.warn("自定义薪酬提成获取异常：{}", e.getMessage(), e);
            return inspireSalaryReq;
        } finally {
            stopWatch.stop();
            log.info(stopWatch.prettyPrint());
        }
        return inspireSalaryReq;
    }

    @Override
    @Async("asyncServiceExecutor")
    public void taskCalculate(Integer type) {
        TaskCalculateVo taskCalculateVo = TaskCalculateVo.getTaskCalculateVo(type);
        R<List<Ch999UserSalaryVo>> ch999IdListForSalary = userInfoClient.getCh999IdListForSalary(taskCalculateVo.getStartTimeText(), taskCalculateVo.getEndTimeText());
        if (!CommonUtils.isRequestSuccess(ch999IdListForSalary)) {
            log.error("autoCalculateSalary获取用户信息失败");
            return;
        }
        List<Ch999UserSalaryVo> data = ch999IdListForSalary.getData();

        //全额利润薪等
        int liRun = 5;
        String redisSalaryKey = type == 2 ? RedisKeyConstant.CUSTOM_SALARY_REPORT : RedisKeyConstant.AUTO_CALCULATE_SALARY_KEY;
        //第一次请求进来缓存为空，先把工号加入缓存
        if (Objects.requireNonNull(redisTemplate.opsForSet().size(redisSalaryKey)).intValue() == 0
                && (Objects.isNull(redisTemplate.opsForValue().get(RedisKeyConstant.AUTO_CALCULATE_SALARY_FLAG)) || type == 2)
        ) {
            //设置标记并且设置过期时间
            redisTemplate.opsForValue().set(RedisKeyConstant.AUTO_CALCULATE_SALARY_FLAG, 1, 1, TimeUnit.DAYS);
            List<Integer> needCalculateCh999Ids = data.stream().map(Ch999UserSalaryVo::getCh999Id).collect(Collectors.toList());
            needCalculateCh999Ids.forEach(ch999Id -> redisTemplate.opsForSet().add(redisSalaryKey, ch999Id));
        }
        R<List<ZhiWuVO>> listR = userInfoClient.listSimpleZhiWuInfo();
        Map<Integer, ZhiWuVO> zhiWuVOMap = listR.getData().stream().collect(Collectors.toMap(ZhiWuVO::getId, Function.identity()));
        while (Objects.requireNonNull(redisTemplate.opsForSet().size(redisSalaryKey)).intValue() != 0) {
            // 程序只能在这段时间运行
            if (LocalTime.now().isBefore(LocalTime.of(3, 6)) || LocalTime.now().isAfter(LocalTime.of(6, 57))) {
                log.info("autoCalculateSalary时间限制,跳出循环");
                continue;
            }
            Integer ch999Id = redisTemplate.opsForSet().pop(redisSalaryKey);
            log.info("autoCalculateSalary 队列大小{},当前计算工号{}", redisTemplate.opsForSet().size(redisSalaryKey), ch999Id);
            //如果有数据直接进行计算
            Map<Integer, Ch999UserSalaryVo> ch999UserSalaryVoMap = data.stream().collect(Collectors.toMap(Ch999UserSalaryVo::getCh999Id, Function.identity()));
            Ch999UserSalaryVo ch999UserSalaryVo = ch999UserSalaryVoMap.get(ch999Id);
            //没有找到数据
            if (ch999UserSalaryVo == null || ch999Id == null) {
                continue;
            }
            try {
                //如果是全额利润薪等才进行计算，否则直接发送mq消息
                if (ch999UserSalaryVo.getIslirun() != null && ch999UserSalaryVo.getIslirun() == liRun) {
                    runTaskCalculate(ch999UserSalaryVo, taskCalculateVo.getStartTime(), taskCalculateVo.getEndTime(), taskCalculateVo.getSalaryMonth(), zhiWuVOMap, type);
                }
            } catch (Exception e) {
                log.error("autoCalculateSalary自动计算提成出错 ch999Id:{}", ch999Id, e);
            }
        }
        if (type != 2) {
            //调用线上的接口发送mq
            HttpUtil.post(jiujiSystemProperties.getMoa() + "/cloudapi_nc/orderservice/api/salary/task/taskCalculateSendMsg?xservicename=oa-orderservice", "");
        }
    }

    public List<Ch999UserSalaryVo> getCh999UserSalary(LocalDateTime date) {
        DateTimeFormatter fmtYmd = DateTimeFormatter.ofPattern(DateTimeUtils.DATE_FORMAT);
        //默认跑上个月的数据
        LocalDateTime firstDay = date.with(TemporalAdjusters.firstDayOfMonth());
        LocalDateTime lastDay = date.with(TemporalAdjusters.lastDayOfMonth());
        String startTimeText = firstDay.format(fmtYmd) + " 00:00:00";
        String endTimeText = lastDay.format(fmtYmd) + " 23:59:59";
        R<List<Ch999UserSalaryVo>> ch999IdListForSalary = userInfoClient.getCh999IdListForSalary(startTimeText, endTimeText);
        if (!CommonUtils.isRequestSuccess(ch999IdListForSalary)) {
            log.error("autoCalculateSalary获取用户信息失败");
            throw new CustomizeException("获取用户信息失败");
        }
        return ch999IdListForSalary.getData();
    }

    @Override
    @Async("asyncServiceExecutor")
    public void taskCalculateSendMsg(List<Integer> userIds) {
        LocalDateTime date = LocalDateTime.now().plusMonths(-1);
        List<Ch999UserSalaryVo> ch999UserSalary = getCh999UserSalary(date);
        if (!CollectionUtils.isEmpty(userIds)) {
            ch999UserSalary = ch999UserSalary.stream().filter(item -> userIds.contains(item.getCh999Id())).collect(Collectors.toList());
        }
        String salaryMonth = date.format(DateTimeFormatter.ofPattern("yyyyMM"));
        ch999UserSalary.forEach(item -> {
            log.info("taskCalculateSendMsg发送mq。ch999Id:{},salaryMonth:{}", item.getCh999Id(), salaryMonth);
            try {
                TimeUnit.MILLISECONDS.sleep(2000);
            } catch (InterruptedException e) {
                log.error("taskCalculateSendMsg发送mq出错", e);
            }
            rabbitTemplate.convertAndSend(SalaryConstant.SALARY_DLX_QUEUE, JSON.toJSONString(new SalaryMqDto("CalculateSalaryHander", new SalaryMqDto.Data(item.getCh999Id(), salaryMonth))));
        });
        redisTemplate.delete(RedisKeyConstant.AUTO_CALCULATE_SALARY_FLAG);
    }

    @Override
    public R<List<BrandRes.BrandDetailRes>> getBrandsV1() {
        return R.success(salaryDao.getBrandsV1());
    }

    @Override
    public R<Object> taskAutoCalculate() {
        boolean check = AutoCalculateConfigEnum.check(tenantId);
        if (check) {
            taskCalculate(0);
            return R.success("正在进行提成自动计算");
        }
        return R.success("该租户不需要进行提成计算提成");
    }

    @Override
    public SalaryConfigTotalLogRes trackingData(SalaryCalculateReq salaryCalculateReq) {
        return callSalary(salaryCalculateReq.setTrackingProcess(true));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void runTaskCalculate(Ch999UserSalaryVo vo, LocalDateTime firstDateTime, LocalDateTime lastDateTime, String salaryMonth, Map<Integer, ZhiWuVO> zhiWuVOMap, Integer type) {
        Integer ch999Id = vo.getCh999Id();
        SalaryCalculateReq salaryCalculateReq = new SalaryCalculateReq()
                .setCh999Id(ch999Id).setAreaId(vo.getAreaId())
                .setStartDate(firstDateTime).setEndDate(lastDateTime);
        log.info("autoCalculateSalary自动计算自定义薪酬提成 type:{},ch999Id:{},时间:{},salaryMonth:{}", type, ch999Id, firstDateTime.toLocalDate(), salaryMonth);
        //如果是九机需要获取结存后的xtenant和areaId
        Integer xtenant = null;
        if (Objects.equals("10000", tenantId.toString())) {
            SalaryBalanceInfoDto salaryBalanceInfoDto = ch999UserMapper.getXtenantOfSalary(ch999Id, salaryMonth, InnerProfileJudgeUtil.isJiuJi() || InnerProfileJudgeUtil.isJiuJiTest());
            xtenant = salaryBalanceInfoDto.getXtenant();
            salaryCalculateReq.setAreaId(salaryBalanceInfoDto.getAreaId());
        }
        SalaryConfigTotalLogRes salaryConfigTotalLogRes = calculateCommission(salaryCalculateReq, zhiWuVOMap, xtenant);
        List<SalaryConfigLogRes> salaryConfigLogRes = salaryConfigTotalLogRes.getSalaryConfigLogRes();
        if (CollectionUtils.isEmpty(salaryConfigLogRes)) {
            return;
        }

        //类型是2，每日结存
        if (Objects.equals(type, 2)) {
            customSalaryReport(vo, salaryConfigLogRes, firstDateTime.toLocalDate());
            return;
        }

        BigDecimal totalAmount = salaryConfigLogRes.stream().map(SalaryConfigLogRes::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        //大于20000放弃保存，推送信息=
        if (totalAmount.compareTo(new BigDecimal(20000)) > 0) {
            log.info("autoCalculateSalary自动计算提成大于20000放弃保存，推送信息 ch999Id:{}", ch999Id);
            messagePushCloud.pushOaMessageAsync("12975", "【" + ch999Id + "】提成计算大于20000，放弃保存，请手工核查", null);
            return;
        }

        //先删除之前的记录再插入新的记录
        List<Integer> salaryConfigLogResIds = salaryResDao.getIdsByCh999IdAndMonth(ch999Id, salaryMonth);
        if (!CollectionUtils.isEmpty(salaryConfigLogResIds)) {
            salaryResDao.removeSalaryDetails(salaryConfigLogResIds);
        }
        salaryResDao.removeByCh999IdAndMonth(ch999Id, salaryMonth);
        salaryConfigLogRes.forEach((SalaryConfigLogRes res) -> {
            Optional.of(res).filter(item -> item.getCardinalNumber() == null).ifPresent(item -> item.setCardinalNumber(BigDecimal.ZERO));
            Optional.of(res).filter(item -> org.apache.commons.lang3.StringUtils.isBlank(item.getSalaryPath())).ifPresent(item -> item.setSalaryPath(""));
            salaryResDao.saveSalaryRes(ch999Id, salaryMonth, res);
            if (!CollectionUtils.isEmpty(res.getBusinessDataResList())) {
                List<SalaryConfigLogRes.BusinessDataRes> businessDataResList = res.getBusinessDataResList();
                int splitSize = 290;
                for (List<SalaryConfigLogRes.BusinessDataRes> details : CollUtil.split(businessDataResList, splitSize)) {
                    salaryResDao.batchSaveSalaryResDetails(res.getSalaryConfigLogResId(), details);
                }
            }
        });
    }

    private void customSalaryReport(Ch999UserSalaryVo vo, List<SalaryConfigLogRes> salaryConfigLogRes, LocalDate date) {
        CustomSalaryReportReq req = new CustomSalaryReportReq();
        BigDecimal amount = salaryConfigLogRes.stream().map(SalaryConfigLogRes::getAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal cardinalNumber = salaryConfigLogRes.stream().map(SalaryConfigLogRes::getCardinalNumber).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

        List<CustomSalaryReportReq.DetailReportReq> details = new ArrayList<>();
        salaryConfigLogRes.forEach(configLogRes -> {
            List<SalaryConfigLogRes.BusinessDataRes> businessDataResList = configLogRes.getBusinessDataResList();
            businessDataResList.forEach(item -> details.add(new CustomSalaryReportReq
                    .DetailReportReq()
                    .setAmount(item.getAmount() == null ? BigDecimal.ZERO : item.getAmount())
                    .setCardinalNumber(item.getCardinalNumber())
                    .setSubId(item.getSubId())
                    .setPpid(item.getPpId())
                    .setCategoryId(configLogRes.getSalaryCategoryId())
                    .setCategory(configLogRes.getSalaryCategoryName())
                    .setSmallCategoryId(configLogRes.getSalaryChildCategoryId())
                    .setSmallCategory(configLogRes.getSalaryChildCategoryName())));

        });
        req.setDay(date)
                .setCh999Id(vo.getCh999Id())
                .setCh999Name(vo.getCh999Name())
                .setAreaId(vo.getAreaId())
                .setAmount(amount)
                .setCardinalNumber(cardinalNumber)
                .setDetails(details);
        customSalaryReportCloud.save(req);
    }

    @Override
    public R<Object> updateAreaIds() {
        String url = InnerProfileJudgeUtil.isJiuJiTest() ? "https://moa.dev.9ji.com/salary_service/api/salaryConfig/updateAreaIds" : "https://moa.9ji.com/salary_service/api/salaryConfig/updateAreaIds";
        JSONObject jsonObject = JSON.parseObject(HttpRequest.post(url)
                .header("xtenant", jiujiSystemProperties.getXtenant())
                .header("x-tenant-scope", jiujiSystemProperties.getMoa())
                .execute().body());
        String result = jsonObject.get("data").toString();
        if (org.apache.commons.lang3.StringUtils.isBlank(result)) {
            return R.success(true);
        }
        Integer data = JSON.parseObject(JSON.parseObject(result).get("data").toString(), Integer.class);
        return data != null ? R.success("更新成功，更新数据" + data + "条") : R.error("更新失败");
    }

    @Override
    public List<AfterOrderTrueProfitVo> getAfterOrderTrueProfitVoOfArea(AfterOrderTrueProfitReq req) {
        return salaryDao.getAfterOrderTrueProfitVoOfArea(req);
    }

    @Override
    public List<ReturnOrder> getRecoverDiffNewDto(List<ReturnOrder> returnOrders) {
        if (CollectionUtils.isEmpty(returnOrders)) {
            return returnOrders;
        }
        List<Integer> subIds = returnOrders.stream().map(ReturnOrder::getSubId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(subIds)) {
            return returnOrders;
        }
        List<RecoverDiffNewDto> recoverDiffNewDtoList = salaryRecoverDao.getRecoverDiffNewDto(subIds, SpringContext.isJiuJiEnvironment());
        if (CollectionUtils.isEmpty(recoverDiffNewDtoList)) {
            return returnOrders;
        }
        return returnOrders.stream().peek(returnOrder -> {
            RecoverDiffNewDto recoverDiffNewDto = recoverDiffNewDtoList.stream()
                    .filter(item -> Objects.equals(item.getSubId(), returnOrder.getSubId()) && Objects.equals(item.getMkcId(), returnOrder.getMkcId()))
                    .findFirst().orElse(null);
            Optional.ofNullable(recoverDiffNewDto).ifPresent(item -> returnOrder.setDifference(item.getDifference()));
        }).collect(Collectors.toList());
    }

    @Override
    public List<ReturnOrder> listReturnOrderSettlementProfit(List<ReturnOrder> returnOrders, Integer ch999Id, List<Integer> areaId, LocalDateTime startDate, LocalDateTime endDate) {
        if (!SpringContext.isJiuJiEnvironment()) {
            return returnOrders;
        }
        List<ReturnSettlementProfitDto> settlementProfitList = salaryRecoverDao.listReturnOrderSettlementProfit(ch999Id, areaId, startDate, endDate);
        if (CollectionUtils.isEmpty(settlementProfitList)) {
            return returnOrders;
        }
        return returnOrders.stream().peek(returnOrder -> {
            ReturnSettlementProfitDto returnSettlementProfitDto = settlementProfitList.stream()
                    .filter(item -> Objects.equals(item.getSubId(), returnOrder.getSubId()) && Objects.equals(item.getBasketId(), returnOrder.getBasketId()))
                    .findFirst().orElse(null);
            Optional.ofNullable(returnSettlementProfitDto).ifPresent(item -> returnOrder.setSettlementGrossProfit(item.getSettlementProfit()));
        }).collect(Collectors.toList());
    }

    @Override
    public R<Object> updateOrderKind() {
        List<Long> allSalaryKind = salaryResDao.getAllSalaryId();
        String url = InnerProfileJudgeUtil.isJiuJiTest() || InnerProfileJudgeUtil.isSassTest() ? "https://moa.dev.9ji.com/salary_service/api/salaryConfig/getOrderKindById" : "https://moa.9ji.com/salary_service/api/salaryConfig/getOrderKindById";
        JSONObject jsonObject = JSON.parseObject(HttpRequest.post(url)
                .body(JSON.toJSONString(allSalaryKind), "application/json")
                .execute().body());
        List<UpdateOrderKindDto> data = JSON.parseObject(jsonObject.get("data").toString(), new TypeReference<List<UpdateOrderKindDto>>() {
        });
        if (CollectionUtils.isEmpty(data)) {
            return R.success("没有数据");
        }
        CollUtil.split(data, 1000).forEach(item -> salaryResDao.updateOrderKind(item));
        return R.success("更新成功" + data.size() + "条");
    }

    /**
     * 获取销售网上支付方式，特殊处理的key，会在原key加上 MarketingOnlinePayType 中的前面的字段，例如 x-x
     */
    private List<SysConfigNameValueVO> getMarketingOnlineMethod() {
        List<SysConfigNameValueVO> resultList = new ArrayList<>();
        salaryDao.getMarketingOnlineUPayMethod().forEach(item ->
                resultList.add(new SysConfigNameValueVO().setName(item.getName()).setValue(MarketingOnlinePayTypeEnum.U_PAY.getCode() + "-" + item.getValue())));
        /* 阿里接口暂时注释
        salaryEnumsDao.getMarketingOnlineAliPayMethod().forEach(item ->
                resultList.add(new SysConfigNameValueVO().setName(item.getName()).setValue(MarketingOnlinePayType.ALI.getCode() + String.valueOf(item.getValue()))));*/
        salaryDao.getMarketingOnlineWeiXinPayMethod().forEach(item ->
                resultList.add(new SysConfigNameValueVO().setName(item.getName()).setValue(MarketingOnlinePayTypeEnum.WEI_XIN.getCode() + "-" + item.getValue())));
        salaryDao.getMarketingOnlineSwiftPassMethod().forEach(item ->
                resultList.add(new SysConfigNameValueVO().setName(item.getName()).setValue(MarketingOnlinePayTypeEnum.SWIFT_PASS.getCode() + "-" + item.getValue())));
        return resultList;
    }

    private UserReq getUserInfoByCh999Id(SalaryCalculateReq salaryCalculateReq, Integer xTenant, SalaryBalanceInfoDto salaryBalanceInfoDto) {
        UserReq userInfo = new UserReq();
        R<AreaInfo> areaInfoRes = areaInfoClient.getAreaInfoById(salaryCalculateReq.getAreaId());
        AreaInfo areaInfo = areaInfoRes.getData();
        Ch999UserBasicBO ch999User = ch999UserMapper.getCh999UserBasicBO(salaryCalculateReq.getCh999Id());
        //areaId必须从salaryCalculateReq中获取，不能直接从用户信息中获取
        userInfo.setAreaId(salaryCalculateReq.getAreaId());
        userInfo.setCh999Id(salaryCalculateReq.getCh999Id());
        userInfo.setCh999Name(ch999User.getCh999Name());
        userInfo.setDepartId(ch999User.getDepartId() == null ? "" : ch999User.getDepartId().toString());
        userInfo.setSalaryRoles(salaryBalanceInfoDto.getMainRole() == null ? (ch999User.getMainRole() == null ? "" : ch999User.getMainRole().toString()) : salaryBalanceInfoDto.getMainRole().toString());
        userInfo.setAreaKind(areaInfo.getKind1());
        userInfo.setAreaLevel(areaInfo.getLevel1());
        userInfo.setStartTime(salaryCalculateReq.getStartDate().toLocalDate().toString());
        userInfo.setEndTime(salaryCalculateReq.getEndDate().toLocalDate().toString());
        userInfo.setJob(salaryBalanceInfoDto.getMainStation() == null ? (ch999User.getMainStation() == null ? "" : ch999User.getMainStation().toString()) : salaryBalanceInfoDto.getMainStation().toString());
        userInfo.setPosition(salaryBalanceInfoDto.getZhiWu() == null ? (ch999User.getZhiwuId() == null ? "" : ch999User.getZhiwuId().toString()) : salaryBalanceInfoDto.getZhiWu().toString());
        userInfo.setSalaryUserClassify(salaryBalanceInfoDto.getIsShiXi() == null ? (ch999User.getIsShiXi() == null ? "" : ch999User.getIsShiXi().toString()) : salaryBalanceInfoDto.getIsShiXi().toString());
        userInfo.setXtenant(xTenant);
        log.info("InspireSalaryGetXtenant:{}", xTenant);
        userInfo.setMedalsMap(achievementMedalClient.medalListOfLight(userInfo.getCh999Id(), DateTimeUtils.formatDate(salaryCalculateReq.getStartDate(), DateTimeUtils.DATE_FORMAT)).getData());
        userInfo.setIsShiXi(ch999User.getIsShiXi());
        userInfo.setZhiJi(ch999User.getZhiji());
        userInfo.setSzhiJi(ch999User.getSZhiji());
        userInfo.setNumberOfPeople(ch999UserMapper.getUserCountByAreaId(areaInfo.getId()));
        userInfo.setZhuanzhendate(ch999User.getZhuanzhendate());
        if (SpringContext.isJiuJiEnvironment()) {
            Map<Integer, AreaPopReSaleDto> areaPopReSaleMap = salaryRecoverDao.getAreaPopReSale(salaryCalculateReq.getStartDate(), salaryCalculateReq.getEndDate());
            userInfo.setAreaPopReSale(areaPopReSaleMap.getOrDefault(salaryCalculateReq.getAreaId(), new AreaPopReSaleDto()).getAreaPopReSaleRate());
        }
        userInfo.setIsDaiPei(salaryCalculateReq.getIsDaiPei());
        if (salaryCalculateReq.getIsDaiPei() == null || Boolean.FALSE.equals(salaryCalculateReq.getIsDaiPei())) {
            userInfo.setInDate(ch999User.getInDate());
        } else {
            userInfo.setInDate(salaryCalculateReq.getInDate());
        }
        return userInfo;
    }

    public Map<Integer, UserCompletionRate> mapUserCompletionRate(LocalDateTime startDate, LocalDateTime endDate) {
        return salaryDao.listUserCompletionRate(startDate, endDate).stream().collect(Collectors.toMap(UserCompletionRate::getCh999Id, Function.identity(), (key1, key2) -> key1));
    }

    public Map<Integer, AreaCompletionRate> mapAreaCompletionRate(LocalDateTime startDate, LocalDateTime endDate) {
        return salaryDao.listAreaCompletionRate(startDate, endDate).stream().collect(Collectors.toMap(AreaCompletionRate::getAreaId, Function.identity(), (key1, key2) -> key1));
    }

    public Map<Integer, RecoverCheckUserDiffRate> mapRecoverCheckUserDiffRate(LocalDateTime startDate, LocalDateTime endDate) {
        return salaryDao.listRecoverCheckUserDiffRate(startDate, endDate).stream().collect(Collectors.toMap(RecoverCheckUserDiffRate::getCh999Id, Function.identity(), (key1, key2) -> key1));
    }

    private List<ReturnOrder> transformReturnOrderVType(List<RetrunOrderEx> returnOrderExList) {
        if (CollectionUtils.isEmpty(returnOrderExList)) {
            return new ArrayList<>(0);
        }
        List<ReturnOrder> returnOrderList = new ArrayList<>();
        returnOrderExList.forEach(item -> {
            ReturnOrder returnOrder = new ReturnOrder();
            BeanUtils.copyProperties(item, returnOrder);

            //咨询过回收组，如果为空就是1
            if (item.getOrderVTypeSubType() == null) {
                item.setOrderVTypeSubType(ReturnOrderVTypeEnum.OA_ORDER.getCode());
            }
            // 根据RetrunOrderEx的orderVTypeNetsub和orderVTypeSubType的值唯一确定ReturnOrder的orderVType
            switch (item.getOrderVTypeSubType()) {
                case 0:
                    if (item.getOrderVTypeNetsub().equals(0)) {
                        returnOrder.setOrderVType(ReturnOrderVTypeEnum.OA_ORDER.getCode());
                    }
                    if (item.getOrderVTypeNetsub().equals(1)) {
                        returnOrder.setOrderVType(ReturnOrderVTypeEnum.ONLINE_ORDER.getCode());
                    }
                    break;
                case 1: //定价APP
                    returnOrder.setOrderVType(ReturnOrderVTypeEnum.PRICING_APP.getCode());
                    break;
                case 2: //九机APP
                    returnOrder.setOrderVType(ReturnOrderVTypeEnum.JIUJI_APP.getCode());
                    break;
                case 5:
                    returnOrder.setOrderVType(ReturnOrderVTypeEnum.SCAN_ORDER.getCode());
                default:
                    break;
            }
            returnOrderList.add(returnOrder);
        });
        return returnOrderList;
    }


}
