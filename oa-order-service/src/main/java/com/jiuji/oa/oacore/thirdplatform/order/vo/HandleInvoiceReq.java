package com.jiuji.oa.oacore.thirdplatform.order.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class HandleInvoiceReq {

    @NotNull(message = "美团订单不能为空")
    private Long order_id;

    private String app_poi_code;

    /**
     * 需上传的发票url
     */
    @NotBlank(message = "美团订单不能为空")
    private String invoice_urls;

    /**
     * ppid
     */
    @NotNull(message = "ppid不能为空")
    private Integer ppid;

    /**
     * 是否上传红票，0-否，1-是。默认为0
     */
    @NotNull(message = "是否上传红票不能为空")
    private Integer is_red_invoice;

    /**
     * 商品映射
     */
    private List<SpuData> spuDataList;
}
