package com.jiuji.oa.oacore.thirdplatform.order.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.atlas.AtlasUtil;
import com.ch999.common.util.atlas.CoordinateUtil;
import com.ch999.common.util.utils.XtenantJudgeUtil;
import com.ch999.common.util.vo.Result;
import com.ch999.common.util.vo.atlas.Coordinate;
import com.ch999.common.util.vo.atlas.TencentMapResultVO;
import com.doudian.open.api.instantShopping_notifyDeliveryStatus.param.InstantShoppingNotifyDeliveryStatusParam;
import com.doudian.open.api.instantShopping_reportRiderLocation.data.InstantShoppingReportRiderLocationData;
import com.doudian.open.api.instantShopping_reportRiderLocation.param.InstantShoppingReportRiderLocationParam;
import com.doudian.open.api.order_orderDetail.data.ShopOrderDetail;
import com.google.common.collect.HashBasedTable;
import com.jiuji.cloud.org.service.AreaInfoCloud;
import com.jiuji.cloud.org.vo.response.AreaOpeningRes;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.oacore.apollo.ApolloKeys;
import com.jiuji.oa.oacore.cloud.WebCloud;
import com.jiuji.oa.oacore.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.config.lmstfy.LmstfyConfig;
import com.jiuji.oa.oacore.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.constant.RedisKeyConstant;
import com.jiuji.oa.oacore.common.constant.RequestAttrKeys;
import com.jiuji.oa.oacore.common.enums.OaMesTypeEnum;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.common.util.*;
import com.jiuji.oa.oacore.common.util.branchcontrol.BranchControlUtil;
import com.jiuji.oa.oacore.csharp.recover.CsharpRecoverService;
import com.jiuji.oa.oacore.goldseed.service.AreaInfoService;
import com.jiuji.oa.oacore.mapstruct.CommonStructMapper;
import com.jiuji.oa.oacore.oaorder.dao.BbsxpUsersMapper;
import com.jiuji.oa.oacore.oaorder.enums.BasketTypeEnum;
import com.jiuji.oa.oacore.oaorder.enums.SubCheckEnum;
import com.jiuji.oa.oacore.oaorder.enums.SubTypeEnum;
import com.jiuji.oa.oacore.oaorder.po.*;
import com.jiuji.oa.oacore.oaorder.service.*;
import com.jiuji.oa.oacore.oaorder.vo.ThirdOrderInfoRes;
import com.jiuji.oa.oacore.other.bo.PingzhengBO;
import com.jiuji.oa.oacore.other.bo.PingzhengResultBO;
import com.jiuji.oa.oacore.other.service.VoucherService;
import com.jiuji.oa.oacore.thirdplatform.annotation.AddLogKind;
import com.jiuji.oa.oacore.thirdplatform.annotation.AddOrderLog;
import com.jiuji.oa.oacore.thirdplatform.common.ThirdPlatformCommonConst;
import com.jiuji.oa.oacore.thirdplatform.common.enums.*;
import com.jiuji.oa.oacore.thirdplatform.common.util.DataUtils;
import com.jiuji.oa.oacore.thirdplatform.common.util.JsonUtils;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.DoudianFactory;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.MyAccessToken;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.util.OAStub;
import com.jiuji.oa.oacore.thirdplatform.doudian.service.DefaultDouDianService;
import com.jiuji.oa.oacore.thirdplatform.doudian.service.DouyinBizService;
import com.jiuji.oa.oacore.thirdplatform.mapstruct.OrderMapstruct;
import com.jiuji.oa.oacore.thirdplatform.oplog.po.MeituanJdWorkLog;
import com.jiuji.oa.oacore.thirdplatform.oplog.service.MeituanJdWorkLogService;
import com.jiuji.oa.oacore.thirdplatform.order.bo.*;
import com.jiuji.oa.oacore.thirdplatform.order.entity.JdOrder;
import com.jiuji.oa.oacore.thirdplatform.order.entity.Order;
import com.jiuji.oa.oacore.thirdplatform.order.entity.OrderItem;
import com.jiuji.oa.oacore.thirdplatform.order.mapper.OrderMapper;
import com.jiuji.oa.oacore.thirdplatform.order.service.*;
import com.jiuji.oa.oacore.thirdplatform.order.vo.*;
import com.jiuji.oa.oacore.thirdplatform.order.vo.meituan.ThirdDeliveryMeituanOrderVO;
import com.jiuji.oa.oacore.thirdplatform.productconfig.bo.RecoverMkcBO;
import com.jiuji.oa.oacore.thirdplatform.productconfig.entity.ProductConfig;
import com.jiuji.oa.oacore.thirdplatform.productconfig.mapper.ProductConfigMapper;
import com.jiuji.oa.oacore.thirdplatform.productconfig.service.ProductConfigService;
import com.jiuji.oa.oacore.thirdplatform.stock.bo.StockRes;
import com.jiuji.oa.oacore.thirdplatform.stock.service.StockService;
import com.jiuji.oa.oacore.thirdplatform.store.entity.JdStore;
import com.jiuji.oa.oacore.thirdplatform.store.entity.Store;
import com.jiuji.oa.oacore.thirdplatform.store.service.JdStoreService;
import com.jiuji.oa.oacore.thirdplatform.store.service.StoreService;
import com.jiuji.oa.oacore.thirdplatform.taobao.service.TaoBaoBizService;
import com.jiuji.oa.oacore.thirdplatform.taobao.service.TaoBaoService;
import com.jiuji.oa.oacore.thirdplatform.taobao.vo.TaobaoToken;
import com.jiuji.oa.oacore.thirdplatform.tenant.bo.TenantUserBO;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.ThirdPlatformTenantUser;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.TenantService;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.ThirdPlatformTenantUserService;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.oacore.weborder.enums.GiftClassEnum;
import com.jiuji.oa.oacore.weborder.enums.OrderSubTypeEnum;
import com.jiuji.oa.oacore.weborder.vo.ProductGiftListVO;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.sysconfig.vo.SysConfigVo;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCodeEnum;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.TraceIdUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SubCheckStatusEnum;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum;
import com.jiuji.tc.utils.enums.coupon.SubSubTypeEnum;
import com.jiuji.tc.utils.enums.xtenant.ShortXtenantEnum;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import com.jiuji.wcf.wcfclient.csharp.gen.AddOrderSub_cs.SubOrderResult;
import com.meitu.platform.lmstfy.client.LmstfyClient;
import com.meitu.platform.lmstfy.exception.LmstfyException;
import com.sankuai.meituan.shangou.open.sdk.domain.SystemParam;
import com.sankuai.meituan.shangou.open.sdk.exception.SgOpenException;
import com.sankuai.meituan.shangou.open.sdk.request.EcommerceOrderLogisticsSyncRequest;
import com.sankuai.meituan.shangou.open.sdk.request.OrderDeliveringRequest;
import com.sankuai.meituan.shangou.open.sdk.request.OrderGetOrderDetailRequest;
import com.sankuai.meituan.shangou.open.sdk.request.OrderViewStatusRequest;
import com.sankuai.meituan.shangou.open.sdk.response.SgOpenResponse;
import com.taobao.api.request.AlibabaAelophyOrderGetRequest;
import com.taobao.api.response.AlibabaAelophyOrderGetResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 库存管理接口实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements OrderService {
    private static final String INUSER = "inuser";
    private static final Integer MSG_MAX_LENGTH = 300;
    private String meiTuanAppId;
    private String meiTuanAppSecret;

    private static final Integer ERROR_CODE = 200;

    @Resource
    @Lazy
    private OrderService orderService;

    @Resource(name = "oaAsyncRabbitTempe")
    private RabbitTemplate rabbitTemplate;
    @Resource
    private MeiTuanService meiTuanService;
    @Resource
    private MeiTuanSDKService meiTuanSDKService;
    @Autowired
    private StockService stockService;
    @Resource
    private SubLogsCloud subLogsCloud;
    @Resource
    private JdStoreService jdStoreService;
    @Resource
    private StoreService storeService;
    @Autowired
    private ProductConfigService productConfigService;
    @Autowired
    private TenantService tenantService;
    @Autowired
    private OrderItemService orderItemService;
    @Autowired
    private AreaInfoService areaInfoService;

    @Autowired
    private IAreaInfoService iareaInfoService;
    @Resource
    private RestTemplate restTemplate;
    @Autowired
    private BbsxpUsersService bbsxpUsersService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private OaSysConfigService oaSysConfigService;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private ProductinfoService productinfoService;
    @Resource
    private MeituanJdWorkLogService meituanJdWorkLogService;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private VoucherService voucherService;
    @Resource
    private SubService subService;
    @Resource
    private SmsService smsService;
    @Autowired
    private UserInfoClient userInfoClient;
    @Resource
    private ProductConfigMapper productConfigMapper;
    @Resource
    private OAStub oaStub;
    @Resource
    private WebCloud webCloud;
    @Resource
    private BasketService basketService;
    @Resource
    private OrderMapstruct orderMapstruct;
    @Resource
    private ThirdPlatformTenantUserService thirdPlatformTenantUserService;

    @Resource(name = "firstLmstfyClient")
    private LmstfyClient firstLmstfyClient;

    @PostConstruct
    private void init() {
        restTemplate = new RestTemplate();
    }


    @DS(DataSourceConstants.SMALLPRO_WRITE)
    @Override
    public List<Order> getOrderList(String orderId) {
        return this.lambdaQuery().eq(Order::getOrderId, orderId).list();
    }


    @DS(DataSourceConstants.SMALLPRO_WRITE)
    @Override
    public List<Order> getOrderListBySubId(String subId) {
        return this.lambdaQuery().eq(Order::getSubId, subId).list();
    }

    @Override
    @DS(DataSourceConstants.SMALLPRO_WRITE)
    @RepeatSubmitCheck(expression = "#{packageFullName}:#{methodSignName}:#{param['order_id']}")
    public R createMeituanOrder(Map<String, Object> param) {
        log.warn("美团订单创建回调参数：{}", JSON.toJSONString(param));
        OrderReq req = JSON.parseObject(JSON.toJSONString(param), OrderReq.class);
        log.warn("美团订单创建回调参数实体转换：{}", JSON.toJSONString(req));
        log.warn("美团闪购-开始创建订单：{appId:{},sign:{},timestamp:{}}", req.getAppId(), req.getSig(), req.getTimestamp());
        //校验appid
        List<Tenant> list = tenantService.list(new LambdaQueryWrapper<Tenant>()
                .select(Tenant::getTenantCode)
                .eq(Tenant::getIsEnable, Boolean.TRUE));
        if (CollectionUtils.isEmpty(list)) {
            log.error("美团闪购创建订单校验：商户信息配置不存在或未启用");
            MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.ORDER.getMessage(), "美团闪购创建订单校验：商户信息配置不存在或未启用",
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.MT.getCode(),list.stream().map(Tenant::getTenantCode).findFirst().orElse("无法获取"));
            meituanJdWorkLogService.save(structure);
            return LambdaBuild.create(new R(OrderRes.RESULT_NG)).set(R::setMsg,"商户信息配置不存在或未启用").build();
        }
        List<String> tenantCodes = list.stream().map(Tenant::getTenantCode).collect(Collectors.toList());
        if (!tenantCodes.contains(req.getAppId())) {
            log.warn("美团闪购创建订单校验：app_id不匹配【{}】", req.getAppId());
            if (StringUtils.isNotEmpty(req.getAppId())){
                MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.ORDER.getMessage(), MessageFormat.format("美团闪购创建订单校验：app_id不匹配【{0}】", req.getAppId()) ,
                        "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.MT.getCode(),list.stream().map(Tenant::getTenantCode).findFirst().orElse("无法获取"));
                meituanJdWorkLogService.save(structure);
            }
            return LambdaBuild.create(new R(OrderRes.RESULT_NG)).set(R::setMsg,"商户与app_id不匹配").build();
        }
        //时间戳校验
        long currentMinutes = System.currentTimeMillis() / ThirdPlatformCommonConst.MIN_MILL;
        //为了测试方便暂时把时间放宽
        if (Math.abs(currentMinutes - req.getTimestamp()) > ThirdPlatformCommonConst.OFFSET) {
            log.warn("美团闪购创建订单校验：时间戳校验不通过【{}】", req.getTimestamp());
            MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.ORDER.getMessage(), MessageFormat.format("美团闪购创建订单校验：时间戳校验不通过【{0}】", req.getTimestamp()) ,
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.MT.getCode(),list.stream().map(Tenant::getTenantCode).findFirst().orElse("无法获取"));
            meituanJdWorkLogService.save(structure);
            return LambdaBuild.create(new R(OrderRes.RESULT_NG)).set(R::setMsg,"时间戳校验不通过").build();
        }
        Long orderId = req.getOrderId();
        //校验订单是否已经创建
        if (CommonUtil.isNullOrZero(orderId)) {
            log.warn("美团闪购创建订单校验：参数错误：order_id为空");
            MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.ORDER.getMessage(), "美团闪购创建订单校验：参数错误：order_id为空" ,
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.MT.getCode(),list.stream().map(Tenant::getTenantCode).findFirst().orElse("无法获取"));
            meituanJdWorkLogService.save(structure);
            return LambdaBuild.create(new R(OrderRes.RESULT_NG)).set(R::setMsg,"order_id不能为空").build();
        }
        if (getOrderAutoId(ThirdPlatformCommonConst.THIRD_PLAT_MT, req.getOrderId(), qw -> qw.isNotNull(Order::getSubId)) != null) {
            log.warn("美团闪购创建订单校验：订单已存在，不能重复创建【{}】", orderId);
            MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.ORDER.getMessage(), MessageFormat.format("美团闪购创建订单校验：订单已存在，不能重复创建【{0,number,#}】", orderId) ,
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.MT.getCode(),list.stream().map(Tenant::getTenantCode).findFirst().orElse("无法获取"));
            meituanJdWorkLogService.save(structure);
            return LambdaBuild.create(new R(OrderRes.RESULT_OK)).set(R::setMsg,"订单已存在，不能重复创建").build();
        }

        // 处理商品数量重新赋值
        processQuantityReassignment(req);
        //参数转换
        Order order = buildOrderParam(ThirdPlatformCommonConst.THIRD_PLAT_MT, req);
        //参数校验
        if (null == order) {
            return LambdaBuild.create(new R(OrderRes.RESULT_NG)).set(R::setMsg,SpringContextUtil.getRequestErrorMsg()
                    .stream().collect(Collectors.joining(StringPool.SLASH))).build();
        }
        //开启写库
        AtomicReference<Boolean> isSubmitOaOrder = new AtomicReference<>(Boolean.FALSE);
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () ->{
            //没有创建美团订单才插入
            Integer orderAutoId = getOrderAutoId(ThirdPlatformCommonConst.THIRD_PLAT_MT, req.getOrderId(), null);
            if(orderAutoId == null){
                save(order);
            }else{
                order.setId(orderAutoId);
            }
            List<OrderItem> itemList = buildOrderItemParam(order, req);
            boolean isItemNotEmpty = CollectionUtils.isNotEmpty(itemList);
            if(isItemNotEmpty){
                isSubmitOaOrder.set(Boolean.TRUE);
            }else{
                log.warn("美团闪购订单【{}】匹配不到本地商品详情信息,不进行建单", orderId);
                SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "美团闪购订单【{}】匹配不到本地商品详情信息,不进行建单", orderId);
            }
            //详情没有创建才进行插入
            if (isItemNotEmpty && orderItemService.lambdaQuery().eq(OrderItem::getOrderId,Convert.toStr(orderId)).count()<=0) {
                orderItemService.saveBatch(itemList);
            }
        }).commit();
        LambdaBuild<R> rlb = LambdaBuild.create(new R(OrderRes.RESULT_OK));
        if(Boolean.TRUE.equals(isSubmitOaOrder.get())){
            //提交oa订单走单独的事务,避免重复创建订单
            OrderExtendVO orderExtendVO = new OrderExtendVO();
            orderExtendVO.setPickType(req.getPickType());
            boolean sooR = submitOaOrder(order.getId(),order.getEstimateArrivalTime(),orderExtendVO);
            if(!sooR){
                rlb.set(R::setData,OrderRes.RESULT_NG);
            }
            Optional.ofNullable(order).map(o -> getById(o.getId()))
                    .ifPresent(o -> order.setSubId(o.getSubId()));
            if(Boolean.TRUE.equals(order.getGovernmentSubsidyFlag())){
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("act", "MT_GovernmentSubsidy");
                jsonObject.put("data", order.getSubId());
                rabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_TOPIC_OAASYNC, jsonObject.toString());
            }
        }
        if(CollUtil.isNotEmpty(SpringContextUtil.getRequestErrorMsg())){
            rlb.set(R::setMsg,SpringContextUtil.getRequestErrorMsg().stream().collect(Collectors.joining(StringPool.SLASH)));
        }
        if (XtenantEnum.isJiujiXtenant()) {
            BigDecimal fee = BigDecimal.ZERO;
            if (Objects.nonNull(req.getPoiReceiveDetailYuan())
                    && Objects.nonNull(req.getPoiReceiveDetailYuan().getProductShareFeeChargeByPoi())) {
                Double productShareFeeChargeByPoi = req.getPoiReceiveDetailYuan().getProductShareFeeChargeByPoi();
                fee = fee.add(Convert.toBigDecimal(productShareFeeChargeByPoi));
            }
            if (Objects.nonNull(req.getPoiReceiveDetailYuan())
                    && Objects.nonNull(req.getPoiReceiveDetailYuan().getReconciliationExtras())
                    && Objects.nonNull(req.getPoiReceiveDetailYuan().getReconciliationExtras().getPerformanceServiceFee())) {
                Double performanceServiceFee = req.getPoiReceiveDetailYuan().getReconciliationExtras().getPerformanceServiceFee();
                fee = fee.add(Convert.toBigDecimal(performanceServiceFee));
            }

            LossOrderMessagePushParam data = new LossOrderMessagePushParam();
            data.setFee(fee);
            data.setSubId(order.getSubId());
            data.setPlatform(order.getPlatCode());
            log.info(StrUtil.format("发送亏损订单消息, 平台：{}, 单号: {}, 服务费: {}", data.getPlatform(), data.getSubId(), data.getFee()));
            rabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_LOSS_ORDER_MESSAGE_PUSH, JSON.toJSONString(data));
        }
        return rlb.build();
    }

    @Override
    public Page<OrderVO> listByPage(OrderSearchBO search) {
        Page<OrderVO> page = new Page<>(search.getCurrent(), search.getSize());
        page.setDesc("t.order_time");
        //默认查询新品订单
        if (Boolean.TRUE.equals(CommonUtil.isNullOrZero(search.getType()))){
            search.setType(NumberConstant.ZERO);
        }
        List<OrderVO> list;
        if (Objects.equals(search.getType(),NumberConstant.ONE)){
            list = baseMapper.recoverOrderList(search, page);
        }else {
            list = baseMapper.orderList(search, page);
        }

        rebuildList(list);
        if (CollectionUtils.isNotEmpty(list)) {
            page.setRecords(list);
        }
        return page;
    }

    private void rebuildList(List<OrderVO> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            List<Integer> orderIdList = list.stream().map(OrderVO::getId).collect(Collectors.toList());
            List<OrderItem> orderItems = CommonUtils.bigDataInQuery(NumberConstant.ONE_THOUSAND, orderIdList, ids ->
                    orderItemService.listOrderItemByOutIdList(ids));
            Map<Integer,List<OrderItem>> OrderItemMap = orderItems.stream().collect(Collectors.groupingBy(OrderItem::getOutjdid,LinkedHashMap::new,Collectors.toList()));
            list.forEach(obj -> {
                //查询订单明细
                List<OrderItem> itemList = OrderItemMap.get(obj.getId());
                if (itemList == null || itemList.isEmpty()){
                    obj.setSkuCount(0);
                    obj.setSubCheckName(EnumUtil.getMessageByCode(SubCheckEnum.class, obj.getSubCheck()));
                    return;
                }
                StringBuffer buffer = new StringBuffer();
                Integer qty = itemList.stream().mapToInt(e -> e.getSkuCount()).sum();
                if (CollectionUtils.isNotEmpty(itemList)) {
                    itemList.forEach(item -> {
                        String productStr = String.format("[%s]%s %sx%d", item.getPpriceid(), item.getProductName(),
                                null == item.getProductColor() ? "" : item.getProductColor(), item.getSkuCount());
                        buffer.append(productStr);
                        buffer.append("\r\n");
                    });
                }
                obj.setProductName(StringUtils.removeEnd(buffer.toString(), "\r\n"));
                obj.setSkuCount(qty);
                obj.setSubCheckName(EnumUtil.getMessageByCode(SubCheckEnum.class, obj.getSubCheck()));
                obj.setGovernmentSubsidyFlagName(Objects.equals(1, obj.getGovernmentSubsidyFlag()) ? "是" : "否");
            });
        }
    }

    /**
     * Excel导出
     * @param req
     * @param response
     * @return
     */
    @Override
    public R exportExcel(OrderSearchBO req, HttpServletResponse response) {
        //默认查询新品订单
        if (Boolean.TRUE.equals(CommonUtil.isNullOrZero(req.getType()))){
            req.setType(NumberConstant.ZERO);
        }
        String fileName = "订单列表数据导出-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMddHHmm")) + ".xlsx";
        if (ExcelWriterUtil.outCacheFile("mtExportExcel",req, Duration.ofMinutes(NumberConstant.TEN),fileName,response)){
            return R.success("导出成功");
        }

        List<OrderVO> list = baseMapper.orderList(req, null);
        rebuildList(list);
        ArrayList<String> titles = new ArrayList<>(Arrays.asList("平台下单时间", "商户号", "平台订单号", "平台门店", "本地门店", "\t商品\t", "数量",
                "实付金额", "平台承担", "本地订单号", "本地订单状态",
                "订单备注", "错误消息", "取消原因"));
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<List<Object>> dataList = new ArrayList<>();
        list.forEach(obj -> {
            List<Object> cells = new ArrayList<>();
            if (null == obj.getOrderTime()) {
                cells.add("");
            } else {
                cells.add(sdf.format(obj.getOrderTime()));
            }
            cells.add(String.format("%s(%s)", obj.getTenantCode(), obj.getTenantCode()));
            cells.add(obj.getOrderId());
            cells.add(obj.getStoreCode());
            cells.add(obj.getAreaCode());
            cells.add(obj.getProductName());
            cells.add(obj.getSkuCount());
            cells.add(obj.getPayableMoney());
            cells.add(obj.getPlatMoney());
            cells.add(obj.getSubId());
            cells.add(obj.getSubCheckName());
            cells.add(obj.getBuyerRemark());
            cells.add(obj.getSubMessage());
            cells.add(obj.getCancelReason());
            dataList.add(cells);
        });

        try {
            ExcelUtils.export(response, dataList, titles, null,
                    fileName, 1);
        } catch (IOException e) {
            return R.error("导出失败" + e.getMessage());
        }
        return R.success("导出成功");
    }

    /**
     * 查询用户参与x员任选外键活动次数
     * @param userId
     * @param actId
     * @return
     */
    @Override
    public R<Integer> queryUserXSelectYCount(Integer userId, Integer actId) {
        return R.success(baseMapper.queryUserXSelectYCount(userId,actId));
    }

    /**
     * 查询用户参与x元任选y件 活动次数
     * @param userId    用户id
     * @param actIds     活动ld列表
     * @return
     */
    @Override
    public Map<String, Integer> queryUserXSelectYCountMap(Integer userId, String actIds) {
        if (StringUtils.isBlank(actIds)) {
            return Collections.emptyMap();
        }
        List<UserXSelectYActInfo> userXSelectYActInfoList = baseMapper.queryUserXSelectYCountList(userId,
                CommonUtils.covertIdStr(actIds).stream().map(String::valueOf).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(userXSelectYActInfoList)) {
            return userXSelectYActInfoList.stream().collect(Collectors.toMap(UserXSelectYActInfo::getActId, UserXSelectYActInfo::getCountNum, (x, y) -> x));
        }
        return Collections.emptyMap();
    }

    /**
     * 取消美团订单
     * @param params params
     * @return
     */
    @Override
    @DS(DataSourceConstants.SMALLPRO_WRITE)
    public boolean cancelMeiTuanOrder(Map<String, Object> params) {
        log.warn("请求参数：{}", JSON.toJSONString(params));
        CancelOrder req = JSON.parseObject(JSON.toJSONString(params), CancelOrder.class);
        log.warn("美团取消订单: {}", req.getOrderId());
        if (Objects.isNull(req) ){
            SpringContextUtil.addRequestErrorMsg("美团取消订单参数解析失败【{}】");
            return false;
        }
        if (StrUtil.isBlank(req.getReason()) || StrUtil.isBlank(req.getDealOpType())){
            SpringContextUtil.addRequestErrorMsg("美团取消订单的原因或人员类型不能为空");
            return false;
        }
        //获取当前订单
        Order order = baseMapper.selectOne(new LambdaQueryWrapper<Order>().eq(Order::getOrderId, req.getOrderId()));
        if (Objects.isNull(order)){
            SpringContextUtil.addRequestErrorMsg("美团取消订单获取失败【{}】",req.getOrderId());
            return false;
        }
        //获取商户信息
        Tenant tenant = tenantService.getOneTenantBy(order.getTenantCode(), ThirdPlatformCommonConst.THIRD_PLAT_MT);
        if (Objects.isNull(tenant) ){
            log.error("美团商户信息获取失败");
            SpringContextUtil.addRequestErrorMsg("美团商户信息获取失败");
            return false;
        }
        if (!Objects.equals(tenant.getTenantCode(),req.getAppId())) {
            log.error("美团闪购取消订单：app_id不匹配【{}】,【{}】", req.getAppId(),req.getOrderId());
            SpringContextUtil.addRequestErrorMsg("美团闪购取消订单：app_id不匹配【{}】,【{}】", req.getAppId(),req.getOrderId());
            return false;
        }
        long currentMinutes = System.currentTimeMillis() / ThirdPlatformCommonConst.MIN_MILL;
        if (Math.abs(currentMinutes - req.getTimestamp()) > ThirdPlatformCommonConst.OFFSET) {
            log.error("美团闪购取消订单：时间戳校验不通过【{}】", req.getTimestamp());
            SpringContextUtil.addRequestErrorMsg("美团闪购取消订单：时间戳校验不通过");
            return false;
        }
        boolean isCancelSuccess = false;
        try {
            //更新订单状态的数据为取消, 避免oa取消失败,但是标识没更新的问题
            Order upOrder = new Order();
            upOrder.setCancelCheck(NumberConstant.ONE);
            upOrder.setId(order.getId());
            upOrder.setCancelReason(getCancelReason(order.getOrderId()));
            baseMapper.updateById(upOrder);
            //提交订单到OA
            R<String> conf = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST);
            if (null == conf || StringUtils.isBlank(conf.getData())) {
                log.error("美团闪购取消订单：获取inwcf前缀失败【{}】", conf);
                SpringContextUtil.addRequestErrorMsg("美团闪购取消订单：获取inwcf前缀失败【{}】", JSON.toJSONString(conf));
                return false;
            }
            String prefix = conf.getData();
            String url = prefix + ThirdPlatformCommonConst.OA_API_SUB_REFUND;
            //构建订单参数，调用oa订单退款接口
            Map<String, Object> map = new HashMap<>(NumberConstant.FIVE);
            map.put("subId", order.getSubId());
            map.put("comment", MessageFormat.format("订单取消操作人：{0}，订单取消原因：{1}",MeiTuanResultCode.valueOfByCode(req.getDealOpType()).getMessage(),req.getReason()));
            log.warn("调用订单退订接口,链接{},参数{}", url,map);
            ResponseEntity<R> subRefundEntityR = restTemplate.postForEntity(url, map, R.class);
            String resStr = JSON.toJSONString(subRefundEntityR);
            log.warn("调用订单退订接口,返回参数{}", resStr);
            if (Objects.requireNonNull(subRefundEntityR.getBody()).getCode() == 0) {
                isCancelSuccess = true;
                return true;
            }
            SpringContextUtil.addRequestErrorMsg("美团闪购取消订单：美团返回错误【{}】", resStr);
            return true;
        } finally {
            //发送消息通知给门店的管理层和销售
            toMessageNotification(order);
        }

    }

    /**
     * 从美团获取取消原因
     * @param orderId
     * @return
     */
    @Override
    public String getCancelReason(String orderId)   {
        SgOpenResponse sgOpenResponse =new SgOpenResponse();
        try {
             sgOpenResponse = meiTuanSDKService.cancelReason(orderId);
        } catch (Exception e) {
            smsService.sendOaMsgTo9Ji(AddLogKind.ORDER_REFUND_DETAIL+"操作异常,订单号："+ orderId, "13495",OaMesTypeEnum.YCTZ.getCode().toString());
            log.error(AddLogKind.ORDER_REFUND_DETAIL+"操作异常",e);
        }
        String requestResult = Optional.ofNullable(sgOpenResponse.getRequestResult()).orElseThrow(()->new CustomizeException("从美团获取取消原因为空"));
        JSONObject result = JSON.parseObject(requestResult);
        String data = Optional.ofNullable(result.get("data")).orElse("").toString();
        String applyReason = "";
        if(data.equals(StockRes.RESULT_NG)){
            Object message = JSON.parseObject(Optional.ofNullable(result.get("error")).orElse("").toString()).get("msg");
            smsService.sendOaMsgTo9Ji(AddLogKind.ORDER_REFUND_DETAIL+":"+message+"美团订单号:"+orderId, "13495",OaMesTypeEnum.YCTZ.getCode().toString());
        } else {
            try {
                List<CancelReasonBO> cancelReasonBOS = JSONObject.parseArray(data,CancelReasonBO.class);
                applyReason = cancelReasonBOS.get(0).getApplyReason();
            }catch (Exception e){
                String message=AddLogKind.ORDER_REFUND_DETAIL+"解析applyReason异常，订单号为:"+orderId;
                log.error(message,e);
                smsService.sendOaMsgTo9Ji(message, "13495",OaMesTypeEnum.YCTZ.getCode().toString());
            }
        }
        log.warn(AddLogKind.ORDER_REFUND_DETAIL+"获取结果为："+ applyReason);
        return applyReason;
    }

    @Override
    public R<Object> toMessageNotification(Order order){
        return toMessageNotification(order, Collections.emptySet(), null);
    }

    @Override
    public R<Object> toMessageNotification(Order order, Set<Integer> ch999Ids, String msgSuffix){
        //查询当前订单
        Long subId = order.getSubId();
        if (CommonUtil.isNullOrZero(subId)){
            return R.error("没有单号不进行通知");
        }
        List<String> sellers;
        Integer subCheck;
        String trader;
        Integer areaid;
        //默认为空函数啥也不做
        Consumer<String> writeSubLogFun = msg -> {};
        boolean isDouYin = ThirdPlatformCommonConst.THIRD_PLAT_DY.equals(order.getPlatCode());
        boolean isTaobao = ThirdPlatformCommonConst.THIRD_PLAT_TB.equals(order.getPlatCode());
        if(Order.OrderTypeEnum.lP_ORDER.getCode().equals(order.getType())){
            RecoverMarketinfo sub = MultipleTransaction.query(DataSourceConstants.SMALLPRO_WRITE,()->
                    SpringUtil.getBean(RecoverMarketinfoService.class).lambdaQuery().eq(RecoverMarketinfo::getSubId, subId)
                    .select(RecoverMarketinfo::getSubCheck,RecoverMarketinfo::getTrader,RecoverMarketinfo::getAreaid).one());
            subCheck = sub.getSubCheck();
            trader = sub.getTrader();
            areaid = sub.getAreaid();
            sellers = MultipleTransaction.query(DataSourceConstants.SMALLPRO_WRITE,()-> SpringUtil.getBean(RecoverMarketsubinfoService.class)
                    .lambdaQuery().eq(RecoverMarketsubinfo::getSubId, subId)
                    .select(RecoverMarketsubinfo::getSeller).list().stream().map(RecoverMarketsubinfo::getSeller).collect(Collectors.toList()));
            if(isDouYin){
                writeSubLogFun = msg -> subLogsCloud.addLpSubLog(LambdaBuild.create(new SubLogsNewReq()).set(SubLogsNewReq::setComment, msg)
                            .set(SubLogsNewReq::setSubId, Convert.toInt(subId)).set(SubLogsNewReq::setType, 1)
                            .set(SubLogsNewReq::setShowType, false).set(SubLogsNewReq::setInUser, "系统")
                            .set(SubLogsNewReq::setDTime, LocalDateTime.now()).build());
            }
        }else{
            Sub sub = MultipleTransaction.query(DataSourceConstants.SMALLPRO_WRITE,()-> subService.getSub(Math.toIntExact(subId)));
            subCheck = sub.getSubCheck();
            trader = sub.getTrader();
            areaid = sub.getAreaid();
            List<Basket> baskets = basketService.selectBasketList(subId);
            sellers = baskets.stream().map(Basket::getSeller).collect(Collectors.toList());

            if(isDouYin || isTaobao){
                writeSubLogFun = msg -> subLogsCloud.addSubLog(LambdaBuild.create(new SubLogsNewReq()).set(SubLogsNewReq::setComment, msg)
                        .set(SubLogsNewReq::setSubId, Convert.toInt(subId)).set(SubLogsNewReq::setType, 1)
                        .set(SubLogsNewReq::setShowType, false).set(SubLogsNewReq::setInUser, "系统")
                        .set(SubLogsNewReq::setDTime, LocalDateTime.now()).build());
            }
        }

        //当订单状态为退订,不进行推送
        if (Arrays.asList(SubCheckStatusEnum.RETURN.getCode()).contains(subCheck)){
            return R.success("当订单状态为退订,不进行推送");
        }
        //发送消息给门店人员
        if(StrUtil.isBlank(msgSuffix)){
            if (XtenantJudgeUtil.isJiujiMore()) {
                msgSuffix = StrUtil.format("订单异常，顾客已在{}平台原路径退款，请及时追回商品处理OA订单，如有疑问咨询小九助手。", PlatfromEnum.getMessageByName(order.getPlatCode()));
                Tenant tenant = tenantService.getOneTenantBy(order.getTenantCode(), ThirdPlatformCommonConst.THIRD_PLAT_DY);
                if (Objects.nonNull(tenant) && AppTypeEnum.MALL_HOURS.getCode().equals(tenant.getAppType())) {
                    msgSuffix = StrUtil.format("抖音小时达平台用户已取消订单，请及时追回相关商品！");
                }
            } else {
                msgSuffix = StrUtil.format("{}平台用户已取消订单，请及时追回相关商品！", PlatfromEnum.getMessageByName(order.getPlatCode()));
            }
        }
        String msg = StrUtil.format("订单号：<a href='%s'>%s</a>，{}", msgSuffix);

        //获取店长和主管,副店长
        List<Integer> personneList = baseMapper.getPersonneList(areaid);
        Set<Integer> userIdList = new HashSet<>(personneList);
        if (StrUtil.isNotBlank(trader)){
            //推送交易人员
            Ch999UserVo ch999UserVo = Optional.ofNullable(userInfoClient.getCh999UserByUserName(trader)).filter(R::isSuccess).map(R::getData).orElse(new Ch999UserVo());
            if (CommonUtil.isNotNullZero(ch999UserVo.getCh999Id())){
                userIdList.add(ch999UserVo.getCh999Id());
            }
        }

        //推送销售人员
        for (String seller : sellers) {
            if(!"网络".equals(seller)){
                Ch999UserVo temp = Optional.ofNullable(userInfoClient.getCh999UserByUserName(seller)).filter(R::isSuccess).map(R::getData).orElse(new Ch999UserVo());
                userIdList.add(temp.getCh999Id());
            }
        }
        Integer orderType = order.getType();
        if(userIdList.isEmpty() && CollUtil.isNotEmpty(ch999Ids)){
            //追加通知人
            userIdList.addAll(ch999Ids);
        }
        // 执行写入日志
        writeSubLogFun.accept(msgSuffix);
        return sendOrderNotice(Convert.toStr(subId), orderType, msg, userIdList);
    }

    @Override
    public R<Object> sendOrderNotice(String subId, Integer orderType, String msg, Set<Integer> userIdList) {
        String cacheKey = StrUtil.format(RedisKeyConstant.MEITUAN_CANCEL_ORDER_NOTICE, subId);
        if(stringRedisTemplate.hasKey(cacheKey)){
            // 已经通知过了,不重复通知
            return R.success(null);
        }
        if(CollUtil.isNotEmpty(userIdList)){
            String link = "";
            if(Stream.of(0,1).anyMatch(type -> ObjectUtil.defaultIfNull(orderType, 0).equals(type))){
                String host = sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL).getData();
                link = host + Optional.ofNullable(orderType).filter(type -> Objects.equals(type, 1))
                        .map(type -> "/mstockout/editorder?SubID=").orElse("/order/editorder?SubID=") + subId;
            }
            //通知消息类型改成订单通知
            smsService.sendOaMsg(String.format(msg, link, subId),link, userIdList.stream().map(String::valueOf)
                    .collect(Collectors.joining(",")), String.valueOf(OaMesTypeEnum.DDTZ.getCode()));
            stringRedisTemplate.opsForValue().set(cacheKey,Boolean.TRUE.toString(), Duration.ofMinutes(9));
        }else{
            return R.error("用户id为空不进行推送");
        }
        return R.success(null);
    }

    /**
     * 构建第三方订单明细参数（入库用）
     *
     * @param order
     * @param req
     * @return
     */
    @Override
    public List<OrderItem> buildOrderItemParam(Order order, OrderReq req) {
        List<OrderItem> itemList = new ArrayList<>();
        List<OrderProduct> detailList = req.getDetail();
        if (CollectionUtils.isEmpty(detailList)) {
            return itemList;
        }
        for (OrderProduct orderProduct : detailList) {
            String appFoodCode = orderProduct.getAppFoodCode();
            if (StringUtils.isNotEmpty(appFoodCode)){
                orderProduct.setAppFoodCode(StringUtils.trim(appFoodCode));
            }
            // 存储一份商品金额
            orderProduct.setCostPrice(orderProduct.getPrice());
            if (CollUtil.isNotEmpty(req.getSkuBenefitDetail()) && XtenantEnum.isJiujiXtenant()) {
                req.getSkuBenefitDetail().forEach(skuBenefitDetailBO -> {

                    if (Objects.equals(skuBenefitDetailBO.getSkuId(), orderProduct.getSkuId())) {
                        try {
                            double totalMtCharge = Optional.ofNullable(skuBenefitDetailBO.getTotalMtCharge()).orElse(0D);
                            double totalActivityPrice = Optional.ofNullable(skuBenefitDetailBO.getTotalActivityPrice()).orElse(0D);
                            int count = Optional.ofNullable(skuBenefitDetailBO.getCount()).orElse(0);

                            // 确保count不为零
                            if (count > 0) {
                                // 客户实付单价 = (商品sku参加活动优惠的美团承担成本总金额+ 商品sku参活动优惠后的总价（包含商品包装费）) / 商品总量
                                // (totalMtCharge + totalActivityPrice) / count
                                double actualPaidUnitPrice = NumberUtil.div(NumberUtil.add(totalMtCharge, totalActivityPrice), count);
                                orderProduct.setPrice(actualPaidUnitPrice);
                            } else {
                                log.warn("美团订单{}，计算实际支付单价时，count为零, skuId:{}", order.getOrderId(), orderProduct.getSkuId());
                            }
                        } catch (Exception e) {
                            log.error("美团订单{}，计算实际支付单价异常, skuId:{}", order.getOrderId(), orderProduct.getSkuId(), e);
                        }
                    }
                });
            }
        }
        //kv 用户应付金额
        BigDecimal kv = BigDecimal.valueOf(order.getPayableMoney()).add(ObjectUtil.defaultIfNull(order.getGovernmentSubsidyMoney(), BigDecimal.ZERO));
        //kp 平台承担金额
        BigDecimal kp = BigDecimal.valueOf(order.getPlatMoney());
        //商品数量*单价之和
        BigDecimal pdPriceTotal = detailList.stream().map(e -> BigDecimal.valueOf(e.getPrice()).multiply(BigDecimal.valueOf(e.getQuantity())))
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal kvPvTotal = kp.add(kv);
        //运费独立收银
        if((ThirdPlatformCommonConst.THIRD_PLAT_DY.equals(order.getPlatCode())
                && req.getTenant() != null && AppTypeEnum.MALL_HOURS.getCode().equals(req.getTenant().getAppType()))
                || (ThirdPlatformCommonConst.THIRD_PLAT_MT.equals(order.getPlatCode()) && XtenantEnum.isJiujiXtenant())
                || ThirdPlatformCommonConst.THIRD_PLAT_TB.equals(order.getPlatCode())){
            BigDecimal freightMoney = NumberUtil.toBigDecimal(order.getFreightMoney());
            kvPvTotal = kvPvTotal.subtract(freightMoney);
            log.warn("运费独立字段 分摊扣减运费{}, 实际商品分摊费用: {}", freightMoney, kvPvTotal);
        }
        splitOrder(order, itemList, detailList.stream().sorted(Comparator.comparing(d->ObjectUtil.defaultIfNull(d.getPrice(),0D)))
                .collect(Collectors.toList()), kv, kp, pdPriceTotal, kvPvTotal);
        return itemList;
    }

    /***
     *
     * @param order
     * @param itemList
     * @param detailList
     * @param kv 用户应付金额
     * @param kp 平台承担金额
     * @param pdPriceTotal 所有商品总金额
     * @param kvPvTotal 用户应付金额+平台承担金额
     */
    protected void splitOrder(Order order, List<OrderItem> itemList, List<OrderProduct> detailList, BigDecimal kv, BigDecimal kp, BigDecimal pdPriceTotal, BigDecimal kvPvTotal) {
        BigDecimal splitAfterPriceTotal = BigDecimal.ZERO;
        //只有一个商品的item
        OrderItem oneItem = null;
        Map<OrderProduct, List<ProductConfig>> localProductListMap = CollUtil.newHashMap(detailList.size());
        //批量获取本地商品映射
        detailList.forEach(pd -> {
            List<ProductConfig> localProductList;
            if (CommonUtil.isNullOrZero(pd.getDouDianType())){
                //美团一直为0,所以只走这个逻辑
                localProductList = productConfigService.getOneProductConfigBySkuId(order.getPlatCode(), order.getTenantCode(),
                        pd.getAppFoodCode(), pd.getSkuId());
            }else {
                //抖音良品的逻辑
                localProductList = productConfigService.getOneProductConfigBySkuId(order.getPlatCode(), order.getTenantCode(),
                        pd.getAppFoodCode(), DecideUtil.iif(NumberUtil.isNumber(pd.getSkuId()), "L" + pd.getSkuId(), pd.getSkuId()));
            }
            if(CollUtil.isNotEmpty(localProductList)){
                localProductListMap.put(pd, localProductList);
            }
        });
        for (int i = 0, len = detailList.size(); i< len; i++) {
            OrderProduct pd = detailList.get(i);
            //根据类型判断是 查询本地的ppid 还是本地的mkcId
            List<ProductConfig> localProductList = localProductListMap.get(pd);

            if (CollectionUtils.isNotEmpty(localProductList)) {
                localProductList.sort(Comparator.comparing(e -> ObjectUtil.defaultIfNull(e.getPriceSplit(),0D)));
                BigDecimal price = BigDecimal.valueOf(pd.getPrice());
                BigDecimal quantity = BigDecimal.valueOf(pd.getQuantity());
                // 当前商品的金额占比
                BigDecimal pricePercentage = pdPriceTotal.compareTo(BigDecimal.ZERO) == 0 ?  BigDecimal.ZERO :
                        price.multiply(quantity).divide(pdPriceTotal,NumberConstant.FOUR,RoundingMode.HALF_UP);
                // 当前商品分配用户应付金额
                BigDecimal pdKv = kv.multiply(pricePercentage);
                // 当前商品分配平台承担金额
                BigDecimal pdKp = kp.multiply(pricePercentage);
                BigDecimal splitTotal = localProductList.stream().map(e -> BigDecimal.valueOf(e.getPriceSplit())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                for (int j = 0,llen = localProductList.size(); j < llen; j++) {
                    ProductConfig localProduct = localProductList.get(j);
                    OrderItem item = getOrderItem(order, pd, localProduct);
                    itemList.add(item);
                    if(quantity.compareTo(BigDecimal.ONE) == 0){
                        oneItem = item;
                    }
                    // 用户应付金额+平台承担金额 == 所有商品总金额 或者数量为0 不需要分摊,直接设置
                    if(kvPvTotal.compareTo(pdPriceTotal) == 0 || splitTotal.compareTo(BigDecimal.ZERO) == 0
                            || quantity.compareTo(BigDecimal.ZERO) == 0){
                        item.setPriceSplit(pd.getPrice());
                        item.setTradePrice(pd.getPrice());
                    }else{
                        //计算价格分摊
                        BigDecimal priceSplitDecimal = pdKv.add(pdKp).multiply(BigDecimal.valueOf(localProduct.getPriceSplit()))
                                .divide(splitTotal, NumberConstant.FOUR, RoundingMode.HALF_UP)
                                .divide(quantity, NumberConstant.TWO, RoundingMode.DOWN);
                        double priceSplit = priceSplitDecimal.doubleValue();
                        item.setPriceSplit(priceSplit);
                        item.setTradePrice(priceSplit);
                        //分摊价格求和
                        splitAfterPriceTotal = splitAfterPriceTotal.add(priceSplitDecimal.multiply(quantity));
                        //最后一个
                        boolean isLast = i == len - 1 && j == llen-1;
                        //修正分摊价格
                        repairSplitPrice(kvPvTotal, splitAfterPriceTotal, oneItem, isLast,balancePrice->{
                            //大于1,提取一个商品单独出来进行填补,不能直接添加单价,只能单独提取一个商品,设置单价
                            BigDecimal newQuantity = quantity.subtract(BigDecimal.ONE);
                            item.setSkuCount(newQuantity.intValue());
                            OrderItem reItem = item.copy();
                            BigDecimal rePriceSplitDecimal = priceSplitDecimal.add(balancePrice);
                            double rePriceSplit = rePriceSplitDecimal.doubleValue();
                            reItem.setPriceSplit(rePriceSplit);
                            reItem.setTradePrice(rePriceSplit);
                            reItem.setSkuCount(BigDecimal.ONE.intValue());
                            itemList.add(reItem);
                        });
                    }
                }
            }else{
                log.warn("sku[{}]spu[{}]本地商品没有配置",pd.getSkuId(),pd.getAppFoodCode());
                SpringContextUtil.addRequestErrorMsg("sku[{}]spu[{}]本地商品没有配置",pd.getSkuId(),pd.getAppFoodCode());
            }
        }
    }

    protected static void repairSplitPrice(BigDecimal kvPvTotal, BigDecimal splitAfterPriceTotal, OrderItem oneItem, boolean isLast, Consumer<BigDecimal> addOneItem) {
        if (isLast && splitAfterPriceTotal.compareTo(kvPvTotal) != 0) {
            BigDecimal balancePrice = kvPvTotal.subtract(splitAfterPriceTotal);
            if (oneItem == null) {
                addOneItem.accept(balancePrice);
            } else {
                // 存在单商品,直接添加单价
                double rePriceSplit = BigDecimal.valueOf(oneItem.getPriceSplit()).add(balancePrice).doubleValue();
                oneItem.setPriceSplit(rePriceSplit);
                oneItem.setTradePrice(rePriceSplit);
            }

        }
    }

    protected static OrderItem getOrderItem(Order order, OrderProduct pd, ProductConfig localProduct) {
        OrderItem item = new OrderItem();
        item.setOutjdid(order.getId());
        item.setOrderId(order.getOrderId());
        item.setSkuId(pd.getSkuId());
        item.setProductCode(pd.getAppFoodCode());
        item.setPpriceid(localProduct.getPpriceid());
        if (localProduct.getType() == 1){
            item.setType(localProduct.getType());
            item.setMkcId(localProduct.getMkcId());
        }
        item.setSkuCount(pd.getQuantity());
        item.setPrice(pd.getPrice());
        item.setCostPrice(pd.getCostPrice());
        item.setType(localProduct.getType());
        item.setBizSubOrderId(pd.getBizSubOrderId());
        item.setOutSubOrderId(pd.getOutSubOrderId());
        return item;
    }

    /**
     * 构建第三方订单参数（入库用）
     *
     * @param platCode
     * @param req
     * @return
     */
    private Order buildOrderParam(String platCode, OrderReq req) {
        String storeCode = req.getAppPoiCode();
        //查询门店
        Store store = storeService.getOneStoreByStoreCode(platCode, storeCode);
        if (null == store) {
            log.error("门店({})不存在或未启用", storeCode);
            SpringContextUtil.addRequestErrorMsg("门店({})不存在或未启用", storeCode);
            return null;
        }
        Order order = new Order();
        order.setPlatCode(platCode);
        order.setOrderId(String.valueOf(req.getOrderId()));
        order.setStoreCode(storeCode);
        order.setAreaId(store.getAreaId());
        order.setTenantCode(store.getTenantCode());
        order.setBuyerAddress(req.getRecipientAddress());
        order.setBuyerCity(req.getCityId());
        order.setCardCode(Optional.ofNullable(req.getUserMemberInfo()).orElse(new UserMemberInfo()).getCardCode());
        order.setBuyerMobile(req.getRecipientPhone());
        order.setBuyerName(req.getRecipientName());
        order.setEarlyArrival(ObjectUtil.defaultIfNull(req.getDeliveryTimeLeft(), 0L) == 0);
        if(ObjectUtil.defaultIfNull(req.getDeliveryTimeLeft(), 0L)>0){
            order.setEarliestReceiptTime(LocalDateTime.ofInstant(Instant.ofEpochSecond(req.getDeliveryTimeLeft()), ZoneId.systemDefault()));
        }
        if(ObjectUtil.defaultIfNull(req.getDeliveryTimeRight(), 0L)>0){
            order.setLatestReceiptTime(LocalDateTime.ofInstant(Instant.ofEpochSecond(req.getDeliveryTimeRight()), ZoneId.systemDefault()));
        }
        long ctime = req.getCtime();
        long utime = req.getUtime();
        long estimateArrivalTime = req.getEstimateArrivalTime();
        if (ctime > 0L) {
            Date orderTime = new Date(ctime * ThirdPlatformCommonConst.MIN_MILL);
            order.setOrderTime(orderTime);
        }
        if (utime > 0L) {
            Date tradeTime = new Date(utime * ThirdPlatformCommonConst.MIN_MILL);
            order.setTradeTime(tradeTime);
        }
        if (estimateArrivalTime > 0L) {
            Date estimateArrival = new Date(estimateArrivalTime * ThirdPlatformCommonConst.MIN_MILL);
            order.setEstimateArrivalTime(estimateArrival);
        }else {
            Date estimateArrival = new Date(ctime * ThirdPlatformCommonConst.MIN_MILL + ThirdPlatformCommonConst.HOUR);
            order.setEstimateArrivalTime(estimateArrival);
        }
        if (StringUtils.isNotBlank(req.getLongitude()) && StringUtils.isNotBlank(req.getLatitude())) {
            //高德坐标系转wgs84
            Coordinate coordinate = CoordinateUtil.gcj2wgs(new Coordinate(req.getLongitude() + "," + req.getLatitude()));
            order.setBuyerPosition(coordinate.toString());
        }
        order.setPickType(req.getPickType());
        order.setGenerateManualSubFlag(0);
        Double total = req.getTotal();
        //订单实际运费 变更：
        Double orderFreightMoney = req.getShippingFee();
        //如为商家自配，获取用户实付配送费金额记录到订单运费上。其他方式无需获取用户配送费用。
        if (CollUtil.isNotEmpty(req.getExtras()) && XtenantEnum.isJiujiXtenant()) {
            Double reduceFee = req.getExtras().stream()
                    .filter(et -> Objects.nonNull(et) && Objects.equals(et.getType(), 25))
                    .map(ExtrasBO::getReduceFee)
                    .filter(Objects::nonNull) // 确保ReduceFee不为null
                    .reduce(NumberUtil::add)
                    .orElse(0D);

            Double shippingFee = Optional.ofNullable(req.getShippingFee()).orElse(0D);
            orderFreightMoney = NumberUtil.sub(shippingFee, reduceFee);

            // 确保orderFreightMoney不为负数
            if (orderFreightMoney < 0) {
                log.warn(StrUtil.format("订单：{}, 客户实付为负数：{}，自动修正为配送费：{}", req.getOrderId(), orderFreightMoney, shippingFee));
                orderFreightMoney = shippingFee;
            }

            log.info(StrUtil.format("订单：{}, 配送费: {}, 美团减免配送费: {}, 客户实付: {}", req.getOrderId(), shippingFee, reduceFee, orderFreightMoney));
        }

        //客户实付部分=客户实付金额（total）
        Double userPayMoney = total;
        //平台补贴即为平台补贴之和（poi_receive_detail_yuan下actOrderChargeByMt的money求和）
        BigDecimal platDiscountMoney = req.getPoiReceiveDetailYuan().getActOrderChargeByMt().stream()
                .map(e -> BigDecimal.valueOf(e.getMoney())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //商家承担金额
        BigDecimal tenantDiscountMoney = req.getPoiReceiveDetailYuan().getActOrderChargeByPoi().stream()
                .map(e -> BigDecimal.valueOf(e.getMoney())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        //订单商品销售总金额
        BigDecimal orderTotalMoney = BigDecimal.valueOf(req.getOriginalPrice());
        //订单优惠金额(商家优惠+平台优惠)
        BigDecimal orderDiscountMoney = platDiscountMoney.add(tenantDiscountMoney);
        //订单货款总金额 [订单货款金额（订单总金额-商家优惠金额）]
        BigDecimal orderGoodsMoney = orderTotalMoney.subtract(tenantDiscountMoney);
        //订单原总价
        order.setTotalMoney(orderTotalMoney.doubleValue());
        //订单优惠金额(商家优惠+平台优惠)
        order.setDiscountMoney(orderDiscountMoney.doubleValue());
        //客户实付部分=客户实付金额（total）
        order.setPayableMoney(userPayMoney);
        order.setFreightMoney(orderFreightMoney);
        order.setGoodMoney(orderGoodsMoney.doubleValue());
        order.setPlatMoney(platDiscountMoney.doubleValue());
        order.setVenderMoney(tenantDiscountMoney.doubleValue());
        //根据租户展示不同的文案
        AtomicReference<String> remark = new AtomicReference<>("");
        //根据门店盘点是否是九机的租户
        AreaInfo areaInfoById = areaInfoService.getAreaInfoById(order.getAreaId());
        if (areaInfoById.getXtenant()<NumberConstant.ONE_THOUSAND
                || CommonUtils.toShotWebXtenant(areaInfoById.getXtenant()) == ShortXtenantEnum.ZLF.getCode()) {
            long deliveryTime = req.getDeliveryTime();
            BranchControlUtil.isTureOrFalse(deliveryTime!=0).trueOrFalseHandle(()->{
                //判断订单是否为到店自取（控制文案不同的选择）
                Integer pickType = Optional.ofNullable(req.getPickType()).orElse(PickTypeEnum.ZERO.getCode());
                String str;
                if(pickType.equals(PickTypeEnum.ONE.getCode())){
                    str="【美团订单，顾客将在%s到店取货，如商品无货，请在顾客到店前联系附近门店调拨送到。客户号码：%s】#%s ，%s，客户备注：%s ，备用号码：%s";
                } else {
                    str="【美团预定订单，请注意顾客要求在%s送达，请合理安排配送，客户号码：%s】#%s ，%s，客户备注：%s ，备用号码：%s";
                }
                Date date = new Date(deliveryTime * 1000);
                DateFormat format = new SimpleDateFormat("MM月dd日 HH:mm");
                remark.set(String.format(str,format.format(date), req.getRecipientPhone(), req.getOrderId(),
                        order.getBuyerAddress(), req.getCaution(), req.getBackupRecipientPhone().toString()));
            },()-> remark.set(String.format("客户备注：%s，【美团订单，需一小时送达，麻烦及时安排配送，客户号码：%s】#%s @NPS，%s@NPE，备用号码：%s",req.getCaution(), req.getRecipientPhone(), req.getOrderId(),
                    order.getBuyerAddress(), req.getBackupRecipientPhone().toString())));
        }else {
            remark.set(String.format("【MT】#%s ，实付：%s，平台补贴：%s，%s，客户备注：%s ", req.getOrderId(), userPayMoney, platDiscountMoney,
                    order.getBuyerAddress(), req.getCaution()));
        }
        order.setBuyerRemark(remark.get());
        order.setGovernmentSubsidyFlag(Boolean.FALSE);
        if(CollUtil.contains(req.getOrderTagList(), ThirdPlatformCommonConst.ORDER_TAG_YUNAN_GOVERNMENT_SUBSIDY)){
            //国补订单
            order.setGovernmentSubsidyFlag(Boolean.TRUE);
            order.setGovernmentSubsidyMoney(BigDecimal.ZERO);
            Optional.ofNullable(req.getPoiReceiveDetailYuan())
                    .map(ReceiveDetailYuan::getGovernmentCharge)
                    .map(GovernmentCharge::getGovAmount)
                    .ifPresent(order::setGovernmentSubsidyMoney);
            //从客户实付减掉国补金额
            order.setPayableMoney(BigDecimal.valueOf(order.getPayableMoney()).subtract(order.getGovernmentSubsidyMoney())
                    .doubleValue());
        }
        //美团订单加密手机号
        order.setEncryptPostTel(ReUtil.getGroup1(ThirdPlatformCommonConst.MEITUAN_ENCRYPT_MOBILE_REGEX, req.getCaution()));
        return order;
    }

    /**
     * 根据订单号校验订单是否已入库
     *
     * @param platCode
     * @param orderId
     * @return
     */
    @Override
    public Integer getOrderAutoId(String platCode, Long orderId, Consumer<LambdaQueryChainWrapper<Order>> wrapperCallback) {
        LambdaQueryChainWrapper<Order> lqw = lambdaQuery().select(Order::getId).eq(Order::getPlatCode, platCode).eq(Order::getOrderId, Convert.toStr(orderId));
        if(wrapperCallback != null){
            wrapperCallback.accept(lqw);
        }

        return lqw.orderByDesc(Order::getId).list().stream().findFirst().map(Order::getId).orElse(null);
    }

    /**
     * 提交订单到OA
     *
     * @param id
     * @return
     */
    @Override
    @DS(DataSourceConstants.SMALLPRO_WRITE)
    public boolean submitOaOrder(Integer id, Date estimateArrivalTime,OrderExtendVO orderExtendVO) {
        Order order = getById(id);
        if (null == order || null != order.getSubId()) {
            log.error("订单id={}不存在或已经提交到OA",id);
            SpringContextUtil.addRequestErrorMsg("订单id={}不存在或已经提交到OA",id);
            return false;
        }
        //商户校验
        Store store = storeService.getOneStoreByStoreCode(order.getPlatCode(), order.getStoreCode());
        boolean isDouYin = Objects.equals(order.getPlatCode(), ThirdPlatformCommonConst.THIRD_PLAT_DY);
        boolean isTaobao = Objects.equals(order.getPlatCode(), PlatfromEnum.TB.name());
        if (null == store) {
            if (isDouYin){
                meituanJdWorkLogService.saveByLog(StrUtil.format("提交订单失败,订单号：{}",id), StrUtil.format("【{}】提交订单到OA：匹配门店【{}】失败", order.getPlatCode(), order.getStoreCode()),
                        "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            }
            log.error("【{}】提交订单到OA：匹配门店【{}】失败", order.getPlatCode(), order.getStoreCode());
            SpringContextUtil.addRequestErrorMsg("【{}】提交订单到OA：匹配门店【{}】失败", order.getPlatCode(), order.getStoreCode());
            return false;
        }
        Tenant tenant = tenantService.getOneTenantBy(store.getTenantCode(), store.getPlatCode());
        if (null == tenant) {
            if (isDouYin){
                meituanJdWorkLogService.saveByLog(StrUtil.format("提交订单失败,订单号：{}",id), StrUtil.format("【{}】提交订单到OA：匹配商户【{}】失败", order.getPlatCode(), store.getTenantCode()),
                        "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            }
            log.error("【{}】提交订单到OA：匹配商户【{}】失败", order.getPlatCode(), store.getTenantCode());
            SpringContextUtil.addRequestErrorMsg("【{}】提交订单到OA：匹配商户【{}】失败", order.getPlatCode(), store.getTenantCode());
            return false;
        }
        //获取userid
        String platKemu = StringUtils.EMPTY;
        String venderKemu = StringUtils.EMPTY;
        Integer tenantUserId = tenant.getUserId();
        Optional<ThirdPlatformTenantUser> tenantUserOpt;
        if (XtenantEnum.isJiujiXtenant()){
            Integer xtenant = Optional.ofNullable(areaInfoService.getAreaInfoById(order.getAreaId())).map(AreaInfo::getXtenant).orElse(0);
            ThirdPlatformTenantUser byTenant = thirdPlatformTenantUserService.getByTenant(TenantUserBO.builder()
                    .tenantId(tenant.getId()).platCode(tenant.getPlatCode()).xtenant(xtenant).build());
            tenantUserOpt = Optional.ofNullable(byTenant);
            Integer userIdByTenant = Objects.nonNull(byTenant) ? byTenant.getUserId() : null;
             platKemu = Objects.nonNull(byTenant) ? byTenant.getPlatKemu() : null;
             venderKemu = Objects.nonNull(byTenant) ? byTenant.getVenderKemu() : null;
            if (null != userIdByTenant && userIdByTenant != 0) {
                tenantUserId = userIdByTenant;
            }
        }else{
            tenantUserOpt = Optional.empty();
        }
        String cardCode = order.getCardCode();
        Integer userId = StringUtils.isNumeric(cardCode)?Integer.valueOf(cardCode):tenantUserId;
        //九机判断如果该用户在九机已经进行了账号的注销那就取tenantUserId
        try {
            if(XtenantEnum.isJiujiXtenant() && StringUtils.isNumeric(cardCode)){
                Integer memberUserId = Convert.toInt(cardCode);
                List<Integer> delUserIds = SpringUtil.getBean(BbsxpUsersMapper.class).selectCancellationMember(memberUserId);
                if(CollectionUtils.isNotEmpty(delUserIds)){
                    userId = tenantUserId;
                }
            }
        }catch (Exception e){
            RRExceptionHandler.logError("用户在九机已经进行了账号的注销那就取tenantUserId异常", order, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }

        //获取用户信息，主要是获取到手机号
        BbsxpUsers bbsxpUsers =
                bbsxpUsersService.getInfoByUserId(new LambdaUpdateWrapper<BbsxpUsers>().eq(BbsxpUsers::getId, userId));
        if (null == bbsxpUsers || StringUtils.isEmpty(bbsxpUsers.getMobile())) {
            if (isDouYin){
                meituanJdWorkLogService.saveByLog(StrUtil.format("提交订单失败,订单号：{}",id), StrUtil.format("用户信息异常【用户id{}】", userId),
                        "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            }
            log.error("用户信息异常【用户id{}】", userId);
            SpringContextUtil.addRequestErrorMsg("用户信息异常【用户id{}】", userId);
            return false;
        }
        String userMobile = bbsxpUsers.getMobile();

        //校验本地门店
        AtomicReference<AreaInfo> localArea = new AtomicReference<>(areaInfoService.getAreaInfoById(store.getAreaId()));
        if (null == localArea.get()) {
            if (isDouYin){
                meituanJdWorkLogService.saveByLog(StrUtil.format("提交订单失败,订单号：{}",id), StrUtil.format("【{}】提交订单到OA：匹配OA门店【{}】失败", order.getPlatCode(), store.getAreaId()),
                        "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            }
            log.error("【{}】提交订单到OA：匹配OA门店【{}】失败", order.getPlatCode(), store.getAreaId());
            SpringContextUtil.addRequestErrorMsg("【{}】提交订单到OA：匹配OA门店【{}】失败", order.getPlatCode(), store.getAreaId());
            return false;
        }
        //校验本地商品
        List<OrderItem> itemList = orderItemService.listOrderItemByOutIdV2(order.getId(), order.getPlatCode());
        if (CollectionUtils.isEmpty(itemList)) {
            if (isDouYin) {
                meituanJdWorkLogService.saveByLog(StrUtil.format("提交订单失败,订单号：{}", id), StrUtil.format("【{}-{}】订单下单商品无法匹配OA商品配置", order.getPlatCode(), order.getId()),
                        "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            }
            log.error("【{}-{}】订单下单商品无法匹配OA商品配置", order.getPlatCode(), order.getId());
            SpringContextUtil.addRequestErrorMsg("【{}-{}】订单下单商品无法匹配OA商品配置", order.getPlatCode(), order.getId());
            return false;
        }
        List<Integer> areaIdList = new LinkedList<>();

        Integer pickType = Optional.ofNullable(orderExtendVO.getPickType()).orElse(PickTypeEnum.ZERO.getCode());
        //根据门店盘点是否是九机的租户
        AreaInfo areaInfoById = areaInfoService.getAreaInfoById(order.getAreaId());
        //如果是九机  校验本地商品时，会对门店库存进行查询 查询无库存后会对是否配对关联门店进行查询
        if (areaInfoById.getXtenant() < NumberConstant.ONE_THOUSAND
                && !isDouYin
                && !isTaobao
                && pickType.equals(PickTypeEnum.ZERO.getCode())) {
            //查询是否有关联门店
            Store oneStoreByStoreCode = storeService.getOneStoreByStoreCode(ThirdPlatformCommonConst.THIRD_PLAT_MT, order.getStoreCode());
            List<String> areaIdListStrings = CommonUtil.toStrList(oneStoreByStoreCode.getAssociatedStores());
            List<AreaInfo> areaInfos = iareaInfoService.listAreaInfoByAreas(areaIdListStrings);
            areaIdList.add(0, order.getAreaId());
            computeAreaIdList(areaIdList, areaIdListStrings, areaInfos);

            //拿到所有的门店或者是关联门店的库存数据
            List<Integer> orderPpid = itemList.stream().map(OrderItem::getPpriceid).collect(Collectors.toList());
            List<ProductKcBO> realInventory = baseMapper.getRealInventory(areaIdList, orderPpid);
//            //判断商品数量是否有多个
//            List<Integer> ppids = realInventory.stream().map(ProductKcBO::getPpriceid).distinct().collect(Collectors.toList());
//            //建单优先创建再多个商品都有库存的门店
//            //根据门店进行分组
//            Map<Integer, List<ProductKcBO>> collect1 = realInventory.stream().filter(re -> CommonUtil.isNotNullZero(re.getPpriceid())).collect(Collectors.groupingBy(ProductKcBO::getAreaId));
//            List<ProductKcBO> productKcBOS = new ArrayList<>();
//            for (Map.Entry<Integer, List<ProductKcBO>> integerListEntry : collect1.entrySet()) {
//                //存在多个商品且门店的商品数量相同且库存数量大于0
//                if (ppids.size() > NumberConstant.ONE && Objects.equals((int) integerListEntry.getValue().stream().map(ProductKcBO::getPpriceid).count(), ppids.size())
//                        && integerListEntry.getValue().stream().anyMatch(re -> CommonUtil.isNotNullZero(re.getLeftCount()))) {
//                    for (ProductKcBO productKcBO : integerListEntry.getValue()) {
//                        ProductKcBO pro = new ProductKcBO();
//                        pro.setAreaId(integerListEntry.getKey());
//                        pro.setPpriceid(productKcBO.getPpriceid());
//                        pro.setLeftCount(productKcBO.getLeftCount());
//                        productKcBOS.add(pro);
//                    }
//                }
//            }
//            //存在商品均无库存
//            if (CollUtil.isEmpty(productKcBOS)){
//                productKcBOS = realInventory;
//            }
//            //取出第一个有库存的门店 并且结束循环
//            AtomicBoolean flag = new AtomicBoolean(false);
//            for (Integer areaId : areaIdList) {
//                productKcBOS.forEach(re -> {
//                    if (Objects.equals(re.getAreaId(), areaId) && CommonUtil.isNotNullZero(re.getLeftCount())) {
//                        order.setAreaId(re.getAreaId());
//                        localArea.set(areaInfoService.getAreaInfoById(re.getAreaId()));
//                        flag.set(true);
//                    }
//                });
//                if (flag.get()) {
//                    break;
//                }
//            }

            Integer orderAreaId = this.computeOrderAreaId(realInventory, areaIdList, itemList);
            if (!oneStoreByStoreCode.getAssociatedStoresFlag() || Objects.isNull(orderAreaId)) {
                orderAreaId = order.getAreaId();
            }
            order.setAreaId(orderAreaId);
            localArea.set(areaInfoService.getAreaInfoById(orderAreaId));
        }
        //订单类型--美团闪购
        int subType = 19;
        if (isDouYin) {
            //订单类型--抖音订单
            subType = 22;
        } else if (isTaobao) {
            subType = SubTypeEnum.TAO_BAO.getCode();
        }
        //构建订单对象
        Map<String, Object> oaOrder;

        //重新计算行政区划
        String areaCodeByLocation = this.computeAreaCodeByLocation(order.getBuyerAddress());
        if (StringUtils.isNotEmpty(areaCodeByLocation) && !"null".equals(areaCodeByLocation)) {
            order.setBuyerCity(areaCodeByLocation);
        }

        Integer logisticsId;
        boolean isMeiTuanOrders = false;
        //美团订单做特殊处理
        if (Objects.equals(order.getPlatCode(), ThirdPlatformCommonConst.THIRD_PLAT_MT)) {
            isMeiTuanOrders = true;
            //OA自动建单后，如果订单为 城市代理、加盟、快送  OA订单配送方式默认为【到店自取】
            SelectMeiTuanOrderDetailBO selectMeiTuanOrderDetailBO = new SelectMeiTuanOrderDetailBO();
            selectMeiTuanOrderDetailBO.setOrderId(Optional.ofNullable(order.getOrderId()).orElse(Integer.MAX_VALUE+""));
            selectMeiTuanOrderDetailBO.setIsMtLogistics(1);
            ShowMeiTuanOrderDetailBO orderDetail = meiTuanService.getOrderDetail(selectMeiTuanOrderDetailBO);
            logisticsId = orderDetail.getLogisticsId();
            if(StrUtil.isNotBlank(orderDetail.getDaySeqNum())){
                order.setBuyerRemark(StrUtil.format("#{}号订单, {}", orderDetail.getDaySeqNum(), order.getBuyerRemark()));
            }
        } else {
            logisticsId=Integer.MAX_VALUE;
        }

        boolean isDouYinMallHours = Objects.equals(ThirdPlatformCommonConst.THIRD_PLAT_DY,tenant.getPlatCode()) && AppTypeEnum.MALL_HOURS.getCode().equals(tenant.getAppType());
        if (areaInfoById.getXtenant() < NumberConstant.ONE_THOUSAND
                || CommonUtils.toShotWebXtenant(areaInfoById.getXtenant()) == ShortXtenantEnum.ZLF.getCode()) {
            //当号码不为空 且 小于15位时
            if (!isDouYinMallHours && StrUtil.isNotEmpty(order.getBuyerMobile()) && order.getBuyerMobile().length() < NumberConstant.FIFTEEN) {
                userMobile = order.getBuyerMobile();
            }

            List<Integer> list = LogisticsIdMeiTuanEnum.getSelfToShop();
            //抖音小时达
            if (isDouYinMallHours) {
                //订单类型--抖音小时达订单
                subType = 32;
                //平台配送
                //到店自取订单
                if (!Objects.equals(DouDianDeliveryTypeEnum.SELF_DELIVERY.getCode(), store.getDeliveryType())) {
                    pickType = 1;
                }
                //抖音邮寄配送
                if (Objects.equals(DouDianDeliveryTypeEnum.POSTAL_DELIVERY.getCode(), store.getDeliveryType())) {
                    pickType = 2;
                }
            }else if(ObjectUtil.defaultIfNull(store.getDeliveryType(), 0) >0
                    && !Objects.equals(DouDianDeliveryTypeEnum.SELF_DELIVERY.getCode(), store.getDeliveryType())){
                //其他平台订单, 如果配置了门店属性, 以门店属性为主, 到店自取订单
                pickType = 1;
            }



            //抖音小时达订单配送方式是否为九机快送
            boolean isDouYinMallHoursDelivery = isDouYinMallHours && !Objects.equals(2, pickType);
            if (isTaobao) {
                isDouYinMallHours = true;
            }
            if(list.contains(logisticsId) || PickTypeEnum.ONE.getCode().equals(pickType)){
                //九机的参数构建
                oaOrder = buildOaOrderParamToJiuji(order.getAreaId(),userId, order.getBuyerName(), userMobile, ThirdPlatformCommonConst.ORDER_OP_USER_INTERNET,ThirdPlatformCommonConst.DELIVERY_TYPE_SELF
                     , ThirdPlatformCommonConst.ORDER_PAY_TYPE_ONLINE, subType, order.getBuyerRemark(),
                        order.getOrderTime(), order.getBuyerCity(), order.getBuyerAddress(), estimateArrivalTime,orderExtendVO);
            } else {
                //九机的参数构建
                oaOrder = buildOaOrderParamToJiuji(order.getAreaId(), userId, order.getBuyerName(), userMobile, ThirdPlatformCommonConst.ORDER_OP_USER_INTERNET,
                        DecideUtil.iif(subType == 19 || isDouYinMallHoursDelivery || isTaobao,
                                ThirdPlatformCommonConst.DELIVERY_TYPE_EXPRESS, ThirdPlatformCommonConst.DELIVERY_TYPE_EXPRESS_TRANSPORT),
                        ThirdPlatformCommonConst.ORDER_PAY_TYPE_ONLINE, subType, order.getBuyerRemark(),
                        order.getOrderTime(), order.getBuyerCity(), order.getBuyerAddress(), estimateArrivalTime,orderExtendVO);
                //获取出库门店
                if(isDouYin && StrUtil.isNotBlank(store.getAssociatedStores()) && Boolean.TRUE.equals(store.getAssociatedStoresFlag())){
                    List<String> kcAreas = StrUtil.splitTrim(store.getAssociatedStores(), StringPool.COMMA);
                    iareaInfoService.listAreaInfoByAreas(kcAreas).stream().findFirst().ifPresent(kcAreaInfo -> oaOrder.put("kcAreaid", kcAreaInfo.getId()));
                }
            }
            if(isDouYinMallHours || (isMeiTuanOrders && XtenantEnum.isJiujiXtenant())){
                //运费拆出来独立字段
                oaOrder.put("feeM_", NumberUtil.toBigDecimal(order.getFreightMoney()));
            }
        } else {
            //租户不做改变
            oaOrder = buildOaOrderParam(order.getAreaId(), userId, order.getBuyerName(), userMobile, ThirdPlatformCommonConst.ORDER_OP_USER_INTERNET,
                    ThirdPlatformCommonConst.DELIVERY_TYPE_SELF, ThirdPlatformCommonConst.ORDER_PAY_TYPE_ONLINE, subType, order.getBuyerRemark(),
                    order.getOrderTime(), order.getBuyerCity(), order.getBuyerAddress(), estimateArrivalTime);
        }
        //添加日志
        List<SubLogsNewReq> logs = new ArrayList<>();
        //如果备注信息超过长度,备注到进程中
        if(StrUtil.length(order.getBuyerRemark()) > NumberConstant.FIVE_HUNDRED){
            SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
            String comment = StrUtil.format("备注超长,原备注信息:  {}", order.getBuyerRemark());
            subLogsNewReq.setComment(comment);
            logs.add(subLogsNewReq);
        }
        //构建购物车对象
        List<Map<String, Object>> baskets = buildOaBasketsParam(itemList, order);
        //构建商品赠品对象
        //1.判断是否设置过赠品ppid
        if (StringUtils.isNotEmpty(tenant.getBasketTypeGift()) && !isDouYin) {
            //取出配置的ppid
            List<Integer> ppidList = CommonUtil.toIntList(tenant.getBasketTypeGift());
            //2.判断赠品库存是否充足
            //通过ppid和areaId查询当前门店的库存量
            List<ProductKcBO> kcCount = orderItemService.getKcCount(ppidList, order.getAreaId());
            //取出库存小于一的值
            List<ProductKcBO> collect = kcCount.stream().filter(kc -> kc.getLeftCount() < NumberConstant.ONE).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)){
                //取两个list的差集
                Collection<ProductKcBO> disjunction = CollUtil.disjunction(kcCount, collect);
                kcCount.clear();
                kcCount.addAll(disjunction);
            }
            //判断赠品库存是否为空
            if (CollectionUtils.isNotEmpty(kcCount)) {
                //3.构建赠品对象 （构建购物车对象）

                //查询主商品ppid
                Integer mainPpid = itemList.stream().sorted(Comparator.comparing(OrderItem::getTradePrice).reversed())
                        .map(OrderItem::getPpriceid).findFirst().orElse(null);
                List<Integer> ppids = kcCount.stream().map(ProductKcBO::getPpriceid).collect(Collectors.toList());
                List<Map<String, Object>> maps = buildOaBasketTypeGift(ppids, mainPpid);
                //4.塞入购物车对象
                baskets.addAll(maps);
            }
        }
        //主站配置的赠品赠送
        boolean MtThirdPlatformFlag = Objects.equals(order.getPlatCode(), ThirdPlatformCommonConst.THIRD_PLAT_MT);
        List<Integer> ppids = itemList.stream().map(OrderItem::getPpriceid).collect(Collectors.toList());

        if (MtThirdPlatformFlag) {
            List<Integer> ppidList = itemList.stream()
                    .filter((OrderItem orderItem) -> Objects.nonNull(orderItem.getGiftOff()) && orderItem.getGiftOff())
                    .map(OrderItem::getPpriceid).collect(Collectors.toList());
            Map<Integer, Integer> mainPpidToAmountMap = itemList.stream()
                    .collect(Collectors.toMap(OrderItem::getPpriceid, OrderItem::getSkuCount, (v1, v2) -> v1));
            //1.查询主站配置的赠品
            Map<Integer, Integer> giftPpidToAmountMap = new HashMap<>();
            Map<Integer, List<Integer>> giftPpidToMainPpidListMap = new HashMap<>();
            HashBasedTable<Integer, Integer, Integer> mainAndGiftPpidToAmountMap = HashBasedTable.create();
            ppidList.forEach((Integer mainPpid) -> {

                Integer mainPpidAmount = mainPpidToAmountMap.get(mainPpid);
                R<List<ProductGiftListVO>> giftPpidList = webCloud.getGiftByPPid(mainPpid);
                List<ProductGiftListVO> data = giftPpidList.getData();
                for (ProductGiftListVO x : data) {
                    Integer giftPpid = x.getId();
                    Integer giftClass = x.getGiftClass();
                    Integer giftPpidAmount = x.getAmount() * mainPpidAmount;
                    if (!GiftClassEnum.NORMAL.getCode().equals(giftClass)) {
                        continue;
                    }
                    LocalDateTime effectStartTime = x.getEffectStartTime();
                    LocalDateTime effectEndTime = x.getEffectEndTime();
                    boolean effectFlag = Objects.isNull(effectStartTime)
                            || Objects.isNull(effectEndTime)
                            || (LocalDateTime.now().isAfter(effectStartTime)
                            && LocalDateTime.now().isBefore(effectEndTime));
                    if (!effectFlag) {
                        continue;
                    }
                    String areaCityId = String.valueOf(areaInfoById.getCityId());
                    String cityIds = x.getCityIds();
                    if (StringUtils.isNotEmpty(cityIds)) {
                        if ("0".equals(cityIds)) {
                            Integer oldGiftPpidAmount = Objects.isNull(giftPpidToAmountMap.get(giftPpid)) ?
                                    0 : giftPpidToAmountMap.get(giftPpid);
                            giftPpidToAmountMap.put(giftPpid, oldGiftPpidAmount + giftPpidAmount);
                            mainAndGiftPpidToAmountMap.put(mainPpid, giftPpid, giftPpidAmount);
                            List<Integer> mainPpidList = giftPpidToMainPpidListMap.get(giftPpid);
                            if (Objects.isNull(mainPpidList)) {
                                mainPpidList = new ArrayList<>();
                                giftPpidToMainPpidListMap.put(giftPpid, mainPpidList);
                            }
                            mainPpidList.add(mainPpid);
                        } else {
                            String[] cityIdList = cityIds.split(StringPool.COMMA);
                            for (String cityid : cityIdList) {
                                if (areaCityId.contains(cityid)) {
                                    Integer oldGiftPpidAmount = Objects.isNull(giftPpidToAmountMap.get(giftPpid)) ?
                                            0 : giftPpidToAmountMap.get(giftPpid);
                                    giftPpidToAmountMap.put(giftPpid, oldGiftPpidAmount + giftPpidAmount);
                                    mainAndGiftPpidToAmountMap.put(mainPpid, giftPpid, giftPpidAmount);
                                    List<Integer> mainPpidList = giftPpidToMainPpidListMap.get(giftPpid);
                                    if (Objects.isNull(mainPpidList)) {
                                        mainPpidList = new ArrayList<>();
                                        giftPpidToMainPpidListMap.put(giftPpid, mainPpidList);
                                    }
                                    mainPpidList.add(mainPpid);
                                    break;
                                }
                            }
                        }
                    }
                }
            });

            Set<Integer> giftPpidSet = giftPpidToAmountMap.keySet();
            ArrayList<Integer> giftPpidList = new ArrayList<>(giftPpidSet);
            if (CollectionUtils.isNotEmpty(giftPpidList)) {
                //2.判断赠品库存是否充足
                //通过ppid和areaId查询当前门店的库存量
                List<ProductKcBO> kcCount = orderItemService.getKcCount(giftPpidList, order.getAreaId());
                //取出库存小于一的值
                List<ProductKcBO> collect = kcCount.stream().filter(kc -> {
                    Integer ppriceid = kc.getPpriceid();
                    Integer amount = giftPpidToAmountMap.get(ppriceid);
                    return true;
                }).collect(Collectors.toList());
//                if (CollectionUtils.isNotEmpty(collect)) {
//                    //取两个list的差集
//                    Collection<ProductKcBO> disjunction = CollUtil.disjunction(kcCount, collect);
//                    kcCount.clear();
//                    kcCount.addAll(disjunction);
//                }
                //判断赠品库存是否为空
                if (CollectionUtils.isNotEmpty(kcCount)) {
                    //3.构建赠品对象 （构建购物车对象）
                    List<Integer> giftPpids = kcCount.stream().map(ProductKcBO::getPpriceid).collect(Collectors.toList());

                    List<Productinfo> productinfoByPpid = productinfoService.getProductinfoByPpid(giftPpids);
                    Map<Integer, String> ppriceidToProductNameMap = productinfoByPpid.stream()
                            .collect(Collectors.toMap(Productinfo::getPpriceid, Productinfo::getProductName, (x, y) -> x));
                    giftPpids.forEach((Integer giftPpid) -> {
                        List<Integer> mainPpidList = giftPpidToMainPpidListMap.get(giftPpid);
                        mainPpidList.forEach((Integer mainPpid) -> {
                            List<Map<String, Object>> maps = buildOaBasketTypeGift(Collections.singletonList(giftPpid), mainPpid);
                            maps.forEach((Map<String, Object> x) -> {
                                Integer amount = mainAndGiftPpidToAmountMap.get(mainPpid, giftPpid);
                                if (Objects.nonNull(amount)) {
                                    x.put("product_num", amount);
                                    //自动备货锁定
                                    x.put("beihuo", NumberConstant.TWO);
                                }
                            });
                            //4.塞入购物车对象
                            baskets.addAll(maps);
                            ppids.add(giftPpid);

                            SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
                            String productName = ppriceidToProductNameMap.get(giftPpid);
                            String comment = String.format("追加赠品【%s】", productName);
                            subLogsNewReq.setComment(comment);
                            logs.add(subLogsNewReq);
                        });
                    });

                }
            }
        }
        //赋值下单经纬度信息
        if(XtenantEnum.isJiujiXtenant() && MapUtils.isNotEmpty(oaOrder)){
            oaOrder.put("sub_adds_position",order.getBuyerPosition());
        }

        //提交订单到OA
        R<String> conf = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST);
        if (null == conf || StringUtils.isBlank(conf.getData())) {
            log.error("平台单号:{},获取inwcf前缀失败:{}", order.getOrderId(), conf);
            SpringContextUtil.addRequestErrorMsg("平台单号:{},获取inwcf前缀失败:{}", order.getOrderId(), conf);
            return false;
        }

        String prefix = conf.getData();
        String url = prefix + ThirdPlatformCommonConst.OA_API_SUBMIT_ORDER_EX;
        Map<String, Object> map = new HashMap<>(16);
        map.put("sub", oaOrder);
        map.put("baskets", baskets);
        log.warn("调用订单接口{},参数，{}",url, JSON.toJSONString(map));

        ResponseEntity<String> responseEntityR = null;
        try {
            responseEntityR = SpringUtil.getBean(RetryService.class).retrySaveException(() -> restTemplate.postForEntity(url, map, String.class));
        } catch (Exception e) {
            log.error("平台单号:{},调用OA接口异常",order.getOrderId(), e);
            SpringContextUtil.addRequestErrorMsg("平台单号:{},调用OA接口异常",order.getOrderId());
            return false;
        }
        String res = JSON.toJSONString(responseEntityR);
        log.warn("平台单号:{},调用订单提交接口响应，{}",order.getOrderId(),responseEntityR.getBody());
        if (responseEntityR.getBody() != null) {
            Result<Integer> result = JSON.parseObject(responseEntityR.getBody(), new TypeReference<Result<Integer>>() {});
            if (result.getCode() == 0) {
                log.warn("平台单号:{},创建订单:{}成功",order.getOrderId(),result.getData());
                //取订单id
                Integer subId = result.getData();
                order.setSubId(Convert.toLong(subId));
                //非美团发消息
                if (XtenantEnum.isJiujiXtenant() && !Objects.equals(order.getPlatCode(), ThirdPlatformCommonConst.THIRD_PLAT_MT)) {
                    LossOrderMessagePushParam data = new LossOrderMessagePushParam();
                    data.setSubId(order.getSubId());
                    data.setPlatform(order.getPlatCode());
                    log.info(StrUtil.format("发送亏损订单消息, 平台：{}, 单号: {}, 服务费: {}", data.getPlatform(), data.getSubId(), data.getFee()));
                    rabbitTemplate.convertAndSend(RabbitMqConfig.QUEUE_LOSS_ORDER_MESSAGE_PUSH, JSON.toJSONString(data));
                }
                //如果是美团,新增提单库存实时同步逻辑
                orderService.orderTimeSyncToMetiuan(order, ppids, localArea.get());
                //2分钟后进行黄牛预警
                ScalperWarningReq scalperWarningReq = ScalperWarningReq.builder().order(order).ppids(ppids).build();
                try {
                    firstLmstfyClient.publish(LmstfyConfig.resolve(LmstfyConfig.THIRD_ORDER_SCALPER_WARNING),
                            JSON.toJSONString(scalperWarningReq).getBytes(StandardCharsets.UTF_8),
                            0, (short) 1, 60*2);
                } catch (LmstfyException e) {
                    RRExceptionHandler.logError("美团黄牛预警推送",scalperWarningReq, e, smsService::sendOaMsgTo9JiMan);
                }
                //调用支付接口
                AtomicBoolean platPayFlag = new AtomicBoolean(false);
                AtomicBoolean payablePayFlag = new AtomicBoolean(false);
                AtomicBoolean governmentFlag = new AtomicBoolean(true);
                Integer finalUserId = userId;
                if (StringUtils.isEmpty(platKemu)){
                    platKemu = tenant.getPlatKemu();
                }
                if (StringUtils.isEmpty(venderKemu)){
                    venderKemu = tenant.getVenderKemu();
                }
                String finalPlatKemu = platKemu;
                String finalVenderKemu = venderKemu;
                MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE,()->{
                    baseMapper.updateStatusAndMessageById(null, "已生成订单", order.getId(), subId);
                    platPayFlag.set(orderPayment(prefix, order.getId(), subId, finalUserId, order.getOrderId() + "-1", finalPlatKemu, "平台付款科目", tenant.getTenantCode(), order.getPlatMoney(), localArea.get().getId(), localArea.get().getKind1(), localArea.get().getAuthorizeId(),0));
                    payablePayFlag.set(orderPayment(prefix, order.getId(), subId, finalUserId, order.getOrderId(), finalVenderKemu,
                            "商家付款科目", tenant.getTenantCode(), order.getPayableMoney(),
                            localArea.get().getId(), localArea.get().getKind1(), localArea.get().getAuthorizeId(),0));
                    if(Boolean.TRUE.equals(order.getGovernmentSubsidyFlag()) && order.getGovernmentSubsidyMoney().compareTo(BigDecimal.ZERO) >0){
                        governmentFlag.set(orderPayment(prefix, order.getId(), subId, finalUserId, order.getOrderId(),
                                tenantUserOpt.map(ThirdPlatformTenantUser::getGovernmentSubsidyKemu).orElse(finalVenderKemu),
                                "国补付款科目", tenant.getTenantCode(), order.getGovernmentSubsidyMoney().doubleValue(),
                                localArea.get().getId(), localArea.get().getKind1(), localArea.get().getAuthorizeId(),0));
                    }
                    if (platPayFlag.get() && payablePayFlag.get() && governmentFlag.get()) {
                        baseMapper.updateStatusAndMessageById(1, "已收银", order.getId(), subId);
                    }
                }).commit();
                SysConfigVo sysConfig1 = getSysConfigVos(platKemu, localArea.get().getId(), localArea.get().getAuthorizeId()).stream().findFirst().orElse(new SysConfigVo());
                SysConfigVo sysConfig2 = getSysConfigVos(venderKemu, localArea.get().getId(), localArea.get().getAuthorizeId()).stream().findFirst().orElse(new SysConfigVo());
                //平台处理
                if (isDouYin) {
                    //做账处理
                    if (platPayFlag.get() && payablePayFlag.get()) {
                        voucherByDouDian(order, localArea, sysConfig1, areaInfoById, subId);
                    }
                    return true;
                }
                if (platPayFlag.get() && payablePayFlag.get()) {
                    //做账处理
                    voucher(order, localArea, sysConfig1, sysConfig2, areaInfoById, subId);
                } else if (platPayFlag.get() && !payablePayFlag.get()) {
                    voucher(order, localArea, sysConfig1, null, areaInfoById, subId);
                } else if (!platPayFlag.get() && payablePayFlag.get()) {
                    voucher(order, localArea, null, sysConfig2, areaInfoById, subId);
                }

                if (CollectionUtils.isNotEmpty(logs)) {
                    logs.forEach((SubLogsNewReq log) -> {
                        log.setDTime(LocalDateTime.now());
                        log.setSubId(subId);
                        log.setInUser("系统");
                        log.setType(1);
                        log.setShowType(true);
                    });
                    CompletableFuture.runAsync(() -> subLogsCloud.addSubLogBatch(logs));
                }
            } else {
                String message = result.getMsg();
                SpringContextUtil.addRequestErrorMsg("平台单号:{},调用OA接口[{}]异常",order.getOrderId(), message);
                if (StringUtils.isNotBlank(message) && message.length() > MSG_MAX_LENGTH) {
                    message = message.substring(0, MSG_MAX_LENGTH);
                }
                String finalMessage = message;
                MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE,()->
                        baseMapper.updateStatusAndMessageById(null, finalMessage, order.getId(), null)).commit();
            }
        }
        return true;
    }

    @Override
    public void orderTimeSyncToMetiuan(Order order, List<Integer> ppids, AreaInfo areaInfo) {
        CompletableFuture.runAsync(SpringContextUtil.runnableWithContext(() -> {
            List <Integer> syncPpids = ppids.stream().distinct().collect(Collectors.toList());
            // 获取大件预占库存的门店信息
            Long subId = order.getSubId();
            if (subId == null){
                // 生成本地订单失败,不需要同步库存
                log.warn("平台{}订单{}生成本地订单失败,不需要同步库存", order.getPlatCode(), order.getOrderId());
                return;
            }
            List<Integer> basketIds = basketService.lambdaQuery().eq(Basket::getSubId, subId).eq(Basket::getIsmobile, Boolean.TRUE)
                    .and(cnd -> cnd.isNull(Basket::getIsdel).or().eq(Basket::getIsdel, Boolean.TRUE)).select(Basket::getBasketId)
                    .list().stream().map(Basket::getBasketId).collect(Collectors.toList());

            List<Integer> bigAreaIds = Collections.emptyList();
            if(!basketIds.isEmpty()){
                // 通过baksetId 获取预占大件信息
                bigAreaIds = SpringUtil.getBean(ProductMkcService.class).lambdaQuery().in(ProductMkc::getBasketId, basketIds)
                        .select(ProductMkc::getAreaid).list().stream().map(ProductMkc::getAreaid).distinct()
                        .collect(Collectors.toList());
            }

            Function<AreaInfo, List<String>> storeCodeListFun = ai -> storeService.lambdaQuery().eq(Store::getPlatCode, order.getPlatCode()).eq(Store::getIsEnable, Boolean.TRUE)
                    .and(cnd -> cnd.eq(Store::getAreaId, ai.getId()).or().apply("concat(',',associated_stores,',') like concat('%',{0},'%')", ai.getArea()))
                    .list().stream().map(Store::getStoreCode).distinct().collect(Collectors.toList());
            List <String> storeCodeList = storeCodeListFun.apply(areaInfo);
            for (Integer bigAreaId : bigAreaIds) {
                // 这里循环调用, 数据量少
                if(ObjectUtil.equal(areaInfo.getId(), bigAreaId)){
                    continue;
                }
                // 获取门店编码
                AreaInfo bigAreaInfo = SpringUtil.getBean(CommonStructMapper.class).areaInfoToClientAreaInfo(iareaInfoService.getById(bigAreaId));
                storeCodeList.addAll(storeCodeListFun.apply(bigAreaInfo));
            }

            Dict input = Dict.create().set("ppids", syncPpids).set("platCode", order.getPlatCode())
                    .set("storeCodeList", storeCodeList.stream().distinct().collect(Collectors.toList()));
            stockService.syncToMeiTuanV1(input);
        })).exceptionally(e ->{
            log.error("下单即时同步{}库存异常", order.getPlatCode(), e);
            RRExceptionHandler.logError(StrUtil.format("下单即时同步{}库存", order.getPlatCode()), Dict.create().set("order",order).set("ppids", ppids)
                    .set("areaInfo", areaInfo), e, smsService::sendOaMsgTo9JiMan);
            return null;
        });
    }

    @Override
    public String computeAreaForJD(ComputeAreaForJDReq req){
        String tenantCode = req.getTenantCode();
        Integer oldAreaId = req.getOldAreaId();
        List<OrderItem> itemList = req.getItemList();
        if (StringUtils.isEmpty(tenantCode)
                || Objects.isNull(oldAreaId)
                || CollectionUtils.isEmpty(itemList)) {
            return Convert.toStr(oldAreaId);
        }
        List<Integer> areaIdList = new LinkedList<>();
        //查询是否有关联门店
        JdStore oneStoreByStoreCode = jdStoreService.lambdaQuery()
                .eq(JdStore::getTenantCode,tenantCode)
                .eq(JdStore::getAreaId,oldAreaId)
                .eq(JdStore::getIsEnable,true)
                .list().stream().findFirst().orElse(null);
        if (Objects.isNull(oneStoreByStoreCode) || StringUtils.isEmpty(oneStoreByStoreCode.getAssociatedStores())) {
            return Convert.toStr(oldAreaId);
        }
        List<String> areaIdListStrings = CommonUtil.toStrList(oneStoreByStoreCode.getAssociatedStores());
        R<List<AreaInfo>> listR = areaInfoClient.listAreaInfoByAreas(areaIdListStrings);
        List<AreaInfo> areaInfos = Optional.ofNullable(listR).map(R::getData).orElse(new ArrayList<>());
        areaIdList.add(0, oldAreaId);
        computeAreaIdList(areaIdList, areaIdListStrings, areaInfos);

        //拿到所有的门店或者是关联门店的库存数据
        List<Integer> orderPpid = itemList.stream().map(OrderItem::getPpriceid).collect(Collectors.toList());
        List<ProductKcBO> realInventory = baseMapper.getRealInventory(areaIdList, orderPpid);
        Integer orderAreaId = this.computeOrderAreaId(realInventory, areaIdList, itemList);
        if (!oneStoreByStoreCode.getAssociatedStoresFlag() || Objects.isNull(orderAreaId)) {
            orderAreaId = oldAreaId;
        }
        return Convert.toStr(orderAreaId);
    }

    @Override
    public void computeAreaIdList(List<Integer> areaIdList, List<String> areaIdListStrings, List<AreaInfo> areaInfos) {
        areaIdListStrings.forEach(s -> areaInfos.forEach(area -> {
            if (Objects.equals(area.getArea(), s)) {
                //筛选出在营业时间内的门店
                boolean onBusinessHours;
                if(XtenantEnum.isJiujiXtenant()) {
                    onBusinessHours = isAreaOnBusinessHours(area.getId());
                } else {
                    onBusinessHours = DataUtils.isOnBusinessHours(area.getHours());
                }
                if (onBusinessHours) {
                    areaIdList.add(area.getId());
                }
            }
        }));
    }

    private Boolean isAreaOnBusinessHours(Integer areaId) {
        try {
            //获取门店营业时间
            List<AreaOpeningRes.OpeningTime> hoursList = Optional.ofNullable(SpringUtil.getBean(AreaInfoCloud.class).getAreaOpeningTime(areaId))
                    .map(R::getData).map(AreaOpeningRes::getHoursList).orElse(null);
            //未配置营业时间
            if (CollectionUtils.isEmpty(hoursList)) {
                return false;
            }
            LocalDateTime now = LocalDateTime.now();
            int weekDay = now.getDayOfWeek().getValue() - 1;
            Map<Integer, AreaOpeningRes.OpeningTime> deliveryTimeMap = hoursList.stream().collect(Collectors.toMap(AreaOpeningRes.OpeningTime::getKey, Function.identity()));
            Integer key = Convert.toInt(Math.pow(2, (weekDay)));
            AreaOpeningRes.OpeningTime deliveryTime = deliveryTimeMap.get(key);
            //未查询到配置时间
            if (deliveryTime != null && Objects.nonNull(deliveryTime.getDate1()) && Objects.nonNull(deliveryTime.getDate2())) {
                LocalDateTime startTime = LocalDate.now().atTime(deliveryTime.getDate1());
                LocalDateTime endTime = LocalDate.now().atTime(deliveryTime.getDate2());
                return now.isAfter(startTime) && now.isBefore(endTime);
            }
        } catch (Exception e) {
            RRExceptionHandler.logError("判断门店是否营业时间异常", Dict.create().set("areaId", areaId), e, smsService::sendOaMsgTo9JiMan);
        }
        return false;
    }

    @Override
    public Integer computeOrderAreaId(List<ProductKcBO> productKcBOS, List<Integer> areaIdList, List<OrderItem> orderItem) {
        try {
            //全部品类数量
            long needTypeCount = orderItem.stream().map(OrderItem::getPpriceid).distinct().count();
            //全部下单数量
            long needCount = orderItem.stream().mapToInt(OrderItem::getSkuCount).sum();
            Map<Integer, Integer> areaIdProductTypeCountMap = new HashMap<>();
            Map<Integer, Integer> areaIdProductCountMap = new HashMap<>();
            for (OrderItem item : orderItem) {
                Integer ppriceid = item.getPpriceid();
                Integer skuCount = item.getSkuCount();
                for (ProductKcBO productKcBO : productKcBOS) {
                    Integer ppriceidKc = productKcBO.getPpriceid();
                    Integer leftCount = productKcBO.getLeftCount() > skuCount ? skuCount : productKcBO.getLeftCount();
                    if (ppriceid.equals(ppriceidKc)) {
                        Integer areaId = productKcBO.getAreaId();
                        int productTypeCount = Objects.isNull(areaIdProductTypeCountMap.get(areaId)) ? 0 : areaIdProductTypeCountMap.get(areaId);
                        areaIdProductTypeCountMap.put(areaId, productTypeCount + 1);
                        int productCount = Objects.isNull(areaIdProductCountMap.get(areaId)) ? 0 : areaIdProductCountMap.get(areaId);
                        areaIdProductCountMap.put(areaId, productCount + leftCount);
                    }
                }
            }

            //第一个门店为本地门店
            for (Integer areaId : areaIdList) {
                if (!areaIdProductTypeCountMap.containsKey(areaId)) {
                    continue;
                }
                Integer productTypeCount = areaIdProductTypeCountMap.get(areaId);
                Integer productCount = areaIdProductCountMap.get(areaId);
                //优先匹配同时满足库存和品类的门店
                if (needTypeCount == productTypeCount && needCount == productCount) {
                    return areaId;
                }
            }
            Integer maxProductTypeCount = areaIdProductTypeCountMap.values().isEmpty() ? 0 : Collections.max(areaIdProductTypeCountMap.values());
            Integer maxProductCount = areaIdProductCountMap.values().isEmpty() ? 0 : Collections.max(areaIdProductCountMap.values());
            //第一个门店为本地门店
            for (Integer areaId : areaIdList) {
                if (!areaIdProductTypeCountMap.containsKey(areaId)) {
                    continue;
                }
                Integer productTypeCount = areaIdProductTypeCountMap.get(areaId);
                Integer productCount = areaIdProductCountMap.get(areaId);
                //其次匹配满足品类,库存最大的门店
                if (needTypeCount == productTypeCount) {
                    if (productCount.equals(maxProductCount)) {
                        return areaId;
                    }
                }
            }

            Map<Integer, Integer> maxProductTypeAreaMap = new HashMap<>();
            List<Integer> maxProductTypeAreaIdList = new ArrayList<>();
            //第一个门店为本地门店
            for (Integer areaId : areaIdList) {
                if (!areaIdProductTypeCountMap.containsKey(areaId)) {
                    continue;
                }
                Integer productTypeCount = areaIdProductTypeCountMap.get(areaId);
                Integer productCount = areaIdProductCountMap.get(areaId);
                //再匹配最大品类的门店
                if (maxProductTypeCount.equals(productTypeCount)) {
                    maxProductTypeAreaIdList.add(areaId);
                    maxProductTypeAreaMap.put(areaId, productCount);
                }
            }
            //最大品类的最大库存
            Integer max = maxProductTypeAreaMap.values().isEmpty() ? 0 : Collections.max(maxProductTypeAreaMap.values());
            for (Integer areaId : maxProductTypeAreaIdList) {
                Integer productCount = maxProductTypeAreaMap.get(areaId);
                //再匹配最大库存的门店
                if (max.equals(productCount)) {
                    return areaId;
                }
            }
        }catch (Exception e){
            log.error("订单id={}匹配门店失败", orderItem.get(0).getId(), e);
            return null;
        }
        return null;
    }

    @Override
    @DS(DataSourceConstants.SMALLPRO_WRITE)
    public boolean submitOaOrderByRecover(Integer id, Date estimateArrivalTime) {
        Order order = MultipleTransaction.query(DataSourceConstants.SMALLPRO_WRITE, () -> getById(id));
        if (null == order || null != order.getSubId()) {
            meituanJdWorkLogService.saveByLog(StrUtil.format("提交订单失败,订单号：{}",id),"不存在或已经提交到OA",
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            SpringContextUtil.addRequestErrorMsg("订单id={}不存在或已经提交到OA",id);
            return false;
        }
        //商户校验
        Store store = storeService.getOneStoreByStoreCode(order.getPlatCode(), order.getStoreCode());
        if (null == store) {
            meituanJdWorkLogService.saveByLog(StrUtil.format("提交订单失败,订单号：{}",id), StrUtil.format("【{}】提交订单到OA：匹配门店【{}】失败", order.getPlatCode(), order.getStoreCode()),
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            SpringContextUtil.addRequestErrorMsg("【{}】提交订单到OA：匹配门店【{}】失败", order.getPlatCode(), order.getStoreCode());
            return false;
        }
        Tenant tenant = tenantService.getOneTenantBy(store.getTenantCode(), store.getPlatCode());
        if (null == tenant) {
            meituanJdWorkLogService.saveByLog(StrUtil.format("提交订单失败,订单号：{}",id), StrUtil.format("【{}】提交订单到OA：匹配商户【{}】失败", order.getPlatCode(), store.getTenantCode()),
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            SpringContextUtil.addRequestErrorMsg("【{}】提交订单到OA：匹配商户【{}】失败", order.getPlatCode(), store.getTenantCode());
            return false;
        }
        String platKemu = StringUtils.EMPTY;
        String venderKemu = StringUtils.EMPTY;
        Integer tenantUserId = tenant.getUserId();
        if (XtenantEnum.isJiujiXtenant()){
            Integer xtenant = Optional.ofNullable(areaInfoService.getAreaInfoById(order.getAreaId())).map(AreaInfo::getXtenant).orElse(0);
            ThirdPlatformTenantUser byTenant = thirdPlatformTenantUserService.getByTenant(TenantUserBO.builder().tenantId(tenant.getId()).platCode(tenant.getPlatCode()).xtenant(xtenant).build());
            Integer userIdByTenant = Objects.nonNull(byTenant) ? byTenant.getUserId() : null;
            platKemu = Objects.nonNull(byTenant) ? byTenant.getPlatKemu() : null;
            venderKemu = Objects.nonNull(byTenant) ? byTenant.getVenderKemu() : null;
            if (null != userIdByTenant && userIdByTenant != 0) {
                tenantUserId = userIdByTenant;
            }
        }
        //获取用户信息，主要是获取到手机号
        BbsxpUsers bbsxpUsers =
                bbsxpUsersService.getInfoByUserId(new LambdaUpdateWrapper<BbsxpUsers>().eq(BbsxpUsers::getId,
                        tenantUserId));
        if (null == bbsxpUsers || StringUtils.isEmpty(bbsxpUsers.getMobile())) {
            meituanJdWorkLogService.saveByLog(StrUtil.format("提交订单失败,订单号：{}",id), StrUtil.format("用户信息异常【用户id{}】", tenant.getUserId()),
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            SpringContextUtil.addRequestErrorMsg("用户信息异常【用户id{}】", tenant.getUserId());
            return false;
        }
        String userMobile = bbsxpUsers.getMobile();
        //校验本地门店
        AtomicReference<AreaInfo> localArea = new AtomicReference<>(areaInfoService.getAreaInfoById(store.getAreaId()));
        //根据门店盘点是否是九机的租户
        AreaInfo areaInfoById = areaInfoService.getAreaInfoById(order.getAreaId());
        if (null == localArea.get()) {
            meituanJdWorkLogService.saveByLog(StrUtil.format("提交订单失败,订单号：{}",id), StrUtil.format("【{}】提交订单到OA：匹配OA门店【{}】失败", order.getPlatCode(), store.getAreaId()),
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            SpringContextUtil.addRequestErrorMsg("【{}】提交订单到OA：匹配OA门店【{}】失败", order.getPlatCode(), store.getAreaId());
            return false;
        }
        //校验本地商品
        List<OrderItem> itemList = MultipleTransaction.query(DataSourceConstants.SMALLPRO_WRITE, () -> orderItemService.listOrderItemByMkcId(order.getId()));
        if (CollectionUtils.isEmpty(itemList)) {
            itemList.forEach(it ->
                    it.setSkuId(StrUtil.subSuf(it.getSkuId(), 1)));
            meituanJdWorkLogService.saveByLog(StrUtil.format("提交订单失败,订单号：{}", id), StrUtil.format("【{}-{}】订单下单商品无法匹配OA商品配置", order.getPlatCode(), order.getId()),
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            SpringContextUtil.addRequestErrorMsg("【{}-{}】订单下单商品无法匹配OA商品配置", order.getPlatCode(), order.getId());
            return false;
        }
        //更改商品的价格
        List<Integer> mkcIds = itemList.stream().map(OrderItem::getMkcId).collect(Collectors.toList());
        List<Integer> toBasketIds = itemList.stream().map(OrderItem::getToBasketId).filter(Objects::nonNull).collect(Collectors.toList());
        List<RecoverMkcBO> recoverMkcList = MultipleTransaction.query(DataSourceConstants.SMALLPRO_WRITE, () -> productConfigMapper.getPriceByMkcId(mkcIds));
        //解锁商品预占
        if (unLockBasket(itemList, mkcIds, toBasketIds)){
            return false;
        }

        if (CollUtil.isEmpty(recoverMkcList)) {
            itemList.forEach(it -> it.setMemberPrice(it.getTradePrice()));
        } else {
            itemList.forEach(it -> recoverMkcList.forEach(re -> {
                if (Objects.equals(re.getMkcId(), it.getMkcId())) {
                    it.setMemberPrice(re.getSalePirce());
                }
            }));
        }
        //订单类型--抖音订单
        int subType = 22;
        //当号码不为空 且 小于15位时
        if (StrUtil.isNotEmpty(order.getBuyerMobile()) && order.getBuyerMobile().length()<NumberConstant.FIFTEEN){
            userMobile = order.getBuyerMobile();
        }
        //构建订单对象
        SubmitRecoverWcfDo submitOrder = new SubmitRecoverWcfDo();
        //构建订单对象
        SubmitRecoverWcfDo.Sub sub  = buildOaOrderParamByRecover(order.getAreaId(), tenant.getUserId(), order.getBuyerName(), userMobile, ThirdPlatformCommonConst.ORDER_OP_USER_INTERNET,
                ThirdPlatformCommonConst.DELIVERY_TYPE_EXPRESS_TRANSPORT, ThirdPlatformCommonConst.ORDER_PAY_TYPE_ONLINE, subType, order.getBuyerRemark(),
                order.getOrderTime(), order.getBuyerCountry(), order.getBuyerAddress(), estimateArrivalTime);
        //构建购物车对象
        List<SubmitRecoverWcfDo.Basket> baskets = buildOaBasketsParamByRecover(itemList);
        submitOrder.setSub(sub);
        submitOrder.setBasket(baskets);
        //获取接口
        Map param = (Map) JSON.parse(JSONObject.toJSONString(submitOrder));
        //提交订单到OA
        R<String> conf = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST);
        if (null == conf || StringUtils.isBlank(conf.getData())) {
            SpringContextUtil.addRequestErrorMsg("获取inwcf前缀失败:{}", conf);
            return false;
        }
        String prefix = conf.getData();
        String url = prefix + ThirdPlatformCommonConst.OA_API_SUBMIT_ORDER_ZHKEY;
        ResponseEntity<String> responseEntityR ;
        SubOrderResult subOrderResult;
        try {
            responseEntityR = restTemplate.postForEntity(url, param, String.class);
            subOrderResult = JSON.parseObject(responseEntityR.getBody(), SubOrderResult.class);
        } catch (Exception e) {
            SpringContextUtil.addRequestErrorMsg("调用订单创建接口失败{},调用OA接口异常", e);
            return false;
        }
        log.warn("良品提单result:{}", param);
        if (subOrderResult != null && subOrderResult.getOrderid() > 0) {
            log.warn("创建订单成功");
            //取订单id
            Integer subId = subOrderResult.getOrderid();
            //调用支付接口
            AtomicBoolean platPayFlag = new AtomicBoolean(false);
            AtomicBoolean payablePayFlag = new AtomicBoolean(false);
            if (StringUtils.isEmpty(platKemu)){
                platKemu = tenant.getPlatKemu();
            }
            if (StringUtils.isEmpty(venderKemu)){
                venderKemu = tenant.getVenderKemu();
            }
            String finalPlatKemu = platKemu;
            String finalVenderKemu = venderKemu;
            MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE,()->{
                //更新订单状态
                baseMapper.updateStatusAndMessageById(null, "已生成订单", order.getId(), subId);
                //良品订单要更新商品的状态
                List<Integer> collect = itemList.stream().map(OrderItem::getMkcId).collect(Collectors.toList());
                R<Boolean> booleanR = productConfigService.updateSellType(collect, NumberConstant.ONE);
                if (!booleanR.isSuccess()){
                    meituanJdWorkLogService.saveByLog("订单接口调用异常", StrUtil.format("更新商品售出状态失败，请手动更新：{}", collect),
                            "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
                }
                platPayFlag.set(orderPayment(prefix, order.getId(), subId, tenant.getUserId(), order.getOrderId() + "-1", finalPlatKemu, "平台付款科目", tenant.getTenantCode(), order.getPlatMoney(), localArea.get().getId(), localArea.get().getKind1(), localArea.get().getAuthorizeId(),1));
                payablePayFlag.set(orderPayment(prefix, order.getId(), subId, tenant.getUserId(), order.getOrderId(), finalVenderKemu, "商家付款科目", tenant.getTenantCode(), order.getPayableMoney(), localArea.get().getId(), localArea.get().getKind1(), localArea.get().getAuthorizeId(),1));
                if (platPayFlag.get() && payablePayFlag.get()) {
                    baseMapper.updateStatusAndMessageById(1, "已收银", order.getId(), subId);
                }
            }).commit();
            SysConfigVo sysConfig1 = getSysConfigVos(platKemu, localArea.get().getId(), localArea.get().getAuthorizeId()).stream().findFirst().orElse(new SysConfigVo());
            //做账处理
            if (platPayFlag.get() && payablePayFlag.get()) {
                voucherByDouDian(order, localArea, sysConfig1, areaInfoById, subId);
            }
            return true;
        }else {
            if (!Optional.ofNullable(subOrderResult).isPresent() || subOrderResult.getMsg() == null) {
                SpringContextUtil.addRequestErrorMsg("oa建单返回结果空异常");
                return false;
            }
            meituanJdWorkLogService.saveByLog("订单接口调用异常", StrUtil.format("良品创建订单失败：{}", subOrderResult.getMsg()),
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            SpringContextUtil.addRequestErrorMsg("良品创建订单失败{}", subOrderResult.getMsg());
            return false;
        }
    }

    private boolean unLockBasket(List<OrderItem> itemList, List<Integer> mkcIds, List<Integer> toBasketIds) {
        //批量获取良品配置, 进行解锁占用
        Map<Integer, ProductConfig> mkcIdConfigMap = MultipleTransaction.query(DataSourceConstants.SMALLPRO_WRITE, () ->
                productConfigService.lambdaQuery().in(ProductConfig::getMkcId, mkcIds).list()
                .stream().collect(Collectors.toMap(ProductConfig::getMkcId, Function.identity(), (v1, v2) -> v1)));
        CsharpRecoverService csharpRecoverService = SpringUtil.getBean(CsharpRecoverService.class);
        //批量获取订单的门店信息
        Map<Integer, RecoverMarketinfo> basketIdSubMap = MultipleTransaction.query(DataSourceConstants.SMALLPRO_WRITE,
                () -> SpringUtil.getBean(RecoverMarketinfoService.class).listSubByBasketId(toBasketIds));
        //批量获取门店信息
        Map<Integer, AreaInfo> areaInfoMap = CommonUtils.getResultData(areaInfoClient.listAreaInfo(basketIdSubMap.values().stream()
                        .map(RecoverMarketinfo::getAreaid).distinct().collect(Collectors.toList())),
                errMsg -> {
                    SpringContextUtil.addRequestErrorMsg("门店信息获取异常");
                    throw new CustomizeException("门店信息获取异常");
                }).stream().collect(Collectors.toMap(AreaInfo::getId, Function.identity(), (v1,v2) -> v1));
        AbstractCurrentRequestComponent currentRequestComponent = SpringUtil.getBean(AbstractCurrentRequestComponent.class);
        for (OrderItem orderItem : itemList) {
            ProductConfig productConfig = mkcIdConfigMap.get(orderItem.getMkcId());
            if(productConfig == null){
                continue;
            }
            if(productConfig != null && ObjectUtil.equals(productConfig.getRuleCode(), Convert.toStr(orderItem.getToBasketId()))){
                //自动解锁商品
                RecoverMarketinfo recoverMarketinfo = basketIdSubMap.get(orderItem.getToBasketId());
                if(recoverMarketinfo == null){
                    continue;
                }
                AreaInfo areaInfo = areaInfoMap.get(recoverMarketinfo.getAreaid());
                R delResult = currentRequestComponent.invokeWithUser(areaInfo.getXtenant(),simulateUser(areaInfo, "抖音"),
                        oaUser -> csharpRecoverService.delOrder(orderItem.getToBasketId(), 2, "抖音下单自动解锁预占"));
                if(!delResult.isSuccess()){
                    SpringContextUtil.addRequestErrorMsg("抖音良品下单自动解锁[{}]预占失败, 原因: {}", orderItem.getToBasketId(), delResult.getUserMsg());
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 构建商品信息
     * @param itemList
     * @return
     */
    private List<SubmitRecoverWcfDo.Basket> buildOaBasketsParamByRecover(List<OrderItem> itemList) {
        List<SubmitRecoverWcfDo.Basket> baskets = new ArrayList<>();
        for (OrderItem item : itemList) {
            SubmitRecoverWcfDo.Basket basket = new SubmitRecoverWcfDo.Basket();
            int isMobile = 0;
            if (item.getMobile1() != null && item.getMobile1()) {
                isMobile = 1;
            }
            basket.setPpriceid(item.getPpriceid());
            basket.setProduct_num(item.getSkuCount());
            basket.setProduct_price(NumberUtil.toBigDecimal(item.getTradePrice()));
            basket.setProduct_price1(NumberUtil.toBigDecimal(item.getMemberPrice()));
            basket.setIsmobile(isMobile);
            basket.setBeihuo(NumberConstant.TWO);
            basket.setMkc_id(item.getMkcId());
            baskets.add(basket);
        }
        return baskets;
    }

    /**
     * 良品订单对象构建
     * @param areaId areaId
     * @param userId userId
     * @param buyerName 收货人名称
     * @param userMobile 收货人电话
     * @param inUser  订单操作人 网络
     * @param delivery 配送方式 1：到店自取
     * @param subPay 订单支付方式：1 在线支付
     * @param subType 订单类型
     * @param comment 订单备注
     * @param orderTime 下单时间
     * @param buyerCity 收货人市id
     * @param buyerAddress 收货人地址
     * @param deliveryTime 派送日期
     * @return 良品订单对象
     */
    private SubmitRecoverWcfDo.Sub buildOaOrderParamByRecover(Integer areaId, Integer userId, String buyerName, String userMobile, String inUser,
                                                           Integer delivery, Integer subPay, Integer subType, String comment,
                                                           Date orderTime, String buyerCity, String buyerAddress, Date deliveryTime) {
        SubmitRecoverWcfDo.Sub sub = createOaDataSub();
        sub.setArea(String.valueOf(areaId));
        sub.setUserid(userId);
        sub.setSub_to(buyerName);
        sub.setSub_mobile(userMobile);
        sub.setDelivery(delivery);
        sub.setInuser(inUser);
        sub.setComment(comment);
        sub.setSub_pay(subPay);
        sub.setSubtype(subType);
        sub.setDid(Integer.valueOf(buyerCity));
        sub.setSub_adds(buyerAddress);
        //偏移10毫秒
        sub.setExpectTime(DateUtil.offsetMillisecond(deliveryTime,10));
        return sub;
    }

    /**
     * 初始化订单参数
     * @return
     */
    private static SubmitRecoverWcfDo.Sub createOaDataSub() {
        SubmitRecoverWcfDo.Sub oaDataSub = new SubmitRecoverWcfDo.Sub();
        oaDataSub.setCoinM(0);
        oaDataSub.setFeeM(0);
        oaDataSub.setJidianM(0);
        oaDataSub.setJifens(0);
        oaDataSub.setSave_money(0);
        oaDataSub.setYifuM(0);
        oaDataSub.setYingfuM(0);
        oaDataSub.setYouhui1M(0);
        oaDataSub.setZitifankuan(0);
        oaDataSub.setAreaid(0);
        oaDataSub.setBeiState(0);
        oaDataSub.setCh999Userid(0);
        oaDataSub.setCityid(0);
        oaDataSub.setDelivery(0);
        oaDataSub.setFromid(0);
        oaDataSub.setIsSpecial(0);
        oaDataSub.setSub_check(0);
        oaDataSub.setSub_id(0);
        oaDataSub.setSub_pay(0);
        oaDataSub.setSubtype(0);
        oaDataSub.setUserTime(0);
        oaDataSub.setUserclass(0);
        oaDataSub.setZitidianID("0");
        return oaDataSub;
    }

    /**
     * 美团凭证做账
     * @param order order
     * @param localArea localArea
     * @param sysConfig1 sysConfig1
     * @param sysConfig2 sysConfig2
     * @param areaInfoById  areaInfoById
     * @param subId subId
     */
    private void voucher(Order order, AtomicReference<AreaInfo> localArea, SysConfigVo sysConfig1, SysConfigVo sysConfig2, AreaInfo areaInfoById, Integer subId) {
        if (areaInfoById.getXtenant() < NumberConstant.ONE_THOUSAND) {
            PingzhengBO pingzhengBO = new PingzhengBO();
            String zhaiyao = "", fzhs = "", jief = "", daif = "", kemu = "";
            //摘要
            String zy = localArea.get().getArea() + "店，美团订单，订单号：" + subId;
            if (sysConfig1 != null && sysConfig2 != null) {
                zhaiyao = zy + "|" + zy + "|" + zy;
                //科目
                kemu = sysConfig1.getKemu() + "|" + sysConfig2.getKemu() + "|220301";
                //平台承担金额+用户应付金额 = 贷
                double price = order.getPlatMoney() + order.getPayableMoney();
                jief = DecideUtil.iif(BigDecimal.valueOf(order.getPlatMoney()).compareTo(BigDecimal.ZERO) == 0, "0", order.getPlatMoney()) + "|" + DecideUtil.iif(BigDecimal.valueOf(order.getPayableMoney()).compareTo(BigDecimal.ZERO) == 0, "0", order.getPayableMoney()) + "|0";
                daif = "0|0|" + DecideUtil.iif(BigDecimal.valueOf(price).compareTo(BigDecimal.ZERO) == 0, "0", price);
                fzhs = "无|无|无";
            } else if (sysConfig2 == null && sysConfig1 != null) {
                Double platMoney = order.getPlatMoney();
                zhaiyao = zy + "|" + zy;
                //科目
                kemu = sysConfig1.getKemu() + "|220301";
                jief = DecideUtil.iif(BigDecimal.valueOf(order.getPlatMoney()).compareTo(BigDecimal.ZERO) == 0, "0", order.getPlatMoney()) + "|0";
                daif = "0|" + DecideUtil.iif(BigDecimal.valueOf(platMoney).compareTo(BigDecimal.ZERO) == 0, "0", platMoney);
                fzhs = "无|无";
            } else if (sysConfig2 != null) {
                zhaiyao = zy + "|" + zy;
                //科目
                kemu = sysConfig2.getKemu() + "|220301";
                jief = DecideUtil.iif(BigDecimal.valueOf(order.getPayableMoney()).compareTo(BigDecimal.ZERO) == 0, "0", order.getPayableMoney()) + "|无";
                daif = "无|" + DecideUtil.iif(BigDecimal.valueOf(order.getPayableMoney()).compareTo(BigDecimal.ZERO) == 0, "0", order.getPayableMoney());
                fzhs = "无|无";
            }
            pingzhengBO.setZhaiyao(zhaiyao).setKemu(kemu).setFzhs(fzhs).setJief(jief).setDaif(daif);
            PingzhengResultBO result = voucherService.addPingZheng(pingzhengBO);
            if (result == null || !result.getFlag()) {
                String msg = "美团凭证生成失败,订单号:" + subId;
                if (result != null) {
                    msg = msg + ",原因：" + result.getErrorMsg();
                }
                log.error(msg);
            }
        }
    }

    /**
     * 抖音凭证做账
     * @param order order
     * @param localArea localArea
     * @param sysConfig sysConfig1
     * @param areaInfoById  areaInfoById
     * @param subId subId
     */
    private void voucherByDouDian(Order order, AtomicReference<AreaInfo> localArea, SysConfigVo sysConfig, AreaInfo areaInfoById, Integer subId) {
        if (areaInfoById.getXtenant() < NumberConstant.ONE_THOUSAND) {
            PingzhengBO pingzhengBO = new PingzhengBO();
            String zhaiyao = "", fzhs = "", jief = "", daif = "", kemu = "";
            //摘要
            String zy = localArea.get().getArea() + "店，抖音订单，订单号：" + subId;
            if (sysConfig != null) {
                zhaiyao = zy + "|" + zy;
                //科目
                kemu = DecideUtil.iif(StrUtil.isEmpty(sysConfig.getKemu()), "1122131", sysConfig.getKemu()) + "|220301";
                //平台承担金额+用户应付金额 = 贷
                double price = order.getPlatMoney() + order.getPayableMoney();
                jief = DecideUtil.iif(BigDecimal.valueOf(price).compareTo(BigDecimal.ZERO) == 0, "0", price) + "|0";
                daif = "0|" + DecideUtil.iif(BigDecimal.valueOf(price).compareTo(BigDecimal.ZERO) == 0, "0", price);
                fzhs = "无|无";
            }
            pingzhengBO.setZhaiyao(zhaiyao).setKemu(kemu).setFzhs(fzhs).setJief(jief).setDaif(daif);
            PingzhengResultBO result = voucherService.addPingZheng(pingzhengBO);
            if (result == null || !result.getFlag()) {
                String msg = "抖音凭证生成失败,订单号:" + subId;
                if (result != null) {
                    msg = msg + ",原因：" + result.getErrorMsg();
                }
                log.error(msg);
            }
        }
    }


    /**
     * 构建OA订单购物车对象
     *
     * @param itemList
     * @param order
     * @return
     */
    private static List<Map<String, Object>> buildOaBasketsParam(List<OrderItem> itemList, Order order) {
        List<Map<String, Object>> baskets = new ArrayList<>();
        for (OrderItem item : itemList) {
            Map<String, Object> map = new HashMap<>(16);
            int isMobile = 0;
            if (item.getMobile1() != null && item.getMobile1()) {
                isMobile = 1;
            }

            map.put("ppriceid", item.getPpriceid());
            map.put("product_num", item.getSkuCount());
            map.put("product_price", item.getTradePrice());
            map.put("product_price1", item.getMemberPrice());
            map.put("product_peizhi", item.getConfig());
            map.put("ismobile", isMobile);
            map.put("seller", ThirdPlatformCommonConst.ORDER_OP_USER_INTERNET);
            map.put(INUSER,ThirdPlatformCommonConst.ORDER_OP_USER_INTERNET);
            //是否备货
            if(Boolean.TRUE.equals(item.getMobile1()) && Boolean.TRUE.equals(order.getGovernmentSubsidyFlag())){
                map.put("beihuo", NumberConstant.ZERO);
            }else{
                map.put("beihuo", NumberConstant.TWO);
            }
            baskets.add(map);
        }
        return baskets;
    }

    /**
     * 构建OA订单购物车对象 -- 赠品对象
     *
     * @param ppidList
     * @return
     */
    private List<Map<String, Object>> buildOaBasketTypeGift(List<Integer> ppidList, Integer mainPpid) {
        //根据ppid查询商品信息
        List<Productinfo> productinfoByPpid = productinfoService.getProductinfoByPpid(ppidList);
        List<Map<String, Object>> baskets = new ArrayList<>();
        for (Productinfo item : productinfoByPpid) {
            Map<String, Object> map = new HashMap<>(16);
            map.put("ppriceid", item.getPpriceid());
            map.put("product_num", NumberConstant.ONE);
            map.put("product_price", NumberConstant.ZERO);
            map.put("product_price1", item.getPricefd());
            map.put("product_peizhi", item.getConfig());
            map.put("ismobile", NumberConstant.ZERO);
            map.put("seller", ThirdPlatformCommonConst.ORDER_OP_USER_INTERNET);
            map.put("giftid", mainPpid);
            map.put("type", BasketTypeEnum.BASKET_TYPE_GIFT.getCode());
            map.put(INUSER, ThirdPlatformCommonConst.ORDER_OP_USER_INTERNET);
            baskets.add(map);
        }
        return baskets;
    }

    /**
     * 构建OA订单对象
     *
     * @param areaId     本地门店id
     * @param userId     下单会员id
     * @param buyerName  用户名称
     * @param userMobile 用户手机号
     * @param inUser     销售
     * @param delivery   配送方式
     * @param subPay     支付方式
     * @param subType    订单类型
     * @param comment    备注
     * @return
     */
    private Map<String, Object> buildOaOrderParam(Integer areaId, Integer userId, String buyerName, String userMobile, String inUser,
                                                  Integer delivery, Integer subPay, Integer subType, String comment,
                                                  Date orderTime, String buyerCity, String buyerAddress, Date deliveryTime){
        Map<String, Object> param = new HashMap<>(16);
        param.put("area", String.valueOf(areaId));
        param.put("userid", userId);
        param.put("sub_to", CommonUtils.maxLength(buyerName,20));
        param.put("sub_mobile", userMobile);
        param.put("delivery", delivery);
        param.put(INUSER, inUser);
        //操作人员
        param.put("opUser", ThirdPlatformCommonConst.ORDER_OP_USER_SYSTEM);
        param.put("comment", CommonUtils.maxLength(comment,NumberConstant.FIVE_HUNDRED));
        param.put("sub_pay", subPay);
        param.put("subtype", subType);
        //派送日期
        param.put("dispatchTime", DateUtil.format(orderTime,"yyyy-MM-dd HH:mm:ss"));
        //省份code
        param.put("did", buyerCity);
        //收货地址
        param.put("sub_adds", buyerAddress);
        //送达时间
        param.put("expectTime", DateUtil.format(deliveryTime,"yyyy-MM-dd HH:mm:ss"));
        return param;
    }
    private Map<String, Object> buildOaOrderParamToJiuji(Integer areaId, Integer userId, String buyerName, String userMobile, String inUser,
                                                         Integer delivery, Integer subPay, Integer subType, String comment,
                                                         Date orderTime, String buyerCity, String buyerAddress, Date deliveryTime,OrderExtendVO orderExtendVO) {
        Integer pickType = Optional.ofNullable(orderExtendVO.getPickType()).orElse(PickTypeEnum.ZERO.getCode());
        Map<String, Object> param = new HashMap<>(16);
        if(pickType.equals(PickTypeEnum.ONE.getCode())){
            param.put("kcAreaid",String.valueOf(areaId));
        }
        param.put("area", String.valueOf(areaId));
        param.put("userid", userId);
        param.put("sub_to", CommonUtils.maxLength(buyerName,20));
        param.put("sub_mobile", userMobile);
        param.put("delivery", delivery);
        param.put(INUSER, inUser);
        //操作人员
        param.put("opUser", ThirdPlatformCommonConst.ORDER_OP_USER_SYSTEM);
        param.put("comment", CommonUtils.maxLength(comment,NumberConstant.FIVE_HUNDRED));
        param.put("sub_pay", subPay);
        param.put("subtype", subType);
        //派送日期
        param.put("dispatchTime", DateUtil.format(orderTime,"yyyy-MM-dd HH:mm:ss"));
        //省份code
        param.put("did", buyerCity);
        //收货地址
        param.put("sub_adds", buyerAddress);
        //送达时间
        param.put("expectTime", DateUtil.format(deliveryTime,"yyyy-MM-dd HH:mm:ss"));
        return param;
    }

    //构建订单对象
    private Map<String, Object> buildPayParam(SysConfigVo subPayType, Integer subId, Integer userId, double orderTotal, int areaKind1, int areaid, String jdOrderId, int type) {
        Map<String, Object> param = new HashMap<>(16);
        param.put("sub_id", subId);
        param.put("userid", userId);
        if (Objects.equals(type, NumberConstant.ONE)) {
            param.put("num", jdOrderId);
        } else {
            param.put("num", String.valueOf(subId));
        }
        param.put("numType", subPayType.getValue());
        param.put("hejim", orderTotal);
        param.put("yingshou", orderTotal);
        param.put("sub_pay05", orderTotal);
        param.put("rankList", Arrays.asList(new String[]{"777"}));
        param.put(INUSER, ThirdPlatformCommonConst.ORDER_OP_USER_INTERNET);
        param.put("curUser", "系统");
        param.put("trade", "系统");
        param.put("area_kind1", areaKind1);
        param.put("areaid", areaid);
        param.put("curAreaid", areaid);
        param.put("ch999_id", 0);
        return param;
    }

    /**
     * 收银
     * @param prefix prefix
     * @param id id
     * @param subId subId
     * @param userId userId
     * @param jdOrderId jdOrderId
     * @param keMuName keMuName
     * @param keMuNameDes keMuNameDes
     * @param tenantCode tenantCode
     * @param orderTotal orderTotal
     * @param areaId areaId
     * @param areaKind1 areaKind1
     * @param authorizeId 门店授权Id
     * @param type 0是新品 1是良品
     * @return
     */
    private boolean orderPayment(String prefix, Integer id, Integer subId, Integer userId, String jdOrderId, String keMuName, String keMuNameDes,
                                 String tenantCode, double orderTotal, int areaId, int areaKind1, int authorizeId ,int type) {
        if (BigDecimal.valueOf(orderTotal).compareTo(BigDecimal.ZERO) == 0) {
            return true;
        }
        if (StringUtils.isBlank(keMuName)) {
            String message = String.format("商户(%s)没有配置%s名称", tenantCode, keMuNameDes);
            baseMapper.updateStatusAndMessageById(null, message, id, null);
            return false;
        } else {
            List<SysConfigVo> kemuList = getSysConfigVos(keMuName, areaId, authorizeId);
            if (CollectionUtils.isEmpty(kemuList)) {
                String message = String.format("商户(%s)%s名称配置失效", tenantCode, keMuNameDes);
                baseMapper.updateStatusAndMessageById(null, message, id, null);
                return false;
            } else {
                Map<String, Object> pay = buildPayParam(kemuList.get(0), subId, userId, orderTotal, areaKind1, areaId ,jdOrderId, type);
                R<String> conf = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST);
                if (null == conf || StringUtils.isBlank(conf.getData())) {
                    log.error("获取inwcf前缀失败:{}", conf);
                    return false;
                }
                String payUrl = prefix + ThirdPlatformCommonConst.OA_API_SUB_PAY;
                //判断支付的是新机还是良品
                if (Objects.equals(type, NumberConstant.ONE)) {
                    //判断是良品订单支付
                    payUrl = prefix + ThirdPlatformCommonConst.OA_API_SUBMIT_ORDER_SUBPAY;
                }
                Map<String, Object> payParam = new HashMap<>(2);
                payParam.put("djjNum", jdOrderId);
                payParam.put("pay", pay);
                log.warn("调用订单支付接口{},参数，{}",payUrl,JSON.toJSONString(payParam));
                ResponseEntity<R> payResponseEntityR = restTemplate.postForEntity(payUrl, payParam, R.class);
                String payRes = JSON.toJSONString(payResponseEntityR);
                log.warn("调用订单支付接口响应，{}", payRes);
                if (payResponseEntityR.getBody().getCode() == 0) {
                    return true;
                } else {
                    String message = payResponseEntityR.getBody().getMsg();
                    if (StringUtils.isNotBlank(message) && message.length() > MSG_MAX_LENGTH) {
                        message = message.substring(0, MSG_MAX_LENGTH);
                    }
                    baseMapper.updateStatusAndMessageById(null, message, id, null);
                    return false;
                }
            }
        }
    }

    /**
     * 查询科目配置
     *
     * @param keMuName    科目名称
     * @param areaId      门店id
     * @param authorizeId 门店授权id
     * @return
     */
    private List<SysConfigVo> getSysConfigVos(String keMuName, int areaId, int authorizeId) {
        //查询科目配置
        List<SysConfigVo> kemuList = oaSysConfigService.getListByAuthorizeId(authorizeId);
        kemuList = kemuList.stream()
                .sorted(Comparator.comparing(obj -> {
                    boolean match = StringUtils.isBlank(obj.getAreaids())
                            || CollectionUtils.isEmpty(CommonUtil.covertIdStr(obj.getAreaids()))
                            || CommonUtil.covertIdStr(obj.getAreaids()).contains(areaId);
                    return !match; // 匹配的返回 false，排在前面
                }))
                .filter(obj -> StringUtils.equals(obj.getName(), keMuName))
                .collect(Collectors.toList());
        return kemuList;
    }


    /**
     * 同步美团发货状态和配送信息（不支持同步骑手信息）
     *
     * @return
     */
    @Override
    public void ecommerceOrderLogisticsSync(EcommerceOrderParam param) {
        Long subId = param.getSubId();
        //判断空
        if (CommonUtil.isNullOrZero(subId)) {
            return;
        }
        //根据单号获取对应的商户code
        Order order = baseMapper.selectOne(new LambdaQueryWrapper<Order>().eq(Order::getSubId, subId));
        if(order == null){
            return;
        }
        //根据商户编码获取对应的 AppId  AppSecret
        Tenant oneTenantBy = tenantService.getOneTenantBy(order.getTenantCode(), order.getPlatCode());
        //抖音小时达处理
        if (oneTenantBy == null) {
            log.error(StrUtil.format("同步{}发货状态和配送信息异常，未查询到配置信息，订单id：{}", PlatfromEnum.getCodeByName(order.getPlatCode()), subId));
            return;
        }
        //抖音小时达发货
        if (Objects.equals(ThirdPlatformCommonConst.THIRD_PLAT_DY, oneTenantBy.getPlatCode()) && Objects.equals(AppTypeEnum.MALL_HOURS.getCode(),oneTenantBy.getAppType())) {
            SpringUtil.getBean(DouyinBizService.class).logisticsAdd(order.getOrderId());
            return;
        }
        //淘宝小时达
        if (Objects.equals(PlatfromEnum.TB.name(), oneTenantBy.getPlatCode())) {
            //打包出库
            TaoBaoWorkCallbackBO taoBaoWorkCallbackBO = new TaoBaoWorkCallbackBO();
            taoBaoWorkCallbackBO.setOrderId(order.getOrderId());
            taoBaoWorkCallbackBO.setWorkCallbackStatus(TaobaoWorkCallbackStatusEnum.PACKAGED.getCode());
            SpringUtil.getBean(TaoBaoBizService.class).taobaoWorkCallback(taoBaoWorkCallbackBO);
            //开始配送
            taoBaoWorkCallbackBO.setWorkCallbackStatus(TaobaoWorkCallbackStatusEnum.SHIPPING.getCode());
            R<String> workCallback = SpringUtil.getBean(TaoBaoBizService.class).taobaoWorkCallback(taoBaoWorkCallbackBO);
            if (!workCallback.isSuccess() && Objects.equals(ResultCodeEnum.RETURN_ERROR.getCode(), workCallback.getCode())) {
                RRExceptionHandler.logError("调用淘宝小时达开始配送回传失败," + workCallback.getMsg(), taoBaoWorkCallbackBO, null, errorMsg -> {
                    SpringUtil.getBean(SmsService.class).sendOaMsgTo9JiMan(errorMsg);
                });
            }
            return;
        }
        if (oneTenantBy != null) {
            meiTuanAppId = oneTenantBy.getTenantCode();
            meiTuanAppSecret = oneTenantBy.getAppSecret();
        }
        SystemParam systemParam = new SystemParam(meiTuanAppId, meiTuanAppSecret);
        OrderDeliveringRequest request = new OrderDeliveringRequest(systemParam);
        SgOpenResponse sgOpenResponse = null;
        request.setOrder_id(order.getOrderId());
        try {
            sgOpenResponse = request.doRequest();
        } catch (Exception e) {
            log.error("同步美团发货状态和配送信息异常！", e);
            return;
        }
        //发起请求时的sig，用来联系美团员工排查问题时使用
        String requestSig = sgOpenResponse.getRequestSig();
        //请求返回的结果，按照官网的接口文档自行解析即可
        String requestResult = sgOpenResponse.getRequestResult();
        if (StringUtils.isBlank(requestResult)) {
            log.error("调用美团库存同步接口异常：requestResult为空");
        }
        JSONObject rrObj = JSON.parseObject(requestResult);
        rrObj.fluentPut("msg", JSON.parseArray(rrObj.getString("msg")));
        StockRes res = JsonUtils.fromJson(rrObj.toJSONString(), StockRes.class);
        //失败记录相关信息
        if (!StringUtils.equals(StockRes.RESULT_OK, res.getData())) {
            log.warn("调用发货状态和配送信息requestSig：{}", requestSig);
            log.warn("调用发货状态和配送信息接口返回结果：{}", requestResult);
        }
    }

    @Override
    @AddOrderLog(type = AddLogKind.ORDER_LOGISTICS_SYNCHRONIZATION)
    public R<Boolean> orderLogisticsSync(LogisticsSync logisticsSync) {
        //判断空
        if (CommonUtil.isNullOrZero(logisticsSync.getSubId())) {
            return R.error("订单参数不能为空！");
        }
        Order order;
        Tenant oneTenantBy;
        String orderJsonString = stringRedisTemplate.opsForValue().get("orderLogisticsSync_" + logisticsSync.getSubId());
        String tenantJsonString = stringRedisTemplate.opsForValue().get("tenantLogisticsSync_" + logisticsSync.getSubId());
        if (StringUtils.isNotBlank(orderJsonString) && StringUtils.isNotBlank(tenantJsonString)) {
            order = JSON.parseObject(orderJsonString, Order.class);
            oneTenantBy = JSON.parseObject(tenantJsonString, Tenant.class);
        }else {
            //查询美团订单号
            order = baseMapper.selectOne(new LambdaQueryWrapper<Order>().eq(Order::getSubId, logisticsSync.getSubId()));
            //兼容拆单情况
            if (order == null) {
                Sub sub = subService.getSub(Convert.toInt(logisticsSync.getSubId()));
                if (sub != null && sub.getSubPID() != null) {
                    order = baseMapper.selectOne(new LambdaQueryWrapper<Order>().eq(Order::getSubId, sub.getSubPID()));
                }
            }
            if (order == null || StrUtil.isEmpty(order.getOrderId())){
                return R.error("平台订单无法查询，请检查数据是否正确！");
            }
            //根据商户编码获取对应的 AppId  AppSecret
            oneTenantBy = tenantService.getOneTenantBy(order.getTenantCode(), order.getPlatCode());
            //缓存订单实体一小时
            stringRedisTemplate.opsForValue().set(StrUtil.format("orderLogisticsSync_{}", logisticsSync.getSubId()), JSON.toJSONString(order),
                    NumberConstant.ONE, TimeUnit.HOURS);
            //缓存商户实体一小时
            stringRedisTemplate.opsForValue().set(StrUtil.format("tenantLogisticsSync_{}", logisticsSync.getSubId()), JSON.toJSONString(oneTenantBy),
                    NumberConstant.ONE, TimeUnit.HOURS);
        }

        //抖音小时达
        if (Objects.equals(ThirdPlatformCommonConst.THIRD_PLAT_DY, order.getPlatCode())) {
            if (Objects.nonNull(oneTenantBy) && Objects.equals(AppTypeEnum.MALL_HOURS.getCode(),oneTenantBy.getAppType())) {
                //抖音小时达邮寄订单
                Sub sub = subService.getSub(Convert.toInt(order.getSubId()));
                if (Objects.nonNull(sub) && Objects.equals(ThirdPlatformCommonConst.DELIVERY_TYPE_EXPRESS_TRANSPORT, sub.getDelivery())) {
                    return R.success("同步成功！");
                }

                MyAccessToken myAccessToken = SpringUtil.getBean(DoudianFactory.class).getMyAccessToken(Convert.toLong(oneTenantBy.getTenantCode(),0L));
                DefaultDouDianService douDianService = SpringUtil.getBean(DefaultDouDianService.class);
                InstantShoppingReportRiderLocationParam param = orderMapstruct.toInstantShoppingReportRiderLocationParam(logisticsSync);
                param.setShopOrderId(order.getOrderId());
                param.setReportTime(logisticsSync.getReportTime());
                R<InstantShoppingReportRiderLocationData> dataR = douDianService.reportRiderLocation(myAccessToken, param);
                if (dataR.isSuccess()) {
                    //已送达
                    if (Objects.equals(40, logisticsSync.getLogisticsStatus())) {
                        InstantShoppingNotifyDeliveryStatusParam deliveryStatusParam = orderMapstruct.toInstantShoppingNotifyDeliveryStatusParam(logisticsSync);
                        deliveryStatusParam.setShopOrderId(order.getOrderId());
                        deliveryStatusParam.setUpdateTime(DateUtil.currentSeconds());
                        deliveryStatusParam.setReportTime(logisticsSync.getReportTime());
                        douDianService.notifyDeliveryStatus(myAccessToken, deliveryStatusParam);
                    }
                    return R.success("同步成功！");
                } else if(Objects.equals(50002, dataR.getCode())) {
                    //查询不到运力订单，重新发货
                    R<String> logisticsR = SpringUtil.getBean(DouyinBizService.class).logisticsAdd(order.getOrderId());
                    if (logisticsR.isSuccess()) {
                        douDianService.reportRiderLocation(myAccessToken, param);
                    }
                    return R.error(dataR.getUserMsg());
                } else {
                    return R.error(dataR.getUserMsg());
                }
            }
            return R.success("同步成功！");
        }
        //淘宝小时达
        if (Objects.equals(ThirdPlatformCommonConst.THIRD_PLAT_TB, order.getPlatCode())) {
            R<String> logisticsTraceCallback = SpringUtil.getBean(TaoBaoBizService.class).logisticsTraceCallback(logisticsSync, order, oneTenantBy);
            if (logisticsTraceCallback.isSuccess()) {
                return R.success("同步成功！");
            } else {
                return R.error(logisticsTraceCallback.getUserMsg());
            }
        }

        if (oneTenantBy != null) {
            meiTuanAppId = oneTenantBy.getTenantCode();
            meiTuanAppSecret = oneTenantBy.getAppSecret();
        }
        SystemParam systemParam = new SystemParam(meiTuanAppId, meiTuanAppSecret);
        EcommerceOrderLogisticsSyncRequest request = new EcommerceOrderLogisticsSyncRequest(systemParam);
        request.setOrder_id(order.getOrderId());
        request.setCourier_name(logisticsSync.getCourierName());
        request.setCourier_phone(logisticsSync.getCourierPhone());
        request.setLogistics_provider_code(logisticsSync.getLogisticsProviderCode());
        request.setLogistics_status(logisticsSync.getLogisticsStatus());
        request.setLatitude(logisticsSync.getLatitude());
        request.setLongitude(logisticsSync.getLongitude());
        request.setThird_carrier_order_id(Optional.ofNullable(logisticsSync.getThirdCarrierOrderId()).orElse(String.valueOf(NumberConstant.TWENTY)));
        SgOpenResponse sgOpenResponse;
        try {
            sgOpenResponse = request.doRequest();
        } catch (SgOpenException e) {
            log.error("同步美团发货状态和配送信息异常！", e);
            return R.error(ERROR_CODE,"配送信息同步失败！美团接口调用异常！");
        } catch (Exception e) {
            log.error("同步美团发货状态和配送信息异常！", e);
            return R.error(ERROR_CODE,"配送信息同步失败！本地接口调用异常！");
        }
        //发起请求时的sig，用来联系美团员工排查问题时使用
        String requestSig = sgOpenResponse.getRequestSig();
        //请求返回的结果，按照官网的接口文档自行解析即可
        String requestResult = sgOpenResponse.getRequestResult();
        if (StringUtils.isBlank(requestResult)) {
            log.error("调用美团库存同步接口异常：requestResult为空");
        }
        JSONObject rrObj = JSON.parseObject(requestResult);
        rrObj.fluentPut("msg", JSON.parseArray(rrObj.getString("msg")));
        StockRes res = JSON.parseObject(rrObj.toJSONString(), StockRes.class);
        //失败记录相关信息
        if (!StringUtils.equals(StockRes.RESULT_OK, res.getData())) {
            log.warn("调用发货状态和配送信息requestSig：{}", requestSig);
            log.warn("调用发货状态和配送信息接口返回结果：{}", requestResult);
            log.warn("调用发货状态和配送信息请求参数：{}", JSON.toJSONString(request));
            return R.error(res.getError().getMsg());
        }
        return R.success("同步成功！");
    }

    @Override
    public List<Order> getListOrderById(String orderId) {
        if (StrUtil.isEmpty(orderId)) {
            return new ArrayList<>();
        }
        return baseMapper.getListOrderById(orderId);
    }

    @Override
    public String summitOaOrderManual(OrderSearchBO search) {
        StringBuilder resultString = new StringBuilder();
        String platCode = search.getPlatCode();

        if (StringUtils.isNotEmpty(search.getSearchValue())) {
            String[] orderList = search.getSearchValue().split(StringPool.COMMA);

            for (String order : orderList) {
                if (ThirdPlatformCommonConst.THIRD_PLAT_MT.equals(platCode)) {

                    SelectMeiTuanOrderDetailBO detailBO = new SelectMeiTuanOrderDetailBO();
                    detailBO.setOrderId(order);
                    SgOpenResponse meiTuanSgOpenResponse;
                    try {
                        //根据商户编码获取对应的 AppId  AppSecret
                        Tenant oneTenantBy = Optional.ofNullable(tenantService.getOneTenantBy(search.getTenantCode(), ThirdPlatformCommonConst.THIRD_PLAT_MT))
                                .orElseThrow(() -> new CustomizeException("美团数据库相关查询为空"));
                        String meiTuanAppId = Optional.ofNullable(oneTenantBy.getTenantCode()).orElseThrow(() -> new CustomizeException("美团商户编码为空"));
                        String meiTuanAppSecret = Optional.ofNullable(oneTenantBy.getAppSecret()).orElseThrow(() -> new CustomizeException("美团appSecret为空"));
                        SystemParam systemParam = new SystemParam(meiTuanAppId, meiTuanAppSecret);
                        OrderGetOrderDetailRequest orderGetOrderDetailRequest = new OrderGetOrderDetailRequest(systemParam);
                        orderGetOrderDetailRequest.setOrder_id(detailBO.getOrderId());
                        orderGetOrderDetailRequest.setIs_mt_logistics(detailBO.getIsMtLogistics());
                        meiTuanSgOpenResponse = orderGetOrderDetailRequest.doRequest();;
                    } catch (Exception e) {
                        log.error("美团订单详情查询",e);
                        resultString.append(order).append("找不到可一键建单的订单;");
                        continue;
                    }
                    String requestResult = Optional.ofNullable(meiTuanSgOpenResponse.getRequestResult()).orElseThrow(() -> new CustomizeException("查询美团订单详情为空"));
                    JSONObject result = JSON.parseObject(requestResult);
                    if("ng".equals(result.get("data"))){
                        resultString.append(order).append(JSON.toJSONString(result.get("error")));
                        continue;
                    }
                    JSONObject data = (JSONObject) result.get("data");
                    Map<String, Object> params = new HashMap();
                    for (Map.Entry<String, Object> entry : data.entrySet()) {
                        Object value = entry.getValue();
                        params.put(entry.getKey(), OrderService.toJSON(value, 100));
                    }
                    params.put("app_id", search.getTenantCode());
                    params.put("timestamp", System.currentTimeMillis() / ThirdPlatformCommonConst.MIN_MILL);
                    R meituanOrder = null;
                    try {
                        meituanOrder = orderService.createMeituanOrder(params);
                    } catch (Exception e) {
                        log.warn("建单发生异常, 单号:{}", order, e);
                       meituanOrder = R.error(e.getMessage());
                    }
                    resultString.append(order).append(meituanOrder.getMsg()).append(meituanOrder.getUserMsg()).append(";");
                } else if (ThirdPlatformCommonConst.THIRD_PLAT_DY.equals(platCode)) {
                    continue;
                }
            }
        }

        if(ThirdPlatformCommonConst.THIRD_PLAT_MT.equals(platCode)){
            //美团已经处理, 可以直接返回
            return resultString.toString();
        }

        //未生成oa订单的平台号进行建单
        search.setGenerateManualSubFlag(0);
        Page<OrderVO> orderVOPage = this.listByPage(search);
        List<OrderVO> records = orderVOPage.getRecords();

        if (CollectionUtils.isEmpty(records)) {
            return resultString.toString();
        }
        int successNum = 0;
        int failNum = 0;
        //抖音新品、美团新品建单
        for (OrderVO order : records) {
            OrderExtendVO orderExtendVO = new OrderExtendVO();
            orderExtendVO.setPickType(order.getPickType());
            Integer id = order.getId();
            boolean success = submitOaOrder(id, order.getEstimateArrivalTime(), orderExtendVO);
            if (success) {
                successNum++;
                AtomicReference<Order> temp = new AtomicReference<>(new Order());
                MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE,
                        ()->{
                            this.lambdaUpdate().eq(Order::getId, id).set(Order::getGenerateManualSubFlag, 1).update();
                            //修改为主库查询
                            temp.set(this.getById(id));
                        } ).commit();
                //添加日志
                SubLogsNewReq log = new SubLogsNewReq();
                String comment = "一键建单，订单类型：";
                if (ThirdPlatformCommonConst.THIRD_PLAT_MT.equals(platCode)) {
                    comment += "美团订单";
                } else if (ThirdPlatformCommonConst.THIRD_PLAT_DY.equals(platCode)) {
                    comment += "抖音订单";
                }
                log.setComment(comment);
                log.setDTime(LocalDateTime.now());
                log.setSubId(Convert.toInt(temp.get().getSubId()));
                log.setInUser("系统");
                log.setType(1);
                log.setShowType(true);
                CompletableFuture.runAsync(() -> subLogsCloud.addSubLog(log));
            } else {
                failNum++;
            }
        }

        //抖音良品建单
        search.setType(NumberConstant.ONE);
        Page<OrderVO> recoverVOPage = this.listByPage(search);
        List<OrderVO> recoverRecords = recoverVOPage.getRecords();
        for (OrderVO order : recoverRecords) {
            Integer id = order.getId();
            boolean success = submitOaOrderByRecover(id, order.getEstimateArrivalTime());
            if (success) {
                successNum++;
                this.lambdaUpdate().eq(Order::getId, id).set(Order::getGenerateManualSubFlag, 1).update();
                Order temp = this.getById(id);
                //添加日志
                SubLogsNewReq log = new SubLogsNewReq();
                log.setComment("一键建单，订单类型：抖音订单");
                log.setDTime(LocalDateTime.now());
                log.setSubId(Convert.toInt(temp.getSubId()));
                log.setInUser("系统");
                log.setType(1);
                log.setShowType(true);
                CompletableFuture.runAsync(() -> subLogsCloud.addLpSubLog(log));
            } else {
                failNum++;
            }
        }
        resultString.append(";建单成功").append(successNum).append("个");
        if (failNum != 0) {
            resultString.append(",失败").append(failNum).append("个");
        }
        return resultString.toString();
    }

    @Override
    public boolean payThirdPlatformOrder(ThirdPlatformOrderPayReq req) {
        AreaInfo localArea = areaInfoService.getAreaInfoById(req.getAreaId());
        if (Objects.isNull(localArea)) {
            SpringContextUtil.addRequestErrorMsg("门店id({})未查询到门店信息，请检查门店id是否正确", req.getAreaId());
            return false;
        }
        //获取用户信息
        BbsxpUsers bbsxpUsers =
                bbsxpUsersService.getInfoByUserId(new LambdaUpdateWrapper<BbsxpUsers>().eq(BbsxpUsers::getId,
                        req.getUserId()));
        if (Objects.isNull(bbsxpUsers) || StringUtils.isEmpty(bbsxpUsers.getMobile())) {
            SpringContextUtil.addRequestErrorMsg("用户信息异常【用户id{}】", req.getUserId());
            return false;
        }

        List<SysConfigVo> kemuList = getSysConfigVos(req.getKeMuName(), localArea.getId(), localArea.getAuthorizeId());
        if (CollectionUtils.isEmpty(kemuList)) {
            String message = String.format("%s名称配置失效", req.getKeMuName());
            SpringContextUtil.addRequestErrorMsg(message);
            return false;
        } else {
            Map<String, Object> pay = buildPayParam(kemuList.get(0), req.getSubId(), req.getUserId(), Convert.toDouble(req.getPayAmount()), localArea.getKind1(), localArea.getId() ,req.getOrderId(), 0);
            R<String> conf = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST);
            if (null == conf || StringUtils.isBlank(conf.getData())) {
                log.error("获取inwcf前缀失败:{}", conf);
                SpringContextUtil.addRequestErrorMsg("获取inwcf接口地址失败");
                return false;
            }
            String prefix = conf.getData();
            String payUrl = prefix + ThirdPlatformCommonConst.OA_API_SUB_PAY;
            /*//判断支付的是新机还是良品
            if (Objects.equals(type, NumberConstant.ONE)) {
                //判断是良品订单支付
                payUrl = prefix + ThirdPlatformCommonConst.OA_API_SUBMIT_ORDER_SUBPAY;
            }*/
            Map<String, Object> payParam = new HashMap<>(2);
            payParam.put("djjNum", req.getOrderId());
            payParam.put("pay", pay);
            log.warn("调用订单支付接口{},参数，{}",payUrl,JSON.toJSONString(payParam));
            ResponseEntity<R> payResponseEntityR = restTemplate.postForEntity(payUrl, payParam, R.class);
            String payRes = JSON.toJSONString(payResponseEntityR);
            log.warn("调用订单支付接口响应，{}", payRes);
            if (Objects.nonNull(payResponseEntityR.getBody()) && payResponseEntityR.getBody().getCode() == 0) {
                return true;
            } else {
                String message = Objects.nonNull(payResponseEntityR.getBody()) ? payResponseEntityR.getBody().getMsg() : "调用订单支付接口异常，请稍后再试";
                SpringContextUtil.addRequestErrorMsg(message);
                return false;
            }
        }
    }

    /**
     * 优惠码使用
     *
     * @param req
     * @return
     */
    @Override
    public R youhuimaUse(YouhuimaUseReq req) {
        Tenant tenant = tenantService.getOneTenantBy(req.getTenantCode(), req.getPlatCode());
        Order order = baseMapper.selectOne(new LambdaQueryWrapper<Order>().eq(Order::getOrderId, req.getOrderId()).eq(Order::getPlatCode, req.getPlatCode()));
        if (Objects.isNull(order)) {
            //return false;
        }
        R<String> conf = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST);
        if (null == conf || StringUtils.isBlank(conf.getData())) {
            log.error("获取inwcf前缀失败:{}", conf);
            //return false;
        }
        String prefix = conf.getData();
        String youhuimaUseUrl = prefix + ThirdPlatformCommonConst.OA_API_YOUHUIMA_USE;
        Map<String, Object> payParam = new HashMap<>();
        payParam.put("sub_id", order.getSubId());
        payParam.put("ma", req.getYouhuima());
        payParam.put("client", 2);
        log.warn("调用优惠码使用接口{},参数，{}",youhuimaUseUrl,JSON.toJSONString(payParam));
        ResponseEntity<R> payResponseEntityR = restTemplate.postForEntity(youhuimaUseUrl, payParam, R.class);
        String payRes = JSON.toJSONString(payResponseEntityR);
        log.warn("调用优惠码使用接口响应，{}", payRes);
        return payResponseEntityR.getBody();
    }

    /**
     * 创建第三方订单
     *
     * @param req
     * @return
     */
    @Override
    public R<ThirdPlatformOrderRes> createThirdPlatformOrder(ThirdPlatformOrderReq req) {
        Tenant tenant = tenantService.getOneTenantBy(req.getTenantCode(), req.getPlatCode());
        String platCode = req.getPlatCode();
        String platformName = PlatfromEnum.getMessageByName(platCode);
        if (Objects.isNull(tenant)) {
            log.error("{}创建订单校验：商户信息配置不存在或未启用param={}", platformName, req);
            MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.ORDER.getMessage(), platformName+"创建订单校验：商户信息配置不存在或未启用",
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.MT.getCode(),"无法获取");
            meituanJdWorkLogService.save(structure);
            return R.error("商户信息配置不存在或未启用");
        }
        Long orderId = Convert.toLong(req.getOrderId());
        if (getOrderAutoId(req.getPlatCode(), orderId, qw -> qw.isNotNull(Order::getSubId)) != null) {
            log.warn("{}创建订单校验：订单已存在，不能重复创建【{}】", platformName, orderId);
            MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.ORDER.getMessage(), MessageFormat.format("美团闪购创建订单校验：订单已存在，不能重复创建【{0,number,#}】", orderId) ,
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.MT.getCode(),tenant.getTenantCode());
            meituanJdWorkLogService.save(structure);
            return R.error("订单已存在，不能重复创建");
        }
        String storeCode = req.getShopId();
        //参数转换
        Order order = orderMapstruct.toOrder(req);
        //查询门店
        Store store = storeService.getOneStoreByStoreCode(platCode, storeCode);
        if (null == store) {
            log.error("门店({})不存在或未启用", storeCode);
            SpringContextUtil.addRequestErrorMsg("门店({})不存在或未启用", storeCode);
            return R.error(String.join(StringPool.SLASH, SpringContextUtil.getRequestErrorMsg()));
        }
        order.setAreaId(store.getAreaId());
        //参数校验
        //开启写库
        AtomicReference<Boolean> isSubmitOaOrder = new AtomicReference<>(Boolean.FALSE);
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () ->{
            //没有创建美团订单才插入
            Integer orderAutoId = getOrderAutoId(platCode, orderId, null);
            if(orderAutoId == null){
                save(order);
            }else{
                order.setId(orderAutoId);
            }
            OrderReq orderReq = new OrderReq();
            List<OrderProduct> orderProductList = req.getOrderItemList().stream().map(orderMapstruct::toOrderProduct).collect(Collectors.toList());
            orderReq.setDetail(orderProductList);
            List<OrderItem> itemList = buildOrderItemParam(order, orderReq);
            boolean isItemNotEmpty = CollectionUtils.isNotEmpty(itemList);
            if(isItemNotEmpty){
                isSubmitOaOrder.set(Boolean.TRUE);
            }else{
                log.warn("{}订单【{}】匹配不到本地商品详情信息,不进行建单", platformName,orderId);
            }
            //详情没有创建才进行插入
            if (isItemNotEmpty && orderItemService.lambdaQuery().eq(OrderItem::getOrderId,Convert.toStr(orderId)).count()<=0) {
                orderItemService.saveBatch(itemList);
            }
        }).commit();
        ThirdPlatformOrderRes res = new ThirdPlatformOrderRes();
        res.setSubId(order.getId());
        if(CollUtil.isNotEmpty(SpringContextUtil.getRequestErrorMsg())){
            return R.error(String.join(StringPool.SLASH, SpringContextUtil.getRequestErrorMsg()));
        }
        return R.success(res);
    }

    /**
     * 第三方平台创建oa订单
     *
     * @param req
     * @return
     */
    @Override
    public R<ThirdPlatformOrderRes> createThirdPlatformOaOrder(ThirdPlatformOaOrderReq req) {
        if (req.getSubType() <= OrderSubTypeEnum.WHOLESALE.getCode()) {
            return R.error("订单类型参数错误");
        }
        //获取用户信息，主要是获取到手机号
        BbsxpUsers bbsxpUsers =
                bbsxpUsersService.getInfoByUserId(new LambdaUpdateWrapper<BbsxpUsers>().eq(BbsxpUsers::getId,
                        req.getUserId()));
        if (Objects.isNull(bbsxpUsers) || StringUtils.isEmpty(bbsxpUsers.getMobile())) {
            log.error("用户信息异常【用户id{}】", req.getUserId());
            String message = StrUtil.format("用户信息异常【用户id{}】", req.getUserId());
            SpringContextUtil.addRequestErrorMsg(message);
            return R.error(message);
        }
        String userMobile = bbsxpUsers.getMobile();
        R<String> conf = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST);
        if (null == conf || StringUtils.isBlank(conf.getData())) {
            log.error("第三方平台创建oa订单，获取inwcf前缀失败:{}，参数{}", conf, req);
            String message = StrUtil.format("获取inwcf前缀失败:{}", conf);
            SpringContextUtil.addRequestErrorMsg(message);
            return R.error(message);
        }

        Map<String, Object> oaOrder = buildOaOrderParam(req.getAreaId(), req.getUserId(), req.getBuyerName(), userMobile, ThirdPlatformCommonConst.ORDER_OP_USER_INTERNET,
                req.getDelivery(), req.getSubPay(), req.getSubType(), req.getBuyerRemark(),
                req.getOrderTime(), req.getBuyerCity(), req.getBuyerAddress(), req.getEstimateArrivalTime());

        //添加日志
        List<SubLogsNewReq> logs = new ArrayList<>();
        //如果备注信息超过长度,备注到进程中
        if(StringUtils.isNotBlank(req.getBuyerRemark())) {
            SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
            String comment = req.getBuyerRemark();
            subLogsNewReq.setComment(comment);
            logs.add(subLogsNewReq);
        }
        //构建购物车对象
        if (CollectionUtils.isEmpty(req.getOrderItemList())) {
            return R.error("商品信息不能为空");
        }
        List<Integer> ppids = req.getOrderItemList().stream().map(OaOrderItemReq::getSkuId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ppids)) {
            List<Productinfo> productinfoList = productinfoService.getProductinfoByPpid(ppids);
            Map<Integer, String> productConfig = productinfoList.stream().filter(v -> Objects.nonNull(v.getPpriceid()) && StringUtils.isNotBlank(v.getConfig())).collect(Collectors.toMap(Productinfo::getPpriceid, Productinfo::getConfig, (k1, k2) -> k1));
            req.getOrderItemList().forEach(v -> {
                if (StringUtils.isBlank(v.getConfig()) && StringUtils.isNotBlank(productConfig.get(v.getSkuId()))) {
                    v.setConfig(productConfig.get(v.getSkuId()));
                }
            });
        }

        List<Map<String, Object>> baskets = req.getOrderItemList().stream().map(item -> {
            Map<String, Object> map = new HashMap<>(16);
            map.put("ppriceid", item.getSkuId());
            map.put("product_num", item.getSkuCount());
            map.put("product_price", item.getPrice());
            map.put("product_price1", item.getMemberPrice());
            map.put("product_peizhi", item.getConfig());
            map.put("ismobile", item.getIsmobile());
            map.put("seller", ThirdPlatformCommonConst.ORDER_OP_USER_INTERNET);
            map.put(INUSER,ThirdPlatformCommonConst.ORDER_OP_USER_INTERNET);
            //是否备货
            map.put("beihuo", NumberConstant.TWO);
            return map;
        }).collect(Collectors.toList());

        String prefix = conf.getData();
        String url = prefix + ThirdPlatformCommonConst.OA_API_SUBMIT_ORDER_EX;
        Map<String, Object> map = new HashMap<>(16);
        map.put("sub", oaOrder);
        map.put("baskets", baskets);
        log.warn("调用订单接口{},参数{}",url, JSON.toJSONString(map));

        ResponseEntity<String> responseEntityR = null;
        try {
            responseEntityR = restTemplate.postForEntity(url, map, String.class);
        } catch (Exception e) {
            log.error("调用OA接口异常,参数{}", map, e);
            return R.error("调用OA接口异常,请稍后再试");
        }
        log.warn("调用订单提交接口响应，{}",responseEntityR.getBody());
        ThirdPlatformOrderRes res = new ThirdPlatformOrderRes();
        if (responseEntityR.getBody() != null) {
            Result<Integer> result = JSON.parseObject(responseEntityR.getBody(), new TypeReference<Result<Integer>>() {});
            if (result.getCode() == 0) {
                log.warn("创建订单:{}成功",result.getData());
                //取订单id
                Integer subId = result.getData();
                res.setSubId(subId);
                if (CollectionUtils.isNotEmpty(logs)) {
                    logs.forEach((SubLogsNewReq log) -> {
                        log.setDTime(LocalDateTime.now());
                        log.setSubId(subId);
                        log.setInUser("系统");
                        log.setType(1);
                        log.setShowType(true);
                    });
                    CompletableFuture.runAsync(() -> subLogsCloud.addSubLogBatch(logs));
                }
            } else {
                String message = result.getMsg();
                SpringContextUtil.addRequestErrorMsg("调用OA接口[{}]异常", message);
            }
        }
        if(CollUtil.isNotEmpty(SpringContextUtil.getRequestErrorMsg())){
            return R.error(String.join(StringPool.SLASH, SpringContextUtil.getRequestErrorMsg()));
        }
        return R.success(res);
    }

    /**
     * 获取美团取消订单
     *
     * @param
     */
    @Override
    public boolean cancelThirdPlatformOrder(ThirdPlatformOrderCancelReq req) {
        log.warn("请求参数：{}", JSON.toJSONString(req));
        String platName = PlatfromEnum.getMessageByName(req.getPlatCode());
        //获取商户信息
        Tenant tenant = tenantService.getOneTenantBy(req.getTenantCode(), req.getPlatCode());
        String platformName = PlatfromEnum.getMessageByName(req.getPlatCode());
        if (Objects.isNull(tenant)) {
            log.error("{}创建订单校验：商户信息配置不存在或未启用param={}", platformName, req);
            SpringContextUtil.addRequestErrorMsg("{}商户信息配置不存在或未启用【{}】",platName);
            return false;
        }
        //获取当前订单
        Order order = baseMapper.selectOne(new LambdaQueryWrapper<Order>().eq(Order::getOrderId, req.getOrderId()));
        if (Objects.isNull(order)){
            SpringContextUtil.addRequestErrorMsg("{}取消订单获取失败【{}】",platName, req.getOrderId());
            return false;
        }

        Order upOrder = new Order();
        upOrder.setCancelCheck(NumberConstant.ONE);
        upOrder.setId(order.getId());
        upOrder.setCancelReason(getCancelReason(order.getOrderId()));
        return updateById(upOrder);
    }

    /**
     * 取消订单
     *
     * @param
     */
    @Override
    public boolean cancelThirdPlatformOaOrder(ThirdPlatformOaOrderCancelReq req) {
        log.warn("请求参数：{}", JSON.toJSONString(req));
        //提交订单到OA
        R<String> conf = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST);
        if (null == conf || StringUtils.isBlank(conf.getData())) {
            log.error("获取inwcf前缀失败【{}】", conf);
            return false;
        }
        String prefix = conf.getData();
        String url = prefix + ThirdPlatformCommonConst.OA_API_SUB_REFUND;
        //构建订单参数，调用oa订单退款接口
        Map<String, Object> map = new HashMap<>(NumberConstant.FIVE);
        map.put("subId", req.getSubId());
        map.put("comment", MessageFormat.format("订单取消操作人：{0}，订单取消原因：{1}",req.getOperatorName(), req.getCancelReason()));
        log.warn("调用订单退订接口,链接{},参数{}", url,map);
        ResponseEntity<R> payResponseEntityR = restTemplate.postForEntity(url, map, R.class);
        log.warn("调用订单退订接口,返回参数{}", JSON.toJSONString(payResponseEntityR));
        if (Objects.nonNull(payResponseEntityR.getBody()) && payResponseEntityR.getBody().getCode() == 0) {
            return true;
        } else {
            String message = Objects.nonNull(payResponseEntityR.getBody()) ? payResponseEntityR.getBody().getMsg() : "调用订单退订接口异常，请稍后再试";
            SpringContextUtil.addRequestErrorMsg(message);
            return false;
        }
    }

    /**
     * 更新subId
     *
     * @param req
     * @return
     */
    @Override
    public boolean updateSubIdById(ThirdPlatformOrderUpdateSubIdReq req) {
        Order order = this.getById(req.getId());
        if (Objects.isNull(order)) {
            SpringContextUtil.addRequestErrorMsg("单号不存在，请检查订单号{}是否正确", req.getId());
            return false;
        }
        if (!PlatfromEnum.YT.name().equals(order.getPlatCode())) {
            SpringContextUtil.addRequestErrorMsg("平台编码校验不通过，单号{}", req.getId());
            return false;
        }

        return this.lambdaUpdate()
                .set(Order::getSubId, req.getSubId())
                .set(Order::getSubMessage, req.getSubMessage())
                .eq(Order::getId, req.getId())
                .update();
    }

    @Override
    public R<Boolean> orderViewStatus(Integer subId) {
        //判断空
        if (Boolean.TRUE.equals(CommonUtil.isNullOrZero(subId))) {
            return R.error("订单参数不能为空！");
        }
        //查询美团订单号
        Order order = baseMapper.selectOne(new LambdaQueryWrapper<Order>().eq(Order::getSubId, subId));
        if (order == null || StrUtil.isEmpty(order.getOrderId())) {
            //如果没查询到单号 不进行校验 因为涉及到拆单
            return R.success(Boolean.FALSE);
        }
        //根据商户编码获取对应的 AppId  AppSecret
        Tenant oneTenantBy = tenantService.getOneTenantBy(order.getTenantCode(), order.getPlatCode());
        if (oneTenantBy != null) {
            meiTuanAppId = oneTenantBy.getTenantCode();
            meiTuanAppSecret = oneTenantBy.getAppSecret();
        }
        //抖音小时达
        if (Objects.nonNull(oneTenantBy)
                && Objects.equals(oneTenantBy.getPlatCode(), ThirdPlatformCommonConst.THIRD_PLAT_DY)
                && Objects.equals(AppTypeEnum.MALL_HOURS.getCode(), oneTenantBy.getAppType())) {
            MyAccessToken myAccessToken = SpringUtil.getBean(DoudianFactory.class).getMyAccessToken(Convert.toLong(oneTenantBy.getTenantCode(), 0L));
            //查询订单详情
            R<ShopOrderDetail> shopOrderDetailR = SpringUtil.getBean(DefaultDouDianService.class).getOrderDetail(order.getOrderId(), myAccessToken);
            if (shopOrderDetailR.isSuccess()) {
                //已取消
                return R.success(Objects.equals(4L, shopOrderDetailR.getData().getOrderStatus()));
            } else {
                //当时间超14天的时候 查询本地库记录
                if (DateUtil.between(order.getOrderTime(), DateUtil.date(), DateUnit.DAY) > 14) {
                    //1是取消
                    return R.success(Objects.equals(order.getCancelCheck(), NumberConstant.ONE));
                }
                return R.error(shopOrderDetailR.getUserMsg());
            }
        }
        //淘宝小时达
        if (Objects.nonNull(oneTenantBy)
                && Objects.equals(oneTenantBy.getPlatCode(), ThirdPlatformCommonConst.THIRD_PLAT_TB)) {
            TaoBaoService taoBaoService = SpringUtil.getBean(TaoBaoService.class);
            TaobaoToken taobaoToken = taoBaoService.getTaobaoToken(oneTenantBy.getAppKey());
            //查询订单详情
            AlibabaAelophyOrderGetRequest.OrderGetRequest requestParam = new AlibabaAelophyOrderGetRequest.OrderGetRequest();
            requestParam.setStoreId(order.getStoreCode());
            requestParam.setBizOrderId(Convert.toLong(order.getBizOrderId()));
            R<AlibabaAelophyOrderGetResponse.OrderResponse> orderDetailR = taoBaoService.getOrderDetail(requestParam, taobaoToken);
            if (orderDetailR.isSuccess()) {
                //已取消
                return R.success(Objects.equals(TaobaoOrderStatusEnum.CLOSE.getCode(), orderDetailR.getData().getOrderStatus()));
            } else {
                //当时间超14天的时候 查询本地库记录
                if (DateUtil.between(order.getOrderTime(), DateUtil.date(), DateUnit.DAY) > 14) {
                    //1是取消
                    return R.success(Objects.equals(order.getCancelCheck(), NumberConstant.ONE));
                }
                return R.error(orderDetailR.getUserMsg());
            }
        }

        boolean flag = Boolean.FALSE;
        SystemParam systemParam = new SystemParam(meiTuanAppId, meiTuanAppSecret);
        OrderViewStatusRequest request = new OrderViewStatusRequest(systemParam);
        request.setOrder_id(order.getOrderId());
        SgOpenResponse sgOpenResponse;
        try {
            sgOpenResponse = request.doRequest();
        } catch (SgOpenException e) {
            log.error("查询美团订单信息异常！", e);
            return R.error("查询美团订单信息异常！美团接口调用异常！");
        } catch (Exception e) {
            log.error("查询美团订单信息异常！", e);
            return R.error("查询美团订单信息异常！本地接口调用异常！");
        }
        //发起请求时的sig，用来联系美团员工排查问题时使用
        String requestSig = sgOpenResponse.getRequestSig();
        //请求返回的结果，按照官网的接口文档自行解析即可
        String requestResult = sgOpenResponse.getRequestResult();
        if (StringUtils.isBlank(requestResult)) {
            log.error("查询美团订单信息异常：requestResult为空");
        }
        JSONObject rrObj = JSON.parseObject(requestResult);
        rrObj.fluentPut("msg", JSON.parseArray(rrObj.getString("msg")));
        StockRes res = JSON.parseObject(rrObj.toJSONString(), StockRes.class);
        //失败记录相关信息
        if (StringUtils.equals(StockRes.RESULT_NG, res.getData())) {
            log.warn("查询美团订单信息requestSig：{}", requestSig);
            log.error("查询美团订单信息接口返回结果：{}", requestResult);
            log.error("查询美团订单信息请求参数：{}", JSON.toJSONString(request));
            return R.error(res.getError().getMsg());
        }else {
            String status = JSON.parseObject(res.getData()).getString("status");
            if (NumberUtil.isNumber(status)){
                //订单已取消 (status=9)
                flag = Objects.equals(Integer.valueOf(status), NumberConstant.NINE);
            }
        }
        //美团那边只能查询14天的订单  当时间超14天的时候 查询本地库记录
        if (DateUtil.between(order.getOrderTime(), DateUtil.date(), DateUnit.DAY) > 14) {
            //1是取消
            flag = Objects.equals(order.getCancelCheck(), NumberConstant.ONE);
        }
        return R.success(flag);
    }

    private String computeAreaCodeByLocation(String address) {
        String areaCodeByLocation = "";
        if (address.contains("@#")) {
            address = address.substring(address.indexOf("@#") + 2);
            TencentMapResultVO mapInfoUsingTencent = AtlasUtil.getMapInfoUsingTencent(address);
            if (Objects.nonNull(mapInfoUsingTencent)
                    && Objects.nonNull(mapInfoUsingTencent.getResult())
                    && Objects.nonNull(mapInfoUsingTencent.getResult().getLocation())) {
                TencentMapResultVO.Result result = mapInfoUsingTencent.getResult();
                TencentMapResultVO.Result.Location location = result.getLocation();
                areaCodeByLocation = String.valueOf(AtlasUtil.getAreaCodeByLocation(String.valueOf(location.getLat())
                        , String.valueOf(location.getLng())));
            }
        }
        return areaCodeByLocation;
    }

    @Override
    public boolean addOrderSubMessage(Integer id, CharSequence template, Object... params){

        AtomicReference<Boolean> resultRef = new AtomicReference<>(Boolean.FALSE);
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE,()-> {
            String currMsg = orderService.lambdaQuery().eq(Order::getId, id).select(Order::getSubMessage).list().stream()
                    .filter(Objects::nonNull).findFirst().map(Order::getSubMessage).orElse(StringPool.EMPTY);
            log.warn(currMsg);
            resultRef.set(orderService.lambdaUpdate().eq(Order::getId, id)
                    .set(Order::getSubMessage, CommonUtils.maxLength(String.join(StringPool.SPACE,currMsg, StrUtil.format(template, params)),300))
                    .update());
        }).commit();
        return resultRef.get();
    }



    @Override
    public OaUserBO simulateUser(AreaInfo areaInfo, String userName){
        OaUserBO user = new OaUserBO();
        user.setUserName(userName);
        user.setXTenant(areaInfo.getXtenant());
        user.setAuthorizeId(areaInfo.getAuthorizeId());
        user.setArea(areaInfo.getArea());
        user.setRank(Arrays.asList("777", "55"));
        user.setAreaId(areaInfo.getId());
        user.setUserId(Integer.MAX_VALUE);
        return user;
    }

    /**
     * 查询订单信息
     *
     * @param orderId
     * @param platCode
     */
    @Override
    public List<Order> getOrderListByOrderIdAndPlatCode(String orderId, String platCode) {
        return this.lambdaQuery().eq(Order::getOrderId, orderId).eq(Order::getPlatCode, platCode).list();
    }

    /**
     * 查询物流信息
     *
     * @param orderId
     * @param platCode
     */
    @DS(DataSourceConstants.SMALLPRO_WRITE)
    @Override
    public OrderLogisticsBO getLogisticsByOrderId(String orderId, String platCode) {
        return this.baseMapper.getLogisticsByOrderId(orderId, platCode);
    }

    /**
     * 查询物流单跑腿信息
     *
     * @param wuliuId
     */
    @Override
    public LogisticsPtInfoBO getLogisticsPtInfoByWuliuId(Integer wuliuId) {
        return this.baseMapper.getLogisticsPtInfoByWuliuId(wuliuId);
    }

    @Override
    public void scalperWarning(ScalperWarningReq req) {
        Order order = req.getOrder();
        if(order == null || CollUtil.isEmpty(req.getPpids())){
            return;
        }
        List<Integer> ppids = req.getPpids();
        if (Objects.equals(order.getPlatCode(), ThirdPlatformCommonConst.THIRD_PLAT_MT)) {
            String traceId = MDC.get(TraceIdUtil.TRACE_ID_KEY);
            CompletableFuture
                    .runAsync(() -> {
                        try {
                            MDC.put(TraceIdUtil.TRACE_ID_KEY, traceId);
                            String address = StrUtil.subAfter(order.getBuyerAddress(), "@#", false);
                            if (StrUtil.startWith(address, "到店自取")) {
                                //到店自取进行跳过
                                return;
                            }
                            //固定通知的人员
                            String apolloCh999Ids = ApolloKeys.getApolloWithMainTenant(ApolloKeys.MEITUAN_SCALPER_WARNING_NOTICE_CH999IDS, null);
                            if (StrUtil.isNotBlank(apolloCh999Ids)) {
                                //查询黄牛单信息
                                List<Integer> productIds = SpringUtil.getBean(ProductinfoViewService.class).lambdaQuery()
                                        .in(ProductinfoView::getPpriceid, ppids).select(ProductinfoView::getPpriceid, ProductinfoView::getProductId)
                                        .list().stream().map(ProductinfoView::getProductId).distinct().collect(Collectors.toList());
                                ScalperWarningBO scalperWarning = baseMapper.getScalperWarning(address, productIds, order.getPlatCode(), LocalDate.now().atStartOfDay());
                                if (Objects.nonNull(scalperWarning)) {
                                    //查询到黄牛单信息
                                    String contentFormat = "商品id：{productId}，商品名：{productName}，美团近期在{address}地址已产生{subCount}个订单【单号：{subIdWithLink}】，存在亏损风险，请查看并及时处理。";
                                    Dict param = Dict.parse(scalperWarning);
                                    param.put("address", address);
                                    //每个单号加上订单详情地址
                                    Environment env = SpringContextUtil.getContext().getEnvironment();
                                    String moaUrl = env.getProperty("jiuji.sys.moa");
                                    String subIdWithLink = StrUtil.splitTrim(scalperWarning.getSubIdListStr(), StringPool.COMMA).stream()
                                            .map(subId -> StrUtil.format("<a target='_blank' href='{}/order/editorder?SubID={}'>{}</a>", moaUrl, subId, subId))
                                            .collect(Collectors.joining(", "));
                                    param.put("subIdWithLink", subIdWithLink);
                                    smsService.sendOaMsg(StrUtil.format(contentFormat, param), null, apolloCh999Ids, OaMesTypeEnum.YCTZ);
                                }
                            }
                        }catch (Exception e){
                            RRExceptionHandler.logError("美团黄牛预警", order, e, smsService::sendOaMsgTo9JiMan);
                        }finally {
                            MDC.remove(TraceIdUtil.TRACE_ID_KEY);
                        }
                    });
        }

    }

    /**
     *   1. 包含大件,按大件逻辑推送
     *   2. 大件只有苹果, 不推送
     *   3. 大件包含其他品牌(除了苹果, 小米,一加,iQOO), 亏损>50就推送
     *   4. 大件存在(小米,一加,iQOO), 亏损>100就推送
     *   3. 只有小件,亏损就推送
     * @param data
     */
    @Override
    public void lossOrderMessagePush(LossOrderMessagePushParam data) {
        String platform = data.getPlatform();
        Long subId = data.getSubId();
        if (Objects.isNull(subId)){
            return;
        }
        List<Basket> baskets = basketService.selectBasketList(subId);
        if (CollectionUtils.isEmpty(baskets)){
            return;
        }
        BigDecimal fee = Objects.isNull(data.getFee()) ? BigDecimal.ZERO : data.getFee();
        BigDecimal lossSum = null;
        if (Objects.equals(platform, ThirdPlatformCommonConst.THIRD_PLAT_MT)) {
            //美团亏损计算方式：订单商品金额+平台补贴-佣金-商品成本（负数为亏损）
            BigDecimal lossSum1 = MultipleTransaction.query(DataSourceConstants.SMALLPRO_WRITE, () -> this.baseMapper.getLossSum(subId));
            log.info(subId + "订单亏损查询金额:" + lossSum1 + ",服务费:" + fee);
            lossSum1 = Objects.isNull(lossSum1) ? BigDecimal.ZERO : lossSum1;
            lossSum = lossSum1.subtract(fee);
        } else if (Objects.equals(platform, ThirdPlatformCommonConst.THIRD_PLAT_JD)) {
            //京东
            //亏损计算方式（安卓）：（订单商品金额+平台补贴）*0.96-商品成本 （负数为亏损）
            //亏损计算方式（苹果）：（订单商品金额+平台补贴）*0.95-成本（负数为亏损）
            //美团亏损计算方式：订单商品金额+平台补贴-佣金-商品成本（负数为亏损）
            BigDecimal lossSum1 = MultipleTransaction.query(DataSourceConstants.SMALLPRO_WRITE, () -> this.baseMapper.getLossSumJd(subId));
            log.info(subId + "订单亏损查询金额:" + lossSum1 + ",服务费:" + fee);
            lossSum1 = Objects.isNull(lossSum1) ? BigDecimal.ZERO : lossSum1;
            lossSum = lossSum1.subtract(fee);
        }
        if (Objects.isNull(lossSum)){
            return;
        }

        List<Integer> allPpriceidList = baskets.stream().map(basket1 -> Convert.toInt(basket1.getPpriceid())).collect(Collectors.toList());
        Map<Integer, Productinfo> productinfoMap = productinfoService.getProductinfoByPpid(allPpriceidList)
                .stream().collect(Collectors.toMap(Productinfo::getPpriceid, Function.identity(), (v1, v2) -> v1));
        List<Basket> notMobileList = baskets.stream().filter(x -> !Objects.equals(Boolean.TRUE,x.getIsmobile())).collect(Collectors.toList());
        List<Integer> notMilePpidList = notMobileList.stream().map(basket -> Convert.toInt(basket.getPpriceid())).collect(Collectors.toList());
        List<Basket> mobileList = baskets.stream().filter(x -> Objects.equals(Boolean.TRUE,x.getIsmobile())).collect(Collectors.toList());
        List<Integer> mobilePpidList = mobileList.stream().map(basket -> Convert.toInt(basket.getPpriceid())).collect(Collectors.toList());
        List<Productinfo> mobileSkuList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(mobilePpidList)){
            mobileSkuList = productinfoService.getProductinfoByPpid(mobilePpidList);
        }
        log.info("订单亏损实际金额:" + lossSum);
        List<Integer> lossPpidList = this.getLossPpidList(platform,lossSum,mobileSkuList,notMilePpidList);
        if (CollectionUtils.isEmpty(lossPpidList)){
            return;
        }
        String platformName = PlatfromEnum.getMessageByName(platform);

        StringBuilder sb = new StringBuilder();
        sb.append(platformName).append("订单:").append(subId).append("，");
        lossPpidList.forEach(x->{
            Productinfo productinfo = productinfoMap.get(x);
            if (Objects.nonNull(productinfo)){
                sb.append("[").append(productinfo.getProductName()).append("][").append(productinfo.getPpriceid()).append("]，");
            }
        });
        sb.append("有亏损风险，请及时检查调整");

        String apolloCh999Ids = ApolloKeys.getApolloWithMainTenant(ApolloKeys.MEITUAN_SCALPER_WARNING_NOTICE_CH999IDS, null);
        smsService.sendOaMsg(sb.toString(), null, Convert.toStr(apolloCh999Ids), OaMesTypeEnum.SYSTEM);

    }

    @Override
    public R<ThirdOrderInfoRes> getThirdOrderInfo(Integer subId, Integer subType, Integer businessType) {
        // 所有参数都不能为空
        if (subId == null || subType == null || businessType == null) {
            return R.error("参数错误, 订单号, 订单类型, 业务类型都不能为空");
        }
        ThirdOrderInfoRes orderInfoRes;
        if(SubSubTypeEnum.JD_TO_HOME.getCode().equals(subType)){
            List<JdOrder> orders = SpringUtil.getBean(JdOrderService.class).lambdaQuery().eq(JdOrder::getSubId, subId).list();
            orderInfoRes = orders.stream().map(o -> ThirdOrderInfoRes.builder().id(o.getId()).platCode(PlatfromEnum.JD.name())
                            .orderId(o.getOrderId()).earlyArrival(o.getEarlyArrival()).earliestReceiptTime(o.getEarliestReceiptTime())
                            .latestReceiptTime(o.getLatestReceiptTime())
                            .build())
                    .findFirst().orElse(null);
        }else{
            // 其他订单类型直接查询三方表
            Order.OrderTypeEnum orderType = Order.OrderTypeEnum.NEW_ORDER;
            if(BusinessTypeEnum.LP_ORDER.getCode().equals(businessType)){
                orderType = Order.OrderTypeEnum.lP_ORDER;
            }
            List<Order> orders = orderService.lambdaQuery().eq(Order::getSubId, subId).eq(Order::getType, orderType.getCode()).list();
            orderInfoRes = orders.stream().map(o -> ThirdOrderInfoRes.builder().id(o.getId()).platCode(o.getPlatCode()).orderId(o.getOrderId())
                            .earlyArrival(o.getEarlyArrival()).earliestReceiptTime(o.getEarliestReceiptTime())
                            .latestReceiptTime(o.getLatestReceiptTime())
                            .build())
                    .findFirst().orElse(null);

        }
        return R.success(orderInfoRes);
    }

    @Override
    public ThirdDeliveryMeituanOrderVO selectThirdDeliveryOrderBySub(Integer subId) {
        return this.baseMapper.selectThirdDeliveryOrderBySub(subId);
    }

    /**
     *   1. 包含大件,按大件逻辑推送
     *   2. 大件只有苹果, 不推送
     *   3. 大件包含其他品牌(除了苹果, 小米,一加,iQOO), 亏损>50就推送
     *   4. 大件存在(小米,一加,iQOO), 亏损>100就推送
     *   3. 只有小件,亏损就推送
     */
    private List<Integer> getLossPpidList(String platform,BigDecimal lossSum,List<Productinfo> mobileSkuList, List<Integer> notMilePpidList) {
        List<Integer> lossPpidList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(mobileSkuList)) {
            for (Productinfo productinfo: mobileSkuList) {
                Integer brand = productinfo.getBrandID();
                Integer cid = productinfo.getCid();
                if (lossSum.compareTo(BigDecimal.ZERO) < 0 && !Arrays.asList(2, 20, 21).contains(cid)){
                    lossPpidList.addAll(notMilePpidList);
                }
                if (brand == 1){
                    if (Objects.equals(platform, ThirdPlatformCommonConst.THIRD_PLAT_JD)) {
                        if (lossSum.compareTo(BigDecimal.valueOf(-100)) < 0) {
                            lossPpidList.add(productinfo.getPpriceid());
                        }
                    }
                } else if (brand == 9 || brand == 1154 || brand == 3131){
                    if (lossSum.compareTo(BigDecimal.valueOf(-100)) < 0){
                        lossPpidList.add(productinfo.getPpriceid());
                    }
                }else {
                    if (lossSum.compareTo(BigDecimal.valueOf(-50)) < 0){
                        lossPpidList.add(productinfo.getPpriceid());
                    }
                }
            }
        } else if (CollectionUtils.isNotEmpty(notMilePpidList)) {
            if (lossSum.compareTo(BigDecimal.ZERO) < 0){
                lossPpidList.addAll(notMilePpidList);
            }
        }
        String lossPpidWhiteListString = sysConfigClient.getValueByCode(SysConfigConstant.LOSS_PPID_WHITE_LIST).getData();
        if (StringUtils.isNotEmpty(lossPpidWhiteListString)){
            List<Integer> lossPpidWhiteList = CommonUtils.covertIdStr(lossPpidWhiteListString);
            lossPpidList.removeAll(lossPpidWhiteList);
        }
        return lossPpidList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 处理商品数量重新赋值
     * 赋值逻辑：如果是商品库存同步方式为【相乘】且【系数大于10】且【单个商品数量大于10】 那就修改为1
     *
     * @param req 订单请求
     * @param order 订单信息
     */
    private void processQuantityReassignment(OrderReq req) {
        //只有九机开放
        if (!XtenantEnum.isJiujiXtenant() || CollUtil.isEmpty(req.getDetail())) {
            return;
        }
        List<OrderProduct> detail = req.getDetail();
        if(CollUtil.isEmpty(detail)){
            return;
        }
        List<String> skuIdList = detail.stream().filter(item -> {
            //单个商品数量小于等于10 不做处理
            Integer quantity = Optional.ofNullable(item.getQuantity()).orElse(NumberConstant.ZERO);
            if (quantity <= NumberConstant.TEN) {
                return Boolean.FALSE;
            }
            return Boolean.TRUE;
        }).map(OrderProduct::getSkuId).collect(Collectors.toList());
        String appId = req.getAppId();
        if(CollUtil.isEmpty(skuIdList) || StrUtil.isEmpty(appId)){
            return;
        }
        List<ProductConfig> list = productConfigService.lambdaQuery().eq(ProductConfig::getTenantCode, appId)
                .eq(ProductConfig::getPlatCode, ThirdPlatformCommonConst.THIRD_PLAT_MT)
                .in(ProductConfig::getSkuId, skuIdList)
                .list();
        if(CollUtil.isEmpty(list)){
            return;
        }
        for (OrderProduct product : detail) {
            ProductConfig config = list.stream().filter(item -> ObjectUtil.equals(item.getSkuId(), product.getSkuId()))
                    .findFirst().orElse(null);
            if(ObjectUtil.isNotNull(config)){
                //商品库存同步方式为【相乘】且【系数大于10】
                Double syncRatio = Optional.ofNullable(config.getSyncRatio()).orElse(0D);
                if (ObjectUtil.equals(config.getSyncType(), ThirdPlatformCommonConst.SYNC_TYPE_MULTI) && syncRatio > 10.0) {
                    log.warn("美团商品数量重新赋值：美团订单={} ,skuId={}, 原数量={}, 新数量=1",req.getOrderId() ,product.getSkuId(), product.getQuantity());
                    product.setQuantity(NumberConstant.ONE);
                }
            }
        }
    }

}
