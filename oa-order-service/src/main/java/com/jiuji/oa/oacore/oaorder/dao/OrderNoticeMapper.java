package com.jiuji.oa.oacore.oaorder.dao;

import com.jiuji.oa.oacore.oaorder.bo.UndoneOrderBo;
import com.jiuji.oa.oacore.oaorder.bo.UndoneOrderNumberBO;
import com.jiuji.oa.oacore.oaorder.vo.req.OrderNoticeReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 15:26
 * @Description
 */
@Mapper
public interface OrderNoticeMapper {
    /**
     * 获取未完成订单信息
     *
     * @param inUser 操作人姓名
     * @return UndoneOrderNumberBO
     */
    UndoneOrderNumberBO getUndoneOrder(@Param("inUser") String inUser);

    /**
     * 获取未完成的转售单信息（内部购买，内部借用）
     * @param inUser
     * @return
     */
    List<UndoneOrderNumberBO> getZhuanShouUndoneOrder(@Param("inUser") String inUser);

    /**
     * 回收赎回待办 推给回收门店的所有人
     * @param areaId 回收单门店
     * @return
     */
    List<UndoneOrderNumberBO> getRecoverRedemption(@Param("areaId") Integer areaId);


    UndoneOrderNumberBO getGoldOrder(@Param("inUser") String inUser);

    /**
     * 获取代办列表数据
     * @param req
     * @return
     */
    List<UndoneOrderBo> listUndoneOrder(@Param("req") OrderNoticeReq req);

    long countUndoneOrder(@Param("req") OrderNoticeReq req);

    UndoneOrderNumberBO getPendingRecoverToArea(@Param("areaId") Integer areaId);

}
