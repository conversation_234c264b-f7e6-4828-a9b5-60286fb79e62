package com.jiuji.oa.oacore.thirdplatform.yading.util;

import cn.hutool.core.date.DateTime;
import com.jiuji.oa.oacore.common.constant.CommonConstant;
import org.apache.commons.lang.StringUtils;

import java.sql.Date;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Optional;

public class DateUtil {

    public static LocalDateTime toLocalDateTime(String time) {
        return StringUtils.isBlank(time) ? null :
                LocalDateTime.parse(time, CommonConstant.COMMON_DATE_TIME_FORMATTER);
    }

    public static LocalDateTime toLocalDateTime(String time,DateTimeFormatter formatter) {
        return StringUtils.isBlank(time) ? null :
                LocalDateTime.parse(time, formatter);
    }

    public static LocalDate toLocalDate(String time) {
        return StringUtils.isBlank(time) ? null :
                LocalDate.parse(time, CommonConstant.COMMON_DATE_FORMATTER);
    }


    public static String toLocalDateStr(String time) {
        return StringUtils.isBlank(time) ? "" :
                LocalDateTime.parse(time, CommonConstant.COMMON_DATE_TIME_FORMATTER)
                        .format(CommonConstant.DATE_FORMATTER_NO_SPACE);
    }

    public static String toDateStr(String time, DateTimeFormatter format){
        return StringUtils.isBlank(time) ? "" :
                LocalDateTime.parse(time, CommonConstant.COMMON_DATE_TIME_FORMATTER)
                        .format(format);
    }

    public static LocalDate toLocalDateWithDash(String time) {
        return StringUtils.isBlank(time) ? null :
                LocalDate.parse(time, CommonConstant.COMMON_DATE_FORMATTER);
    }


    public static String format(LocalDateTime time) {
        return time == null ? null : time.format(CommonConstant.COMMON_DATE_TIME_FORMATTER);
    }

    public static String format(LocalDateTime time,DateTimeFormatter formatter) {
        return time == null ? null : time.format(formatter);
    }

    public static String formatWithEmptyString(LocalDateTime time) {
        return time == null ? "" : time.format(CommonConstant.COMMON_DATE_TIME_FORMATTER);
    }

    public static String formatYmd(LocalDateTime time) {
        return time == null ? null : time.format(CommonConstant.DATE_FORMATTER_NO_SPACE);
    }

    public static String format(LocalDate time) {
        return time == null ? null : time.format(CommonConstant.COMMON_DATE_FORMATTER);
    }

    public static String format2Day(LocalDate time) {
        return time == null ? "" : time.format(CommonConstant.DATE_DAY);
    }

    public static String format2Year(LocalDate time) {
        return time == null ? "" : time.format(CommonConstant.DATE_YEAR);
    }

    public static String formatToDateStr(LocalDateTime time) {
        return time == null ? null : time.format(CommonConstant.COMMON_DATE_FORMATTER);
    }
    public static String formatV1(LocalDate time) {
        return time == null ? null : time.format(CommonConstant.COMMON_DATE_FORMATTER_VI);
    }
    public static LocalDateTime getDateTimeOfTimestamp(String t) {
        Instant instant = Instant.ofEpochMilli(Long.parseLong(t));
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }
    public static LocalDateTime getDateTimeOfTimestampSecond(String t) {
        Instant instant = Instant.ofEpochSecond(Long.parseLong(t));
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }


    public static long getTicksByTimestamp() {
        long ticksAtEpoch = 621355968000000000L;
        long ticksPerMillisecond = 10000;
        long time = System.currentTimeMillis();
        return (time + 28800000) * ticksPerMillisecond + ticksAtEpoch;
    }

    /**
     * 查询当前时间
     */
    public static LocalDate getNowLocalDate() {
        return LocalDate.now();
    }

    /**
     * mouths个月后的时间
     */
    public static LocalDate addMouthsToLocalDate(LocalDate start, Integer months) {
        if (null == months) {
            return start;
        }
        return start.plusMonths(months);
    }

    /**
     * days天后的时间
     */
    public static LocalDate addDaysToLocalDate(LocalDate start, Integer days) {
        if (null == days) {
            return start;
        }
        return start.plusDays(days);
    }

    /**
     * 日期文案
     *
     * @return
     */
    public static String getWelcome() {
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        String hello = "";
        if (hour >= 6 && hour < 12) {
            hello = "早上好";
        } else if (hour >= 12 && hour < 14) {
            hello = "中午好";
        } else if (hour >= 14 && hour < 17) {
            hello = "下午好";
        } else if (hour >= 17 && hour < 19) {
            hello = "傍晚好";
        } else if (hour >= 19 && hour < 24) {
            hello = "晚上好";
        } else {
            hello = "夜已深了";
        }
        return hello;
    }

    /**
     * 查询当前时间
     */
    public static LocalDateTime getNowLocalDateTime() {
        return LocalDateTime.now();
    }

    public static LocalDateTime localDateToStartOfDay(LocalDate time) {
        return Optional.ofNullable(time).orElse(LocalDate.now()).atStartOfDay();
    }


    /**
     * 获取一天结束时间
     * @param time
     * @return String
     */
    public static LocalDateTime localDateToEndOfDay(LocalDate time) {
        return DateUtils.convertEndTime(Optional.ofNullable(time).orElse(LocalDate.now()));
    }

    /**
     * 月初
     *
     * @param time    日期
     * @return String
     */
    public static LocalDateTime localDateToStartOfMonth(LocalDate time) {
        DateTime startOfMonth = cn.hutool.core.date.DateUtil.beginOfMonth(Date.valueOf(Optional.ofNullable(time).orElse(LocalDate.now())));
        LocalDate localDate = startOfMonth.toSqlDate().toLocalDate();
        return DateUtils.convertStartTime(localDate);
    }

    /**
     * 月末
     *
     * @param time    日期
     * @return String
     */
    public static LocalDateTime localDateToEndOfMonth(LocalDate time) {
        DateTime endOfMonth = cn.hutool.core.date.DateUtil.beginOfMonth(Date.valueOf(Optional.ofNullable(time).orElse(LocalDate.now())));
        LocalDate localDate = endOfMonth.toSqlDate().toLocalDate();
        return DateUtils.convertEndTime(localDate);
    }
}
