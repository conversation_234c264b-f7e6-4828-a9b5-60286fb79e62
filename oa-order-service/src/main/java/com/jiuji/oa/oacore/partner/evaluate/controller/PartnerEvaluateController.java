package com.jiuji.oa.oacore.partner.evaluate.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jiuji.cloud.org.vo.enums.RoleTermModuleEnum;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.component.UserComponent;
import com.jiuji.oa.oacore.common.util.BusinessUtil;
import com.jiuji.oa.oacore.common.util.DateTimeFormatterUtil;
import com.jiuji.oa.oacore.partner.evaluate.service.IPartnerEvaluateService;
import com.jiuji.oa.oacore.partner.evaluate.util.HttpUtils;
import com.jiuji.oa.oacore.partner.evaluate.util.UrlHandler;
import com.jiuji.oa.oacore.partner.evaluate.vo.req.AddEvaluateReq;
import com.jiuji.oa.oacore.partner.evaluate.vo.req.QueryEvaluateListReq;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021/7/19
 */
@Api(value = "PartnerEvaluateController", tags = "合作伙伴评价")
@Slf4j
@RestController
@RequestMapping("partner/evaluate")
public class PartnerEvaluateController {

    @Resource
    private IPartnerEvaluateService partnerEvaluateService;

    @Resource
    private UserComponent userComponent;

    /**
     * ======================================http转发调用九机接口==========================================
     */
    @GetMapping("/first")
    @ApiOperation(value = "对外是否首次评价", httpMethod = "GET", notes = "对外对外是否首次评价")
    @ApiResponse(code = 0, message = "查询成功")
    @ApiImplicitParam(name = "messageId", value = "推送消息id", dataType = "string", required = true, paramType = "query")
    public R firstCheck2JiuJi(@RequestParam String messageId) {
        HashMap<String, String> params = new HashMap<String, String>(1) {
            private static final long serialVersionUID = 1L;

            {
                put("messageId", messageId);
            }
        };
        //String res = HttpUtils.getUrl(UrlHandler.getCurrentUrl() + "/partner/evaluate/jiuJi/first" + UrlHandler.XSERVICENAME, null, params, null);
        String res = HttpUtils.getUrl( "https://moa.dev.9ji.com/cloudapi_nc/orderservice/partner/evaluate/jiuJi/first" + UrlHandler.XSERVICENAME, null, params, null);
        return JSONObject.toJavaObject(JSON.parseObject(res), R.class);
    }

    @PostMapping("/add")
    @ApiOperation(value = "对外新增服务评价", httpMethod = "POST", notes = "对外新增服务评价")
    @ApiResponse(code = 0, message = "新增成功")
    public R save2JiuJi(@RequestBody @Valid AddEvaluateReq req) {
        String res = HttpUtils.postJson("https://moa.dev.9ji.com/cloudapi_nc/orderservice/partner/evaluate/jiuJi/add" + UrlHandler.XSERVICENAME, JSON.toJSONString(req));
       // String res = HttpUtils.postJson(UrlHandler.getCurrentUrl() + "/partner/evaluate/jiuJi/add" + UrlHandler.XSERVICENAME, JSON.toJSONString(req));
        return JSONObject.toJavaObject(JSON.parseObject(res), R.class);
    }

    @PostMapping("/list")
    @ApiOperation(value = "对外查询服务评价列表", httpMethod = "POST", notes = "对外查询服务评价列表")
    @ApiResponse(code = 0, message = "查询成功")
    public R queryList2JiuJi(@RequestBody(required = false) QueryEvaluateListReq req) {
        OaUserBO oaUserBo = userComponent.getOaUserBO(false);
        BusinessUtil.checkDataViewScope(req, oaUserBo.getToken());
        String res = HttpUtils.postJson(UrlHandler.getCurrentUrl() + "/partner/evaluate/jiuJi/list" + UrlHandler.XSERVICENAME, JSON.toJSONString(req));
        return JSONObject.toJavaObject(JSON.parseObject(res), R.class);
    }

    @GetMapping("/detail")
    @ApiOperation(value = "对外查询服务评价详情", httpMethod = "GET", notes = "对外查询服务评价详情")
    @ApiResponse(code = 0, message = "查询成功")
    @ApiImplicitParam(name = "id", value = "评价id", dataType = "int", required = true, paramType = "query")
    public R query2JiuJi(@RequestParam Integer id) {
        HashMap<String, String> params = new HashMap<String, String>(1) {
            private static final long serialVersionUID = 1L;

            {
                put("id", id.toString());
            }
        };
        String res = HttpUtils.getUrl( "https://moa.dev.9ji.com/cloudapi_nc/orderservice/partner/evaluate/jiuJi/detail" + UrlHandler.XSERVICENAME, null, params, null);
        //String res = HttpUtils.getUrl(UrlHandler.getCurrentUrl() + "/partner/evaluate/jiuJi/detail" + UrlHandler.XSERVICENAME, null, params, null);
        return JSONObject.toJavaObject(JSON.parseObject(res), R.class);
    }

    /**
     * =================================================九机接口=====================================================
     */
    @GetMapping("/jiuJi/first")
    @ApiOperation(value = "是否首次评价", httpMethod = "GET", notes = "是否首次评价")
    @ApiResponse(code = 0, message = "查询成功")
    @ApiImplicitParam(name = "messageId", value = "推送消息id", dataType = "string", required = true, paramType = "query")
    public R firstCheck(@RequestParam("messageId") String messageId) {
        return partnerEvaluateService.isFirst(messageId);
    }

    /**
     * 新增服务评价
     */
    @PostMapping("/jiuJi/add")
    @ApiOperation(value = "新增服务评价", httpMethod = "POST", notes = "新增服务评价")
    @ApiResponse(code = 0, message = "新增成功")
    public R save(@RequestBody Map req) {
        AddEvaluateReq addEvaluateReq = JSON.parseObject(JSON.toJSONString(req), AddEvaluateReq.class);
        return partnerEvaluateService.saveEvaluate(addEvaluateReq);
    }

    /**
     * 查询服务评价列表
     */
    @PostMapping("/jiuJi/list")
    @ApiOperation(value = "查询服务评价列表", httpMethod = "POST", notes = "查询服务评价列表")
    @ApiResponse(code = 0, message = "查询成功")
    public R queryList(@RequestBody(required = false) QueryEvaluateListReq req) {
        req = Optional.ofNullable(req).orElse(new QueryEvaluateListReq());
        return partnerEvaluateService.listEvaluate(req);
    }

    @GetMapping("/jiuJi/detail")
    @ApiOperation(value = "查询服务评价详情", httpMethod = "GET", notes = "查询服务评价详情")
    @ApiResponse(code = 0, message = "查询成功")
    @ApiImplicitParam(name = "id", value = "评价id", dataType = "int", required = true, paramType = "query")
    public R query(@RequestParam Integer id) {
        return partnerEvaluateService.queryDetail(id);
    }
}
