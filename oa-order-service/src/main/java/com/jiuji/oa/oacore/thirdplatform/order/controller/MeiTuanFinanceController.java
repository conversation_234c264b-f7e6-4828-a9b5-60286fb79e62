package com.jiuji.oa.oacore.thirdplatform.order.controller;


import cn.hutool.core.util.StrUtil;
import com.jiuji.oa.oacore.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.oacore.thirdplatform.order.service.MeiTuanFinanceService;
import com.jiuji.oa.oacore.thirdplatform.order.vo.meituan.MeituanDownloadBillFileReq;
import com.jiuji.oa.oacore.thirdplatform.order.vo.meituan.MeituanFinanceCurlReq;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;


/**
 * 美团财务对账
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/admin/api/meituan/finance")
public class MeiTuanFinanceController {
    @Resource
    private MeiTuanFinanceService meiTuanFinanceService;

    /**
     * 获取美团账单token
     *
     * @param req
     * @return
     */
    @PostMapping("/getHeadersByCurl/v1")
    public R getHeadersByCurl(@RequestBody @Valid MeituanFinanceCurlReq req) {
        return meiTuanFinanceService.getHeadersByCurl(req);
    }

    /**
     * 定时处理美团账单
     *
     * @return
     */
    @GetMapping("/handleMeiTuanFinanceBill/v1")
    public R<String> handleMeiTuanFinanceBill() {
        return meiTuanFinanceService.handleMeiTuanFinanceBill();
    }

    /**
     * 创建导出任务
     *
     * @return
     */
    @GetMapping("/createBillExportTask/v1")
    public R createBillExportTask(String appKey,
                                  String beginDate,
                                  String endDate) {
        if (StrUtil.isNotBlank(beginDate) && StrUtil.isNotBlank(endDate)) {
            return meiTuanFinanceService.createBillExportTask(appKey, beginDate, endDate);
        }
        return meiTuanFinanceService.createBillExportTask(appKey);
    }

    /**
     * 获取美团账单下载列表
     *
     * @return
     */
    @GetMapping("/getBillDownloadList/v1")
    public R getBillDownloadList(String appKey, String taskNo) {
        meiTuanFinanceService.getBillDownloadList(appKey,taskNo);
        return R.success("操作成功");
    }

    /**
     * 美团账单导入
     *
     * @return
     */
    @PostMapping("/importBillFile/v1")
    @ResponseBody
    @RepeatSubmitCheck(expression = "#{packageFullName}:#{methodSignName}",message = "有用户正在使用此功能导入数据，请稍后再尝试，以免冲突")
    public R<Map<String,Object>> importBillFile(MultipartFile file) {
        return R.success(meiTuanFinanceService.importBillFile(file));
    }

    /**
     * 获取美团账单下载列表
     *
     * @return
     */
    @PostMapping("/downloadBillFile/v1")
    public R downloadBillFile(@RequestBody MeituanDownloadBillFileReq req) {
        meiTuanFinanceService.downloadBillFile(req);
        return R.success("操作成功");
    }

    @PostMapping("/uploadMeituanFinance")
    public R uploadMeituanFinance(@RequestParam("file") MultipartFile file) {
        return meiTuanFinanceService.saveUploadedMeituanFinanceFile(file);
    }


}
