package com.jiuji.oa.oacore.oaorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.cloud.after.enums.ShouhouyuyueFromSourceEnum;
import com.jiuji.oa.nc.MessagePushCloud;
import com.jiuji.oa.oacore.brand.dji.vo.SalesInfoVo;
import com.jiuji.oa.oacore.brand.vo.SalesVO;
import com.jiuji.oa.oacore.common.config.properties.JiujiSystemProperties;
import com.jiuji.oa.oacore.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.oacore.common.constant.CommonConstant;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.constant.RedisKeyConstant;
import com.jiuji.oa.oacore.common.constant.RequestAttrKeys;
import com.jiuji.oa.oacore.common.enums.SubCollectTypeEnum;
import com.jiuji.oa.oacore.common.source.MoaUrlSource;
import com.jiuji.oa.oacore.common.source.UrlSource;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.common.util.EnumUtil;
import com.jiuji.oa.oacore.common.util.SpringContextUtil;
import com.jiuji.oa.oacore.goldseed.po.Zitidian;
import com.jiuji.oa.oacore.goldseed.service.ZitidianService;
import com.jiuji.oa.oacore.oaorder.bo.*;
import com.jiuji.oa.oacore.oaorder.dao.ProductinfoMapper;
import com.jiuji.oa.oacore.oaorder.dao.SmallproMapper;
import com.jiuji.oa.oacore.oaorder.dao.SubMapper;
import com.jiuji.oa.oacore.oaorder.document.SubLogsNewDocument;
import com.jiuji.oa.oacore.oaorder.enums.*;
import com.jiuji.oa.oacore.oaorder.po.Productinfo;
import com.jiuji.oa.oacore.oaorder.po.Sub;
import com.jiuji.oa.oacore.oaorder.po.SubCollection;
import com.jiuji.oa.oacore.oaorder.req.CalculateDifferenceReq;
import com.jiuji.oa.oacore.oaorder.req.CheckOrderUserReq;
import com.jiuji.oa.oacore.oaorder.res.*;
import com.jiuji.oa.oacore.oaorder.service.*;
import com.jiuji.oa.oacore.oaorder.vo.req.SubReq;
import com.jiuji.oa.oacore.oaorder.vo.res.NetworkSubCountRes;
import com.jiuji.oa.oacore.oaorder.vo.res.SubIdAndRecoverIdRes;
import com.jiuji.oa.oacore.oaorder.vo.res.UnfinishedNetworkSubRes;
import com.jiuji.oa.oacore.operator.service.OperatorBasketService;
import com.jiuji.oa.oacore.server.entity.ServiceRecord;
import com.jiuji.oa.oacore.tousu.dao.TouSuMapper;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.oacore.tousu.po.AreaInfoModel;
import com.jiuji.oa.oacore.tousu.po.Receiver;
import com.jiuji.oa.oacore.tousu.po.ReceiverCache;
import com.jiuji.oa.oacore.tousu.service.ReciversService;
import com.jiuji.oa.oacore.weborder.res.*;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import com.jiuji.tc.utils.xtenant.Namespaces;
import jodd.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * description: <Sub表服务实现类>
 * translation: <Sub table service implementation class>
 *
 * <AUTHOR>
 * @date 15:00 2019/11/11
 * @since 1.0.0
 **/
@Service
@Slf4j
public class SubServiceImpl extends ServiceImpl<SubMapper, Sub> implements SubService {

    @Resource
    private UrlSource urlSource;
    @Resource
    private MoaUrlSource moaUrlSource;
    @Autowired
    private SubMapper subMapper;
    @Autowired
    private RecoverMarketinfoService recoverMarketinfoService;
    @Autowired
    private OrderDetailService orderDetailService;
    @Autowired
    private BasketService basketService;
    @Resource
    private SmallproMapper smallproMapper;

    @Resource
    private SubLogsNewLogService subLogsNewLogService;

    @Autowired
    private MessagePushCloud messagePushCloud;

    @Autowired
    private ReciversService reciversService;
    @Autowired
    private ZitidianService zitidianService;
    @Autowired(required = false)
    private TouSuMapper touSuMapper;
    @Autowired
    private RecoverSubService recoverSubService;
    @Autowired
    private AreaInfoClient areaInfoClient;
    @Autowired
    private RecoverBasketService recoverBasketService;
    @Autowired
    private JiujiSystemProperties jiujiSystemProperties;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Resource(name = "redisTemplate4")
    private RedisTemplate<String, Object> redisTemplate4;

    @Autowired
    private SysConfigClient sysConfigClient;
    @Autowired
    private AmqpTemplate amqpTemplate;
    @Autowired
    private ProductinfoMapper productinfoMapper;

    @Resource
    private OperatorBasketService operatorBasketService;

    private static final long HOUR = (long) 60 * 60 * 1000;

    private static final long DAY = (long) 24 * 60 * 60 * 1000;

    private static final long MIN = (long) 60 * 1000;

    private static final long LONG_OVERTIME_MIN = (long) 60 * 1000 * 30;

    private static final long SUPER_LONG_OVERTIME_MIN = (long) 24 * 60 * 60 * 1000;

    private static final long SHORT_OVERTIME_MIN = (long) 60 * 1000 * 5;

    /**
     * 口罩商品PPID
     */
    private static final List<Integer> MASK_PPID_LIST = new ArrayList<>(12);

    /**
     * 恒定过滤ppid
     */
    private static final List<Integer> PPID_LIST = new ArrayList<>(3);

    static {
        /**
         * 添加默认口罩商品PPID
         */
        MASK_PPID_LIST.add(87788);
        MASK_PPID_LIST.add(88051);
        MASK_PPID_LIST.add(87998);
        MASK_PPID_LIST.add(87997);
        MASK_PPID_LIST.add(87443);
        MASK_PPID_LIST.add(87458);
        MASK_PPID_LIST.add(88147);
        MASK_PPID_LIST.add(88185);
        MASK_PPID_LIST.add(87874);
        MASK_PPID_LIST.add(88255);
        MASK_PPID_LIST.add(88289);
        MASK_PPID_LIST.add(88361);
        MASK_PPID_LIST.add(89317);
        MASK_PPID_LIST.add(89304);
        MASK_PPID_LIST.add(88361);
        MASK_PPID_LIST.add(89305);
        PPID_LIST.add(75774);
        PPID_LIST.add(75775);
        PPID_LIST.add(60175);
    }


    @DS(DataSourceConstants.SMALLPRO_WRITE)
    @Override
    public List<Sub> selectSubList(List<Long> subIdList) {
        return this.lambdaQuery().in(Sub::getSubId,subIdList).list();
    }

    /**
     * description: <获取未完成的网络订单列表>
     * translation: <Get an unfinished list of network orders>
     *
     * @param areaId   组织Id
     * @param subCheck 订单确认状态 0 未处理 1已处理
     * @return UnfinishedNetworkSubRes
     * <AUTHOR>
     * @date 18:35 2019/11/11
     * @since 1.0.0
     **/
    @Override
    public UnfinishedNetworkSubRes listUnfinishedNetworkSub(Integer areaId, Integer subCheck) {
        UnfinishedNetworkSubRes result = new UnfinishedNetworkSubRes();
        if (!isSendArea(areaId)) {
            result.setCount(0).setGoodProductCount(0).setList(new ArrayList<>(0));
            return result;
        }
        //是否为九机
        boolean isJiuji = XtenantEnum.isJiujiXtenant();
        List<NetOrderInfoBO> queryResult = subMapper.listUnfinishedNetworkSub(areaId, subCheck);
        List<NetOrderInfoBO> queryGoodProductResult = subMapper.listUnfinishedNetworkSubGoodProduct(areaId, subCheck);
        List<NetOrderInfoBO> queryYuyueResult = isJiuji ? subMapper.listUnfinishedNetworkYuYue(areaId, subCheck) : Collections.emptyList();
        List<NetOrderInfoBO> recycleResult = subMapper.listUnfinishedNetworkRecycle(areaId, subCheck);
        int count = queryResult.size();
        int goodProductCount = queryGoodProductResult.size();
        int yuyueCount = queryGoodProductResult.size();
        int recycleCount = recycleResult.size();

        // 记录订单级的结构size=销售单总数+良品单总数
        // 记录商品级的结构size=销售单查询结果总数+良品单查询结果总数
        // resultDataMap:循环时存储订单级BO,key为subId
        // resultDataDateMap:记录每一个数据的
        // resultDateDataMap:循环时用时间做key，用于最后订单级下单时间正序排序
        // resultOverTimeDataMap:循环时用超时时间做key，用于订单级超时排序
        // resultDataOverTimeMap:记录每一个数据的最长超时时间
        // resultDataOverTimeFlagMap:记录每一个数据是否超时，用于最后组装数据时避免重复
        // resultDataToSonListMap:循环式用来存储订单级下的商品级List，key为subId
        HashMap<Integer, UnfinishedNetworkSubBO> resultDataMap = new HashMap<>(count + goodProductCount+yuyueCount + recycleCount);
        HashMap<UnfinishedNetworkSubBO, Long> resultDataDateMap = new HashMap<>(count + goodProductCount+yuyueCount+recycleCount);
        HashMap<Long, UnfinishedNetworkSubBO> resultDateDataMap = new HashMap<>(count + goodProductCount+yuyueCount + recycleCount);
        HashMap<Long, UnfinishedNetworkSubBO> resultOverTimeDataMap = new HashMap<>(count + goodProductCount+yuyueCount + recycleCount);
        HashMap<UnfinishedNetworkSubBO, Long> resultDataOverTimeMap = new HashMap<>(count + goodProductCount+yuyueCount + recycleCount);
        HashMap<UnfinishedNetworkSubBO, Boolean> resultDataOverTimeFlagMap = new HashMap<>(count + goodProductCount+yuyueCount + recycleCount);
        HashMap<Integer, List<UnfinishedNetworkSubProductBO>> resultDataToSonListMap =
                new HashMap<>(count + goodProductCount+yuyueCount + recycleCount);
        // resultData:存放商品列表的最后结果List
        List<UnfinishedNetworkSubBO> resultData = new ArrayList<>(count + goodProductCount+yuyueCount + recycleCount);

        traversing(queryResult, 1, resultDataMap, resultDataDateMap,
                resultDataOverTimeMap, resultDataToSonListMap, resultDataOverTimeFlagMap, subCheck, areaId);
        count = resultDataDateMap.size() + resultDataOverTimeMap.size();
        traversing(queryGoodProductResult, 2, resultDataMap,
                resultDataDateMap, resultDataOverTimeMap, resultDataToSonListMap, resultDataOverTimeFlagMap, subCheck
                , areaId);
        goodProductCount = resultDataDateMap.size() + resultDataOverTimeMap.size() - count;
        traversing(queryYuyueResult, 3, resultDataMap,
                resultDataDateMap, resultDataOverTimeMap, resultDataToSonListMap, resultDataOverTimeFlagMap, subCheck
                , areaId);
        yuyueCount = resultDataDateMap.size() + resultDataOverTimeMap.size() - count - goodProductCount;

        traversing(recycleResult, 4, resultDataMap,
                resultDataDateMap, resultDataOverTimeMap, resultDataToSonListMap, resultDataOverTimeFlagMap, subCheck
                , areaId);
        recycleCount = resultDataDateMap.size() + resultDataOverTimeMap.size() - count - goodProductCount - yuyueCount;

        // 先根据超时时间逆序排序
        resultDataOverTimeMap.entrySet().forEach(dataOverTime -> {
            resultOverTimeDataMap.put(dataOverTime.getValue(), dataOverTime.getKey());
        });
        Set overDateSortSet = resultOverTimeDataMap.keySet();
        Object[] overTimeSortArray = overDateSortSet.toArray();
        Arrays.sort(overTimeSortArray);
        long overTime;
        for (int topi = overTimeSortArray.length - 1; topi >= 0; topi--) {
            overTime = (long) overTimeSortArray[topi];
            UnfinishedNetworkSubBO boTemp = resultOverTimeDataMap.get(overTime);
            if (subCheck == 0) {
                String overTimeString = "超时:";
                if (overTime < HOUR) {
                    // 1小时内显示分钟
                    overTimeString += (overTime / MIN) + "分钟";
                } else if (overTime > HOUR && overTime < DAY) {
                    // 24小时内显示小时
                    overTimeString += (overTime / HOUR) + "小时";
                } else if (overTime > DAY) {
                    // 超过1天显示天
                    overTimeString += (overTime / DAY) + "天";
                }
                boTemp.setOverTime(overTimeString);
            }
            resultData.add(boTemp);
        }

        // 再根据订单时间正序排序
        resultDataDateMap.entrySet().forEach(dataOverTime -> {
            resultDateDataMap.put(dataOverTime.getValue(), dataOverTime.getKey());
        });
        Set dateSortSet = resultDateDataMap.keySet();
        Object[] dateSortArry = dateSortSet.toArray();
        Arrays.sort(dateSortArry);
        for (int topi = 0; topi < dateSortArry.length; topi++) {
            if (!resultDataOverTimeFlagMap.get(resultDateDataMap.get(dateSortArry[topi]))) {
                resultData.add(resultDateDataMap.get(dateSortArry[topi]));
            }
        }

        // 对订单的商品级List排序，只存储两个
        for (Integer subId : resultDataToSonListMap.keySet()) {
            List<UnfinishedNetworkSubProductBO> tempList = resultDataToSonListMap.get(subId);
            HashMap<Integer, UnfinishedNetworkSubProductBO> tempMap = new HashMap<>(2);
            for (UnfinishedNetworkSubProductBO bo : tempList) {
                UnfinishedNetworkSubProductBO first = tempMap.get(1);
                if (first == null) {
                    tempMap.put(1, bo);
                } else if (compareProductBo(first, bo)) {
                    tempMap.put(1, bo);
                    tempMap.put(2, first);
                } else if (tempMap.get(2) == null) {
                    tempMap.put(2, bo);
                } else if (compareProductBo(tempMap.get(2), bo)) {
                    tempMap.put(2, bo);
                }
            }
            resultDataMap.get(subId).getProductList().add(tempMap.get(1));
            if (tempMap.size() > 1) {
                resultDataMap.get(subId).getProductList().add(tempMap.get(2));
            }
        }

        result.setCount(count)
                .setGoodProductCount(goodProductCount)
                .setYuyueCount(yuyueCount)
                .setRecycleCount(recycleCount)
                .setList(resultData);
        return result;
    }



    @Override
    @Cached(name = "orderservice:SubService:getNetworkSubOrderCount", expire = 10, timeUnit = TimeUnit.SECONDS, cacheType = CacheType.REMOTE)
    public NetworkSubCountRes getNetworkSubOrderCount(Integer areaId) {
        NetworkSubCountRes networkSubCountRes = new NetworkSubCountRes();

        if (!isSendArea(areaId)) {
            networkSubCountRes.setOverTimeCount(0).setCount(0);
            return networkSubCountRes;
        }

//        networkSubCountRes.setCount(baseMapper.getNetworkSubOrderCount(areaId,false).stream().mapToInt(Integer::intValue).sum());
//        networkSubCountRes.setOverTimeCount(baseMapper.getNetworkSubOrderCount(areaId,true).stream().mapToInt(Integer::intValue).sum());
        long currTimeStamp = System.currentTimeMillis();
        CompletableFuture<Integer> subOrderCountFuture = CompletableFuture.supplyAsync(() -> baseMapper.getNetworkSubOrderCountV2(areaId, false,
                currTimeStamp).stream().mapToInt(Integer::intValue).sum());
        CompletableFuture<Integer> subOrderOverTimeCountFuture = CompletableFuture.supplyAsync(() -> baseMapper.getNetworkSubOrderCountV2(areaId,true,
                currTimeStamp).stream().mapToInt(Integer::intValue).sum());
        boolean isJiuji = XtenantEnum.isJiujiXtenant();
        //预约单的待处理数量
        CompletableFuture<Integer> yuyueCountFuture = CompletableFuture.supplyAsync(() ->  isJiuji ? baseMapper.getNetworkYuyueCountCount(areaId, false,
                currTimeStamp) : 0);
        //预约单的待处理数量
        CompletableFuture<Integer> yuyueOverTimeCountFuture = CompletableFuture.supplyAsync(() -> isJiuji ? baseMapper.getNetworkYuyueCountCount(areaId, true,
                currTimeStamp) : 0);
        Integer subOrderCount = subOrderCountFuture.join();
        Integer yuyueCount = yuyueCountFuture.join();
        Integer subOrderOverTimeCount = subOrderOverTimeCountFuture.join();
        Integer yuyueOverTimeCount = yuyueOverTimeCountFuture.join();
        SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"新机单: {} 新机单超时: {} 预约单: {} 预约单超时: {}",
                subOrderCount,subOrderOverTimeCount,yuyueCount,yuyueOverTimeCount);
        networkSubCountRes.setCount(subOrderCount + yuyueCount);
        networkSubCountRes.setOverTimeCount(subOrderOverTimeCount + yuyueOverTimeCount);
        return networkSubCountRes;
    }

    @Override
    public List<CheckOrderUserRes> checkOrderUser(CheckOrderUserReq req) {
        List<CheckOrderUserRes> res = new ArrayList<>();
        List<CheckOrderUserBO> checkOrderUserBOS = baseMapper.getOrderUserComplete(req);
        Map<Long, List<CheckOrderUserBO>> collect =
                checkOrderUserBOS.stream().collect(Collectors.groupingBy(CheckOrderUserBO::getProductId));
        collect.entrySet().forEach(e -> {
            CheckOrderUserRes checkOrderUserRes = new CheckOrderUserRes();
            List<CheckOrderUserBO> value = e.getValue();
            List<Long> userIds = value.stream().map(CheckOrderUserBO::getUserId).collect(Collectors.toList());
            checkOrderUserRes.setProductId(e.getKey());
            checkOrderUserRes.setUserId(userIds);
            res.add(checkOrderUserRes);
        });
        return res;
    }


    /**
     * description: <比较两个商品对象的排序优先级，bo大于temp返回true,bo小于temp返回false>
     * translation: <Compare the sort priority of two commodity objects, bo is greater than temp and returns true, bo
     * is less than temp and returns false.>
     *
     * @param temp 被比较对象
     * @param bo   比较对象
     * @return boolean
     * <AUTHOR>
     * @date 15:57 2019/11/13
     * @since 1.0.0
     **/
    private boolean compareProductBo(UnfinishedNetworkSubProductBO temp,
                                     UnfinishedNetworkSubProductBO bo) {
        String big = "大件";
        String small = "小件";
        if (bo.getOverTimestamp() > temp.getOverTimestamp()) {
            return true;
        }
        if (small.equals(temp.getMobile()) && big.equals(bo.getMobile())) {
            return true;
        }
        if (bo.getBasketDate() < temp.getBasketDate()) {
            return true;
        }
        return false;
    }

    /**
     * 根据areaId获取printName
     *
     * @param areaId 门店id
     * @return 公司名
     */
    private String getPrintName(Integer areaId) {
        String printName = "";
        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(areaId);
        if (areaInfoR.getCode() == 0 && areaInfoR.getData() != null) {
            AreaInfo areaInfo = areaInfoR.getData();
            printName = areaInfo.getPrintName();
            if (StringUtils.isNotEmpty(printName) && printName.contains(CommonConstant.JIU_JI)) {
                printName = "";
            }
        }
        return printName;
    }

    /**
     * description: <遍历查询结果构建结果集>
     * translation: <Traverse query results to build a result set>
     *
     * @param queryResult               网单查询结果集
     * @param sourceCode                数据来源[1商品|2良品3预约4回收]
     * @param resultDataMap
     * @param resultDataDateMap
     * @param resultDataOverTimeMap
     * @param resultDataToSonListMap
     * @param resultDataOverTimeFlagMap
     * @param subCheck
     * @param areaId                    门店Id
     * @return void
     * <AUTHOR>
     * @date 10:53 2020/3/27
     * @since 1.0.0
     **/
    private void traversing(List<NetOrderInfoBO> queryResult, Integer sourceCode,
                            HashMap<Integer, UnfinishedNetworkSubBO> resultDataMap,
                            HashMap<UnfinishedNetworkSubBO, Long> resultDataDateMap,
                            HashMap<UnfinishedNetworkSubBO, Long> resultDataOverTimeMap,
                            HashMap<Integer, List<UnfinishedNetworkSubProductBO>> resultDataToSonListMap,
                            HashMap<UnfinishedNetworkSubBO, Boolean> resultDataOverTimeFlagMap,
                            Integer subCheck, Integer areaId) {
        //开始循环
        String[] timeFormat = new String[2];
        timeFormat[0] = "yyyy-MM-dd HH:mm:ss";
        timeFormat[1] = "yyyy-MM-dd";
        DateTimeFormatter dateTimeFormatterWithTime = DateTimeFormatter.ofPattern(timeFormat[0]);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(timeFormat[1]);
        Long now = System.currentTimeMillis();
        LocalDateTime nowDate = LocalDateTime.now();
        String nowDateString = nowDate.format(dateTimeFormatter);

        String printName = getPrintName(areaId);
        queryResult.forEach(temp -> {
            // 过滤口罩订单
            if (filterMaskOrder(temp.getPpriceId(), temp.getDelivery(), areaId)) {
                return;
            }
            // 订单状态
            Integer tempSubCheck = temp.getSubCheck();
            // 已确认订单需要判断是否为当天订单
            if (subCheck == 1) {
                String subDateDate = (temp.getSubDate() == null) ? "" :
                        temp.getSubDate().format(dateTimeFormatter);
                if (!subDateDate.equals(nowDateString)) {
                    return;
                }
            }
            // 录入订单级数据
            // 配送方式
            Integer delivery = temp.getDelivery();
            String deliveryMessage = getMessageByCodeAndSourceCode(SubDeliveryEnum.class, delivery,sourceCode);
            if (StringUtils.isNotEmpty(deliveryMessage) && StringUtils.isNotEmpty(printName)) {
                deliveryMessage = deliveryMessage.replace(CommonConstant.JIU_JI, printName);
            }

            // 订单类型
            Integer subType = temp.getSubType();
            String subTypeMessage = getMessageByCodeAndSourceCode(SubTypeEnum.class, subType,sourceCode);
            // String subCheckMessage = EnumUtil.getMessageByCode(SubCheckEnum.class, subCheck);
            // 订单类别
            Integer basketType = temp.getBasketType();
            String basketTypeMessage = getMessageByCodeAndSourceCode(BasketTypeEnum.class, basketType,sourceCode);
            // 订单来源
            String subSourceMessage = EnumUtil.getMessageByCode(SubSourceEnum.class, sourceCode);


            Integer subId = temp.getSubId();
            String url = "";
            if (sourceCode.equals(SubSourceEnum.SUB_SOURCE_SALE.getCode())) {
                url = jiujiSystemProperties.getMoa() + urlSource.getOrderDetail(String.valueOf(subId));
            } else if (sourceCode.equals(SubSourceEnum.SUB_SOURCE_GOOD_PRODUCT.getCode())) {
                url = jiujiSystemProperties.getMoa() + urlSource.getGoodsOrderDetail(String.valueOf(subId));
            } else if (sourceCode.equals(SubSourceEnum.SUB_SOURCE_YU_YUE.getCode())) {
                url = StrUtil.format("{}/new/#/afterService/detail/{}",jiujiSystemProperties.getMoa(),subId);
            } else if (sourceCode.equals(SubSourceEnum.SUB_SOURCE_RECYCLE.getCode())){
                url = StrUtil.format("{}/new/#/recycle/{}",jiujiSystemProperties.getMoa(),subId);
            }
            //subId不存在则新建一个订单对象
            UnfinishedNetworkSubBO resultDataTemp = resultDataMap.get(subId);
            Long subDateTime = 0L;
            subDateTime = temp.getSubDate().toInstant(ZoneOffset.of("+8")).toEpochMilli();
            if (resultDataTemp == null) {
                resultDataTemp = new UnfinishedNetworkSubBO();
                String trader = (temp.getTrader() == null) ? "" : temp.getTrader();
                String overTimeString = "";
                // 填充订单级数据到临时存储空间
                resultDataTemp.setSubId(subId)
                        .setDelivery(deliveryMessage)
                        .setSubDate(temp.getSubDate().format(dateTimeFormatterWithTime))
                        .setTrader(trader)
                        .setSubType(subTypeMessage)
                        .setSubCheck(subCheck)
                        .setSubSource(subSourceMessage)
                        .setProductList(new ArrayList<>(2))
                        .setOverTime(overTimeString)
                        .setBasketType(basketTypeMessage)
                        .setRecycleSubDelivery(RecycleSubDeliveryEnum.getMessageByCode(temp.getRecycleSubDelivery()))
                        .setUrl(url);
                resultDataMap.put(resultDataTemp.getSubId(), resultDataTemp);
                resultDataToSonListMap.put(resultDataTemp.getSubId(), new ArrayList<>(queryResult.size()));
            }

            // 录入条目级数据
            // 商品是否为大件or小件
            String mobileMessage = "";
            if (Boolean.TRUE.equals(temp.getIsMobile())) {
                mobileMessage = IsMobileEnum.IS_MOBILE_TRUE.getMessage();
            } else {
                mobileMessage = IsMobileEnum.IS_MOBILE_FALSE.getMessage();
            }
            // 商品尺寸
            String productColor = (temp.getProductColor() == null) ? "" : temp.getProductColor();
            // 商品数量
            Integer basketCount = (temp.getBasketCount() == null) ? 0 : temp.getBasketCount();
            // SKU ID
            Integer pPriceId = (temp.getPpriceId() == null) ? 0 : temp.getPpriceId();
            // 价格，用来判断是否为1元数据线
            Double price = (temp.getPrice() == null) ? 0.0 : temp.getPrice().doubleValue();

            // 条目创建时间
            Long basketDateTime = 0L;
            if (temp.getBasketCount() != null) {
                basketDateTime = temp.getBasketDate().toInstant(ZoneOffset.of("+8")).toEpochMilli();
            }
            // 条目超时计算
            long overTime = calculationTimeout(now, basketDateTime, delivery, price,sourceCode);

            // 填充数据到临时存储空间
            UnfinishedNetworkSubProductBO resultDataTempSon = new UnfinishedNetworkSubProductBO();
            resultDataTempSon.setBasketId(temp.getBasketId())
                    .setMobile(mobileMessage)
                    .setPpriceId(pPriceId)
                    .setProductName(temp.getProductName())
                    .setProductColor(productColor)
                    .setBasketCount(basketCount)
                    .setOverTimestamp((overTime > 0) ? overTime : 0)
                    .setSeller(temp.getSeller())
                    .setBasketDate(basketDateTime)
                    .setBasketDateString((temp.getBasketDate() == null) ? "" :
                            temp.getBasketDate().format(dateTimeFormatterWithTime));
            resultDataToSonListMap.get(resultDataTemp.getSubId()).add(resultDataTempSon);
            if (overTime > 0) {
                if (resultDataOverTimeMap.get(resultDataTemp) == null) {
                    resultDataOverTimeMap.put(resultDataTemp, overTime);
                } else {
                    if (resultDataOverTimeMap.get(resultDataTemp) < overTime) {
                        resultDataOverTimeMap.put(resultDataTemp, overTime);
                    }
                }
                resultDataOverTimeFlagMap.put(resultDataTemp, true);
            } else if (resultDataDateMap.get(resultDataTemp) == null || resultDataDateMap.get(resultDataTemp) < subDateTime) {
                resultDataDateMap.put(resultDataTemp, subDateTime);
                if (resultDataOverTimeFlagMap.get(resultDataTemp) == null) {
                    resultDataOverTimeFlagMap.put(resultDataTemp, false);
                }
            }
        });
    }

    /**
     *
     * @param enumClassParam
     * @param code
     * @param sourceCode 数据来源[1商品|2良品3预约]
     * @return
     */
    private <T extends CodeMessageEnumInterface> String getMessageByCodeAndSourceCode(Class<T> enumClassParam, Integer code, Integer sourceCode) {
        Class<? extends com.jiuji.tc.utils.enums.CodeMessageEnumInterface> enumClassTc = null;
        Class<? extends CodeMessageEnumInterface> enumClass = enumClassParam;
        if(Objects.equals(sourceCode,SubSourceEnum.SUB_SOURCE_YU_YUE.getCode())){
            if (SubDeliveryEnum.class.equals(enumClass)) {
                enumClassTc = YuYueSTypeEnum.class;
            }else if (SubTypeEnum.class.equals(enumClass)) {
                enumClassTc = ShouhouyuyueFromSourceEnum.class;
            }
        }
        if(enumClassTc != null){
            return com.jiuji.tc.utils.enums.EnumUtil.getMessageByCode(enumClassTc,code);
        }

        return EnumUtil.getMessageByCode(enumClass,code);
    }

    private long calculationTimeout(long now, long aimTime, Integer delivery, Double price, Integer sourceCode) {
        long overTimeLine = 0;

        if(Objects.equals(sourceCode,3)){
            //预约单超过三十分钟才算超时
            overTimeLine = LONG_OVERTIME_MIN;
        } else if (Objects.equals(sourceCode,4)){
            overTimeLine = 0L;
        } else if (delivery.equals(2)) {
            overTimeLine = SHORT_OVERTIME_MIN;
        } else if (delivery.equals(1) && price == 1.0) {
            overTimeLine = SUPER_LONG_OVERTIME_MIN;
        } else {
            overTimeLine = LONG_OVERTIME_MIN;
        }
        return now - aimTime - overTimeLine;
    }

    private boolean isSendArea(Integer areaId) {
        Map isSendAreaMap = baseMapper.isSendArea(areaId);
        if (isSendAreaMap == null || isSendAreaMap.get("areaId") == null) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * description: <过滤口罩订单>
     * translation: <Filter mask order>
     *
     * @param ppriceId 订单ppid
     * @param delivery 订单配送方式
     * @param areaId   门店Id
     * @return boolean
     * <AUTHOR>
     * @date 9:56 2020/3/27
     * @since 1.0.0
     **/
    private boolean filterMaskOrder(Integer ppriceId, Integer delivery, Integer areaId) {
        // delivery=2/5不过滤
        // delivery=4 除开DC其他不过滤 areaId=16
        boolean filterFlag = false;
        if (PPID_LIST.contains(ppriceId)) {
            filterFlag = true;
            return filterFlag;
        }
        if (MASK_PPID_LIST.contains(ppriceId)) {
            switch (delivery) {
                case 1:
                case 3:
                case 6:
                    filterFlag = true;
                    break;
                case 2:
                case 5:
                case 4:
                    break;
                default:
                    break;
            }
        }
        return filterFlag;
    }

    @Override
    public void addRemind(long subId, String mobile, String content) {
        boolean isTrue = false;
        int isRemind = subMapper.getIsRemind(subId, content);
        List<Long> subIds = new ArrayList<>();
        if (isRemind == 0) {
            isTrue = subMapper.addRemind(subId, content, mobile) > 0 ? true : false;
        }
        if (isTrue) {
            HashMap<String, String> info = subMapper.getInfo(subId);
            String userName = info.get("UserName");
            String seller = info.get("seller");
            String msg =
                    "客户催单->会员名称：" + userName + " 单号：" + "<a href='https://moa.9ji.com/order/editorder?SubID=" + subId + "'>" + subId + "</a>" + " 电话：" + mobile + " 时间：" +
                            DateTime.now().toString("yyyy年MM月dd日 HH:mm") + "销售员：" + seller + " 内容：" + content;
            SubLogsNewDocument log = new SubLogsNewDocument();
            log.setId(subId);

            List<SubLogsNewDocument.Conts> conts = new ArrayList<>();
            SubLogsNewDocument.Conts cont = new SubLogsNewDocument.Conts();

            cont.setComment("客户催单->内容：" + content);
            cont.setShowType(true);
            cont.setInUser("系统");
            cont.setType(1);
            conts.add(cont);
            log.setConts(conts);

            subLogsNewLogService.saveSubLogsNewLog(log);
            List<Receiver> smsReceiverList = touSuMapper.smsReceiverList("2");
            List<String> departId = touSuMapper.getDepartIdList(smsReceiverList.get(0).getAreaids());
            List<AreaInfoModel> areaList = touSuMapper.loadAllAreaInfo();
            List<ReceiverCache> receiverCaches = reciversService.GetReceiverIds(smsReceiverList, "2", 0, departId,
                    smsReceiverList.get(0).getAreaids(), areaList);
            List<Integer> receiverIds =
                    receiverCaches.stream().map(ReceiverCache::getCh999_id).collect(Collectors.toList());
            List<Integer> userIds;
            if (receiverIds.size() > 0) {
                userIds = subMapper.getUserIds(receiverIds, subId);
                messagePushCloud.pushOaMessagePost(userIds.toString(), msg, "https://moa.9ji" +
                        ".com/order/editorder?SubID=" + subId);
                subIds.add(subId);
                List<SubCollection> subCollentUser = getSubCollentUser(subIds,
                        SubCollectTypeEnum.SUB_COLLECT_TYPE_TYPE_1.getCode());
                if (userIds.size() > 0) {
                    for (SubCollection s : subCollentUser) {
                        int ch999Id = s.getCh999Id();
                        userIds.remove(ch999Id);
                    }
                }
                if (userIds.size() > 0) {
                    msg = "亲爱的九机人，您关注的订单【" + "<a href='" + moaUrlSource.getBasicUrl() + "/order/editorder?SubID=" + subId + "'>" + subId + "</a>" + "】" + msg;
                    messagePushCloud.pushOaMessagePost(userIds.toString(), msg, moaUrlSource.getBasicUrl() + "/order" +
                            "/editorder?SubID=" + subId);
                }
            }
        }
    }

    @Override
    public MyOrderNewRes getMyOrderNew(String userId, int curPage, int rows, int type, String[] orderArr,
                                       String isweixiu, boolean recover, boolean smallShop, long xtenant) {
        MyOrderNewRes info = new MyOrderNewRes();
        // 容灾处理
        if (rows > 10) {
            info.setResult(new MyOrderNewRes.ResultModel(0, "查询数据过多，请减少页数", null));
            return info;
        }

        try {
            if (curPage == 0) {
                curPage = 1;
            }
            if (rows == 0) {
                rows = 4;
            }
            if (null == isweixiu) {
                isweixiu = "";
            }
            Integer zitidianId = null;
            if (smallShop) {
                List<Zitidian> list = zitidianService.list(new LambdaQueryWrapper<Zitidian>().eq(Zitidian::getUserid,
                        userId));
                if (CollectionUtils.isEmpty(list)) {
                    info.setResult(new MyOrderNewRes.ResultModel(0, "查无相关小店信息", null));
                }
                zitidianId = list.get(0).getId();
            }
            if (StringUtils.isEmpty(userId)) {
                info.setResult(new MyOrderNewRes.ResultModel(0, "查询用户不能为空", null));
            } else {
                List<String> orderIds = new ArrayList<>();
                if (orderArr != null && orderArr.length != 0) {
                    type = 5;
                    orderIds = Arrays.asList(orderArr);
                }
                Page<SubDataBO> page = new Page<>(curPage, rows);
                IPage<SubDataBO> ipage = baseMapper.getSubData(page, smallShop, zitidianId, CommonUtil.replaces(userId),
                        SubDelCollectTypeEnum.SUB_ORDER.getCode(), orderIds, type, xtenant, isweixiu, recover);
                if (null != ipage && CollectionUtils.isNotEmpty(ipage.getRecords())) {
                    ArrayList<MyOrderNewRes.SubSearchRes> subs = new ArrayList<>();
                    StringBuilder subIds = new StringBuilder();
                    info.setResult(new MyOrderNewRes.ResultModel(1, null, null));
                    info.setOrderList(new ArrayList<>());
                    List<SubDataBO> records = ipage.getRecords();
                    Integer finalZitidianId = zitidianId;
                    List<Integer> subIdsAll = records.stream().map(SubDataBO::getSubId).collect(Collectors.toList());
                    Map<Integer, List<EvaluateScoreBO>> evaluateScoreMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(subIdsAll)) {
                        List<EvaluateScoreBO> bos = baseMapper.getEvaluateScore(subIdsAll,
                                jiujiSystemProperties.getOfficeName());
                        evaluateScoreMap =
                                bos.stream().collect(Collectors.groupingBy(EvaluateScoreBO::getSubId));
                    }

                    Map<Integer, List<EvaluateScoreBO>> finalEvaluateScoreMap = evaluateScoreMap;
                    records.forEach(e -> {
                        MyOrderNewRes.SubSearchRes sub = new MyOrderNewRes.SubSearchRes();
                        sub.setSubTo(e.getSubTo());
                        sub.setSubDate(e.getSubDate());
                        sub.setSubId(e.getSubId());
                        sub.setSubCheckName(EnumUtil.getMessageByCode(SubCheckEnum.class, e.getSubCheck()));
                        sub.setSubCheck(e.getSubCheck());
                        sub.setDeliveryName(EnumUtil.getMessageByCode(SubDeliveryEnum.class, e.getDelivery()));
                        sub.setDelivery(e.getDelivery());
                        sub.setSubPayName(com.jiuji.tc.utils.enums.EnumUtil.getMessageByCode(SubPayEnum.class,
                                e.getSubPay()));
                        sub.setSub_pay(e.getSubPay());
                        sub.setYingfuM(e.getYingfuM());
                        sub.setYifuM(e.getYifuM().add(orderDetailService.getWhiteStripPrice(e.getSubId())));
                        sub.setZitiid(String.valueOf(finalZitidianId));
                        sub.setFeeM(e.getFeeM());
                        sub.setYouhui1M(e.getYouhui1M());
                        sub.setJidianM(e.getJidianM());
                        sub.setTradeDate(e.getTradeDate());
                        sub.setTradeDate1(e.getTradeDate1());
                        sub.setUserDate(e.getUserDate());
                        sub.setUserTime(e.getUserTime());
                        sub.setException(orderDetailService.checkException(OrderDetailServiceImpl.ExceptionSubRedisKey, e.getSubId().toString()));
                        if ("欠款".equals(sub.getSubCheckName())) {
                            sub.setSubCheckName("待付款");
                        }
                        if (subIds.length() == 0) {
                            subIds.append(e.getSubId());
                        } else {
                            subIds.append("," + e.getSubId());
                        }
                        subs.add(sub);
                        try {
                            //获取以旧换新的全部订单
                            MyOrderNewRes.ResultRecover recoverOrder = recoverorderList(userId, 1, 20, e.getSubId());
                            if (recoverOrder.getResult_().getStats() == 1) {
                                sub.setRecoverOrder(recoverOrder);
                            }
                        } catch (Exception e1) {
                            log.error(e1.getMessage());
                        }
                        //判断是否有评价
                        List<EvaluateScoreBO> evaluateScoreBOS = finalEvaluateScoreMap.get(e.getSubId());
                        if (CollectionUtils.isNotEmpty(evaluateScoreBOS)) {
                            sub.setIsPj(1);
                        } else {
                            sub.setIsPj(0);
                        }
                    });
                    //查找basket
                    List<BasketSearchRes> basketSearchs = new ArrayList<>();
                    List<BasketSearchRes> basketSearchTaocans = new ArrayList<>();
                    List<BasketSearchRes> baskethaomas = new ArrayList<>();
                    List<Integer> basketIds = new ArrayList<>();
                    List<Integer> basketHaomas = new ArrayList();
                    if (StringUtils.isNotEmpty(subIds)) {
                        List<String> subIdList = Arrays.asList(subIds.toString().split(","));
                        basketSearchs = basketService.getBasketDataBySubIds(subIdList, isweixiu);
                        basketHaomas = basketSearchs.stream().filter(e -> "9".equals(e.getType()))
                                .map(BasketSearchRes::getBasketId).collect(Collectors.toList());
                        basketIds = basketSearchs.stream()
                                .map(BasketSearchRes::getBasketId).collect(Collectors.toList());
                    }
                    //套餐
                    if (CollectionUtils.isNotEmpty(basketIds)) {
                        basketSearchTaocans = baseMapper.getTaoCanData(basketIds);
                    }
                    //靓号
                    if (CollectionUtils.isNotEmpty(basketHaomas)) {
                        baskethaomas = baseMapper.getHaoMaData(basketHaomas);
                    }
                    Map<Integer, BasketSearchRes> basketSearchTaocansMap =
                            basketSearchTaocans.stream().collect(Collectors.toMap(e -> e.getBasketId(), e -> e));
                    Map<Integer, BasketSearchRes> baskethaomasMap =
                            baskethaomas.stream().collect(Collectors.toMap(e -> e.getBasketId(), e -> e));
                    basketSearchs.forEach(e -> {
                        e.setPlanId(Optional.ofNullable(basketSearchTaocansMap.get(e.getBasketId())).map(BasketSearchRes::getPlanId).orElse(""));
                        e.setMobile(Optional.ofNullable(basketSearchTaocansMap.get(e.getBasketId())).map(BasketSearchRes::getMobile).orElse(""));
                        e.setPackageType(Optional.ofNullable(basketSearchTaocansMap.get(e.getBasketId())).map(BasketSearchRes::getPackageType).orElse(""));
                        e.setIsptype(Optional.ofNullable(basketSearchTaocansMap.get(e.getBasketId())).map(BasketSearchRes::getIsptype).orElse(""));
                        e.setContractPeroid(Optional.ofNullable(basketSearchTaocansMap.get(e.getBasketId())).map(BasketSearchRes::getContractPeroid).orElse(""));
                        e.setHaoma(Optional.ofNullable(baskethaomasMap.get(e.getBasketId())).map(BasketSearchRes::getHaoma).orElse(""));
                    });
                    List<BasketSearchRes> finalBasketSearchs = basketSearchs;
                    subs.forEach(e -> {
                        e.setBasketInfo(finalBasketSearchs.stream().filter(a -> a.getSubId() == e.getSubId()).collect(Collectors.toList()));
                    });
                    info.setOrderList(subs);
                    info.setCurPage(ipage.getCurrent());
                    info.setTotalPage(ipage.getPages());
                    info.setTotalCount(ipage.getTotal());
                } else {
                    info.setResult(new MyOrderNewRes.ResultModel(0, "查无相关记录", null));
                }
            }
            return info;
        } catch (Exception e) {
            info.setResult(new MyOrderNewRes.ResultModel(0, e.getMessage(), null));
            log.error(e.getMessage());
            return info;
        }
    }

    @Override
    public void updateNetSub(Integer subId) {
        String switchValue = redisTemplate.opsForValue().get(RedisKeyConstant.HASH_REDIS_SWITCH);
        String netSubRedisKey = "netSubRedis";
        if (StringUtils.isEmpty(switchValue) || "true".equals(switchValue)) {
            if (Boolean.TRUE.equals(redisTemplate4.opsForHash().hasKey(netSubRedisKey, subId + ""))) {
                addNetSubPush(subId);
            }
        } else {
            if (Boolean.TRUE.equals(redisTemplate.opsForHash().hasKey(netSubRedisKey, subId + ""))) {
                addNetSubPush(subId);
            }
        }
        //加入派送单
        addPaiSongPush(1, Collections.singletonList(subId));
        //加入拿货队列
        addNahuoItemPush(subId, 2);
    }

    @Override
    public void addNetSubPush(Integer subId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("act", "addNetSubPush");
        jsonObject.put("data", subId);
        amqpTemplate.convertAndSend(RabbitMqConfig.QUEUE_TOPIC_OAASYNC, jsonObject.toString());
    }

    @Override
    public void addPaiSongPush(Integer type, List<Integer> id) {
        type = type == null ? 1 : type;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("act", "addPaiSongPush");
        JSONObject data = new JSONObject();
        data.put("type", type);
        data.put("id", id);
        jsonObject.put("data", data);
        amqpTemplate.convertAndSend(RabbitMqConfig.QUEUE_TOPIC_OAASYNC, jsonObject.toString());
    }

    @Override
    public void addNahuoItemPush(Integer id, Integer type) {
        type = type == null ? 1 : type;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("act", "addNahuoItemPush");
        JSONObject data = new JSONObject();
        data.put("basket_id", id);
        data.put("type", type);
        jsonObject.put("data", data);
        amqpTemplate.convertAndSend(RabbitMqConfig.QUEUE_TOPIC_OAASYNC, jsonObject.toString());
    }

    @Override
    @DS("smallpro_write")
    public Boolean updateSub(Sub sub) {
        return this.updateById(sub);
    }

    /**
     * 获得订单id和良品Id
     *
     * @return SubIdAndRecoverIdRes
     */
    @Override
    public SubIdAndRecoverIdRes getSubIdAndRecoverId(SubReq subReq) {
        SubIdAndRecoverIdRes idRes = new SubIdAndRecoverIdRes();
        List<Integer> subIds = subMapper.getSubIds(subReq);
        List<Integer> recoverIds = subMapper.getRecoverIds(subReq);
        idRes.setSubIds(subIds);
        idRes.setRecoverId(recoverIds);
        return idRes;
    }

    @Override
    public List<SalesInfoVo> listDjiSalesInfo(LocalDateTime startTime, LocalDateTime endTime, List<Integer> areaIds) {
        int code = 118000;
        R<String> valueByCode = sysConfigClient.getValueByCode(code);
        String data = valueByCode.getData();
        List<Integer> djAreaIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(data)) {
            List<String> djAreaIdsString = Arrays.asList(data.split(StringPool.COMMA));
            djAreaIds = djAreaIdsString.stream().map(Integer::parseInt).collect(Collectors.toList());
        }
        return baseMapper.listDjiSalesInfo(startTime, endTime, areaIds, djAreaIds);
    }

    @Override
    public List<SalesVO> listXiaomiSalesInfo(LocalDateTime startTime, LocalDateTime endTime, List<Integer> cityIds, List<Integer> cidList, List<Integer> brandIdList) {
        return baseMapper.listXiaomiSalesInfo(startTime, endTime, cityIds, cidList, brandIdList);
    }

    @Override
    @DS("oanew")
    public Sub getSub(Integer subId) {
        return this.getById(subId);
    }

    @Override
    public R<String> xiaomiSalesProportion(String startTime, String endTime, List<Integer> cityIds, List<Integer> cidList, List<Integer> brandIdList) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        try {
            LocalDateTime start = LocalDateTime.parse(startTime, formatter);
            LocalDateTime end = LocalDateTime.parse(endTime, formatter);
            if (start.isAfter(end)) {
                return R.error("结束时间必须大于开始时间");
            }
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime lastMonth = now.minusMonths(1).minusDays(now.getDayOfMonth() - 1L).minusHours(now.getHour())
                    .minusMinutes(now.getMinute()).minusSeconds(now.getSecond()).minusNanos(now.getNano());
            if (start.isBefore(lastMonth)) {
                return R.error("非法的时间段，只允许查询当前时间到上个月1号的数据");
            }
            BigDecimal percent = baseMapper.xiaomiSalesProportion(start, end, cityIds, cidList, brandIdList);
            return R.success("请求成功", percent == null ? "0" : percent + "%");
        } catch (DateTimeParseException ex) {
            return R.error("错误的时间格式!");
        }
    }

    @Override
    public R<Boolean> existsProcessingOrderInfo(Integer userId) {
        return R.success("操作成功", !CommonUtil.isNullOrZero(baseMapper.existsProcessingOrderInfo(userId)));
    }

    /// <summary>
    /// 根据新机订单号和userid获取关联回收单的单号
    /// </summary>
    /// <param name="userid"></param>
    /// <param name="page"></param>
    /// <param name="rows"></param>
    /// <returns></returns>
    private MyOrderNewRes.ResultRecover recoverorderList(String userId, int pageC, int rows, Integer subId) {
        MyOrderNewRes.ResultRecover info = new MyOrderNewRes.ResultRecover();
        Page<SubDataBO> page = new Page<>(pageC, rows);
        List<MyOrderNewRes.RecoverInfo> list = new ArrayList<>();
        IPage<RecoverSubRes> ipage = recoverSubService.getRecoverDataByUserId(page, userId, subId);
        if (null != ipage && CollectionUtils.isNotEmpty(ipage.getRecords())) {
            info.setResult_(new MyOrderNewRes.ResultModel(1, null, null));
            info.setList(new ArrayList<>());
            List<RecoverSubRes> records = ipage.getRecords();
            Map<Integer, String> areaCompanyMap = getIntegerStringMap(records);
            Map<Integer, List<RecoverBasketRes>> basketMap = getIntegerListMap(records);
            Map<Integer, String> finalAreaCompanyMap = areaCompanyMap;
            Map<Integer, List<RecoverBasketRes>> finalBasketMap = basketMap;
            records.forEach(e -> {
                MyOrderNewRes.RecoverInfo one = new MyOrderNewRes.RecoverInfo();
                e.setSubPayName(com.jiuji.tc.utils.enums.EnumUtil.getMessageByCode(RecoverPayEnum.class,
                        e.getSubPay()));
                e.setSubDeliveryName(com.jiuji.tc.utils.enums.EnumUtil.getMessageByCode(RecoverTradeWayEnum.class,
                        e.getSubPay()));
                e.setSubCheckName(com.jiuji.tc.utils.enums.EnumUtil.getMessageByCode(RecoverStateEnum.class,
                        e.getSubCheck()));
                if (e.getSubDelivery() == 3) {
                    CityIDListBO ids = orderDetailService.getAreaIDByCityID(String.valueOf(e.getCityid()),
                            1);
                    e.setPid(ids.getPid());
                    e.setDid(ids.getDid());
                    e.setZid(ids.getZid());
                }
                if (e.getSubDelivery() == 1) {
                    e.setShopAddress(Optional.ofNullable(finalAreaCompanyMap.get(e.getAreaId())).orElse(""));
                }
                List<RecoverBasketRes> basket = finalBasketMap.get(e.getSubId());
                e.setTotalPrice(basket.stream()
                        .map(s -> new BigDecimal(s.getPpriceid())
                                .multiply(new BigDecimal(s.getCount()))).reduce(BigDecimal.ZERO, BigDecimal::add));
                one.setSub(e);
                list.add(one);
            });
            info.setList(list);
            info.setCurPage(ipage.getCurrent());
            info.setTotalPage(ipage.getPages());
        } else {
            info.setResult_(new MyOrderNewRes.ResultModel(0, "无记录", null));
        }
        return info;
    }

    private Map<Integer, List<RecoverBasketRes>> getIntegerListMap(List<RecoverSubRes> records) {
        List<Integer> subIds =
                records.stream().map(RecoverSubRes::getSubId).collect(Collectors.toList());
        Map<Integer, List<RecoverBasketRes>> basketMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(subIds)) {
            List<RecoverBasketRes> basket = recoverBasketService.getBasketDta(subIds);
            if (CollectionUtils.isNotEmpty(basket)) {
                basketMap =
                        basket.stream().collect(Collectors.groupingBy(RecoverBasketRes::getSubId));
            }
        }
        return basketMap;
    }

    private Map<Integer, String> getIntegerStringMap(List<RecoverSubRes> records) {
        List<Integer> areaIds =
                records.stream().filter(e -> e.getSubDelivery() == 1).map(RecoverSubRes::getAreaId).distinct().collect(Collectors.toList());
        Map<Integer, String> areaCompanyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(areaIds)) {
            R<List<AreaInfo>> listR =
                    areaInfoClient.listAreaInfo(areaIds);
            if (null != listR && CollectionUtils.isNotEmpty(listR.getData())) {
                areaCompanyMap = listR.getData().stream().collect(Collectors.toMap(e -> e.getId(),
                        e -> e.getCompanyAddress()));
            }
        }
        return areaCompanyMap;
    }

    /**
     * 获取订单关注人员记录
     *
     * @param subIds
     * @param kind
     * @return
     */
    private List<SubCollection> getSubCollentUser(List<Long> subIds, int kind) {
        List<SubCollection> data = new ArrayList<>();
        if (subIds == null) {
            return data;
        }
        return subMapper.getSubCollentUser(subIds, SubCollectTypeEnum.SUB_COLLECT_TYPE_TYPE_1.getCode());
    }

    @Override
    public Integer getAfterSaleQuantity(int subId) {
        //根据订单获取basket_id
        List<Integer> basketIdsBySubId = subMapper.getBasketIdsBySubId(subId);
        if (CollectionUtils.isEmpty(basketIdsBySubId)){
            return NumberConstant.ZERO;
        }
        //获取小件的维修记录
        Integer smallProNumber = subMapper.getSmallProQuantity(subId);
        Integer shouhouNumber = subMapper.getAfterSaleQuantity(basketIdsBySubId);
        return smallProNumber+shouhouNumber;
    }

    @Override
    public R<TaxSubInfoRes> getTaxSubInfo(Integer subId, Integer type) {
        TaxSubInfoRes result = subMapper.getTaxSubInfo(subId,type, Namespaces.get());
        //历史库查询
        if(result==null){
            result = subMapper.getTaxSubInfoHis(subId,type, Namespaces.get());
        }
        if(result==null){
            return R.error("订单已被删除，无法开具发票");
        }
        Integer check = Optional.ofNullable(result.getCheckState()).orElse(NumberConstant.ZERO);
        if (type == null || type.equals(NumberConstant.ZERO)) {
            //新机单数据校验
            if(!check.equals(NumberConstant.THREE)){
                return R.error("订单未交易完成或已退款，如需开票请联系门店");
            }
        } else if(type.equals(NumberConstant.ONE)){
            //维修单数据校验
            if(!check.equals(NumberConstant.ONE)){
                return R.error("订单未交易完成或已退款，如需开票请联系门店");
            }

        } else if (type.equals(NumberConstant.TWO)) {
            //良品单数据校验
            if(!check.equals(NumberConstant.THREE)){
                return R.error("订单未交易完成或已退款，如需开票请联系门店");
            }
        } else {
            return R.error("查询订单类型错误");
        }
        return R.success(result);
    }

    @Override
    public R<HqLargeScreenOrderCountRes> getHqLargeScreenOrderCount() {
        HqLargeScreenOrderCountRes countRes = new HqLargeScreenOrderCountRes();
        // 开始时间
        LocalDateTime startTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(0, 0, 0));
        // 结束时间
        LocalDateTime endTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(23, 59, 59));
        // 新机订单量
        CompletableFuture<List<Map<String, Integer>>> normalOrdersFuture = CompletableFuture.supplyAsync(() ->
                this.baseMapper.countOrdersByAreaAttr(startTime, endTime));
        // 运营商订单量
        CompletableFuture<Integer> operatorOrdersFuture = CompletableFuture.supplyAsync(() ->
                this.operatorBasketService.countOrdersByToday(startTime, endTime));
        // 给订单量设置默认值
        // 综合店订单量
        AtomicReference<Integer> synthesisOrderCount = new AtomicReference<>(NumberConstant.ZERO);
        // 专卖店订单量
        AtomicReference<Integer> specialtyOrderCount = new AtomicReference<>(NumberConstant.ZERO);
        // 大疆订单量
        AtomicReference<Integer> dajiangOrderCount = new AtomicReference<>(NumberConstant.ZERO);
        List<Map<String, Integer>> normalOrders = normalOrdersFuture.join();
        if (CollectionUtils.isNotEmpty(normalOrders)) {
            // 专卖店code
            List<Integer> specialtyCodes = Arrays.asList(AreaAttrEnum.APPLE_SPECIALTY.getCode(), AreaAttrEnum.HUAWEI_SPECIALTY.getCode(),
                    AreaAttrEnum.HONOR_SPECIALTY.getCode(), AreaAttrEnum.XIAOMI_SPECIALTY.getCode(), AreaAttrEnum.OPPO_SPECIALTY.getCode(),
                    AreaAttrEnum.VIVO_SPECIALTY.getCode(), AreaAttrEnum.ONEPLUS_SPECIALTY.getCode(), AreaAttrEnum.SWITCH_SPECIALTY.getCode(),
                    AreaAttrEnum.OTHER_SPECIALTY.getCode());
            normalOrders.forEach(map -> {
                Integer orderCount = map.get("orderCount");
                if (Objects.isNull(orderCount)) {
                    
                    return;
                }
                Integer code = map.get("attr");
                if (Objects.isNull(code)) {
                    synthesisOrderCount.updateAndGet(v -> v + orderCount);
                    return;
                }
                // 综合
                if (AreaAttrEnum.BRAND_SYNTHESIS.getCode().equals(code)) {
                    synthesisOrderCount.updateAndGet(v -> v + orderCount);
                    return;
                }
                // 大疆
                if (AreaAttrEnum.DJI_SPECIALTY.getCode().equals(code)) {
                    dajiangOrderCount.updateAndGet(v -> v + orderCount);
                    return;
                }
                // 专卖
                if (specialtyCodes.contains(code)) {
                    specialtyOrderCount.updateAndGet(v -> v + orderCount);
                    return;
                }
            });
        }

        Integer operatorOrderCount = operatorOrdersFuture.join();
        countRes.setOperatorOrderCount(operatorOrderCount);
        countRes.setDajiangOrderCount(dajiangOrderCount.get());
        countRes.setSpecialtyOrderCount(specialtyOrderCount.get());
        countRes.setSynthesisOrderCount(synthesisOrderCount.get());

        return R.success(countRes);
    }

    /**
     * 更新订单备注信息
     * @param spickupGoodsCode
     * @return
     */
    @Override
    public boolean updateSubComment(String spickupGoodsCode, List<Long> subIds) {
        if (StringUtils.isEmpty(spickupGoodsCode) || CollectionUtils.isEmpty(subIds)) {
            return false;
        }
        return this.baseMapper.updateSubComment(spickupGoodsCode, subIds);
    }


    @Override
    public R<List<DeviceRes>> getDevicesByUser(String queryParameters) {
        if (StrUtil.isBlank(queryParameters)) {
            return R.success("查询数据为空！", Collections.emptyList());
        }

        String subMobile = DecideUtil.iif(Validator.isMobile(queryParameters), queryParameters, null);
        String imei = DecideUtil.iif(StrUtil.isBlank(subMobile), queryParameters, null);

        if (StrUtil.isBlank(subMobile) && StrUtil.isBlank(imei)) {
            return R.success("查询数据为空！", Collections.emptyList());
        }

        List<DeviceRes> deviceResList;
        try {
            // 查询订单数据
            deviceResList = getServiceInfo(subMobile, imei);
        } catch (Exception e) {
            return R.error("查询订单数据时发生错误: " + e.getMessage());
        }

        if (CollUtil.isEmpty(deviceResList)) {
            return R.success("无订单数据", Collections.emptyList());
        }

        List<String> imeiList = deviceResList.stream()
                .map(DeviceRes::getImei)
                .distinct()
                .collect(Collectors.toList());

        // 查询相关的出险记录并过滤掉无关的数据
        List<ServiceRecord> serviceRecordList = baseMapper.getDevicesByUser(imeiList);

        // 过滤设备列表
        List<String> serviceRecordImeiList = serviceRecordList.stream()
                .map(ServiceRecord::getImei)
                .collect(Collectors.toList());

        deviceResList = deviceResList.stream()
                .filter(deviceRes -> serviceRecordImeiList.contains(deviceRes.getImei()))
                .collect(Collectors.toList());

        // 查询赠品
        List<Integer> basketIds = deviceResList.stream()
                .map(DeviceRes::getBasketId)
                .collect(Collectors.toList());
        List<DeviceRes.Gift> giftInfo = getGiftInfo(basketIds);

        return R.success(processingData(deviceResList, giftInfo));
    }


    /**
     * 获取赠品
     * @param deviceResList
     * @param giftInfo
     * @return
     */
    private List<DeviceRes> processingData(List<DeviceRes> deviceResList, List<DeviceRes.Gift> giftInfo) {
        Map<Integer, List<DeviceRes.Gift>> giftsMap = giftInfo.stream()
                .collect(Collectors.groupingBy(DeviceRes.Gift::getGiftid));

        for (DeviceRes deviceRes : deviceResList) {
            List<DeviceRes.Gift> relatedGifts = giftsMap.get(deviceRes.getBasketId());
            deviceRes.setGifts(relatedGifts);
            if (relatedGifts != null) {
                List<DeviceRes.GiftsExhibition> giftsExhibition = relatedGifts.stream()
                        .collect(Collectors.groupingBy(DeviceRes.Gift::getPpriceId))
                        .values().stream()
                        .map(giftGroup -> {
                            DeviceRes.GiftsExhibition exhibition = new DeviceRes.GiftsExhibition();
                            exhibition.setGiftName(StrUtil.format("【赠品】{}", giftGroup.get(0).getProductName()));
                            exhibition.setQuantity(giftGroup.stream().mapToInt(DeviceRes.Gift::getQuantity).sum());
                            exhibition.setCommodityValue(giftGroup.stream()
                                    .map(gift -> Optional.ofNullable(gift.getPrice()).orElse(BigDecimal.ZERO)
                                            .multiply(BigDecimal.valueOf(Optional.ofNullable(gift.getQuantity()).orElse(1))))
                                    .reduce(BigDecimal.ZERO, BigDecimal::add));
                            //塞入默认值
                            if (CommonUtil.isNullOrZero(exhibition.getQuantity())) {
                                exhibition.setQuantity(1);
                            }
                            return exhibition;
                        })
                        .collect(Collectors.toList());

                deviceRes.setGiftsExhibition(giftsExhibition);
            }
            calculateCoefficient(deviceRes,null,null);
        }
        return deviceResList;
    }

    /**
     * 计算存储系数
     *
     */
    private void calculateCoefficient(DeviceRes deviceRes, CalculateDifferenceReq calculateDifferenceReq, CalculateDifferenceRes calculateDifferenceRes) {
        LocalDateTime transactionDate = deviceRes.getTransactionDate();
        //订单类型为“定金预定的商品”按照加单时间进行生效
        if (Objects.equals(deviceRes.getBasketType(), 26)) {
            transactionDate = deviceRes.getBasketDate();
        }

        // 计算当前时间与 deviceRes.getTransactionDate() 相隔多少天
        long time = LocalDateTimeUtil.between(transactionDate ,LocalDateTime.now(), ChronoUnit.DAYS);
        BigDecimal subPrice = deviceRes.getPrice().subtract(Optional.ofNullable(deviceRes.getGifts()).orElse(new ArrayList<>())
                .stream().map(DeviceRes.Gift::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add));

        // 定义系数和类型
        String coefficientTypeName = null;
        BigDecimal coefficient = null;
        boolean isTradeInCoefficient = false;

        // 根据交易日期和品牌 ID 计算系数
        if (transactionDate.isAfter(LocalDateTime.of(2024, 7, 23, 0, 0, 0))) {

            if (transactionDate.isAfter(LocalDateTime.of(2024, 10, 1, 0, 0, 0))) {
                //10月1号以后的走新逻辑
                if (Objects.equals(deviceRes.getBrandId(), 1)) { // 苹果
                    if (time <= 365) {
                        coefficient = new BigDecimal("0.6");
                        coefficientTypeName = "保值系数";
                    } else if (time <= 731) {
                        coefficient = new BigDecimal("0.5");
                        coefficientTypeName = "保值系数";
                    }
                } else { // 安卓
                    if (time <= 365) {
                        coefficient = new BigDecimal("0.5");
                        coefficientTypeName = "保值系数";
                    } else if (time <= 731) {
                        coefficient = new BigDecimal("0.3");
                        coefficientTypeName = "保值系数";
                    }
                }
            }else {
                if (Objects.equals(deviceRes.getBrandId(), 1)) { // 苹果
                    if (time <= 365) {
                        coefficient = new BigDecimal("0.7");
                        coefficientTypeName = "保值系数";
                    } else if (time <= 731) {
                        coefficient = new BigDecimal("0.5");
                        coefficientTypeName = "保值系数";
                    }
                } else { // 安卓
                    if (time <= 365) {
                        coefficient = new BigDecimal("0.5");
                        coefficientTypeName = "保值系数";
                    } else if (time <= 731) {
                        coefficient = new BigDecimal("0.3");
                        coefficientTypeName = "保值系数";
                    }
                }
            }

            // 计算保值金额
            DeviceRes.CalculateValuePreservation calculateValuePreservation = new DeviceRes.CalculateValuePreservation();
            calculateValuePreservation.setValuePreservationAmount(subPrice.multiply(coefficient));
            calculateValuePreservation.setComputationalLogic(StrUtil.format("(￥{}-{})*{}", deviceRes.getPrice(), extractAndFormatPrices(deviceRes.getGifts()), coefficient));
            calculateValuePreservation.setMessage("保值金额 = 原商品售价(排除赠品价格) * 保值系数");
            deviceRes.setCalculateValuePreservation(calculateValuePreservation);

        } else {
            if (transactionDate.isAfter(LocalDateTime.of(2023, 6, 12, 0, 0, 0))
                    && transactionDate.isBefore(LocalDateTime.of(2024, 7, 23, 0, 0, 0))) {
                if (Objects.equals(deviceRes.getBrandId(), 1)) { // 苹果
                    isTradeInCoefficient = true;
                    coefficientTypeName = "换购系数";
                    coefficient = DecideUtil.iif((time <= 365) , new BigDecimal("0.3") , new BigDecimal("0.5"));
                } else { // 安卓
                    isTradeInCoefficient = true;
                    coefficientTypeName = "换购系数";
                    coefficient = new BigDecimal("0.5");
                }
            } else {
                if (Objects.equals(deviceRes.getBrandId(), 1)) { // 苹果
                    isTradeInCoefficient = true;
                    coefficientTypeName = "换购系数";
                    coefficient = DecideUtil.iif((time <= 365) , new BigDecimal("0.28") , new BigDecimal("0.48"));
                } else { // 安卓
                    isTradeInCoefficient = true;
                    coefficientTypeName = "换购系数";
                    coefficient = new BigDecimal("0.48");
                }
            }
        }

        // 设置系数和类型
        deviceRes.setIsItATradeInCoefficient(isTradeInCoefficient);
        deviceRes.setCoefficientTypeName(coefficientTypeName);
        deviceRes.setCoefficient(String.valueOf(coefficient));

        // 计算客户补差金额
        if (Objects.nonNull(calculateDifferenceReq)) {
            BigDecimal waitPay = subPrice.subtract(NumberUtil.toBigDecimal(calculateDifferenceReq.getPrice()));
            BigDecimal differenceAmount;

            if (waitPay.compareTo(BigDecimal.ZERO) < 0) {
                BigDecimal multiply = subPrice.multiply(coefficient);
                differenceAmount = NumberUtil.toBigDecimal(calculateDifferenceReq.getPrice()).subtract(subPrice).add(multiply);
                calculateDifferenceRes.setComputationalLogic(StrUtil.format("￥{}- (￥{}-{}) + (￥{}-{}) *{}", calculateDifferenceReq.getPrice(), deviceRes.getPrice(), extractAndFormatPrices(deviceRes.getGifts()), deviceRes.getPrice(), extractAndFormatPrices(deviceRes.getGifts()), coefficient));
                calculateDifferenceRes.setDifferenceAmount(differenceAmount);
                calculateDifferenceRes.setMessage("客户补差金额 = 换新商品售价-原商品售价(排除赠品价格) + 原商品售价(排除赠品价格) * 换购系数");
            } else {
                differenceAmount = subPrice.multiply(coefficient);
                calculateDifferenceRes.setComputationalLogic(StrUtil.format("(￥{}-{})*{}", deviceRes.getPrice(), extractAndFormatPrices(deviceRes.getGifts()), coefficient));
                calculateDifferenceRes.setDifferenceAmount(differenceAmount);
                calculateDifferenceRes.setMessage("客户补差金额 = 原商品售价(排除赠品价格) * 换购系数");
            }


        }
    }

    /**
     * 查询订单数据
     * @param subMobile
     * @param imei
     * @return
     */
    public List<DeviceRes> getServiceInfo(String subMobile, String imei) {
        // 输入参数校验
        if (StrUtil.isBlank(subMobile) && StrUtil.isBlank(imei)) {
            return Collections.emptyList();
        }

        // 获取历史和普通订单
        List<DeviceRes> serviceInfoVOAllList = new ArrayList<>();
        serviceInfoVOAllList.addAll(getHistorySub(subMobile, imei));
        serviceInfoVOAllList.addAll(getNormalSub(subMobile, imei));

        // 如果没有订单数据，直接返回空列表
        if (CollUtil.isEmpty(serviceInfoVOAllList)) {
            return Collections.emptyList();
        }

        // 获取唯一的 ppids
        List<Integer> ppids = serviceInfoVOAllList.stream().map(DeviceRes::getPpriceId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());

        // 如果没有 ppids，直接返回空列表
        if (CollUtil.isEmpty(ppids)) {
            return Collections.emptyList();
        }

        // 查询产品信息
        List<Productinfo> productinfoByPpid;
        try {
            productinfoByPpid = productinfoMapper.getProductListByPpids(ppids);
        } catch (Exception e) {
            return Collections.emptyList();
        }

        // 将产品信息映射到一个 Map 中
        Map<Integer, Productinfo> productInfoMap = productinfoByPpid.stream()
                .collect(Collectors.toMap(Productinfo::getPpriceid, Function.identity()));

        // 查询国补信息
        List<Integer> subIdList = serviceInfoVOAllList.stream().map(DeviceRes::getSubId).distinct().collect(Collectors.toList());
        List<Integer> isNationalSubsidy = subMapper.getNationalSubsidy(subIdList);


        // 更新设备信息
        serviceInfoVOAllList.forEach(deviceRes -> {
            Productinfo productinfo = productInfoMap.get(deviceRes.getPpriceId());
            if (productinfo != null) {
                deviceRes.setProductName(productinfo.getProductName());
                deviceRes.setProductColor(StrUtil.trim(productinfo.getProductColor()));
                deviceRes.setBrandId(productinfo.getBrandID());
                deviceRes.setCid(productinfo.getCid());
            }

            //更新国补
            if (CollUtil.isNotEmpty(isNationalSubsidy) && isNationalSubsidy.contains(deviceRes.getSubId())) {
                BigDecimal maxSubsidyPrice = new BigDecimal("500");
                BigDecimal subsidyRatio = new BigDecimal("15");
                //国补补贴金额 = 实付金额 * 国补补贴比例 < 国补补贴最高金额
                BigDecimal subsidyPrice = NumberUtil.mul(deviceRes.getPrice(), NumberUtil.div(subsidyRatio, 100))
                        .setScale(2, RoundingMode.HALF_UP);
                if (new BigDecimal("500").compareTo(subsidyPrice) < 0) {
                    subsidyPrice = maxSubsidyPrice;
                }
                deviceRes.setSubsidyPrice(subsidyPrice);
                deviceRes.setPrice(NumberUtil.sub(deviceRes.getPrice(), subsidyPrice).setScale(2, RoundingMode.HALF_UP));
            }
        });

        return serviceInfoVOAllList;
    }


    /**
     * 查询赠品逻辑
     * @param basketIds
     * @return
     */
    public List<DeviceRes.Gift> getGiftInfo(List<Integer> basketIds) {
        // 输入参数校验
        if (CollUtil.isEmpty(basketIds)) {
            return Collections.emptyList();
        }

        // 获取历史赠品和普通赠品
        List<DeviceRes.Gift> giftList = new ArrayList<>();
        giftList.addAll(getHistoryGift(basketIds));
        giftList.addAll(getNormalGift(basketIds));

        // 如果没有赠品，直接返回空列表
        if (CollUtil.isEmpty(giftList)) {
            return Collections.emptyList();
        }

        // 获取唯一的 ppids
        List<Integer> ppids = giftList.stream()
                .map(DeviceRes.Gift::getPpriceId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 如果没有 ppids，直接返回空列表
        if (CollUtil.isEmpty(ppids)) {
            return Collections.emptyList();
        }

        // 查询产品信息
        List<Productinfo> productinfoByPpid;
        try {
            productinfoByPpid = productinfoMapper.getProductListByPpids(ppids);
        } catch (Exception e) {
            // 处理查询异常
            return Collections.emptyList();
        }

        // 将产品信息映射到一个 Map 中
        Map<Integer, Productinfo> productInfoMap = productinfoByPpid.stream()
                .collect(Collectors.toMap(Productinfo::getPpriceid, Function.identity()));

        // 更新赠品信息
        giftList.forEach(gift -> {
            Productinfo productinfo = productInfoMap.get(gift.getPpriceId());
            if (productinfo != null) {
                gift.setProductName(productinfo.getProductName());
                gift.setProductColor(StrUtil.trim(productinfo.getProductColor()));
            }
        });

        return giftList;
    }


    // 数据源方法保持不变
    public List<DeviceRes> getHistorySub(String subMobile, String imei) {
        return MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS,
                () ->subMapper.getSubById(subMobile, imei));
    }

    public List<DeviceRes> getNormalSub(String subMobile, String imei) {
        return MultipleTransaction.query(DataSourceConstants.CH999_OA_NEW,
                () ->subMapper.getSubById(subMobile, imei));
    }

    public List<DeviceRes.Gift> getHistoryGift(List<Integer> basketIds) {
        return MultipleTransaction.query(DataSourceConstants.OA_NEW_HIS,
                () ->subMapper.getGiftByBasketIds(basketIds));
    }

    public List<DeviceRes.Gift> getNormalGift(List<Integer> basketIds) {
        return MultipleTransaction.query(DataSourceConstants.CH999_OA_NEW,
                () ->subMapper.getGiftByBasketIds(basketIds));
    }


    /**
     * 计算赠品数据
     * @param gifts
     * @return
     */
    private static String extractAndFormatPrices(List<DeviceRes.Gift> gifts) {
        if (gifts == null || gifts.isEmpty()) {
            // 如果 gifts 为 null 或空，返回空字符串
            return "￥0.00";
        }
        return gifts.stream()
                .map(gift -> {
                    if (gift != null && gift.getPrice() != null) {
                        return "￥" + gift.getPrice();
                    }
                    // 如果 gift 或 gift.getPrice() 为 null，返回默认值
                    return "￥0.00";
                })
                .collect(Collectors.joining("-"));
    }


    /**
     * 计算客户补差金额接口
     * @param req
     * @return
     */
    @Override
    public R<CalculateDifferenceRes> calculateCustomerDifference(CalculateDifferenceReq req) {
        // 输入参数校验
        if (req == null) {
            return R.error("请求参数不能为空");
        }

        // 处理价格为0的情况
        BigDecimal price = NumberUtil.toBigDecimal(Optional.ofNullable(req.getPrice()).orElse("0"));
        if (price.compareTo(BigDecimal.ZERO) == 0) {
            Productinfo productinfo = productinfoMapper.selectById(req.getProductId());
            if (productinfo == null) {
                return R.error("未找到产品信息");
            }
            req.setPrice(String.valueOf(productinfo.getMemberprice()));
        }

        // 获取用户设备
        R<List<DeviceRes>> devicesByUser = getDevicesByUser(req.getImei());
        if (!devicesByUser.isSuccess()) {
            return R.error(devicesByUser.getUserMsg());
        }

        // 检查设备列表是否为空
        List<DeviceRes> data = devicesByUser.getData();
        if (CollUtil.isEmpty(data)) {
            return R.error("未找到与该IMEI相关的设备");
        }

        // 处理设备信息
        DeviceRes deviceRes = data.get(0);
        CalculateDifferenceRes calculateDifferenceRes = new CalculateDifferenceRes();

        // 计算系数
        try {
            calculateCoefficient(deviceRes, req, calculateDifferenceRes);
        } catch (Exception e) {
            return R.error("计算过程中发生错误: " + e.getMessage());
        }

        return R.success(calculateDifferenceRes);
    }

}
