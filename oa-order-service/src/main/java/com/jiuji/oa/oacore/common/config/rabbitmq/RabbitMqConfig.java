package com.jiuji.oa.oacore.common.config.rabbitmq;

import com.jiuji.tc.foundation.rabbitmq.config.MultipleRabbitProperties;
import com.jiuji.tc.foundation.rabbitmq.config.RabbitFactory;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.config.AbstractRabbitListenerContainerFactory;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionNameStrategy;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.RabbitListenerContainerFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @since 2020/04/13
 */
@Component
@Configuration
public class RabbitMqConfig {

    /**
     * oa队列
     */
    public static final String QUEUE_TOPIC_OAASYNC = "oaAsync";

    public static final String QUEUE_TOPIC_DELAYQUEUE = "delayQueue";
    /**抖音消息队列*/
    public static final String QUEUE_DOUYIN_MESSAGE = "douyinMessageQueue";

    public static final String QUEUE_LOSS_ORDER_MESSAGE_PUSH = "lossOrderMessagePushQueue";

    public static final String addNewVoucherPush = "addNewVoucherPush";

    /**
     * 自动计算工资队列
     */
    public static final String AUTO_CALCULATE_OF_SALARY = "autoCalculateOfSalary";

    /**
     * 订单动态消息队列
     */
    public static final String QUEUE_ORDER_DYNAMICS_MESSAGE = "order-dynamics";

    /**
     * 亚丁消息队列
     */
    public static final String  YADING_SERVICE = "yading-service";

    /**
     * 宝尊采购消息队列
     */
    public static final String  BAOZUN_PURCHASE_INSTOCK = "baozun-purchase-instock";

    /**
     * 美团拣货消息队列
     */
    public static final String  MEITUAN_PRINT_PICK = "meituan-print-pick";

    public static final String  SAMSUNG_INSTALMENT = "samsung-instalment";

    /**
     * 订单动态回调消息队列
     */
    public static final String QUEUE_ORDER_DYNAMICS_CALLBACK_MESSAGE = "order-dynamics-wuliu";

    /**
     * 宝尊oa接口调用队列
     */
    public static final String BAOZUN_S47_OASUBMIT = "baozun-s47-oasubmit";

    /**
     * 宝尊云仓监听队列
     */
    public static final String BAOZUN_CLOUD_STORE = "orderservice_baozun_cloud_store";

    /**
     * 宝尊结算文件广播
     */
    public static final String BAOZUN_PARSE_A01_TOPIC = "baozun_parse_a01_topic";

    /**
     * 宝尊自动退货完成广播
     */
    public static final String BAOZUN_AUTO_REFUND_TOPIC = "baozun_auto_refund_topic";

    /**
     * 宝尊云仓订单文件解析后生成采购单
     */
    public static final String BAOZUN_PARSE_A01_CLOUD_STORE_QUEUE = "baozun_parse_a01_after_cloud_store";

    /**
     * 宝尊云仓
     */
    public static final String BAOZUN_REFUND_CLOUD_STORE_QUEUE = "baozun_refund_cloud_store";

    /**
     * 宝尊报错消息通知
     */
    public static final String BAOZUN_ERROR_NOTICE_QUEUE = "baozun_error_notice";
    @Autowired
    MultipleRabbitProperties multipleRabbitProperties;


    /**
     * 获取库存变动消息队列
     *
     * @return
     */
    @Bean
    public Queue yadingService() {
        return new Queue(YADING_SERVICE, true);
    }

    @Bean
    public Queue baozunPurchaseInstock() {
        return new Queue(BAOZUN_PURCHASE_INSTOCK, true);
    }


    @Bean(name = "oaAsyncConnectionFactory")
    @Primary
    public ConnectionFactory oaAsyncConnectionFactory(MultipleRabbitProperties multipleRabbitProperties
            , ObjectProvider<ConnectionNameStrategy> connectionNameStrategy) throws Exception {
        return RabbitFactory.createConnectionFactory(
                multipleRabbitProperties.getMultiple().get(QUEUE_TOPIC_OAASYNC),
                connectionNameStrategy);
    }

    @Bean(name = "oaAsyncRabbitTempe")
    @Primary
    public RabbitTemplate oaAsyncRabbitTemplate(
            MultipleRabbitProperties multipleRabbitProperties,
            @Qualifier("oaAsyncConnectionFactory") ConnectionFactory connectionFactory) {
        return RabbitFactory.createRabbitTemplate(
                multipleRabbitProperties.getMultiple().get(QUEUE_TOPIC_OAASYNC),
                connectionFactory);
    }

    @Bean(name = "oaAsyncListenerContainerFactory")
    @Primary
    public RabbitListenerContainerFactory oaAsyncListenerFactory(
            MultipleRabbitProperties multipleRabbitProperties,
            @Qualifier("oaAsyncConnectionFactory") ConnectionFactory connectionFactory) {
        return RabbitFactory.createRabbitListenerContainerFactory(
                multipleRabbitProperties.getMultiple().get(QUEUE_TOPIC_OAASYNC),
                connectionFactory);
    }

    @Bean(name = "oaListenerContainerFactory")
    public RabbitListenerContainerFactory oaListenerContainerFactory(
            MultipleRabbitProperties multipleRabbitProperties,
            @Qualifier("oaConnectionFactory") ConnectionFactory connectionFactory) {
        return RabbitFactory.createRabbitListenerContainerFactory(
                multipleRabbitProperties.getMultiple().get("oa"), connectionFactory);
    }

    @Bean(name = "oaConnectionFactory")
    public ConnectionFactory oaConnectionFactory(MultipleRabbitProperties multipleRabbitProperties
            , ObjectProvider<ConnectionNameStrategy> connectionNameStrategy) throws Exception {
        Map<String, RabbitProperties> multiple = multipleRabbitProperties.getMultiple();
        return RabbitFactory.createConnectionFactory(
                Optional.ofNullable(multiple.get("oa")).orElseGet(()->multiple.get(QUEUE_TOPIC_OAASYNC)),
                connectionNameStrategy);
    }

    /**
     * 手动ack的工厂
     * @param multipleRabbitProperties
     * @param connectionFactory
     * @return
     */
    @Bean(name = "oaAsyncManualListenerContainerFactory")
    public RabbitListenerContainerFactory oaAsyncManualListenerFactory(
            MultipleRabbitProperties multipleRabbitProperties,
            @Qualifier("oaAsyncConnectionFactory") ConnectionFactory connectionFactory) {
        RabbitListenerContainerFactory rlcf = RabbitFactory.createRabbitListenerContainerFactory(
                multipleRabbitProperties.getMultiple().get(QUEUE_TOPIC_OAASYNC), connectionFactory);
        if (rlcf instanceof AbstractRabbitListenerContainerFactory) {
            AbstractRabbitListenerContainerFactory arlcf = (AbstractRabbitListenerContainerFactory) rlcf;
            arlcf.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        }
        return rlcf;
    }

    @Bean(name = "oaRabbitAdmin")
    public RabbitAdmin oaRabbitAdmin(@Qualifier("oaConnectionFactory") ConnectionFactory connectionFactory) {
        return new RabbitAdmin(connectionFactory);
    }

    @Bean(name = "oaAsyncRabbitAdmin")
    public RabbitAdmin oaAsyncRabbitAdmin(@Qualifier("oaAsyncConnectionFactory") ConnectionFactory connectionFactory) {
        return new RabbitAdmin(connectionFactory);
    }
}
