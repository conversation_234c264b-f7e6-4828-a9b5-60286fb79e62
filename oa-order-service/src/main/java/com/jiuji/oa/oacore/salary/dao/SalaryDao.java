package com.jiuji.oa.oacore.salary.dao;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.salary.bo.dto.*;
import com.jiuji.oa.oacore.salary.entity.vo.RetrunOrderEx;
import com.jiuji.oa.orginfo.sysconfig.vo.SysConfigNameValueVO;
import com.jiuji.oa.salary.*;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/2/23
 * @description 自定义薪酬获取对应租户枚举数据 dao
 */
@Mapper
public interface SalaryDao {
    /**
     * 获取销售网上支付方式首信易枚举
     *
     * @return List<SysConfigNameValueVO>
     */
    List<SysConfigNameValueVO> getMarketingOnlineUPayMethod();

    /**
     * 获取销售网上支付方式阿里枚举
     *
     * @return List<SysConfigNameValueVO>
     */
    List<SysConfigNameValueVO> getMarketingOnlineAliPayMethod();

    /**
     * 获取销售网上支付方式微信枚举
     *
     * @return List<SysConfigNameValueVO>
     */
    List<SysConfigNameValueVO> getMarketingOnlineWeiXinPayMethod();

    /**
     * 获取销售网上支付方式威富通枚举
     *
     * @return List<SysConfigNameValueVO>
     */
    List<SysConfigNameValueVO> getMarketingOnlineSwiftPassMethod();

    /**
     * 获取运营商类型
     *
     * @return List<SysConfigNameValueVO>
     */
    List<SysConfigNameValueVO> getOperatorKind();

    /**
     * 获取销售派送方式
     *
     * @return List<SysConfigNameValueVO>
     */
    List<SysConfigNameValueVO> getOrderDeliveryList();

    /**
     * 获取销售订单数据 使用优惠券
     *
     * @param ch999Id   员工id
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 销售数据列表
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<SaleOrder> listSaleOrder(@Param("ch999Id") Integer ch999Id, @Param("startDate") LocalDateTime startDate,
                                  @Param("endDate") LocalDateTime endDate, @Param("mkcIds") List<Integer> mkcIds,
                                  @Param("oldMkcIds") List<Integer> oldMkcIds, @Param("isOpenCheck") Integer isOpenCheck,
                                  @Param("isJiuJi") boolean isJiuJi);
    /**
     * 获取回收订单数据
     *
     * @param ch999Id   员工id
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 销售数据列表
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<RetrunOrderEx> listReturnOrder(@Param("ch999Id") Integer ch999Id, @Param("startDate") LocalDateTime startDate,
                                        @Param("endDate") LocalDateTime endDate);

    /**
     * 获取售后订单数据
     *
     * @param ch999Id   员工id
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 销售数据列表
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<AfterOrder> listAfterOrder(@Param("ch999Id") Integer ch999Id, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 运营数据
     * @param ch999Id
     * @param startDate
     * @param endDate
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<OperatorOrder> listOperatorOrder(@Param("ch999Id") Integer ch999Id, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
    /**
     * 新机单
     * @param calculateSaleReq
     * @param isOpenCheck
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<SaleOrder> listSaleOrderNew(@Param("calculateSaleReq") CalculateSaleReq calculateSaleReq, @Param("isOpenCheck") Integer isOpenCheck);
    /**
     * 良品单
     * @param calculateSaleReq
     * @param isOpenCheck
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<SaleOrder> listSaleOrderGoodProduct(@Param("calculateSaleReq") CalculateSaleReq calculateSaleReq, @Param("isOpenCheck") Integer isOpenCheck, @Param("isJiuJi") boolean isJiuJi);
    /**
     * 一手优品
     * @param calculateSaleReq
     * @param isOpenCheck
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<SaleOrder> listSaleOrderBestProduct(@Param("calculateSaleReq") CalculateSaleReq calculateSaleReq, @Param("isOpenCheck") Integer isOpenCheck);
    /**
     * 回收单
     * @param calculateReturnReq
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<ReturnOrder> listReturnOrderReturn(@Param("calculateReturnReq") CalculateReturnReq calculateReturnReq,
                                            @Param("isJiuJi") boolean isJiuJi);
    /**
     * 帮卖单
     * @param calculateReturnReq
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<ReturnOrder> listReturnOrderHelpSell(@Param("calculateReturnReq") CalculateReturnReq calculateReturnReq, @Param("isJiuJi") boolean isJiuJi);
    /**
     * 查询回收毛利
     * @param calculateReturnReq
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<ReturnOrder> listReturnOrderProfit(@Param("calculateReturnReq") CalculateReturnReq calculateReturnReq, @Param("isJiuJi") boolean isJiuJi);
    /**
     * 软件在保
     * @param calculateAfterReq
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<AfterOrder> listAfterOrderSoftUnderWarranty(@Param("calculateAfterReq") CalculateAfterReq calculateAfterReq);
    /**
     * 软件不在保
     * @param calculateAfterReq
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<AfterOrder> listAfterOrderNotSoftUnderWarranty(@Param("calculateAfterReq") CalculateAfterReq calculateAfterReq);
    /**
     * 硬件接件
     * @param calculateAfterReq
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<AfterOrder> listAfterOrderHardFit(@Param("calculateAfterReq") CalculateAfterReq calculateAfterReq);
    /**
     * 运用商 按门门店查询
     * @param calculateOperatorReq
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<OperatorOrder> listOperatorOrderOperator(@Param("calculateOperatorReq") CalculateOperatorReq calculateOperatorReq);
    /**
     * 获取某个月所有用户的完成率列表
     * @param startDate
     * @param endDate
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    @Cached(expire = 23, timeUnit = TimeUnit.HOURS, cacheType = CacheType.REMOTE)
    List<UserCompletionRate> listUserCompletionRate(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 获取某个月所有用户的个人回收差异率
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 个人回收差异率
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    @Cached(name = "oaOrder.SalaryDao.getRecoverDiffRateDto", expire = 23, timeUnit = TimeUnit.HOURS, cacheType = CacheType.REMOTE, key = "#startDate+'-'+#endDate")
    List<RecoverDiffRateDto> getRecoverDiffRateDto(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("isRecoverTeacher") boolean isRecoverTeacher);

    /**
     * 获取某个月所有门店的完成率列表
     * @param startDate
     * @param endDate
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    @Cached(name = "order.salary.listAreaCompletionRate.20220905",expire = 23, timeUnit = TimeUnit.HOURS, cacheType = CacheType.REMOTE)
    List<AreaCompletionRate> listAreaCompletionRate(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
    /**
     * 获取订单支付方式
     *
     * @param subIds    订单列表
     * @param orderType 订单类型
     * @return 支付方式列表
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<PayType> getNewOrderPayType(@Param("subIds") List<Long> subIds, @Param("orderType") String orderType);
    /**
     * 获取租户是否开通考核毛利
     *
     * @return
     */
    Integer getCheckProfit(@Param("xtenant") Integer xtenant);
    /**
     * 获取新机单和一手优品单的支付方式
     *
     * @param newAndBestSaleOrders
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<SaleOrderPayType> newAndBestList(@Param("newAndBestSaleOrders") List<Integer> newAndBestSaleOrders);
    /**
     * 获取良品单的支付方式
     *
     * @param goodSaleOrders
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<SaleOrderPayType> goodList(@Param("goodSaleOrders") List<Integer> goodSaleOrders);
    /**
     * 获取良品单的支付方式
     * @param month month
     * @param endMonth endMonth
     * @param ch999Id ch999Id
     * @return RacingDataDto
     */
    List<RacingDataDto> listRacing(@Param("month") LocalDate month,@Param("endMonth") LocalDate endMonth, @Param("ch999Id") Integer ch999Id);
    /**
     * 获取手机品牌
     * @return
     */
    @DS("oanew")
    @Cached(name = "com.jiuji.oa.oacore.salary.dao.SalaryDao.getBrands.BrandDetailRes", expire = 1, timeUnit = TimeUnit.HOURS)
    List<BrandRes.BrandDetailRes> getBrands();
    /**
     * 获取手机品牌
     * @return
     */
    @DS("oanew")
    @Cached(name = "com.jiuji.oa.oacore.salary.dao.SalaryDao.getBrandsV1.BrandDetailRes", expire = 1, timeUnit = TimeUnit.HOURS)
    List<BrandRes.BrandDetailRes> getBrandsV1();

    /**
     * 获取售后真实毛利
     * @return list
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<AfterOrderTrueProfitVo> getAfterOrderTrueProfitVo(@Param("ch999Id") Integer ch999Id, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
    /**
     * 获取售后真实毛利
     * @return list
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<AfterOrderTrueProfitVo> getAfterOrderTrueProfitVoOfArea( @Param("req") AfterOrderTrueProfitReq req);

    /**
     * 获取订单收银方式
     * @param subIds
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<ShouYinSubPayDto> getShouYinSubPay(@Param("subIds") List<Integer> subIds);

    /**
     * 获取检测人的差异率
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return list
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    @Cached(name = "order.salary.listRecoverCheckUserDiffRate",expire = 23, timeUnit = TimeUnit.HOURS, cacheType = CacheType.REMOTE)
    List<RecoverCheckUserDiffRate> listRecoverCheckUserDiffRate(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 查询国补优惠记录
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return list
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    List<NationalSubCouponDto> getNationalSubCouponDto(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 获取销售任务
     * @param date
     * @param ch999Id
     * @param areaId
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW_REPORT)
    SalesTaskVO getSalesTaskVO(@Param("date") String date, @Param("ch999Id") Integer ch999Id, @Param("areaId") Integer areaId);
}
