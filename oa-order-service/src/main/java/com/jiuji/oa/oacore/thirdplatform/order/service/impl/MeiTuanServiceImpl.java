package com.jiuji.oa.oacore.thirdplatform.order.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.ch999.common.util.atlas.CoordinateUtil;
import com.ch999.common.util.vo.atlas.Coordinate;
import com.doudian.open.api.instantShopping_notifyPickingStatus.data.InstantShoppingNotifyPickingStatusData;
import com.doudian.open.api.instantShopping_notifyPickingStatus.param.InstantShoppingNotifyPickingStatusParam;
import com.jiuji.cloud.org.service.AreaInfoCloud;
import com.jiuji.cloud.org.vo.response.AreaOpeningRes;
import com.jiuji.infra.lmstfy.anotation.LmstfyConsume;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.oacore.cloud.InWcfCloud;
import com.jiuji.oa.oacore.cloud.bo.DeliveryInputWrapperReq;
import com.jiuji.oa.oacore.cloud.bo.WXSmsReceiverReq;
import com.jiuji.oa.oacore.common.config.lmstfy.LmstfyConfig;
import com.jiuji.oa.oacore.common.enums.OaMesTypeEnum;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.common.util.SpringContextUtil;
import com.jiuji.oa.oacore.electronicTicket.enums.DeliveryEnum;
import com.jiuji.oa.oacore.oaorder.enums.SubCheckEnum;
import com.jiuji.oa.oacore.oaorder.enums.SubTypeEnum;
import com.jiuji.oa.oacore.oaorder.enums.WuliuStatusEnum;
import com.jiuji.oa.oacore.oaorder.po.Ch999User;
import com.jiuji.oa.oacore.oaorder.po.Sub;
import com.jiuji.oa.oacore.oaorder.service.Ch999UserService;
import com.jiuji.oa.oacore.oaorder.service.SubService;
import com.jiuji.oa.oacore.thirdplatform.annotation.AddLogKind;
import com.jiuji.oa.oacore.thirdplatform.annotation.AddOrderLog;
import com.jiuji.oa.oacore.thirdplatform.common.ThirdPlatformCommonConst;
import com.jiuji.oa.oacore.thirdplatform.common.enums.LogTypeEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.PlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.TaobaoWorkCallbackStatusEnum;
import com.jiuji.oa.oacore.thirdplatform.common.util.DataUtils;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.DoudianFactory;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.MyAccessToken;
import com.jiuji.oa.oacore.thirdplatform.doudian.service.DefaultDouDianService;
import com.jiuji.oa.oacore.thirdplatform.oplog.po.MeituanJdWorkLog;
import com.jiuji.oa.oacore.thirdplatform.oplog.service.MeituanJdWorkLogService;
import com.jiuji.oa.oacore.thirdplatform.order.bo.*;
import com.jiuji.oa.oacore.thirdplatform.order.entity.AreaInfo;
import com.jiuji.oa.oacore.thirdplatform.order.entity.Order;
import com.jiuji.oa.oacore.thirdplatform.order.enums.GetErrorEnum;
import com.jiuji.oa.oacore.thirdplatform.order.enums.SaveErrorEnum;
import com.jiuji.oa.oacore.thirdplatform.order.service.*;
import com.jiuji.oa.oacore.thirdplatform.order.vo.*;
import com.jiuji.oa.oacore.thirdplatform.order.vo.meituan.ThirdDeliveryMeituanOrderVO;
import com.jiuji.oa.oacore.thirdplatform.productconfig.entity.ProductConfig;
import com.jiuji.oa.oacore.thirdplatform.productconfig.service.ProductConfigService;
import com.jiuji.oa.oacore.thirdplatform.order.vo.meituan.ThirdDeliveryMeituanOrderVO;
import com.jiuji.oa.oacore.thirdplatform.order.vo.NationalSupplementUpSnReq;
import com.jiuji.oa.oacore.thirdplatform.order.vo.NationalSupplementUpSnRes;
import com.jiuji.oa.oacore.thirdplatform.order.vo.SpuData;
import com.jiuji.oa.oacore.thirdplatform.productconfig.entity.ProductConfig;
import com.jiuji.oa.oacore.thirdplatform.productconfig.service.ProductConfigService;
import com.jiuji.oa.oacore.thirdplatform.stock.bo.StockRes;
import com.jiuji.oa.oacore.thirdplatform.store.entity.Store;
import com.jiuji.oa.oacore.thirdplatform.store.service.StoreService;
import com.jiuji.oa.oacore.thirdplatform.taobao.service.TaoBaoBizService;
import com.jiuji.oa.oacore.thirdplatform.yading.util.DateUtil;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCodeEnum;
import com.jiuji.tc.utils.common.TraceIdUtil;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.meitu.platform.lmstfy.Job;
import com.meitu.platform.lmstfy.client.LmstfyClient;
import com.meitu.platform.lmstfy.exception.LmstfyException;
import com.sankuai.meituan.shangou.open.sdk.response.SgOpenResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MeiTuanServiceImpl implements MeiTuanService {

    @Resource(name = "firstLmstfyClient")
    private LmstfyClient firstLmstfyClient;
    @Autowired
    private Ch999UserService ch999UserService;
    private static final Integer CLASSID = 119;
    @Resource
    private InWcfCloud inWcfCloud;
    @Resource
    private UserInfoClient userInfoClient;
    @Resource
    private SubLogsCloud subLogsCloud;
    @Resource
    private SubService subService;
    @Resource
    private OrderService orderService;
    @Resource
    private MeiTuanSDKService meiTuanSDKService;
    @Resource
    private SmsService smsService;
    @Resource
    private LogisticsStatusContext logisticsStatusContext;
    @Resource
    private IAreaInfoService areaInfoService;
    @Resource
    private StoreService storeService;
    @Resource
    private MeituanJdWorkLogService meituanJdWorkLogService;


    /**
     * 接受消息队列并且进行美团门店营业时间的同步
     *
     * @param message
     */
    @RabbitHandler
    @RabbitListener(bindings = {
            @QueueBinding(                  //exchange与队列的绑定
                    value = @Queue(value = "consumer_orderservice_meituan", admins = "oaRabbitAdmin"),         //绑定队列，如果没有指定队列名称，系统会给一个生成一个临时的队列名
                    exchange = @Exchange(value = "updateAreainfo", type = "fanout", durable = "false") //指定exchange的名称和类型
            )
    }, containerFactory = "oaListenerContainerFactory", admin = "oaRabbitAdmin")
    public void consumeOAMsg(Message message) {
        try {
            MDC.put(TraceIdUtil.TRACE_ID_KEY, TraceIdUtil.getTraceId());
            //判断之后九机做这个逻辑
            if (XtenantEnum.isJiujiXtenant()) {
                String areaId = new String(message.getBody());
                log.warn("门店营业时间同步美团消费MQ消息{}", areaId);
                //判断改areaId有没有在美团门店里面
                if (!StringUtils.isNumeric(areaId)) {
                    log.error("门店营业时间同步美团消费MQ消息不为数字：{}", areaId);
                    return;
                }
                //同步OA门店营业时间倒美团开放门店,判断是正式环境才进行功能操作
                if (SpringContextUtil.isProduce()) {
                    synchronizationBusinessHoursV2(areaId);
                    SpringUtil.getBean(StoreService.class).synchronizationAreaHourV1(areaId);
                }

            }
        } catch (Throwable e) {
            RRExceptionHandler.logError("门店营业时间同步美团", Dict.create().put("areaId", message.getBody()), e
                    , SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }finally {
            MDC.remove(TraceIdUtil.TRACE_ID_KEY);
        }
    }

    /**
     * 通过门店id进行门店营业时间的同步
     *
     * @param areaId
     */
    @Override
    public void synchronizationAreaHour(String areaId) {
        if (StringUtils.isNotEmpty(areaId)) {
            String[] split = areaId.split(",");
            if (split.length > 0) {
                for (String item : split) {
                    synchronizationBusinessHours(item);
                }
            }
        }
    }

    @Override
    public NationalSupplementUpSnRes nationalSupplementUpSn(NationalSupplementUpSnReq req) {
        NationalSupplementUpSnRes res = new NationalSupplementUpSnRes();

        try {
            // 调用查询接口获取是否需要上传SN码
            SgOpenResponse getResponse = meiTuanSDKService.orderIdentificationGetRequest(req.getOrder_id());
            if (getResponse == null || getResponse.getRequestResult() == null) {
                res.setSuccess(false);
                res.setMessage("查询SN码需求失败，返回为空");
                return res;
            }

            JSONObject result = JSON.parseObject(getResponse.getRequestResult());
            Integer resultCode = Optional.ofNullable(result.getInteger("result_code")).orElse(-1);
            // 处理查询接口的响应结果
            if (resultCode != 1) {
                res.setSuccess(false);
                String errorMsg = GetErrorEnum.getMessage(resultCode);
                res.setMessage(errorMsg);
                return res;
            }
            //更具ppid封装出 spuDataList
            Integer ppid = req.getPpid();
            ProductConfig productConfig = SpringUtil.getBean(ProductConfigService.class).lambdaQuery()
                    .eq(ProductConfig::getPpriceid, ppid)
                    .eq(ProductConfig::getPlatCode,ThirdPlatformCommonConst.THIRD_PLAT_MT)
                    .orderByDesc(ProductConfig::getId).list().stream().findFirst().orElse(null);
            if(ObjectUtil.isNull(productConfig)){
                res.setSuccess(false);
                res.setMessage("ppid:"+ppid+"商品查询为空");
                return res;
            } else {
                SpuData spuData = new SpuData();
                spuData.setApp_spu_code(productConfig.getProductCode());
                spuData.setSku_id(productConfig.getSkuId());
                req.setSpuDataList(Collections.singletonList(spuData));
            }
            // 查询成功，准备保存SN码
            SgOpenResponse saveResponse = meiTuanSDKService.orderIdentificationSaveRequest(req);
            if (saveResponse == null || saveResponse.getRequestResult() == null) {
                res.setSuccess(false);
                res.setMessage("保存SN码失败，返回为空");
                return res;
            }
            JSONObject saveResult = JSON.parseObject(saveResponse.getRequestResult());
            Integer saveResultCode = Optional.ofNullable(saveResult.getInteger("result_code")).orElse(-1);
            if (saveResultCode == 1) {
                res.setSuccess(true);
                res.setMessage("SN码上传成功");
                return res;
            } else {
                res.setSuccess(false);
                String data = Optional.ofNullable(saveResult.getString("data")).orElse("");
                if ("ng".equals(data)) {
                    JSONObject errorObj = saveResult.getJSONObject("error");
                    if (errorObj != null) {
                        Integer errorCode = errorObj.getInteger("code");
                        String errorMsg = errorObj.getString("msg");
                        res.setMessage(String.format("上传失败，错误码: %d, 错误信息: %s", errorCode, errorMsg));
                    } else {
                        res.setMessage("上传失败，未知错误");
                    }
                } else {
                    res.setMessage(Convert.toStr(saveResult.get("error_list")));
                }
                return res;
            }
        } catch (Exception e) {
            RRExceptionHandler.logError("美团上传SN码异常", req , e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            res.setSuccess(false);
            res.setMessage("美团上传SN码异常: " + e.getMessage());
            return res;
        }
    }

    @Override
    public String selectInvoice(SelectInvoiceReq req) {
        Long orderId = req.getOrder_id();
        List<Order> listOrderById = orderService.getOrderList(Convert.toStr(orderId));
        if(CollUtil.isEmpty(listOrderById)){
            throw new CustomizeException("订单查询为空");
        }
        String storeCode = listOrderById.get(NumberConstant.ZERO).getStoreCode();
        req.setApp_poi_code(storeCode);
        SgOpenResponse sgOpenResponse = new SgOpenResponse();
        try {
            sgOpenResponse = meiTuanSDKService.queryTipsRequest(req);
        } catch (Exception e) {
            RRExceptionHandler.logError("美团查询发票异常", req , e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
        return JSONUtil.toJsonStr(sgOpenResponse.getRequestResult());
    }

    @Override
    public NationalSupplementUpSnRes handleInvoice(HandleInvoiceReq req) {
        NationalSupplementUpSnRes res = new NationalSupplementUpSnRes();
        //更具ppid封装出 spuDataList
        Integer ppid = req.getPpid();
        ProductConfig productConfig = SpringUtil.getBean(ProductConfigService.class).lambdaQuery()
                .eq(ProductConfig::getPpriceid, ppid)
                .eq(ProductConfig::getPlatCode,ThirdPlatformCommonConst.THIRD_PLAT_MT)
                .orderByDesc(ProductConfig::getId).list().stream().findFirst().orElse(null);
        if(ObjectUtil.isNull(productConfig)){
            res.setSuccess(false);
            res.setMessage("ppid:"+ppid+"商品查询为空");
            return res;
        } else {
            SpuData spuData = new SpuData();
            spuData.setApp_spu_code(productConfig.getProductCode());
            spuData.setSku_id(productConfig.getSkuId());
            req.setSpuDataList(Collections.singletonList(spuData));
        }
        Long orderId = req.getOrder_id();
        List<Order> listOrderById = orderService.getOrderList(Convert.toStr(orderId));
        if(CollUtil.isEmpty(listOrderById)){
            res.setSuccess(false);
            res.setMessage("订单查询为空");
            return res;
        }
        String storeCode = listOrderById.get(NumberConstant.ZERO).getStoreCode();
        req.setApp_poi_code(storeCode);

        SgOpenResponse sgOpenResponse = new SgOpenResponse();
        try {
            sgOpenResponse = meiTuanSDKService.saveInvoiceRequest(req);
            if (sgOpenResponse == null || sgOpenResponse.getRequestResult() == null) {
                res.setSuccess(false);
                res.setMessage("美团发票上传失败，返回为空");
                return res;
            }
            JSONObject saveResult = JSON.parseObject(sgOpenResponse.getRequestResult());
            Integer saveResultCode = Optional.ofNullable(saveResult.getInteger("result_code")).orElse(-1);
            if (saveResultCode == 1) {
                res.setSuccess(true);
                res.setMessage("美团发票上传上传成功");
                return res;
            } else {
                res.setSuccess(false);
                String data = Optional.ofNullable(saveResult.getString("data")).orElse("");
                if ("ng".equals(data)) {
                    JSONObject errorObj = saveResult.getJSONObject("error");
                    if (errorObj != null) {
                        Integer errorCode = errorObj.getInteger("code");
                        String errorMsg = errorObj.getString("msg");
                        res.setMessage(String.format("美团发票上传失败，错误码: %d, 错误信息: %s", errorCode, errorMsg));
                    } else {
                        res.setMessage("美团发票上传失败，未知错误");
                    }
                } else {
                    res.setMessage(Convert.toStr(saveResult.get("error_list")));
                }
                String reason = String.format("美团外卖国补订单：%s 上传平台发票失败，原因:%s", orderId, Convert.toStr(saveResult.get("error_list")));
                String gotifyReceiver = inWcfCloud.getGotifyReceiver(new WXSmsReceiverReq(101));
                log.warn("获取推送人传入参数：{}，返回结果：{}", new WXSmsReceiverReq(101),gotifyReceiver);
                String cleanedJsonString = gotifyReceiver.replaceAll("^\"|\"$", "").replace("\\\"", "\"");
                R<List<String>> listResult = JSON.parseObject(cleanedJsonString, new TypeReference<R<List<String>>>() {
                });
                if (listResult != null && listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) {
                    List<String> ch999UserIds = listResult.getData();
                    if (CollectionUtils.isNotEmpty(ch999UserIds)) {
                        String userIds = ch999UserIds.stream().collect(Collectors.joining(","));
                        SpringUtil.getBean(SmsService.class).sendOaMsgTo9Ji(reason,userIds,OaMesTypeEnum.YCTZ.getCode().toString());
                    }
                }else {
                    throw new CustomizeException("获取推送人失败");
                }
                return res;
            }
        } catch (Exception e) {
            RRExceptionHandler.logError("美团发票上传异常", req , e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            res.setSuccess(false);
            res.setMessage("美团发票上传异常: " + e.getMessage());
        }
        return res;
    }

    /**
     * 同步OA门店营业时间倒美团开放门店
     *
     * @param areaId
     */
    @Override
    public void synchronizationBusinessHours(String areaId) {
        List<Store> list = storeService.lambdaQuery().eq(Store::getPlatCode, ThirdPlatformCommonConst.THIRD_PLAT_MT)
                .eq(Store::getIsEnable, Boolean.TRUE)
                .eq(Store::getAreaId, areaId)
                .list();
        //判断如果过推送门店不是美团营业门店那就不用进行营业时间的同步
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //获取门店信息
        AreaInfo areaInfo = areaInfoService.selectAreaInfoById(areaId);
        Optional.ofNullable(areaInfo).ifPresent(item -> {
            String hours = item.getHours();
            //解析门店的印业时间
            BusinessHoursDTO businessHoursDTO = DataUtils.computeHourField(hours);
            String startTime = businessHoursDTO.getStartTime();
            String endTime = DataUtils.getEndTime(businessHoursDTO.getEndTime());
            //如果isweb 为false那就说明该门店需要闭店，需要把闭店的信息同步给
            Boolean isweb = Optional.ofNullable(item.getIsweb()).orElse(Boolean.TRUE);
            //构造同步给美团的门店营业时间
            String shippingTime = "";
            //获取美团平台编码
            Store store = list.get(0);
            String storeCode = store.getStoreCode();
            CreateMeiTuanSystem createMeiTuanSystem = new CreateMeiTuanSystem();
            createMeiTuanSystem.setStoreCode(storeCode);
            createMeiTuanSystem.setTenantCode(store.getTenantCode());
            createMeiTuanSystem.setStoreName(item.getAreaName());
            //判断如果过isweb是false那就说明该门店在九机停业，那对应的美团也需要停业休息
            if (!isweb) {
                createMeiTuanSystem.setOpenLevel(NumberConstant.THREE);
                //数据同步美团
                synchronizationMeiTuan(createMeiTuanSystem);
                return;
            }
            //如果isweb是true并且门店营业开始时间和结束时间都不为空那就将时间同步给美团
            if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime) && isweb) {
                //拼接同步营业时间文案
                shippingTime = startTime + "-" + endTime;
                createMeiTuanSystem.setShippingTime(shippingTime)
                        .setOpenLevel(NumberConstant.ONE);
                //数据同步美团
                synchronizationMeiTuan(createMeiTuanSystem);
            }
        });
    }

    /**
     * 同步OA门店营业时间倒美团开放门店
     *
     * @param areaId
     */
    @Override
    public void synchronizationBusinessHoursV2(String areaId) {
        List<Store> list = storeService.lambdaQuery().eq(Store::getPlatCode, ThirdPlatformCommonConst.THIRD_PLAT_MT)
                .eq(Store::getIsEnable, Boolean.TRUE)
                .eq(Store::getAreaId, areaId)
                .list();
        //判断如果过推送门店不是美团营业门店那就不用进行营业时间的同步
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //获取门店信息
        AreaInfo areaInfo = areaInfoService.selectAreaInfoById(areaId);
        Optional.ofNullable(areaInfo).ifPresent(item -> {
            //如果isweb 为false那就说明该门店需要闭店，需要把闭店的信息同步给
            Boolean isweb = Optional.ofNullable(item.getIsweb()).orElse(Boolean.TRUE);
            //构造同步给美团的门店营业时间
            String shippingTime = "";
            //获取美团平台编码
            Store store = list.get(0);
            String storeCode = store.getStoreCode();
            CreateMeiTuanSystem createMeiTuanSystem = new CreateMeiTuanSystem();
            createMeiTuanSystem.setStoreCode(storeCode);
            createMeiTuanSystem.setTenantCode(store.getTenantCode());
            createMeiTuanSystem.setStoreName(item.getAreaName());
            //判断如果过isweb是false那就说明该门店在九机停业，那对应的美团也需要停业休息
            if (!isweb) {
                createMeiTuanSystem.setOpenLevel(NumberConstant.THREE);
                //数据同步美团
                synchronizationMeiTuan(createMeiTuanSystem);
                return;
            }

            //获取门店营业时间
            List<AreaOpeningRes.OpeningTime> hoursList = Optional.ofNullable(SpringUtil.getBean(AreaInfoCloud.class).getAreaOpeningTime(item.getId()))
                    .map(R::getData).map(AreaOpeningRes::getHoursList).orElse(null);
            if (CollectionUtils.isNotEmpty(hoursList)) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
                Map<Integer, AreaOpeningRes.OpeningTime> openingTimeMap = hoursList.stream().collect(Collectors.toMap(AreaOpeningRes.OpeningTime::getKey, Function.identity(), (v1, v2) -> v1));
                StringBuilder sb = new StringBuilder();
                //解析门店营业时间
                for (int i = 0; i < NumberConstant.SEVEN; i ++) {
                    Integer key = Convert.toInt(Math.pow(2, i));
                    AreaOpeningRes.OpeningTime openingTime = openingTimeMap.get(key);
                    if (Objects.isNull(openingTime) || Objects.isNull(openingTime.getDate1()) || Objects.isNull(openingTime.getDate2())) {
                        sb.append(";");
                    } else {
                        LocalTime endtime = openingTime.getDate2();
                        /*LocalTime maxEndtime = LocalTime.of(21, 30);
                        if (openingTime.getDate2().isAfter(maxEndtime)) {
                            // 如果大于21:30，则设置为21:30
                            endtime = maxEndtime;
                        }*/
                        sb.append(openingTime.getDate1().format(formatter)).append(StrUtil.DASHED).append(endtime.format(formatter)).append(";");
                    }
                }
                shippingTime = sb.toString();
                if (StringUtils.isNotBlank(shippingTime)) {
                    shippingTime = shippingTime.substring(0,shippingTime.length() - 1);
                    createMeiTuanSystem.setShippingTime(shippingTime).setOpenLevel(NumberConstant.ONE);
                    //数据同步美团
                    synchronizationMeiTuan(createMeiTuanSystem);
                }
            }
        });
    }

    /**
     * 通过门店id进行门店营业时间的同步
     *
     * @param areaId
     */
    @Override
    public void synchronizationAreaHourV2(String areaId) {
        if (StringUtils.isNotEmpty(areaId)) {
            //同步所有门店
            if ("all".equals(areaId)) {
                List<Store> list = storeService.lambdaQuery().eq(Store::getPlatCode, ThirdPlatformCommonConst.THIRD_PLAT_MT)
                        .eq(Store::getIsEnable, Boolean.TRUE)
                        .list();
                for (Store item : list) {
                    synchronizationBusinessHoursV2(Convert.toStr(item.getAreaId()));
                }
                return;
            }

            String[] split = areaId.split(",");
            for (String item : split) {
                synchronizationBusinessHoursV2(item);
            }
        }
    }


    /**
     * 同步美团门店营业时间
     *
     * @param createMeiTuanSystem
     */
    private void synchronizationMeiTuan(CreateMeiTuanSystem createMeiTuanSystem) {
        SgOpenResponse meiTuanSgOpenResponse = new SgOpenResponse();
        try {
            meiTuanSgOpenResponse = meiTuanSDKService.synchronizationBusinessHours(createMeiTuanSystem);
            log.info("同步美团营业时间，参数:{},结果：{}",JSONUtil.toJsonStr(createMeiTuanSystem), JSONUtil.toJsonStr(meiTuanSgOpenResponse));
        } catch (Exception e) {
            smsService.sendOaMsgTo9Ji(AddLogKind.SYNCHRONIZATION_BUSINESS_HOURS + "操作异常", "13495", OaMesTypeEnum.YCTZ.getCode().toString());
            log.error(AddLogKind.SYNCHRONIZATION_BUSINESS_HOURS + "操作异常", e);
        }
        String requestResult = meiTuanSgOpenResponse.getRequestResult();
        JSONObject result = JSON.parseObject(requestResult);
        String data = Optional.ofNullable(result.get("data")).orElse("").toString();
        MeituanJdWorkLog meituanJdWorkLog = new MeituanJdWorkLog();
        meituanJdWorkLog.setPlatform(PlatfromEnum.MT.getCode());
        meituanJdWorkLog.setLogType(LogTypeEnum.STORE_UPDATE.getCode());
        meituanJdWorkLog.setWorkName(LogTypeEnum.STORE_UPDATE.getMessage());
        meituanJdWorkLog.setTenantCode(createMeiTuanSystem.getTenantCode());
        meituanJdWorkLog.setCreateUser("系统");
        meituanJdWorkLog.setIsDel(Boolean.FALSE);
        String storeName = createMeiTuanSystem.getStoreName();
        if (data.equals(StockRes.RESULT_NG)) {
            meituanJdWorkLog.setWorkContent(storeName + "营业时间同步失败");
            //如果过同步失败进行消息推送
            Object msg = JSON.parseObject(Optional.ofNullable(result.get("error")).orElse("").toString()).get("msg");
            smsService.sendOaMsgTo9Ji(AddLogKind.SYNCHRONIZATION_BUSINESS_HOURS + ":" + msg, "13495", OaMesTypeEnum.YCTZ.getCode().toString());
        } else {
            meituanJdWorkLog.setWorkContent(storeName + "营业时间同步成功");
        }
        meituanJdWorkLogService.saveByLog(meituanJdWorkLog.getWorkName(), meituanJdWorkLog.getWorkContent(), meituanJdWorkLog.getCreateUser()
                , meituanJdWorkLog.getLogType(), meituanJdWorkLog.getPlatform(), meituanJdWorkLog.getTenantCode());
    }


    @Override
    public ShowMeiTuanOrderDetailBO getOrderDetail(SelectMeiTuanOrderDetailBO detailBO) {

        SgOpenResponse meiTuanSgOpenResponse = new SgOpenResponse();
        AtomicReference<String> msgRef = new AtomicReference<>();
        try {
            meiTuanSgOpenResponse = meiTuanSDKService.getMeiTuanSgOpenResponse(detailBO);
        } catch (Exception e) {
            RRExceptionHandler.logError("查询美团订单详情", detailBO, e, msgRef::set);
            throw new CustomizeException(msgRef.get());
        }
        String requestResult = Optional.ofNullable(meiTuanSgOpenResponse.getRequestResult()).orElseThrow(() -> new CustomizeException("查询美团订单详情为空"));
        ShowMeiTuanOrderDetailBO showMeiTuanOrderDetailBO = new ShowMeiTuanOrderDetailBO();
        JSONObject result = JSON.parseObject(requestResult);
        String data = Optional.ofNullable(result.get("data")).orElse("").toString();
        if (data.equals(StockRes.RESULT_NG)) {

            Object message = JSON.parseObject(Optional.ofNullable(result.get("error")).orElse("").toString()).get("msg");
            RRExceptionHandler.logError(StrUtil.format("查询美团订单详情结果: {}", message), Dict.create().set("detailBO", detailBO)
                    .set("result", result), null, msgRef::set);
            // 获取订单明细异常, 中断后续流程
            throw new CustomizeException(msgRef.get());
        } else {
            JSONObject json = JSON.parseObject(data);
            Integer logisticsId = (int) Optional.ofNullable(json.get("logistics_id")).orElse(Integer.MAX_VALUE);
            showMeiTuanOrderDetailBO.setLogisticsId(logisticsId);
            showMeiTuanOrderDetailBO.setDaySeqNum(json.getString("day_seq_num"));
        }
        log.warn(AddLogKind.MEITUAN_ORDER_DETAIL + "获取结果" + JSONObject.toJSONString(showMeiTuanOrderDetailBO));
        return showMeiTuanOrderDetailBO;
    }

    @Override
    public R<String> pickingCompleted(PreparationMealCompleteBO preparationMealCompleteBO) {
        if (Objects.equals(SubTypeEnum.DOU_YIN_HOUR.getCode(), preparationMealCompleteBO.getSubType())) {
            return douyinNotifyPickingStatus(preparationMealCompleteBO);
        }
        if (Objects.equals(SubTypeEnum.TAO_BAO.getCode(), preparationMealCompleteBO.getSubType())) {
            TaoBaoWorkCallbackBO taoBaoWorkCallbackBO = new TaoBaoWorkCallbackBO();
            taoBaoWorkCallbackBO.setOrderId(preparationMealCompleteBO.getOrderId());
            taoBaoWorkCallbackBO.setWorkCallbackStatus(TaobaoWorkCallbackStatusEnum.PACKAGED.getCode());
            R<String> workCallback = SpringUtil.getBean(TaoBaoBizService.class).taobaoWorkCallback(taoBaoWorkCallbackBO);
            if (!workCallback.isSuccess() && Objects.equals(ResultCodeEnum.RETURN_ERROR.getCode(), workCallback.getCode())) {
                RRExceptionHandler.logError("调用淘宝小时达仓配作业结果回传接口失败," + workCallback.getMsg(), taoBaoWorkCallbackBO, null, errorMsg -> {
                    SpringUtil.getBean(SmsService.class).sendOaMsgTo9JiMan(errorMsg);
                });
            }
            return workCallback;
        }

        SgOpenResponse meiTuanSgOpenResponse = new SgOpenResponse();
        try {
            meiTuanSgOpenResponse = meiTuanSDKService.preparationMealComplete(preparationMealCompleteBO);
        } catch (Exception e) {
            smsService.sendOaMsgTo9JiMan(AddLogKind.MEITUAN_PICKING_COMPLETE + "操作异常" + "美团订单号:" + preparationMealCompleteBO.getOrderId());
            log.error(AddLogKind.MEITUAN_PICKING_COMPLETE + "操作异常" + "美团订单号:" + preparationMealCompleteBO.getOrderId(), e);
        }
        String requestResult = Optional.ofNullable(meiTuanSgOpenResponse.getRequestResult()).orElseThrow(() -> new CustomizeException("美团订单拣选完成调用返回接口为空"));
        JSONObject result = JSON.parseObject(requestResult);
        String data = Optional.ofNullable(result.get("data")).orElse("").toString();
        if (data.equals(StockRes.RESULT_NG)) {
            Object message = JSON.parseObject(Optional.ofNullable(result.get("error")).orElse("").toString()).get("msg");
            return R.error(message + "");
        } else {
            return R.success("同步成功");
        }
    }

    /**
     * 抖音小时达拣货完成
     * @param preparationMealCompleteBO
     * @return
     */
    private R<String> douyinNotifyPickingStatus(PreparationMealCompleteBO preparationMealCompleteBO) {
        OrderLogisticsBO orderLogistics = orderService.getLogisticsByOrderId(preparationMealCompleteBO.getOrderId(), ThirdPlatformCommonConst.THIRD_PLAT_DY);
        if (Objects.isNull(orderLogistics)) {
            return R.error("查询订单物流信息为空，订单号:" + preparationMealCompleteBO.getOrderId());
        }
        if (Objects.equals(DeliveryEnum.DELIVERY_ONE.getCode(), orderLogistics.getDelivery())) {
            log.info("订单配送方式为到店自取，订单号:{}", preparationMealCompleteBO.getOrderId());
            return R.error("订单配送方式为到店自取，订单号:" + preparationMealCompleteBO.getOrderId());
        }
        MyAccessToken myAccessToken = SpringUtil.getBean(DoudianFactory.class).getMyAccessToken(Convert.toLong(orderLogistics.getTenantCode(), 0L));
        InstantShoppingNotifyPickingStatusParam param = new InstantShoppingNotifyPickingStatusParam();
        param.setShopOrderId(preparationMealCompleteBO.getOrderId());
        param.setNotifyType(2L);
        param.setStoreId(Convert.toLong(orderLogistics.getStoreId(), 0L));
        param.setUpdateTime(System.currentTimeMillis() / ThirdPlatformCommonConst.MIN_MILL);
        R<InstantShoppingNotifyPickingStatusData> orderLogisticsAddDataR = SpringUtil.getBean(DefaultDouDianService.class).notifyPickingStatus(myAccessToken, param);
        if (orderLogisticsAddDataR.isSuccess()) {
            return R.success("同步成功");
        } else {
            smsService.sendOaMsgTo9JiMan("抖音小时达订单拣货失败，原因：{}", orderLogisticsAddDataR.getUserMsg());
            return R.error(orderLogisticsAddDataR.getUserMsg());
        }
    }

    /**
     * 接受消息队列并且进行打印小票完成美团拣货同步
     *
     * @param message
     */
    @RabbitHandler
    @RabbitListener(bindings = {
            @QueueBinding(                  //exchange与队列的绑定
                    value = @Queue("consumer_orderservice_meituan_pick"),         //绑定队列，如果没有指定队列名称，系统会给一个生成一个临时的队列名
                    exchange = @Exchange(value = "meituanPrintPick", type = "fanout", durable = "false") //指定exchange的名称和类型
            )
    }, containerFactory = "oaListenerContainerFactory")
    private void consumeOAPickingCompletedMsg(Message message) {
        try {
            String orderId = new String(message.getBody());
            log.warn("美团打印小票消费MQ消息{}", orderId);
            //判断改areaId有没有在美团门店里面
            if (!StringUtils.isNumeric(orderId)) {
                log.error("美团打印小票消费MQ消息不为数字：{}", orderId);
                return;
            }

            List<Order> orderList = orderService.getOrderListBySubId(orderId);
            if (CollectionUtils.isEmpty(orderList)) {
                log.error("找不到美团订单号：{}", orderId);
                return;
            }
            //同步OA门店营业时间倒美团开放门店,判断是正式环境才进行功能操作
            if (SpringContextUtil.isProduce()) {
                PreparationMealCompleteBO vo = new PreparationMealCompleteBO();
                vo.setOrderId(orderId);
                try {
                    String publish = firstLmstfyClient.publish(LmstfyConfig.resolve(LmstfyConfig.MEITUAN_PRINT_PICK),
                            JSONUtil.toJsonStr(vo).getBytes(), 0, (short) 1, 120);
                    log.error("接受消息队列并且进行打印小票完成美团拣货同步推送成功，队列名称{}，推送参数{}，返回结果{}", LmstfyConfig.MEITUAN_PRINT_PICK
                            , JSONUtil.toJsonStr(vo), publish);
                } catch (LmstfyException e) {
                    log.error("接受消息队列并且进行打印小票完成美团拣货同步推送异常，队列名称{}，推送参数{}", LmstfyConfig.MEITUAN_PRINT_PICK, JSONUtil.toJsonStr(vo), e);
                }
            }
        } catch (Throwable e) {
            RRExceptionHandler.logError("美团打印小票", Dict.create().put("orderId", message.getBody()), e
                    , SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
    }


    @LmstfyConsume(queues = LmstfyConfig.MEITUAN_PRINT_PICK, clientBeanName = "firstLmstfyClient")
    public void consumeMeituanPrintPickQueue(Job job) {
        log.warn("美团小票队列消费到数据：{}", JSONUtil.toJsonStr(job));
        String data = Optional.ofNullable(job).orElse(new Job()).getData();
        if (StringUtils.isEmpty(data)) {
            log.warn("美团小票队列消费到数据为空");
            return;
        }
        PreparationMealCompleteBO vo;
        try {
            vo = JSONUtil.toBean(data, PreparationMealCompleteBO.class);
        } catch (Exception e) {
            log.error("美团小票队列反序列化失败，反序列数据为{}", data, e);
            return;
        }
        pickingCompleted(vo);
    }


    /**
     * @param params
     * @return
     */
    @Override
    @AddOrderLog(type = AddLogKind.DELIVERY_STATUS_SYNCHRONIZATION)
    public R deliverySynchronization(Map<String, Object> params) {
        Object statusObject = Optional.ofNullable(params.get("logistics_status")).orElseThrow(() -> new CustomizeException("logistics_status为空值"));
        if (!StringUtils.isNumeric(statusObject.toString())) {
            throw new CustomizeException("logistics_status不为数字");
        }
        int state = Integer.parseInt(statusObject.toString());
        //开启策略设计模式
        LogisticsStatusStrategy statusStrategy = logisticsStatusContext.getStatusStrategy(state);
        if (statusStrategy == null) {
            return new R(OrderRes.RESULT_OK);
        }
        //参数封装
        StatusStrategyBO statusStrategyBO = new StatusStrategyBO();
        statusStrategyBO.setDispatcherName(Optional.ofNullable(params.get("dispatcher_name")).orElse("").toString())
                .setOrderId(Optional.ofNullable(params.get("order_id")).orElse("0").toString())
                .setDispatcherMobile(Optional.ofNullable(params.get("dispatcher_mobile")).orElse("").toString());
        statusStrategy.handleOrderInfo(statusStrategyBO);
        return new R(OrderRes.RESULT_OK);
    }



    @Override
    @AddOrderLog(type = AddLogKind.MODIFY_ORDER_INFO_SYNCHRONIZATION)
    public R modifyOrderInfoSynchronization(Map<String, Object> params) {
        Object orderIdO = Optional.ofNullable(params.get("order_id")).orElseThrow(() -> new CustomizeException("order_id为空值"));
        if (!StringUtils.isNumeric(orderIdO.toString())) {
            throw new CustomizeException("order_id不为数字");
        }
        Long orderId = Convert.toLong(orderIdO);
        List<Order> orderList = orderService.getOrderListByOrderIdAndPlatCode(orderId.toString(), ThirdPlatformCommonConst.THIRD_PLAT_MT);
        if (CollUtil.isEmpty(orderList)){
            throw new CustomizeException("找不到该美团订单:"+orderId);
        }
        Order order = orderList.get(0);
        Sub sub = subService.getSub(Convert.toInt(order.getSubId()));
        if (Objects.isNull(sub)){
            throw new CustomizeException("找不到该美团订单:" + orderId);
        }
        StringBuilder message = new StringBuilder("美团订单").append(sub.getSubId());
        SgOpenResponse meiTuanSgOpenResponse = new SgOpenResponse();
        SelectMeiTuanOrderDetailBO detailBO = new SelectMeiTuanOrderDetailBO();
        detailBO.setOrderId(orderId.toString());
        AtomicReference<String> msgRef = new AtomicReference<>();
        try {
            meiTuanSgOpenResponse = meiTuanSDKService.getMeiTuanSgOpenResponse(detailBO);
        } catch (Exception e) {
            RRExceptionHandler.logError("查询美团订单详情", detailBO, e, msgRef::set);
            throw new CustomizeException(msgRef.get());
        }
        String requestResult = Optional.ofNullable(meiTuanSgOpenResponse.getRequestResult()).orElseThrow(() -> new CustomizeException("查询美团订单详情为空"));
        JSONObject result = JSON.parseObject(requestResult);
        String data = Optional.ofNullable(result.get("data")).orElse("").toString();
        Boolean modifyFlag = false;
        String logisticsCode = "";
        StringBuilder content = new StringBuilder("用户在平台侧修改配送信息为：");
        String buyerMobile = "";
        String buyerAddress = "";
        String recipientName = "";
        Integer cityId = null;
        Double longitude = null;
        Double latitude = null;
        LocalDateTime estimateArrivalTime = null;
        DeliveryInputWrapperReq.DeliveryInfoVO input = new DeliveryInputWrapperReq.DeliveryInfoVO();
        if (!data.equals(StockRes.RESULT_NG)) {
            JSONObject json = JSON.parseObject(data);
            logisticsCode = json.getString("logistics_code");
            buyerMobile = json.getString("recipient_phone");
            recipientName = json.getString("recipient_name");
            buyerAddress = json.getString("recipient_address");
            cityId = json.getInteger("city_id");
            latitude = json.getDouble("latitude");
            longitude = json.getDouble("longitude");
            estimateArrivalTime = DateUtil.getDateTimeOfTimestampSecond(json.getString("estimate_arrival_time"));
            input.setSubId(order.getSubId());
            input.setExpectTime(estimateArrivalTime.format(DateTimeFormatter.ofPattern("yyyy/M/d HH:mm:ss")));
            input.setCityId(cityId);
            input.setAddress(buyerAddress);
//            input.setDetailAddress(null);
            input.setSubUserName(recipientName);
            Coordinate coordinate = CoordinateUtil.gcj2wgs(new Coordinate(longitude + "," + latitude));
            input.setAddressGps(coordinate.toString());
            if (!Objects.equals(buyerMobile,order.getBuyerMobile())&&!buyerMobile.contains("*")){
                modifyFlag = true;
                content.append("号码").append(buyerMobile).append("，");
                input.setMobile(buyerMobile);
            }else {
                input.setMobile(order.getBuyerMobile());
            }
            if (!Objects.equals(recipientName,order.getBuyerName())){
                modifyFlag = true;
                content.append("收货人").append(recipientName).append("，");
            }
            if (!Objects.equals(buyerAddress,order.getBuyerAddress())){
                modifyFlag = true;
                content.append("地址").append(buyerAddress).append("，");
            }
            if (!Objects.equals(estimateArrivalTime,order.getEstimateArrivalTime().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime())){
                modifyFlag = true;
                content.append("送达时间").append(estimateArrivalTime).append("，");
            }
        }
        if (modifyFlag) {
            ThirdDeliveryMeituanOrderVO orderVO = orderService.selectThirdDeliveryOrderBySub(sub.getSubId());
            if (Objects.isNull(orderVO)) {
                return new R(OrderRes.RESULT_OK);
            }
            SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
            subLogsNewReq.setSubId(sub.getSubId());
            subLogsNewReq.setComment(content.toString());
            subLogsNewReq.setInUser("系统");
            subLogsNewReq.setDTime(LocalDateTime.now());
            subLogsNewReq.setShowType(false);
            subLogsNewReq.setType(1);
            subLogsCloud.addSubLog(subLogsNewReq);
            List<Integer> ch999UserId = new ArrayList<>();
            if (Objects.equals(logisticsCode,"0000")) {
                SubCheckEnum subCheckEnum = SubCheckEnum.getEnumByCode(sub.getSubCheck());
                if (Objects.isNull(subCheckEnum)) {
                    return new R(OrderRes.RESULT_OK);
                }
                switch (subCheckEnum) {
                    case SUB_CHECK_UNCONFIRMED:
                    case SUB_CHECK_CONFIRMED:
                        if (Arrays.asList(WuliuStatusEnum.WAITING_GETTING_GOODS.getCode(),WuliuStatusEnum.WAITING_GETTING_GOODS.getCode()).contains(orderVO.getStats())){
                            message.append("，顾客修改收货信息，请注意核对信息是否变更，按最新收货信息配送。");
                            DeliveryInputWrapperReq req = new DeliveryInputWrapperReq();
                            req.setInput(input);
                            String editResult = inWcfCloud.editThirdPlatformOrder(req);
                            log.info("编辑第三方订单信息 ,请求参数：{},结果：{}", JSONUtil.toJsonStr(req), editResult);
                        } else if (Objects.equals(WuliuStatusEnum.DELIVERING.getCode(), orderVO.getStats())){
                            message.append("，顾客修改收货信息，订单日志可查询，如已送出请及时拦截，未配送请更新收货信息重新呼叫跑腿，如有疑问联系小九助手");
                            if (StringUtils.isNotEmpty(orderVO.getPaijianren())){
                                //名字转为工号
                                Integer ch999Id = Optional.ofNullable(userInfoClient.getCh999UserByUserName(orderVO.getPaijianren()).getData())
                                        .map(Ch999UserVo::getCh999Id).orElse(null);
                                if (Objects.nonNull(ch999Id)){
                                    ch999UserId.add(ch999Id);
                                }
                            }
                        }
                        break;
                    case SUB_CHECK_OUT_OF_STOCK:
                        message.append("，顾客修改收货信息，订单日志可查询，请及时拦截货物，请更新收货信息重新呼叫跑腿，如有疑问联系小九助手。");
                        break;
                    case SUB_CHECK_COMPLETED:
                        message.append("异常，顾客在美团平台修改收货信息，请联系顾客处理，如有疑问联系小九助手。");
                        break;
                    default:
                        break;
                }
            }else {
                message = new StringBuilder();
            }
            List<Ch999User> ch999UsersList = ch999UserService.listCh999UsersByAreaId(order.getAreaId());
            List<Integer> ch999UsersIdList = ch999UsersList.stream().map(Ch999User::getCh999Id).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(message.toString())) {
                // 获取推送人员工号1
                String gotifyReceiver = inWcfCloud.getGotifyReceiver(new WXSmsReceiverReq(CLASSID));
                String cleanedJsonString = gotifyReceiver.replaceAll("^\"|\"$", "").replace("\\\"", "\"");
                R<List<String>> listResult = JSON.parseObject(cleanedJsonString, new TypeReference<R<List<String>>>() {
                });
                if (listResult != null && listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) {
                    List<String> ch999UserIds = listResult.getData();
                    if (CollectionUtils.isNotEmpty(ch999UserIds)) {
                        List<Integer> temp = ch999UserIds.stream().filter(NumberUtil::isNumber).map(Integer::parseInt).collect(Collectors.toList());
                        Collection<Integer> intersection = CollectionUtils.intersection(temp, ch999UsersIdList);
                        if (CollectionUtils.isNotEmpty(intersection)){
                            ch999UserId.addAll(intersection);
                        }
                    }
                    if (CollUtil.isNotEmpty(ch999UserId)) {
                        smsService.sendOaMsgTo9Ji(message.toString(), StringUtils.join(ch999UserId, StringPool.COMMA), OaMesTypeEnum.DDTZ.getCode().toString());
                    }
                }
            }
        }
        return new R(OrderRes.RESULT_OK);
    }

    @Override
    public void invoiceCall(Map<String, Object> parms) {
        try {
            Object status = Optional.ofNullable(parms.get("status")).orElseThrow(() -> new CustomizeException("status为空"));
            //不等于1 那就是失败
            if(!Convert.toInt(status).equals(NumberConstant.ONE)){
                String msg = Convert.toStr(Optional.ofNullable(parms.get("error_info_detail")).orElse(""));
                String msg1 = Convert.toStr(Optional.ofNullable(parms.get("failed_desc")).orElse(""));
                String orderId = Convert.toStr(Optional.ofNullable(parms.get("order_view_id")).orElseThrow(() -> new CustomizeException("order_view_id为空")));
                String reason = String.format("美团外卖国补订单：%s 上传平台发票失败，原因:%s", orderId, msg+msg1);
                String gotifyReceiver = inWcfCloud.getGotifyReceiver(new WXSmsReceiverReq(101));
                log.warn("获取推送人传入参数：{}，返回结果：{}", new WXSmsReceiverReq(101),gotifyReceiver);
                String cleanedJsonString = gotifyReceiver.replaceAll("^\"|\"$", "").replace("\\\"", "\"");
                R<List<String>> listResult = JSON.parseObject(cleanedJsonString, new TypeReference<R<List<String>>>() {
                });
                if (listResult != null && listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) {
                    List<String> ch999UserIds = listResult.getData();
                    if (CollectionUtils.isNotEmpty(ch999UserIds)) {
                        String userIds = ch999UserIds.stream().collect(Collectors.joining(","));
                        SpringUtil.getBean(SmsService.class).sendOaMsgTo9Ji(reason,userIds,OaMesTypeEnum.YCTZ.getCode().toString());
                    }
                }else {
                    throw new CustomizeException("获取推送人失败");
                }

            }
        }catch (Exception e){
            RRExceptionHandler.logError("发票回调异常", parms, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
    }


}
