package com.jiuji.oa.oacore.yearcardtransfer.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.config.rabbitmq.RabbitMqConfig;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.enums.MTableInfoEnum;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.oaorder.po.Basket;
import com.jiuji.oa.oacore.oaorder.po.BbsxpUsers;
import com.jiuji.oa.oacore.oaorder.service.BasketService;
import com.jiuji.oa.oacore.oaorder.service.BbsxpUsersService;
import com.jiuji.oa.oacore.yearcardtransfer.dto.YearPackageTransferCancelDto;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.oacore.yearcardtransfer.dto.YearPackageTransferDetailDto;
import com.jiuji.oa.oacore.yearcardtransfer.entity.TiemoCard;
import com.jiuji.oa.oacore.yearcardtransfer.entity.YearPackageTransferPo;
import com.jiuji.oa.oacore.yearcardtransfer.enums.YearPackageTransferStatusEnum;
import com.jiuji.oa.oacore.yearcardtransfer.mapper.YearPackageTransferMapper;
import com.jiuji.oa.oacore.yearcardtransfer.service.IYearPackageTransferLogService;
import com.jiuji.oa.oacore.yearcardtransfer.service.IYearPackageTransferService;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.oa.oacore.yearcardtransfer.service.TiemoCardService;
import com.jiuji.tc.utils.business.small.SmallproUtil;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class YearPackageTransferServiceImpl extends ServiceImpl<YearPackageTransferMapper, YearPackageTransferPo>
    implements IYearPackageTransferService {

    @Resource
    private BasketService basketService;
    @Resource
    private IYearPackageTransferLogService yearPackageTransferLogService;
    @Resource
    private BbsxpUsersService bbsxpUsersService;
    @Autowired
    private AmqpTemplate amqpTemplate;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private TiemoCardService tiemoCardService;

    @Override
    public YearPackageTransferDetailDto getTransferDetail(String transferCode, Integer userId) {
        YearPackageTransferDetailDto dto = baseMapper.selectTransferDetailByCode(transferCode, userId);
        if (dto == null) {
            throw new CustomizeException("无效的转赠码或无权访问");
        }
        dto.setMemberPrice(BigDecimal.ZERO);
        // 根据主商品查询销售的原价,兼容历史库
        if(dto.getBindBasketId() != null){
            Optional<Basket> basketOpt = CommonUtil.autoQueryHist(() -> basketService.lambdaQuery()
                    .eq(Basket::getBasketId, dto.getBindBasketId()).select(Basket::getBasketId, Basket::getPrice1)
                    .list().stream()
                    .findFirst(), MTableInfoEnum.BASKET, dto.getBindBasketId());
            basketOpt.ifPresent(b -> dto.setMemberPrice(b.getPrice1()));
        }

        // 待领取 已过期 状态修改为: 已返还
        LocalDateTime now = LocalDateTime.now();
        if(YearPackageTransferStatusEnum.PENDING_CLAIM.getCode().equals(dto.getStatus()) && !dto.isEffectiveAt(now)){
            dto.setStatus(YearPackageTransferStatusEnum.RETURNED.getCode());
        }
        // 待使用 已过期 状态修改为: 已过期
        if(YearPackageTransferStatusEnum.PENDING_USE.getCode().equals(dto.getStatus()) &&!dto.isEffectiveAt(now)){
            dto.setStatus(YearPackageTransferStatusEnum.EXPIRED.getCode());
        }

        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public YearPackageTransferDetailDto claimTransfer(String transferCode, Integer userId) {
        Optional<YearPackageTransferPo> yptOpt = lambdaQuery().eq(YearPackageTransferPo::getTransferCode, transferCode)
                .list().stream().findFirst();
        if(!yptOpt.isPresent()){
            throw new CustomizeException("无效的转赠码");
        }
        YearPackageTransferPo ypt = yptOpt.get();
        // 1. 状态必须是待领取状态
        if(!YearPackageTransferStatusEnum.PENDING_CLAIM.getCode().equals(ypt.getStatus())){
            throw new CustomizeException("该转赠码不是待领取状态");
        }
        // 2. 限制不能自己领取
        if(Objects.equals(userId, ypt.getSenderId())){
            throw new CustomizeException("不能领取自己的转赠码");
        }
        // 赠送还在有效期内
        LocalDateTime now = LocalDateTime.now();
        if (!ypt.isEffectiveAt(now)){
            throw new CustomizeException("该转赠码已超过领取时间");
        }
        TiemoCard tiemoCard = Optional.ofNullable(tiemoCardService.getById(ypt.getOriginCardId())).orElse(new TiemoCard());
        LocalDateTime etime = null;
        if(ObjectUtil.isNull(tiemoCard.getEtime())){
            etime = SmallproUtil.getEndTime(now, 30).withNano(0);
            RRExceptionHandler.logError("年包获取结束时间为空", transferCode, null, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        } else {
            etime =  tiemoCard.getEtime();
        }
        // 4. 提交的时候, 日志和领取状态要同时提交, 多事务工具类进行提交
        LambdaUpdateChainWrapper<YearPackageTransferPo> updateTransferChain = lambdaUpdate()
                .eq(YearPackageTransferPo::getId, ypt.getId())
                .eq(YearPackageTransferPo::getStatus, YearPackageTransferStatusEnum.PENDING_CLAIM.getCode())
                .set(YearPackageTransferPo::getReceiverId, userId)
                .set(YearPackageTransferPo::getStartTime, now)
                .set(YearPackageTransferPo::getEndTime,etime)
                .set(YearPackageTransferPo::getStatus, YearPackageTransferStatusEnum.PENDING_USE.getCode());
        // 获取当前会员信息
        String userName = bbsxpUsersService.lambdaQuery().eq(BbsxpUsers::getId, userId)
                .select(BbsxpUsers::getId, BbsxpUsers::getUserName, BbsxpUsers::getRealname)
                .list().stream().findFirst().map(u -> ObjectUtil.defaultIfBlank(u.getRealname(), u.getUserName()))
                .orElse(Convert.toStr(userId));
        MultipleTransaction.build()
                .execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
                    boolean updated =updateTransferChain.update();
                    if(!updated){
                        throw new CustomizeException("领取转赠码失败");
                    }
                })
                .execute(DataSourceConstants.OA_LOG, () -> {
                    yearPackageTransferLogService.saveLog(ypt.getId(), "系统", "用户[{}]领取赠礼", userName);
                })
                .commit();
        // 推送领取信息
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("act", "claim_year_package_transfer");
        jsonObject.put("data", ypt.getId());
        amqpTemplate.convertAndSend(RabbitMqConfig.QUEUE_TOPIC_OAASYNC, jsonObject.toString());
        return getTransferDetail(transferCode, userId);
    }

    @Override
    public List<YearPackageTransferDetailDto> listTransferDetail(Collection<Integer> bindBasketIds, Integer userId) {
        // todo 兼容历史库
        return baseMapper.listTransferDetail(bindBasketIds, userId);
    }

    @Override
    public R<Boolean> revokeTransfer(YearPackageTransferCancelDto req) {
        //统一调用C#的接口进行相关的修改处理 业务归口
        String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST)).map(R::getData)
                .filter(StringUtils::isNotEmpty).orElseThrow(() -> new CustomizeException("获取域名出错"));
        String cancelUrl = host + "/oaApi.svc/rest/CancelYearPackageTransfer";

        // 构建请求参数
        JSONObject requestBody = new JSONObject();
        requestBody.put("transferCode", req.getTransferCode());
        requestBody.put("userId", req.getUserId());

        HttpResponse cancelResult = HttpUtil.createPost(cancelUrl)
                .header("Content-Type", "application/json")
                .body(JSONUtil.toJsonStr(requestBody))
                .execute();
        log.warn("撤销年包转赠传入参数：{}，返回结果：{}", req, cancelResult.body());
        if(cancelResult.isOk()){
            // 解析返回结果
            R result = JSONUtil.toBean(JSONUtil.toJsonStr(cancelResult.body()), R.class);
            if(!result.isSuccess()){
                return R.error(result.getUserMsg());
            }
        } else {
            log.warn("撤销年包转赠调用异常传入参数：{}", cancelUrl);
            return R.error("撤销年包转赠接口调用异常");
        }
        return R.success("撤销成功");
    }
}
