package com.jiuji.oa.oacore.weborder.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.jiuji.oa.oacore.apollo.OrderAssistantMenuConfig;
import com.jiuji.oa.oacore.cloud.AfterCloud;
import com.jiuji.oa.oacore.common.constant.RedisKeyConstant;
import com.jiuji.oa.oacore.common.enums.ERecoverStateEnum;
import com.jiuji.oa.oacore.common.util.SaasManagerUtils;
import com.jiuji.oa.oacore.oaorder.enums.SubCheckEnum;
import com.jiuji.oa.oacore.oaorder.enums.SubTypeEnum;
import com.jiuji.oa.oacore.weborder.dao.OrderAssistantMapper;
import com.jiuji.oa.oacore.weborder.dto.OrderContextDTO;
import com.jiuji.oa.oacore.weborder.dto.OrderProductInfoDTO;
import com.jiuji.oa.oacore.weborder.dto.RepairOrderContextDTO;
import com.jiuji.oa.oacore.weborder.dto.RepairSmallOrderContextDTO;
import com.jiuji.oa.oacore.weborder.enums.OrderAssistantMenuEnum;
import com.jiuji.oa.oacore.weborder.req.OrderAssistantMeunReq;
import com.jiuji.oa.oacore.weborder.res.AppAssistantMenuDataVO;
import com.jiuji.oa.oacore.weborder.res.AppAssistantMenuResVO;
import com.jiuji.oa.oacore.weborder.res.AssistantMenuDataVO;
import com.jiuji.oa.oacore.weborder.res.AssistantMenuResVO;
import com.jiuji.oa.oacore.weborder.service.OrderAssistantMenuService;
import com.jiuji.oa.oacore.weborder.service.OrderAssistantService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @since 2020/4/9
 */
@Service
@Slf4j
public class OrderAssistantMenuServiceImpl implements OrderAssistantMenuService {
    @Resource
    private OrderAssistantMapper orderAssistantMapper;
    @Resource
    private OrderAssistantService orderAssistantService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource(name = "redisTemplate4")
    private RedisTemplate<String, String> redisTemplate4;
    @Resource
    private OrderAssistantMenuConfig orderAssistantMenuConfig;
    @Resource
    private AfterCloud afterCloud;

    private final static List<Integer> mineOrderCode=Arrays.asList(SubCheckEnum.SUB_CHECK_CONFIRMED.getCode()
            ,SubCheckEnum.SUB_CHECK_UNCONFIRMED.getCode()
            ,SubCheckEnum.SUB_CHECK_WAITING_CONFIRMATION.getCode());

    /**
     * app销售助手快捷消息胶囊
     *
     * @return
     */
    @Override
    public AppAssistantMenuResVO getAppAssistantMeuns(OrderAssistantMeunReq req) {
        AppAssistantMenuResVO res = new AppAssistantMenuResVO();
        res.setSalesSkillsLink(SaasManagerUtils.getMoaUrl() + orderAssistantMenuConfig.getSalesSkillsLink());
        List<AppAssistantMenuDataVO> appAssistantMenuList = new ArrayList<>(getAppAssistantMenuData(req));
        //APP 要求把msg替换成sendOrder
        if(CollectionUtils.isNotEmpty(appAssistantMenuList)){
            appAssistantMenuList.forEach(item->{
                item.setType("sendOrder");
                if (OrderAssistantMenuEnum.REPAIRSMALL_ASSISTANT_MENU5.getCode().equals(item.getId())) {
                    item.setType("actionLink");
                    String submitNotDiscountLink = String.format(orderAssistantMenuConfig.getSubmitNotDiscountLink(), SaasManagerUtils.getMoaUrl(), req.getOrderId());
                    item.setActionLink(submitNotDiscountLink);
                }
            });
        }
        res.setMainMenus(appAssistantMenuList);
        return res;
    }

    /**
     * 销售助手快捷消息胶囊
     *
     * @return
     */
    @Override
    public AssistantMenuResVO getAssistantMeuns(OrderAssistantMeunReq req) {
        AssistantMenuResVO res = new AssistantMenuResVO();
        List<AppAssistantMenuDataVO> appAssistantMenuList = getAppAssistantMenuData(req);
        Map<String, List<AppAssistantMenuDataVO>> menuMap = appAssistantMenuList.stream().collect(Collectors.groupingBy(AppAssistantMenuDataVO::getParentName));
        List<AssistantMenuDataVO> menusList = new ArrayList<>();
        menuMap.forEach((k, v) -> {
            AssistantMenuDataVO assistantMenuDataVO = LambdaBuild.create(AssistantMenuDataVO.class).set(AssistantMenuDataVO::setText, k).set(AssistantMenuDataVO::setChildren, v).build();
            menusList.add(assistantMenuDataVO);
        });
        if (CollectionUtils.isEmpty(menusList)) {
            AppAssistantMenuDataVO defaultMenu = OrderAssistantMenuEnum.getDefaultMenus();
            AssistantMenuDataVO assistantMenuDataVO = LambdaBuild.create(AssistantMenuDataVO.class)
                    .set(AssistantMenuDataVO::setText, defaultMenu.getParentName())
                    .set(AssistantMenuDataVO::setChildren, Collections.singletonList(defaultMenu)).build();
            menusList.add(assistantMenuDataVO);
        }
        res.setMainMenus(menusList);
        return res;
    }

    private List<AppAssistantMenuDataVO> getAppAssistantMenuData(OrderAssistantMeunReq req) {
        List<AppAssistantMenuDataVO> result = new ArrayList<>();
        List<String> filterOrderMenus = null;
        switch (req.getOrderType()) {
            case "mine":
                // 新机
                OrderContextDTO orderContext = orderAssistantMapper.getOrderContext(req);
                filterOrderMenus = filterOrderMenus(orderContext);
                break;
            case "repair":
                // 维修（售后）
                RepairOrderContextDTO repairOrderContext = orderAssistantMapper.getRepairOrderContext(req);
                filterOrderMenus = filterRepairMenus(repairOrderContext, req);
                break;
            case "repairSmall":
                // 维修（小件）
                RepairSmallOrderContextDTO repairSmallOrderContext = orderAssistantMapper.getRepairSmallOrderContext(req);
                filterOrderMenus = filterRepairSmallMenus(repairSmallOrderContext);
                break;
            case "recover":
                // 回收
                OrderContextDTO recoverOrderContext = orderAssistantMapper.getRecoverOrderContext(req);
                filterOrderMenus = filterRecoverMenus(recoverOrderContext);
                break;
            case "secondhand":
                // 良品
                OrderContextDTO secondhandOrderContext = orderAssistantMapper.getSecondhandOrderContext(req);
                filterOrderMenus = filterSecondhandMenus(secondhandOrderContext);
                break;
            default:
                break;
        }
        if (CollectionUtils.isEmpty(filterOrderMenus)) {
            return result;
        }
        List<AppAssistantMenuDataVO> allAssistantMenusList = OrderAssistantMenuEnum.getAssistantMenusByOrderType(req.getOrderType());
        List<String> finalFilterOrderMenus = filterOrderMenus;
        result = allAssistantMenusList.stream().filter(v -> finalFilterOrderMenus.contains(v.getText())).collect(Collectors.toList());
        return result;
    }

    private List<String> filterOrderMenus(OrderContextDTO orderContext) {
        List<String> menusList = new ArrayList<>();
        if (Objects.isNull(orderContext) || Objects.equals(1, orderContext.getIsBargain())) {
            return menusList;
        }
        Boolean isexceptionSub = stringRedisTemplate.opsForHash().hasKey("ExceptionSubRedisKey", ""+orderContext.getSubId());
        //订单被“异常锁定”
        if (Boolean.TRUE.equals(isexceptionSub)) {
            menusList.add("订单解锁");
        }
        //1.订单“有已提交的退订记录”
        //2.退订记录在“审核中”或“没有删除”
        if (Objects.equals(1,orderContext.getIsTuihuan())) {
            menusList.add("退订审核");
        }
        //订单商品信息
        List<OrderProductInfoDTO> orderInfoList = orderAssistantService.getXinjiOrderProduct(orderContext.getSubId());
        if (mineOrderCode.contains(orderContext.getSubCheck())) {
            //订单状态为“已确认”  切膜审核
            //商品分类包含622
            if (orderInfoList.stream().anyMatch(v -> Objects.equals(1, v.getIsCid662()))) {
                menusList.add("切膜审核");
            }
            if (Objects.equals(1, orderContext.getIsStaffSub())) {
                //订单状态为“已确认”且订单为“员工订单  内购改价
                menusList.add("内购改价");
                log.info("订单状态为“已确认”且订单为“员工订单");
            } else {
                //订单状态为“已确认”且订单为“非员工订单
                String menusStr = "特殊改价,删除订单,余额收银";
                menusList.addAll(Arrays.stream(menusStr.split(",")).collect(Collectors.toList()));
                log.info("订单状态为“已确认”且订单为“非员工订单");
            }

            //订单状态为“已确认”且订单为“含有小件商品”或者“订单不含小件商品，且订单关联以旧换新回收单”
            if (orderInfoList.stream().anyMatch(v -> Objects.equals(0, v.getIsmobile()))) {
                menusList.add("换新补贴改价");
                log.info("订单状态为“已确认”且订单为“含有小件商品");
            } else if (Objects.equals(1, orderContext.getIsRecoverSub())) {
                menusList.add("换新补贴改价");
                log.info("订单状态为“已确认”且订单不含小件商品，订单关联以旧换新回收单");
            }
            if(SubCheckEnum.SUB_CHECK_CONFIRMED.getCode().equals(orderContext.getSubCheck()) && Objects.equals(0, orderContext.getIsStaffSub()) ){
                menusList.addAll(Arrays.asList(OrderAssistantMenuEnum.ASSISTANT_MENU50.getName(),
                        OrderAssistantMenuEnum.ASSISTANT_MENU51.getName(),
                        OrderAssistantMenuEnum.ASSISTANT_MENU52.getName(),
                        OrderAssistantMenuEnum.ASSISTANT_MENU54.getName()
//                        OrderAssistantMenuEnum.ASSISTANT_MENU55.getName()
                ));
                //判断是否拥有取件码
                boolean hasPayKey = false;
                String switchValue = stringRedisTemplate.opsForValue().get(RedisKeyConstant.HASH_REDIS_SWITCH);
                if (StringUtils.isEmpty(switchValue) || "true".equals(switchValue)) {
                    hasPayKey = redisTemplate4.opsForHash().hasKey("paykey", "" + orderContext.getSubId());
                } else {
                    hasPayKey = stringRedisTemplate.opsForHash().hasKey("paykey", "" + orderContext.getSubId());
                }
                if (hasPayKey) {
                    menusList.add(OrderAssistantMenuEnum.ASSISTANT_MENU53.getName());
                }


            }
        } else if (SubCheckEnum.SUB_CHECK_COMPLETED.getCode().equals(orderContext.getSubCheck())) {
            String menusStr = "订单商品对调";
            menusList.addAll(Arrays.stream(menusStr.split(",")).collect(Collectors.toList()));
            log.info("订单状态为“已完成”");
            //订单状态为“已完成”
            //2.商品的ppid 包含商品id：65684
            if (orderInfoList.stream().anyMatch(v -> Objects.equals(65684, v.getProductId()))) {
                menusList.add("diy年包返销审核");
            }
            //1.订单状态为“已完成”
            //2.订单已经有关联发票
            if (Objects.equals(1, orderContext.getIsTaxpiao())) {
                String taxpiaoMenusStr = "发票审核,发票删除";
                menusList.addAll(Arrays.stream(taxpiaoMenusStr.split(",")).collect(Collectors.toList()));
            } else {
                //1.订单状态为“已完成”
                //2.订单没有关联发票
                menusList.add("发票开具");
            }

        } else if (SubCheckEnum.SUB_CHECK_OUT_OF_STOCK.getCode().equals(orderContext.getSubCheck())
                || SubCheckEnum.SUB_CHECK_ARREARS.getCode().equals(orderContext.getSubCheck())) {
            //订单状态为“已出库/欠款”且订单为“员工订单”
            if (Objects.equals(1, orderContext.getIsStaffSub())) {
                menusList.add("订单内购审核");
            }
            log.info("订单状态为“已出库/欠款”且订单为“员工订单");
            if(SubCheckEnum.SUB_CHECK_ARREARS.getCode().equals(orderContext.getSubCheck())){
                menusList.add(OrderAssistantMenuEnum.ASSISTANT_MENU56.getName());
            }
        }

        //订单的保护膜商品绑定了串号  串号解绑
        if (orderInfoList.stream().anyMatch(v -> Objects.equals(1, v.getIsBasketBindRecord()))) {
            menusList.add("串号解绑");
        }

        // 订单状态为【未确认、已确认、欠款、已出库】
        if(SubCheckEnum.SUB_CHECK_UNCONFIRMED.getCode().equals(orderContext.getSubCheck())
                || SubCheckEnum.SUB_CHECK_CONFIRMED.getCode().equals(orderContext.getSubCheck())
                || SubCheckEnum.SUB_CHECK_ARREARS.getCode().equals(orderContext.getSubCheck())
                || SubCheckEnum.SUB_CHECK_OUT_OF_STOCK.getCode().equals(orderContext.getSubCheck())){
            // 美团
            if(SubTypeEnum.MEI_TUAN.getCode().equals(orderContext.getSubType())) {
                menusList.add(OrderAssistantMenuEnum.ASSISTANT_MENU60.getName());
                menusList.add(OrderAssistantMenuEnum.ASSISTANT_MENU61.getName());
                menusList.add(OrderAssistantMenuEnum.ASSISTANT_MENU62.getName());
            }
            //京东
            if (SubTypeEnum.SUB_TYPE_JINGDONG.getCode().equals(orderContext.getSubType())) {
                menusList.add(OrderAssistantMenuEnum.ASSISTANT_MENU63.getName());
                menusList.add(OrderAssistantMenuEnum.ASSISTANT_MENU64.getName());
                menusList.add(OrderAssistantMenuEnum.ASSISTANT_MENU65.getName());
            }

            // 添加通用的
            menusList.add(OrderAssistantMenuEnum.ASSISTANT_MENU57.getName());
            menusList.add(OrderAssistantMenuEnum.ASSISTANT_MENU55.getName());
            menusList.add(OrderAssistantMenuEnum.ASSISTANT_MENU58.getName());
            menusList.add(OrderAssistantMenuEnum.ASSISTANT_MENU59.getName());
        }

        return menusList;
    }

    private List<String> filterRepairMenus(RepairOrderContextDTO orderContext, OrderAssistantMeunReq req) {
        List<String> menusList = new ArrayList<>();
        //售后大件组合退功能增加内部聊天功能
        boolean ishaun = "huan".equals(req.getRepairType());
        if(ishaun) {
            menusList.add("退换审核");
            return menusList;
        }
        if (Objects.isNull(orderContext)) {
            return menusList;
        }
        if (Objects.equals(0, orderContext.getIsquji())) {
            if (Objects.equals(1,orderContext.getIsStaffSub())) {
                //1.维修单“未取机”
                //2.维修单为“内部员工”订单
                menusList.add("员工维修改价");
            } else {
                //1.维修单“未取机”
                //2.维修单为“非内部员工”订单
                menusList.add("维修费用改价");
            }
            //1.维修单“未取机”
            //2.维修单为“出险九机服务”订单
            if (Optional.ofNullable(orderContext.getServiceType()).orElse(0) > 0) {
                menusList.add("九机服务出险授权");
            }
            //1.订单“有已提交的退订记录”
            //2.退订记录在“审核中”且“没有删除”
            if (Objects.equals(1,orderContext.getIstuiding())) {
                menusList.add("退订审核");
            }
            if (Objects.equals(1,orderContext.getIstuihuan())) {
                menusList.add("退换审核");
            }
        }
        //1.订单“有已提交的退维修费记录”
        //2.退订记录在“审核中”且“没有删除”
        //3.维修单已取机
        if (Objects.equals(1, orderContext.getIsquji()) && Objects.equals(1,orderContext.getIstuifei())) {
            menusList.add("退维修费审核");
        }
        //维修单【已取机】
        if(Objects.equals(1, orderContext.getIsquji())){
            menusList.add(OrderAssistantMenuEnum.REPAIR_ASSISTANT_MENU7.getName());
        }
        return menusList;
    }

    private List<String> filterRepairSmallMenus(RepairSmallOrderContextDTO orderContext) {
        List<String> menusList = new ArrayList<>();
        if (Objects.isNull(orderContext)) {
            return menusList;
        }
        //1.小件单“待验证”状态
        //2.小件单处理方式为“换货/退货”
        boolean tuihuan = Arrays.asList(2, 3).contains(Optional.ofNullable(orderContext.getKind()).orElse(0));
        if (StringUtils.isBlank(orderContext.getCodeMsg()) && tuihuan) {
            menusList.add("验证授权");
        }
        //1.小件单“已取件”状态
        //2.小件单处理方式为“换货/退货”
        //3.小件单的旧件为“待处理（没有转现/报废）”
        if (tuihuan && Objects.equals(1, orderContext.getStats()) && Objects.equals(1, orderContext.getIshandle())) {
            menusList.add("小件报废审核");
        }
        //1.小件单“未取件”状态
        //2.小件单处理方式为“换货/退货”
        //3.小件单“待换货审核”或者“有退款且没有审核完成的记录”
        boolean weiqujian = Objects.equals(0, orderContext.getStats());
        boolean tuihuanhuo = Objects.equals(1, orderContext.getIstui()) || Objects.equals(1, orderContext.getIshuan());
        if (tuihuan && weiqujian && tuihuanhuo) {
            menusList.add("小件退换审核");
        }
        //1.小件单“未取件”状态
        //2.小件单处理方式为“换货”
        //3.小件单“待换货审核”
        //4.小件订单的商品所属分类为“662”
        boolean huanhuo = Objects.equals(2, orderContext.getKind());
        boolean huanhuoshenhe = Objects.equals(1, orderContext.getIshuan());
        if (huanhuo && weiqujian && huanhuoshenhe && Objects.equals(1, orderContext.getIsCid662())) {
            menusList.add("切膜审核");
        }
        //1.小件售后单处理方式为退款
        //2.退款商品为服务类商品
        //3.退款申请没有进行中数据
        boolean tuihuo = Objects.equals(3, orderContext.getKind());
        if (tuihuo && Objects.isNull(orderContext.getStSmallproId())) {
            boolean isServiceProduct = Optional.ofNullable(afterCloud.getSmallproIsServiceProduct(orderContext.getSubId())).filter(R::isSuccess).map(v -> Boolean.TRUE.equals(v.getData())).orElse(false);
            if (isServiceProduct) {
                menusList.add("服务不折价退款");
            }
        }
        // 订单【已取件完成】
        if(Objects.equals(1, orderContext.getStats())){

            menusList.add(OrderAssistantMenuEnum.REPAIRSMALL_ASSISTANT_MENU6.getName());

            // 对应销售订单为【美团订单】
            if(SubTypeEnum.MEI_TUAN.getCode().equals(orderContext.getSubType())) {
                menusList.add(OrderAssistantMenuEnum.REPAIRSMALL_ASSISTANT_MENU7.getName());
            }
            // 对应销售订单为【京东订单】
            if(SubTypeEnum.SUB_TYPE_JINGDONG.getCode().equals(orderContext.getSubType())) {
                menusList.add(OrderAssistantMenuEnum.REPAIRSMALL_ASSISTANT_MENU8.getName());
            }
        }
        return menusList;
    }

    private List<String> filterRecoverMenus(OrderContextDTO orderContext) {
        List<String> menusList = new ArrayList<>();
        if (Objects.isNull(orderContext)) {
            return menusList;
        }
        //1.订单状态为“已确认”
        if (Objects.equals(ERecoverStateEnum.CONFIRMED.getCode(), orderContext.getSubCheck())) {
            menusList.add("回收报价");
        } else if (Objects.equals(ERecoverStateEnum.COMPLETED.getCode(), orderContext.getSubCheck())) {
            //订单状态为“已完成”
            menusList.add("回收赎回审核");
        } else if (Objects.equals(ERecoverStateEnum.STORAGED.getCode(), orderContext.getSubCheck())) {
            //订单状态为“已入库”
            menusList.add("回收打款");

            menusList.add(OrderAssistantMenuEnum.RECOVER_ASSISTANT_MENU4.getName());
        }
        return menusList;
    }

    private List<String> filterSecondhandMenus(OrderContextDTO orderContext) {
        List<String> menusList = new ArrayList<>();
        if (Objects.isNull(orderContext)) {
            return menusList;
        }
        Boolean isexceptionSub = stringRedisTemplate.opsForHash().hasKey("ExceptionLpRecoverSubRedisKey", ""+orderContext.getSubId());
        //订单被“异常锁定”
        if (Boolean.TRUE.equals(isexceptionSub)) {
            menusList.add("订单解锁");
        }
        //1.订单“有已提交的退订记录”
        //2.退订记录在“审核中”或“没有删除”
        if (Objects.equals(1,orderContext.getIsTuihuan())) {
            menusList.add("退订审核");
        }
        if (SubCheckEnum.SUB_CHECK_CONFIRMED.getCode().equals(orderContext.getSubCheck())) {
            if (Objects.equals(1, orderContext.getIsStaffSub())) {
                //订单状态为“已确认”且订单为“员工订单  内购改价
                menusList.add("内购改价");
                log.info("订单状态为“已确认”且订单为“员工订单");
            } else {
                //订单状态为“已确认”且订单为“非员工订单
                String menusStr = "特殊改价,删除订单,余额收银";
                menusList.addAll(Arrays.stream(menusStr.split(",")).collect(Collectors.toList()));
                log.info("订单状态为“已确认”且订单为“非员工订单");
            }

            //订单状态为“已确认”且订单关联以旧换新回收单”
            if (Objects.equals(1, orderContext.getIsRecoverSub())) {
                menusList.add("换新补贴改价");
                log.info("订单状态为“已确认”且订单不含小件商品，订单关联以旧换新回收单");
            }
        } else if (SubCheckEnum.SUB_CHECK_COMPLETED.getCode().equals(orderContext.getSubCheck())) {
            String menusStr = "订单商品对调";
            menusList.addAll(Arrays.stream(menusStr.split(",")).collect(Collectors.toList()));
            log.info("订单状态为“已完成”");
            //1.订单状态为“已完成”
            //2.订单已经有关联发票
            if (Objects.equals(1, orderContext.getIsTaxpiao())) {
                String taxpiaoMenusStr = "发票审核,发票删除";
                menusList.addAll(Arrays.stream(taxpiaoMenusStr.split(",")).collect(Collectors.toList()));
            } else {
                //1.订单状态为“已完成”
                //2.订单没有有关联发票
                menusList.add("发票开具");
            }

        } else if (SubCheckEnum.SUB_CHECK_OUT_OF_STOCK.getCode().equals(orderContext.getSubCheck())
                || SubCheckEnum.SUB_CHECK_ARREARS.getCode().equals(orderContext.getSubCheck())) {
            //订单状态为“已出库/欠款”且订单为“员工订单”
            if (Objects.equals(1, orderContext.getIsStaffSub())) {
                menusList.add("订单内购审核");
            }
            log.info("订单状态为“已出库/欠款”且订单为“员工订单");
        }

        // 订单状态为【未确认、已确认、欠款、已出库】
        if(SubCheckEnum.SUB_CHECK_UNCONFIRMED.getCode().equals(orderContext.getSubCheck())
                || SubCheckEnum.SUB_CHECK_CONFIRMED.getCode().equals(orderContext.getSubCheck())
            || SubCheckEnum.SUB_CHECK_ARREARS.getCode().equals(orderContext.getSubCheck())
                || SubCheckEnum.SUB_CHECK_OUT_OF_STOCK.getCode().equals(orderContext.getSubCheck())){
            menusList.add(OrderAssistantMenuEnum.SECONDHAND_ASSISTANT_MENU14.getName());
            menusList.add(OrderAssistantMenuEnum.SECONDHAND_ASSISTANT_MENU15.getName());
        }

        return menusList;
    }
}
