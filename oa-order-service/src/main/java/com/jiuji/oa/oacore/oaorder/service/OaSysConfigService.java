package com.jiuji.oa.oacore.oaorder.service;

import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.orginfo.sysconfig.vo.SysConfigVo;

import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * OA配置管理
 * <AUTHOR>
 */
public interface OaSysConfigService extends IService<SysConfigVo> {

    /**
     * 根据门店授权查询配置
     * @param authorizeId
     * @param areaId
     * @return
     */
    List<SysConfigVo> getListByAuthorizeIdAndAreaId(Integer authorizeId, Integer areaId);

    /**
     * 根据门店授权查询配置
     * @param authorizeId
     * @return
     */
    List<SysConfigVo> getListByAuthorizeId(Integer authorizeId);


    String getListByCode(Integer code);

    /**
     * 查询付款方式 10分钟缓存
     * @return
     */
    @Cached(name = "com.jiuji.oa.oacore.oaorder.service.OaSysConfigService.getPayWay", expire = 10, timeUnit = TimeUnit.MINUTES)
    List<SysConfigVo> getPayWay();

    /**
     * 根据code编辑配置内容
     *
     * @param code 配置code
     * @param value 配置内容
     * @return boolean
     */
    boolean updateValueByCode(Integer code, String value);

    /**
     * 查询sysconfig
     * @param code
     * @return
     */
    List<SysConfigVo> getListByCoode(Integer code);

    String getValueByCodeAndXtenant(Integer marketingChannel, Integer xTenant);
}
