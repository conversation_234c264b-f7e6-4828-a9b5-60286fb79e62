package com.jiuji.oa.oacore.thirdplatform.tenant.service.impl;


import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.constant.RedisKeyConstant;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.goldseed.service.AreaInfoService;
import com.jiuji.oa.oacore.oaorder.service.OaSysConfigService;
import com.jiuji.oa.oacore.thirdplatform.common.ThirdPlatformCommonConst;
import com.jiuji.oa.oacore.thirdplatform.common.enums.PlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.TuangouPlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.common.util.JsonUtils;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.DoudianFactory;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.MyAccessToken;
import com.jiuji.oa.oacore.thirdplatform.tenant.bo.TenantSearchBO;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.ThirdPlatformTenantUser;
import com.jiuji.oa.oacore.thirdplatform.tenant.mapper.TenantMapper;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.TenantService;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.ThirdPlatformTenantUserService;
import com.jiuji.oa.oacore.thirdplatform.tenant.vo.*;
import com.jiuji.oa.oacore.thirdplatform.tuangou.dto.TenantStoreDto;
import com.jiuji.oa.oacore.thirdplatform.tuangou.vo.MeituanTuangouToken;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.vo.SysConfigVo;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 商户配置接口实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TenantServiceImpl extends ServiceImpl<TenantMapper, Tenant> implements TenantService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private SmsService smsService;
    @Resource
    private ThirdPlatformTenantUserService thirdPlatformTenantUserService;
    @Resource
    private OaSysConfigService sysConfigService;
    @Resource
    private AreaInfoService areaInfoService;


    @Override
    public Tenant getOneTenantBy(String tenantCode, String platCode) {
        QueryWrapper<Tenant> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_code", tenantCode);
        queryWrapper.eq("plat_code", platCode);
        List<Tenant> list = list(queryWrapper);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }

    @Override
    public Page<TenantVO> listByPage(TenantSearchBO search) {
        OaUserBO currentStaffId = abstractCurrentRequestComponent.getCurrentStaffId();
        boolean mdspFlag = currentStaffId.getRank().contains("mdsp");
        Page<TenantVO> page = new Page<>(search.getCurrent(), search.getSize());
        if (ThirdPlatformCommonConst.THIRD_PLATTG.equalsIgnoreCase(search.getPlatCode())) {
            search.setPlatCodeList(TuangouPlatfromEnum.getAllTuangouPlatfromCode());
            search.setPlatCode("");
        }
        List<TenantVO> list = baseMapper.tenantList(search, page);
        if (CollectionUtils.isNotEmpty(list)) {
            page.setRecords(list);
            List<Integer> idList = list.stream().map(TenantVO::getId).collect(Collectors.toList());
            String platCode = list.get(0).getPlatCode();
            List<SysConfigVo> sysConfigVoList = sysConfigService.getListByCoode(SysConfigConstant.PLATE_FORM);
            Map<String, String> xtenantNameMap = sysConfigVoList.stream().collect(Collectors.toMap(SysConfigVo::getValue, SysConfigVo::getName));
            List<ThirdPlatformTenantUser> userList = thirdPlatformTenantUserService.getTenantUserListByTenantId(platCode,idList);
            Map<Integer, List<ThirdPlatformTenantUserVO>> tenantUserMap = userList.stream()
                    .map(v -> ThirdPlatformTenantUserVO.builder().id(v.getId())
                            .thirdPlatformTenantId(v.getThirdPlatformTenantId())
                            .userId(v.getUserId()).xtenant(Convert.toStr(v.getXtenant()))
                            .xtenantName(xtenantNameMap.get(Convert.toStr(v.getXtenant())))
                            .platKemu(v.getPlatKemu())
                            .refundKemu(v.getRefundKemu())
                            .venderKemu(v.getVenderKemu())
                            .governmentSubsidyKemu(v.getGovernmentSubsidyKemu())
                            .platCode(v.getPlatCode()).build())
                    .collect(Collectors.groupingBy(ThirdPlatformTenantUserVO::getThirdPlatformTenantId));
            for (TenantVO item : list) {
                item.setTenantUserList(tenantUserMap.get(item.getId()));
                item.setPlatform(PlatfromEnum.getCodeByName(item.getPlatCode()));
                item.setTokenExpirationDate(Optional.ofNullable(getExpirationTime(item)).map(v -> DateUtil.format(v, "yyyy-MM-dd HH:mm:ss")).orElse(""));
                if (!mdspFlag){
                    item.setAppKey("************");
                    item.setAppSecret("************");
                }
            }
        }
        return page;
    }

    @Override
    public Tenant getTenantBy(String platCode, String storeCode) {
        return baseMapper.getTenantByStore(platCode,storeCode);
    }

    /**
     * 获取应用信息
     *
     * @param platCode 平台代码
     * @param areaId   oa门店
     * @return
     */
    @Override
    @Cached(name = "orederservice.TenantService.getTenantByPlatCodeAndAreaId", expire = 10, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.REMOTE)
    public Tenant getTenantByPlatCodeAndAreaId(String platCode, Integer areaId) {
        return baseMapper.getTenantByPlatCodeAndAreaId(platCode,areaId);
    }

    /**
     * 获取应用信息
     *
     * @param platCode 平台代码
     * @param areaId   oa门店
     * @return
     */
    @Override
    @Cached(name = "orederservice.TenantService.getTenantStoreByPlatCodeAndAreaId", expire = 10, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.REMOTE)
    public TenantStoreDto getTenantStoreByPlatCodeAndAreaId(String platCode, Integer areaId) {
        return baseMapper.getTenantStoreByPlatCodeAndAreaId(platCode,areaId);
    }

    /**
     * 获取应用信息
     * @param req
     * @return
     */
    @Override
    public TenantStoreDto getTenantStore(TenantStoreReq req) {
        if (req.getAppType() == null) {
            TenantStoreDto tenantStore = SpringUtil.getBean(TenantService.class).getTenantStoreByPlatCodeAndAreaId(req.getPlatCode(), req.getAreaId());
            if (Objects.nonNull(tenantStore)) {
                return tenantStore;
            }
        }

        TenantStoreDto tenantStore = baseMapper.getTenantStore(req);
        //团购核券校验门店未配置或未启用的提示文案优化https://jira.9ji.com/browse/HS-10821?goToView=3
        if (Objects.isNull(tenantStore)) {
            AreaInfo areaInfo = areaInfoService.getAreaInfoById(req.getAreaId());
            if (areaInfo != null) {
                throw new CustomizeException(StrUtil.format("门店（{} {}）未配置，请联系运营核实配置信息~", areaInfo.getAreaName(), areaInfo.getArea()));
            } else {
                throw new CustomizeException(StrUtil.format("门店ID（{}）未配置，请联系运营核实配置信息~", req.getAreaId()));
            }
        }
        if (!Objects.equals(1,tenantStore.getStoreEnable())) {
            throw new CustomizeException(StrUtil.format("门店（{} {}）未启用，请联系运营核实配置信息~", tenantStore.getAreaName(), tenantStore.getArea()));
        }
        return tenantStore;
    }

    /**
     * 查询支付方式
     *
     * @param req
     * @return
     */
    @Override
    public List<String> getPaymentConfig(PaymentConfigReqVO req) {
        return baseMapper.getPaymentConfig(req);
    }

    /**
     * 平台编码和appkey查询配置
     *
     * @param platCode
     * @param appKey
     * @return
     */
    @Override
    public Tenant getTenantByPlatCodeAndAppkey(String platCode, String appKey) {
        return baseMapper.getTenantByPlatCodeAndAppkey(platCode,appKey);
    }

    /**
     * 平台编码和appkey查询配置
     * 忽略is_enable
     * req
     * @return
     */
    @Override
    public Tenant getTenantByPlatCodeAndAppkeyV2(String platCode, String appKey) {
        return baseMapper.getTenantByPlatCodeAndAppkeyV2(platCode, appKey);
    }

    /**
     * 查询token过期时间
     */
    private LocalDateTime getExpirationTime(TenantVO tenant) {
        try {
            PlatfromEnum platfromEnum = Arrays.stream(PlatfromEnum.values()).filter(v -> v.name().equalsIgnoreCase(tenant.getPlatCode())).findFirst().orElse(null);
            if (platfromEnum == null) {
                return null;
            }
            switch (platfromEnum) {
                case JD:
                case MT:
                case YT:
                case MI:
                    return null;
                case DY:
                    if (StringUtils.isNumeric(tenant.getTenantCode())) {
                        return Optional.ofNullable(SpringUtil.getBean(DoudianFactory.class).getMyAccessToken(Convert.toLong(tenant.getTenantCode(),0L)))
                                .map(MyAccessToken::getExpireTime).orElse(null);
                    } else {
                        return null;
                    }
                case MTTG:
                case DYTG:
                case KSTG:
                    String tokenKey = RedisKeyConstant.MEITUAN_DIANPIN_TOKEN + tenant.getAppKey();
                    if (PlatfromEnum.DYTG.equals(platfromEnum)) {
                        tokenKey = RedisKeyConstant.DOUYIN_TUANGOU_TOKEN + tenant.getAppKey();
                    } else if (PlatfromEnum.KSTG.equals(platfromEnum)) {
                        tokenKey = RedisKeyConstant.KUAI_SHOU_TOKEN + tenant.getAppKey();
                    }
                    String tokenStr = stringRedisTemplate.opsForValue().get(tokenKey);
                    if (StringUtils.isNotBlank(tokenStr)) {
                        MeituanTuangouToken meituanTuangouToken = JsonUtils.fromJson(tokenStr, MeituanTuangouToken.class);
                        if (meituanTuangouToken != null && meituanTuangouToken.getExpiresTime() != null) {
                            return meituanTuangouToken.getExpiresTime();
                        }
                    }
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            RRExceptionHandler.logError("获取三方平台token配置异常！", tenant, e, null);
        }
        return null;
    }
}
