package com.jiuji.oa.oacore.tousu.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.utils.DateTimeUtils;
import com.ch999.common.util.utils.XtenantJudgeUtil;
import com.google.common.collect.Lists;
import com.jiuji.cloud.office.service.MsgPushCloud;
import com.jiuji.cloud.office.service.ShortUrlCloud;
import com.jiuji.cloud.org.service.AreaInfoCloud;
import com.jiuji.cloud.org.vo.enums.RoleTermModuleEnum;
import com.jiuji.oa.nc.BaseCommentCloud;
import com.jiuji.oa.nc.MessagePushCloud;
import com.jiuji.oa.nc.OaMessageLogCloud;
import com.jiuji.oa.nc.StoreAreaInfoCloud;
import com.jiuji.oa.nc.comment.BaseCommentAddReq;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.bo.ZnSendConnBo;
import com.jiuji.oa.oacore.common.component.UserComponent;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.config.properties.ImageProperties;
import com.jiuji.oa.oacore.common.config.properties.JiujiSystemProperties;
import com.jiuji.oa.oacore.common.constant.UrlConstant;
import com.jiuji.oa.oacore.common.enums.EAreaLevelEnum;
import com.jiuji.oa.oacore.common.enums.EStatsEnum;
import com.jiuji.oa.oacore.common.enums.OaMesTypeEnum;
import com.jiuji.oa.oacore.common.req.TodoListMqVo;
import com.jiuji.oa.oacore.common.res.PageRes;
import com.jiuji.oa.oacore.common.source.MUrlSource;
import com.jiuji.oa.oacore.common.source.MoaUrlSource;
import com.jiuji.oa.oacore.common.util.*;
import com.jiuji.oa.oacore.common.util.profileutil.InnerProfileJudgeUtil;
import com.jiuji.oa.oacore.oaorder.dao.BbsxpUsersMapper;
import com.jiuji.oa.oacore.oaorder.dao.QudaoMapper;
import com.jiuji.oa.oacore.oaorder.enums.EUserClassNewEnum;
import com.jiuji.oa.oacore.oaorder.po.BbsxpUsers;
import com.jiuji.oa.oacore.oaorder.po.Ch999User;
import com.jiuji.oa.oacore.oaorder.po.Evaluate;
import com.jiuji.oa.oacore.oaorder.res.UserBO;
import com.jiuji.oa.oacore.oaorder.service.BbsxpUsersService;
import com.jiuji.oa.oacore.oaorder.service.Ch999UserService;
import com.jiuji.oa.oacore.oaorder.service.EvaluateService;
import com.jiuji.oa.oacore.partner.evaluate.util.SpringContext;
import com.jiuji.oa.oacore.thirdplatform.yading.constant.WebConstant;
import com.jiuji.oa.oacore.partner.feedback.enums.SmsMsgTypeEnum;
import com.jiuji.oa.oacore.thirdplatform.yading.constant.WebConstant;
import com.jiuji.oa.oacore.tousu.bo.*;
import com.jiuji.oa.oacore.tousu.constant.ComplainConstant;
import com.jiuji.oa.oacore.tousu.dao.AreaInfoMapper;
import com.jiuji.oa.oacore.tousu.dao.TouSuDepartMapper;
import com.jiuji.oa.oacore.tousu.dao.TouSuMapper;
import com.jiuji.oa.oacore.tousu.dao.TousuAreaMapper;
import com.jiuji.oa.oacore.tousu.entity.ComplainPushRelation;
import com.jiuji.oa.oacore.tousu.enums.*;
import com.jiuji.oa.oacore.tousu.po.*;
import com.jiuji.oa.oacore.tousu.req.HighOpinionReq;
import com.jiuji.oa.oacore.tousu.req.TousuPublicReq;
import com.jiuji.oa.oacore.tousu.res.*;
import com.jiuji.oa.oacore.tousu.service.*;
import com.jiuji.oa.oacore.tousu.vo.AnswerVO;
import com.jiuji.oa.oacore.tousu.vo.TousuAnswerVO;
import com.jiuji.oa.oacore.tousu.vo.req.*;
import com.jiuji.oa.oacore.tousu.vo.res.*;
import com.jiuji.oa.oacore.tousu.vo.res.CoupleBackInfoRes;
import com.jiuji.oa.oacore.tousu.vo.res.CoupleBackRes;
import com.jiuji.oa.oacore.tousu.vo.res.TouSuModelRes;
import com.jiuji.oa.operation.message.OaTousuMessageLogDTO;
import com.jiuji.oa.operation.message.enums.MessageLogSourceEnum;
import com.jiuji.oa.operation.outdoorstore.res.AreaFindVo;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.departinfo.client.DepartInfoClient;
import com.jiuji.oa.orginfo.departinfo.vo.DepartInfoVO;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.foundation.message.send.service.MessageSendService;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.constants.IntConstant;
import com.jiuji.tc.utils.constants.SignConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.area.AreaGradeEnum;
import com.jiuji.tc.utils.enums.office.BaseCommentRefTypeEnum;
import com.jiuji.tc.utils.xtenant.Namespaces;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-22
 */
@Slf4j
@Service
@DS("office")
public class TousuAreaServiceImpl extends ServiceImpl<TousuAreaMapper, TouSuModel> implements TousuAreaService {
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private TousuAreaMapper tousuAreaMapper;
    @Resource
    private TouSuMapper touSuMapper;
    @Resource
    private DepartInfoClient departInfoClient;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private BbsxpUsersMapper bbsxpUsersMapper;
    @Resource
    private MessageSendService sendService;
    @Autowired
    private TouSuServiceImpl touSuService;
    @Autowired
    private JiujiSystemProperties jiujiSystemProperties;
    @Resource
    private ImageProperties imageProperties;
    @Autowired
    private TouSuDepartService touSuDepartService;

    @Autowired
    private TousuAreaService tousuAreaService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private TousuCategoryRelationService tousuCategoryRelationService;
    @Autowired
    private BbsxpUsersService bbsxpUsersService;
    @Resource
    private QudaoMapper qudaoMapper;
    @Autowired
    private TousuAnswerService tousuAnswerService;
    @Resource
    private MUrlSource mUrlSource;
    @Autowired
    private StoreAreaInfoCloud storeAreaInfoCloud;

    @Autowired
    private AreaInfoMapper areaInfoMapper;
    @Resource
    private ComplainPushRelationService complainPushRelationService;

    @Resource
    private MessagePushCloud messagePushCloud;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private UserComponent userComponent;

    @Resource
    private MsgPushCloud msgPushCloud;
    @Resource
    private OaMessageLogCloud oaMessageLogCloud;
    @Resource
    private MoaUrlSource moaUrlSource;
    @Resource
    private TsProcessService processService;
    @Resource
    private ShortUrlCloud shortUrlCloud;
    @Resource
    private Ch999UserService ch999UserService;

    @Resource
    private AttachmentsService attachmentsService;

    @Resource
    private TouSuDepartMapper touSuDepartMapper;

    @Resource(name = "oaAsyncRabbitTempe")
    private RabbitTemplate oaAsyncRabbitTemplate;

    @Resource
    private AreaInfoCloud areaInfoCloud;

    @Resource
    private EvaluateService evaluateService;

    @Resource
    private TsProcessService tsProcessService;

    @Resource
    private BaseCommentCloud baseCommentCloud;

    @Resource
    private RedissonClient redissonClient;

    private static final String WEB_APP_PUSH_URL = "https://m.9ji.com/cloudapi_nc/pushGateway/api/pushGateway/pushMsg/v1?xservicename=push-gateway";
    private static final String WEB_APP_MSG_URL = "https://m.9ji.com/web/inApi/userMsg/insert";
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 投诉列表 新版
     *
     * @param touSuSearchReq
     * @return
     */
    @Override
    public Page<TouSuModelRes> touSuResListV2(TouSuModelReq touSuSearchReq) {
        OaUserBO userBO = userComponent.getOaUserBO(false);
        touSuSearchReq.setCurrentStaffId(userBO.getUserId());
        if (touSuSearchReq.getCurrent() == null || touSuSearchReq.getCurrent() == 0 || touSuSearchReq.getSize() == null) {
            touSuSearchReq.setCurrent(1);
            touSuSearchReq.setSize(10);
        }
        if (XtenantEnum.isJiujiXtenant() && touSuSearchReq.getOverFiveFlag() == null){
            touSuSearchReq.setOverFiveFlag(0);
        }
        Page<TouSuModelRes> page = new Page<>(touSuSearchReq.getCurrent(), touSuSearchReq.getSize());
        //SearchKind为8表示使用进程内容来搜索
        if (8 == touSuSearchReq.getSearchKind() && StringUtils.isNotEmpty(touSuSearchReq.getKey())) {
            //先搜索出包含关键字的进程内容对应的进程id
            // touSuSearchReq.setIds(getTouSuIdsByProcessContent(touSuSearchReq.getKey()));
            touSuSearchReq.setIdsStr(StringUtils.join(getTouSuIdsByProcessContent(touSuSearchReq.getKey()), ","));
        }

        //SearchKind=9表示某个责任人的所有投诉
        if (9 == touSuSearchReq.getSearchKind()) {
            if (StringUtils.isNotEmpty(touSuSearchReq.getKey())) {
                // 是否是责任人id字符串
                List<Integer> touSuIdsByZeRenRen;
                boolean isZeReRenIdStr = touSuSearchReq.getKey().matches("^[0-9]*$");
                if (isZeReRenIdStr) {
                    touSuIdsByZeRenRen = getTouSuIdsByZeRenRen(Integer.parseInt(touSuSearchReq.getKey()));
                } else {
                    touSuIdsByZeRenRen = getTouSuIdsByZeRenRen(touSuSearchReq.getKey());
                }
                if (CollectionUtils.isEmpty(touSuIdsByZeRenRen)) {
                    return page;
                }
                touSuSearchReq.setIds(touSuIdsByZeRenRen);
            }
        }
        // 投诉id
        if (6 == touSuSearchReq.getSearchKind() && StringUtils.isNotEmpty(touSuSearchReq.getKey())) {
            String[] split = touSuSearchReq.getKey().split(",");
            List<String> stringList = Arrays.asList(split);
            List<Integer> ids = stringList.stream().filter(t -> t.matches("^[0-9]*$")).
                    filter(StringUtils::isNotEmpty).map(Integer::valueOf).collect(Collectors.toList());
            touSuSearchReq.setIds(ids);

            // 投诉id查询时，忽略其他条件
            TouSuModelReq newReq = new TouSuModelReq();
            newReq.setSearchKind(touSuSearchReq.getSearchKind());
            newReq.setKey(touSuSearchReq.getKey());
            newReq.setIds(ids);
            newReq.setExportFlag(touSuSearchReq.getExportFlag());
            touSuSearchReq = newReq;
        } else {
            OaUserBO oaUserBo = userComponent.getOaUserBO(false);
            boolean resetTime = BusinessUtil.checkDataViewScope(RoleTermModuleEnum.STORE_OPERATION, oaUserBo.getToken(),
                    touSuSearchReq.getStartTime(), touSuSearchReq.getEndTime(), touSuSearchReq::setStartTime, touSuSearchReq::setEndTime);
            if (resetTime && Objects.isNull(touSuSearchReq.getDateType())) {
                touSuSearchReq.setDateType(1);
            }
        }
        List<TouSuModelRes> tsList;

        if (CollectionUtils.isNotEmpty(touSuSearchReq.getAreaIdM())) {
            List<Integer> areaIds = getQueryAreaIds(touSuSearchReq.getAreaIdM());
            touSuSearchReq.setAreaIds(areaIds);
        } else if ("jiuji".equals(SpringContext.getActiveProfile())) {
            //不为空时c#统一对地区选项做了筛选，java端针对地区为空时税务模式调整即可
            //手否开启了税务模式
            if ( userBO.getIsTaxModel() != null && userBO.getIsTaxModel()){
                AreaFindVo findVo = new AreaFindVo();
                findVo.setIsTaxModel(userBO.getIsTaxModel());
                findVo.setTaxCompanyId(userBO.getTaxCompanyId());
                List<Integer> list = storeAreaInfoCloud.idsByCondition(findVo).getData();
                if (CollectionUtils.isEmpty(list)){
                    return page;
                }
                touSuSearchReq.setAreaIds(list);
            }
        }
        // 税务模式
        if (ActiveProfileJudgeUtil.isJiuJiEnvironment()) {
            List<Integer> taxCompanyIdList = Collections.emptyList();
            R<List<Integer>> taxResult = areaInfoCloud.listTaxCompanyIdByToken(userBO.getToken());
            if (CommonUtils.isRequestSuccess(taxResult)) {
                taxCompanyIdList = taxResult.getData();
            }
            boolean isTaxModel = CollectionUtils.isNotEmpty(taxCompanyIdList);
            R<List<AreaInfo>> listAreaResult = areaInfoClient.listAll();
            if (CommonUtils.isRequestSuccess(listAreaResult)
                    && Boolean.TRUE.equals(isTaxModel)) {
                List<Integer> finalTaxCompanyIdList = taxCompanyIdList;
                List<Integer> taxModelAreaIdList = listAreaResult.getData()
                        .stream()
                        .filter(item -> {
                            if (CollectionUtils.isEmpty(finalTaxCompanyIdList)) {
                                return Objects.nonNull(item.getTaxpayer())
                                        && item.getTaxpayer().equals(1);
                            }
                            return Objects.nonNull(item.getTaxpayer())
                                    && item.getTaxpayer().equals(1)
                                    && Objects.nonNull(item.getMainCompany())
                                    && finalTaxCompanyIdList.contains(item.getMainCompany());
                        })
                        .map(AreaInfo::getId)
                        .distinct()
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(touSuSearchReq.getAreaIds())) {
                    taxModelAreaIdList.retainAll(touSuSearchReq.getAreaIds());
                }
                touSuSearchReq.setAreaIds(taxModelAreaIdList);
            }
        }

        // 修复数据隔离不生效
        Integer xtenant = userComponent.getXtenant(false, userBO);
        if (xtenant > 1000) {
            // 输出数据隔离
            touSuSearchReq.setXtenant(xtenant);
        }

        // 有gjkp（高级客评）和99权限的可以看已删除投诉
        if ( userBO.getRank() != null && (userBO.getRank().contains("gjkp") || userBO.getRank().contains("99"))) {
            touSuSearchReq.setContainDel(true);
            touSuSearchReq.setHighRankFlag(1);
        }else {
            touSuSearchReq.setContainDel(false);
        }
        Long count = tousuAreaMapper.getListCount(jiujiSystemProperties.getOfficeName(), touSuSearchReq);
        if (count == 0) {
            return page;
        }
        tsList = tousuAreaMapper.touSuResList(jiujiSystemProperties.getOfficeName(), touSuSearchReq, (page.getCurrent() - 1) * page.getSize(), page.getSize());
//        获取进程
        List<Integer> ids = tsList.stream().map(TouSuModelRes::getTsId).collect(Collectors.toList());
        List<Integer> userIds = tsList.stream().map(TouSuModelRes::getUserId).collect(Collectors.toList());
        String userIdStr = org.apache.commons.lang3.StringUtils.join(userIds, ",");
        List<UserBO> userList = bbsxpUsersService.getUserList(userIdStr);
        Map<Integer, Boolean> judgeUserSpecialMap = bbsxpUsersService.judgeUserSpecial(userIds);
        Map<Integer, Integer> growthMap = userList.stream().collect(Collectors.toMap(UserBO::getUserid, UserBO::getGrowth));
        List<TsProcess> processList = processService.list(Wrappers.<TsProcess>lambdaQuery()
                .select(TsProcess::getId, TsProcess::getTsId)
                .in(TsProcess::getTsId, ids)
                .eq(TsProcess::getFakeLog, Boolean.TRUE));
        Map<Integer, List<TsProcess>> processMap = processList.stream().collect(Collectors.groupingBy(TsProcess::getTsId));
//         责任门店
        Map<Integer, List<TouSuDepartRes>> zeRenAreasAndDepartsMap = getTouSuDepartsByTsIds(ids, touSuSearchReq.getExportFlag());
        R<List<AreaInfo>> areaInfolist = areaInfoClient.listAll();
        boolean needCateName = !Boolean.TRUE.equals(touSuSearchReq.getExportFlag());
        tsList.forEach(ts -> {
            if (CollectionUtils.isNotEmpty(zeRenAreasAndDepartsMap) && CollectionUtils.isNotEmpty(zeRenAreasAndDepartsMap.get(ts.getTsId()))) {
//                每条投诉的门店、和部门
                List<TouSuDepartRes> zeRenAreas = zeRenAreasAndDepartsMap.get(ts.getTsId());
                ts.setZenRenAreasAndDeparts(zeRenAreas);
                if (ts.getAreaId() != null && areaInfolist.getCode() == ResultCode.SUCCESS && areaInfolist.getData() != null) {
                    List<AreaInfo> areas = areaInfolist.getData().stream().filter(h -> h.getId().equals(ts.getAreaId())).collect(Collectors.toList());
                    ts.setArea(areas.size() != 0 ? areas.get(0).getArea() : "");
                }
                if (needCateName) {
                    ts.setCat(zeRenAreas.get(0).getCat());
                    if (ComplaintCatEnum.SHARE.getCode().equals(zeRenAreas.get(0).getCat())) {
                        ts.setCatName("警示投诉");
                    } else {
                        ts.setCatName(ComplaintCatEnum.of(zeRenAreas.get(0).getCat()) == null ? "" : ComplaintCatEnum.of(zeRenAreas.get(0).getCat()).getMessage());
                    }
                }
            }
            ts.setUserClassName(EnumUtil.getMessageByCode(EUserClassNewEnum.class, ts.getUserClass()));
            ts.setTsTypeStr(EnumUtil.getMessageByCode(TsTypeEnum.class, ts.getTsType()));
            ts.setMemberStarLevel(userComponent.getUserStarLevel(growthMap.getOrDefault(ts.getUserId(), null)));
            ts.setXtenantName(XtenantEnum.getMessageByCode(ts.getXtenant()));
            ts.setFakeLog(CollectionUtils.isNotEmpty(processMap.get(ts.getTsId())));
            ts.setSpecialFlag(judgeUserSpecialMap.get(ts.getUserId()));
            if (Boolean.TRUE.equals(ts.getSpecialFlag())) {
                ts.setSpecialIcon(ComplainConstant.SPECIAL_USER_ICON);
            }
        });
        page.setTotal(count);
        page.setRecords(tsList);

        return page;
    }

    @Override
    public void touSuResListExportV2(HttpServletResponse response, TouSuModelReq touSuReq) {
        boolean jiuJiEnv = ActiveProfileJudgeUtil.isJiuJiEnvironment();
        touSuReq.setCurrent(1);
        touSuReq.setSize(2000);
        touSuReq.setExportFlag(Boolean.TRUE);
        Page<TouSuModelRes> touSuModelResPage = touSuResListV2(touSuReq);
        dutyAreaDepart(touSuModelResPage, jiuJiEnv);
        String fileName = "投诉列表导出";
        EasyExcelUtil.initExcept(fileName, response, ActiveProfileJudgeUtil.isJiuJiEnvironment() ? TouSuModelExportJiuji.class : TouSuModelExportOutput.class);
        EasyExcelUtil.export(touSuModelResPage.getRecords(), "", 1);
        int pageTotal = new BigDecimal(touSuModelResPage.getTotal())
                .divide(new BigDecimal(touSuModelResPage.getSize()), RoundingMode.HALF_UP).intValue();
        while (touSuModelResPage.getCurrent() < pageTotal) {
            touSuModelResPage.setCurrent(touSuModelResPage.getCurrent() + 1);
            Page<TouSuModelRes> touSuModelResPage1 = touSuResListV2(touSuReq);
            dutyAreaDepart(touSuModelResPage1, jiuJiEnv);
            EasyExcelUtil.export(touSuModelResPage1.getRecords(), "", 1);
        }
        EasyExcelUtil.finishExport();
    }

    /**
     * 导出责任区域、部门、责任人、投诉原因封装
     * @param touSuModelResPage 查询数据
     */
    public void dutyAreaDepart(Page<TouSuModelRes> touSuModelResPage, boolean jiuJiEnv){
        if (touSuModelResPage != null && CollectionUtils.isNotEmpty(touSuModelResPage.getRecords())){
            List<Integer> ids = touSuModelResPage.getRecords().stream().map(TouSuModelRes::getTsId).collect(Collectors.toList());
            List<ComplainReward> complainRewards = complainPushRelationService.getComplainRewardByComplainId(ids);
            Map<Integer, BigDecimal> rewardMap = Optional.ofNullable(complainRewards).orElse(Lists.newArrayList())
                    .stream()
                    .collect(Collectors.toMap(ComplainReward::getComplainId, ComplainReward::getReward, (t1, t2) -> t1));
            //投诉责任人
            Map<Integer,String> dutyMap = baseMapper.getTousuZeRenRens(ids).stream()
                    .collect(Collectors.toMap(TouSuZenRenRen::getTousuId,
                            item -> getDutyUserValue(item, jiuJiEnv),
                            (t1, t2)->t1+ "、"+ t2));
            List<TouSuModelRes> suModelRes = new ArrayList<>();
            // 处理人
            new HashSet<String>();
            Map<Integer, String> dealUserMap = MapUtil.newHashMap();
            Map<Integer, String> dealUserIdMap = touSuModelResPage.getRecords().stream().filter(ts -> StringUtils.isNotBlank(ts.getDealUserIds())).collect(Collectors.toMap(TouSuModelRes::getTsId, TouSuModelRes::getDealUserIds));
            Set<Integer> staffIds = new HashSet<>();
            dealUserIdMap.values().forEach(dealUserIds -> staffIds.addAll(Arrays.stream(StringUtils.split(dealUserIds, ",")).filter(StringUtils::isNumeric).map(Integer::parseInt).collect(Collectors.toList())));
            List<Ch999User> staffList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(staffIds)) {
                List<List<Integer>> partition = Lists.partition(new ArrayList<>(staffIds), 1000);
                partition.forEach(staffIdList -> staffList.addAll(ch999UserService.getCh999UsersById(staffIdList)));
            }
            dealUserIdMap.forEach((tsId, userIds) -> {
                List<Integer> userIdList = Arrays.stream(StringUtils.split(userIds, ",")).filter(StringUtils::isNumeric).map(Integer::parseInt).collect(Collectors.toList());
                List<String> staffs = staffList.stream().filter(staff -> userIdList.contains(staff.getCh999Id())).map(Ch999User::getCh999Name).collect(Collectors.toList());
                dealUserMap.put(tsId, StringUtils.join(staffs, ","));
            });
            //时间格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateTimeUtils.DATETIME_FORMAT);
            touSuModelResPage.getRecords().forEach(it->{
                it.setFinishTime2(it.getFinishTime() == null ? "无" : formatter.format(it.getFinishTime()));
                it.setProcessUser(StringUtils.isNotBlank(it.getProcessUser()) ? it.getProcessUser() : "无");
                suModelRes.add(it);
                it.setDutyUser(dutyMap.get(it.getTsId()) == null ? "无" : dutyMap.get(it.getTsId()));
                // 奖励金额
                String rewards = StrUtil.format(ComplainConstant.REWARD, it.getBonusMoney(), rewardMap.getOrDefault(it.getTsId(), BigDecimal.ZERO));
                it.setRewards(rewards);
                //处理进程
                TousuStateEnum tousuStateEnum = Stream.of(TousuStateEnum.values()).filter(item -> item.getCode().equals(it.getStates())).findFirst().orElse(null);
                it.setStatusName(tousuStateEnum == null ? "无" : tousuStateEnum.getMessage());
                // 投诉责任划分和投诉原因
                List<TouSuDepartRes> touSuDepartRes = Optional.ofNullable(it.getZenRenAreasAndDeparts()).orElse(new ArrayList<>());
                //第一条塞进数据标记（第一条没有添加的需要把第一条门店塞进去，其余的复制除门店外相同的数据）
                boolean firstExit = false;
                for (int i = 0; i < touSuDepartRes.size(); i++) {
                    TouSuDepartRes suDepartRes = touSuDepartRes.get(i);
                    String catName = ComplaintCatEnum.of(suDepartRes.getCat()) == null ? "无" : ComplaintCatEnum.of(suDepartRes.getCat()).getMessage();
                    //区域
                    if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotEmpty(suDepartRes.getAreaName())){
                        //除第一个外都要对应重新生成行
                        if (firstExit){
                            TouSuModelRes res = new TouSuModelRes();
                            BeanUtils.copyProperties(it,res);
                            res.setDutyArea(suDepartRes.getAreaName());
                            res.setDutyDepart(StringUtils.isNotBlank(suDepartRes.getDepartAreaName()) ? suDepartRes.getDepartAreaName() : "无");
                            res.setScore(suDepartRes.getScoreArea() != null ? suDepartRes.getScoreArea() : BigDecimal.ZERO);
                            res.setCatName(catName);
                            res.setTousuReason(suDepartRes.getTouSuTypeStr()); // 投诉原因
                            suModelRes.add(res);
                            continue;
                        }
                        it.setDutyArea(suDepartRes.getAreaName());
                        it.setDutyDepart(StringUtils.isNotBlank(suDepartRes.getDepartAreaName()) ? suDepartRes.getDepartAreaName() : "无");
                        it.setScore(suDepartRes.getScoreArea() != null ? suDepartRes.getScoreArea() : BigDecimal.ZERO);
                        it.setCatName(catName);
                        firstExit = true;
                        it.setTousuReason(suDepartRes.getTouSuTypeStr()); // 投诉原因
                    }
                    //部门
                    if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotEmpty(suDepartRes.getDepartment())){
                        //除第一个外都要对应重新生成行
                        if (firstExit){
                            TouSuModelRes res = new TouSuModelRes();
                            BeanUtils.copyProperties(it,res);
                            res.setDutyArea(suDepartRes.getDepartment());
                            res.setDutyDepart(StringUtils.isNotBlank(suDepartRes.getDepartmentCentName()) ? suDepartRes.getDepartmentCentName() : "无");
                            res.setScore(suDepartRes.getScoreDep() != null ? suDepartRes.getScoreDep() :BigDecimal.ZERO);
                            res.setCatName(catName);
                            res.setTousuReason(suDepartRes.getTouSuTypeStr()); // 投诉原因
                            suModelRes.add(res);
                            continue;
                        }
                        it.setDutyArea(suDepartRes.getDepartment());
                        it.setDutyDepart(StringUtils.isNotBlank(suDepartRes.getDepartmentCentName()) ? suDepartRes.getDepartmentCentName() : "无");
                        it.setScore(suDepartRes.getScoreDep() != null ? suDepartRes.getScoreDep() :BigDecimal.ZERO);
                        it.setCatName(catName);
                        it.setTousuReason(suDepartRes.getTouSuTypeStr()); // 投诉原因
                        firstExit = true;
                    }
                }
                if (CollectionUtil.isEmpty(touSuDepartRes)){
                    //空值处理
                    it.setDutyArea(StringUtils.isEmpty(it.getDutyArea()) ? "无" : it.getDutyArea());
                    it.setDutyDepart(StringUtils.isEmpty(it.getDutyDepart()) ? "无" : it.getDutyDepart());
                    it.setCatName(StringUtils.isEmpty(it.getCatName()) ? "无" : it.getCatName());
                }
                if (StringUtils.isNotBlank(dealUserMap.get(it.getTsId()))) {
                    it.setDealUsers(dealUserMap.get(it.getTsId()));
                }
            });
            touSuModelResPage.setRecords(suModelRes);
        }
    }

    private String getDutyUserValue(TouSuZenRenRen tszrr, boolean jiuJiEnv) {
        if (Boolean.FALSE.equals(jiuJiEnv)) {
            return tszrr.getUserName();
        }
        // 扣除积分
        int tousuPoints = Optional.ofNullable(tszrr.getTousuPoint()).orElse(IntConstant.ZERO);
        // 扣分
        BigDecimal tousuLosePoints = Optional.ofNullable(tszrr.getTousuLosePoint()).orElse(BigDecimal.ZERO);
        if (tousuPoints == IntConstant.ZERO
                && tousuLosePoints.compareTo(BigDecimal.ZERO) == 0) {
            return tszrr.getUserName();
        }
        List<String> pointsTexts = new ArrayList<>(IntConstant.TWO);
        if (tousuLosePoints.compareTo(BigDecimal.ZERO) > 0) {
            pointsTexts.add(String.format("扣分：%s", DecimalFormatUtils.decimalFormat(tousuLosePoints, "#.##")));
        }
        if (tousuPoints > 0) {
            pointsTexts.add(String.format("扣除积分：%s", tousuPoints));
        }
        return String.format("%s(%s)", tszrr.getUserName(), String.join(",", pointsTexts));
    }

    /**
     * 获取责任门店、部门、投诉原因id
     *
     * @param tsIds
     * @return
     */
    private Map<Integer, List<TouSuDepartRes>> getTouSuDepartsByTsIds(List<Integer> tsIds, Boolean exportFlag) {
        Map<Integer, List<TouSuDepartRes>> tousuAreaAndDepart = new HashMap<>();
        if (CollectionUtils.isNotEmpty(tsIds)) {
            List<TouSuDepartRes> touSuDeparts = tousuAreaMapper.getTouSuDepartsByTsIds(tsIds, !Boolean.TRUE.equals(exportFlag));
            // 获取投诉原因id
            List<TousuCategoryRelationBO> categoryRelationList = touSuDepartMapper.getTouSUCategoryRelationByTsIds(tsIds);
            Map<String,List<TousuCategoryRelationBO>> relationMap = categoryRelationList.stream().collect(Collectors.groupingBy(i->String.format("%s-%s",i.getTouSuId(),i.getDepartId())));
            for (TouSuDepartRes touSuDepart : touSuDeparts) {
                touSuDepartSetName(touSuDepart, departInfoClient);
                List<TousuCategoryRelationBO> relationList = relationMap.get(String.format("%s-%s",touSuDepart.getTousuId(),touSuDepart.getId()));
                // 设置投诉原因
                touSuDepart.setTouSuTypeStr(CollectionUtils.isEmpty(relationList) ? null : StringUtils.join(relationList.stream().map(TousuCategoryRelationBO::getName).collect(Collectors.toList()),","));
            }
            if (CollectionUtils.isNotEmpty(touSuDeparts)) {
                tousuAreaAndDepart = touSuDeparts.stream().collect(Collectors.groupingBy(TouSuDepartRes::getTousuId));
            }
        }
        return tousuAreaAndDepart;
    }

    public void touSuDepartSetName(TouSuDepartRes touSuDepart, DepartInfoClient departInfoClient) {

        if (touSuDepart.getAreaId() != null) {
            R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(touSuDepart.getAreaId());
            AreaInfo area = new AreaInfo();
            if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                area = areaInfoR.getData();
            }
            touSuDepart.setAreaName(area.getArea());
            touSuDepart.setAreaType(area.getAreaType());
            R<Integer> departTypeIdR = departInfoClient.getDepartTypeId(area.getDepartId(), DataTypeEnum.S_COMMUNITY.getCode());
            //获取小区
            if (departTypeIdR.getCode() == ResultCode.SUCCESS) {
                if (Objects.nonNull(departTypeIdR.getData())) {
                    touSuDepart.setDepartIdArea(departTypeIdR.getData());
                } else if (XtenantEnum.isJiujiXtenant()){
                    touSuDepart.setDepartIdArea(area.getDepartId());
                }
            }

        }

        if (touSuDepart.getDepartIdArea() != null) {
            R<DepartInfoVO> byDepartCode = departInfoClient.getByDepartId(touSuDepart.getDepartIdArea());
            if (byDepartCode.getCode() == ResultCode.SUCCESS && byDepartCode.getData() != null) {
                DepartInfoVO data = byDepartCode.getData();
                //责任归属区域名
                touSuDepart.setDepartAreaName(data.getName());
            }
        }
        if (touSuDepart.getDepartId() != null) {
            R<DepartInfoVO> byDepartCode = departInfoClient.getByDepartId(touSuDepart.getDepartId());
            if (byDepartCode.getCode() == ResultCode.SUCCESS && byDepartCode.getData() != null) {
                DepartInfoVO data = byDepartCode.getData();
                //部门名称
                touSuDepart.setDepartment(data.getName());
                R<Integer> departTypeIdR = departInfoClient.getDepartTypeId(touSuDepart.getDepartId(), DataTypeEnum.CENTER.getCode());
                //获取部门所属中心Id
                if (departTypeIdR.getCode() == ResultCode.SUCCESS) {
                    touSuDepart.setDepartmentCentId(departTypeIdR.getData());
                }

            }
        }

        if (touSuDepart.getDepartmentCentId() != null) {
            R<DepartInfoVO> byDepartCode = departInfoClient.getByDepartId(touSuDepart.getDepartmentCentId());
            if (byDepartCode.getCode() == ResultCode.SUCCESS && byDepartCode.getData() != null) {
                DepartInfoVO data = byDepartCode.getData();
                //所属中心
                touSuDepart.setDepartmentCentName(data.getName());
            }
        }

    }

    /**
     * 获取投诉日志
     *
     * @param ids
     * @return
     */
    public Map<Integer, List<TsProcess>> getTouSuLogs(List<Integer> ids) {
        Map<Integer, List<TsProcess>> collect = new HashMap<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<TsProcess> touSuProcesses = tousuAreaMapper.getTsProcess(ids);
            if (CollectionUtils.isNotEmpty(touSuProcesses)) {
                collect = touSuProcesses.stream().collect(Collectors.groupingBy(TsProcess::getTsId));
            }
        }
        return collect;
    }


    @Override
    public List<TouSuTypesRes> getTouSuTypes() {
        List<TousuCategory> list = tousuAreaService.listTousuCategoryByKind(1);
        Map<Integer, List<TouSuTypesRes>> areaMap = new HashMap<>(10);
        if (CollectionUtils.isNotEmpty(list)) {
            areaMap = list.stream().map(e -> {
                TouSuTypesRes typesVO = new TouSuTypesRes();
                typesVO.setName(e.getName());
                typesVO.setId(e.getId());
                typesVO.setParentId(e.getParentId());
                return typesVO;
            }).collect(Collectors.groupingBy(TouSuTypesRes::getParentId));
        }
        List<TouSuTypesRes> rootDepart = areaMap.get(0);
        buildDepartAreaTree(rootDepart, areaMap);
        return areaMap.get(0);
    }


    private void buildDepartAreaTree(List<TouSuTypesRes> root, Map<Integer, List<TouSuTypesRes>> areaMap) {
        root.forEach(e -> {
            if (areaMap.containsKey(e.getId())) {
                e.setChildren(areaMap.get(e.getId()));
                buildDepartAreaTree(areaMap.get(e.getId()), areaMap);
            }
        });
    }

    /**
     * 获取投诉分类树列表
     *
     * @return
     */
    @Override
    public List<TouSuTypesRes> getTouSuCategoryList(Integer kind) {
        List<TouSuTypesRes> touSuTypesRes = new ArrayList<>();
        List<TousuCategory> list = listTousuCategoryByKind(kind);
        if (CollectionUtils.isNotEmpty(list)) {
            touSuTypesRes = list.stream().map(e -> {
                TouSuTypesRes typesVO = new TouSuTypesRes();
                typesVO.setName(e.getName());
                typesVO.setId(e.getId());
                typesVO.setIsdel(e.getIsdel());
                typesVO.setParentId(e.getParentId());
                typesVO.setDisOrder(e.getDisOrder());
                return typesVO;
            }).collect(Collectors.toList());
            Set<Integer> parentSet = touSuTypesRes.stream().map(TouSuTypesRes::getParentId).collect(Collectors.toSet());
            Set<Integer> idSet = touSuTypesRes.stream().map(TouSuTypesRes::getId).collect(Collectors.toSet());
            parentSet.removeAll(idSet);
            parentSet.remove(0);
            if(!parentSet.isEmpty()){
                List<TousuCategory> categories = tousuAreaMapper.getTousuCategoryByIds(parentSet);
                touSuTypesRes.addAll(categories.stream().map(e -> {
                    TouSuTypesRes typesVO = new TouSuTypesRes();
                    typesVO.setName(e.getName());
                    typesVO.setId(e.getId());
                    typesVO.setIsdel(e.getIsdel());
                    typesVO.setParentId(e.getParentId());
                    typesVO.setDisOrder(e.getDisOrder());
                    return typesVO;
                }).collect(Collectors.toList()));
            }
            touSuTypesRes = getTouSuCategoryChildren(touSuTypesRes, 0, 1);
        }
        return touSuTypesRes;
        //return treeToList(touSuTypesRes);
    }

    @Override
    public List<TousuCategory> listTousuCategoryByKind(Integer kind) {
        if (Objects.isNull(kind)) {
            return Collections.emptyList();
        }
        Set<Integer> kindList = multiGetKind(kind);
        return tousuAreaMapper.listAllTousuCategory(kindList);
    }

    /**
     * 投诉标签多选
     *
     * @param kind 当前投诉标签
     * @return 投诉标签多选的组合
     */
    private Set<Integer> multiGetKind(Integer kind) {
        Set<Integer> combinations = new HashSet<>();
        if (kind.equals(IntConstant.ZERO)) {
            return combinations;
        }
        combinations.add(kind);
        // 所有的标签选项
        Integer[] kindArray = {1, 2, 4};
        for (int i : kindArray) {
            for (int j : kindArray) {
                long count = Stream.of(i, j)
                        .filter(item -> item.equals(kind))
                        .count();
                if (count == 1) {
                    combinations.add(i|j);
                }
            }
        }
        // 所有元素的和
        Stream.of(kindArray)
                .reduce(Integer::sum)
                .ifPresent(combinations::add);
        return combinations;
    }

    /**
     * 压缩数据不要第二层，只取第三层和第一层的数据
     *
     * @param checkItemList
     * @return
     */
    private List<TouSuTypesRes> treeToList(List<TouSuTypesRes> checkItemList) {
        List<TouSuTypesRes> result = new ArrayList<>();
        for (TouSuTypesRes first : checkItemList) {
            //用来存放第三层的list
            List<TouSuTypesRes> sec = new ArrayList<>();
            List<TouSuTypesRes> second = first.getChildren();
            if (CollectionUtils.isNotEmpty(second)) {
                second.forEach(child -> sec.addAll(child.getChildren()));
//                清空第二层的数据
                first.getChildren().clear();
//                把第三层的数据添加到第二层
                first.getChildren().addAll(sec);
                result.add(first);
            }
        }
        return result;
    }

    /**
     * 保存投诉分类
     *
     * @param touSuCategory
     * @return
     */
    @Override
    public String saveTouSuCategory(TousuCategoryReq touSuCategory) {
        OaUserBO currentStaffId = abstractCurrentRequestComponent.getCurrentStaffId();
        touSuCategory.setUserName(currentStaffId.getUserName());
        try {
            if (StringUtils.isEmpty(touSuCategory.getName())) {
                log.error("投诉标签分类不能为空");
            }
            Integer input = tousuAreaMapper.getTouSuCategoryByName(touSuCategory.getName(), touSuCategory.getId());
            if (touSuCategory.getId() == 0 && input > 0) {
                log.error("投诉标签分类" + touSuCategory.getName() + "已存在");
            } else {
                tousuAreaMapper.saveTouSuCategory(touSuCategory);
            }
            if (touSuCategory.getId() > 0 && touSuCategory.getParentId() > 0) {
//                修改投诉分类
                Integer i = tousuAreaMapper.getTouSuCategoryById(touSuCategory.getParentId());
                if (i < 0) {
                    log.error("投诉标签父目录id=" + touSuCategory.getParentId() + "不存在");
                } else {
                    tousuAreaMapper.updateTouSuCategory(touSuCategory);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return "保存失败";
        }
        return "保存成功";
    }

    /**
     * 搜索会员信息
     *
     * @param keywordType value
     * @param keyword     key
     * @return object
     */
    @Override
    @DS("oanew")
    public Object findUserInfo(String keywordType, String keyword) {
        QueryWrapper<BbsxpUsers> queryWrapper = new QueryWrapper();
        if ("userid".equals(keywordType)) {
            queryWrapper.lambda().eq(BbsxpUsers::getId, Integer.parseInt(keyword));
        } else if ("name".equals(keywordType)) {
            queryWrapper.lambda().eq(BbsxpUsers::getUserName, keyword);
        } else {
            queryWrapper.lambda().eq(BbsxpUsers::getMobile, keyword);
        }
        List<BbsxpUsers> list = bbsxpUsersService.list(queryWrapper);
        List<MemberBO> memberRes = new ArrayList<>();
        for (BbsxpUsers bbsxpUsers : list) {
            MemberBO member = new MemberBO();
            member.setUserId(bbsxpUsers.getId());
            member.setMobile(bbsxpUsers.getMobile());
            member.setMemberName(bbsxpUsers.getUserName());
            member.setUserClassName(CommonUtil.getUserType(bbsxpUsers.getUserclass()));
            member.setXtenant(EnumUtil.getMessageByCode(XtenantEnum.class, bbsxpUsers.getXtenant()));
            memberRes.add(member);
        }
        return memberRes;
    }

    @Override
    @DS("oanew")
    public Object findUserInfoV2(String keywordType, String keyword) {
        OaUserBO currentUser = abstractCurrentRequestComponent.getCurrentStaffId();
        if (currentUser == null){
            throw new RuntimeException("用户信息获取失败");
        }
        QueryWrapper<BbsxpUsers> queryWrapper = new QueryWrapper();
        if ("userid".equals(keywordType)) {
            queryWrapper.lambda().eq(BbsxpUsers::getId, Integer.parseInt(keyword));
        } else if ("name".equals(keywordType)) {
            queryWrapper.lambda().eq(BbsxpUsers::getUserName, keyword);
        } else {
            queryWrapper.lambda().eq(BbsxpUsers::getMobile, keyword);
        }
        queryWrapper.lambda().eq(BbsxpUsers::getXtenant, currentUser.getXTenant());
        List<BbsxpUsers> list = bbsxpUsersService.list(queryWrapper);
        List<MemberBO> memberRes = new ArrayList<>();
        List<Integer> userIds = list.stream().map(BbsxpUsers::getId).distinct().collect(Collectors.toList());
        Map<Integer, Boolean> judgeUserSpecialMap = bbsxpUsersService.judgeUserSpecial(userIds);
        for (BbsxpUsers bbsxpUsers : list) {
            MemberBO member = new MemberBO();
            member.setUserId(bbsxpUsers.getId());
            member.setMobile(bbsxpUsers.getMobile());
            member.setMemberName(bbsxpUsers.getUserName());
            member.setUserClassName(CommonUtil.getUserType(bbsxpUsers.getUserclass()));
            member.setXtenant(EnumUtil.getMessageByCode(XtenantEnum.class, bbsxpUsers.getXtenant()));
            member.setXtenantCode(bbsxpUsers.getXtenant());
            member.setSpecialFlag(judgeUserSpecialMap.get(bbsxpUsers.getId()));
            if (Boolean.TRUE.equals(member.getSpecialFlag())) {
                member.setSpecialIcon(ComplainConstant.SPECIAL_USER_ICON);
            }
            memberRes.add(member);
        }
        return memberRes;
    }


    @Override
    public PageRes<CoupleBackRes> coupleBackList(CoupleBackReq coupleBackReq) {
        Integer count = tousuAreaMapper.coupleBackListCount(coupleBackReq);
        PageRes<CoupleBackRes> pageRes = new PageRes<>(coupleBackReq.getCurrent(), coupleBackReq.getSize());
        List<CoupleBackRes> list = tousuAreaMapper.getAllCoupleBack(pageRes, coupleBackReq, (pageRes.getCurrent() - 1L) * pageRes.getSize(), (long) pageRes.getSize());
        pageRes.setTotal(count);
        pageRes.setRecords(list);
        return pageRes;
    }

    @Override
    public PageRes<TousuPublicRes> listTousuPublic(TousuPublicReq tousuPublicReq) {
        PageRes<TousuPublicRes> pageRes = new PageRes<>(tousuPublicReq.getCurrent(), tousuPublicReq.getSize());
        // 分页条数
        Integer count = tousuAreaMapper.countTousuPublic(jiujiSystemProperties.getOfficeName(), tousuPublicReq);
        pageRes.setTotal(count);
        if (Objects.isNull(count) || count == 0) {
            return pageRes;
        }
        List<TousuPublicRes> records = tousuAreaMapper.pageTousuPublic(pageRes, jiujiSystemProperties.getOfficeName(), tousuPublicReq,
                (pageRes.getCurrent() - 1L) * pageRes.getSize(), (long) pageRes.getSize());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(records)) {

            return pageRes;
        }
        // 投诉及进程附件
        List<Integer> attachIdList = new ArrayList<>();
        // 投诉id
        List<Integer> tsIds = records.stream()
                .map(TousuPublicRes::getId)
                .collect(Collectors.toList());
        List<TsProcess> tsProcesses = tousuAreaMapper.listTsProcessWithShowWebByTsIds(tsIds);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(tsProcesses)) {
            tsProcesses.stream()
                    .filter(item -> org.apache.commons.lang.StringUtils.isNotEmpty(item.getAttachFiles()))
                    .forEach(item -> {
                        attachIdList.addAll(CommonUtil.covertIdStr(item.getAttachFiles()));
                    });
        }
        records.forEach(item -> {
            // 时间格式化
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(item.getAddTime())) {
                String addTime = item.getAddTime();
                if (addTime.contains(".")) {
                    addTime = addTime.substring(0, addTime.indexOf("."));
                }
                item.setAddTime(addTime);
            }
            // 门店名称
            if (Objects.nonNull(item.getShopId())) {
                item.setShopName(item.getShopName() + "·" + item.getShopCode());
            }
            // 投诉状态
            if (Objects.nonNull(item.getStatus())) {
                List<Integer> realStatusList = Arrays.asList(TousuStateEnum.TousuStateEnum_1.getCode(),
                        TousuStateEnum.TousuStateEnum_3.getCode());
                TousuStateEnum tousuStateEnum = Stream.of(TousuStateEnum.values())
                        .filter(enumItem -> enumItem.getCode().equals(item.getStatus()))
                        .findFirst()
                        .orElse(null);
                String statusDes = tousuStateEnum == null ? "无" : tousuStateEnum.getMessage();
                if (Objects.nonNull(tousuStateEnum)
                        && !realStatusList.contains(tousuStateEnum.getCode())) {
                    statusDes = "处理中";
                }
                item.setStatusDes(statusDes);
            }
            // 投诉性质
            if (Objects.nonNull(item.getTousuKinds())) {
                TouSuTagEnum tagEnum = TouSuTagEnum.of(item.getTousuKinds());
                item.setTousuKindsName(Objects.isNull(tagEnum)
                        ? org.apache.commons.lang3.StringUtils.EMPTY
                        : tagEnum.getMessage());
            }
            // 投诉附件信息
            if (StringUtils.isNotEmpty(item.getAttachIds())) {
                attachIdList.addAll(CommonUtil.covertIdStr(item.getAttachIds()));
            }
            List<TsProcess> tsProcessesByTsId = tsProcesses.stream()
                    .filter(tsProcess -> tsProcess.getTsId().equals(item.getId()))
                    .collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(tsProcessesByTsId)) {

                return;
            }
            // 投诉进程
            List<TousuPublicRes.TousuProcess> tsResList = tsProcessesByTsId.stream()
                    .map(processItem -> {
                        if (org.apache.commons.lang3.StringUtils.isNotEmpty(processItem.getAttachFiles())) {
                            attachIdList.addAll(CommonUtil.covertIdStr(processItem.getAttachFiles()));
                        }
                        TousuPublicRes.TousuProcess tsRes = new TousuPublicRes.TousuProcess();
                        tsRes.setId(processItem.getId());
                        tsRes.setTsId(processItem.getTsId());
                        tsRes.setContent(processItem.getDsc());
                        tsRes.setCate(processItem.getCate());
                        tsRes.setCateContent(processItem.getCateContent());
                        if (Objects.nonNull(processItem.getIntime())) {
                            tsRes.setInTime(processItem.getIntime().format(DateTimeFormatter.ofPattern(DateTimeFormatterUtil.YMD_HMS_STR)));
                        }
                        tsRes.setAttachIds(processItem.getAttachFiles());
                        return tsRes;
                    })
                    .collect(Collectors.toList());
            item.setProcesses(tsResList);
        });
        // 处理投诉和进程中的附件
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(attachIdList)) {
            List<AttachmentsRes> attachments = attachmentsService.listTousuAttachmentsByIds(attachIdList);
            records.forEach(item -> {
                // 投诉附件信息
                if (StringUtils.isNotEmpty(item.getAttachIds())) {
                    List<AttachmentsRes> tsAttachments = attachments.stream()
                            .filter(a -> CommonUtil.covertIdStr(item.getAttachIds()).contains(a.getId()))
                            .peek(a -> {
                                a.setRealFlePath(a.getFilepath());
                                String httpPath = imageProperties.getSelectImgUrl()
                                        + WebConstant.PICTURE_PATH.PATH_NEWSTATIC
                                        + changeFid(a.getFilename(), a.getFid());
                                a.setFilepath(httpPath);
                            })
                            .collect(Collectors.toList());
                    item.setAttachments(tsAttachments);
                }
                // 进程附件
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(item.getProcesses())) {

                    return;
                }
                item.getProcesses()
                        .stream()
                        .filter(tousuProcess -> org.apache.commons.lang3.StringUtils.isNotEmpty(tousuProcess.getAttachIds()))
                        .forEach(tousuProcess -> {
                            List<AttachmentsRes> tsProcessAttachments = attachments.stream()
                                    .filter(a -> CommonUtil.covertIdStr(tousuProcess.getAttachIds()).contains(a.getId()))
                                    .peek(a -> {
                                        a.setRealFlePath(a.getFilepath());
                                        String httpPath = imageProperties.getSelectImgUrl()
                                                + WebConstant.PICTURE_PATH.PATH_NEWSTATIC
                                                + changeFid(a.getFilename(), a.getFid());
                                        a.setFilepath(httpPath);
                                    })
                                    .collect(Collectors.toList());
                            tousuProcess.setAttachments(tsProcessAttachments);
                        });
            });
        }

        pageRes.setRecords(records);
        return pageRes;
    }

    @Override
    @DS("oanew")
    public Boolean getCoupleBackByUserId(Integer userId) {
        List<String> companyName = qudaoMapper.isQuDao(userId);
        return CollectionUtils.isNotEmpty(companyName);
    }

    @Override
    @DS("officeWrite")
    public String addCoupzleBack(AddCoupleBackReq coupleBackReq) {
        try {
            TouSuModel touSuModel = new TouSuModel();
            BeanUtils.copyProperties(coupleBackReq, touSuModel);
            touSuModel.setType(5);
            List<String> companyName = qudaoMapper.isQuDao(coupleBackReq.getUserId());
            if (CollectionUtils.isNotEmpty(companyName)) {
                touSuModel.setSupplier(companyName.get(0));
            }
            if (CollectionUtils.isNotEmpty(coupleBackReq.getFiles())) {
                String attachIds = touSuDepartService.uploadFiles(coupleBackReq.getFiles());
                touSuModel.setAttachIds(attachIds);
            }
            LocalDateTime now = LocalDateTime.now();
            touSuModel.setAddTime(now);
            touSuModel.setHuanyuantimeout(now.plusHours(1));
            touSuModel.setDealTimeout(now.plusHours(60));
            tousuAreaService.save(touSuModel);
            if (CollectionUtils.isNotEmpty(coupleBackReq.getAnswer())) {
                for (TousuAnswerVO tousuAnswerVO : coupleBackReq.getAnswer()) {
                    TousuAnswer tousuAnswer = TousuAnswer.builder().answer(JSONObject.toJSONString(tousuAnswerVO.getAnswer()))
                            .question(tousuAnswerVO.getQuestion()).tsId(touSuModel.getId()).build();
                    tousuAnswerService.save(tousuAnswer);
                }
            }
        } catch (BeansException e) {
            log.error(e.getMessage());
            return "添加失败";
        }
        return "添加成功";
    }

    @Override
    public CoupleBackInfoRes getCoupleBack(Integer id, Integer userId) {
        TouSuModel touSu = this.getTouSu(id);
        if (!touSu.getUserId().equals(userId)) {
            return null;
        }
        CoupleBackInfoRes coupleBackInfoRes = new CoupleBackInfoRes();
        BeanUtils.copyProperties(touSu, coupleBackInfoRes);
        coupleBackInfoRes.setAddTime(touSu.getAddTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        LambdaQueryWrapper<TousuAnswer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TousuAnswer::getTsId, id);
        List<TousuAnswer> answers = tousuAnswerService.list(queryWrapper);
        List<TousuAnswerVO> answersVo = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(answers)) {
            for (TousuAnswer answer : answers) {
                List<AnswerVO> answerVOS = new ArrayList<>();
                if (StringUtils.isNotEmpty(answer.getAnswer())) {
                    answerVOS = JSONObject.parseArray(answer.getAnswer(), AnswerVO.class);
                }
                TousuAnswerVO build = TousuAnswerVO.builder().answer(answerVOS).question(answer.getQuestion()).tsId(answer.getTsId())
                        .build();
                answersVo.add(build);
            }
            coupleBackInfoRes.setAnswerList(answersVo);
        }
        List<TsProcess> tsProcesses = touSuDepartService.showProcessInfo(id, null);
        if (CollectionUtils.isNotEmpty(tsProcesses)) {
            coupleBackInfoRes.setProcesses(tsProcesses);
        }

        return coupleBackInfoRes;
    }

    @Override
    public R<Boolean> pushMsgCoupleBack() {
        String url = mUrlSource.getBasicUrl() + "/member/supplier/my-feedback";
        List<String> tels = qudaoMapper.getTel();
        String content = "尊敬的各位合作伙伴，为确保采购业务的高效开展，现诚邀您对以下内容进行填写，感谢您的支持与配合！" + url;
        Boolean send = null;
        if (CollectionUtils.isNotEmpty(tels)) {
            send = true;
            List<String> collect = tels.stream().distinct().collect(Collectors.toList());
            for (String tel : collect) {
                R<Boolean> b = smsService.sendSms(tel, 0, content, LocalDateTime.now().toString(), "系统");
                if (b.getCode() == ResultCode.SUCCESS) {
                    send = send && b.getData();
                }
            }
        }
        if (send != null && send) {
            return R.success("发送成功", true);
        } else {
            return R.error(ResultCode.RETURN_ERROR, "短信发送失败");
        }
    }

    @Override
    @DS("officeWrite")
    public Integer updateTousuById(TouSuModel touSuModel) {
        return touSuMapper.updateById(touSuModel);
    }


    private List<TouSuTypesRes> getTouSuCategoryChildren(List<TouSuTypesRes> list, Integer pid, Integer level) {
        List<TouSuTypesRes> result = new ArrayList<>();
        List<TouSuTypesRes> tempList = list.stream().filter(p -> p.getParentId().equals(pid)).collect(Collectors.toList());
        for (TouSuTypesRes item : tempList) {
            result.add(new TouSuTypesRes().setName(item.getName()).setId(item.getId()).setParentId(item.getParentId()).
                    setLevel(level).setDisOrder(item.getDisOrder()).setChildren(getTouSuCategoryChildren(list, item.getId(), level + 1)));
        }
        return result;
    }


    @Override
    public List<Integer> getTouSuIdsByZeRenRen(Integer zeRenRenId) {
        List<Integer> idList = new ArrayList<>();
        try {
            return tousuAreaMapper.getTousuIdByUserId(zeRenRenId);
        } catch (Exception ex) {
            return idList;
        }

    }

    @Override
    public List<Integer> getTouSuIdsByZeRenRen(String zeRenRenName) {
        List<Integer> idList = new ArrayList<>();
        try {
            return tousuAreaMapper.getTousuIdByUserName(zeRenRenName);
        } catch (Exception ex) {
            return idList;
        }

    }

    @Override
    public List<Integer> getTouSuIdsByProcessContent(String processContent) {
        List<Integer> idList = new ArrayList<>();
        try {
            return tousuAreaMapper.getTsIdsByDsc(processContent);
        } catch (Exception ex) {
            return idList;
        }
    }

    public Map<Integer, String> getTouSuTags() {
        List<TouSuTag> touSuTags = tousuAreaMapper.getAllTouSuTags();
        Map<Integer, String> map = new HashMap<>();
        for (TouSuTag touSuTag : touSuTags) {
            map.put(touSuTag.getId(), touSuTag.getTagName());
        }
        return map;
    }


    /**
     * 根据地区查询条件，取得需查询的门店Id
     *
     * @param areaIds
     * @return
     */
    public List<Integer> getQueryAreaIds(List<String> areaIds) {
        List<Integer> queryAreaIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(areaIds)) {
            queryAreaIds.add(0);
        }
        for (String str : areaIds) {
            if (str.matches("^[0-9]*$")) {
                int areaId = Integer.parseInt(str);
                queryAreaIds.add(areaId);
            } else {
                List<AreaInfo> allAreaInfo = areaInfoClient.listAll().getData();
                R<List<Integer>> allLowNodeR = departInfoClient.getAllLowNode(str.replace("a", ""));
                List<Integer> allLowNode = CommonUtils.isRequestSuccess(allLowNodeR)? allLowNodeR.getData():new ArrayList<>();
                List<Integer> collect = allAreaInfo.stream().filter(model -> model.getDepartId().equals(str.replace("a", "")) ||
                        model.getArea().equals(str) || allLowNode.contains(model.getDepartId())).
                        map(AreaInfo::getId).collect(Collectors.toList());
                queryAreaIds.addAll(collect);
            }
        }
        if (CollectionUtils.isEmpty(queryAreaIds)) {
            queryAreaIds.add(0);
        }
        return queryAreaIds;
    }

    /**
     * 获取投诉的被投诉店面
     *
     * @param touSuIds
     * @return
     */
    public List<TouSuAreaOutputBO> getTousuAreas(Integer touSuIds, Integer areaId) {
        List<TouSuAreaOutputBO> areaList = new ArrayList<>();
        List<TousuArea> areas = tousuAreaMapper.selectByTouSuId(touSuIds);
        areas = areas.stream().filter(Objects::nonNull).filter(p -> p.getAreaId() > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(areas)) {
            return areaList;
        }
//      投诉门店ids
        List<Integer> areaIds = areas.stream().map(TousuArea::getAreaId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(areas)) {
            //所有获取门店被投诉次数
            areaList = getAreaMonthTouSuCount(areaIds);
        } else {
            // 一开始取客户填写投诉表单里面的门店被投诉次数
            areaList = getAreaMonthTouSuCount(areaId);
        }
        R<List<AreaInfo>> allAreasR = areaInfoClient.listAll();
        List<AreaInfo> allAreas = new ArrayList<>();
        if (ResultCode.SUCCESS == allAreasR.getCode() && CollectionUtils.isNotEmpty(allAreasR.getData())) {
            allAreas = allAreasR.getData();
        }
        for (TouSuAreaOutputBO t : areaList) {
            AreaInfo areaInfo = allAreas.stream().filter(a -> a.getId().equals(t.getAreaId())).findFirst().orElse(null);
            if (areaInfo != null) {
                Integer level1 = areaInfo.getLevel1();
                String levelDiscretion = (level1 == null || level1 == 0) ? "" : EnumUtil.getMessageByCode(EAreaLevelEnum.class, level1) + "门店";
                if(XtenantJudgeUtil.isJiujiMore()){
                    levelDiscretion = (level1 == null || level1 == 0) ? "" : EnumUtil.getMessageByCode(AreaGradeEnum.class, level1) + "门店";
                }
                t.setAreaLevel(levelDiscretion).setArea(areaInfo.getArea()).setAreaName(areaInfo.getAreaName());
            }

        }

        return areaList;
    }

    /**
     * 获取所有被投诉店面本月累计投诉次数
     *
     * @param areaIds
     */
    public List<TouSuAreaOutputBO> getAreaMonthTouSuCount(List<Integer> areaIds) {
        if (CollectionUtils.isEmpty(areaIds)) {
            return new ArrayList<>();
        }
        List<TouSuAreaOutputBO> list = new ArrayList<>();
        List<TousuArea> tousuAreas = tousuAreaMapper.getAreaIdsCount(areaIds);
        List<TousuArea> areasByDate = tousuAreaMapper.getAreaIdsCountByDate(areaIds,TouSuAreaOutputBO.DATE_NUM);
        Map<Integer, List<TousuArea>> map = tousuAreas.stream().filter(t -> t.getAreaId() != null).collect(Collectors.groupingBy(TousuArea::getAreaId));
        Map<Integer, List<TousuArea>> mapByDate = areasByDate.stream().filter(t -> t.getAreaId() != null).collect(Collectors.groupingBy(TousuArea::getAreaId));
        for (Integer areaId : areaIds) {
            Integer tousuCount = Optional.ofNullable(map.get(areaId)).map(i->i.size()).orElse(0);
            Integer complaintCount = Optional.ofNullable(mapByDate.get(areaId)).map(i->i.size()).orElse(0);
            TouSuAreaOutputBO t = new TouSuAreaOutputBO().setAreaId(areaId).setMonthTouSuCount(tousuCount)
                    .setComplaintCount(complaintCount);
            list.add(t);
        }
        return list;
    }

    /**
     * 获取所有被投诉店面本月累计投诉次数
     *
     * @param areaId
     */
    public List<TouSuAreaOutputBO> getAreaMonthTouSuCount(Integer areaId) {
        if (areaId == null) {
            return new ArrayList<>();
        }
        List<TouSuAreaOutputBO> list = new ArrayList<>();
        List<TouSuModel> tousuAreas = tousuAreaMapper.getAreaIdCount(areaId);
        Map<Integer, List<TouSuModel>> map = tousuAreas.stream().filter(t -> t.getAreaId() != null).collect(Collectors.groupingBy(TouSuModel::getAreaId));
        if (map.containsKey(areaId)) {
            tousuAreas = map.get(areaId);
            TouSuAreaOutputBO t = new TouSuAreaOutputBO().setAreaId(areaId).setMonthTouSuCount(tousuAreas.size());
            list.add(t);
        }

        return list;
    }

    @Override
    public TouSuModel getTouSu(int id) {
        TouSuModel tousu = tousuAreaMapper.getTouSuById(id);
        tousu.setAreas(getTousuAreas(id, tousu.getAreaId()));
        List<String> attarUrls = new ArrayList<>();
        if (StringUtils.isNotEmpty(tousu.getAttachIds())) {
            // 投诉附件信息
            List<AttachmentsRes> touSuPicByIds = tousuAreaMapper.getTouSuPicByIds(tousu.getAttachIds());
            tousu.setAttachments(touSuPicByIds);
            if (CollectionUtils.isNotEmpty(touSuPicByIds)) {
                for (AttachmentsRes a : touSuPicByIds) {
                    String filePath = imageProperties.getSelectImgUrl() + "newstatic/" + changeFid(a.getFilename(), a.getFid());
                    a.setFilepath(filePath);
                    attarUrls.add(filePath);
                }
            }
        }
        tousu.setAttachUrls(attarUrls);
        return tousu;
    }

    @Override
    public String changeFid(String fileName, String fid) {
        String suffix = "";
        String pngSuffix = ".png";
        String jpgSuffix = ".jpg";
        String jpegSuffix=".jpeg";
        String mp4Suffix=".mp4";
        String aviSuffix=".avi";
        String mp4UpSuffix=".MP4";
        String aviUpSuffix=".AVI";
        if (org.apache.commons.lang3.StringUtils.isNotBlank(fileName) && org.apache.commons.lang3.StringUtils.contains(fileName, ".")) {
            suffix = org.apache.commons.lang3.StringUtils.substring(fileName, fileName.lastIndexOf(SignConstant.POINT) + 1);
            if (!fid.contains(pngSuffix) && !fid.contains(jpgSuffix) && !fid.contains(jpegSuffix) && !fid.contains(mp4Suffix) && !fid.contains(aviSuffix)&& !fid.contains(mp4UpSuffix) && !fid.contains(aviUpSuffix)) {
                suffix = fid.replace(SignConstant.COMMA, SignConstant.ZHENG_XIE_GANG) + SignConstant.POINT + suffix;
            } else {
                suffix = fid.replace(SignConstant.COMMA, SignConstant.ZHENG_XIE_GANG);
            }
        }
        return suffix;
    }

    @Override
    public String addTouSuOtherInfoByWebServer(Integer tousuId,String storeCode) {
        TousuDepart tousuDepart = createTousuDepart(tousuId, storeCode);
        return this.addTouSuDepart(tousuDepart);
    }


    public TousuDepart createTousuDepart(Integer tousuId, String storeCode) {
        TousuDepart tousuDepart = new TousuDepart();
        tousuDepart.setTousuId(tousuId);
        tousuDepart.setType(0);
        Integer areaId = areaInfoMapper.getIdByArea(storeCode);
        tousuDepart.setAreaId(areaId);
        return tousuDepart;
    }

    /**
     * 获取投诉相关乐捐
     *
     * @param id
     * @return
     */
    private List<Integer> getTousuPunish(Integer id) {
        return tousuAreaMapper.getTousuPunish(id);
    }


    /**
     * 获取投诉责任人最近180天累计投诉次数
     *
     * @param ch999ids
     * @return
     */
    public List<Tuple> getZeRenRenTouSuCount(List<Integer> ch999ids) {
        if (CollectionUtils.isEmpty(ch999ids)) {
            return new ArrayList<>();
        }
        List<Tuple> list = new ArrayList<>();
        List<TouSuZenRenRen> byUserIds = tousuAreaMapper.getTousuZeRenRenByUserIds(ch999ids);
        Map<Integer, List<TouSuZenRenRen>> map = byUserIds.stream().filter(t -> t.getUserId() != null).collect(Collectors.groupingBy(TouSuZenRenRen::getUserId));
        for (Integer userId : ch999ids) {
            if (map.containsKey(userId)) {
                List<TouSuZenRenRen> touSuZenRenRens = map.get(userId);
                Tuple t = new Tuple().setId(userId).setCount(touSuZenRenRens.size());
                list.add(t);
            }
        }
        return list;

    }

    /**
     * 投诉地区扣分
     */
    private void tousuAreaDeduct(Integer id, String bumen, Integer type, int fen, String inUser, TousuDepart oldEntity) {
        if (Objects.nonNull(oldEntity) && Objects.equals(fen, oldEntity.getTousuPoint())) {
            return;
        }
        if (fen == 0 && Objects.nonNull(oldEntity) && Objects.isNull(oldEntity.getTousuPoint())) {
            return;
        }
        String month = YearMonth.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        TousuDepartPointsBO tousuDepartPointsBO = tousuAreaMapper.getTousuDepartPoints(bumen, type, month);
        if (Objects.isNull(tousuDepartPointsBO)) {
            log.error("投诉地区扣除积分，查询部门积分数据为空");
            return;
        }
        RLock rLock = redissonClient.getFairLock("complainDepart:deduct:point:" + id);
        try {
            boolean res = rLock.tryLock(2, 10, TimeUnit.SECONDS);
            if (!res) {
                log.error("投诉地区扣除积分，获取锁失败");
                return;
            }
            int finalFen = (Objects.isNull(oldEntity) || Objects.isNull(oldEntity.getTousuPoint())) ? fen : fen - oldEntity.getTousuPoint();
            String comment = "投诉扣除积分";
            if (Objects.isNull(oldEntity)) {
                comment = "投诉扣除积分";
            } else {
                if (finalFen > 0) {
                    comment = "投诉责任积分再扣除";
                } else if (finalFen < 0) {
                    comment = "投诉责任积分补偿";
                }
            }
            tousuDepartPointsBO.setJifens(Objects.nonNull(tousuDepartPointsBO.getJifens()) ? tousuDepartPointsBO.getJifens() - finalFen : finalFen);
            tousuDepartPointsBO.setTotaljifen(Objects.nonNull(tousuDepartPointsBO.getTotaljifen()) ? tousuDepartPointsBO.getTotaljifen() - finalFen : finalFen);
            tousuDepartPointsBO.setComment(comment);
            tousuDepartPointsBO.setDtime(LocalDateTime.now());
            tousuDepartPointsBO.setInuser(inUser);
            tousuDepartPointsBO.setFen(finalFen * -1);

            tousuAreaMapper.updateTousuDepartFen(tousuDepartPointsBO);
            tousuAreaMapper.addTousuDepartFenLog(tousuDepartPointsBO);
            log.info("扣分成功，id: {}, 扣分值: {}", id, finalFen);
        } catch (Exception e) {
            log.error("投诉地区扣分异常:{}", e.getMessage(), e);
        } finally {
            if (rLock != null && rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }

    /**
     * 责任划分——添加门店/部门
     *
     * @param touSuDepartReq
     * @return
     */
    @Override
    @DS("officeWrite")
    public String addTouSuDepart(TousuDepart touSuDepartReq) {
        OaUserBO oaUser = abstractCurrentRequestComponent.getCurrentStaffId();
        //if (oaUser == null) {
        //    return "没有登录";
        //}

        //门店
        try {
            if (touSuDepartReq.getAreaId() != null && touSuDepartReq.getType() == 0) {
                Integer areaId = touSuDepartReq.getAreaId();
                R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(areaId);
                AreaInfo area = new AreaInfo();
                if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                    area = areaInfoR.getData();
                }
                touSuDepartReq.setAreaName(area.getAreaName() + "." + area.getArea());
                //获取大区
                R<Integer> departTypeIdR = departInfoClient.getDepartTypeId(area.getDepartId(), DataTypeEnum.L_COMMUNITY.getCode());
                if (area.getDepartId() != null && departTypeIdR.getCode() == ResultCode.SUCCESS) {
                    touSuDepartReq.setDepartArea(departTypeIdR.getData());
                }
            }
            if (oaUser != null) {
                touSuDepartReq.setCreateUser(oaUser.getUserName());
            }
            touSuDepartReq.setCreateTime(LocalDateTime.now());
            touSuDepartService.add(touSuDepartReq);
            if (touSuDepartReq.getAreaId() != null) {
                String userName = null;
                if (oaUser != null) {
                    userName = oaUser.getUserName();
                }
                // 在tousuArea中保存 areaId
                tousuAreaMapper.saveTousuAreaByTouSuId(touSuDepartReq.getTousuId(), touSuDepartReq.getAreaId(),userName , touSuDepartReq.getId());
            }
            if (CollectionUtils.isNotEmpty(touSuDepartReq.getCategoryIds())) {
                for (Integer cateId : touSuDepartReq.getCategoryIds()) {
                    TousuCategoryRelation tousuCategoryRelation = new TousuCategoryRelation();
                    tousuCategoryRelation.setCateId(cateId);
                    tousuCategoryRelation.setCreateTime(LocalDateTime.now());
                    if (oaUser != null) {
                        tousuCategoryRelation.setCreateUser(oaUser.getUserName());
                    }
                    tousuCategoryRelation.setTousuId(touSuDepartReq.getTousuId());
                    tousuCategoryRelation.setDepartId(touSuDepartReq.getId());
                    tousuCategoryRelationService.save(tousuCategoryRelation);
                }
            }
            if (Objects.nonNull(touSuDepartReq.getTousuPoint())) {
                String bumen = Objects.equals(touSuDepartReq.getType(), 0) ? String.valueOf(touSuDepartReq.getAreaId()) : String.valueOf(touSuDepartReq.getDepartId());
                Integer type = Objects.equals(touSuDepartReq.getType(), 0) ? 1 : 2;
                String inUser = Objects.isNull(oaUser) ? "系统" : oaUser.getUserName();
                this.tousuAreaDeduct(touSuDepartReq.getId(), bumen, type, touSuDepartReq.getTousuPoint(), inUser, null);
            }
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
            return "添加失败";
        }

        return "添加成功";
    }


    /**
     * 修改责任划分——添加门店,部门
     *
     * @param touSuDepartReq
     * @return
     */
    @Override
    @DS("officeWrite")
    public R<String> modifyTouSuDepart(TousuDepart touSuDepartReq) {
        OaUserBO oaUser = abstractCurrentRequestComponent.getCurrentStaffId();
        try {
            if (touSuDepartReq.getAreaId() != null) {
                R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(touSuDepartReq.getAreaId());
                AreaInfo area = new AreaInfo();
                if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                    area = areaInfoR.getData();
                }
                //获取大区
                R<Integer> departTypeIdR = departInfoClient.getDepartTypeId(area.getDepartId(), DataTypeEnum.CENTER.getCode());

                if (area.getDepartId() != null && departTypeIdR.getCode() == ResultCode.SUCCESS) {
                    //责任归属区域名
                    touSuDepartReq.setDepartArea(departTypeIdR.getData());
                }
                touSuDepartReq.setAreaName(area.getAreaName() + "." + area.getArea());
                //            在tousuArea中修改 areaId
                tousuAreaMapper.setTousuAreaByTouSuId(touSuDepartReq.getTousuId(), touSuDepartReq.getAreaId(), oaUser.getUserName(), touSuDepartReq.getId());
            }


            touSuDepartReq.setCreateUser(oaUser.getUserName());
            touSuDepartReq.setCreateTime(LocalDateTime.now());
            TousuDepart oldEntity = touSuDepartService.getById(touSuDepartReq.getId());
            Integer tousuPoint = touSuDepartReq.getTousuPoint();
            if (Objects.isNull(tousuPoint)) {
                tousuPoint = 0;
            }
            touSuDepartService.modifyTouSuDepart(touSuDepartReq);

            if (CollectionUtils.isNotEmpty(touSuDepartReq.getCategoryIds())) {
                QueryWrapper<TousuCategoryRelation> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(TousuCategoryRelation::getTousuId, touSuDepartReq.getTousuId());
                queryWrapper.lambda().eq(TousuCategoryRelation::getDepartId, touSuDepartReq.getId());
                tousuCategoryRelationService.remove(queryWrapper);
                for (Integer cateId : touSuDepartReq.getCategoryIds()) {
                    TousuCategoryRelation tousuCategoryRelation = new TousuCategoryRelation();
                    tousuCategoryRelation.setCateId(cateId);
                    tousuCategoryRelation.setCreateTime(LocalDateTime.now());
                    tousuCategoryRelation.setCreateUser(oaUser.getUserName());
                    tousuCategoryRelation.setTousuId(touSuDepartReq.getTousuId());
                    tousuCategoryRelation.setDepartId(touSuDepartReq.getId());
                    tousuCategoryRelationService.saveOrUpdate(tousuCategoryRelation);
                }
            }
            String bumen = Objects.equals(touSuDepartReq.getType(), 0) ? String.valueOf(touSuDepartReq.getAreaId()) : String.valueOf(touSuDepartReq.getDepartId());
            Integer type = Objects.equals(touSuDepartReq.getType(), 0) ? 1 : 2;
            this.tousuAreaDeduct(touSuDepartReq.getId(), bumen, type, tousuPoint, oaUser.getUserName(), oldEntity);
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
            return R.error(ResultCode.PARAM_ERROR,"修改失败");
        }
          return R.success("修改成功");
    }

    /**
     * 相同投诉
     *
     * @param id
     * @return
     */
    @Override
    public R<Boolean> setTouSuEndAndInvalid(Integer id, Integer joinTousuId, Boolean isSupplier) {
        String userName = abstractCurrentRequestComponent.getCurrentStaffId().getUserName();
        if (joinTousuId.equals(id)) {
            return R.error(ResultCode.RETURN_ERROR, "输入了相同的id");
        }
        LambdaQueryWrapper<TouSuModel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (isSupplier) {
            lambdaQueryWrapper.eq(TouSuModel::getType, 5);
            lambdaQueryWrapper.eq(TouSuModel::getId, joinTousuId);
        } else {
            lambdaQueryWrapper.ne(TouSuModel::getType, 5);
            lambdaQueryWrapper.eq(TouSuModel::getId, joinTousuId);
        }

        TouSuModel touSu = baseMapper.selectOne(lambdaQueryWrapper);
        if (touSu == null) {
            return R.error(ResultCode.RETURN_ERROR, "输入了不存在的id");
        }
        Integer i = tousuAreaMapper.setTousuEndAndinvalid(id);
        if (i > 0) {
            TouSuProcessBO process = new TouSuProcessBO();
            process.setTsId(id);
            process.setOpUser(userName);
            process.setShow(true);
            process.setNotice(false);
            process.setDsc("此条投诉反馈问题与投诉ID：" + joinTousuId + "相同，系统自动合并，我们将在该条投诉中持续跟进，请于该投诉中关注处理进度。");
            process.setAttachFiles("");
            touSuService.addTsProcess(process, null);
            return R.success("修改成功");
        }
        return R.error(ResultCode.RETURN_ERROR, "修改失败");
    }

    @Override
    public R<String> verifyBeforeSendMa(DiscountsReq discounts) {
        if (!ActiveProfileJudgeUtil.isJiuJiEnvironment()) {
            return R.success("ok", "ok");
        }
        // 微信现金的金额不能大于500
        if (ComplainSendType.WECHAT.getCode().equals(discounts.getType())) {
            if (discounts.getPrice().compareTo(BigDecimal.valueOf(IntConstant.FIVE_HUNDRED)) > 0) {
                return R.error("金额不能大于500");
            }
            List<ComplainWeChatRes> wechatMoneyPushList = complainPushRelationService.listComplainPushByTousuId(discounts.getTousuId());
            if (CollectionUtils.isNotEmpty(wechatMoneyPushList)) {
                return R.error("此投诉已经发放过现金，不能再次发放");
            }
        }
        return R.success("ok", "ok");
    }

    /**
     * 发送优惠码
     *
     * @param discounts
     * @return
     */
    @Override
    public String sendMa(DiscountsReq discounts) {

        // 搬的下面的代码
        String limintClint = "";
        int xtenant = 0;
        if (discounts.getUserId() != null && discounts.getUserId() != 0) {
            xtenant = tousuAreaMapper.getXtenant(discounts.getUserId());
            if (xtenant == 1) {
                limintClint = "5";
            }
        }

        // 微信现金发放
        OaUserBO currentStaffId = abstractCurrentRequestComponent.getCurrentStaffId();
        ComplainPushRelation relation = new ComplainPushRelation();
        relation.setComplainId(discounts.getTousuId());
        relation.setMoney(discounts.getPrice());
        if (ComplainSendType.WECHAT.getCode().equals(discounts.getType()) && ActiveProfileJudgeUtil.isJiuJiEnvironment()) {
            // copy下面的代码
            relation.setType(ComplainSendType.WECHAT.getCode());
            TsProcess process = new TsProcess();
            process.setTsId(discounts.getTousuId());
            process.setOpUser(currentStaffId.getUserName());
            process.setIsShow(true);
            // 进程类型 5 领取日志
            process.setCate(5);
            process.setIntime(LocalDateTime.now());
            processService.saveProcess(process);
            process.setDsc(StrUtil.format(ComplainConstant.WE_CHAT_PUSH_MESSAGE_OA_LOG,discounts.getPrice(), process.getId(),discounts.getPrice()));
            processService.updateProcess(process);
            relation.setProcessId(process.getId());
            pushWeChatMoneyMsg(discounts, xtenant, currentStaffId);
        } else {
        try {
            String userName = currentStaffId.getUserName();

            if (StringUtils.isNotEmpty(discounts.getMobile()) || discounts.getUserId() != null && discounts.getUserId() != 0) {
                AddModelReq model = new AddModelReq();
                model.setGname("投诉(" + discounts.getTousuId() + ")");
                model.setCount(1);
                model.setTotal(discounts.getPrice());
                model.setLimintClint(limintClint);
                DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                model.setStarttime(LocalDateTime.now().format(df));
                model.setEndtime(discounts.getTime());
                model.setLimit1("3");
                model.setLimit2("0");
                model.setLimitprice(discounts.getPrice().toString());
                model.setCh999_id(-20);
                model.setUserid(discounts.getUserId());
                model.setIsdjq("1");
                AddMoble addMoble = new AddMoble();
                addMoble.setModel(model);
                Integer tousuid = discounts.getTousuId();
                addMoble.setTousuid(tousuid);
                String addModelString = JSONObject.toJSONString(addMoble);
                String inwcfUrl = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.IN_WCF_HOST, (int)Namespaces.get()).getData();
//                调用wcf接口
                String post = HttpUtil.post(inwcfUrl + UrlConstant.SEND_YHM_URL, addModelString);
//                反序列化结果
                SendMaRes sendMaRes = JSON.parseObject(post, SendMaRes.class);
                if (sendMaRes.getStats() == 1) {
                    LocalDate endTime = LocalDate.parse(discounts.getTime(), DATE_TIME_FORMATTER);
                    relation.setEndTime(endTime);
                    relation.setType(ComplainSendType.VOUCHER.getCode());
                    relation.setIsCheck(1);
                    TouSuProcessBO touSuProcess = new TouSuProcessBO();
                    touSuProcess.setTsId(discounts.getTousuId());
                    touSuProcess.setOpUser(userName);
                    touSuProcess.setShow(true);
                    touSuProcess.setAttachFiles("");
                    touSuProcess.setDsc("发送优惠码：" + sendMaRes.getResult().substring(0, 2) + "**" + sendMaRes.getResult().substring(sendMaRes.getResult().length() - 2) + "，金额：" + discounts.getPrice() + "，结束日期：" + discounts.getTime());
                    touSuProcess.setNotice(false);
                    touSuProcess.setDuanxin(false);
                    touSuProcess.setWeixin(false);
                    touSuProcess.setTongzhi(false);
                    TouSuModel touSuModel = tousuAreaService.getTouSu(discounts.getTousuId());
                    if (touSuModel.getStates() == 1) {
                        updateTouSuStatus(discounts.getTousuId(), 1, 2, userName, "");
                    }
                    if (touSuModel.getBonusMoney() != null) {
                        touSuModel.setBonusMoney(touSuModel.getBonusMoney().add(discounts.getPrice()));
                    } else {
                        touSuModel.setBonusMoney(discounts.getPrice());
                    }
                    tousuAreaService.updateTousuById(touSuModel);
                    int id = touSuService.addTsProcess(touSuProcess, touSuModel);
                    relation.setProcessId(id);
                    pushTousuYouhuimaMsg(discounts, xtenant,sendMaRes.getResult(), currentStaffId);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return "失败";
        }
        }
        complainPushRelationService.saveComplainPushRelation(relation);
        return "成功";
    }

    /**
     * 超时未跟进发送消息
     *
     * @return
     */
    @Override
    public String sendMsg() {
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime time1 = now.plusMinutes(-1);
            time1 = LocalDateTime.of(time1.getYear(), time1.getMonth(), time1.getDayOfMonth(), time1.getHour(), time1.getMinute(), 0);
            LocalDateTime time2 = now.plusMinutes(1);
            time2 = LocalDateTime.of(time2.getYear(), time2.getMonth(), time2.getDayOfMonth(), time2.getHour(), time2.getMinute(), 0);
            List<TousuNotice> tousuNotices = tousuAreaMapper.getTousuNoticeByTime(time1, time2);
            for (TousuNotice t : tousuNotices) {
                String msg = "您有一条投诉【投诉ID:" + t.getTousuID() + "】跟进超时，请及时处理";
//                判断通知人是否在指定的时间之前添加了进程添加进程
                List<TsProcess> list = tousuAreaMapper.getTsProcessByTsIdAndUser(t.getTousuID(), t.getToUserName(), t.getCreateTime(), t.getLastTime());
                if (CollectionUtils.isEmpty(list)) {
                    String link = moaUrlSource.getBasicUrl() + "/new/#/operation/complaint/links?conplaintid=" + t.getToUserId() ;
                    smsService.sendOaAppAndWeiXing(msg, t.getToUserId() + "", 9, link);
                }
                tousuAreaMapper.updateStatusById(t.getId(), t.getToUserId(), t.getTousuID());
                // 待办事项处理完毕
                oaAsyncRabbitTemplate.convertAndSend("office.direct.to_do_list",JSON.toJSONString(TodoListMqVo.builder().type(17).businessNo(t.getId()+"").staffId(t.getToUserId()).mqKind(2).build()));

            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return "失败";
        }
        return "成功";

    }

    /**
     * 删除责任划分——门店,部门
     *
     * @param touSuDepartReq
     * @return
     */
    @Override
    @DS("officeWrite")
    public R<String> deleteTouSuDepart(TousuDepart touSuDepartReq) {
        OaUserBO oaUser = abstractCurrentRequestComponent.getCurrentStaffId();
        try {
            if (touSuDepartReq.getAreaId() != null) {
                //            在tousuArea中删除 areaId
                tousuAreaMapper.deleteTousuAreaByTouSuId(touSuDepartReq.getTousuId(), touSuDepartReq.getAreaId());
            }
            TousuDepart oldEntity = touSuDepartService.getById(touSuDepartReq.getId());
            touSuDepartService.delete(touSuDepartReq);
            if (CollectionUtils.isNotEmpty(touSuDepartReq.getCategoryIds())) {
                for (Integer cateId : touSuDepartReq.getCategoryIds()) {
                    tousuAreaMapper.deleteTousuCategoryRelation(cateId, touSuDepartReq.getTousuId(), touSuDepartReq.getId());
                }
            }
            if (Objects.nonNull(oldEntity) && Objects.nonNull(oldEntity.getTousuPoint())) {
                Integer tousuPoint = oldEntity.getTousuPoint() * -1;
                oldEntity.setTousuPoint(0);
                String bumen = Objects.equals(touSuDepartReq.getType(), 0) ? String.valueOf(touSuDepartReq.getAreaId()) : String.valueOf(touSuDepartReq.getDepartId());
                Integer type = Objects.equals(touSuDepartReq.getType(), 0) ? 1 : 2;
                this.tousuAreaDeduct(touSuDepartReq.getId(), bumen, type, tousuPoint, oaUser.getUserName(), oldEntity);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return R.error(ResultCode.PARAM_ERROR,"删除失败");
        }
        return R.success("删除成功");
    }

    /**
     * 推送微信现金消息
     */
    public void pushWeChatMoneyMsg(DiscountsReq res, int tenantId, OaUserBO oaUserBo) {
        Integer userId = res.getUserId();
        String mobile = res.getMobile();
        XtenantSubject xtenantSubject = touSuService.getXtenantSubject(tenantId);
        String mUrl = xtenantSubject.getMUrl();
        if (SpringContext.isDevEnvironment()) {
            mUrl = ComplainConstant.WEB_URL_M_DEV;
        }

        // 给评价人员推送APP消息（仅九机）
        if (res.getAppMsg() != null && res.getAppMsg() && userId != null) {
            String url = mUrl+ "/member/complaint/mine/" + res.getTousuId() + "?pusType=1";
            String msg = StrUtil.format(ComplainConstant.WE_CHAT_PUSH_MESSAGE_LOG,res.getPrice());
            pushAppMsg(userId, msg, url);
        }
        // 短信
        if (res.getDuanxin() != null && res.getDuanxin() && StringUtils.isNotEmpty(mobile)) {
            String url = mUrl+ "/member/complaint/mine/" + res.getTousuId() + "?pusType=2";
            String authorization = SecureUtil.md5(LocalDate.now().toString());
            String shortUrlStr = shortUrlCloud.shortUrl(url, authorization);
            String shortUrl = getShortUrl(shortUrlStr, url);
            String msg = StrUtil.format(ComplainConstant.WE_CHAT_PUSH_MESSAGE_SMS, res.getPrice(), shortUrl);
            String addTime = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm").format(LocalDateTime.now());
            smsService.sendSmsNew(mobile, msg, addTime, "系统", SmsMsgTypeEnum.NOTICE.getCode(), tenantId);
        }
    }

    private static String getShortUrl(String shortUrlStr, String longUrl) {
        JSONObject jsonObject = JSON.parseObject(shortUrlStr);
        String shortUrl = jsonObject.getString(ComplainConstant.SHORT_URL);
        if (StringUtils.isNotBlank(shortUrl)) {
            // 6i.cn/ 为短链接域名，后续同步输出咨询研发对应租户的短链接域名
            String prefix = ComplainConstant.JIUJI_SHORT_CHAIN_PREFIX;
            return prefix.concat(shortUrl);
        }
        return longUrl;
    }

    /**
     * 推送投诉优惠码消息
     */
    public void pushTousuYouhuimaMsg(DiscountsReq discounts, int xtenant, String code, OaUserBO oaUserBo) {
        Integer userId = discounts.getUserId();
        String mobile = discounts.getMobile();
        BigDecimal price = discounts.getPrice();
        String endTime = discounts.getTime();

        XtenantSubject xtenantSubject = touSuService.getXtenantSubject(xtenant);
        // 推送点击详情跳转优惠码详情
        String url = xtenantSubject.getMUrl() + "/member/coupon/detail?coupon=" + code + "&codeTitle=投诉专享优惠";
        String title = String.format("尊敬的顾客您好，九机对本次给您带来的不佳体验深表歉意，特向您赠送一张%s元代金券", price);
        String webName = EnumUtil.getMessageByCode(XtenantEnum.class, xtenant);
        String msg = "尊敬的顾客您好，" + webName + "对本次给您带来的不佳体验深表歉意，特向您赠送一张" + price + "元代金券【" + code + "】，有效期至" + endTime;

        // 给评价人员推送APP消息（仅九机）
        if (discounts.getAppMsg() != null && discounts.getAppMsg() && userId != null  && ActiveProfileJudgeUtil.isJiuJiEnvironment()) {
            pushAppMsg(userId, msg, url);
        }
        // 短信
        if (discounts.getDuanxin() != null && discounts.getDuanxin() && StringUtils.isNotEmpty(mobile)) {
            msg = "尊敬的顾客您好，" + webName + "对本次给您带来的不佳体验深表歉意，特向您赠送一张" + price + "元代金券(" + code + ")，有效期至" + endTime + "，可至" + webName + "app个人中心—优惠券查看具体使用条件。我们一直秉承“一个手机，一个朋友”的经营理念，望您继续监督我们的服务，祝您生活愉快。";
            String addTime = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm").format(LocalDateTime.now());
            smsService.sendSmsNew(mobile, msg, addTime, "系统", SmsMsgTypeEnum.NOTICE.getCode(), xtenant);
        }

        // 抽方法，这是之前的代码逻辑
        ZnSendConnBo znSendConn = new ZnSendConnBo();
        znSendConn.setKind(12);
        znSendConn.setSmsnumber(userId.toString());
        znSendConn.setTitle(title);
        znSendConn.setContent(msg);
        znSendConn.setLink(url);
        smsService.sendZnMsg(znSendConn);

    }

    public void pushAppMsg(Integer userId, String msg, String url) {
        String finalMsg = msg;

        // 商城消息中心
        UserMsg userMsg = new UserMsg()
                .setUserID(userId)
                .setTitle("投诉专享优惠")
                .setContent(finalMsg)
                .setLink(url)
                .setKindID(12)
                .setAddTime(new Date());
        HttpRequest.post(WEB_APP_MSG_URL).body(JSON.toJSONString(userMsg)).execute().body();

        String webUrl = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.M_URL, (int)Namespaces.get()).getData();

        Map<String, Object> params = new HashMap<String, Object>(16){
            private static final long serialVersionUID = -6571332082347964462L;
            {
                //  必填，应用名称，和App上报的注册信息保持一致； oa就是oa，商城是web
                put("appName", "web");
                // 必填，推送消息的标题，长度不能超过20字符，中英文均算一个字符；
                put("title", "投诉专享优惠");
                // 必填，推送消息的内容，建议长度不超过50字符，中英文均算一个字符，超过50个字符的，将自动转成前47个字符加上...的形式推送；
                put("content", finalMsg);
                // 可选，推送消息的业务附加消息，格式必须为json；
                put("extra", JSON.toJSONString(new HashMap<String, String>(){
                    private static final long serialVersionUID = 2096573057728210572L;
                    {
                        // 等ios和安卓统一之后再改
                        // put("value", StringUtils.isNotBlank(webUrl) ? webUrl + "/msgCenter" : "");
                        // put("type", "2");
                        put("value", "");
                        put("type", "3");
                    }
                }));
                // 可选，当使用别名推送消息时，指定该字段，最多100个alias；推送会员id
                put("alias", Collections.singletonList(userId));
            }
        };
        // 极光推送
        HttpRequest.post(WEB_APP_PUSH_URL).body(JSON.toJSONString(params)).execute().body();
    }

    public void updateTouSuStatus(int tousuid, Integer oldStatus, Integer status, String user, String remark) {
        tousuAreaMapper.updateTousuStatus(tousuid, status);
        TouSuModel touSuById = tousuAreaMapper.getTouSuById(tousuid);
        LocalDateTime addTime = touSuById.getAddTime();
        if (addTime != null) {
            Integer touSuTimerId = tousuAreaMapper.getTousuProcessTimeByTsId(tousuid);
            // 新版不用计时 ，新版用TousuNotice表既可以进行超时推送
            if (touSuTimerId != null && touSuTimerId == 0) {
                LocalDateTime processTime = addTime;
                if (addTime.getHour() > 21 || (addTime.getHour() == 21 && addTime.getMinute() > 30)) {
                    //第二天9点30开始计时
                    processTime = addTime.plusDays(1).plusHours(9).plusMinutes(30);
                } else if (addTime.getHour() < 9) {
                    //当天9点30开始计时
                    processTime = addTime.plusHours(9).plusMinutes(30);
                }
                tousuAreaMapper.insertTousuStatus(tousuid, processTime);
            }
            tousuAreaMapper.updateTousuProcessTime(user, status, tousuid);
            //仲裁推送
            try {
                // 赋值-1，数据库一大堆mainRole=0
                int roleId = -1;
                if (oldStatus != null && oldStatus != 0) {
                    if (TousuStateEnum.TousuStateEnum_9.getCode().equals(oldStatus) && status.equals(TousuStateEnum.TousuStateEnum_10.getCode())) {
                        // 运营中心总监
                        roleId = 226;
                    } else if (oldStatus.equals(status)) {
                        status = 4;
                        tousuAreaMapper.updateTousuStatus(tousuid, status);
                        // COO
                        roleId = 374;
                    }
                }
                // 九机取消以下推送逻辑：客诉处理状态变为“待复核”与“待仲裁”时会推送消息给运营中心总监，客诉处理状态变为“未受理、已受理、已还原、已处理、已界定、待整改、已整改、已完成”时会推送消息给COO
                if (InnerProfileJudgeUtil.isJiuJi() || InnerProfileJudgeUtil.isJiuJiTest()) {
                    return;
                }
                List<String> list = new ArrayList<>();
                String userIds = "";
                if (roleId != -1) {
                    list = tousuAreaMapper.getCh999IdByMainRole(roleId);
                    userIds = StringUtils.join(list, ",");
                }
                if (CollectionUtils.isNotEmpty(list) && StringUtils.isNotEmpty(userIds)) {
                    remark = remark.replace("<span style='color:red;'>", "").replace("</span>", "");
                    String reason = StringUtils.isEmpty(remark) ? "无" : remark;
                    String link = moaUrlSource.getBasicUrl() + "/new/#/operation/complaint/links?conplaintid=" + tousuid;
                    smsService.sendOaAppAndWeiXing("投诉ID【" + tousuid + "】 需要您仲裁,理由：" + reason, userIds, 9, link);
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }

    }


    /**
     * 保存典型投诉 心声投诉
     *
     * @param archiveCategory 典型投诉
     * @param isxinsheng      心声投诉
     * @return
     */
    @Override
    public String setArchiveCategory(Integer tsId, Integer archiveCategory, Boolean isxinsheng, String processUser,
                                     Boolean showWeb, String dealUser) {
        OaUserBO oaUser = abstractCurrentRequestComponent.getCurrentStaffId();

        TouSuModel touSuModel = tousuAreaMapper.getTouSuById(tsId);
        if (touSuModel == null) {
            throw new RuntimeException("该条投诉不存在");
        }
        if (archiveCategory != null) {
            touSuModel.setArchiveCategory(archiveCategory);
            if (archiveCategory == 1) {
                TouSuProcessBO process = new TouSuProcessBO();
                process.setTsId(touSuModel.getId()).setOpUser(oaUser.getUserName()).
                        setDsc("设置为" + EnumUtil.getMessageByCode(TousuArchiveCategoryEnum.class, touSuModel.getArchiveCategory())).
                        setNotice(false);
                touSuService.addTsProcess(process, touSuModel);
            }
        }
        if (isxinsheng != null) {
            touSuModel.setIsxinsheng(isxinsheng);
            if (isxinsheng) {
                TouSuProcessBO process = new TouSuProcessBO();
                process.setTsId(touSuModel.getId()).setOpUser(oaUser.getUserName()).
                        setDsc("添加至心声社区").
                        setNotice(false);
                touSuService.addTsProcess(process, touSuModel);
            }
        }
        if (StringUtils.isNotEmpty(processUser)) {
            touSuModel.setProcessUser(processUser);
        }
        if (StringUtils.isNotEmpty(dealUser)) {
            touSuModel.setDealUser(dealUser);
            TouSuProcessBO process = new TouSuProcessBO();
            List<Integer> staffIds = Arrays.stream(org.apache.commons.lang3.StringUtils.split(dealUser, ",")).filter(org.apache.commons.lang3.StringUtils::isNumeric).map(Integer::parseInt).collect(Collectors.toList());
            List<String> dealUsers = ch999UserService.getCh999UsersById(staffIds).stream().map(Ch999User::getCh999Name).collect(Collectors.toList());
            process.setTsId(touSuModel.getId()).setOpUser(oaUser.getUserName()).
                    setDsc("修改处理人为：" + org.apache.commons.lang3.StringUtils.join(dealUsers, ",")).
                    setNotice(false);
            touSuService.addTsProcess(process, touSuModel);
        } else if (StringUtils.isBlank(dealUser) && StringUtils.isNotBlank(touSuModel.getDealUser())) {
            touSuService.updateTsDealUser(touSuModel.getId());
            TouSuProcessBO process = new TouSuProcessBO();
            process.setTsId(touSuModel.getId()).setOpUser(oaUser.getUserName()).setDsc("修改处理人为无").setNotice(false);
            touSuService.addTsProcess(process, touSuModel);
        }
        // 更新是否在网站展示
        if (Objects.isNull(showWeb)) {
            showWeb = false;
        }
        Boolean originShowWeb = touSuModel.getShowWeb();
        if (!Objects.equals(originShowWeb, showWeb)) {
            touSuModel.setShowWeb(showWeb);
            TouSuProcessBO process = new TouSuProcessBO();
            process.setTsId(touSuModel.getId()).setOpUser(oaUser.getUserName()).
                    setDsc((Boolean.TRUE.equals(showWeb) ? "设置为" : "取消") + "在网站展示").
                    setNotice(false);
            // 是否网站显示未勾选，填写了跟进人，没有勾选/取消勾选，不用保存 (仅九机)
            boolean notSave = org.apache.commons.lang3.StringUtils.isNotBlank(processUser) && !Boolean.TRUE.equals(originShowWeb) && Boolean.FALSE.equals(showWeb);
            if (!notSave || !XtenantEnum.isJiujiXtenant()) {
                touSuService.addTsProcess(process, touSuModel);
            }
        }

        int count = tousuAreaService.updateTousuById(touSuModel);
        if (count > 0) {
            return "成功";
        }

        return "失败";
    }

    @Override
    public String setArchiveCategoryV2(TousuAddProcessUserReq tousuAddProcessUserReq) {
        OaUserBO oaUser = abstractCurrentRequestComponent.getCurrentStaffId();
        TouSuModel touSuModel = tousuAreaMapper.getTouSuById(tousuAddProcessUserReq.getId());
        if (touSuModel == null) {
            throw new RuntimeException("该条投诉不存在");
        }
        if (Objects.nonNull(tousuAddProcessUserReq.getArchiveCategory())) {
            touSuModel.setArchiveCategory(tousuAddProcessUserReq.getArchiveCategory());
            if (Objects.equals(tousuAddProcessUserReq.getArchiveCategory(), 1)) {
                TouSuProcessBO process = new TouSuProcessBO();
                process.setTsId(touSuModel.getId()).setOpUser(oaUser.getUserName()).
                        setDsc("设置为" + EnumUtil.getMessageByCode(TousuArchiveCategoryEnum.class, touSuModel.getArchiveCategory())).
                        setNotice(false);
                touSuService.addTsProcess(process, touSuModel);
            }
        }
        if (Objects.nonNull(tousuAddProcessUserReq.getIsxinsheng())) {
            touSuModel.setIsxinsheng(tousuAddProcessUserReq.getIsxinsheng());
            if (Boolean.TRUE.equals(tousuAddProcessUserReq.getIsxinsheng())) {
                TouSuProcessBO process = new TouSuProcessBO();
                process.setTsId(touSuModel.getId()).setOpUser(oaUser.getUserName()).
                        setDsc("添加至心声社区").
                        setNotice(false);
                touSuService.addTsProcess(process, touSuModel);
            }
        }
        if (StringUtils.isNotEmpty(tousuAddProcessUserReq.getProcessUser())) {
            String oldProcessUser = touSuModel.getProcessUser();
            touSuModel.setProcessUser(tousuAddProcessUserReq.getProcessUser());
            if (!StringUtils.equals(oldProcessUser, tousuAddProcessUserReq.getProcessUser())) {
                TouSuProcessBO process = new TouSuProcessBO();
                process.setTsId(touSuModel.getId()).setOpUser(oaUser.getUserName()).
                        setDsc("修改跟进人为：" + tousuAddProcessUserReq.getProcessUser()).
                        setNotice(false);
                touSuService.addTsProcess(process, touSuModel);
            }
        }
        if (StringUtils.isNotEmpty(tousuAddProcessUserReq.getDealUser())) {
            String oldDealUser = touSuModel.getDealUser();
            touSuModel.setDealUser(tousuAddProcessUserReq.getDealUser());
            if (!StringUtils.equals(tousuAddProcessUserReq.getDealUser(), oldDealUser)) {
                TouSuProcessBO process = new TouSuProcessBO();
                List<Integer> staffIds = Arrays.stream(org.apache.commons.lang3.StringUtils.split(tousuAddProcessUserReq.getDealUser(), ",")).filter(org.apache.commons.lang3.StringUtils::isNumeric).map(Integer::parseInt).collect(Collectors.toList());
                List<String> dealUsers = ch999UserService.getCh999UsersById(staffIds).stream().map(Ch999User::getCh999Name).collect(Collectors.toList());
                process.setTsId(touSuModel.getId()).setOpUser(oaUser.getUserName()).
                        setDsc("修改处理人为：" + org.apache.commons.lang3.StringUtils.join(dealUsers, ",")).
                        setNotice(false);
                touSuService.addTsProcess(process, touSuModel);
            }
        } else if (StringUtils.isBlank(tousuAddProcessUserReq.getDealUser()) && StringUtils.isNotBlank(touSuModel.getDealUser())) {
            touSuModel.setDealUser("");
            touSuService.updateTsDealUser(touSuModel.getId());
            TouSuProcessBO process = new TouSuProcessBO();
            process.setTsId(touSuModel.getId()).setOpUser(oaUser.getUserName()).setDsc("修改处理人为无").setNotice(false);
            touSuService.addTsProcess(process, touSuModel);
        }
        if (StringUtils.isNotBlank(tousuAddProcessUserReq.getComplaintDemarcationContent())) {
            try {
                // 这里的界定目前不用传附件
                BaseCommentAddReq addReq = new BaseCommentAddReq();
                addReq.setRefId(touSuModel.getId().longValue());
                addReq.setRefType(BaseCommentRefTypeEnum.COMPLAINT_DEMARCATION.getCode());
                addReq.setContent(tousuAddProcessUserReq.getComplaintDemarcationContent());
                addReq.setUserId(oaUser.getUserId());
                addReq.setUserName(oaUser.getUserName());
                log.info("pc保存，添加界定,addReq:{}", JSON.toJSONString(addReq));
                R<Boolean> comment4Cloud = baseCommentCloud.addComment4Cloud(addReq);
                log.info("pc保存，添加界定,res:{}", JSON.toJSONString(comment4Cloud));
            } catch (Exception e) {
                log.error("pc保存，添加界定处理异常", e);
            }
        }
        int count = tousuAreaService.updateTousuById(touSuModel);
        if (count > 0) {
            return "成功";
        }
        return "失败";
    }

    @Override
    public String setShowWeb(TousuAddProcessUserReq tousuAddProcessUserReq) {
        OaUserBO oaUser = abstractCurrentRequestComponent.getCurrentStaffId();
        Boolean showWeb = tousuAddProcessUserReq.getShowWeb();
        // 更新是否在网站展示
        if (Objects.isNull(tousuAddProcessUserReq.getShowWeb())) {
            showWeb = false;
        }
        TouSuModel touSuModel = tousuAreaMapper.getTouSuById(tousuAddProcessUserReq.getId());
        if (touSuModel == null) {
            throw new RuntimeException("该条投诉不存在");
        }
        Boolean originShowWeb = touSuModel.getShowWeb();
        if (!Objects.equals(originShowWeb, showWeb)) {
            touSuModel.setShowWeb(showWeb);
            TouSuProcessBO process = new TouSuProcessBO();
            process.setTsId(touSuModel.getId()).setOpUser(oaUser.getUserName()).
                    setDsc((Boolean.TRUE.equals(showWeb) ? "设置为" : "取消") + "在网站展示").
                    setNotice(false);
            touSuService.addTsProcess(process, touSuModel);
            int count = tousuAreaService.updateTousuById(touSuModel);
            if (count > 0) {
                return "成功";
            }
            return "失败";
        }
        return "失败";
    }

    /**
     * 修改联系电话
     *
     * @param ch999User
     * @param moblie
     * @param touSuModel
     */
    @DS("oanew")
    public String saveMobile(String ch999User, String moblie, TouSuModel touSuModel) {
        if (StringUtils.isEmpty(moblie) && moblie.matches("^1[3|4|5|7|8][0-9]{9}$")) {
            log.error("手机号码格式不正确");
            return "手机号码格式不正确";
        }
        String oldMobile = "";
        if (touSuModel.getMobile() != null) {
            oldMobile = touSuModel.getMobile();
        }
        if (StringUtils.equals(moblie, oldMobile)) {
            return "成功";
        }
        touSuModel.setMobile(moblie);
        int count = tousuAreaService.updateTousuById(touSuModel);
        if (count > 0) {
            //写日志
            TouSuProcessBO process = new TouSuProcessBO();
            process.setTsId(touSuModel.getId());
            process.setOpUser(ch999User);
            process.setShow(true);
            process.setNotice(false);
            process.setDsc("联系电话由" + oldMobile + "修改为" + moblie);
            process.setAttachFiles("");
            touSuService.addTsProcess(process, null);
            return "成功";
        }
        return "失败";
    }


    @Override
    @DS("officeWrite")
    public String addNewTouSu(TouSuNewReq touSuModel) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        TouSuModel ts = new TouSuModel();
        if (oaUserBO != null) {
            ts.setInuser(oaUserBO.getUserName());
            if (XtenantEnum.isJiujiXtenant()){
                ts.setXtenant(oaUserBO.getXTenant());
            }
        }
        BeanUtils.copyProperties(touSuModel, ts);
        try {
            if (CollectionUtils.isNotEmpty(touSuModel.getFiles())) {
                String attachIds = touSuDepartService.uploadFiles(touSuModel.getFiles());
                ts.setAttachIds(attachIds);
            }
            LocalDateTime now = LocalDateTime.now();
            ts.setAddTime(now);
            ts.setHuanyuantimeout(now.plusHours(1));
            ts.setDealTimeout(now.plusHours(60));
            tousuAreaService.save(ts);
            if (ts.getId() != null && StringUtils.isNotEmpty(ts.getContent())) {
                Integer id = ts.getId();
                TousuDepart tousuDepart = new TousuDepart();
                tousuDepart.setTousuId(id);
                if (touSuModel.getDepartId() != null) {
                    tousuDepart.setDepartId(touSuModel.getDepartId());
                    tousuDepart.setType(1);
                    this.addTouSuDepart(tousuDepart);
                }
                if (touSuModel.getAreaId() != null) {
                    tousuDepart.setType(0);
                    tousuDepart.setAreaId(touSuModel.getAreaId());
                    this.addTouSuDepart(tousuDepart);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return "失败";
        }
        return ts.getId().toString();
    }

    @Override
    @DS("officeWrite")
    @Transactional
    public R<String> highOpinion(HighOpinionReq req,Integer xtenant) {
        if (req == null || (req.getEvaluateId() == null || req.getEvaluateId() == 0L) || CollectionUtils.isEmpty(req.getComments())){
            return R.error("参数异常");
        }
        Evaluate evaluate = evaluateService.getById(req.getEvaluateId());
        if (evaluate == null){
            return R.error("评论不存在");
        }
        req.setComments(req.getComments().stream().filter(i -> (i.getStaffId() != null  && i.getStaffId() != 0) && StringUtils.isNotBlank(i.getContent())).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(req.getComments())){
            return R.error("参数异常");
        }
        // 如果提交过不能二次提交
        if (this.count(new LambdaQueryWrapper<TouSuModel>().eq(TouSuModel::getEvaluateId,req.getEvaluateId())) > 0){
            return R.success("已经提交，无需二次提交");
        }
        if (xtenant == null){
            Namespaces.get();
        }
        for (HighOpinionReq.CommentContent Comment : req.getComments()){
            TouSuModel model = new TouSuModel();
            BeanUtils.copyProperties(req,model);
            model.setType(1);  // 网站录入
            model.setTag(3); //  表扬
            model.setStates(TousuStateEnum.TousuStateEnum_3.getCode());
            model.setNewTag(model.getTag());
            model.setInuser("系统");
            model.setAddTime(LocalDateTime.now());
            model.setContent(Comment.getContent());
            model.setEvaluateId(Long.valueOf(req.getEvaluateId()));
            if (XtenantEnum.isJiujiXtenant()){
               model.setXtenant(xtenant == null ? abstractCurrentRequestComponent.getCurrentXtenant() : xtenant);
            }
            tousuAreaService.save(model);
            TouSuZenRenRen ren = new TouSuZenRenRen();
            ren.setTousuId(model.getId());
            ren.setUserId(Comment.getStaffId());
            ren.setUserName(Comment.getStaffName());
            tousuAreaMapper.saveTouSuZenRenRen(ren);

            // 进程
            TsProcess tsProcess = new TsProcess();
            tsProcess.setTsId(model.getId());
            tsProcess.setOpUser("系统");
            tsProcess.setCate(0);
            tsProcess.setIntime(LocalDateTime.now());
            tsProcess.setShowWeb(Boolean.FALSE);
            tsProcess.setIsShow(Boolean.TRUE);
            tsProcess.setDsc("您的表扬反馈我们已收到，感谢您对员工服务的高度认可和支持，我们也将对员工进行表扬，非常感谢您的反馈和支持。");

            TsProcess evaluateRelateProcess = new TsProcess();
            evaluateRelateProcess.setTsId(model.getId());
            evaluateRelateProcess.setOpUser("系统");
            evaluateRelateProcess.setCate(4);
            evaluateRelateProcess.setIntime(LocalDateTime.now());
            evaluateRelateProcess.setShowWeb(Boolean.FALSE);
            evaluateRelateProcess.setIsShow(Boolean.FALSE);
            evaluateRelateProcess.setDsc(String.format("顾客通过<a href='%s/new/#/operation/complaint/links?evaluateid=%s'>%s</a>添加了一条超五星好评表扬。",sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.MOA_URL, xtenant).getData(),evaluate.getId(),evaluate.getId()));

            TsProcess autoCompleteProcess = new TsProcess();
            autoCompleteProcess.setTsId(model.getId());
            autoCompleteProcess.setOpUser("系统");
            autoCompleteProcess.setCate(4);
            autoCompleteProcess.setIntime(LocalDateTime.now());
            autoCompleteProcess.setShowWeb(Boolean.FALSE);
            autoCompleteProcess.setIsShow(Boolean.FALSE);
            autoCompleteProcess.setDsc("超五星好评，系统自动变更为已完成状态");

            tsProcessService.saveProcess(tsProcess);
            tsProcessService.saveProcess(evaluateRelateProcess);
            tsProcessService.saveProcess(autoCompleteProcess);
        }
        // 标识评论五星好评过
        evaluateService.update(new LambdaUpdateWrapper<Evaluate>().eq(Evaluate::getId,evaluate.getId()).set(Evaluate::getOverFiveFlg,1));
        return R.success("操作成功");
    }


    public void addNoticeTouSu(int id, int touSer, String username, String content, LocalDateTime time,TouSuModel touSuModel,String handler,Integer state,String platform) {
        String moaUrl = sysConfigClient.getValueByCodeAndXtenant(SysConfigConstant.MOA_URL, (int) Namespaces.get()).getData();
        Integer xtenant = userComponent.getXtenant(true);
        TodoListMqVo todoListMqVo = null;
        TousuNotice tousuNotice = new TousuNotice();
        tousuNotice.setTousuID(id);
        tousuNotice.setToUserId(touSer);
        tousuNotice.setToUserName(username);
        tousuNotice.setNoticeContent(content);
        tousuNotice.setNoticeType(time != null ? 1 : 0);
        tousuNotice.setLastTime(time != null ? time : LocalDateTime.now());
        //设置为已通知
        tousuNotice.setNotice(true);
        tousuAreaMapper.addNoticeTousu(tousuNotice);
        if (time != null) {
            // 待办事项MQ数据组装
            todoListMqVo = TodoListMqVo.builder().type(17).businessNo(tousuNotice.getId()+"").staffId(touSer)
                    .msg(String.format("投诉%s需在%s前跟进！", id, DateTimeFormatter.ofPattern("MM-dd HH:mm").format(time)))
                    .link(ComplainConstant.PARTNER.equals(touSuModel.getType()) ? moaUrl + "/new/#/operation/feedback/jiujiDetail/" + id : moaUrl + "/new/#/operation/complaint/links?conplaintid=" + id)
                            .mqKind(1).build();
        }
        //九机
        if (touSuModel.getSource() == null) {
            String link = moaUrl + "/new/#/operation/complaint/links?conplaintid=" + id;
            BbsxpUsers bbsxpUsers =
                    bbsxpUsersMapper.getInfoByUserId(new LambdaUpdateWrapper<BbsxpUsers>().eq(BbsxpUsers::getId,
                            touSuModel.getUserId()));
            String user = "-";
            String levelName = EUserClassNewEnum.EUserClassNew_0.getMessage();
            if (bbsxpUsers != null) {
                user = Optional.ofNullable(bbsxpUsers.getUserName()).orElse("-");
                levelName = CommonUtil.getUserType(bbsxpUsers.getUserclass() == null ? 0 : bbsxpUsers.getUserclass());
            }
            String sendContext = "投诉ID:<a href='" + link + "'>" + id + "</a>\n会员:" + user + "\n联系号码:" + touSuModel.getMobile() + "\n" + content + "*" + EnumUtil.getMessageByCode(TousuStateEnum.class, state) + " 【" + handler + "】";

            if (SpringContext.isJiuJiEnvironment()) {
                String complainContent = Optional.ofNullable(touSuModel.getContent()).orElse("");
                ImMessageReq imMessageReq = ImMessageReq.builder()
                        .fromOa(true)
                        .sendUserId(abstractCurrentRequestComponent.getCurrentStaffId().getUserId())
                        .subTenant(xtenant)
                        .receiveUserIds(Lists.newArrayList(touSer))
                        .messageBody(
                                ImMessageReq.MessageBodyDTO.builder()
                                        .platform(platform)
                                        .type("complaints")
                                        .title("投诉消息")
                                        .content(complainContent.length() > 100 ? complainContent.substring(0, 100) + "..." : complainContent)
                                        .number(String.valueOf(touSuModel.getId()))
                                        .status(touSuModel.getStates())
                                        .statusName(EnumUtil.getMessageByCode(EStatsEnum.class, touSuModel.getStates()))
                                        .subject(EnumUtil.getMessageByCode(XtenantEnum.class, touSuModel.getXtenant() == null ? 0 : touSuModel.getXtenant()))
                                        .level(levelName)
                                        .remark(Optional.ofNullable(content).orElse("").length() > 40 ? content.substring(0, 40) + "..." : content)
                                        .link(link).build()
                        ).build();
                String url = SpringContext.isDevEnvironment() ? ComplainConstant.IM_DEV_PUSH_MESSAGE_URL : ComplainConstant.IM_PRO_PUSH_MESSAGE_URL;
                String body = HttpRequest.post(url)
                        .header("xtenant", String.valueOf(Namespaces.get()))
                        .header("authorization", abstractCurrentRequestComponent.getCurrentStaffId().getToken())
                        .body(JSON.toJSONString(imMessageReq), "application/json")
                        .execute().body();
                log.info("im消息推送返回body信息{},url:{}", body, url);
            } else {
                msgPushCloud.singleSend(touSer, sendContext, link, OaMesTypeEnum.TSCLJCTZ.getCode(), xtenant);
            }
            // 记录推送日志
            OaTousuMessageLogDTO tousuMessageLogDTO = new OaTousuMessageLogDTO()
                    .setUserId(touSer)
                    .setTousuId(id)
                    .setContent(sendContext)
                    .setLink(link)
                    .setMsgType(OaMesTypeEnum.TSCLJCTZ.getCode())
                    .setXtenant(xtenant)
                    .setSource(MessageLogSourceEnum.ADD_TOUSU_PROCESS.getCode());
            oaMessageLogCloud.saveTousuLog(tousuMessageLogDTO);
            log.info("站内信：投诉{}的进程处理，已远程调用/office/api/cloudComponent/pushMsg/singleSend/v1进行站内消息推送", id);
        } else {
            String link=moaUrl+"/new/#/operation/feedback/jiujiDetail/"+id;
            String sendContext = "投诉ID:<a href='" + link + "'>" + id + "</a>\n投诉人:" + touSuModel.getMemberName() + "\n合作伙伴:" + touSuModel.getXtenantName() + "\n电话:" + touSuModel.getMobile() + "\n" + content + "*" + EnumUtil.getMessageByCode(TousuStateEnum.class, state) + " 【" + handler + "】";
            msgPushCloud.singleSend(touSer, sendContext, link, OaMesTypeEnum.HZHBTSJYTZ.getCode(), xtenant);
            // 记录推送日志
            OaTousuMessageLogDTO tousuMessageLogDTO = new OaTousuMessageLogDTO()
                    .setUserId(touSer)
                    .setTousuId(id)
                    .setContent(sendContext)
                    .setLink(link)
                    .setMsgType(OaMesTypeEnum.HZHBTSJYTZ.getCode())
                    .setXtenant(xtenant)
                    .setSource(MessageLogSourceEnum.ADD_TOUSU_PROCESS_HZHB.getCode());
            oaMessageLogCloud.saveTousuLog(tousuMessageLogDTO);
            log.info("站内信：合作伙伴投诉建议{}，已远程调用/office/api/cloudComponent/pushMsg/singleSend/v1进行站内消息推送", id);
        }

        if (todoListMqVo != null  && XtenantJudgeUtil.isJiujiMore()){
            // 发送投诉待办MQ
            oaAsyncRabbitTemplate.convertAndSend("office.direct.to_do_list",JSON.toJSONString(todoListMqVo));
        }
    }

    @DS("officeWrite")
    public Boolean updateTousuModel(TouSuModel touSu) {
        return this.updateById(touSu);
    }
}
