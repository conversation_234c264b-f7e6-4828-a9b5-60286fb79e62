/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.oacore.thirdplatform.order.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 第三方订单Entity
 *
 * <AUTHOR>
 * @date 2021-05-26
 */
@Data
@TableName("third_platform_order")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "第三方订单实体")
public class Order extends Model<Order> {
    private static final long serialVersionUID = 4L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /**
     * 会员号码
     * 该值是美团回传过来的对应我们系统的 userid
     */
    private String cardCode;
    /**
     * 平台编码（JD-京东;MT-美团）
     */
    private String platCode;

    /**
     * 平台订单id
     */
    private String orderId;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 商户编码
     */
    @TableField("org_code")
    private String tenantCode;

    /**
     * 本地门店id
     */
    private Integer areaId;

    /**
     * 本地门店编码
     */
    private String areaCode;

    /**
     * 平台门店编码
     */
    private String storeCode;

    /**
     * 买家账号
     */
    private String buyerPin;

    /**
     * 收货人名称
     */
    private String buyerName;

    /**
     * 收货人地址
     */
    private String buyerAddress;

    /**
     * 收货人手机号
     */
    private String buyerMobile;

    /**
     * 收货人电话
     */
    private String buyerTel;

    /**
     * 成交时间
     */
    private Date tradeTime;

    /**
     * 订单商品销售总金额
     */
    private Double totalMoney;

    /**
     * 订单优惠金额
     */
    private Double discountMoney;

    /**
     * 订单货款总金额 [订单货款金额（订单总金额-商家优惠金额）]
     */
    private Double goodMoney;

    /**
     * 用户积分抵扣金额
     */
    private Double pointMoney;

    /**
     * 订单实际运费
     */
    private Double freightMoney;

    /**
     * 用户应付金额
     */
    private Double payableMoney;

    /**
     * 收货人市id
     */
    private String buyerCity;

    /**
     * 收货人市名称
     */
    private String buyerCityName;

    /**
     * 收货人区县id
     */
    private String buyerCountry;

    /**
     * 收货人区县名称
     */
    private String buyerCountryName;

    /**
     * 订单备注
     */
    private String buyerRemark;

    /**
     * 平台订单状态
     */
    private Integer orderStatus;

    /**
     * 本地订单id
     */
    private Long subId;

    /**
     * 订单生成消息
     */
    private String subMessage;

    /**
     * 支付状态，对OA订单是否已支付
     */
    private Integer payStatus;

    /**
     * 商家承担金额
     */
    private Double venderMoney;

    /**
     * 平台承担金额
     */
    private Double platMoney;

    /**
     * 预计送达时间
     */
    private Date estimateArrivalTime;


    /**
     * 0--普通配送
     * 1--用户到店自取
     */
    private Integer pickType;

    /**
     * 0--未手动生成订单
     * 1--已手动生成订单
     */
    private Integer generateManualSubFlag;


    /**
     * 订单取消状态 1-取消
     */
    private Integer cancelCheck;

    /**
     * 订单取消原因
     */
    @TableField("cancel_reason")
    private String cancelReason;

    /**
     * 良品订单1 新品订单0
     * @see OrderTypeEnum
     */
    private Integer type;

    /**
     * 客户号码的密文字段存储
     */
    private String encryptPostTel;
    /**
     * 客户地址的密文字段存储
     */
    private String encryptDetail;
    /**
     * 客户姓名的密文字段存储
     */
    private String encryptPostReceiver;

    /**
     * 收货地址经纬度
     */
    @TableField("buyer_position")
    private String buyerPosition;

    /**
     * 尽快送达 true 预约派送 false
     */
    @TableField("early_arrival")
    private Boolean earlyArrival;

    /**
     * 预约最早送达时间
     */
    @TableField("earliest_receipt_time")
    private LocalDateTime earliestReceiptTime;

    /**
     * 预约最晚送达时间
     */
    @TableField("latest_receipt_time")
    private LocalDateTime latestReceiptTime;

    /**
     * 业务单号
     */
    @TableField("biz_order_id")
    private String bizOrderId;

    /**
     * 国补订单标志
     */
    @TableField("government_subsidy_flag")
    private Boolean governmentSubsidyFlag;

    /**
     * 国补订单补贴金额
     */
    @TableField("government_subsidy_money")
    private BigDecimal governmentSubsidyMoney;


    /** 内部流转字段 start */
    /**
     * 符合订单的商品
     */
    @TableField(exist = false)
    private List<OrderItem> orderItemList;

    /**
     * 是否为部分退
     */
    @TableField(exist = false)
    @JSONField(serialize = false, deserialize = false)
    @JsonIgnore
    private Boolean isPartRefund;

    /*** 内部流转字段 end*/


    @Getter
    @AllArgsConstructor
    public enum OrderTypeEnum implements CodeMessageEnumInterface {
        NEW_ORDER(0, "新品订单"),
        lP_ORDER(1, "良品订单");

        @EnumValue
        private final Integer code;
        private final String message;
    }

}
