package com.jiuji.oa.oacore.oaorder.service;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.res.PageRes;
import com.jiuji.oa.oacore.oaorder.bo.JiFen1;
import com.jiuji.oa.oacore.oaorder.po.Evaluate;
import com.jiuji.oa.oacore.oaorder.po.EvaluateDepart;
import com.jiuji.oa.oacore.oaorder.req.EvaluateListQuery;
import com.jiuji.oa.oacore.oaorder.req.EvaluateRedPackAddReq;
import com.jiuji.oa.oacore.oaorder.req.EvaluateReq;
import com.jiuji.oa.oacore.oaorder.req.SendMaReq;
import com.jiuji.oa.oacore.oaorder.res.*;
import com.jiuji.oa.oacore.oaorder.vo.req.ChangeEvaluateUserReq;
import com.jiuji.oa.oacore.oaorder.vo.req.EvaluateRedPackReq;
import com.jiuji.oa.oacore.oaorder.vo.req.WuXiaoReq;
import com.jiuji.oa.oacore.oaorder.vo.res.EvaluateEndInfo;
import com.jiuji.oa.oacore.tousu.res.EvaluateOrderInfoRes;
import com.jiuji.oa.oacore.tousu.res.EvaluateRewardInfo;
import com.jiuji.oa.oacore.tousu.res.HighOpinionRes;
import com.jiuji.oa.office.evaluate.vo.EvaluateDetailVO;
import com.jiuji.tc.common.vo.R;
import com.jiuji.wcf.wcfclient.csharp.gen.member;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-31
 */
public interface EvaluateService extends IService<Evaluate> {
    /**
     * 获取评价内容
     * @param current
     * @param size
     * @param type
     * @param authId
     * @return
     */
    PageRes<EvaluateInfoRes> getEvaluatePages(Integer current, Integer size, Integer type, Integer authId);

    /**
     *由于数据量较大获取100条进行缓存，从这100条缓存中进行获取
     * @param type
     * @param authId
     * @param jiujiMore 是否是九机环境
     * @return
     */
//    @Cached(name = "getEvaluateListByCacheV2",expire = 120,timeUnit = TimeUnit.MINUTES)
//    @CacheRefresh(refresh = 60, timeUnit = TimeUnit.MINUTES)
    List<EvaluateInfoRes> getEvaluateListByCache(Integer type, Integer authId, boolean jiujiMore);

    IPage<Evaluate> getPage(IPage<Evaluate> page, Wrapper<Evaluate> queryWrapper);


    member.EvaluateResult evaluateResult(member.EvaluateConn conn, int xtenant);

    ResultModelBO addEvaluateNew(Evaluate info, String type, boolean isapp, int sourceFrom, String version);

    String jiFenManage1(JiFen1 jifenM, String ip);

    /**
     * 获取评价分页数据 从C# EvaluateService.EvaluateListV2重构
     * @param
     * @param req
     * @param areaMap 门店id和门店号映射
     * @return
     */
    R<Page<EvaluateListVO>> getEvaluateList(EvaluateListQuery req, Map<Integer, String> areaMap,boolean isExport);

    R<Page<EvaluateListVO>> getEvaluateStatisticsList(String evaluateIdsKey, Integer page, Integer size);

    /**
     * 根据客评id获取评分记录展示
     * @param evaluateId
     * @return
     */
    R<List<EvaluateJobScoreVO>> getStaffScoreRedpack(Integer evaluateId);

    /**
     * 添加客评积分红包 c# 方法evaluateService.AddStaffScoreRedpack
     * @param req
     * @return
     */
    R addStaffScoreRedpack(OaUserBO oaUserBO, EvaluateRedPackAddReq req);

    R exportExcel(EvaluateListQuery req, HttpServletResponse response);

    /**
     * 获取红包激励数据
     * @param req
     * @return
     */
    R<List<EvaluateRedPackVO>> getEvaluateRedPackData(EvaluateRedPackReq req);

    R exportRedpackExcel(EvaluateRedPackReq req, HttpServletResponse response);

    R<EvaluateDetailVO> getEvaluateDetail(Integer evaluateId);

    /**
     * 发送优惠码 c# 方法evaluateService.SendMa
     * @param req
     * @return
     */
    R<Boolean> sendMa(SendMaReq req);

    /**
     * 同意申诉，设置客评无效 c# 方法evaluateService.SetWuxiao
     * @param req
     * @param userName
     * @return
     */
    R<Boolean> updateWuxiao(WuXiaoReq req, String userName);

    /**
     * tcc 模式
     * 同意申诉，设置客评无效 c# 方法evaluateService.SetWuxiao
     * @param req
     * @param userName
     * @return
     */
    R<Boolean> updateWuxiaoWithTcc(WuXiaoReq req, String userName) throws Exception;

    /**
     * 设置客评无效转有效
     */
    R<Boolean> updateWuxiaoToYouxiao(WuXiaoReq req, String userName);

    /**
     * 更改评价人 c# 方法evaluateService.editEvaluateUser
     * @param req
     * @param userName
     * @return
     */
    R<Boolean> changeEvaluateUser(ChangeEvaluateUserReq req, String userName);

    /**
     * 获取客评
     *
     * @param evaluateId 客评id
     * @return 客评
     */
    Evaluate getEvaluate(Integer evaluateId);

    /**
     * 修改客评
     *
     * @param evaluate 客评
     */
    void updateEvaluate(Evaluate evaluate);

    EvaluateRes saveEvaluate(EvaluateReq evaluateReq);

    void addEvaluateDepart(EvaluateDepart evaluateDepart);

    void modifyEvaluateDepart(EvaluateDepart evaluateDepart);

    void deleteEvaluateDepart(EvaluateDepart evaluateDepart);

    EvaluateEndInfo getEvaluateEndInfo(Long evaluateId);

    /**
     * 获取售后统计
     *
     * @return 售后统计
     */
    EvaluateShouhouBO getShouhouEvaluate();

    R<HighOpinionRes> getOrderHighOpinion(Integer memberId,Long subId, String type, Boolean ignoreEvaluated);

    /**
     * 员工打赏信息 c端专用
     * @param memberId
     * @param ch999Id
     * @return
     */
    R<HighOpinionRes> getOrderHighOpinionToCV2(Integer memberId,Long subId, String type, Boolean ignoreEvaluated, Integer ch999Id);

    /**
     * 员工打赏信息 c端专用 -- 废弃
     * @param memberId
     * @param ch999Id
     * @return
     */
    R<HighOpinionRes> getOrderHighOpinionToC(Integer memberId,Integer ch999Id);

    R<String> HighOpinionIgnore(Long subId, String type);

    R<List<EvaluateRewardInfo>> getStaffRewardList(Integer current, Integer size, Integer staffId);

    R<HighOpinionRes.StaffMessage> getStaffRewardSimpleMsg(Integer staffId);

    /**
     * 获取交易完成时间在24小时内的且没有评价过的订单
     *
     * @param userId
     * @return
     */
    EvaluateOrderInfoRes getCompleteOrderInfoWithinHours(Integer userId);

    /**
     * 商品分数转服务员工分数
     * 
     * @param evaluateId
     * @return
     */
    Boolean productScoreToEvaluateScore(Integer evaluateId);
}
