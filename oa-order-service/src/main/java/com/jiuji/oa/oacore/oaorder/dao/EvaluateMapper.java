package com.jiuji.oa.oacore.oaorder.dao;

import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.oaorder.bo.EvaluateNonFiveStarDeductBO;
import com.jiuji.oa.oacore.oaorder.bo.EvaluateOrderBO;
import com.jiuji.oa.oacore.oaorder.bo.EvaluateRewardBO;
import com.jiuji.oa.oacore.oaorder.po.Evaluate;
import com.jiuji.oa.oacore.oaorder.po.EvaluateCategoryRelation;
import com.jiuji.oa.oacore.oaorder.po.EvaluateDepart;
import com.jiuji.oa.oacore.oaorder.po.EvaluateTag;
import com.jiuji.oa.oacore.oaorder.req.EvaluateListQuery;
import com.jiuji.oa.oacore.oaorder.res.*;
import com.jiuji.oa.oacore.oaorder.vo.req.EvaluateListExQuery;
import com.jiuji.oa.oacore.oaorder.vo.req.EvaluateRedPackReq;
import com.jiuji.oa.oacore.oaorder.vo.res.EvaluateEndInfo.EvaluateDepartInfo;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-31
 */
@Mapper
public interface EvaluateMapper extends BaseMapper<Evaluate> {
    /**
     * 查询总条数
     *
     * @param type
     * @return
     */
    Integer geteEaluateTotal(@Param("type") Integer type);

    /**
     * 获取评价内容
     *
     * @param startRow
     * @param size
     * @param type
     * @param authId
     * @return
     */
    @DS("office")
//    @Cached(name = "oaorder.EvaluateMapper.getEvaluatePages", expire = 24, timeUnit = TimeUnit.HOURS)
    List<EvaluateInfoRes> getEvaluatePages(@Param("isJiuji") boolean isJiuji,@Param("startRow") Integer startRow, @Param("size") Integer size, @Param("type") Integer type, @Param("authId") Integer authId);

    /**
     * 根据订单id和评价类型查询评价
     *
     * @param subId        订单id
     * @param evaluateType 评价类型
     * @return 评价
     */
    @DS("office")
    Evaluate getEvaluateBySubIdAndType(Integer subId, Integer evaluateType);

    /**
     * 根据订单id和评价类型查询评价
     *
     * @param subId         订单id
     * @param evaluateTypes 评价类型集合
     * @return 评价
     */
    @DS("office")
    Evaluate getEvaluateBySubIdAndTypes(Integer subId, List<Integer> evaluateTypes);

    /**
     * 根据订单id和评价类型查询呼叫服务评价
     *
     * @param subId        订单id
     * @param evaluateType 评价类型
     * @return 呼叫服务评价
     */
    @DS("office")
    Map<String, Object> getCallService(Integer subId, Integer evaluateType);

    /**
     * 根据评价id获取整个订单已评价标签
     *
     * @param evaluateId 评价id
     * @return 整个订单已评价标签
     */
    @DS("office")
    List<TagsInfoBO> getAllTagsInfo(Integer evaluateId);

    /**
     * 根据评价id查询评价问题
     *
     * @param evaluateId 评价id
     * @return 评价问题
     */
    @DS("office")
    List<PjQuestionBO> getPjQuestions(Integer evaluateId);

    /**
     * 根据员工id查询问题用户
     *
     * @param ch999Ids 员工id
     * @return 问题用户
     */
    @DS("office")
    List<Map<String, Integer>> getQuestionUsers(@Param("ch999Ids") List<Integer> ch999Ids,
                                                @Param("officeName") String officeName);

    /**
     * 通过订单id和评价类型获取对应的网站推荐分标签
     *
     * @param subId        订单id
     * @param evaluateType 评价类型
     * @return 推荐分标签
     */
    @DS("office")
    List<PjTagsBO> getRecommendationWebsiteTagsByOrder(Integer subId, Integer evaluateType,
                                                       @Param("officeName") String officeName);


    /**
     * 服务评价统计接口查询(电话号码个数)
     *
     * @param ch999_id
     * @param type_
     * @return
     */
    @Select("select mobileCount from Ch999EvaluateInfo with(nolock) where ch999_id=#{ch999_id} and type_=#{type_} ")
    int getSellProductCount(@Param("id") Integer ch999_id, @Param("type_") int type_);

    /**
     * 员工的印象标签
     *
     * @param userId
     * @return
     */
    @DS("office")
    @Select({"select bb.Name,aa.num from (select top 10 COUNT(1) AS num,TagId FROM dbo.EvaluateTagRecord with(nolock) where RelateCh999Id = #{userId} group by TagId order by count(1) desc) aa join dbo.EvaluateTag bb with(nolock) on aa.TagId=bb.Id"})
    List<Map<String, Integer>> getCh999UserTags(@Param("userId") int userId);


    Integer isExistV(@Param("subId") Integer subId);

    @Select("SELECT 1 FROM dbo.sub WITH(NOLOCK) WHERE sub_id = #{subId} DATEDIFF(MINUTE,tradeDate1,GETDATE()) < 30")
    Integer isExistsV(@Param("subId") Integer subId);

    Integer existsV(@Param("subId") Integer subId);

    /**
     * 表示已过期
     *
     * @param subId
     * @return
     */
    @Select("SELECT 1 FROM dbo.shouhou sh WITH(NOLOCK) WHERE sh.id = #{subId} AND DATEDIFF(day,sh.offtime,GETDATE()) >= 15")
    Integer isExistS(Integer subId);

    /**
     * 内部员工不可评价
     *
     * @param subId
     * @return
     */
    Integer existsS(Integer subId);

    @Select("SELECT 1 FROM dbo.msoft m WITH(NOLOCK) WHERE id = #{subId} AND DATEDIFF(day,m.modidate,GETDATE()) >= 15")
    Integer isExistR(@Param("subId") Integer subId);

    Integer isExistL(@Param("subId") Integer subId);

    Integer isExistsL(@Param("subId") Integer subId);

    Integer existsL(@Param("subId") Integer subId);

    Integer isExistKX(@Param("subId") Integer subId);

    Integer isExistsR(@Param("subId") Integer subId);

    Integer isExistsKX(@Param("subId") Integer subId);

    Integer isExistH(@Param("subId") Integer subId);

    Integer isExistsH(@Param("subId") Integer subId);

    Integer existsH(@Param("subId") Integer subId);

    Integer existsZ1(@Param("subId") Integer userId);

    @DS("office")
    Evaluate getEvaluateData(@Param("subId") Integer subId, @Param("evaluateType") Integer evaluateType);

    @DS("office")
    List<EvaluateTag> getEvaluateTags(@Param("tagIds") List<Integer> tagIds);

    @DS("office")
    Integer getId(@Param("subId") Integer subId, @Param("jobId") Integer jobId);

    @DS("office")
    Integer getID(@Param("subId") Integer subId, @Param("jobId") Integer jobId, @Param("ch999Id") Integer ch999Id, @Param("type") String type);

    @DS("office")
    Integer getSingle(@Param("evaluateId") Integer evaluateId);

    @DS("office")
    Integer getUObj(@Param("evaluateId") Integer evaluateId);

    @DS("office")
    Integer getNObj(@Param("subId") Integer subId);

    @DS("office")
    void updateEvaluate(@Param("process") String process, @Param("evaluateId") Integer evaluateId);

    /**
     * 获取评价分页数据
     * @param req
     * @return
     */
    @DS("oanew")
    List<EvaluateListVO> getEvaluateList(@Param("req") EvaluateListQuery req,
                                         @Param("exQuery")EvaluateListExQuery exQuery,
                                         @Param("officeName") String officeName,
                                         @Param("authorizeId") Integer authorizeId);

    /**
     * 客评得分统计-评价列表页
     */
    @DS("oanew")
    Page<EvaluateListVO> getEvaluateStatisticsList(IPage<EvaluateListVO> page, @Param("evaluateIds") Set<Integer> evaluateIds, @Param("officeName") String officeName);

    @DS("oanew")
    Long getEvaluateListTotal(@Param("req") EvaluateListQuery req,
                              @Param("exQuery")EvaluateListExQuery exQuery,
                              @Param("officeName") String officeName,
                              @Param("authorizeId") Integer authorizeId);

    /**
     * 根据客评id 获取用户评分展示
     * @param evaluateIds
     * @return
     */
    @DS("oanew")
    List<EvaluateJobScoreVO> getEvaluateJobScoreByEvaluateIds(@Param("evaluateIds") List<Integer> evaluateIds,
                                                              @Param("officeName") String officeName);

    /**
     * 根据客评id 获取用户评分展示
     * @param evaluateIdStr
     * @return
     */
    @DS("oanew")
    List<EvaluateJobScoreVO> getEvaluateJobScoreByEvaluateIdStr(@Param("evaluateIdStr") String evaluateIdStr,
                                                              @Param("officeName") String officeName);

    /**
     * 获取用户评分展示
     * @param
     * @return
     */
    List<EvaluateJobScoreVO> getEvaluateJobScoreByEvaluateId(@Param("evaluateId") Integer evaluateId,
                                                             @Param("officeName") String officeName);

    /**
     * 获取用户评分展示
     * @param
     * @return
     */
    List<EvaluateJobScoreVO> getEvaluateJobScoreByScoreIds(@Param("scoreIds") List<Integer> scoreIds,
                                                           @Param("officeName") String officeName);

    /**
     * 获取客评记录标签信息
     * @param evaluateIds
     * @return
     */
    @DS("office")
    List<EvaluateTagRecordNameVO> getEvaluateTargetList(@Param("evaluateIds") List<Integer> evaluateIds);

    /**
     * 获取客评记录标签信息
     * @param evaluateIdStr
     * @return
     */
    @DS("office")
    List<EvaluateTagRecordNameVO> getEvaluateTargetListByEvaluateIdStr(@Param("evaluateIdStr") String evaluateIdStr);

    /**
     * 查询非五星扣分
     * @param evaluateScoreIdStr
     * @return
     */
    @DS("oanew")
    List<EvaluateNonFiveStarDeductBO> getEvaluateNonFiveStarDeductByEvaluateIdStr(@Param("evaluateScoreIdStr") String evaluateScoreIdStr);

    /**
     * 获取客评积分红包数据
     * @param req
     * @return
     */
    @DS("oanew")
    List<EvaluateRedPackVO> getEvaluateRedPackList(@Param("req") EvaluateRedPackReq req,
                                                   @Param("officeName") String officeName);

    /**
     * 根据员工id获取员工名字
     */
    @DS("oanew")
    String getUserInfoById(@Param("userId") Integer userId);

    /**
     * 获取新机订单basketIds
     * @param subId
     * @return
     */
    @DS("oanew")
    List<Integer> getBasketsOfNew(Integer subId);

    List<EvaluateDepartInfo> selectDepartInfo(@Param("id")Long EvaluateId);

    @DS("office")
    List<EvaluateDepart> departInfos(@Param("ids") String ids);

    @DS("office")
    List<EvaluateCategoryRelation> cateInfos(@Param("ids")List ids);

    /**
     * 获取查询售后评价
     *
     * @return 售后评价
     */
    @DS(DataSourceConstants.OFFICE)
    String getShouhouEvaluate();

    /**
     * 查询维修查询统计
     *
     * @return 维修查询统计
     */
    @DS(DataSourceConstants.CH999_OA_NEW)
    Integer getCumulativeService();

    /**
     * 获取打赏积分和红包
     *
     * @param officeName
     * @param evaluateIdStr
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW)
    List<EvaluateRewardBO> getEvaluateReward(@Param("officeName") String officeName,
                                             @Param("evaluateIdStr") String evaluateIdStr);

    /**
     * 获取交易完成时间在24小时内的订单数据
     *
     * @param userId
     * @param beforeTime
     * @param finalTieMoCidList
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW)
    List<EvaluateOrderBO> getOrderInfoWithinHours(@Param("userId") Integer userId,
                                                  @Param("beforeTime") LocalDateTime beforeTime,
                                                  @Param("afterTime") LocalDateTime afterTime,
                                                  @Param("finalTieMoCidList") String finalTieMoCidList);

    /**
     * 根据单号筛选没有评价过的数据 子表
     *
     * @param subIds
     * @return
     */
    @DS(DataSourceConstants.OFFICE)
    List<EvaluateOrderBO> getNotEvaluateOrderInfo(@Param("subIds") List<Integer> subIds);

    /**
     * 根据单号筛选评价过的数据 主表
     *
     * @param subIds
     * @return
     */
    @DS(DataSourceConstants.OFFICE)
    List<EvaluateOrderBO> getEvaluateOrderInfoMain(@Param("subIds") List<Integer> subIds);

    /**
     * 查询会员对应手机号是否和员工表手机号相同 相同不用返回订单信息
     *
     * @param userId 会员id
     * @return
     */
    @DS(DataSourceConstants.CH999_OA_NEW)
    String checkPhoneForCh999User(@Param("userId")Integer userId);

}
