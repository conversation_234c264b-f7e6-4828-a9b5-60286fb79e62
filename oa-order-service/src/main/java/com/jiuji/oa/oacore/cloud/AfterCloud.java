package com.jiuji.oa.oacore.cloud;

import com.jiuji.oa.oacore.cloud.bo.*;
import com.jiuji.oa.oacore.cloud.fallback.AfterCloudFallbackFactory2;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.tc.common.vo.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 售后接口
 *
 * @author: xiexiongkun
 * @date: 2021/10/11
 */
@FeignClient(value = "AFTERSERVICE", path = "afterservice",url = "${afterservice.url:}", fallbackFactory = AfterCloudFallbackFactory2.class)
public interface AfterCloud {

    /**
     * 接件保存
     *
     * @param shouhou   售后实体
     * @param iswaiguan iswaiguan
     * @param isweb     isweb
     * @param doReCb    doReCb
     * @param token
     * @param xtenant
     * @return R
     * @see .net:oa999UI.Controllers.ShouhouController.FormSave
     */
    @ApiOperation(value = "接件保存", httpMethod = "POST")
    @PostMapping("/api/bigpro/shouhou/save")
    R<String> shouhouFormSave(@RequestBody ShouhouReceiveReq shouhou,
                              @RequestParam(required = false, defaultValue = "-1") Integer iswaiguan,
                              @RequestParam(required = false, defaultValue = "0") Integer isweb,
                              @RequestParam(required = false, defaultValue = "0") String doReCb,
                              @RequestHeader(AbstractCurrentRequestComponent.REQUEST_HEADER_TOKEN) String token,
                              @RequestHeader(value = "xtenant") Integer xtenant);

    @ApiOperation(value = "售后维修：批量获取售后日志信息")
    @GetMapping("/api/bigpro/shouhou/listShouhouLogs")
    R<Map<Integer, List<ShouhouLogBo>>> listShouhouLogs(@RequestParam(value = "shouhouId", required = true) List<Integer> shouhouIds,
                                                        @RequestParam(value = "type", required = false) Integer type,
                                                        @RequestHeader(AbstractCurrentRequestComponent.REQUEST_HEADER_TOKEN) String token,
                                                        @RequestHeader(value = "xtenant") Integer xtenant);

    @ApiOperation(value = "售后维修：增加处理日志")
    @PostMapping("/api/bigpro/shouhou/addShouHouLog")
    R<Boolean> addShouhouLog(@RequestBody ShouhouLogReq req,
                             @RequestHeader(AbstractCurrentRequestComponent.REQUEST_HEADER_TOKEN) String token,
                             @RequestHeader(value = "xtenant") Integer xtenant);

    @ApiOperation(value = "获取售后的orderId", httpMethod = "GET")
    @GetMapping("/api/bigpro/shouhou/getShouhouOrderId")
    R<String> getShouhouOrderId(@RequestHeader(AbstractCurrentRequestComponent.REQUEST_HEADER_TOKEN) String token,
                                @RequestHeader(value = "xtenant") Integer xtenant);

    /**
     * description: <添加小件接件进程>
     * translation: <Adding smallpro logs>
     *
     * @param smallproAddLogReq 小件接件进程Req
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 14:44 2019/12/13
     * @see SmallproService#addSmallproLogWithPush(SmallproAddLogReq, String)
     * @since 1.0.0
     **/
    @PostMapping("/api/smallpro/addSmallproLog")
    @ApiOperation(value = "添加小件接件进程", httpMethod = "POST", response = SmallproLogRes.class)
    R<SmallproLogRes> addSmallproLog(@RequestBody SmallproAddLogReq smallproAddLogReq,
                                            @RequestHeader(AbstractCurrentRequestComponent.REQUEST_HEADER_TOKEN) String token,
                                            @RequestHeader(value = "xtenant") Integer xtenant);

    @ApiOperation(value = "判断小件单是否服务商品")
    @GetMapping("/api/apiAfterSalesController/getSmallproIsServiceProduct/v1")
    R<Boolean> getSmallproIsServiceProduct(@RequestParam(value = "smallproId") Integer smallproId);
}
