package com.jiuji.oa.oacore.thirdplatform.doudian.message.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.ch999.common.util.utils.XtenantJudgeUtil;
import com.doudian.open.api.afterSale_Detail.data.AfterSaleDetailData;
import com.doudian.open.api.afterSale_Detail.data.SkuOrderInfosItem;
import com.doudian.open.core.msg.DoudianOpMsgParamRecord;
import com.doudian.open.msg.refund_RefundAgreed.param.RefundRefundAgreedParam;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.common.util.SpringContextUtil;
import com.jiuji.oa.oacore.csharp.recover.CsharpRecoverService;
import com.jiuji.oa.oacore.oaorder.po.Basket;
import com.jiuji.oa.oacore.oaorder.po.RecoverMarketinfo;
import com.jiuji.oa.oacore.oaorder.po.RecoverMarketsubinfo;
import com.jiuji.oa.oacore.oaorder.res.SearchProductInfoVO;
import com.jiuji.oa.oacore.oaorder.service.BasketService;
import com.jiuji.oa.oacore.oaorder.service.ProductinfoService;
import com.jiuji.oa.oacore.oaorder.service.RecoverMarketinfoService;
import com.jiuji.oa.oacore.oaorder.service.SubOtherService;
import com.jiuji.oa.oacore.other.bo.PingzhengBO;
import com.jiuji.oa.oacore.other.bo.PingzhengResultBO;
import com.jiuji.oa.oacore.other.service.VoucherService;
import com.jiuji.oa.oacore.thirdplatform.baozun.enums.DoudianTagEnum;
import com.jiuji.oa.oacore.thirdplatform.common.ThirdPlatformCommonConst;
import com.jiuji.oa.oacore.thirdplatform.common.enums.AppTypeEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.GisgroundEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.LogTypeEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.PlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.DoudianFactory;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.MyAccessToken;
import com.jiuji.oa.oacore.thirdplatform.doudian.message.service.ProductChangeMessageService;
import com.jiuji.oa.oacore.thirdplatform.doudian.message.service.RefundAgreedMessageService;
import com.jiuji.oa.oacore.thirdplatform.doudian.service.DefaultDouDianService;
import com.jiuji.oa.oacore.thirdplatform.oplog.service.MeituanJdWorkLogService;
import com.jiuji.oa.oacore.thirdplatform.order.entity.Order;
import com.jiuji.oa.oacore.thirdplatform.order.entity.OrderItem;
import com.jiuji.oa.oacore.thirdplatform.order.service.OrderItemService;
import com.jiuji.oa.oacore.thirdplatform.order.service.OrderService;
import com.jiuji.oa.oacore.thirdplatform.productconfig.entity.ProductConfig;
import com.jiuji.oa.oacore.thirdplatform.productconfig.mapper.ProductConfigMapper;
import com.jiuji.oa.oacore.thirdplatform.productconfig.service.ProductConfigService;
import com.jiuji.oa.oacore.thirdplatform.store.entity.Store;
import com.jiuji.oa.oacore.thirdplatform.store.service.StoreService;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.TenantService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCodeEnum;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.sms.SmsReceiverClassfyEnum;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 抖音消息处理的主接口
 * <AUTHOR>
 * @since 2022/3/20 20:55
 */
@Service
@Slf4j
public class RefundAgreedMessageServiceImpl extends ParentMessageServiceImpl<DoudianOpMsgParamRecord<RefundRefundAgreedParam>> implements RefundAgreedMessageService {
    private static final String orderError = "抖音退款订单,平台单号：{}";

    @Resource
    private OrderService orderService;
    @Resource
    private TenantService tenantService;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MeituanJdWorkLogService meituanJdWorkLogService;
    @Resource
    private ProductConfigMapper productConfigMapper;
    @Resource
    private ProductConfigService productConfigService;
    @Resource
    private OrderItemService orderItemService;
    @Resource
    private VoucherService voucherService;
    @Resource
    private DefaultDouDianService defaultDouDianService;
    @Resource
    private DoudianFactory doudianFactory;
    @Resource
    @Lazy
    private RefundAgreedMessageService refundAgreedMessageService;
    @Resource
    private AreaInfoClient areaInfoClient;


    /**
     * 创建抖音订单
     * @param paramRecord paramRecord
     * @return
     */
    @Override
    @DS(DataSourceConstants.SMALLPRO_WRITE)
    protected R protectedHandleMessage(DoudianOpMsgParamRecord<RefundRefundAgreedParam> paramRecord) {
        List<Order> orderList = new LinkedList<>();
        AtomicReference<AfterSaleDetailData> afterSaleDetailRef = new AtomicReference<>();
        boolean isNeedNotice = true;
        try {
            R<String> result = refundAgreedMessageService.invokeHandleMessage(paramRecord, orderList, afterSaleDetailRef);
            if(!result.isSuccess() && ResultCodeEnum.PARAM_ERROR.getCode() == result.getCode()){
                //参数错误无需通知
                isNeedNotice = false;
            }
            return result;
        } finally {
            if(isNeedNotice){
                noticeCh999User(paramRecord, orderList, afterSaleDetailRef.get());
            }
        }
    }

    private void noticeCh999User(DoudianOpMsgParamRecord<RefundRefundAgreedParam> paramRecord, List<Order> orderList, AfterSaleDetailData afterSaleDetail) {
        RefundRefundAgreedParam refundAgreedParam = paramRecord.getData();
        Optional<Store> storeOpt = SpringUtil.getBean(StoreService.class).lambdaQuery().eq(Store::getPlatCode, ThirdPlatformCommonConst.THIRD_PLAT_DY)
                .eq(Store::getStoreCode, refundAgreedParam.getStoreId()).list().stream().findFirst();
        Optional<Tenant> tenantOpt = storeOpt
                .map(store -> tenantService.getOneTenantBy(store.getTenantCode(), ThirdPlatformCommonConst.THIRD_PLAT_DY));
        //获取全区推送人
        Set<Integer> ch999Ids = SpringUtil.getBean(ProductChangeMessageService.class)
                .getNoticeCh999Ids(tenantOpt.filter(tenant -> AppTypeEnum.MALL_HOURS.getCode().equals(tenant.getAppType()))
                        .map(tenant -> SmsReceiverClassfyEnum.DOU_YIN_MALL_HOURS_NOTICE)
                        .orElse(SmsReceiverClassfyEnum.DOU_DIAN_PRODUCT_SYSNC));
        Optional<AfterSaleDetailData> afterSaleDetailOpt = Optional.ofNullable(afterSaleDetail);
        OrderService orderService = SpringUtil.getBean(OrderService.class);

        //发送消息通知给门店的管理层和销售
        if(CollUtil.isNotEmpty(orderList)){
            orderList.forEach(order -> {
                String msgSuffix;
                if (XtenantJudgeUtil.isJiujiMore()) {
                    Tenant tenant = tenantOpt.orElseGet(() -> tenantService.getOneTenantBy(order.getTenantCode(), ThirdPlatformCommonConst.THIRD_PLAT_DY));
                    if (Objects.nonNull(tenant) && AppTypeEnum.MALL_HOURS.getCode().equals(tenant.getAppType())) {
                        //是否为部分退
                        boolean isRefundPart = Boolean.TRUE.equals(order.getIsPartRefund());
                        if(isRefundPart){
                            String productNames = afterSaleDetail.getOrderInfo().getSkuOrderInfos()
                                    .stream().map(soi -> StrUtil.format("sku[{}] 退数量({})", soi.getShopSkuCode(), soi.getItemQuantity()))
                                    .collect(Collectors.joining(StringPool.SPACE));
                            msgSuffix = "抖音小时达平台用户已退商品["+ productNames +"]，请及时手动删除相关商品并原路径退款！";
                        }else{
                            msgSuffix = "抖音小时达平台用户已取消订单，请及时追回相关商品！";
                        }
                    } else {
                        msgSuffix = "订单异常，顾客已在抖音平台原路径退款，请及时追回商品处理OA订单，如有疑问咨询小九助手。";
                    }
                } else {
                    msgSuffix = "平台用户已取消订单，请及时追回相关商品！";
                }
                R<Object> noticeR = orderService.toMessageNotification(order, ch999Ids, msgSuffix);
                if(!noticeR.isSuccess()){
                    orderService.sendOrderNotice(order.getOrderId(), 2,StrUtil.format("抖音小时达订单号：{}，{}", order.getOrderId(), msgSuffix), ch999Ids);
                }
            });
        }else{
            //通知所有的相关人员
            String afterSaleId = Convert.toStr(refundAgreedParam.getAftersaleId());
            orderService.sendOrderNotice(afterSaleId, 2,
                    StrUtil.format("抖音售后单号：{} 子订单号: {}平台用户已取消订单，请及时追回相关商品！", afterSaleId, refundAgreedParam.getSId()), ch999Ids);
        }
    }


    @Override
    @DS(DataSourceConstants.SMALLPRO_WRITE)
    public R<String> invokeHandleMessage(DoudianOpMsgParamRecord<RefundRefundAgreedParam> paramRecord, List<Order> outOrderList,
                                         AtomicReference<AfterSaleDetailData> outAfterSaleDetailRef) {
        //获取到抖音推送过来的订单信息
        RefundRefundAgreedParam refundRefundAgreedParam = paramRecord.getData();
        if (ObjectUtil.isNull(refundRefundAgreedParam) || CommonUtil.isNullOrZero(refundRefundAgreedParam.getShopId())){
            meituanJdWorkLogService.saveByLog("抖音退款订单", "门店id不能为空！",
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            return R.error(ResultCodeEnum.PARAM_ERROR.getCode(), "抖音退款数据解析失败！");
        }
        MyAccessToken myAccessToken = doudianFactory.getMyAccessToken(refundRefundAgreedParam.getShopId());
        //维修订单ID获取
        Long afterSaleId =refundRefundAgreedParam.getAftersaleId();
        String orderId = null;
        //根据售后ID获取详细的售后信息
        R<AfterSaleDetailData> afterSaleDetailDataR = defaultDouDianService.afterSaleDetail(afterSaleId, myAccessToken);
        if(!afterSaleDetailDataR.isSuccess()){
            return R.error(afterSaleDetailDataR.getCode(), afterSaleDetailDataR.getUserMsg());
        }
        AfterSaleDetailData afterDetailData = afterSaleDetailDataR.getData();
        outAfterSaleDetailRef.set(afterDetailData);
        orderId = String.valueOf(afterDetailData.getOrderInfo().getShopOrderId());
        if (StrUtil.isBlank(orderId)){
            meituanJdWorkLogService.saveByLog(StrUtil.format(orderError,orderId), "抖音平台订单获取失败！",
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            return R.error("抖音取消订单获取失败！");
        }

        //根据订单获取到当前oa端订单信息
        List<Order> orderList = orderService.getListOrderById(orderId);
        if (CollUtil.isEmpty(orderList)){
            meituanJdWorkLogService.saveByLog(StrUtil.format(orderError,orderId), "本地平台订单获取失败！",
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            return R.error("抖音取消订单, 本地平台订单获取失败！");
        }

        outOrderList.addAll(orderList);
        Order order = orderList.get(0);
        //获取商户信息
        Tenant tenant = tenantService.getOneTenantBy(order.getTenantCode(), ThirdPlatformCommonConst.THIRD_PLAT_DY);
        if (Objects.isNull(tenant) ){
            meituanJdWorkLogService.saveByLog(StrUtil.format(orderError,orderId), "商户信息获取失败！",
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            return R.error("抖音商户信息获取失败！");
        }
        //获取oa订单的总商品数量
        List<Long> subIds = orderList.stream().map(Order::getSubId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(subIds)){
            //oa商品总量
            int basketTotal = SpringUtil.getBean(BasketService.class).lambdaQuery().in(Basket::getSubId, subIds)
                    .and(CommonUtil.isNullOrEq(Basket::getIsdel, 0)).gt(Basket::getPrice, 0)
                    .select(Basket::getBasketCount, Basket::getSubId).list().stream()
                    .filter(Objects::nonNull).mapToInt(Basket::getBasketCount).sum();
            //退款商品总量
            int refundTotal = afterDetailData.getOrderInfo().getSkuOrderInfos().stream().map(SkuOrderInfosItem::getItemQuantity)
                    .filter(Objects::nonNull).mapToInt(Convert::toInt).sum();
            log.warn("oa商品总量: {}, 退款商品总量: {}", basketTotal, refundTotal);
            //是否为部分退
            boolean isRefundPart = refundTotal < basketTotal;
            orderList.forEach(o -> o.setIsPartRefund(isRefundPart));
            if(isRefundPart){
                //增加需客服处理的标记
                subIds.stream().map(Convert::toInt)
                        .forEach(subId -> SpringUtil.getBean(SubOtherService.class).saveOrUpdateSubSpecialFlag(subId, 3, "需客服处理", "系统"));
                return R.error("客户退订部分商品！");
            }
        }


        // 标记取消原因,并更新
        orderService.lambdaUpdate().in(Order::getId, orderList.stream().map(Order::getId).collect(Collectors.toList()))
                .set(Order::getCancelCheck, 1)
                .set(Order::getCancelReason, afterDetailData.getProcessInfo().getAfterSaleInfo().getReason())
                .update();

        //提交订单到OA
        R<String> conf = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST);
        if (null == conf || StringUtils.isBlank(conf.getData())) {
            meituanJdWorkLogService.saveByLog(StrUtil.format(orderError,orderId), "获取inwcf前缀失败！",
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            return R.error("获取inwcf前缀失败！");
        }
        String prefix = conf.getData();
        //良品订单退款
        Optional<Order> orderByLP = Optional.of(orderList.stream().filter(or -> Objects.equals(or.getType(), NumberConstant.ONE)).findFirst()).orElse(null);
        String oldUrl = prefix + ThirdPlatformCommonConst.OA_API_SUBMIT_REFUND;
        if (orderByLP.isPresent() && orderByLP.get().getSubId() != null){
            //构建订单参数，调用oa订单退款接口
            Map<String, Object> map = new HashMap<>(NumberConstant.FIVE);
            map.put("sub_id", orderByLP.get().getSubId());
            map.put("comment", StrUtil.format("抖音订单平台取消订单"));
            map.put("inuser", StrUtil.format("系统"));
            log.warn("调用订单退订接口,链接{},参数{}", oldUrl,map);
            ResponseEntity<R> payResponseEntityR = restTemplate.postForEntity(oldUrl, map, R.class);
            log.warn("调用订单退订接口,返回参数{}", JSON.toJSONString(payResponseEntityR));
            if (Objects.requireNonNull(payResponseEntityR.getBody()).getCode() == 0) {
                //更新订单状态的数据为取消
                updateCancelOtherData(orderByLP.get());
                //退订凭证
                voucherByDouDianRefund(orderByLP.get());
            }else{
                meituanJdWorkLogService.saveByLog(StrUtil.format(orderError,orderId), StrUtil.format("订单退订失败！原因{}",payResponseEntityR.getBody().getUserMsg()),
                        "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            }
        }

        //新品订单退款
        Optional<Order> orderByXP = Optional.of(orderList.stream().filter(or -> Objects.equals(or.getType(), NumberConstant.ZERO)).findFirst()).orElse(null);
        String url = prefix + ThirdPlatformCommonConst.OA_API_SUB_REFUND;
        if (orderByXP.isPresent()){
            //构建订单参数，调用oa订单退款接口
            Map<String, Object> map = new HashMap<>(NumberConstant.FIVE);
            map.put("subId", orderByXP.get().getSubId());
            map.put("comment", StrUtil.format("抖音订单平台取消订单"));
            log.warn("调用订单退订接口,链接{},参数{}", url,map);
            ResponseEntity<R> payResponseEntityR = restTemplate.postForEntity(url, map, R.class);
            log.warn("调用订单退订接口,返回参数{}", JSON.toJSONString(payResponseEntityR));
            if (Objects.requireNonNull(payResponseEntityR.getBody()).getCode() == 0) {
                //更新订单状态的数据为取消
                updateCancelOtherData(orderByXP.get());
                //退订凭证
                voucherByDouDianRefund(orderByXP.get());

            }else{
                meituanJdWorkLogService.saveByLog(StrUtil.format(orderError,orderId), StrUtil.format("订单退订失败！原因{}",payResponseEntityR.getBody().getUserMsg()),
                        "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            }

        }
        return R.success("无更新", orderId);
    }

    /**
     * 抖音凭证做账
     * @param order order
     */
    private void voucherByDouDianRefund(Order order) {
        PingzhengBO pingzhengBO = new PingzhengBO();
        String zhaiyao = "", fzhs = "", jief = "", daif = "", kemu = "";
        //摘要
        String zy = order.getAreaCode() + "店，抖音订单退订，订单号：" + order.getSubId();
        zhaiyao = zy + "|" + zy;
        //科目
        kemu = "1122131|220301";
        //平台承担金额+用户应付金额 = 贷
        double price = order.getPlatMoney() + order.getPayableMoney();
        jief = DecideUtil.iif(BigDecimal.valueOf(price).compareTo(BigDecimal.ZERO) == 0, "0", price) + "|0";
        daif = "0|" + DecideUtil.iif(BigDecimal.valueOf(price).compareTo(BigDecimal.ZERO) == 0, "0", price);
        fzhs = "无|无";
        pingzhengBO.setZhaiyao(zhaiyao).setKemu(kemu).setFzhs(fzhs).setJief(jief).setDaif(daif);
        PingzhengResultBO result = voucherService.addPingZheng(pingzhengBO);
        if (result == null || !result.getFlag()) {
            String msg = "抖音凭证生成失败,订单号:" + order.getSubId();
            if (result != null) {
                msg = msg + ",原因：" + result.getErrorMsg();
            }
            log.error(msg);
        }
    }

    @Override
    public void updateCancelOtherData(Order order) {
        //更新状态 良品更新为退订状态
        List<Integer> mkcIds = Optional.ofNullable(order.getType())
                .filter(type -> Order.OrderTypeEnum.lP_ORDER.getCode().equals(type))
                .map(type -> orderItemService.listOrderItemByMkcId(order.getId()))
                .map(orderItems -> orderItems.stream().map(OrderItem::getMkcId).filter(Objects::nonNull).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        //批量获取配置信息
        List<ProductConfig> productConfigs;
        if (CollUtil.isNotEmpty(mkcIds)) {
            productConfigs = productConfigService.lambdaQuery().in(ProductConfig::getMkcId, mkcIds).list();
        }else{
            productConfigs = Collections.emptyList();
        }
        //开启事务 切换写库
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
            if (CollUtil.isNotEmpty(mkcIds)) {
                List<Integer> lockMkcIds = productConfigs.stream().filter(pc -> Boolean.TRUE.equals(pc.getLibraryLock()))
                        .map(ProductConfig::getMkcId).collect(Collectors.toList());
                if (!lockMkcIds.isEmpty()){
                    //更新 良品自动开启库存锁定、状态修改为【未售出】 商品改为已上架
                    int lockNum = productConfigMapper.lockStockBatch(lockMkcIds, NumberConstant.ONE, null);
                    productConfigMapper.updateSellType(lockMkcIds, NumberConstant.ZERO);
                    if(lockNum >0){
                        productConfigService.updateGisGroundByMkcId(lockMkcIds, GisgroundEnum.NOT_ON_SALE.getCode());
                    }
                }
            }
        }).commit();
        //重新添加商品, 并更新配置的basketId
        //自动添加商品到订单中进行锁定
        autoAddBasketToSub(order.getId(), mkcIds, productConfigs);
    }

    @Override
    public void autoAddBasketToSub(Integer orderId, List<Integer> mkcIds, List<ProductConfig> productConfigs) {
        if (CollUtil.isNotEmpty(mkcIds)) {
            //批量获取良品配置, 进行解锁占用
            Map<Integer, ProductConfig> basketIdIdConfigMap = productConfigs.stream()
                    .filter(pc -> StrUtil.isNotBlank(pc.getRuleCode()) && NumberUtil.isInteger(pc.getRuleCode()))
                    .collect(Collectors.toMap(pc -> Convert.toInt(pc.getRuleCode()), Function.identity(), (v1, v2) -> v1));
            CsharpRecoverService csharpRecoverService = SpringUtil.getBean(CsharpRecoverService.class);
            //批量获取订单的门店信息
            Map<Integer, RecoverMarketinfo> basketIdSubMap = SpringUtil.getBean(RecoverMarketinfoService.class)
                    .listSubByBasketId(basketIdIdConfigMap.keySet());
            Map<RecoverMarketinfo, List<Integer>> subInfoMkcIdsMap = CollUtil.newHashMap(mkcIds.size());
            basketIdSubMap
                    .forEach((basketId, subInfo) -> {
                        ProductConfig productConfig = basketIdIdConfigMap.get(basketId);
                        if(productConfig == null){
                            return;
                        }
                        if(subInfoMkcIdsMap.get(subInfo) == null){
                            subInfoMkcIdsMap.put(subInfo, new LinkedList<>());
                        }
                        subInfoMkcIdsMap.get(subInfo).add(productConfig.getMkcId());
                    });
            //批量获取门店信息
            Map<Integer, AreaInfo> areaInfoMap = CommonUtils.getResultData(areaInfoClient.listAreaInfo(basketIdSubMap.values().stream()
                            .map(RecoverMarketinfo::getAreaid).distinct().collect(Collectors.toList())),
                    errMsg -> {
                        SpringContextUtil.addRequestErrorMsg("门店信息获取异常");
                        throw new CustomizeException("门店信息获取异常");
                    }).stream().collect(Collectors.toMap(AreaInfo::getId, Function.identity(), (v1,v2) -> v1));
            AbstractCurrentRequestComponent currentRequestComponent = SpringUtil.getBean(AbstractCurrentRequestComponent.class);

            subInfoMkcIdsMap
                    .forEach((subInfo, mkcIdList) -> {
                        AreaInfo areaInfo = areaInfoMap.get(subInfo.getAreaid());
                        R addResult = currentRequestComponent.invokeWithUser(areaInfo.getXtenant(),orderService.simulateUser(areaInfo, "抖音"),
                                oaUser -> csharpRecoverService.submitLpSub(subInfo.getSubId(), Convert.toInt(subInfo.getUserid()), mkcIdList));
                        if(!addResult.isSuccess()){
                            orderService.addOrderSubMessage(orderId, "抖音良品退订mkcIds: {} 自动追加到订单[{}]失败, 原因: {}",
                                    mkcIdList, subInfo.getSubId(), addResult.getUserMsg());
                        }
                    });
            //更新对应的basketId信息
            //根据mkcId 查询ppid
            MultipleTransaction.build()
                    .execute(DataSourceConstants.SMALLPRO_WRITE,
                            () -> {
                                List<SearchProductInfoVO> productNamesByMkcId = SpringUtil.getBean(ProductinfoService.class).getProductNamesByMkcId(mkcIds.stream()
                                        .map(Object::toString).collect(Collectors.joining(",")), false);
                                Map<Integer, RecoverMarketsubinfo> outDouYinBasketMap = CollUtil.newHashMap(productNamesByMkcId.size());
                                StringJoiner outErrMsgJoiner = new StringJoiner(jodd.util.StringPool.SLASH);
                                productConfigService.handleDouYinBasket(productNamesByMkcId, outDouYinBasketMap, outErrMsgJoiner);
                                productNamesByMkcId.forEach(pn -> productConfigService.lambdaUpdate().eq(ProductConfig::getMkcId, pn.getId())
                                        .set(ProductConfig::getRuleCode, Optional.ofNullable(pn.getToBasketId()).map(outDouYinBasketMap::get)
                                                .map(RecoverMarketsubinfo::getBasketId).map(Convert::toStr).orElse(null)).update());
                                if(outErrMsgJoiner.length() >0){
                                    orderService.addOrderSubMessage(orderId,outErrMsgJoiner.toString());
                                }
                            }).commit();
        }
    }

    /**
     * 抖音消息种类枚举
     * @return
     */
    @Override
    public DoudianTagEnum acceptTag() {
        return DoudianTagEnum.DOUDIAN_REFUND_AGREED_TAG;
    }

}
