package com.jiuji.oa.oacore.oaorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.res.SubLogRes;
import com.jiuji.oa.oacore.common.config.properties.ImageProperties;
import com.jiuji.oa.oacore.common.config.properties.JiujiSystemProperties;
import com.jiuji.oa.oacore.common.constant.RedisKeyConstant;
import com.jiuji.oa.oacore.common.source.InwcfSource;
import com.jiuji.oa.oacore.common.source.MoaUrlSource;
import com.jiuji.oa.oacore.common.util.EnumUtil;
import com.jiuji.oa.oacore.common.util.HttpClientUtil;
import com.jiuji.oa.oacore.oaorder.dao.OrderDetailMapper;
import com.jiuji.oa.oacore.oaorder.enums.*;
import com.jiuji.oa.oacore.oaorder.req.OrderDetailReq;
import com.jiuji.oa.oacore.oaorder.res.*;
import com.jiuji.oa.oacore.oaorder.service.OrderDetailService;
import com.jiuji.oa.oacore.weborder.enums.OrderType;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.config.DynamicContextHolder;
import com.jiuji.tc.utils.common.CommonUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Auther: qiweiqing
 * @Date: 2020/04/21/16:40
 * @Description:
 */
@Service
public class OrderDetailServiceImpl implements OrderDetailService {

    public static final String subSeparateConfigCache = "subSeparateConfigCache";
    public static final String payFinancialComplete = "payFinancialComplete";
    public static final String ExceptionSubRedisKey = "ExceptionSubRedisKey";
    public static final String areaListDataKey_ = "areaListDataKey_";
    public static final String AllAreaListCacheKey = "AllAreaListCacheKey";
    public static final String All = "AllAreaListCacheKey";
    public static final String HEAD_IMG_DEFAULT = "https://img2.ch999img.com/newstatic/1183,c13e0d2eb61885.jpg";
    public static final String WEBURL = "https://www.9ji.com";
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Resource(name = "redisTemplate4")
    private RedisTemplate<String, Object> redisTemplate4;
    @Autowired
    private OrderDetailMapper orderDetailMapper;
    @Autowired
    private AreaInfoClient areaInfoClient;
    @Autowired
    SubLogsCloud subLogsCloud;
    @Autowired
    InwcfSource inWcfSource;
    @Autowired
    private MoaUrlSource moaUrlSource;
    @Autowired
    private ImageProperties imageProperties;
    @Autowired
    private JiujiSystemProperties jiujiSystemProperties;

    @Override
    public OrderDetailBO getOrderDetail(OrderDetailReq orderDetailReq) {
        Integer recoverSubId = orderDetailReq.getRecoverSubId();
        // 微信红包ppriceid
        List<Integer> redPacketPpriceid = new ArrayList<>();
        redPacketPpriceid.add(42527);
        redPacketPpriceid.add(42665);
        String subId = orderDetailReq.getSubId();
        Long userId = orderDetailReq.getUserId();
        Integer xTenant = orderDetailReq.getXTenant();
        BigDecimal recoverPrice = BigDecimal.ZERO;
        if (null == recoverSubId) {
            recoverSubId = 0;
            orderDetailReq.setRecoverSubId(0);
        }
        if (null == xTenant) {
            xTenant = 0;
            orderDetailReq.setXTenant(0);
        }
        OrderDetailBO info = new OrderDetailBO();
        boolean history = isHistory(Long.parseLong(subId),
                OrderType.Normal.getCode());
        resetDB(history);
        try {
            if (xTenant == 0) {
                // 兼容华为授权店
                xTenant = orderDetailMapper.getXTenantByUserId(subId);
                orderDetailReq.setXTenant(null == xTenant ? 0 : xTenant);
            }
            orderDetailReq.setZiTiId(orderDetailReq.getZiTiId() == null ? "" : orderDetailReq.getZiTiId().trim());
            String ziTiId = orderDetailReq.getZiTiId();
            if (StringUtils.isEmpty(ziTiId)) {
                info.setType("my");
            } else {
                info.setType("ziti");
            }
            List<SubBO> subBOS = orderDetailMapper.getSubMessage(orderDetailReq);
            if (CollectionUtils.isNotEmpty(subBOS)) {
                // 设置订单相关信息
                SubBO subBO = operateSubBO(subId, info, subBOS);
                // 查询订单关联发票 评价
                getFaPiaoPingJia(subId, subBO);
                //配送方式不是到店自取 获取物流信息
                getWuLiuMessage(orderDetailReq, history, subBO);

                // 配送方式是自提点
                if (3 == subBO.getDelivery()) {
                    getZitiDIian(orderDetailReq, info, subBO.getZiTiDianID());
                } else if (1 == subBO.getDelivery()) {
                    // 配送方式为到店自取
                    getToStore(recoverSubId, subId, userId, info, history, subBO);
                }
                // 获取订单相关人
                List<Integer> list = new ArrayList<>();
                list.add(Integer.parseInt(subId));
                List<SubCh999UserBO> subCh999UserBOS = getSubEvaluateUser(list);
                info.setSubCh999UserList(subCh999UserBOS);
                //List<OrderAttachmentsBO> attachmentsBOS = orderDetailMapper.getAttachmentsBySubId(subId);
                info.setAttachments(new ArrayList<>());
                // 操作订单日志
                operateSubLog(subId, info, subBO);

                BigDecimal score = orderDetailMapper.getJiFenBySubId(subId);
                info.getSub().setJiFens(score == null ? BigDecimal.ZERO : score);
                resetDB(history);
                List<BasketSearchBO> dt = orderDetailMapper.getBasketSearch(subId);
                info.setBasket(dt);
                // 操作basket
                operateBasketSearch(redPacketPpriceid, subId, xTenant, info, history, subBO, dt);
            } else {
                resetDB(history);
                List<SubBO> subBOSecond = orderDetailMapper.getSubMessageSecond(orderDetailReq);
                if (CollectionUtils.isNotEmpty(subBOSecond)) {
                    ResultModelBO resultModelBO = new ResultModelBO(0, "订单账户与当前登录账户不匹配，请检查您的账户", null);
                    info.setResult_(resultModelBO);
                } else {
                    ResultModelBO resultModelBO = new ResultModelBO(0, "查无相关记录", null);
                    info.setResult_(resultModelBO);
                }
            }
        } catch (Exception e) {
            ResultModelBO resultModelBO = new ResultModelBO(0, e.getMessage(), null);
            info.setResult_(resultModelBO);
        }
        return info;
    }

    private SubBO operateSubBO(String subId, OrderDetailBO info, List<SubBO> subBOS) {
        SubBO subBO = subBOS.get(0);
        info.setResult_(new ResultModelBO(1, null, null));
        subBO.setPiAoFid("");
        List<SubCh999UserBO> subCh999UserList = new ArrayList<>();
        info.setSubCh999UserList(subCh999UserList);
        info.setSub(subBO);
        subBO.setBeiState(1);
        subBO.setNaHuoFlag(false);
        if (null != subBO.getShopType()) {
            boolean smallShop = subBO.getShopType() > 0;
            subBO.setSmallShop(smallShop);
            if (!smallShop) {
                subBO.setShopType(0);
            }
        }
        subBO.setSubPayName(com.jiuji.tc.utils.enums.EnumUtil.getMessageByCode(SubPayEnum.class, subBO.getSubPay()));
        subBO.setSubCheckName(EnumUtil.getMessageByCode(SubCheckEnum.class, subBO.getSubCheck()));
        subBO.setDeliveryName(EnumUtil.getMessageByCode(SubDeliveryEnum.class, subBO.getDelivery()));
        subBO.setYiFuM(subBO.getYiFuM().add(getWhiteStripPrice(subBO.getSubId())));
        subBO.setLockFlag(subBO.getIsLockNum() == 1);
        subBO.setSubTypeName(EnumUtil.getMessageByCode(SubTypeEnum.class, subBO.getSubType()));
        subBO.setSubType(0);
        subBO.setZiTiFanKuAn(null == subBO.getZiTiFanKuAn() ? BigDecimal.ZERO : subBO.getZiTiFanKuAn());
        subBO.setSaveMoney(null == subBO.getSaveMoney() ? BigDecimal.ZERO : subBO.getSaveMoney());
        subBO.setExceptionFlag(checkException(ExceptionSubRedisKey, subId));
        if ("欠款".equals(subBO.getSubCheckName())) {
            subBO.setSubCheckName("待付款");
        }
        //到店付款 货到付款---》到店支付
        if (1 == subBO.getDelivery() && subBO.getSubPay() == 2) {
            subBO.setSubPayName("到店支付");
        }
        return subBO;
    }

    private void operateBasketSearch(List<Integer> redPacketPpriceid, String subId, Integer xTenant,
                                     OrderDetailBO info, boolean history, SubBO subBO, List<BasketSearchBO> dt) {
        if (CollectionUtils.isNotEmpty(dt)) {
            StringBuilder sb = new StringBuilder();
            HashMap<Integer, String> pjKcState = new HashMap<>();
            List<Integer> basketIds =
                    dt.stream().filter(e -> !e.isIsmMobile()).map(BasketSearchBO::getBasketId).collect(Collectors.toList());
            if (SubCheckEnum.SUB_CHECK_CONFIRMED.getCode() == info.getSub().getSubCheck() && CollectionUtils.isNotEmpty(basketIds)) {
                pjKcState = queryBskPeiJianKcState(basketIds);
            }
            List<Integer> collect2 = dt.stream().filter(t -> Integer.parseInt(t.getType()) == 0 &&
                    t.getGiftId() == 0 &&
                    (Integer.parseInt(t.getSupportService()) & ProSupportServiceEnum.ProSupportService5.getCode())
                            == ProSupportServiceEnum.ProSupportService5.getCode()).map(BasketSearchBO::getBasketId).collect(Collectors.toList());
            List<HalfCountBO> halfBuy = new ArrayList<>();
            if (3 == subBO.getSubCheck() && CollectionUtils.isNotEmpty(collect2)) {
                halfBuy = queryHalfCount(collect2);
            }
            String url = getWebUrlByXtenant(xTenant);
            for (BasketSearchBO basket : dt) {
                basket.setHaoMa("");
                if ("9".equals(basket.getType())) {
                    Integer id = orderDetailMapper.getHaoMaId(basket.getBasketId());
                    basket.setHaoMa(id == null ? "" : id.toString());
                }
                List<SubMkcInfoBO> mkcInfo = new ArrayList<>();
                basket.setMkcInfo(mkcInfo);
                if (basket.isIsmMobile()) {
                    resetDB(history);
                    List<ProductMkcBO> productMkcBOS = orderDetailMapper.getMkcDetail(basket.getBasketId());
                    List<Integer> kcChecks = new ArrayList<>();
                    kcChecks.add(0);
                    kcChecks.add(1);
                    kcChecks.add(2);
                    kcChecks.add(7);
                    if (CollectionUtils.isNotEmpty(productMkcBOS)) {
                        for (ProductMkcBO m : productMkcBOS) {
                            SubMkcInfoBO k = new SubMkcInfoBO();
                            Integer kcCheck = m.getKcCheck();
                            k.setMkcId(m.getId());
                            if (kcChecks.contains(kcCheck)) {
                                k.setText("采购中");
                            } else if (10 == kcCheck) {
                                k.setText("在途");
                                List<MkcAreaBO> mkcArea = orderDetailMapper.getMkcArea(k.getMkcId(),
                                        subBO.getAreaId());
                                if (CollectionUtils.isNotEmpty(mkcArea)) {
                                    MkcAreaBO mkcAreaBO = mkcArea.get(0);
                                    String areaid = mkcAreaBO.getAreaid();
                                    String dtime = mkcAreaBO.getDtime();
                                    String toareaid = mkcAreaBO.getToareaid();
                                    url += "/api/3_0/AreaHandler.ashx?act=PlanTime&from=" + areaid + "&to=" + toareaid + "&timeFrom=" + dtime + "&t=" + System.currentTimeMillis();
                                    String s = HttpClientUtil.get(url);
                                    HashMap hashMap = JSON.parseObject(s, HashMap.class);
                                    Integer code = (Integer)hashMap.get("code");
                                    if (0 == code) {
                                        k.setExpectToAreaTime(hashMap.get("data").toString());
                                    }
                                }
                            } else if (3 == kcCheck) {
                                k.setText("已到店");
                            }
                            mkcInfo.add(k);
                        }
                    }
                } else {
                    String kcOne = pjKcState.get(basket.getBasketId());
                    if (StringUtils.isNotEmpty(kcOne)) {
                        SubMkcInfoBO subMkcInfoBO = new SubMkcInfoBO();
                        subMkcInfoBO.setText(kcOne);
                        mkcInfo.add(subMkcInfoBO);
                    }
                    if (3 == subBO.getSubCheck() && Integer.parseInt(basket.getType()) == 0 &&
                            basket.getGiftId() == 0 &&
                            (Integer.parseInt(basket.getSupportService()) & ProSupportServiceEnum.ProSupportService5.getCode())
                                    == ProSupportServiceEnum.ProSupportService5.getCode()) {
                        HalfCountBO halfCountBO =
                                halfBuy.stream().filter(e -> e.getItem1() == basket.getBasketId()).findFirst().orElse(null);
                        int count = halfCountBO == null ? 0 : halfCountBO.getItem2();
                        count = basket.getBasketCount() - count;
                        basket.setHalfBuy(count > 0);
                        basket.setHalfBuyCount(count > 0 ? count : 0);
                    }
                }
                if (sb.length() == 0) {
                    sb.append(basket.getBasketId());
                } else {
                    sb.append(",").append(basket.getBasketId());
                }
            }

            // 大件串号查询
            List<BasketSearchBO> basketInfo = getBigIMei(subId, info);
            // 套餐
            GetTaoCan(sb, basketInfo);
            //自提点订单  计算自提点返款金额
            getZitiDianAmount(info, subBO, basketInfo);

            //大件备货  小件备货
            getBigSmallBeiHhuo(subId, subBO, basketInfo);

            if (CollectionUtils.isNotEmpty(basketInfo)) {
                if (subBO.getSubCheck() == 1) {
                    List<Integer> baskets =
                            basketInfo.stream().map(BasketSearchBO::getBasketId).collect(Collectors.toList());
                    int count = orderDetailMapper.getCountBasket(baskets);
                    if (count != basketInfo.size()) {
                        subBO.setNaHuoFlag(true);
                    }
                }
                List<BasketSearchBO> collect1 =
                        basketInfo.stream().filter(e -> redPacketPpriceid.contains(e.getPPriceId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect1)) {
                    List<Integer> subIds = orderDetailMapper.getRecoverSubs(subId);
                    if (CollectionUtils.isNotEmpty(subIds)) {
                        BasketSearchBO basketSearchBO = collect1.get(0);
                        basketSearchBO.setProductName("以旧换新补贴红包");
                    }
                }
            }
        } else {
            ResultModelBO resultModelBO = new ResultModelBO(0, "无子表记录", null);
            info.setResult_(resultModelBO);
        }
    }

    private void getBigSmallBeiHhuo(String subId, SubBO subBO, List<BasketSearchBO> basketInfo) {
        List<BasketSearchBO> collect =
                basketInfo.stream().filter(e -> e.isIsmMobile()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            List<Integer> bigBeiHuoBO = orderDetailMapper.getBigBeiHuo(subId);
            if (CollectionUtils.isNotEmpty(bigBeiHuoBO)) {
                subBO.setBeiState(0);
            }
        }
        List<BasketSearchBO> small =
                basketInfo.stream().filter(e -> !e.isIsmMobile()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(small)) {
            List<Integer> smalls = orderDetailMapper.getSmall(subId);
            if (CollectionUtils.isNotEmpty(smalls)) {
                subBO.setBeiState(0);
            }
        }
    }

    private void getZitiDianAmount(OrderDetailBO info, SubBO subBO, List<BasketSearchBO> basketInfo) {
        if ("ziti".equals(info.getType())) {
            BigDecimal ismobilePrice =
                    basketInfo.stream().filter(e -> e.isIsmMobile()).map(e -> new BigDecimal(e.getBasketCount()).multiply(new BigDecimal(60))).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal _ismobilePrice =
                    basketInfo.stream().filter(e -> !e.isIsmMobile()).map(e -> e.getPrice().multiply(new BigDecimal(e.getBasketCount())).multiply(BigDecimal.valueOf(0.1))).reduce(BigDecimal.ZERO, BigDecimal::add);
            _ismobilePrice = _ismobilePrice.setScale(2, BigDecimal.ROUND_HALF_UP);
            if (_ismobilePrice.compareTo(new BigDecimal(100)) > 0) {
                _ismobilePrice = new BigDecimal(100);
            }
            subBO.setZiTiFanKuAn(ismobilePrice.add(_ismobilePrice));
        }
    }

    private void GetTaoCan(StringBuilder sb, List<BasketSearchBO> basketInfo) {
        List<TaocanBO> taocanBOS = orderDetailMapper.getTaocan(CommonUtils.str2List(sb.toString()));
        List<BasketSearchBO> basket = basketInfo;
        if (CollectionUtils.isNotEmpty(taocanBOS)) {
            for (TaocanBO taocanBO : taocanBOS) {
                for (BasketSearchBO basketSearchBO : basket) {
                    if (basketSearchBO.getBasketId() == taocanBO.getBasketId()) {
                        basketSearchBO.setPlanId(taocanBO.getPlanId());
                        basketSearchBO.setMobile(taocanBO.getMobile());
                        basketSearchBO.setPackageType(taocanBO.getPackageType());
                        basketSearchBO.setIsPType(taocanBO.getIsptype());
                        basketSearchBO.setContractPeriod(taocanBO.getContractPeroid());
                    }
                }
            }
        }
    }

    private List<BasketSearchBO> getBigIMei(String subId, OrderDetailBO info) {
        List<BasketImeiBO> imeis = orderDetailMapper.getBasketImei(subId);
        Map<Integer, List<BasketImeiBO>> BasketImeiMap =
                imeis.stream().collect(Collectors.groupingBy(BasketImeiBO::getBasketId));
        List<BasketSearchBO> basketInfo = info.getBasket();
        if (CollectionUtils.isNotEmpty(imeis)) {
            List<BasketSearchBO> baskets = basketInfo;
            for (BasketSearchBO basket : baskets) {
                List<BasketImeiBO> basketImeiBOS = BasketImeiMap.get(basket.getBasketId());
                if (CollectionUtils.isNotEmpty(basketImeiBOS)) {
                    String collect =
                            basketImeiBOS.stream().map(BasketImeiBO::getImei).collect(Collectors.joining(
                                    ","));
                    basket.setIMei(collect);
                }
            }
        }
        return basketInfo;
    }

    private void operateSubLog(String subId, OrderDetailBO info, SubBO subBO) {
        List<SubLogBO> re = new ArrayList<>();
        R<List<SubLogRes>> subLogsListJSON = subLogsCloud.getSubLogsListJSON(Integer.parseInt(subId));
        if (subLogsListJSON != null) {
            List<SubLogRes> data = subLogsListJSON.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                re = data.stream().filter(e -> e.isShowType()).map(e -> {
                    SubLogBO subLogBO = new SubLogBO();
                    BeanUtils.copyProperties(e, subLogBO);
                    return subLogBO;
                }).collect(Collectors.toList());
            }
        }
        if (subBO.getDelivery() != 1 && StringUtils.isNotEmpty(subBO.getWuLiuCompany()) && StringUtils.isNotEmpty(subBO.getWuLiuNo())) {
            String url = inWcfSource.getWuliuLog(subBO.getWuLiuCompany(), subBO.getWuLiuNo());
            String s = HttpClientUtil.get(url);
            JSONObject jsonObject = JSONObject.parseObject(s);
            String data = jsonObject.getString("data");
            ExpressResult expressResult = JSONObject.parseObject(data, ExpressResult.class);
            List<ExpressDetailResultBO> logs = expressResult.getLogs();
            if (null != expressResult && CollectionUtils.isNotEmpty(logs)) {
                List<SubLogBO> objectList = logs.stream().map(e -> {
                    SubLogBO subLogBO = new SubLogBO();
                    subLogBO.setComment(e.getContent());
                    subLogBO.setDTime(e.getTime());
                    subLogBO.setInUser("系统");
                    subLogBO.setShowType(true);
                    subLogBO.setType(9);
                    return subLogBO;
                }).collect(Collectors.toList());
                re.addAll(objectList);
            }
        }
        // todo
        re.stream().filter(e -> StringUtils.isNotEmpty(e.getComment())).forEach(e -> e.setComment(e.getComment().replace("<br>", "")));
        List<SubLogCategoryBO> categoryLogs = new ArrayList<>();
        List<String> addOrderArr = Arrays.asList(new String[]{"添加订单", "订单已经顺利生成了", "订单提交成功", "预计送达时间"});
        List<String> changeOrderArr = Arrays.asList(new String[]{"更改为", "使用优惠码", "追加", "拆单操作", "业务办理", "合并订单"
                , "添加赠品", "预计送达时间", "使用积分支付"});
        List<String> payOrderArr = Arrays.asList(new String[]{"交易", "收到", "支付", "收取"});
        List<String> returnOrderArr = Arrays.asList(new String[]{"退订金", "退款", "秒退"});
        List<String> cancelOrderArr = Arrays.asList(new String[]{"取消订单请求", "客户删单操作", "秒退"});
        List<String> sendOrderArr = Arrays.asList(new String[]{"配送", "快递单打印", "等待出库", "确认收货", "发往", "正在准备发货",
                "已经出库", "已经到达", "发出", "已到达",
                "已从", "快递单号", "物流单号"});
        List<String> completeOrderArr = Arrays.asList(new String[]{"交易完成"});
        List<String> wxRedPackageOrderArr = Arrays.asList(new String[]{"微信红包", "客户订单红包"});
        for (SubLogBO subLogBO : re) {
            SubLogCategoryBO logOne = new SubLogCategoryBO();
            String regStr = "<a.*?>(.*?)</a>";
            String wlRegStr = "wuliutrackShow\\((.*?),(\\d+),(.*?)\\)";
            Pattern regP = Pattern.compile(regStr);
            Pattern wlRegP = Pattern.compile(wlRegStr);
            if (StringUtils.isNotEmpty(subLogBO.getComment())) {
                Matcher wlReg = wlRegP.matcher(subLogBO.getComment());
                if (wlReg.find()) {
                    int count = wlReg.groupCount();
                    String com = wlReg.group(1);
                    String num = wlReg.group(2);
                    String wuid = wlReg.group(3);
                    logOne.setWlId(num);
                }
                subLogBO.setComment(subLogBO.getComment().replaceAll("<a.*?>(.*?)</a>", "$1").replace("查看物流信息", ""));
            }
            logOne.setComment(subLogBO.getComment());
            logOne.setDTime(subLogBO.getDTime());
            logOne.setShowType(subLogBO.isShowType());
            logOne.setInUser(subLogBO.getInUser());
            String logCategoryName = "其他信息";
            String comment = logOne.getComment();
            boolean flag = false;
            if (!flag) {
                for (String s : addOrderArr) {
                    if (comment.contains(s)) {
                        flag = true;
                        logCategoryName = "提交订单";
                    }
                }
            }
            if (!flag) {
                for (String s : changeOrderArr) {
                    if (comment.contains(s)) {
                        flag = true;
                        logCategoryName = "订单变更";
                    }
                }
            }
            if (!flag) {
                for (String s : payOrderArr) {
                    if (comment.contains(s) && comment.contains("元")) {
                        flag = true;
                        logCategoryName = "订单付款";
                    }
                }
            }
            if (!flag) {
                for (String s : returnOrderArr) {
                    if (comment.contains(s)) {
                        flag = true;
                        logCategoryName = "订单退款";
                    }
                }
            }
            if (!flag) {
                for (String s : cancelOrderArr) {
                    if (comment.contains(s)) {
                        flag = true;
                        logCategoryName = "取消订单";
                    }
                }
            }
            if (!flag) {
                for (String s : sendOrderArr) {
                    if (comment.contains(s)) {
                        flag = true;
                        logCategoryName = "商品配送";
                    }
                }
            }
            if (!flag) {
                for (String s : completeOrderArr) {
                    if (comment.contains(s)) {
                        flag = true;
                        logCategoryName = "交易完成";
                    }
                }
            }
            if (!flag) {
                for (String s : wxRedPackageOrderArr) {
                    if (comment.contains(s)) {
                        flag = true;
                        logCategoryName = "微信红包";
                    }
                }
            }
            if (!flag) {
                if (subLogBO.getType() == 9) {
                    flag = true;
                    logCategoryName = "商品配送";
                }
            }
            logOne.setLogCategory(logCategoryName);
            categoryLogs.add(logOne);
        }
        info.setCategoryLogs(categoryLogs);
        if (0 == subBO.getSubCheck() || 1 == subBO.getSubCheck() || 5 == subBO.getSubCheck()) {
            List<SubLogCategoryBO> categoryLogs1 = info.getCategoryLogs();
            if (CollectionUtils.isNotEmpty(categoryLogs1)) {
                List<Integer> collect =
                        categoryLogs1.stream()
                                .filter(e -> StringUtils.isNotEmpty(e.getWlId()))
                                .map(SubLogCategoryBO -> Integer.parseInt(SubLogCategoryBO.getWlId()))
                                .distinct()
                                .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    List<WuLiuBO> wlInfo = orderDetailMapper.getWuLiu(collect);
                    if (CollectionUtils.isNotEmpty(wlInfo)) {
                        for (SubLogCategoryBO subLogCategoryBO : categoryLogs1) {
                            if (StringUtils.isEmpty(subLogCategoryBO.getWlId())) {
                                continue;
                            }
                            WuLiuBO wuLiuBO =
                                    wlInfo.stream().filter(e -> subLogCategoryBO.getWlId().equals(e.getId())).findFirst().orElse(null);
                            if (null != wuLiuBO) {
                                subLogCategoryBO.setWlCompany(wuLiuBO.getCom());
                                subLogCategoryBO.setWlNo(wuLiuBO.getNu());
                            }
                        }
                    }
                }
            }
        }
        List<SubLogBO> collect =
                re.stream().sorted(Comparator.comparing(SubLogBO::getDTime).reversed()).collect(Collectors.toList());
        info.setLogs(collect);
    }


    private void getToStore(Integer recoverSubId, String subId, Long userId, OrderDetailBO info, boolean history,
                            SubBO subBO) {
        BigDecimal recoverPrice;
        AreaInfoBO areaInfoBO = orderDetailMapper.getareaInfoById(subBO.getAreaId());
        info.setAreaInfo(areaInfoBO);
        // 到店时间
        resetDB(history);
        ArrivalTimeBO arrivalTimeBO = orderDetailMapper.getSdate(subId);
        if (null != arrivalTimeBO) {
            Integer type = arrivalTimeBO.getType();
            if (null != type) {
                if (4 == type) {
                    subBO.setShop(true);
                } else {
                    subBO.setShop(false);
                }
                if (3 == type) {
                    subBO.setSDate(arrivalTimeBO.getSDate());
                    subBO.setSTime(arrivalTimeBO.getSTime());
                }
            }
        }
        if (subBO.getSubCheck() == 1) {
            String nahuoId = orderDetailMapper.getNaHuoId(subId);
            if (StringUtils.isNotEmpty(nahuoId)) {
                subBO.setNahuoID(nahuoId);
            }
        }
        try {
            if (recoverSubId == 0) {
                Integer recoverusbObj = orderDetailMapper.getRecoverSubId(subId);
                if (null != recoverusbObj) {
                    recoverSubId = recoverusbObj;
                }
            }
            if (recoverSubId != 0) {
                //如果包含以旧换新，则需要显示
                RecoverResultBO rr = getRecoverDetail(recoverSubId, userId);
                if (rr.getResult().getStats() == 1) {
                    info.setReoverSub(rr);
                    if (rr.getSub().getSubCheck() == 3) {
                        recoverPrice = rr.getSub().getTotalPrice();
                    }
                } else {
                    info.setReoverSub(null);
                }
            }
        } catch (Exception e) {
            info.setReoverSub(null);
        }
    }

    private void getZitiDIian(OrderDetailReq orderDetailReq, OrderDetailBO info, String ziTiDianID) {
        ZiTiDianBO ziTiDianBO = orderDetailMapper.getZiTiDian(ziTiDianID);
        if (null != ziTiDianBO) {
            ZiTiDetailBO ziTiDetailBO = new ZiTiDetailBO();
            ziTiDetailBO.setId(ziTiDianBO.getCityid());
            ziTiDetailBO.setName(ziTiDianBO.getName());
            ziTiDetailBO.setAddress(ziTiDianBO.getAddress());
            ziTiDetailBO.setHours(ziTiDianBO.getHours());
            ziTiDetailBO.setTel1(ziTiDianBO.getTel1());
            ziTiDetailBO.setTel2(ziTiDianBO.getTel2());
            ziTiDetailBO.setZhOuBiAn(ziTiDianBO.getZhoubian());
            ziTiDetailBO.setComment(ziTiDianBO.getComment());
            ziTiDetailBO.setNickContact(ziTiDianBO.getContractName());
            info.setZiTiInfo(ziTiDetailBO);
        }
    }

    private void getWuLiuMessage(OrderDetailReq orderDetailReq, boolean history, SubBO subBO) {
        if (1 != subBO.getDelivery()) {
            resetDB(history);
            List<SubAddressBO> subAddressBOs = orderDetailMapper.getSubAddress(orderDetailReq.getSubId());
            if (CollectionUtils.isNotEmpty(subAddressBOs)) {
                SubAddressBO subAddressBO = subAddressBOs.get(0);
                subBO.setAutoDelTime(subAddressBO.getExpectTime());
                subBO.setSubAdds(subAddressBO.getAddress());
                subBO.setWuLiuCompany(subAddressBO.getWuliucompany());
                subBO.setWuLiuNo(subAddressBO.getWuliuNo());
                String cityid = subAddressBO.getCityid();
                if (StringUtils.isNotEmpty(cityid)) {
                    subBO.setCityId(Integer.parseInt(cityid));
                }
                if (StringUtils.isNotEmpty(cityid) && !"0".equals(cityid)) {
                    CityIDListBO areas = getAreaIDByCityID(subAddressBO.getCityid(), 1);
                    subBO.setCityName(areas.getPname() + "&nbsp;&nbsp;" + areas.getZname() + "&nbsp;&nbsp;" +
                            areas.getDname());
                }
                subBO.setUserDate(subAddressBO.getUserDate());
                subBO.setUserTime(subAddressBO.getUserTime());
                subBO.setIsSpecial(subAddressBO.getIsSpecial());
                Integer paisongState = subAddressBO.getPaisongState();
                LocalDateTime paisongdtime = subAddressBO.getPaisongdtime();
                if (null != paisongState && 4 == paisongState && null != paisongdtime) {
                    subBO.setExpectTime(paisongdtime);
                }
            }
        } else {
            LocalDateTime date = orderDetailMapper.getAutoDeltime(orderDetailReq.getSubId());
            if (null != date && !date.isEqual(LocalDateTime.MIN)) {
                subBO.setAutoDelTime(date);
            }
        }
    }

    private void getFaPiaoPingJia(String subId, SubBO subBO) {
        if (subBO.getSubCheck() == 3) {
            // 交易完成，查询订单关联发票
            TaxPiAoBO taxPiAoBO = orderDetailMapper.getTaxPiAo(subId);
            if (null != taxPiAoBO) {
                subBO.setPiAoFid(taxPiAoBO.getFileId());
                subBO.setPiAoId(taxPiAoBO.getPiAoId());
                subBO.setPiAoHead(taxPiAoBO.getName());
                subBO.setCustomType(taxPiAoBO.getCustomType());
                subBO.setPiAoType(taxPiAoBO.getKind() == 3 ? 1 : 0);
                subBO.setPiaAoStatus(1);
                if (taxPiAoBO.getFlag() >= 3) {
                    subBO.setPiaAoStatus(2);
                }
            }
            //判断是否有评价
            Object id = orderDetailMapper.getHaveComment(subBO.getSubId());
            if (null != id) {
                subBO.setIsPj(1);
            }
        }
    }

    private void resetDB(boolean history) {
        if (history) {
            DynamicContextHolder.setDB("oanewHis");
        } else {
            DynamicContextHolder.setDB("oanew");
        }
    }

    /**
     * 半价复购次数查询
     *
     * @param collect2
     * @return
     */
    private List<HalfCountBO> queryHalfCount(List<Integer> collect2) {
        List<HalfCountBO> halfCountBOS = orderDetailMapper.queryHalfCount(collect2);
        return halfCountBOS;
    }

    private String getWebUrlByXtenant(Integer xTenant) {
        String res = WEBURL;
        try {
            return WEBURL;
           /* List<NegativeStockOutBO> negativeStockOutPpid =
                    orderDetailMapper.getNegativeStockOutPpid(SysConfigEnum.SysConfig3.getCode(), xTenant);
            if (CollectionUtils.isNotEmpty(negativeStockOutPpid)) {
                res = negativeStockOutPpid.get(0).getVALUE();
            }*/
        } catch (Exception e) {
        }
        return res;
    }

    /**
     * 模糊查询 订单小件备货状态
     *
     * @param basketIds
     */
    private HashMap<Integer, String> queryBskPeiJianKcState(List<Integer> basketIds) {
        HashMap<Integer, String> map = new HashMap<>();
        try {
            List<BskPeiJianBO> bskPeiJianBO = orderDetailMapper.getBskPeiJian(basketIds);
            List<Integer> ppIds = ((OrderDetailService)AopContext.currentProxy()).getNegativeStockOutPpid();
            List<Integer> cids = ((OrderDetailService)AopContext.currentProxy()).getNegativeStockOutCId();
            for (BskPeiJianBO b : bskPeiJianBO) {
                if (b.getBeiCount() > b.getBasketCount() || ppIds.contains(b.getPpriceid()) || cids.contains(b.getCid())) {
                    map.put(b.getBasketId(), "已到店");
                } else if ((b.getBeiCount() + b.getDbCount()) >= b.getBasketCount()) {
                    map.put(b.getBasketId(), "在途");
                } else if (b.getCgCount() > 0 || b.getBasketCount() > 0) {
                    map.put(b.getBasketId(), "采购中");
                }
            }
        } catch (Exception e) {

        }
        return map;

    }

    /**
     * 允许负库存出库分类
     *
     * @return
     */
    public List<Integer> getNegativeStockOutCId() {
        try {
            List<NegativeStockOutBO> negativeStockOutBO =
                    orderDetailMapper.getNegativeStockOutPpid(SysConfigEnum.SysConfig2.getCode(), null);
            if (CollectionUtils.isEmpty(negativeStockOutBO)) {
                return new ArrayList<>();
            } else {
                NegativeStockOutBO negativeStockOutBO1 = negativeStockOutBO.get(0);
                List<String> strings = Arrays.asList(negativeStockOutBO1.getVALUE().split(","));
                List<Integer> ppIds = new ArrayList<>();
                for (String string : strings) {
                    ppIds.add(Integer.parseInt(string));
                }
                return ppIds;
            }
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    /**
     * 允许负库存出库ppid
     *
     * @return
     */
    @Override
    public List<Integer> getNegativeStockOutPpid() {
        List<NegativeStockOutBO> negativeStockOutBO =
                orderDetailMapper.getNegativeStockOutPpid(SysConfigEnum.SysConfig1.getCode(), null);
        if (CollectionUtils.isEmpty(negativeStockOutBO)) {
            return new ArrayList<>();
        } else {
            NegativeStockOutBO negativeStockOutBO1 = negativeStockOutBO.get(0);
            List<String> strings = Arrays.asList(negativeStockOutBO1.getVALUE().split(","));
            List<Integer> ppIds = new ArrayList<>();
            for (String string : strings) {
                ppIds.add(Integer.parseInt(string));
            }
            return ppIds;
        }
    }

    private List<SubCh999UserBO> getSubEvaluateUser(List<Integer> subIds) {
        List<SubCh999UserBO> info = new ArrayList<>();
        List<SubCh999UserResultBO> resultBOS = new ArrayList<>();
        List<EvaluateScoreBO> evaluateScoreBO = orderDetailMapper.getEvaluateScore(subIds,
                jiujiSystemProperties.getOfficeName());
        if (CollectionUtils.isNotEmpty(evaluateScoreBO)) {
            List<OrderUserBO> orderUserBOS = ((OrderDetailService)AopContext.currentProxy()).getAllUser();
            evaluateScoreBO.forEach(e -> {
                if (null == e.getSubId()) {
                    e.setSubId(0);
                }
                if (null == e.getSubCheck()) {
                    e.setSubCheck(0);
                }
                e.setRewordFlag(StringUtils.isNotEmpty(e.getUPrices()));
            });
            Map<Integer, List<EvaluateScoreBO>> groups =
                    evaluateScoreBO.stream().collect(Collectors.groupingBy(EvaluateScoreBO::getSubId));
            groups.entrySet().forEach(g -> {
                Integer subId = g.getKey();
                SubCh999UserResultBO one = new SubCh999UserResultBO();
                List<SubCh999UserBO> users = new ArrayList<>();
                one.setUsers(users);
                one.setSubId(subId);
                List<EvaluateScoreBO> value = g.getValue();
                value.forEach(m -> {
                    SubCh999UserBO subCh999UserBO = new SubCh999UserBO();
                    OrderUserBO orderUserBO =
                            orderUserBOS.stream().filter(e -> e.getCh999Id().intValue() == m.getRelateCh999Id().intValue()).findFirst().orElse(null);
                    if (null != orderUserBO) {
                        String areakey = "EvaluateAreaSort_" + m.getArea1id() + "_" + m.getJob();
                        subCh999UserBO.setCh999_id(orderUserBO.getCh999Id());
                        subCh999UserBO.setCh999_name(orderUserBO.getCh999Name());
                        subCh999UserBO.setHeadImg(orderUserBO.getHeadImg());
                        subCh999UserBO.setReword(m.isRewordFlag());
                        subCh999UserBO.setEvaluteID(m.getSubCheck() == 3 ? m.getId() : 0);
                        subCh999UserBO.setJob(com.jiuji.tc.utils.enums.EnumUtil.getMessageByCode(EvaluateJobEnum.class, m.getJob()));
                        Double score = stringRedisTemplate.opsForZSet().score(areakey,
                                orderUserBO.getCh999Id().toString());
                        subCh999UserBO.setAvgPinFen(score == null ? "非数字" : new BigDecimal(score)
                                .setScale(2, RoundingMode.HALF_DOWN).toString());
                        users.add(subCh999UserBO);
                    }
                });
                resultBOS.add(one);
            });
            if (CollectionUtils.isNotEmpty(resultBOS)) {
                List<SubCh999UserBO> users = resultBOS.get(0).getUsers();
                return users;
            }
        }
        return info;
    }

    @Override
    public List<OrderUserBO> getAllUser() {
        List<OrderUserBO> list = orderDetailMapper.getAllOrderUser(jiujiSystemProperties.getOfficeName());
        list.forEach(e -> {
            if (StringUtils.isEmpty(e.getUrl())) {
                e.setHeadImg(HEAD_IMG_DEFAULT);
            } else {
                e.setHeadImg(e.getUrl().replace(moaUrlSource.getBasicUrl() + "/static",
                        imageProperties.getSelectImgUrl() + "/newstatic"));
            }
        });
        return list;
    }

    private RecoverResultBO getRecoverDetail(Integer subId, Long userId) {
        RecoverResultBO info = new RecoverResultBO();
        ReCoverSubBO sub = new ReCoverSubBO();
        List<RecoverBasketBO> basket = new ArrayList<>();
        ResultModelBO result = new ResultModelBO();
        result.setStats(0);
        info.setResult(result);
        boolean isHistory = isHistory(Long.parseLong(subId.toString()), OrderType.Recover.getCode());
        resetDB(isHistory);
        List<ReCoverSubBO> subs = orderDetailMapper.getRecoverDetail(subId, userId);
        if (CollectionUtils.isNotEmpty(subs)) {
            result.setStats(1);
            sub = subs.get(0);
            sub.setSubPayName(com.jiuji.tc.utils.enums.EnumUtil.getMessageByCode(RecoverPayEnum.class,
                    sub.getSubPay()));
            sub.setSubDeliveryName(com.jiuji.tc.utils.enums.EnumUtil.getMessageByCode(RecoverTradeWayEnum.class,
                    sub.getSubDelivery()));
            sub.setSubCheckName(com.jiuji.tc.utils.enums.EnumUtil.getMessageByCode(RecoverStateEnum.class,
                    sub.getSubCheck()));
            if (null != sub.getDTimeTemp()) {
                sub.setDTime(DateTimeFormatter.ofPattern("yyyy/M/d HH:mm:ss").format(sub.getDTimeTemp()));
            }
            if (3 == sub.getSubDelivery()) {
                int cityId = sub.getCityId();
                CityIDListBO ids = getAreaIDByCityID(String.valueOf(cityId), 1);
                sub.setPid(ids.getPid());
                sub.setDid(ids.getDid());
                sub.setZid(ids.getZid());
            }
            if (1 == sub.getSubDelivery()) {
                Integer areaId = sub.getAreaId();
                String address = "";
                if (null != areaId) {
                    address = orderDetailMapper.getCompanyAdress(areaId);
                }
                if (null != areaId && 1 == areaId) {
                    address = address.replace("5楼", "6楼");
                }
                sub.setShopAddress(address);
            }
            resetDB(isHistory);
            basket = orderDetailMapper.getRecoverBaskets(subId);
            BigDecimal totalPrice = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(basket)) {
                for (RecoverBasketBO e : basket) {
                    totalPrice = totalPrice.add(e.getProductPrice().multiply(new BigDecimal(e.getCount())));
                }
            }
            sub.setTotalPrice(totalPrice);
        }
        info.setSub(sub);
        info.setBasket(basket);
        return info;
    }

    /**
     * 根据cityid获取 pid zid did
     *
     * @param cityidStr
     * @param type
     * @return
     */
    @Override
    public CityIDListBO getAreaIDByCityID(String cityidStr, int type) {
        CityIDListBO cityIDListBO = new CityIDListBO();
        cityIDListBO.setPname("");
        cityIDListBO.setDname("");
        cityIDListBO.setZname("");
        cityIDListBO.setStreetName("");
        List<Integer> ids = new ArrayList<>();
        int cityId = Integer.parseInt(cityidStr);
        ids.add(cityId);
        if (cityidStr.length() == 9) {
            ids.add(cityId / (int)1e7);
            ids.add(cityId / (int)1e5);
            ids.add(cityId / (int)1e3);
        } else if (cityidStr.length() == 6) {
            ids.add(cityId / (int)1e4);
            ids.add(cityId / (int)1e2);
        } else if (cityidStr.length() == 4) {
            ids.add(cityId / (int)1e2);
        }
        List<AreaListBO> areaListBOS = getAreaList(type);
        List<AreaListBO> areas = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(areaListBOS)) {
            for (AreaListBO areaListBO : areaListBOS) {
                for (Integer id : ids) {
                    if (areaListBO.getCode() == id.intValue()) {
                        areas.add(areaListBO);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(areas)) {
                for (AreaListBO m : areas) {
                    if (m.getLevel() == 1) {
                        cityIDListBO.setPid(m.getCode());
                        cityIDListBO.setPname(m.getName());
                    }
                    if (m.getLevel() == 2) {
                        cityIDListBO.setZid(m.getCode());
                        cityIDListBO.setZname(m.getName());
                    }
                    if (m.getLevel() == 3) {
                        cityIDListBO.setDid(m.getCode());
                        cityIDListBO.setDname(m.getName());
                    }
                    if (m.getLevel() == 4) {
                        cityIDListBO.setStreetId(m.getCode());
                        cityIDListBO.setStreetName(m.getName());
                    }
                }
            }
        }
        return cityIDListBO;
    }

    private List<AreaListBO> getAreaList(int isreal) {
        List<AreaListBO> list = new ArrayList<>();
        String cachekey = areaListDataKey_ + isreal;
        String productJsonStr = stringRedisTemplate.opsForValue().get(cachekey);
        if (StringUtils.isNotEmpty(productJsonStr)) {
            return JSON.parseArray(productJsonStr, AreaListBO.class);
        }
        list = ((OrderDetailService)AopContext.currentProxy()).getAllAreaList();
        if (1 == isreal) {
            return list.stream().filter(e -> !e.isReal()).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public List<AreaListBO> getAllAreaList() {
        return orderDetailMapper.getAllArea();
        /*String productJsonStr = stringRedisTemplate.opsForValue().get(AllAreaListCacheKey);
        if (StringUtils.isNotEmpty(productJsonStr)) {
            return JSON.parseArray(productJsonStr, AreaListBO.class);
        } else {
            return orderDetailMapper.getAllArea();
        }*/
    }

    @Override
    public Boolean checkException(String key, String subId) {
        return stringRedisTemplate.opsForHash().hasKey(key, subId);
    }

    /**
     * 获取白条支付金额
     *
     * @param subId
     * @return
     */
    @Override
    public BigDecimal getWhiteStripPrice(int subId) {
        BigDecimal price = BigDecimal.ZERO;
        try {
            Object o = null;
            String switchValue = stringRedisTemplate.opsForValue().get(RedisKeyConstant.HASH_REDIS_SWITCH);
            if (StringUtils.isEmpty(switchValue) || "true".equals(switchValue)) {
                o = redisTemplate4.opsForHash().get(payFinancialComplete, String.valueOf(subId));
            } else {
                o = stringRedisTemplate.opsForHash().get(payFinancialComplete, String.valueOf(subId));
            }
            if (null != o) {
                PayFinancialModelBO payFinancialModelBO =
                        (PayFinancialModelBO)o;
                return payFinancialModelBO.getTotalFee();
            }
        } catch (Exception e) {
            price = BigDecimal.ZERO;
        }
        return price;
    }

    @Override
    public boolean isHistory(Long subId, Integer subType) {
        List<SubSeparateConfigBO> subSeparateConfigCache =
                ((OrderDetailService)AopContext.currentProxy()).getSubSeparateConfigCache();
        long subIdSmall = 0;
        if (CollectionUtils.isNotEmpty(subSeparateConfigCache)) {
            SubSeparateConfigBO subSeparateConfigBO =
                    subSeparateConfigCache.stream().filter(e -> e.getSubType() == subType).findFirst().orElse(new SubSeparateConfigBO());
            subIdSmall = subSeparateConfigBO.getSubId();
        }
        boolean isHis = subId.compareTo(subIdSmall) < 0;
        int res = 0;
        if (isHis) {
            switch (subType) {
                case 1:
                    res = orderDetailMapper.getNewSub(subId);
                    break;
                case 2:
                    res = orderDetailMapper.getLiangPingSub(subId);
                    break;
                case 3:
                    res = orderDetailMapper.getshouHouYuYueSub(subId);
                    break;
                case 4:
                    res = orderDetailMapper.getshouHouSub(subId);
                    break;
                case 6:
                    res = orderDetailMapper.getRecoverSub(subId);
                    break;
                case 5:
                    res = orderDetailMapper.getSmallProSub(subId);
                    break;
                default:
                    break;
            }
            if (res > 0) {
                isHis = false;
            }
        }
        return isHis;
    }

    @Override
    public List<SubSeparateConfigBO> getSubSeparateConfigCache() {
        List<SubSeparateConfigBO> list;
        list = orderDetailMapper.getSubSeparateConfig();

        /*String productJsonStr = stringRedisTemplate.opsForValue().get(subSeparateConfigCache);
        if (StringUtils.isNotEmpty(productJsonStr)) {
            list = JSON.parseArray(productJsonStr, SubSeparateConfigBO.class);
        } else {
            list = orderDetailMapper.getSubSeparateConfig();
        }*/
        return list;
    }

}
