package com.jiuji.oa.oacore.weborder.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.res.PageRes;
import com.jiuji.oa.oacore.common.util.SpringContextUtil;
import com.jiuji.oa.oacore.electronicTicket.service.ElectronicTicketService;
import com.jiuji.oa.oacore.electronicTicket.service.ElectronicTicketUpperService;
import com.jiuji.oa.oacore.oaorder.service.OrderNoticeService;
import com.jiuji.oa.oacore.oaorder.vo.req.OrderNoticeReq;
import com.jiuji.oa.oacore.oaorder.vo.req.SubAppReq;
import com.jiuji.oa.oacore.oaorder.vo.res.OrderNoticeRes;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.oacore.web.vo.*;
import com.jiuji.oa.oacore.weborder.enums.OrderAssistantMenuEnum;
import com.jiuji.oa.oacore.weborder.req.*;
import com.jiuji.oa.oacore.weborder.res.*;
import com.jiuji.oa.oacore.weborder.service.ForBargainService;
import com.jiuji.oa.oacore.weborder.service.SubDynamicsService;
import com.jiuji.oa.oacore.weborder.service.WebOrderService;
import com.jiuji.oa.oacore.weborder.vo.*;
import com.jiuji.oa.oacore.weborder.vo.req.*;
import com.jiuji.oa.oacore.weborder.vo.ImOrderObjVo;
import com.jiuji.oa.oacore.weborder.vo.*;
import com.jiuji.oa.oacore.weborder.vo.req.MyClientReq;
import com.jiuji.oa.oacore.weborder.vo.req.WhetherToBuyReq;
import com.jiuji.oa.oacore.weborder.vo.req.*;
import com.jiuji.oa.oacore.weborder.vo.res.CheckStockInfoResVO;
import com.jiuji.oa.oacore.weborder.vo.res.OrderStateRes;
import com.jiuji.oa.oacore.weborder.vo.res.StockInfoResVO;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.xtenant.Namespaces;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 给主站提供的订单查询接口
 * @since 2020/4/9
 */
@RestController
@Api(tags = "订单查询")
@RequestMapping("/InApi/webOrder")
@Slf4j
public class WebOrderController {
    @Resource
    private WebOrderService webOrderService;
    @Autowired
    private OrderNoticeService orderNoticeService;

    @Resource
    private ElectronicTicketUpperService electronicTicketUpperService;
    @Resource
    private ElectronicTicketService electronicTicketService;

    @Resource
    private SubDynamicsService subDynamicsService;
    @Resource
    private ForBargainService forBargainService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;

    private static final String ERROR_MESSAGE = "开始时间不能晚于结束时间";
    private static final String ERROR_MESSAGE_1 = "时间不能为空";

    /**
     * 获取枚举
     * @return
     */
    @GetMapping("/getAssistantMenuEnum/v1")
    R<List<OrderAssistantMenuEnumVO>> getAssistantMenuEnum(){
        List<OrderAssistantMenuEnumVO> list = Arrays.stream(OrderAssistantMenuEnum.values()).map(item -> {
            OrderAssistantMenuEnumVO orderAssistantMenuEnumVO = new OrderAssistantMenuEnumVO();
            BeanUtils.copyProperties(item, orderAssistantMenuEnumVO);
            return orderAssistantMenuEnumVO;
        }).collect(Collectors.toList());
        return R.success(list);
    }
    /**
     * 大小件优品上架逻辑验证
     * @param queryBO
     * @return
     */
    @PostMapping("/queryImeiForBargain/v1")
    public R<BargainVO> queryImeiForBargain (@RequestBody @Valid QueryBO queryBO ){
        return forBargainService.queryImeiForBargain(queryBO);
    }

    @GetMapping("/retreatCount/v1")
    R<Integer> retreatCount(@RequestParam("userId") Integer userId){
        return R.success(webOrderService.queryRetreatCount(ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), userId)));
    }


    /**
     * 查询 购买用户为2021年9月至今在九机购买过iPhone13系列产品且同时购买了care+服务的用户
     * @param userId
     * @return
     */
    @GetMapping("/purchaseCareAndIphone/v1")
    public R<Set<Integer>> purchaseCareAndIphone(@RequestParam("userId") Integer userId){
        return R.success(electronicTicketUpperService.purchaseCareAndIphone(ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), userId)));
    }

    @GetMapping("/purchaseCareAndIphone/v2")
    public R<Set<Integer>> purchaseCareAndIphoneV2(@RequestParam("userId") Integer userId) {
        return R.success(electronicTicketUpperService.purchaseCareAndIphoneV2(ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), userId)));
    }

    /**
     * 为订单添加备注
     * @param updateSubCommentVO
     * @return
     */
    @PostMapping("/updateSubComment/v1")
    public R<String> updateSubComment(@RequestBody UpdateSubCommentVO updateSubCommentVO){
        return electronicTicketUpperService.updateSubComment(updateSubCommentVO);
    }

    /**
     * 电子小票详情查询
     * @param selectElectronicTicketVO
     * @return
     */
    @PostMapping("/getElectronicTicket/v1")
    public R<ElectronicTicketShow> getElectronicTicket(@RequestBody SelectElectronicTicketVO selectElectronicTicketVO){
        selectElectronicTicketVO.setUserId(ObjectUtil.defaultIfNull(Convert.toLong(currentRequestComponent.getWebUserId()),
                selectElectronicTicketVO.getUserId()));
        log.warn("九机电子小票查询传递xtenant，订单号：{}，xtenant：{}",selectElectronicTicketVO.getOrderNo(), XtenantEnum.getXtenant());
        return electronicTicketUpperService.getElectronicTicket(selectElectronicTicketVO);
    }


    /**
     * 订单积分查询
     */
    @GetMapping("/getPointsBySubId/v1")
    public R<Integer> getPointsBySubId(@RequestParam("subType") Integer subType,@RequestParam("subId")Integer subId){
        Integer pointsBySubId = electronicTicketUpperService.getPointsBySubId(subType, subId);
        log.warn("电子小票详情查询返回结果：{}，出入参数：{}",pointsBySubId,subId+"-"+subType);
        return R.success(pointsBySubId);
    }


    /**
     * 电子小票确认
     * @param selectElectronicTicketVO
     * @return
     */
    @PostMapping("/updateTicket/v1")
    public R<String> updateTicket(@RequestBody SelectElectronicTicketVO selectElectronicTicketVO){
        selectElectronicTicketVO.setUserId(ObjectUtil.defaultIfNull(Convert.toLong(currentRequestComponent.getWebUserId()),
                selectElectronicTicketVO.getUserId()));
        return electronicTicketService.updateTicket(selectElectronicTicketVO);
    }


    /**
     * oa查询电子小票状态
     * @param selectElectronicTicketVO
     * @return
     */
    @PostMapping("/selectTicketBySubId/v1")
    public R<TicketBySubIdRes> selectTicketBySubId(@RequestBody @Validated TicketBySubIdReq ticketBySubIdReq){
        TicketBySubIdRes ticketBySubIdRes = electronicTicketService.selectTicketBySubId(ticketBySubIdReq);
        log.warn("oa查询电子小票状态传入参数：{},返回结果：{}",JSONUtil.toJsonStr(ticketBySubIdReq), JSONUtil.toJsonStr(ticketBySubIdRes));
        return R.success(ticketBySubIdRes);
    }

    @PostMapping("list")
    public R<PageRes<XinjiSubVO>> getOrderList(@RequestBody WebOrderQueryReq req) {
        req.setUserId(ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), req.getUserId()));
        return webOrderService.pageNormalV2(req);
    }

    /**
     * 获取个人中心增加待付款提示栏的订单
     * @param req
     * @return
     */
    @PostMapping("/getOrderPendingPayment/v1")
    public R<PendingPaymentVO> getOrderPendingPayment(@RequestBody PendingPaymentReq req) {
        req.setUserId(ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), req.getUserId()));
        log.warn("个人中心增加待付款提示栏传入参数：{}", JSONUtil.toJsonStr(req));
        R<PendingPaymentVO> pendingPaymentVOR = webOrderService.selectOrderPendingPayment(req);
        log.warn("个人中心增加待付款提示栏传入返回结果：{}", JSONUtil.toJsonStr(pendingPaymentVOR));
        return pendingPaymentVOR;
    }


    /**
     * 获取个人中心增加待付款提示栏的订单
     * @param req
     * @return
     */
    @PostMapping("/getOrderPendingPayment/v2")
    public R<List<PendingPaymentVO>> getOrderPendingPaymentV2(@RequestBody PendingPaymentReq req) {
        req.setUserId(ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), req.getUserId()));
        log.warn("新版个人中心增加待付款提示栏传入参数：{}", JSONUtil.toJsonStr(req));
        Integer userId = req.getUserId();
        if(ObjectUtil.isNull(userId)){
            return R.error("userId不能为空");
        }
        Integer xtenant = req.getXtenant();
        if(ObjectUtil.isNull(xtenant)){
            return R.error("xtenant不能为空");
        }
        R<List<PendingPaymentVO>> pendingPaymentVOR = webOrderService.selectOrderPendingPaymentV2(req);
        log.warn("新版个人中心增加待付款提示栏传入返回结果：{}", JSONUtil.toJsonStr(pendingPaymentVOR));
        return pendingPaymentVOR;
    }


    @PostMapping("listLiangpin")
    public R<PageRes<LiangpinSubVO>> getLiangpinList(@RequestBody WebOrderQueryReq req) {
        req.setUserId(ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), req.getUserId()));
        R<PageRes<LiangpinSubVO>> r = R.success(webOrderService.pageLiangpin(req));
        r.put(WebOrderService.QUERY_HISTORY_ORDER, SpringContextUtil.getRequest()
                .map(request -> request.getAttribute(WebOrderService.QUERY_HISTORY_ORDER)).orElse(null));
        return r;
    }

    @PostMapping("listRecover")
    public R<PageRes<RecoverSubVO>> getRecoverList(@RequestBody WebOrderQueryReq req) {
        req.setUserId(ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), req.getUserId()));
        R<PageRes<RecoverSubVO>> r = R.success(webOrderService.pageRecover(req));
        r.put(WebOrderService.QUERY_HISTORY_ORDER, SpringContextUtil.getRequest()
                .map(request -> request.getAttribute(WebOrderService.QUERY_HISTORY_ORDER)).orElse(null));
        return r;
    }

    @PostMapping("listAfterServiceRepair")
    public R<PageRes<AfterServiceSubVO>> getAfterServiceRepairList(@RequestBody WebAfterQueryReq req) {
        req.setUserId(ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), req.getUserId()));
        return webOrderService.pageAfterServiceRepair(req);
    }

    @PostMapping("listAfterAndSmallServiceRepair")
    public R<PageRes<AfterServiceSubVO>> listAfterAndSmallServiceRepair(@RequestBody WebAfterQueryReq req) {
        return webOrderService.pageAfterAndSmallServiceRepair(req);
    }

    @PostMapping("listReservation")
    public R<PageRes<ReservationSubVO>> getReservationList(@RequestBody WebOrderQueryReq req) {
        req.setUserId(ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), req.getUserId()));
        return R.success(webOrderService.pageReservation(req));
    }

    @PostMapping("count")
    public R<OrderCountVO> getOrderCount(@RequestBody WebOrderCountReq req) {
        req.setUserId(ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), req.getUserId()));
        return R.success(webOrderService.getOrderCount(req));
    }

    /**
     * 查询新机订单
     *
     * @return
     */
    @PostMapping("check")
    public R<List<OrderStatusCheckVO>> checkNormal(@RequestBody WebOrderCheckReq req) {
        if (req.getSubIdList() == null) {
            return R.error(ResultCode.PARAM_ERROR, "参数异常，subIdList不能为空");
        }
        if (req.getSubIdList().size() <= 0 || req.getSubIdList().size() > 200) {
            return R.error(ResultCode.PARAM_ERROR, "参数异常，subIdList数组大小范围为：1~200");
        }
        return R.success(webOrderService.checkOrderStatusNormal(req));
    }

    /**
     * 根据用户id查询专属客服
     *
     * @param userId 用户id
     * @return 专属客服
     */
    @GetMapping("/listExclusiveCustomerServiceByUserId")
    public R<List<Ch999UserServiceVO>> listExclusiveCustomerServiceByUserId(@RequestParam("userId") Integer userId,
                                                                            @RequestParam(value = "count", defaultValue = "4") Integer count) {
        List<Ch999UserServiceVO> ch999Users = webOrderService.listExclusiveCustomerServiceByUserId(userId, count, (int) Namespaces.get());
//        return R.success(ch999Users);
        return R.success(null);
    }

    /**
     * 根据用户id查询专属客服
     *
     * @param userId  用户id
     * @param xtenant 租户
     * @return 专属客服
     */
    @GetMapping("/listExclusiveCustomerService")
    public R<List<Ch999UserServiceVO>> listExclusiveCustomerService(@RequestParam("userId") Integer userId,
                                                                    @RequestParam(value = "count", defaultValue = "4") Integer count,
                                                                    @RequestParam(value = "xtenant", defaultValue = "0") Integer xtenant) {
        List<Ch999UserServiceVO> ch999Users = webOrderService.listExclusiveCustomerServiceByUserId(userId, count, xtenant);
        return R.success(ch999Users);
    }

    /**
     * 根据用户id查询未评价订单数量
     *
     * @param userId 用户id
     * @return 未评价订单数量
     */
    @GetMapping("/countNotEvaluatedOrder")
    public R<Integer> countNotEvaluatedOrder(@RequestParam("userId") Integer userId) {
        int count = webOrderService.countNotEvaluatedOrder(userId);
        return R.success(count);
    }

    /**
     * 查询p30订金预定订单
     * C# oa999Services/ajax2.ashx/GetSaleOrders
     *
     * @param productIds 商品id
     * @param pay        订金金额
     */
    @GetMapping("/getSaleOrders")
    public R<List<SaleOrderVO>> getSaleOrders(@RequestParam(value = "productIds", required = false) List<Integer> productIds,
                                              @RequestParam(value = "pay", defaultValue = "0", required = false) Double pay) {
        List<SaleOrderVO> saleOrders = webOrderService.getSaleOrders(productIds, pay);
        return R.success(saleOrders);
    }

    /**
     * 查询p30订金预定订单 2021-3-19需求新添加了一个参数
     * C# oa999Services/ajax2.ashx/GetSaleOrders
     * 查询订单时候加上必须在这个时间点以前（包含这个时间点）下单的订单
     * @param productIds 商品id
     * @param pay        订金金额
     */
    @GetMapping("/getSaleOrders/v2")
    public R<List<SaleOrderVO>> getSaleOrders(@RequestParam(value = "productIds", required = false) List<Integer> productIds,
                                              @RequestParam(value = "pay", defaultValue = "0", required = false) Double pay,
                                              @RequestParam(value = "subDate", required = false) String subDate) {
        try{
            if (StringUtils.isNotEmpty(subDate)){
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime.parse(subDate, formatter);
            }
        } catch (Exception e){
            log.error("调用/orderservice/InApi/webOrder/getSaleOrders/v2异常!",e);
            return R.error("日期格式错误 正确格式yyyy-MM-dd HH:mm:ss");
        }
        List<SaleOrderVO> saleOrders = webOrderService.getSaleOrdersV2(productIds, pay,subDate);
        return R.success(saleOrders);
    }

    /**
     * 获取新机库存预设分组信息
     */
    @PostMapping("/getSaleOrderKcGroupInfo")
    public R<List<SaleOrderKcGroupInfoVO>> getSaleOrderKcGroupInfo(@RequestBody WebOrderSaleGroupReq req) {
        List<SaleOrderKcGroupInfoVO> saleGroupList = webOrderService.getSaleOrderKcGroupInfo(req.getBasketIds(), req.getLevel(), req.getWxBind());
        return R.success(saleGroupList);
    }

    /**
     * 根据订单明细id获取库存预设
     *
     * @param basketId 订单明细id
     * @return 库存预设
     */
    @GetMapping("/getInventoryPreset")
    public R<InventoryPresetVO> getInventoryPreset(@RequestParam("basketId") Integer basketId) {
        InventoryPresetVO inventoryPreset = webOrderService.getInventoryPreset(basketId);
        return R.success(inventoryPreset);
    }

    /**
     * 更换ppriceid
     * C# oa999Services\lib\oaApi.changeSubPpriceid
     *
     * @param userId   客户编号
     * @param basketId 订单明细id
     * @param ppriceid ppriceid
     * @param price    卖价AddressPoolServiceImpl
     */
    @GetMapping("/changeSubPpriceid")
    public R<String> changeSubPpriceid(@RequestParam("userId") Integer userId,
                                       @RequestParam("basketId") Integer basketId,
                                       @RequestParam("ppriceid") Integer ppriceid,
                                       @RequestParam("price") BigDecimal price) {
        return webOrderService.changeSubPpriceid(userId, basketId, ppriceid, price);
    }

    @PostMapping(value = "/getOrderByTypeAndUserId")
    @ApiOperation(value = "通过Type获取不同类别的订单信息")
    public R<ImOrderObjVo> getOrderByTypeAndUserId(@RequestBody @Validated SubAppReq subAppReq) {
        subAppReq.setUserId(ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), subAppReq.getUserId()));
        ImOrderObjVo orderObjVo = new ImOrderObjVo();
        List<Integer> subIds = Arrays.stream(subAppReq.getIds().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        orderObjVo.setList(webOrderService.listOrderProductInfo(subAppReq.getType(), subIds, subAppReq.getUserId(), subAppReq.getKind()));
        return R.success(orderObjVo);
    }


    @PostMapping(value = "/getOrderByTypeAndUserId/v2")
    @ApiOperation(value = "通过Type获取不同类别的订单信息")
    public R<ImOrderObjVo> getOrderByTypeAndUserIdV2(@RequestBody @Validated List<SubAppReq> subAppReqs) {
        if(subAppReqs == null){
            return R.error("参数不能为空");
        }
        ImOrderObjVo orderObjVo = new ImOrderObjVo();
        orderObjVo.setList(new LinkedList<>());
        subAppReqs.stream().map(subAppReq -> {
            subAppReq.setUserId(ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), subAppReq.getUserId()));
            List<Integer> subIds = Arrays.stream(subAppReq.getIds().split(",")).map(Integer::valueOf).collect(Collectors.toList());
           return webOrderService.listOrderProductInfo(subAppReq.getType(), subIds, subAppReq.getUserId(), subAppReq.getKind());
        }).forEach(orderObjVo.getList()::addAll);

        return R.success(orderObjVo);
    }


    /**
     * <AUTHOR>
     * @Date 2021/8/5 14:57
     * @Description 根据传入号码和租户编号，品牌id，时间段，查询当前用户是否存在购买记录
     */
    @PostMapping("/getWhetherToBuy")
    @ApiOperation(value = "根据传入参数，查询当前用户是否存在购买记录")
    public R<List<WhetherToBuyReq.Res>> getWhetherToBuy(@RequestBody @Validated WhetherToBuyReq whetherToBuyReq){
        //判断开始时间或者结束时间为空时，赋予默认值
        if (whetherToBuyReq.getSTime() == null || whetherToBuyReq.getETime() == null){
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime sixDaysBefore = now.minusDays(NumberConstant.SIX).minusHours(now.getHour()).minusMinutes(now.getMinute())
                    .minusSeconds(now.getSecond()).minusNanos(now.getNano());
            //默认查询前5天的数据
            whetherToBuyReq.setSTime(sixDaysBefore).setETime(now);
        }
        if (whetherToBuyReq.getSTime().isAfter(whetherToBuyReq.getETime())) {
            return R.error(ERROR_MESSAGE);
        }
        return R.success(webOrderService.getWhetherToBuy(whetherToBuyReq));
    }

    /*
      我的客户订单查询接口开发提供给会员组
     */

    /**
     * <AUTHOR>
     * @Date 2021/8/18 11:07
     * @Description 筛选近X天未产生过【已完成】状态的订单（30天、90天、半年、1年、2年）
     */
    @PostMapping("/getMyClientNotComplete")
    @ApiOperation(value = "筛选近X天未产生过【已完成】状态的订单（30天、90天、半年、1年、2年）")
    public R<List<Integer>> getMyClientNotComplete(@RequestBody @Validated MyClientReq myClientReq){
        //判断开始时间或者结束时间为空时，返回空数组
        if (myClientReq.getBeginTime() == null || myClientReq.getEndTime() == null){
            return R.error(ERROR_MESSAGE_1);
        }
        if (myClientReq.getBeginTime().isAfter(myClientReq.getEndTime())) {
            return R.error(ERROR_MESSAGE);
        }
        return R.success(webOrderService.getMyClientComplete(myClientReq, false));
    }


    /**
     * <AUTHOR>
     * @Date 2021/8/18 11:07
     * @Description 商品ID 47113，有购买完成的客户（近一年有购买完成该商品的客户）
     */
    @PostMapping("/getMyClientPurchaseComplete")
    @ApiOperation(value = "商品ID 47113，有购买完成的客户（近一年有购买完成该商品的客户）")
    public R<List<Integer>> getMyClientPurchaseComplete(@RequestBody @Validated MyClientReq myClientReq){
        //判断开始时间或者结束时间为空时，返回空数组
        if (myClientReq.getBeginTime() == null || myClientReq.getEndTime() == null){
            return R.error(ERROR_MESSAGE_1);
        }
        if (myClientReq.getBeginTime().isAfter(myClientReq.getEndTime())) {
            return R.error(ERROR_MESSAGE);
        }
        return R.success(webOrderService.getMyClientPurchaseComplete(myClientReq));
    }

    /**
     * <AUTHOR>
     * @Date 2021/8/18 11:07
     * @Description  筛选近X天【已完成】状态的订单
     */
    @PostMapping("/getMyClientIsComplete")
    @ApiOperation(value = "筛选近X天【已完成】状态的订单")
    public R<List<Integer>> getMyClientIsComplete(@RequestBody @Validated MyClientReq myClientReq){
        //判断开始时间或者结束时间为空时，返回空数组
        if (myClientReq.getBeginTime() == null || myClientReq.getEndTime() == null){
            return R.error(ERROR_MESSAGE_1);
        }
        if (myClientReq.getBeginTime().isAfter(myClientReq.getEndTime())) {
            return R.error(ERROR_MESSAGE);
        }
        return R.success(webOrderService.getMyClientComplete(myClientReq, true));
    }

    /**
     * <AUTHOR>
     * @Date 2021/8/18 11:07
     * @Description  筛选近X天【已完成】状态的订单中包含电子烟商品
     */
    @PostMapping("/getMyClientIsCompleteByCigarette")
    @ApiOperation(value = "筛选近X天【已完成】状态的订单中包含电子烟商品")
    public R<List<Integer>> getMyClientIsCompleteByCigarette(@RequestBody @Validated MyClientReq myClientReq){
        //判断开始时间或者结束时间为空时，返回空数组
        if (myClientReq.getBeginTime() == null || myClientReq.getEndTime() == null){
            return R.error(ERROR_MESSAGE_1);
        }
        if (myClientReq.getBeginTime().isAfter(myClientReq.getEndTime())) {
            return R.error(ERROR_MESSAGE);
        }
        return R.success(webOrderService.getMyClientIsCompleteByCigarette(myClientReq));
    }

    /**
     * 获取分页待办事项
     *
     * @param userId 待办事项参数
     * @return 分页待办事项
     */
    @GetMapping("/getOrderNotice")
    @ApiOperation(value = "获取待办事项", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "员工Id", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "条数", paramType = "query"),
            @ApiImplicitParam(name = "current", value = "当前页", paramType = "query")})
    public R<Page<OrderNoticeRes>> getOrderNotice(@RequestParam("userId") Integer userId,
                                                  @RequestParam("xtenant") Integer xtenant,
                                                  @RequestParam(value = "size",required = false) Integer size,
                                                  @RequestParam(value = "current",required = false) Integer current) {
        if (userId == null) {
            return R.error("用户参数不能为空！");
        }
        Page<OrderNoticeRes> result = orderNoticeService.getOrderNotice(userId);
        if (result.getRecords() == null) {
            return R.error("未知错误！");
        }
        return R.success(result);
    }

    /**
     * 获取分页待办事项
     *
     * @param userId 待办事项参数
     * @return 分页待办事项
     */
    @GetMapping("/getOrderNotice/V2")
    @ApiOperation(value = "获取待办事项", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "员工Id", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "条数", paramType = "query"),
            @ApiImplicitParam(name = "current", value = "当前页", paramType = "query")})
    public R<Page<OrderNoticeRes>> getOrderNoticeV2(@RequestParam("userId") Integer userId,
                                                    @RequestParam("xtenant") Integer xtenant,
                                                    @RequestParam(value = "size",required = false) Integer size,
                                                    @RequestParam(value = "current",required = false) Integer current) {
        if (userId == null) {
            return R.error("用户参数不能为空！");
        }
        if (xtenant == null) {
            return R.error("租户id不能为空！");
        }
        Page<OrderNoticeRes> result = orderNoticeService.getOrderNoticeV2(userId,xtenant);
        if (result.getRecords() == null) {
            return R.error("未知错误！");
        }
        return R.success(result);
    }

    /**
     * 获取分页待办事项(v3 按单号单条返回数据)
     *
     * @param userId 待办事项参数
     * @return 分页待办事项
     */
    @GetMapping("/getOrderNotice/V3")
    @ApiOperation(value = "获取待办事项", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "员工Id", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "条数", paramType = "query"),
            @ApiImplicitParam(name = "current", value = "当前页", paramType = "query")})
    public R<Page<OrderNoticeRes>> getOrderNoticeV3(@RequestParam(value = "userId",required = false) Integer userId,
                                                    @RequestParam(value = "areaId",required = false) Integer areaId,
                                                    @RequestParam(value = "orderTypes",required = false,defaultValue = "1,2,3,4,5,6,8,14,15") String orderTypes,
                                                    @RequestParam("xtenant") Integer xtenant,
                                                    @RequestParam(value = "size",required = false,defaultValue = "10") Integer size,
                                                    @RequestParam(value = "current",required = false, defaultValue = "1") Integer current) {
        List<Integer> orderTypeList = StrUtil.splitTrim(orderTypes, StringPool.COMMA)
                .stream().map(Convert::toInt).filter(Objects::nonNull).collect(Collectors.toList());
        return orderNoticeService.getOrderNoticeV3(OrderNoticeReq.builder().userId(userId).areaId(areaId).orderTypes(orderTypeList)
                .xtenant(xtenant).size(size).current(current).build());
    }

    /**
     * 根据mkcIdList查询库存位置
     * @param mkcIdList mkcIdList
     * @return
     */
    @PostMapping("/getGoodProductStockByMkcList")
    @ApiOperation(value = "查询库存位置", httpMethod = "POST")
    public R<List<GoodProductStockVO>> getGoodProductStockByMkcList(@RequestBody List<Integer> mkcIdList) {
        List<GoodProductStockVO> result = webOrderService.getGoodProductStockByMkcList(mkcIdList);
        return R.success(result);
    }


    /**
     * 判断订单是否为良品配件补贴的订单
     * @return
     */
    @PostMapping("/judgeGoodAccessories")
    public R<Boolean> judgeGoodAccessories(@RequestBody JudgeGoodAccessoriesBO judgeGoodAccessoriesBO) {
        Integer subId = judgeGoodAccessoriesBO.getSubId();
        Integer type = judgeGoodAccessoriesBO.getType();
        if(subId==null || type==null){
            return R.success(Boolean.FALSE);
        }
        return webOrderService.judgeGoodAccessories(judgeGoodAccessoriesBO);
    }

    /**
     * 通过会员ID获取退换货次数
     * @param memberId 会员ID
     * @return
     */
    @GetMapping("/getExchangeCount")
    public R<ExchangeCountVO> getExchangeCount(@RequestParam("memberId") Integer memberId) {
        if (memberId == null){
            return R.error("会员号格式不正确");
        }
        return webOrderService.getExchangeCount(ObjectUtil.defaultIfNull(currentRequestComponent.getWebUserId(), memberId));
    }

    /**
     * 通过订单id列表获取订单详情
     * @param orderIdList 订单号列表
     * @return
     */
    @PostMapping("/getOrderStatusByList")
    public R<List<OrderStateRes>> getOrderStatusByList(@RequestBody OrderListReq orderIdList) {
        if (CollUtil.isEmpty(orderIdList.getOrders())){
            return R.error("查询订单数不能为空");
        }
        return webOrderService.getOrderStatusByList(orderIdList.getOrders());
    }

    /**
     * 获取订单动态
     *
     * @param orderId 订单号
     * @param orderType 订单类型
     * @return
     */
    @GetMapping("/orderDynamics/v1")
    public R<List<OrderDynamicsVO>> orderDynamicsV1(@RequestParam("orderId") Integer orderId,
                                                    @RequestParam("orderType") Integer orderType) {
        return subDynamicsService.listOrderDynamicsByIdAndType(orderId, orderType);
    }

    /**
     * 根据basketId查询subId
     *
     * @param basketId 订单商品明细id
     * @return subId，若未查询到订单，返回0
     */
    @GetMapping("/getSubIdByBasketId/v1")
    public R<Integer> getSubIdByBasketIdV1(@RequestParam("basketId") Integer basketId) {
        return webOrderService.getSubIdByBasketIdV1(basketId);
    }

    /**
     * 根据ppid查询库存信息
     *
     * @return
     */
    @PostMapping("/getOaStockTimelyByPpids/v1")
    public R<List<StockInfoResVO>> getOaStockTimelyByPpids(@RequestBody StockInfoReqVO req) {
        return webOrderService.getOaStockTimelyByPpids(req);
    }

    /**
     * 查询区域库存信息
     *
     * @return
     */
    @PostMapping("/getOaStockTimely/v1")
    public R<List<StockInfoResVO>> getOaStockTimely(@RequestBody OaStockInfoReqVO req) {
        return webOrderService.getOaStockTimely(req);
    }

    /**
     * 根据ppid查询库存信息
     *
     * @return
     */
    @PostMapping("/getStockByPpids/v1")
    public R<List<WebStockInfoResVO>> getStockByPpids(@RequestBody WebStockInfoReqVO req) {
        return webOrderService.getStockByPpids(req);
    }

    /**
     * 收货门店和ppi查询现货调拨库存
     *
     * @return
     */
    @PostMapping("/getDiaoboByPpidAndToArea/v1")
    public R<List<DiaoboStockInfoResVO>> getDiaoboByPpidAndToArea(@Validated @RequestBody DiaoboStockInfoReqVO req) {
        return webOrderService.getDiaoboByPpidAndToArea(req);
    }

    /**
     * 收货门店和ppi查询现货调拨库存
     *
     * @return
     */
    @PostMapping("/getInPriceByPpid/v1")
    public R<List<WebStockPriceResVO>> getInPriceByPpid(@Validated @RequestBody WebStockPriceReqVO req) {
        return webOrderService.getInPriceByPpid(req);
    }

    /**
     * 检测商品库存信息
     *
     * @return
     */
    @PostMapping("/checkProductStock/v1")
    public R<List<CheckStockInfoResVO>> checkProductStock(@RequestBody CheckStockInfoReqVO req) {
        return webOrderService.checkProductStock(req);
    }

    /**
     * 查询门店赠品库存信息
     *
     * @return
     */
    @PostMapping("/getGiftStockByAreaIds/v1")
    public R<List<GiftStockResVO>> getGiftStockByAreaIds(@RequestBody GiftStockReqVO req) {
        return webOrderService.getGiftStockByAreaIds(req);
    }

    /**
     * 根据支付单号获取订单信息
     *
     * @param outTradeNo    支付单号
     * @return 订单信息
     */
    @GetMapping("/getOrderIdListForWx/v1")
    public R<OrderInfoRes> getOrderByWxNo(@RequestParam("outTradeNo") String outTradeNo) {
        return webOrderService.getOrderByWxNo(outTradeNo);
    }
}
