package com.jiuji.oa.oacore.cloud.fallback;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @since 2021-10-11
 */
@Component("orderAfterCloudFallbackFactory")
@Slf4j
public class AfterCloudFallbackFactory2 implements FallbackFactory<AfterCloudRollback> {

    private final AfterCloudRollback fallback;

    public AfterCloudFallbackFactory2(AfterCloudRollback fallback) {
        this.fallback = fallback;
    }

    @Override
    public AfterCloudRollback create(Throwable throwable) {
        log.error("cloud 调用失效：", throwable);
        return fallback;
    }
}
