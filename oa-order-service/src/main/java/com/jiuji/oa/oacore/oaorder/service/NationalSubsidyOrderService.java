package com.jiuji.oa.oacore.oaorder.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.oacore.oaorder.res.NationalSubsidyEnumRes;
import com.jiuji.oa.oacore.oaorder.vo.res.TreeNode;
import com.jiuji.oa.oacore.oaorder.vo.req.NationalSubsidyOrderDetailReq;
import com.jiuji.oa.oacore.oaorder.vo.res.EfficiencyRes;
import com.jiuji.oa.oacore.oaorder.vo.res.GbOrderAttachmentDownLoadRes;
import com.jiuji.oa.oacore.oaorder.vo.res.NationalSubsidyOrderDetailRes;
import com.jiuji.oa.oacore.oaorder.vo.res.TreeNode;
import com.jiuji.tc.common.vo.R;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/26 13:58
 * @Description
 */
public interface NationalSubsidyOrderService {

    /**
     * 分页查询国补订单明细
     *
     * @param req 查询条件
     * @return 分页数据
     */
    R<Page<NationalSubsidyOrderDetailRes>> queryNationalSubsidyOrderPage(NationalSubsidyOrderDetailReq req);

    /**
     * 导出国补订单明细
     *
     * @param req      查询条件
     * @param response HTTP响应
     */
    void exportNationalSubsidyOrder(NationalSubsidyOrderDetailReq req, HttpServletResponse response) throws Exception;

    /**
     * 枚举类
     *
     * @return
     */
    R<List<NationalSubsidyEnumRes>> getInternalPurchaseEnum();

    /**
     * 获取统计标签
     */
    R<List<TreeNode>> getTagStatistics();

    /**
     * 判断是否国补
     * @param subId
     * @return
     */
    R<Boolean> isNationalSubsidy(Integer subId);



    /**
     * 国补附件下载
     * @param req
     * @return
     */
    String getDownLoadUrl(NationalSubsidyOrderDetailReq req);

    GbOrderAttachmentDownLoadRes getDownLoadData(String key);

    R<List<EfficiencyRes>> getEfficiency(String value);
}
