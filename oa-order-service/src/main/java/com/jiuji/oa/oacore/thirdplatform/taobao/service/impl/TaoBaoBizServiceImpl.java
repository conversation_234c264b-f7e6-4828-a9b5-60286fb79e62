package com.jiuji.oa.oacore.thirdplatform.taobao.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.ch999.common.util.atlas.AtlasUtil;
import com.ch999.common.util.atlas.CoordinateUtil;
import com.ch999.common.util.utils.XtenantJudgeUtil;
import com.ch999.common.util.vo.atlas.Coordinate;
import com.jiuji.oa.oacore.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.common.util.SpringContextUtil;
import com.jiuji.oa.oacore.csharp.cloud.CsharpInWcfCloud;
import com.jiuji.oa.oacore.csharp.vo.req.AddExceptionSubReq;
import com.jiuji.oa.oacore.goldseed.service.AreaInfoService;
import com.jiuji.oa.oacore.oaorder.enums.SubCheckEnum;
import com.jiuji.oa.oacore.oaorder.po.Sub;
import com.jiuji.oa.oacore.oaorder.service.SubOtherService;
import com.jiuji.oa.oacore.oaorder.service.SubService;
import com.jiuji.oa.oacore.other.bo.PingzhengBO;
import com.jiuji.oa.oacore.other.bo.PingzhengResultBO;
import com.jiuji.oa.oacore.other.service.VoucherService;
import com.jiuji.oa.oacore.sys.service.WXSmsReceiverService;
import com.jiuji.oa.oacore.thirdplatform.common.ThirdPlatformCommonConst;
import com.jiuji.oa.oacore.thirdplatform.common.enums.*;
import com.jiuji.oa.oacore.thirdplatform.common.util.JsonUtils;
import com.jiuji.oa.oacore.thirdplatform.oplog.service.MeituanJdWorkLogService;
import com.jiuji.oa.oacore.thirdplatform.order.bo.*;
import com.jiuji.oa.oacore.thirdplatform.order.entity.Order;
import com.jiuji.oa.oacore.thirdplatform.order.entity.OrderItem;
import com.jiuji.oa.oacore.thirdplatform.order.service.OrderItemService;
import com.jiuji.oa.oacore.thirdplatform.order.service.OrderService;
import com.jiuji.oa.oacore.thirdplatform.order.vo.OrderExtendVO;
import com.jiuji.oa.oacore.thirdplatform.store.entity.Store;
import com.jiuji.oa.oacore.thirdplatform.store.service.StoreService;
import com.jiuji.oa.oacore.thirdplatform.taobao.service.TaoBaoBizService;
import com.jiuji.oa.oacore.thirdplatform.taobao.service.TaoBaoService;
import com.jiuji.oa.oacore.thirdplatform.taobao.vo.*;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.TenantService;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCodeEnum;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.sms.SmsReceiverClassfyEnum;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import com.taobao.api.internal.spi.CheckResult;
import com.taobao.api.request.*;
import com.taobao.api.response.AlibabaAelophyOrderGetResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/16 14:51
 */
@Service
@Slf4j
public class TaoBaoBizServiceImpl implements TaoBaoBizService {
    @Resource
    private TenantService tenantService;
    @Resource
    private StoreService storeService;
    @Resource
    private OrderService orderService;
    @Resource
    private OrderItemService orderItemService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private SmsService smsService;
    @Resource
    private TaoBaoService taobaoService;
    @Resource
    private MeituanJdWorkLogService meituanJdWorkLogService;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private VoucherService voucherService;
    @Resource
    private CsharpInWcfCloud csharpInWcfCloud;
    @Resource
    private AreaInfoService areaInfoService;
    @Resource
    private SubService subService;
    @Resource
    @Lazy
    private TaoBaoBizService taoBaoBizService;

    /**
     * 淘宝订单状态下发
     *
     * @param request
     * @return
     */
    @Override
    public TaobaoResponseVO orderUpdatestatus(HttpServletRequest request) {
        OrderUpdatestatusReq req = null;
        try {
            String appKey = request.getParameter("target_appkey");
            //查询配置信息
            Tenant tenant = tenantService.getTenantByPlatCodeAndAppkey(ThirdPlatformCommonConst.THIRD_PLAT_TB, appKey);
            if(tenant == null){
                return TaobaoResponseVO.failure("没有对应的应用信息");
            }
            //验签
            CheckResult checkResult = taobaoService.checkTaobaoSign(request, tenant.getAppSecret());
            if (checkResult == null || !checkResult.isSuccess()) {
                return TaobaoResponseVO.signCheckFailure();
            }
            String requestBody = checkResult.getRequestBody();
            log.info("淘宝正向订单状态更新接口, req: {}", requestBody);
            req = JsonUtils.fromJson(requestBody, OrderUpdatestatusReq.class);
            //订单支付完成
            if (TaobaoOrderStatusEnum.PAID.getCode().equals(req.getOrderStatus())) {
                taoBaoBizService.handleOrder(req,tenant);
            }
        } catch (Exception e) {
            log.error("处理淘宝订单状态下发异常req={}", req, e);
            RRExceptionHandler.logError("处理淘宝订单状态下发异常", req, e, errorMsg -> {
                SpringUtil.getBean(SmsService.class).sendOaMsgTo9JiMan(errorMsg);
            });
        }
        return TaobaoResponseVO.success();
    }

    /**
     * 淘宝小时达
     * 仓配作业结果回传
     * @param req
     * @return
     */
    @Override
    @Retryable(value = {CustomizeException.class},maxAttempts = 3,backoff = @Backoff(delay = 1000,multiplier = 3))
    public R<String> workCallback(TaoBaoWorkCallbackBO req) {
        try {
            OrderLogisticsBO orderLogistics = orderService.getLogisticsByOrderId(req.getOrderId(), ThirdPlatformCommonConst.THIRD_PLAT_TB);
            if (Objects.isNull(orderLogistics)) {
                log.info("查询订单物流信息为空，订单号:{}", req.getOrderId());
                return R.error("查询订单物流信息为空，订单号:" + req.getOrderId());
            }
            /*if (Objects.equals(DeliveryEnum.DELIVERY_ONE.getCode(), orderLogistics.getDelivery())) {
                log.info("订单配送方式为到店自取，订单号:{}", req.getOrderId());
                return R.error("订单配送方式为到店自取，订单号:" + req.getOrderId());
            }*/
            TaobaoWorkCallbackStatusEnum workCallbackStatusEnum = TaobaoWorkCallbackStatusEnum.getWorkCallbackStatusEnum(req.getWorkCallbackStatus());
            if (workCallbackStatusEnum == null) {
                log.info("回调状态异常，订单号:{}，req：{}", req.getOrderId(), req);
                return R.error("回调状态异常，订单号:" + req.getOrderId());
            }
            AlibabaAelophyOrderWorkCallbackRequest.WorkCallbackRequest param = new AlibabaAelophyOrderWorkCallbackRequest.WorkCallbackRequest();
            Long bizOrderId = Convert.toLong(orderLogistics.getBizOrderId());
            switch (workCallbackStatusEnum) {
                case ACCEPTED:
                case REFUSED:
                    break;
                case PICKED:
                case PACKAGED:
                    List<OrderItem> orderItems = orderItemService.listOrderItemByOutIdV2(orderLogistics.getId(), ThirdPlatformCommonConst.THIRD_PLAT_TB);
                    //按照单号合并，解决淘宝商品数量异常问题
                    Map<String, List<OrderItem>> orderItemMap = orderItems.stream().collect(Collectors.groupingBy(OrderItem::getBizSubOrderId));
                    List<OrderItem> mergedOrderItems = orderItemMap.values().stream()
                            .map(subOrderItems -> {
                                OrderItem item = subOrderItems.get(0);
                                int skuCount = subOrderItems.stream().mapToInt(OrderItem::getSkuCount).sum();
                                item.setSkuCount(skuCount);
                                return item;
                            })
                            .collect(Collectors.toList());

                    List<AlibabaAelophyOrderWorkCallbackRequest.WorkCallbackSubOrderInfo> subOrderInfos = mergedOrderItems.stream().map(v -> {
                        AlibabaAelophyOrderWorkCallbackRequest.WorkCallbackSubOrderInfo subOrderInfo = new AlibabaAelophyOrderWorkCallbackRequest.WorkCallbackSubOrderInfo();
                        subOrderInfo.setBizSubOrderId(Convert.toLong(v.getBizSubOrderId()));
                        subOrderInfo.setSkuCode(v.getSkuId());
                        subOrderInfo.setPickSaleQuantity(Convert.toStr(v.getSkuCount()));
                        subOrderInfo.setPickStockQuantity(Convert.toStr(v.getSkuCount()));
                        return subOrderInfo;
                    }).collect(Collectors.toList());
                    param.setWorkCallbackSubOrderInfoList(subOrderInfos);
                    break;
                case SHIPPING:
                case SIGN:
                    param.setDelivererName(req.getDelivererName());
                    param.setDelivererPhone(req.getDelivererPhone());
                    if (StrUtil.isBlank(req.getDelivererName()) || StrUtil.isBlank(req.getDelivererPhone())) {
                        LogisticsPtInfoBO logisticsPtInfo = orderService.getLogisticsPtInfoByWuliuId(Convert.toInt(orderLogistics.getWuliuId()));
                        if (logisticsPtInfo != null && StrUtil.isNotBlank(logisticsPtInfo.getPtUserName()) && StrUtil.isNotBlank(logisticsPtInfo.getPtUserMobile())) {
                            param.setDelivererName(logisticsPtInfo.getPtUserName());
                            param.setDelivererPhone(logisticsPtInfo.getPtUserMobile());
                        } else {
                            AreaInfo areaInfo = areaInfoService.getAreaInfoById(orderLogistics.getAreaId());
                            if (areaInfo != null) {
                                param.setDelivererName(areaInfo.getAreaName());
                                param.setDelivererPhone(areaInfo.getCompanyTel1());
                            }
                        }
                    }
                    param.setBizOrderId(bizOrderId);
                    param.setStoreId(orderLogistics.getStoreId());
                    break;
                default:
                    return R.error("回调状态异常，订单号:" + req.getOrderId());
            }
            param.setBizOrderId(bizOrderId);
            param.setStoreId(orderLogistics.getStoreId());
            param.setStatus(req.getWorkCallbackStatus());
            Tenant tenant = tenantService.getTenantBy(ThirdPlatformCommonConst.THIRD_PLAT_TB, orderLogistics.getStoreId());
            TaobaoToken taobaoToken = taobaoService.getTaobaoToken(tenant.getAppKey());
            R<String> workCallback = taobaoService.workCallback(param, taobaoToken);
            if (Optional.ofNullable(workCallback).map(R::getUserMsg).orElse("").contains("重试")) {
                throw new CustomizeException(workCallback);
            }
            return workCallback;
        }catch (CustomizeException ce) {
            throw ce;
        } catch (Exception e) {
            RRExceptionHandler.logError("淘宝仓配作业结果回传异常", req, e, errorMsg -> {
                SpringUtil.getBean(SmsService.class).sendOaMsgTo9JiMan(errorMsg);
            });
        }
        return R.error("调用淘宝仓配作业结果回传异常");
    }

    /**
     * 淘宝小时达
     * 仓配作业结果回传
     * @param req
     * @return
     */
    @Override
    public R<String> taobaoWorkCallback(TaoBaoWorkCallbackBO req) {
        try {
            return taoBaoBizService.workCallback(req);
        } catch (Exception e) {
            RRExceptionHandler.logError("调用淘宝仓配作业结果回传异常", req, e, errorMsg -> {
                SpringUtil.getBean(SmsService.class).sendOaMsgTo9JiMan(errorMsg);
            });
        }
        return R.error("调用淘宝仓配作业结果回传异常");
    }

    /**
     * 售后申请
     *
     * @param request
     * @return
     */
    @Override
    @DS(DataSourceConstants.SMALLPRO_WRITE)
    public TaobaoResponseVO refundApply(HttpServletRequest request) {
        OrderRefundApplyReq req = new OrderRefundApplyReq();
        try {
            String appKey = request.getParameter("target_appkey");
            //查询配置信息
            Tenant tenant = tenantService.getTenantByPlatCodeAndAppkey(ThirdPlatformCommonConst.THIRD_PLAT_TB, appKey);
            if(tenant == null){
                return TaobaoResponseVO.failure("没有对应的应用信息");
            }
            //验签
            CheckResult checkResult = taobaoService.checkTaobaoSign(request, tenant.getAppSecret());
            if (checkResult == null || !checkResult.isSuccess()) {
                return TaobaoResponseVO.signCheckFailure();
            }
            String requestBody = checkResult.getRequestBody();
            log.info("淘宝售后，用户逆向申请接口, req: {}", requestBody);
            req = JsonUtils.fromJson(requestBody, OrderRefundApplyReq.class);
            taoBaoBizService.handleRefundApply(req);
        } catch (Exception e) {
            log.error("处理淘宝订单状态下发异常req={}", req, e);
        }
        return TaobaoResponseVO.success();
    }

    @Override
    @RepeatSubmitCheck(expression = "#{classFullName}:#{methodSignName}:#{req.refundId}")
    public void handleRefundApply(OrderRefundApplyReq req) {
        //根据订单获取到当前oa端订单信息
        List<Order> orderList = orderService.getOrderListByOrderIdAndPlatCode(req.getOutOrderId(), ThirdPlatformCommonConst.THIRD_PLAT_TB);
        if (CollUtil.isEmpty(orderList)){
            meituanJdWorkLogService.saveByLog(StrUtil.format("淘宝小时达用户申请退款，平台单号：{}", req.getOutOrderId()), "本地平台订单获取失败！",
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.TB.getCode(), null);
            return ;
        }
        Order order = orderList.stream().findFirst().orElse(null);
        if (order == null) {
            meituanJdWorkLogService.saveByLog(StrUtil.format("淘宝小时达用户申请退款，平台单号：{}", req.getOutOrderId()), "本地平台订单获取失败！",
                    "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.TB.getCode(), null);
            return ;
        }
        //更新三方表退款原因
        String refundReason = order.getCancelReason();
        if (StrUtil.isBlank(refundReason)) {
            refundReason = req.getRefundReason();
        } else {
            refundReason = refundReason + ";" + req.getRefundReason();
        }
        orderService.lambdaUpdate().eq(Order::getId, order.getId())
                .set(Order::getCancelCheck, 1)
                .set(Order::getCancelReason, refundReason)
                .update();
        //已经标记退款申请
        if (Objects.equals(1,order.getCancelCheck())) {
            return;
        }
        Long subId = order.getSubId();
        Sub sub = MultipleTransaction.query(DataSourceConstants.SMALLPRO_WRITE,()-> subService.getSub(Math.toIntExact(subId)));
        if (sub != null && (SubCheckEnum.SUB_CHECK_COMPLETED.getCode().equals(sub.getSubCheck()) || SubCheckEnum.SUB_CHECK_UNSUBSCRIBE.getCode().equals(sub.getSubCheck()))) {
            //推送通知
            orderService.toMessageNotification(order, getNoticeCh999Ids(), "顾客在淘宝平台申请退款，请联系顾客处理售后问题，平台客服24小时内联系门店核实，请注意接听，如有疑问咨询小九助手");
            return ;
        }
        //推送通知
        orderService.toMessageNotification(order, getNoticeCh999Ids(), "订单异常，顾客已在淘宝小时达平台申请退款，请勿配送，平台客服24小时内联系门店核实，请注意接听，如有疑问咨询小九助手");
        //标记特殊订单
        SpringUtil.getBean(SubOtherService.class).saveOrUpdateSubSpecialFlag(Convert.toInt(subId), 3, "平台取消订单，官方介入退款中", "系统");
        //锁单
        AddExceptionSubReq addExceptionSubReq = new AddExceptionSubReq();
        addExceptionSubReq.setSubId(subId);
        addExceptionSubReq.setExceptionLockType(33);
        addExceptionSubReq.setComment("平台取消订单，官方介入退款中");
        R<Boolean> addExceptionSubRes = csharpInWcfCloud.addExceptionSub(addExceptionSubReq);
        if (!CommonUtils.isRequestSuccess(addExceptionSubRes)) {
            RRExceptionHandler.logError("淘宝小时达申请退款锁定订单失败",addExceptionSubReq, new CustomizeException("淘宝小时达申请退款锁定订单失败"), smsService::sendOaMsgTo9JiMan);
        }
    }

    /**
     * 物流轨迹回传
     * @param logisticsSync
     * @param order
     * @param tenant
     * @return
     */
    @Override
    public R<String> logisticsTraceCallback(LogisticsSync logisticsSync, Order order, Tenant tenant) {
        TaobaoToken taobaoToken = taobaoService.getTaobaoToken(tenant.getAppKey());
        AlibabaAelophyOrderLogisticsTraceCallbackRequest.LogisticsTraceCallbackRequest requestParam = new AlibabaAelophyOrderLogisticsTraceCallbackRequest.LogisticsTraceCallbackRequest();
        requestParam.setBizOrderId(Convert.toLong(order.getBizOrderId()));
        requestParam.setStoreId(order.getStoreCode());
        requestParam.setUpdateTime(new Date());
        requestParam.setLongitude(logisticsSync.getLongitude());
        requestParam.setLatitude(logisticsSync.getLatitude());
        R<String> logisticsTraceCallbackR = taobaoService.logisticsTraceCallback(requestParam, taobaoToken);
        //配送完成
        if (logisticsTraceCallbackR.isSuccess()) {
            if (Objects.equals(40, logisticsSync.getLogisticsStatus())) {
                TaoBaoWorkCallbackBO workCallbackReq = new TaoBaoWorkCallbackBO();
                workCallbackReq.setOrderId(order.getOrderId());
                workCallbackReq.setWorkCallbackStatus(TaobaoWorkCallbackStatusEnum.SIGN.getCode());
                workCallbackReq.setDelivererName(logisticsSync.getCourierName());
                workCallbackReq.setDelivererPhone(logisticsSync.getCourierPhone());
                R<String> workCallback = taobaoWorkCallback(workCallbackReq);
                if (!workCallback.isSuccess() && Objects.equals(ResultCodeEnum.RETURN_ERROR.getCode(), workCallback.getCode())) {
                    RRExceptionHandler.logError("调用淘宝小时达仓配作业结果回传接口失败," + workCallback.getUserMsg(), workCallbackReq, null, errorMsg -> {
                        SpringUtil.getBean(SmsService.class).sendOaMsgTo9JiMan(errorMsg);
                    });
                }
                return taobaoWorkCallback(workCallbackReq);
            }
        }
        return logisticsTraceCallbackR;
    }

    /**
     * 逆向订单状态下发接口
     *
     * @param request
     * @return
     */
    @DS(DataSourceConstants.SMALLPRO_WRITE)
    @Override
    public TaobaoResponseVO refundComplete(HttpServletRequest request) {
        OrderRefundCompleteReq req = new OrderRefundCompleteReq();
        try {
            String appKey = request.getParameter("target_appkey");
            //查询配置信息
            Tenant tenant = tenantService.getTenantByPlatCodeAndAppkey(ThirdPlatformCommonConst.THIRD_PLAT_TB, appKey);
            if(tenant == null){
                return TaobaoResponseVO.failure("没有对应的应用信息");
            }
            //验签
            CheckResult checkResult = taobaoService.checkTaobaoSign(request, tenant.getAppSecret());
            if (checkResult == null || !checkResult.isSuccess()) {
                return TaobaoResponseVO.signCheckFailure();
            }
            String requestBody = checkResult.getRequestBody();
            log.info("淘宝售后，逆向订单状态下发接口, req: {}", requestBody);
            req = JsonUtils.fromJson(requestBody, OrderRefundCompleteReq.class);
            taoBaoBizService.handleRefundComplete(req);
        } catch (Exception e) {
            log.error("处理逆向订单状态下发接口异常req={}", req, e);
        }
        return TaobaoResponseVO.success();
    }

    @Override
    @RepeatSubmitCheck(expression = "#{classFullName}:#{methodSignName}:#{req.outSubOrderId}")
    public void handleRefundComplete(OrderRefundCompleteReq req) {
        if (!TaobaoRefundStatusEnum.SUCCESS.getCode().equals(req.getOrderStatus())) {
            log.warn("淘宝小时达推送退款状态，不是退款成功状态，req={}", req);
            return ;
        }

        //查询订单商品列表
        String orderId = req.getOutSubOrderId();
        List<OrderItem> orderItems = orderItemService.listOrderItemByOutSubOrderId(req.getOutSubOrderId());
        if (CollUtil.isNotEmpty(orderItems)) {
            boolean isRefund = orderItems.stream().allMatch(v -> Objects.equals(1, v.getCancelCheck()));
            if (isRefund) {
                log.warn("淘宝小时达推送退款状态，已经退款，req={}", req);
                return ;
            }

            List<Integer> orderItemIdList = orderItems.stream()
                    .map(OrderItem::getId)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(orderItemIdList)) {
                orderItemService.lambdaUpdate().in(OrderItem::getId, orderItemIdList)
                        .set(OrderItem::getCancelCheck, 1)
                        .update();
            }
            OrderItem orderItem = orderItems.get(0);
            orderId = orderItem.getOrderId();
            orderItems = orderItemService.listOrderItemByOutId(orderItem.getOutjdid());
        } else {
            log.warn("没有找到对应的订单商品，淘宝小时达子订单号：{}", orderId);
            return ;
        }

        List<Order> orderList = orderService.getOrderListByOrderIdAndPlatCode(orderId, ThirdPlatformCommonConst.THIRD_PLAT_TB);
        //是否为部分退
        boolean isRefundPart = orderItems.stream().anyMatch(v -> CommonUtil.isNullOrZero(v.getCancelCheck()));
        noticeCh999User(orderList,orderItems,orderId, isRefundPart);
        if (isRefundPart) {
            return ;
        }
        Order order = orderList.stream().findFirst().orElse(new Order());
            Sub sub = MultipleTransaction.query(DataSourceConstants.SMALLPRO_WRITE,()-> subService.getSub(Math.toIntExact(order.getSubId())));
            //订单已经完成或退订状态
            if (sub != null && (SubCheckEnum.SUB_CHECK_COMPLETED.getCode().equals(sub.getSubCheck())
                    || SubCheckEnum.SUB_CHECK_UNSUBSCRIBE.getCode().equals(sub.getSubCheck())
                    || SubCheckEnum.SUB_CHECK_REFUND.getCode().equals(sub.getSubCheck()))) {
                return ;
            }

            //全部商品退款
            //提交订单到OA
            R<String> conf = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST);
            if (null == conf || StringUtils.isBlank(conf.getData())) {
                meituanJdWorkLogService.saveByLog(StrUtil.format("淘宝小时达退款，单号：{}",orderId), "获取inwcf前缀失败！",
                        "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.TB.getCode(), null);
                return ;
        }
        String prefix = conf.getData();
        //新品订单退款
        Optional<Order> orderByXP = Optional.of(orderList.stream().filter(or -> Objects.equals(or.getType(), NumberConstant.ZERO)).findFirst()).orElse(null);
        String url = prefix + ThirdPlatformCommonConst.OA_API_SUB_REFUND;
        if (orderByXP.isPresent()){
            //构建订单参数，调用oa订单退款接口
            Map<String, Object> map = new HashMap<>(NumberConstant.FIVE);
            map.put("subId", orderByXP.get().getSubId());
            map.put("comment", StrUtil.format("淘宝小时达订单平台取消订单"));
            log.warn("调用订单退订接口,链接{},参数{}", url,map);
            ResponseEntity<R> payResponseEntityR = restTemplate.postForEntity(url, map, R.class);
            log.warn("调用订单退订接口,返回参数{}", JSON.toJSONString(payResponseEntityR));
            if (Objects.requireNonNull(payResponseEntityR.getBody()).getCode() == 0) {
                //退订凭证
                voucherByRefund(orderByXP.get());
            }else{
                meituanJdWorkLogService.saveByLog(StrUtil.format("淘宝小时达退款，单号：{}",orderId), StrUtil.format("订单退订失败！原因{}",payResponseEntityR.getBody().getUserMsg()),
                        "系统", LogTypeEnum.ORDER.getCode(), PlatfromEnum.DY.getCode(), null);
            }
        }
    }

    /**
     * 处理订单
     * @param req
     */
    @Override
    @RepeatSubmitCheck(expression = "#{classFullName}:#{methodSignName}:#{req.bizOrderId}")
    public void handleOrder(OrderUpdatestatusReq req, Tenant tenant) {
        //查询订单详情
        AlibabaAelophyOrderGetRequest.OrderGetRequest requestParam = new AlibabaAelophyOrderGetRequest.OrderGetRequest();
        requestParam.setStoreId(req.getStoreId());
        requestParam.setBizOrderId(req.getBizOrderId());
        //获取token
        TaobaoToken taobaoToken = taobaoService.getTaobaoToken(tenant.getAppKey());
        R<AlibabaAelophyOrderGetResponse.OrderResponse> orderDetailRes = taobaoService.getOrderDetail(requestParam, taobaoToken);
        if (orderDetailRes.isSuccess()) {
            AlibabaAelophyOrderGetResponse.OrderResponse orderResponse = orderDetailRes.getData();
            if (Objects.isNull(orderResponse)) {
                log.warn("查询淘宝订单信息为空req={}", req);
                return;
            }
            //构建三方订单
            Order order = buildOrderParam(orderResponse);
            if (Objects.isNull(order)) {
                return;
            }
            Long orderId = Convert.toLong(order.getOrderId());
            Integer orderAutoId = orderService.getOrderAutoId(order.getPlatCode(), orderId, null);
            if (orderAutoId == null) {
                OrderReq orderReq = new OrderReq();
                orderReq.setTenant(tenant);
                //三方订单商品
                List<OrderProduct> orderProductList = new ArrayList<>();
                List<SkuBenefitDetailBO> skuBenefitDetailList = new ArrayList<>();
                for(AlibabaAelophyOrderGetResponse.SubOrderResponse subOrder : orderResponse.getSubOrderResponseList()) {
                    OrderProduct orderProduct = new OrderProduct();
                    orderProduct.setBizSubOrderId(Convert.toStr(subOrder.getBizSubOrderId()));
                    orderProduct.setOutSubOrderId(subOrder.getOutSubOrderId());
                    orderProduct.setAppFoodCode(subOrder.getSkuCode());
                    orderProduct.setFoodName(subOrder.getSkuName());
                    orderProduct.setSkuId(subOrder.getSkuCode());
                    orderProduct.setUpc(subOrder.getBarcode());
                    orderProduct.setQuantity(Convert.toInt(subOrder.getBuySaleQuantity()));
                    orderProduct.setPrice(NumberUtil.div(Convert.toBigDecimal(subOrder.getPrice()), NumberConstant.ONE_HUNDRED, 2).doubleValue());
                    orderProduct.setUnit(subOrder.getSaleUnit());
                    //新机
                    orderProduct.setDouDianType(NumberConstant.ZERO);
                    //orderProduct.setBoxNum(Double.valueOf(NumberConstant.ZERO));
                    //orderProduct.setBoxPrice(Double.valueOf(NumberConstant.ZERO));
                    orderProductList.add(orderProduct);

                    SkuBenefitDetailBO skuBenefitDetail = new SkuBenefitDetailBO();
                    skuBenefitDetail.setSkuId(subOrder.getSkuCode());
                    skuBenefitDetail.setTotalMtCharge(NumberUtil.div(Convert.toBigDecimal(subOrder.getDiscountPlatformFee()), NumberConstant.ONE_HUNDRED, 2).doubleValue());
                    skuBenefitDetail.setTotalActivityPrice(NumberUtil.div(Convert.toBigDecimal(subOrder.getOriginalFee()-subOrder.getDiscountMerchantFee()), NumberConstant.ONE_HUNDRED, 2).doubleValue());
                    skuBenefitDetail.setCount(Convert.toInt(subOrder.getBuySaleQuantity()));
                    skuBenefitDetailList.add(skuBenefitDetail);
                }
                orderReq.setSkuBenefitDetail(skuBenefitDetailList);
                saveOrderInfo(order, orderProductList, orderReq);
            } else if (orderService.getOrderAutoId(order.getPlatCode(), orderId, qw -> qw.isNotNull(Order::getSubId)) != null) {
                log.warn("{}创建订单校验：订单已存在，不能重复创建【{}】", order.getPlatCode(), orderId);
                return;
            } else {
                order.setId(orderAutoId);
            }

            //提交oa订单走单独的事务,避免重复创建订单
            OrderExtendVO orderExtendVO = new OrderExtendVO();
            boolean submitResult = false;
            try {
                submitResult = orderService.submitOaOrder(order.getId(), order.getEstimateArrivalTime(), orderExtendVO);
                if(submitResult) {
                    //淘宝接单
                    TaoBaoWorkCallbackBO tbBaoWorkCallback = new TaoBaoWorkCallbackBO();
                    tbBaoWorkCallback.setOrderId(order.getOrderId());
                    tbBaoWorkCallback.setWorkCallbackStatus(TaobaoWorkCallbackStatusEnum.ACCEPTED.getCode());
                    R<String> workCallback = taobaoWorkCallback(tbBaoWorkCallback);
                    if (workCallback.isSuccess()) {
                        //拣货完成
                        tbBaoWorkCallback.setWorkCallbackStatus(TaobaoWorkCallbackStatusEnum.PICKED.getCode());
                        taobaoWorkCallback(tbBaoWorkCallback);
                    } else {
                        RRExceptionHandler.logError("调用淘宝小时达接单回传失败," + workCallback.getUserMsg(), tbBaoWorkCallback, null, errorMsg -> {
                            SpringUtil.getBean(SmsService.class).sendOaMsgTo9JiMan(errorMsg);
                        });
                    }
                }
            } finally {
                if (!submitResult){
                    orderService.addOrderSubMessage(order.getId(), String.join(StrUtil.SPACE, SpringContextUtil.getRequestErrorMsg()));
                }
            }
        } else {
            log.warn("淘宝订单详情查询失败, bizOrderId={}, msg={}", req.getBizOrderId(), orderDetailRes.getMsg());
        }
    }

    /**
     * 保存订单和订单详情
     *
     * @param order         order
     * @param orderProducts orderProducts
     * @param req           req
     */
    private void saveOrderInfo(Order order, List<OrderProduct> orderProducts, OrderReq req) {
        req.setDetail(orderProducts);
        //开启写库
        MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
            orderService.save(order);
            List<OrderItem> itemList = orderService.buildOrderItemParam(order, req);
            if (CollUtil.isNotEmpty(itemList)) {
                orderItemService.saveBatch(itemList);
            }else {
                throw new CustomizeException(StrUtil.format("平台订单[{}]子商品创建失败！订单创建失败！", order.getOrderId()));
            }
        }).commit();
    }

    private Order buildOrderParam(AlibabaAelophyOrderGetResponse.OrderResponse req) {
        String storeCode = req.getStoreId();
        String platCode = ThirdPlatformCommonConst.THIRD_PLAT_TB;
        //查询门店
        Store store = storeService.getOneStoreByStoreCode(platCode, storeCode);
        if (null == store) {
            RRExceptionHandler.logError(StrUtil.format("{}门店({})不存在或未启用", PlatfromEnum.TB.getMessage(), storeCode), req, null, smsService::sendOaMsgTo9JiMan);
            return null;
        }
        Order order = new Order();
        order.setPlatCode(platCode);
        order.setOrderId(String.valueOf(req.getOutOrderId()));
        order.setBizOrderId(String.valueOf(req.getBizOrderId()));
        order.setStoreCode(storeCode);
        order.setAreaId(store.getAreaId());
        order.setTenantCode(store.getTenantCode());
        order.setType(0);

        //收货人信息
        AlibabaAelophyOrderGetResponse.ReceiveInfo receiveInfo = req.getReceiveInfo();
        order.setBuyerAddress(receiveInfo.getReceiverAddress());

        order.setBuyerMobile(receiveInfo.getReceiverPhone());
        order.setBuyerName(receiveInfo.getReceiverName());
        order.setBuyerPin(receiveInfo.getReceiverName());

        order.setOrderTime(req.getPayTime());
        order.setTradeTime(req.getPayTime());
        if (StringUtils.isNotBlank(receiveInfo.getReceiverPoi())) {
            String[] receiverPoiArr = receiveInfo.getReceiverPoi().split(",");
            if (receiverPoiArr.length == 2) {
                Integer cityId = AtlasUtil.getAreaCodeByLocation(receiverPoiArr[1], receiverPoiArr[0]);
                order.setBuyerCity(Convert.toStr(cityId));
            }
            //高德坐标系转wgs84
            Coordinate coordinate = CoordinateUtil.gcj2wgs(new Coordinate(receiveInfo.getReceiverPoi()));
            order.setBuyerPosition(coordinate.toString());
        }
        order.setPickType(0);
        if (TaobaoDeliveryTypeEnum.SELF_DELIVERY.getCode().equals(req.getDeliveryType())) {
            order.setPickType(0);
        } else if (TaobaoDeliveryTypeEnum.USER_PICKUP.getCode().equals(req.getDeliveryType())) {
            order.setPickType(1);
        }
        order.setGenerateManualSubFlag(0);
        //客户实付部分=客户实付金额（total）
        BigDecimal userPayMoney = NumberUtil.div(Convert.toBigDecimal(req.getPayFee()), NumberConstant.ONE_HUNDRED, 2);
        BigDecimal postFee = NumberUtil.div(req.getPostFee(), NumberConstant.ONE_HUNDRED, 2);
        //平台补贴
        Long platformFee = Optional.ofNullable(req.getSkuDiscountPlatformFee()).orElse(0L) + Optional.ofNullable(req.getPostDiscountPlatformFee()).orElse(0L);
        BigDecimal platDiscountMoney = NumberUtil.div(Convert.toBigDecimal(platformFee), NumberConstant.ONE_HUNDRED, 2);
        //商家承担金额
        Long merchantFee = Optional.ofNullable(req.getSkuDiscountMerchantFee()).orElse(0L) + Optional.ofNullable(req.getPostDiscountMerchantFee()).orElse(0L);
        BigDecimal tenantDiscountMoney = NumberUtil.div(Convert.toBigDecimal(merchantFee), NumberConstant.ONE_HUNDRED, 2);

        //订单商品销售总金额
        BigDecimal orderTotalMoney = NumberUtil.div(Convert.toBigDecimal(req.getOriginalFee()), NumberConstant.ONE_HUNDRED, 2);
        //订单优惠金额(商家优惠+平台优惠)
        BigDecimal orderDiscountMoney = tenantDiscountMoney.add(platDiscountMoney);
        //订单货款总金额 [订单货款金额（订单总金额-商家优惠金额）]
        BigDecimal orderGoodsMoney = orderTotalMoney.subtract(tenantDiscountMoney);
        //订单原总价
        order.setTotalMoney(orderTotalMoney.doubleValue());
        //订单优惠金额(商家优惠+平台优惠)
        order.setDiscountMoney(orderDiscountMoney.doubleValue());
        //客户实付部分=客户实付金额（total）
        order.setPayableMoney(userPayMoney.doubleValue());
        order.setFreightMoney(postFee.doubleValue());
        order.setGoodMoney(orderGoodsMoney.doubleValue());
        order.setPlatMoney(platDiscountMoney.doubleValue());
        order.setVenderMoney(tenantDiscountMoney.doubleValue());
        //根据门店盘点是否是九机的租户
        //预计送达时间
        if (StrUtil.isNotBlank(receiveInfo.getExpectArriveTime())) {
            String[] timeArray = receiveInfo.getExpectArriveTime().split("~");
            order.setEarliestReceiptTime(DateUtil.parseLocalDateTime(timeArray[0], DatePattern.NORM_DATETIME_PATTERN));
            order.setEstimateArrivalTime(DateUtil.parse(timeArray[1], DatePattern.NORM_DATETIME_PATTERN));
        }
        order.setGenerateManualSubFlag(0);
        //订单备注
        String remark = StrUtil.format("【淘宝小时达订单】{}备注：{}，收货人：{},电话：{}，地址：{}",receiveInfo.getReceiverMemo(),req.getOutOrderId(), receiveInfo.getReceiverName(), receiveInfo.getReceiverPhone(), receiveInfo.getReceiverAddress());
        order.setBuyerRemark(remark);
        return order;
    }

    private void noticeCh999User(List<Order> orderList,List<OrderItem> orderItemList,String orderId,boolean isRefundPart) {
        //获取全区推送人
        Set<Integer> ch999Ids = getNoticeCh999Ids();
        //发送消息通知给门店的管理层和销售
        if(CollUtil.isNotEmpty(orderList)){
            orderList.forEach(order -> {
                String msgSuffix;
                if (XtenantJudgeUtil.isJiujiMore()) {
                    if(isRefundPart){
                        String productNames = orderItemList.stream().filter(v -> Objects.equals(1,v.getCancelCheck())).map(soi -> StrUtil.format("sku[{}] 退数量({})", soi.getSkuId(), soi.getSkuCount()))
                                .collect(Collectors.joining(StringPool.SPACE));
                        msgSuffix = "淘宝小时达平台用户已退商品["+ productNames +"]，请及时手动删除相关商品并原路径退款！";
                    }else{
                        msgSuffix = "淘宝小时达平台用户已取消订单，请及时追回相关商品！";
                    }
                } else {
                    msgSuffix = "平台用户已取消订单，请及时追回相关商品！";
                }
                R<Object> noticeR = orderService.toMessageNotification(order, ch999Ids, msgSuffix);
                if(!noticeR.isSuccess()){
                    orderService.sendOrderNotice(order.getOrderId(), 2,StrUtil.format("淘宝小时达订单号：{}，{}", order.getOrderId(), msgSuffix), ch999Ids);
                }
            });
        }else{
            //通知所有的相关人员
            orderService.sendOrderNotice(orderId, 2,
                    StrUtil.format("淘宝订单号：{} 平台用户已取消订单，请及时追回相关商品！", orderId), ch999Ids);
        }
    }

    private Set<Integer> getNoticeCh999Ids() {
        return SpringUtil.getBean(WXSmsReceiverService.class)
                .getByClassify(SmsReceiverClassfyEnum.DOU_DIAN_PRODUCT_SYSNC.getCode())
                .map(sr -> StrUtil.splitTrim(sr.getCh999ids(), StringPool.COMMA).stream().map(Convert::toInt).collect(Collectors.toSet()))
                .orElse(Collections.emptySet());
    }

    /**
     * 凭证做账
     * @param order order
     */
    private void voucherByRefund(Order order) {
        PingzhengBO pingzhengBO = new PingzhengBO();
        String zhaiyao = "", fzhs = "", jief = "", daif = "", kemu = "";
        //摘要
        String zy = order.getAreaCode() + "店，淘宝小时达订单退订，订单号：" + order.getSubId();
        zhaiyao = zy + "|" + zy;
        //科目
        kemu = "1122131|220301";
        //平台承担金额+用户应付金额 = 贷
        double price = order.getPlatMoney() + order.getPayableMoney();
        jief = DecideUtil.iif(BigDecimal.valueOf(price).compareTo(BigDecimal.ZERO) == 0, "0", price) + "|0";
        daif = "0|" + DecideUtil.iif(BigDecimal.valueOf(price).compareTo(BigDecimal.ZERO) == 0, "0", price);
        fzhs = "无|无";
        pingzhengBO.setZhaiyao(zhaiyao).setKemu(kemu).setFzhs(fzhs).setJief(jief).setDaif(daif);
        PingzhengResultBO result = voucherService.addPingZheng(pingzhengBO);
        if (result == null || !result.getFlag()) {
            String msg = "淘宝小时达凭证生成失败,订单号:" + order.getSubId();
            if (result != null) {
                msg = msg + ",原因：" + result.getErrorMsg();
            }
            log.error(msg);
        }
    }
}
