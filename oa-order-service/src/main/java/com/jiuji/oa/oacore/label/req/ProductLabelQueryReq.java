package com.jiuji.oa.oacore.label.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jiuji.oa.oacore.common.req.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * description: <  >
 * translation: <  >
 * date : 2020-10-19 10:15
 *
 * <AUTHOR> leee41
 **/
@EqualsAndHashCode(callSuper = true)
@ApiModel("产品标签页面过滤查询REQ")
@Data
@Accessors(chain = true)
public class ProductLabelQueryReq extends PageReq {
    @ApiModelProperty(value = "商品ids")
    private List<Integer> productIds;
    @ApiModelProperty(value = "商品id")
    @Max(value = Integer.MAX_VALUE,message = "商品id错误")
    private Integer productId;
    @ApiModelProperty(value = "标签")
    private Integer label;
    @ApiModelProperty(value = "有效开始时间")
    private LocalDate startStartTime;
    @ApiModelProperty(value = "有效结束时间")
    private LocalDate startEndTime;
    @ApiModelProperty(value = "失效开始时间")
    private LocalDate endStartTime;
    @ApiModelProperty(value = "失效结束时间")
    private LocalDate endEndTime;
    @ApiModelProperty(value = "分类")
    private List<String> categories;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "创建人ID")
    private Integer createBy;
    @ApiModelProperty(value = "ppid")
    @Max(value = Integer.MAX_VALUE,message = "ppriceid错误")
    private Integer ppid;
    @JsonIgnore
    private String cids;
    @ApiModelProperty(value = "过期时间,正负365之内")
    @Max(365)
    @Min(-365)
    @NotNull
    private Integer delay;
    @JsonIgnore
    private LocalDate delayDate;
    private String order;
    private String asc = "asc";
    @ApiModelProperty(value = "品牌id")
    private List<Integer> brandIds;
    @ApiModelProperty(value = "门店类型，1：自营店，2：加盟店，3：小店")
    private Integer areaKind;
    private List<Integer> areaIds;
    @ApiModelProperty(value = "开始时间")
    private LocalDate startTime;
    @ApiModelProperty(value = "结束时间")
    private LocalDate endTime;
    @ApiModelProperty(value = "时间状态")
    private Integer timeStatus;
    @ApiModelProperty(value = "上架状态")
    private Boolean display;

    private Integer xTenant;
    /**
     * 排除大仓
     */
    private List<Integer> excludeIds;

    /**
     * 1周 2月 3季 4年
     * @see com.jiuji.oa.oacore.label.enums.AggregateTypeEnum
     */
    private Integer aggregateType;


    /**
     * 接件量大于
     */
    private Integer connectionQuantityGreaterThan;


    /**
     * 售后率大于
     */
    private BigDecimal afterSalesGreaterThan;


}
