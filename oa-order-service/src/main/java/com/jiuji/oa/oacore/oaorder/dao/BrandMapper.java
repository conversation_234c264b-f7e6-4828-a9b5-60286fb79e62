package com.jiuji.oa.oacore.oaorder.dao;

import com.jiuji.oa.oacore.oaorder.po.Brand;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jiuji.oa.oacore.oaorder.vo.res.BrandRes;
import com.jiuji.oa.oacore.promocode.bo.CommonSwitchBO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-06
 */
@Mapper
public interface BrandMapper extends BaseMapper<Brand> {
    /**
     * 通过分类id获取品牌
     * @param cids
     * @return
     */
    List<BrandRes> getBrandsByCid(@Param("cids") List<Integer>  cids);

    /**
     *根据分类获取分类、品牌信息
     * @param cids 分类集合
     */
    List<BrandRes> getCidAndBrands(@Param("cids") List<Integer> cids);

    List<CommonSwitchBO> getByIds(@Param("ids") List<Integer> limitIdsStr);

    List<Integer> getBrandIds(@Param("ids") List<Integer> split);

}
