package com.jiuji.oa.oacore.yearcardtransfer.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.yearcardtransfer.entity.AreaGuestBookAccountLog;
import com.jiuji.oa.oacore.yearcardtransfer.mapper.AreaGuestBookAccountLogMapper;
import com.jiuji.oa.oacore.yearcardtransfer.service.AreaGuestBookAccountLogService;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@DS(DataSourceConstants.OA_LOG)
public class AreaGuestBookAccountLogServiceImpl extends ServiceImpl<AreaGuestBookAccountLogMapper, AreaGuestBookAccountLog> implements AreaGuestBookAccountLogService {

    @Override
    public boolean saveLog(Integer businessId, String inUser, String contentFormat, String... params) {
        AreaGuestBookAccountLog log = new AreaGuestBookAccountLog();
        log.setContent(StrUtil.format(contentFormat, params));
        log.setInUser(inUser);
        log.setBusinessId(businessId);
        log.setCreateTime(LocalDateTime.now());
        return this.save(log);
    }

    @Override
    public List<AreaGuestBookAccountLog> listByBusinessId(Integer businessId) {
        if(null == businessId){
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<AreaGuestBookAccountLog> query = new LambdaQueryWrapper<>();
        query.eq(AreaGuestBookAccountLog::getBusinessId, businessId);
        query.orderByAsc(AreaGuestBookAccountLog::getCreateTime);
        return baseMapper.selectList(query);
    }
}
