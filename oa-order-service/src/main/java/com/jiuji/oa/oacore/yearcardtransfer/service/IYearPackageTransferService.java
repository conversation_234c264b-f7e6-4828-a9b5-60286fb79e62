package com.jiuji.oa.oacore.yearcardtransfer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.oacore.yearcardtransfer.dto.YearPackageTransferCancelDto;
import com.jiuji.oa.oacore.yearcardtransfer.dto.YearPackageTransferDetailDto;
import com.jiuji.oa.oacore.yearcardtransfer.entity.YearPackageTransferPo;
import com.jiuji.tc.common.vo.R;

import java.util.Collection;
import java.util.List;

public interface IYearPackageTransferService extends IService<YearPackageTransferPo> {
    YearPackageTransferDetailDto getTransferDetail(String transferCode, Integer userId);

    YearPackageTransferDetailDto claimTransfer(String transferCode, Integer userId);

    /**
     * 获取明细列表信息
     *
     * @param bindBasketIds
     * @param userId
     * @return
     */
    List<YearPackageTransferDetailDto> listTransferDetail(Collection<Integer> bindBasketIds, Integer userId);

    /**
     * 撤销赠送
     *
     * @param transferCode 转赠码
     * @param userId 会员ID
     * @return 撤销结果
     */
    R<Boolean> revokeTransfer(YearPackageTransferCancelDto req);
}
