package com.jiuji.oa.oacore.oaorder.service;

import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.cloud.product.vo.request.ClientPriceReqVO;
import com.jiuji.oa.oacore.brand.dji.vo.ProductInfoVO;
import com.jiuji.oa.oacore.oaorder.bo.ProductIsMobileInfoBO;
import com.jiuji.oa.oacore.oaorder.bo.ProductSimpleBO;
import com.jiuji.oa.oacore.oaorder.po.Productinfo;
import com.jiuji.oa.oacore.oaorder.res.ProductInfoSimpleVo;
import com.jiuji.oa.oacore.oaorder.res.SearchProductInfoVO;
import com.jiuji.oa.oacore.oaorder.vo.res.OutboundOrderRes;
import com.jiuji.oa.oacore.operator.po.SelectKeyType;
import com.jiuji.oa.oacore.promocode.bo.CommonSwitchBO;
import com.jiuji.oa.oacore.subRecommended.vo.req.ProductBaseInfoReq;
import com.jiuji.oa.oacore.subRecommended.vo.res.ProductBaseInfo;
import com.jiuji.oa.oacore.weborder.req.DiaoboStockInfoReqVO;
import com.jiuji.oa.oacore.weborder.req.GiftStockReqVO;
import com.jiuji.oa.oacore.weborder.req.WebStockInfoReqVO;
import com.jiuji.oa.oacore.weborder.req.WebStockPriceReqVO;
import com.jiuji.oa.oacore.weborder.res.DiaoboStockInfoResVO;
import com.jiuji.oa.oacore.weborder.res.GiftStockResVO;
import com.jiuji.oa.oacore.weborder.res.WebStockInfoResVO;
import com.jiuji.oa.oacore.weborder.res.WebStockPriceResVO;
import com.jiuji.oa.oacore.weborder.vo.req.StockInfoReqVO;
import com.jiuji.oa.oacore.weborder.vo.res.CheckStockInfoResVO;
import com.jiuji.oa.oacore.weborder.vo.res.StockCountVO;
import com.jiuji.tc.common.vo.R;
import springfox.documentation.annotations.Cacheable;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-06
 */
public interface ProductinfoService extends IService<Productinfo> {

    /**
     * 获取主站价格
     * @param clientPriceReqVO
     * @return
     */
    Map<Integer, BigDecimal> getPriceByWeb(ClientPriceReqVO clientPriceReqVO);
    /**
     * 获取年货包列表
     * @return
     */
    List<Integer> annualPackageList();


    List<Integer> annualPackageListWithCache();
    /**
     * 根据ppid查询商品信息 价格库存
     * @param ppids
     * @return
     */
    Map<Integer,ProductBaseInfo> selectProductBaseInfoMap(List<Integer> ppids,Integer areaId);
    
    /**
     * 根据ppid查询商品信息 价格库存
     * @param ppids
     * @param areaId 归属地
     * @param useCache 是否使用缓存版本的annualPackageList
     * @return
     */
    Map<Integer,ProductBaseInfo> selectProductBaseInfoMap(List<Integer> ppids,Integer areaId, boolean useCache);

    /**
     * 根据串号获取商品信息
     *
     * @param imei
     * @return
     */
    ProductInfoSimpleVo getProductInfoByImei(String imei);

    /**
     * 根据名称获取小件商品信息热销top5
     *
     * @param name name
     */
    List<SearchProductInfoVO> getTop5SmallProductByNameLike(String name);

    /**
     * 通过ppid查询pid
     *
     * @param ppids
     * @return
     */
    Map<Integer, ProductSimpleBO> getProductMapByPpids(List<Integer> ppids);

    Map<Integer, ProductSimpleBO> getProductMapByPpidsNew(List<Integer> ppids);

    List<CommonSwitchBO> getNameByIds(String limitIdsStr);

    List<ProductInfoVO> listProductInfoByBrandId(Integer brandId);

    /**
     * 根据ppid查询商品信息
     * @param ppids
     * @return
     */
    List<SearchProductInfoVO> getProductNamesByPpids(String ppids);

    /**
     * 通过ppid获取商品信息
     * @param ppid
     * @return
     */
    List<Productinfo> getProductinfoByPpid(List<Integer> ppid);

    Map<Integer,List<ProductSimpleBO>> getProductInfoByProductId(List<Integer> productIdList);

    /**
     * 根据mkcId查询商品信息
     * @param mkcId
     * @return
     */
    List<SearchProductInfoVO> getProductNamesByMkcId(String mkcId);

    /**
     * 通过mkcId获取库存信息
     * @param mkcIds
     * @return
     */
    List<SearchProductInfoVO> listProductNamesOnlyByMkcId(List<Integer> mkcIds);

    /**
     * 查询商品信息
     * @param mkcId 库存id
     * @param isToBasketIdNull 是否限制ToBasketId为null
     * @return
     */
    List<SearchProductInfoVO> getProductNamesByMkcId(String mkcId, boolean isToBasketIdNull);

    /**
     * 通过订单号获取出库单
     * @param saleNo
     * @return
     */
    R<OutboundOrderRes> getOutboundOrder(Integer saleNo);

    /**
     * ppid查询商品大小件信息
     * @param ppids
     * @return
     */
    List<ProductIsMobileInfoBO> getProductIsmobileByPpids(List<Integer> ppids);

    /**
     * 查询大件库存
     * @param ppids
     * @return
     */
    List<StockCountVO> getProductMkcCountByPpids(List<Integer> ppids, StockInfoReqVO req);

    /**
     * 查询小件库存
     * @param ppids
     * @return
     */
    List<StockCountVO> getProductKcCountByPpids(List<Integer> ppids, StockInfoReqVO req);

    /**
     * 查询大件库存
     * @param req
     * @return
     */
    List<WebStockInfoResVO> getProductMkcStockByPpids(WebStockInfoReqVO req);

    /**
     * 查询小件库存
     * @param req
     * @return
     */
    List<WebStockInfoResVO> getProductKcStockByPpids(WebStockInfoReqVO req);

    /**
     * 查询调拨库存信息
     * @param req
     * @return
     */
    List<DiaoboStockInfoResVO> getDiaoboByPpidAndToArea(DiaoboStockInfoReqVO req);

    List<WebStockPriceResVO> getInPriceByPpid(WebStockPriceReqVO req);

    /**
     * 查询大件库存数量
     * @param ppids
     * @param areaIds
     * @return
     */
    List<CheckStockInfoResVO> getMkcStockByPpidsAndAreaIds(String ppids, String areaIds);

    /**
     * 查询小件库存数量
     * @param ppids
     * @param areaIds
     * @return
     */
    List<CheckStockInfoResVO> getKcStockByPpidsAndAreaIds(String ppids, String areaIds);

    /**
     * 查询赠品库存
     * @param req
     * @return
     */
    List<GiftStockResVO> getGiftStockByAreaIds(GiftStockReqVO req);

    /**
     * 查询商品成本
     * @param req
     * @return
     */
    List<WebStockPriceResVO> getProductInPriceByPpid(WebStockPriceReqVO req);
}
