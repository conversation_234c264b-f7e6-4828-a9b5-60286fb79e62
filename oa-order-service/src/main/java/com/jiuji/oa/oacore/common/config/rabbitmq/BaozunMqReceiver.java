package com.jiuji.oa.oacore.common.config.rabbitmq;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.jiuji.infra.lmstfy.anotation.LmstfyConsume;
import com.jiuji.oa.oacore.apollo.ApolloKeys;
import com.jiuji.oa.oacore.common.config.lmstfy.LmstfyConfig;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.constant.RedisKeyConstant;
import com.jiuji.oa.oacore.common.constant.RequestAttrKeys;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.common.util.SpringContextUtil;
import com.jiuji.oa.oacore.csharp.cloud.CsharpCloud;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.BzSubmitOrderInput;
import com.jiuji.oa.oacore.thirdplatform.baozun.bo.S47RequestBodyBO;
import com.jiuji.oa.oacore.thirdplatform.baozun.dao.BzTenantClientMapper;
import com.jiuji.oa.oacore.thirdplatform.baozun.po.RetryCloudStore;
import com.jiuji.oa.oacore.thirdplatform.baozun.service.BzTenantClientService;
import com.jiuji.oa.oacore.thirdplatform.baozun.service.BzTenantCloudStoreService;
import com.jiuji.oa.oacore.thirdplatform.baozun.vo.req.BzCloudStoreMqReq;
import com.jiuji.oa.oacore.thirdplatform.baozun.vo.req.BzErrorNoticeReq;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.TraceIdUtil;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import com.meitu.platform.lmstfy.Job;
import com.meitu.platform.lmstfy.client.LmstfyClient;
import com.meitu.platform.lmstfy.exception.LmstfyException;
import com.meitu.platform.lmstfy.response.DeadLetterResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 宝尊mq信息管理
 */
@Slf4j
@Component
public class BaozunMqReceiver {
    @Resource
    private BzTenantClientService bzTenantClientService;
    @Resource
    private BzTenantCloudStoreService bzTenantCloudStoreService;
    @Resource(name = "firstLmstfyClient")
    private LmstfyClient firstLmstfyClient;

    /**
     *
     * @param message
     */
    @RabbitHandler
    @RabbitListener(queuesToDeclare = @Queue(name = RabbitMqConfig.BAOZUN_S47_OASUBMIT, admins = "oaAsyncRabbitAdmin"),
            containerFactory = "oaAsyncListenerContainerFactory", admin = "oaAsyncRabbitAdmin")
    @SneakyThrows
    public void s47OaSubmit(Message message){
        MDC.put(TraceIdUtil.TRACE_ID_KEY, TraceIdUtil.getTraceId());
        String body = StrUtil.str(message.getBody(), Charset.defaultCharset());
        try {
            log.warn("MQ调用oa建单,返回参数{}", body);
            JSONObject json = JSON.parseObject(body);
            String url = json.getString("url");
            S47RequestBodyBO.OrderSubIdBO orderMsg = json.getObject("orderMsg", S47RequestBodyBO.OrderSubIdBO.class);
            BzSubmitOrderInput submitOrder = json.getObject("submitOrder", BzSubmitOrderInput.class);
            Consumer<R> fallback = null;
            if(StrUtil.endWith(url, BzTenantClientService.BAO_ZUN_SUBMIT_ORDER)){
                fallback = r-> orderMsg.setRemark(submitOrder.getRemark());
            }
            bzTenantClientService.postOrder(url, json.getString("workName"), orderMsg, submitOrder, fallback);
            //保存备注和错误信息
            List<S47RequestBodyBO.OrderSubIdBO> updateOrderMsgMarks = Stream.of(orderMsg)
                    .filter(os -> StrUtil.isNotBlank(os.getRemark()) || StrUtil.isNotBlank(os.getMessage()))
                    .collect(Collectors.toList());
            if(!updateOrderMsgMarks.isEmpty()){
                MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () ->
                        SpringUtil.getBean(BzTenantClientMapper.class).batchUpdateOrderMsg(updateOrderMsgMarks)).commit();
            }
            //异步触发完单
            CompletableFuture.runAsync(()-> SpringUtil.getBean(CsharpCloud.class).baoZunAutoCompleteSub(submitOrder.getOrderNum()));

        }catch (Exception e){
            RRExceptionHandler.logError("MQ调用宝尊oa建单接口", body, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }finally {
            MDC.remove(TraceIdUtil.TRACE_ID_KEY);
        }
    }

    /**
     * 生成采购单
     * @param message
     */
    @RabbitHandler
    @RabbitListener(queuesToDeclare = @Queue(value = RabbitMqConfig.BAOZUN_CLOUD_STORE, admins = "oaAsyncRabbitAdmin"),
            containerFactory = "oaAsyncListenerContainerFactory", admin = "oaAsyncRabbitAdmin")
    @SneakyThrows
    public void baoZunCloudStore(Message message){
        MDC.put(TraceIdUtil.TRACE_ID_KEY, TraceIdUtil.getTraceId());
        String body = StrUtil.str(message.getBody(), Charset.defaultCharset());
        log.warn("MQ宝尊云仓统一处理接口,返回参数{}", body);
        Optional<BzCloudStoreMqReq.OperationTypeEnum> operationTypeOpt = Optional.empty();
        Optional<BzCloudStoreMqReq> reqOpt = Optional.empty();
        try {
            BzCloudStoreMqReq cloudStoreMqReq = JSON.parseObject(body, BzCloudStoreMqReq.class);
            reqOpt = Optional.of(cloudStoreMqReq);
            BzCloudStoreMqReq.OperationTypeEnum operationTypeEnum = EnumUtil.getEnumByCode(BzCloudStoreMqReq.OperationTypeEnum.class, cloudStoreMqReq.getOperationType());
            if(operationTypeEnum == null){
               throw new CustomizeException("操作类型错误");
            }
            operationTypeOpt = Optional.of(operationTypeEnum);
            switch (operationTypeEnum){
                case GENERATE_PURCHASE_ORDER:
                    //构建采购单
                    bzTenantCloudStoreService.mqGenerateCaigouSub(cloudStoreMqReq);
                    break;
                case LARGE_INBOUND:
                    //大件自动入库
                    bzTenantCloudStoreService.mqLargeInbound(cloudStoreMqReq);
                    break;
                case SMALL_INBOUND:
                    //小件自动入库
                    bzTenantCloudStoreService.mqsmallInbound(cloudStoreMqReq);
                    break;
                case TRY_SUB_OUT_KC:
                    //尝试订单出库
                    bzTenantCloudStoreService.mqTrySubOut(cloudStoreMqReq);
                    break;
                default:
                    ;
            }
            //
            //订单自动出库
        }catch (Exception e){
            if(e instanceof CustomizeException){
                CustomizeException ee = (CustomizeException) e;
                if(ExceptionUtil.isCausedBy(e, CustomizeException.class)){
                    log.warn("异常信息", e);
                    //进行推送通知
                    bzTenantCloudStoreService.configErrorNotice(Optional.ofNullable(ee.getR()).map(R::getExData)
                                    .map(exd -> exd.get("areaId")).map(Convert::toInt).orElse(null),
                            reqOpt.map(BzCloudStoreMqReq::getTransactionNumber).orElse(StringPool.EMPTY),
                            "{}[{}]处理失败, 原因: {}, 15分钟后重试,请及时调整", EnumUtil.getMessageByCode(BzCloudStoreMqReq.OrderTypeEnum.class,
                                    reqOpt.map(BzCloudStoreMqReq::getOrderType).orElse(BzCloudStoreMqReq.OrderTypeEnum.SALES_ORDER.getCode()))
                            , reqOpt.map(BzCloudStoreMqReq::getTransactionNumber).orElse(StringPool.EMPTY),  ee.getMsg());
                    //重试
                    firstLmstfyClient.publish(LmstfyConfig.resolve(LmstfyConfig.BZ_CLOUD_STORE_ORDER_RETRY),
                            JSON.toJSONString(RetryCloudStore.builder().mqBeanName("oaAsyncRabbitTempe").messageBody(body)
                                    .queueName(RabbitMqConfig.BAOZUN_CLOUD_STORE).build()).getBytes(StandardCharsets.UTF_8),
                            0, (short) 1, BzTenantCloudStoreService.RETRY_DELAY_SECONDS);
                    return;
                }
            }
            RRExceptionHandler.logError(StrUtil.format("宝尊云仓订单[{}]统一处理接口, {}",
                            reqOpt.map(BzCloudStoreMqReq::getTransactionNumber).orElse(StringPool.EMPTY), operationTypeOpt
                            .map(BzCloudStoreMqReq.OperationTypeEnum::getMessage).orElse(StringPool.EMPTY)),
                    body, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }finally {
            MDC.remove(TraceIdUtil.TRACE_ID_KEY);
        }
    }

    @RabbitHandler
    @RabbitListener(queuesToDeclare = @Queue(value = RabbitMqConfig.BAOZUN_PARSE_A01_CLOUD_STORE_QUEUE, admins = "oaAsyncRabbitAdmin"),
            containerFactory = "oaAsyncListenerContainerFactory", admin = "oaAsyncRabbitAdmin")
    @SneakyThrows
    public void baozunParseA01AfterCloudStore(Message message){
        MDC.put(TraceIdUtil.TRACE_ID_KEY, TraceIdUtil.getTraceId());
        String transactionNumber = StrUtil.str(message.getBody(), Charset.defaultCharset());
        log.warn("MQ宝尊解析A接口信息,返回参数{}", transactionNumber);
        try {
            baozunParseA01AfterCloudStoreNoEx(transactionNumber);
        }finally {
            MDC.remove(TraceIdUtil.TRACE_ID_KEY);
        }
    }

    public void baozunParseA01AfterCloudStoreNoEx(String transactionNumber){
        try{
            bzTenantCloudStoreService.parseA01AfterCloudStore(transactionNumber);
        }catch (Exception e){
            if(ExceptionUtil.isCausedBy(e, CustomizeException.class)){
                CustomizeException ee = (CustomizeException) e;
                Integer areaId = SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.BAOZUN_AREA_ID)
                        .stream().findFirst().map(Convert::toInt).orElseGet(() -> Optional.ofNullable(ee.getR()).map(R::getExData)
                                .map(exd -> exd.get("areaId")).map(Convert::toInt).orElse(null));
                if(Objects.equals(ee.getCode(), BzTenantCloudStoreService.RETRY_CODE)){
                    //重试
                    try {
                        log.warn("异常信息", e);
                        //进行推送通知
                        bzTenantCloudStoreService.configErrorNotice(areaId, transactionNumber,
                                "A接口解析单号[{}]生成订单失败, 原因: {}, 15分钟后重试,请及时调整", transactionNumber,  ee.getMsg());
                        firstLmstfyClient.publish(LmstfyConfig.resolve(LmstfyConfig.BZ_CLOUD_STORE_ORDER_RETRY),
                                JSON.toJSONString(RetryCloudStore.builder().mqBeanName("oaAsyncRabbitTempe").messageBody(transactionNumber)
                                        .queueName(RabbitMqConfig.BAOZUN_PARSE_A01_CLOUD_STORE_QUEUE).build()).getBytes(StandardCharsets.UTF_8),
                                0, (short) 1, BzTenantCloudStoreService.RETRY_DELAY_SECONDS);
                    } catch (LmstfyException ex) {
                        RRExceptionHandler.logError(StrUtil.format("宝尊A接口结算, 平台单号: {}, 重试推送异常", transactionNumber),
                                transactionNumber, ex, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
                    }
                }else{
                    //进行推送通知
                    bzTenantCloudStoreService.configErrorNotice(areaId,transactionNumber, "A接口解析单号[{}]生成订单失败, 原因: {}", transactionNumber,  ee.getMsg());
                }
            }else{
                RRExceptionHandler.logError(StrUtil.format("宝尊A接口结算, 平台单号: {}", transactionNumber),
                        transactionNumber, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            }
        }

    }



    @RabbitHandler
    @RabbitListener(bindings = {
            @QueueBinding(                  //exchange与队列的绑定
                    value = @Queue(RabbitMqConfig.BAOZUN_REFUND_CLOUD_STORE_QUEUE),         //绑定队列，如果没有指定队列名称，系统会给一个生成一个临时的队列名
                    exchange = @Exchange(value = RabbitMqConfig.BAOZUN_AUTO_REFUND_TOPIC, type = "fanout", durable = "false") //指定exchange的名称和类型
            )
    }, containerFactory = "oaAsyncListenerContainerFactory", admin = "oaAsyncRabbitAdmin")
    @SneakyThrows
    public void baozunRefundCloudStore(Message message){
        MDC.put(TraceIdUtil.TRACE_ID_KEY, TraceIdUtil.getTraceId());
        String transactionNumber = StrUtil.str(message.getBody(), Charset.defaultCharset());
        log.warn("MQ宝尊云仓退款,返回参数{}", transactionNumber);
        try {
            bzTenantCloudStoreService.mqRefundFactory(transactionNumber);
        }catch (Exception e){
            if(ExceptionUtil.isCausedBy(e, CustomizeException.class)){
                CustomizeException ee = (CustomizeException) e;
                if(Objects.equals(ee.getCode(), BzTenantCloudStoreService.RETRY_CODE)){
                    log.warn("异常信息", e);
                    //进行推送通知
                    bzTenantCloudStoreService.configErrorNotice(Optional.ofNullable(ee.getR()).map(R::getExData)
                                    .map(exd -> exd.get("areaId")).map(Convert::toInt).orElse(null), transactionNumber,
                            "宝尊云仓单号[{}]退款失败, 原因: {}, 15分钟后重试,请及时调整", transactionNumber,  ee.getMsg());
                    //重试
                    firstLmstfyClient.publish(LmstfyConfig.resolve(LmstfyConfig.BZ_CLOUD_STORE_ORDER_RETRY),
                            JSON.toJSONString(RetryCloudStore.builder().mqBeanName("oaAsyncRabbitTempe").messageBody(transactionNumber)
                                    .queueName(RabbitMqConfig.BAOZUN_REFUND_CLOUD_STORE_QUEUE).build()).getBytes(StandardCharsets.UTF_8),
                            0, (short) 1, BzTenantCloudStoreService.RETRY_DELAY_SECONDS);
                    return;
                }
            }
            RRExceptionHandler.logError(StrUtil.format("宝尊云仓退款, 平台单号: {}", transactionNumber), transactionNumber, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }finally {
            MDC.remove(TraceIdUtil.TRACE_ID_KEY);
        }
    }

    @LmstfyConsume(queues = LmstfyConfig.BZ_CLOUD_STORE_ORDER_RETRY, clientBeanName = "firstLmstfyClient")
    public void bzPurchaseOrderRetry(Job job) throws LmstfyException {

        List<String> excludes = StrUtil.splitTrim(ApolloKeys.getApolloWithMainTenant(ApolloKeys.BAOZUN_CLOUDSTORE_RETRY_EXCLUDES, null), StringPool.COMMA);
        if(job == null || excludes.stream().anyMatch(e -> StrUtil.contains(job.getData(), e))){
            return;
        }
        log.warn("宝尊云仓订单重试队列消费到数据：{}", job.getData());
        if(!SpringContextUtil.isProduce()){
            log.warn("测试环境直接舍弃, 不进行重试：{}", job.getData());
            return;
        }
        StringRedisTemplate redisTemplate = SpringUtil.getBean("stringRedisTemplate", StringRedisTemplate.class);
        excludes = StrUtil.splitTrim(redisTemplate.opsForValue().get(RedisKeyConstant.STOP_BAOZUN_RETRY), StringPool.COMMA);
        if(excludes.stream().anyMatch(e -> StrUtil.contains(job.getData(), e))){
            return;
        }
        MDC.put(TraceIdUtil.TRACE_ID_KEY, TraceIdUtil.getTraceId());
        log.warn("宝尊云仓订单重试队列消费到数据：{}", job.getData());
        int queueSize = firstLmstfyClient.queueSize(job.getQueue());
        int deadSize = Optional.ofNullable(firstLmstfyClient.peekDeadLetter(job.getQueue())).map(DeadLetterResponse::getDeadLetterSize).orElse(0);
        int warnQueueSize = 10000;
        if(queueSize > warnQueueSize || deadSize > warnQueueSize){
            // 队列长度达到行预警
            SpringUtil.getBean(SmsService.class).sendOaMsgTo9JiMan(StrUtil.format("宝尊云仓订单重试队列当前队列长度达到{}，死信队列长度{}请及时处理", queueSize, deadSize));
        }
        String data = job.getData();
        try {
            RetryCloudStore req = JSON.parseObject(data, RetryCloudStore.class);
            SpringUtil.getBean(req.getMqBeanName(), RabbitTemplate.class).convertAndSend(req.getQueueName(), req.getMessageBody());
        }catch (Exception e){
            RRExceptionHandler.logError(StrUtil.format("宝尊云仓订单重试"), job, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }finally {
            MDC.remove(TraceIdUtil.TRACE_ID_KEY);
        }
    }

    @RabbitHandler
    @RabbitListener(queuesToDeclare = @Queue(name = RabbitMqConfig.BAOZUN_ERROR_NOTICE_QUEUE, admins = "oaAsyncRabbitAdmin"),
            containerFactory = "oaAsyncListenerContainerFactory", admin = "oaAsyncRabbitAdmin")
    @SneakyThrows
    public void baozunErrorNotice(Message message){
        MDC.put(TraceIdUtil.TRACE_ID_KEY, TraceIdUtil.getTraceId());
        String body = StrUtil.str(message.getBody(), Charset.defaultCharset());
        Optional<BzErrorNoticeReq> reqOpt = Optional.empty();
        try {
            BzErrorNoticeReq bzErrorNoticeReq = JSON.parseObject(body, BzErrorNoticeReq.class);
            reqOpt = Optional.of(bzErrorNoticeReq);
            bzTenantCloudStoreService.configErrorNotice(bzErrorNoticeReq.getAreaId(),bzErrorNoticeReq.getTransferNumber(), bzErrorNoticeReq.getTemplate(), "");
        }catch (Exception e){
            RRExceptionHandler.logError(StrUtil.format("MQ宝尊异常消息推送失败, 平台单号: {}", reqOpt.map(BzErrorNoticeReq::getTransferNumber).orElse("")), reqOpt, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }finally {
            MDC.remove(TraceIdUtil.TRACE_ID_KEY);
        }
    }
}
