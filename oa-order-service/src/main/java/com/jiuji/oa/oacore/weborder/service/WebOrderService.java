package com.jiuji.oa.oacore.weborder.service;

import com.jiuji.oa.oacore.common.res.PageRes;
import com.jiuji.oa.oacore.weborder.bo.AfterServiceSubSmallBO;
import com.jiuji.oa.oacore.weborder.req.*;
import com.jiuji.oa.oacore.weborder.res.*;
import com.jiuji.oa.oacore.weborder.vo.ImOrderVO;
import com.jiuji.oa.oacore.weborder.vo.JudgeGoodAccessoriesBO;
import com.jiuji.oa.oacore.weborder.vo.*;
import com.jiuji.oa.oacore.weborder.vo.req.CheckStockInfoReqVO;
import com.jiuji.oa.oacore.weborder.vo.req.MyClientReq;
import com.jiuji.oa.oacore.weborder.vo.req.OaStockInfoReqVO;
import com.jiuji.oa.oacore.weborder.vo.req.StockInfoReqVO;
import com.jiuji.oa.oacore.weborder.vo.req.WhetherToBuyReq;
import com.jiuji.oa.oacore.weborder.vo.res.CheckStockInfoResVO;
import com.jiuji.oa.oacore.weborder.vo.res.OrderStateRes;
import com.jiuji.oa.oacore.weborder.vo.res.StockInfoResVO;
import com.jiuji.tc.common.vo.R;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @since 2020/4/9
 */
@Service
public interface WebOrderService {
    String QUERY_HISTORY_ORDER = "queryHistoryOrder";


    Integer queryRetreatCount(@RequestParam("userId") Integer userId);

    /**
     * 获取新机订单
     * 1. 新机订单存在历史库，需要注意。
     * 2. 以旧换新业务需要特殊处理。
     * 3. 可能会需要 对应服务人员列表。
     * 4. sub_check<>4 or sub_date<=30 天;可以查询30天内被删除的订单。
     *
     * @param req
     * @return
     */
    PageRes<XinjiSubVO> pageNormal(WebOrderQueryReq req);

    /**
     * 获取新机订单第二版
     * 1. 新机订单存在历史库，需要注意。
     * 2. 以旧换新业务需要特殊处理。
     * 3. 可能会需要 对应服务人员列表。
     * 4. sub_check<>4 or sub_date<=30 天;可以查询30天内被删除的订单。
     *
     * @param req
     * @return
     */
    R<PageRes<XinjiSubVO>> pageNormalV2(WebOrderQueryReq req);


    /**
     * 获取个人中心增加待付款提示栏的订单
     * @param req
     * @return
     */
    R<PendingPaymentVO> selectOrderPendingPayment(PendingPaymentReq req);


    /**
     * 获取个人中心增加待付款提示栏的订单
     * @param req
     * @return
     */
    R<List<PendingPaymentVO>> selectOrderPendingPaymentV2(PendingPaymentReq req);

    /**
     * 获取良品订单
     * 1. 以旧换久业务需要w特殊处理
     *
     * @param req
     * @return
     */
    //TODO: 补充不同页面的查询条件。
    PageRes<LiangpinSubVO> pageLiangpin(WebOrderQueryReq req);

    /**
     * 获取回收订单
     *
     * @param req
     * @return
     */
    //TODO: 补充不同页面的查询条件
    PageRes<RecoverSubVO> pageRecover(WebOrderQueryReq req);

    //TODO: 需要考虑 补充小件逻辑???
    R<PageRes<AfterServiceSubVO>> pageAfterServiceRepair(WebAfterQueryReq req);

    R<PageRes<AfterServiceSubSmallBO>> pageAfterServiceRepairSmall(WebAfterQueryReq req);

    /**
     * 维修单和小件分页查询
     *
     * @param req
     * @return
     */
    R<PageRes<AfterServiceSubVO>> pageAfterAndSmallServiceRepair(WebAfterQueryReq req);

    PageRes<ReservationSubVO> pageReservation(WebOrderQueryReq req);

    OrderCountVO getOrderCount(WebOrderCountReq req);

    List<OrderStatusCheckVO> checkOrderStatusNormal(WebOrderCheckReq req);

    /**
     * 查询订单相关联的所有专属客服
     *
     * @param subIds 订单id
     * @return 专属客服
     */
    Map<Integer, List<Ch999UserServiceVO>> getExclusiveCustomerService(List<Integer> subIds, Integer count);

    Map<Integer, List<Ch999UserServiceVO>> getLiangPinExclusiveCustomerService(Collection<Integer> subIds, Integer count);

    /**
     * 根据用户id查询专属客服
     *
     * @param userId 用户id
     * @return 专属客服
     */
    List<Ch999UserServiceVO> listExclusiveCustomerServiceByUserId(Integer userId, int count, Integer xtenant);

    /**
     * 根据用户id查询专属客服
     *
     * @param userId  用户id
     * @param count   数量
     * @param xtenant 租户
     * @param current 页码
     * @return 专属客服
     */
    List<Ch999UserServiceVO> listExclusiveCustomerServiceByUserId(Integer userId, int count, Integer xtenant, long current, List<Ch999UserServiceVO> list);

    /**
     * 根据用户id查询未评价订单数量
     *
     * @param userId 用户id
     * @return 未评价订单数量
     */
    int countNotEvaluatedOrder(Integer userId);

    /**
     * 查询p30订金预定订单
     *
     * @param productIds 商品id
     * @param pay        订金金额
     * @return 订单
     */
    List<SaleOrderVO> getSaleOrders(List<Integer> productIds, Double pay);

    /**
     * 查询p30订金预定订单 2021-3-19新需求添加一个非必传的日期参数
     *
     * @param productIds 商品id
     * @param pay        订金金额
     * @return 订单
     */
    List<SaleOrderVO> getSaleOrdersV2(List<Integer> productIds, Double pay,String subDate);

    /**
     * 获取新机库存预设分组信息
     *
     * @param basketIds
     * @param level     1-是 2 不是
     * @param wxBind    1 绑定 0 未绑定
     * @return
     */
    List<SaleOrderKcGroupInfoVO> getSaleOrderKcGroupInfo(List<Integer> basketIds, Integer level, Integer wxBind);

    /**
     * 根据订单明细id获取库存预设
     *
     * @param basketId 订单明细id
     * @return 库存预设
     */
    InventoryPresetVO getInventoryPreset(Integer basketId);

    /**
     * 更换ppriceid
     *
     * @param userId   客户编号
     * @param basketId 订单明细id
     * @param ppriceid ppriceid
     * @param price    卖价
     */
    R<String> changeSubPpriceid(Integer userId, Integer basketId, Integer ppriceid, BigDecimal price);

    /**
     * 修改订单总金额
     *
     * @param subId 订单id
     */
    void updateOrderPrice(Integer subId);

    /**
     * 获取IM中的新机订单信息
     *
     * @param subIds 订单id列表
     * @param userId 会员id
     * @return 订单列表
     */
    List<ImOrderVO> getImNewOrders(List<Integer> subIds, Integer userId);

    /**
     * 获取IM中的售后订单信息
     *
     * @param subIds 订单id列表
     * @param userId 会员id
     * @return 订单列表
     */
    List<ImOrderVO> getImAfterServiceOrders(List<Integer> subIds, Integer userId);

    /**
     * 获取IM中的回收订单信息
     *
     * @param subIds 订单id列表
     * @param userId 会员id
     * @return 订单列表
     */
    List<ImOrderVO> getImRecoverOrders(List<Integer> subIds, Integer userId);

    /**
     * 获取IM中的良品订单信息
     *
     * @param subIds 订单id列表
     * @param userId 会员id
     * @return 订单列表
     */
    List<ImOrderVO> getImLiangPinOrders(List<Integer> subIds, Integer userId);

    /**
     * 根据 type、订单 ids 和 用户 id 查询订单商品信息
     *
     * @param type   type
     * @param subIds 订单 ids
     * @param userId 用户 id
     * @return List<ImOrderVO>
     */
    List<ImOrderVO> listOrderProductInfo(String type, List<Integer> subIds, Integer userId, Integer kind);

    /**
     * 根据传入号码和租户编号，品牌id，时间段，查询当前用户是否存在购买记录
     * @param whetherToBuyReq
     * @return
     */
    List<WhetherToBuyReq.Res> getWhetherToBuy(WhetherToBuyReq whetherToBuyReq);


    /**
     * 根据时间和会员id查询 对应的状态
     * @param myClientReq myClientReq
     * @param flag 表示 false为未完成 true为已完成
     * @return 客户编号
     */
    List<Integer> getMyClientComplete(MyClientReq myClientReq,Boolean flag);

    /**
     * 商品ID （commodityId），有购买完成的客户（根据时间筛选）
     * @param myClientReq myClientReq
     * @return 客户编号
     */
    List<Integer> getMyClientPurchaseComplete(MyClientReq myClientReq);

    /**
     * 筛选近X天【已完成】状态的订单中包含电子烟商品
     * @param myClientReq myClientReq
     * @return 客户编号
     */
    List<Integer> getMyClientIsCompleteByCigarette(MyClientReq myClientReq);

    /**
     * 根据mkcIdList查询库存位置
     * @param mkcIdList mkcIdList
     * @return
     */
    List<GoodProductStockVO> getGoodProductStockByMkcList(List<Integer> mkcIdList);


    /**
     * 查询是否为良品配件补贴
     * @param judgeGoodAccessoriesBO
     * @return
     */
    R<Boolean> judgeGoodAccessories(JudgeGoodAccessoriesBO judgeGoodAccessoriesBO);


    /**
     * 通过会员id查询退换机次数
     * @param memberId 会员id
     * @return
     */
    R<ExchangeCountVO> getExchangeCount(Integer memberId);

    /**
     * 通过订单id列表获取订单详情
     * @param orderIdList
     * @return
     */
    R<List<OrderStateRes>> getOrderStatusByList(List<Integer> orderIdList);

    /**
     * 根据basketId查询subId
     *
     * @param basketId 订单商品明细id
     * @return subId，若未查询到订单，返回0
     */
    R<Integer> getSubIdByBasketIdV1(Integer basketId);

    /**
     * 查询库存信息
     * @param req
     * @return
     */
    R<List<StockInfoResVO>> getOaStockTimelyByPpids(StockInfoReqVO req);

    R<List<WebStockInfoResVO>> getStockByPpids(WebStockInfoReqVO req);

    /**
     * ppid和收货门店查询调拨商品库存信息
     * @param req
     * @return
     */
    R<List<DiaoboStockInfoResVO>> getDiaoboByPpidAndToArea(DiaoboStockInfoReqVO req);

    /**
     * 查询商品成本
     * @param req
     * @return
     */
    R<List<WebStockPriceResVO>> getInPriceByPpid(WebStockPriceReqVO req);

    /**
     * 校验商品库存信息
     * @param req
     * @return
     */
    R<List<CheckStockInfoResVO>> checkProductStock(CheckStockInfoReqVO req);

    /**
     * 查询赠品库存
     * @param req
     * @return
     */
    R<List<GiftStockResVO>> getGiftStockByAreaIds(GiftStockReqVO req);

    R<List<StockInfoResVO>> getOaStockTimely(OaStockInfoReqVO req);

    R<OrderInfoRes> getOrderByWxNo(String outTradeNo);
}
