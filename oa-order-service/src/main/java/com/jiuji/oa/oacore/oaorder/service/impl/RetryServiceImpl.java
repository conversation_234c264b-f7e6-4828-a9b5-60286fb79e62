package com.jiuji.oa.oacore.oaorder.service.impl;

import com.doudian.open.exception.DoudianOpException;
import com.jiuji.oa.oacore.oaorder.service.RetryService;
import com.netflix.client.ClientException;
import com.sankuai.meituan.shangou.open.sdk.exception.SgOpenException;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.io.IOException;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2023/1/29 17:57
 */
@Slf4j
@Service
public class RetryServiceImpl implements RetryService {
    @Override
    @Retryable(value = {RetryableException.class, ClientException.class},maxAttempts = 5,backoff = @Backoff(delay = 500,multiplier = 1.5))
    public <T> T retryByFeignRetryableException(Supplier<T> retryFun) {
        try {
            return retryFun.get();
        } catch (Exception e) {
            log.warn("feign重试方法发生异常");
            throw e;
        }
    }

    @Override
    @Retryable(value = {IOException.class, DoudianOpException.class},maxAttempts = 5,backoff = @Backoff(delay = 500,multiplier = 1.5))
    public <T> T retryByMeituanException(RetryMeituanFun<T> retryFun) throws SgOpenException, IOException{
        try {
            return retryFun.get();
        } catch (Exception e) {
            log.warn("feign重试方法发生异常");
            throw e;
        }
    }

    @Override
    @Retryable(value = {HttpClientErrorException.BadRequest.class},maxAttempts = 5,backoff = @Backoff(delay = 500,multiplier = 1.5))
    public <T> T retrySaveException(Supplier<T> retryFun) {
        try {
            return retryFun.get();
        } catch (Exception e) {
            log.warn("feign重试方法发生异常");
            throw e;
        }
    }
}
