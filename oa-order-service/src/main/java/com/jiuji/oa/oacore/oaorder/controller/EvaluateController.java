package com.jiuji.oa.oacore.oaorder.controller;

import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.group.Default;
import com.jiuji.oa.oacore.common.res.PageRes;
import com.jiuji.oa.oacore.common.util.ValidatorUtil;
import com.jiuji.oa.oacore.oaorder.po.EvaluateDepart;
import com.jiuji.oa.oacore.oaorder.req.EvaluateReq;
import com.jiuji.oa.oacore.oaorder.res.*;
import com.jiuji.oa.oacore.oaorder.service.EvaluateService;
import com.jiuji.oa.oacore.oaorder.vo.req.EvaluateVO;
import com.jiuji.oa.oacore.tousu.res.EvaluateOrderInfoRes;
import com.jiuji.oa.oacore.tousu.res.EvaluateRewardInfo;
import com.jiuji.oa.oacore.tousu.res.HighOpinionRes;
import com.jiuji.tc.common.vo.R;
import com.jiuji.wcf.wcfclient.csharp.gen.member;
import com.jiuji.wcf.wcfclient.csharp.gen.oaenum.EvaluateJob;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-31
 */
@RestController
@RequestMapping("/inApi/oaorder/evaluate")
public class EvaluateController {
    @Autowired
    private EvaluateService evaluateService;

    @Autowired
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;

    /**
     * 售后和回收单评价
     *
     * @param current
     * @param size
     * @param type    3售后，15回收
     * @return
     * @see com.jiuji.oa.oacore.oaorder.EvaluateCloud#getEvaluatePages(Integer, Integer, Integer)
     */
    @GetMapping("/getEvaluatePages")
    public R<PageRes<EvaluateInfoRes>> getEvaluatePages(@RequestParam(value = "current") Integer current,
                                                        @RequestParam(value = "size") Integer size,
                                                        @RequestParam(value = "type") Integer type,
                                                        @RequestParam(value = "authId", required = false) Integer authId) {
        return R.success(evaluateService.getEvaluatePages(current, size, type, authId));
    }

    /**
     * 售后和回收单评价
     *
     * @param current
     * @param size
     * @param type    3售后，15回收
     * @return
     * @see com.jiuji.oa.oacore.oaorder.EvaluateCloud#getEvaluatePages(Integer, Integer, Integer)
     */
    @GetMapping("/getEvaluatePages/V2")
    public R<PageRes<EvaluateInfoRes>> getEvaluatePagesV2(@RequestParam(value = "current") Integer current,
                                                          @RequestParam(value = "size") Integer size,
                                                          @RequestParam(value = "type") Integer type,
                                                          @RequestParam(value = "authId", required = false) Integer authId) {
        return R.success(evaluateService.getEvaluatePages(current, size, type, authId));
    }

    /**
     * 服务评价统计接口
     *
     * @return
     * @see com.jiuji.oa.oacore.oaorder.EvaluateCloud#evaluateResult(String, Integer, Integer, Integer, Integer, Integer, Integer)
     */
    @PostMapping("/evaluateResult")
    public R<EvaluateResultBO> evaluateResult(
            @RequestParam(value = "showType") String showType,
            @RequestParam(value = "ch999_id") Integer ch999_id,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "rows", defaultValue = "10") Integer rows,
            @RequestParam(value = "userid") Integer userid,
            @RequestParam(value = "job") Integer job,
            @RequestHeader(value = "xtenant") Integer xtenant
    ) {
        member.EvaluateConn conn = new member.EvaluateConn();
        EvaluateJob evaluateJob = EvaluateJob.forValue(job);
        conn.setCh999Id(ch999_id);
        conn.setJob(null == evaluateJob ? EvaluateJob.None : evaluateJob);
        conn.setPage(page);
        conn.setRows(rows);
        conn.setShowType(showType);
        conn.setUserId(userid);
        member.EvaluateResult evaluateResult = this.evaluateService.evaluateResult(conn, xtenant);
        if (Objects.equals(null, evaluateResult.getInfo().getResult())) {
            //可用MapStruct改进
            EvaluateResultBO bo = new EvaluateResultBO();
            BeanUtils.copyProperties(evaluateResult.getItem(), bo);
            return R.success(bo);
        } else{
            return R.error(evaluateResult.getInfo().getResult());
        }
    }

    /**
     * 获取售后统计
     *
     * @return 售后统计
     */
    @GetMapping("/getShouhouEvaluate")
    public R<EvaluateShouhouBO> getShouhouEvaluate() {
        return R.success("ok", this.evaluateService.getShouhouEvaluate());
    }

    /**
     *  翻译C#中的 AddEvaluateNew接口
     * @param info
     * @return
     */
    @PostMapping("/addEvaluateNew")
    public R<ResultModelBO> addEvaluateNew(@RequestBody EvaluateVO info) {
        return R.success(evaluateService.addEvaluateNew(info,info.getType(),info.getIsApp(), info.getSourceFroms(), info.getVersion()));
    }

    /**
     * 评价保存
     * @param evaluateReq 保存参数类
     * @return R
     */
    @PostMapping("/saveEvaluate")
    public R<EvaluateRes> saveEvaluate(@RequestBody @Validated EvaluateReq evaluateReq){
        if (evaluateReq.getContent()!=null&&evaluateReq.getContent().length()>EvaluateReq.CONTENTLENTH){
            return R.error("评价内容不能大于1000");
        }
        return R.success(evaluateService.saveEvaluate(evaluateReq));
    }

    @PostMapping("/addEvaluateDepart")
    @ApiOperation(value = "添加评价责任划分——门店,部门", httpMethod = "POST")
    public R addTouSuDepart(@RequestBody EvaluateDepart evaluateDepart) {
        ValidatorUtil.validateEntity(evaluateDepart, Default.class);
        evaluateService.addEvaluateDepart(evaluateDepart);
        return R.success(null);
    }

    @PostMapping("/modifyEvaluateDepart")
    @ApiOperation(value = "修改责任划分——门店,部门", httpMethod = "POST")
    public R<String> modifyEvaluateDepart(@RequestBody EvaluateDepart evaluateDepart) {
        ValidatorUtil.validateEntity(evaluateDepart, Default.class);
        evaluateService.modifyEvaluateDepart(evaluateDepart);
        return R.success(null);
    }

    @PostMapping("/deleteEvaluateDepart")
    @ApiOperation(value = "删除责任划分——门店,部门", httpMethod = "POST")
    public R<String> deleteEvaluateDepart(@RequestBody EvaluateDepart evaluateDepart) {
        ValidatorUtil.validateEntity(evaluateDepart, Default.class);
        evaluateService.deleteEvaluateDepart(evaluateDepart);
        return R.success(null);
    }

    @GetMapping("/getEvaluateEndInfo")
    @ApiOperation(value = "评价结果信息", httpMethod = "GET")
    public R getEvaluateEndInfo(@RequestParam Long id) {
        return R.success(evaluateService.getEvaluateEndInfo(id));
    }


    @GetMapping("/getOrderHighOpinion")
    public R<HighOpinionRes> getOrderHighOpinion(@RequestParam Integer memberId,
                                                 @RequestParam(required = false) Long subId,
                                                 @RequestParam(required = false) String type,
                                                 @RequestParam(required = false) Integer staffId) {
        return evaluateService.getOrderHighOpinion(memberId, subId, type, false);
    }

    @GetMapping("/getOrderHighOpinion/v2")
    public R<HighOpinionRes> getOrderHighOpinionV2(@RequestParam Integer memberId,
                                                   @RequestParam(required = false) Long subId,
                                                   @RequestParam(required = false) String type,
                                                   @RequestParam(required = false) Integer staffId,
                                                   @RequestParam(required = false, value = "ignoreEvaluated", defaultValue = "false") Boolean ignoreEvaluated) {
        return evaluateService.getOrderHighOpinion(memberId, subId, type, ignoreEvaluated);
    }

    /**
     * 打赏信息 c端专用 -- 废弃
     * @param memberId
     * @return
     */
    @GetMapping("/getOrderHighOpinionToC/v1")
    public R<HighOpinionRes> getOrderHighOpinionToC(@RequestParam(value = "memberId") Integer memberId,
                                                   @RequestParam(value = "ch999Id") Integer ch999Id) {
        return evaluateService.getOrderHighOpinionToC(memberId, ch999Id);
    }
    /**
     * 打赏信息 c端专用
     * @param memberId
     * @return
     */
    @GetMapping("/getOrderHighOpinionToC/v2")
    public R<HighOpinionRes> getOrderHighOpinionToCV2(@RequestParam Integer memberId,
                                                      @RequestParam(required = false) Long subId,
                                                      @RequestParam(required = false) String type,
                                                      @RequestParam(required = false) Integer ch999Id,
                                                      @RequestParam(required = false, value = "ignoreEvaluated", defaultValue = "false") Boolean ignoreEvaluated) {
        return evaluateService.getOrderHighOpinionToCV2(memberId, subId, type, ignoreEvaluated, ch999Id);
    }

    @PostMapping("/HighOpinionIgnore")
    public R<String> HighOpinionIgnore(@RequestParam Long subId, @RequestParam String type){
        return evaluateService.HighOpinionIgnore(subId,type);
    }


    /**
     * 获取员工打赏记录
     * @return
     */
    @GetMapping("/getStaffRewardList")
    public R<List<EvaluateRewardInfo>> getStaffRewardList(@RequestParam(required = false) Integer current,@RequestParam(required = false) Integer size, @RequestParam Integer staffId){
       // 暂不分页
        current = null;
        size = null;
        return evaluateService.getStaffRewardList(current,size,staffId);
    }
    /**
     * 获取员工打赏记录
     * @return
     */
    @GetMapping("/getStaffRewardSimpleMsg")
    public R<HighOpinionRes.StaffMessage> getStaffRewardSimpleMsg(@RequestParam Integer staffId){
        return evaluateService.getStaffRewardSimpleMsg(staffId);
    }

    @GetMapping("/getCompleteOrderInfoWithinHours/v1")
    public R<EvaluateOrderInfoRes> getCompleteOrderInfoWithinHours(@RequestParam("memberId") Integer memberId){
        return R.success(evaluateService.getCompleteOrderInfoWithinHours(memberId));
    }

    @GetMapping("/productScoreToEvaluateScore/v1")
    public R<Boolean> productScoreToEvaluateScore(@RequestParam("evaluateId") Integer evaluateId){
        return R.success(evaluateService.productScoreToEvaluateScore(evaluateId));
    }
}

