package com.jiuji.oa.oacore.thirdplatform.stock.service;


import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.oacore.common.constant.RedisKeyConstant;
import com.jiuji.oa.oacore.thirdplatform.common.ThirdPlatformCommonConst;
import com.jiuji.oa.oacore.thirdplatform.stock.entity.StockSync;

import java.util.List;

/**
 * 库存管理接口类
 *
 * <AUTHOR>
 */
public interface StockService extends IService<StockSync> {


    /**
     * 同步库存数据到美团
     * @return
     */
    Boolean syncToMeiTuan();


    /**
     * 重构美团同步库存接口
     * @return
     * @param param
     */
    Boolean syncToMeiTuanV1(Dict param);

    static String getSyncCountMapKey(String platCode, String storeCode) {
        if(ThirdPlatformCommonConst.THIRD_PLAT_MT.equals(platCode)){
            return StrUtil.format(RedisKeyConstant.SYNC_MEITUAN_PRODUCT_COUNT, "Meituan", storeCode);
        }
        return StrUtil.format(RedisKeyConstant.SYNC_MEITUAN_PRODUCT_COUNT, platCode, storeCode);
    }

    /**
     * 同步所有美团管理库存门店
     * @param area
     * @return
     */
    Boolean syncStore(List<String> area);

    /**
     * 美团库存功能同步
     * @param platCode
     * @param syncTime
     * @param syncType all big small
     */
    void syncToMtStock (String platCode, Integer syncTime, String syncType);

}
