package com.jiuji.oa.oacore.oaorder.cloud.service.fallback;

import com.jiuji.oa.oacore.oaorder.cloud.service.AttachmentsCloud;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @author: gengjiaping
 * @date: 2019/11/20
 */
@Component("aorderAttachmentsCloudFallback")
@Slf4j
@Deprecated
public class AttachmentsCloudFallback implements AttachmentsCloud {

    @Override
    public R addAttachments(String files, Integer linkedId, Integer type, Integer kind, String userName, Integer userId) {
        return R.error("远程调用/ncSegments/api/attachments/addAttachments失败");
    }
}
