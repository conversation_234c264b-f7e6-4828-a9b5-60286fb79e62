package com.jiuji.oa.oacore.assistantOrderInfo.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.jiuji.oa.oacore.assistantOrderInfo.enums.*;
import com.jiuji.oa.oacore.assistantOrderInfo.service.AssistantOrderInfoService;
import com.jiuji.oa.oacore.assistantOrderInfo.vo.AssistantOrderInfoVO;
import com.jiuji.oa.oacore.assistantOrderInfo.vo.QueryCriteriaVO;
import com.jiuji.oa.oacore.assistantOrderInfo.vo.RecentOrderInfoVO;
import com.jiuji.oa.oacore.common.enums.OaMesTypeEnum;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.oaorder.bo.ProductSimpleBO;
import com.jiuji.oa.oacore.oaorder.enums.SubCheckEnum;
import com.jiuji.oa.oacore.oaorder.po.*;
import com.jiuji.oa.oacore.oaorder.service.*;
import com.jiuji.oa.oacore.thirdplatform.order.entity.AreaInfo;
import com.jiuji.oa.oacore.thirdplatform.order.service.IAreaInfoService;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.oacore.weborder.res.RecoverBasketRes;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.constants.NumberConstant;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AssistantOrderInfoServiceImpl implements AssistantOrderInfoService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private SubService subService;
    @Resource
    private BasketService basketService;
    @Resource
    private ProductinfoService productinfoService;
    @Resource
    private ShouhouService shouhouService;
    @Resource
    private SmallproService smallproService;
    @Resource
    private SmallproBillService smallproBillService;
    @Resource
    private RecoverSubService recoverSubService;
    @Resource
    private RecoverBasketService recoverBasketService;
    @Resource
    private RecoverMarketinfoService marketinfoService;
    @Resource
    private RecoverMarketsubinfoService marketsubinfoService;
    @Resource
    private SmsService smsService;
    @Resource
    private IAreaInfoService areaInfoService;

    private static final String kind = "oaSearch";


    /**
     * 策略查询
     * @return
     */
    private void strategyQuery(QueryCriteriaVO req) {
        Integer orderType = Optional.ofNullable(req.getOrderType()).orElse(Integer.MIN_VALUE);
        if ( orderType.equals(AssistantOrderEnum.SALES_ORDER.getCode())) {
            selectSalesOrder(req);
        } else if (orderType.equals(AssistantOrderEnum.AFTER_ORDER.getCode())) {
            selectAfterOrder(req);
        } else if (orderType.equals(AssistantOrderEnum.RECOVERY_ORDER.getCode())) {
            selectRecoveryOrder(req);
        } else if (orderType.equals(AssistantOrderEnum.SECOND_ORDER.getCode())) {
            selectSecondOrder(req);
        } else if(orderType.equals(AssistantOrderEnum.SMALLPRO_ORDER.getCode())){
            selectSmallproOrder(req);
        } else {
            CompletableFuture<Void> futureSales = CompletableFuture.runAsync(() -> selectSalesOrder(req));
            CompletableFuture<Void> futureAfter = CompletableFuture.runAsync(() -> selectAfterOrder(req));
            CompletableFuture<Void> futureSmallpro = CompletableFuture.runAsync(() -> selectSmallproOrder(req));
            CompletableFuture<Void> futureRecovery = CompletableFuture.runAsync(() -> selectRecoveryOrder(req));
            CompletableFuture<Void> futureSecond = CompletableFuture.runAsync(() -> selectSecondOrder(req));
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureSales, futureAfter, futureSmallpro, futureRecovery, futureSecond);
            try {
                allFutures.get();
                //所有订单整合之后进行按照时间进行排序
                List<AssistantOrderInfoVO> assistantOrderInfoList = req.getAssistantOrderInfoList();
                if(CollectionUtil.isNotEmpty(assistantOrderInfoList)){
                    List<AssistantOrderInfoVO> collect = assistantOrderInfoList
                            .stream()
                            .sorted(Comparator.comparing(AssistantOrderInfoVO::getOrderTime, Comparator.nullsFirst(Comparator.reverseOrder())))
                            .collect(Collectors.toList());
                    req.setAssistantOrderInfoList(collect);
                }
            } catch (Exception e) {
                String message = String.format("app订单查询异常，传入参数：%s", req.toString());
                smsService.sendOaMsgTo9Ji(message + "数据转换异常", "13495", OaMesTypeEnum.YCTZ.getCode().toString());
                log.error(message + "数据转换异常", e);
            }
        }
    }




    @Override
    public R<List<AssistantOrderInfoVO>> selectAssistantOrderInfo(QueryCriteriaVO req)  {
        String key = req.getKey();
        List<AssistantOrderInfoVO> assistantOrderInfoList = new ArrayList<>();
        req.setAssistantOrderInfoList(assistantOrderInfoList);
        if(StringUtil.isEmpty(key)){
            List<RecentOrderInfoVO> recentOrders = getRecentOrders(req);
            recentOrders.forEach(item->{
                req.setKey(Optional.ofNullable(item.getOrderNo()).orElse(Long.MIN_VALUE).toString())
                        .setOrderType(item.getOrderType())
                        .setIsOrderNo(Boolean.TRUE);
                strategyQuery(req);
            });
        } else {
            if(!NumberUtil.isNumber(key)){
                throw new CustomizeException("查询条件应该都为数字");
            }
            req.setIsOrderNo(Long.parseLong(key)<Integer.MAX_VALUE);
            strategyQuery(req);
        }
        return R.success(assistantOrderInfoList);
    }


    /**
     * 获取近期订单
     * @return
     */
    private List<RecentOrderInfoVO> getRecentOrders(QueryCriteriaVO req){
        //获取后10个元素
        Set<String> range = stringRedisTemplate.opsForZSet().range(kind + req.getUserId(), -10, -1);
        List<RecentOrderInfoVO> collect = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(range)){
             collect = range.stream().map(item -> {
                String[] split = item.split("\\|");
                RecentOrderInfoVO recentOrderInfoVO = new RecentOrderInfoVO();
                recentOrderInfoVO.setOrderNo(Long.parseLong(Optional.ofNullable(split[0]).orElse("0")));
                recentOrderInfoVO.setOrderType(Integer.parseInt(Optional.ofNullable(split[1]).orElse("0")));
                return recentOrderInfoVO;
            }).collect(Collectors.toList());
        }
        Collections.reverse(collect);
        return collect;
    }

    /**
     * 销售单查询
     * @param req
     * @return
     */
    private void selectSalesOrder(QueryCriteriaVO req){
        String key = req.getKey();
        List<Sub> list = subService.lambdaQuery().eq(req.getIsOrderNo(),Sub::getSubId, key).or().eq(Sub::getSubMobile, key).list();
        List<AssistantOrderInfoVO> assistantOrderInfoList = req.getAssistantOrderInfoList();
        if(CollectionUtil.isNotEmpty(list)){
            //封装查询areainfo 避免循环查询
            Map<Integer, AreaInfo> areaMap = areaInfoService.getAreaMap(list.stream().map(Sub::getAreaid).collect(Collectors.toList()));
            //封装查询basket 避免循环查询
            List<Long> subIdList = list.stream().map(sub -> sub.getSubId().longValue()).collect(Collectors.toList());
            Map<Long, List<Basket>> basketMap = basketService.getBasketMap(subIdList);
            //封装查询productInfo 避免循环查询
            Map<Integer, ProductSimpleBO> productInfoMap = getProductInfoMap(basketMap);
            list.forEach(item->{
                //订单信息进行封装
                AssistantOrderInfoVO assistantOrderInfoVO = new AssistantOrderInfoVO();
                Optional.ofNullable(item.getSubId()).ifPresent(subId->assistantOrderInfoVO.setOrderNo(Long.parseLong(subId.toString())));
                assistantOrderInfoVO.setOrderType(AssistantOrderEnum.SALES_ORDER.getMessage())
                        .setOrderTypeCode(AssistantOrderEnum.SALES_ORDER.getOrderType())
                        .setOrderState(SubCheckEnum.valueOfByCode(item.getSubCheck()))
                        .setConsignee(item.getSubTo())
                        .setConsigneeName("收货人")
                        .setUserId(item.getUserid())
                        .setConsigneePhoneNumber(item.getSubMobile())
                        .setOrderTime(item.getSubDate());
                //门店信息转换以及设置
                Optional.ofNullable(item.getAreaid()).ifPresent(obj->{
                    AreaInfo areaInfo = Optional.ofNullable(areaMap.get(obj)).orElse(new AreaInfo());
                    assistantOrderInfoVO.setAreaCode(areaInfo.getArea());
                });
                List<Basket> baskets = basketMap.get(item.getSubId().longValue());
                if(CollectionUtil.isNotEmpty(baskets)){
                    //首先进行大件商品的获取
                    Basket basket = getShowBasket(baskets);
                    Optional.ofNullable(productInfoMap.get(Optional.ofNullable(basket.getPpriceid()).orElse(0L).intValue())).ifPresent(v-> assistantOrderInfoVO.setProductName(v.getProductName())
                            .setProductColor(v.getProductColor()));
                }
                assistantOrderInfoList.add(assistantOrderInfoVO);
            });
        }
    }

    /**
     * 获取商品map
     * @param basketMap
     * @return
     */
    private Map<Integer, ProductSimpleBO> getProductInfoMap(Map<Long, List<Basket>> basketMap){
        List<Integer> ppids = basketMap.values().stream().map(this::getShowBasket)
                .map(Basket::getPpriceid)
                .filter(Objects::nonNull)
                .map(Long::intValue)
                .distinct()
                .collect(Collectors.toList());
        return productinfoService.getProductMapByPpidsNew(ppids);
    }

    /**
     * 获取展示basket
     * @param baskets
     * @return
     */
    private Basket getShowBasket (List<Basket> baskets){
        return baskets.stream()
                .filter(Basket::getIsmobile)
                .findFirst()
                .orElseGet(() -> baskets.stream()
                        .filter(obj -> !NumberConstant.TWO.equals(obj.getType()))
                        .findFirst()
                        .orElse(baskets.get(0)));
    }

    /**
     * 售后单查询
     * @param req
     * @return
     */
    private void selectAfterOrder(QueryCriteriaVO req){
        List<Shouhou> list = shouhouService.lambdaQuery().eq(req.getIsOrderNo(),Shouhou::getId, req.getKey()).or().eq(Shouhou::getMobile, req.getKey()).list();
        List<AssistantOrderInfoVO> assistantOrderInfoList = req.getAssistantOrderInfoList();
        if(CollectionUtil.isNotEmpty(list)){
            //封装查询areainfo 避免循环查询
            Map<Integer, AreaInfo> areaMap = areaInfoService.getAreaMap(list.stream().map(Shouhou::getAreaid).collect(Collectors.toList()));
            list.forEach(item->{
                //订单信息进行封装
                String orderState = Optional.ofNullable(item.getIsquji()).orElse(Boolean.FALSE)?"取机":WxStatusEnum.getMessageByCode(item.getStats());
                AssistantOrderInfoVO assistantOrderInfoVO = new AssistantOrderInfoVO();
                Optional.ofNullable(item.getId()).ifPresent(subId->assistantOrderInfoVO.setOrderNo(Long.parseLong(subId.toString())));
                assistantOrderInfoVO.setOrderType(AssistantOrderEnum.AFTER_ORDER.getMessage())
                        .setOrderTypeCode(AssistantOrderEnum.AFTER_ORDER.getOrderType())
                        .setOrderState(orderState)
                        .setUserId(item.getUserid())
                        .setConsignee(item.getUsername())
                        .setConsigneeName("送修人")
                        .setConsigneePhoneNumber(item.getMobile())
                        .setProductName(item.getName())
                        .setProductColor(item.getProductColor())
                        .setOrderTime(item.getModidate());
                //门店信息转换以及设置
                Optional.ofNullable(item.getAreaid()).ifPresent(obj->{
                    AreaInfo areaInfo = Optional.ofNullable(areaMap.get(obj)).orElse(new AreaInfo());
                    assistantOrderInfoVO.setAreaCode(areaInfo.getArea());
                });
                assistantOrderInfoList.add(assistantOrderInfoVO);
            });
        }
    }

    private Map<Integer, ProductSimpleBO> getProductInfoSmallproMap(Map<Integer, List<SmallproBill>> smallproBillMap){
        if(ObjectUtil.isNull(smallproBillMap)){
            return new HashMap<>();
        }
        List<Integer> ppids = new ArrayList<>();
        smallproBillMap.forEach((k,v)-> ppids.addAll(v.stream().map(SmallproBill::getPpriceid)
                .filter(Objects::nonNull)
                .map(Long::intValue)
                .collect(Collectors.toList())));
        return productinfoService.getProductMapByPpidsNew(ppids);
    }

    /**
     * 小件单查询
     * @param req
     * @return
     */
    private void selectSmallproOrder(QueryCriteriaVO req){
        List<Smallpro> list = smallproService.lambdaQuery().eq(req.getIsOrderNo(),Smallpro::getId, req.getKey()).or().eq(Smallpro::getMobile, req.getKey()).list();
        List<AssistantOrderInfoVO> assistantOrderInfoList = req.getAssistantOrderInfoList();
        if(CollectionUtil.isNotEmpty(list)){
            //封装查询areainfo 避免循环查询
            Map<Integer, AreaInfo> areaMap = areaInfoService.getAreaMap(list.stream().map(Smallpro::getAreaid).collect(Collectors.toList()));
            //封装查询SmallproBill 避免循环查询
            Map<Integer, List<SmallproBill>> smallproBillMap = smallproBillService.lambdaQuery()
                    .in(SmallproBill::getSmallproID, list.stream().map(Smallpro::getId).collect(Collectors.toList()))
                    .orderByAsc(SmallproBill::getId)
                    .list().stream().collect(Collectors.groupingBy(SmallproBill::getSmallproID));
            //封装productinfo 避免循环查询
            Map<Integer, ProductSimpleBO> productInfoSmallproMap = getProductInfoSmallproMap(smallproBillMap);
            list.forEach(item->{
                //订单信息进行封装
                AssistantOrderInfoVO assistantOrderInfoVO = new AssistantOrderInfoVO();
                Optional.ofNullable(item.getId()).ifPresent(subId->assistantOrderInfoVO.setOrderNo(Long.parseLong(subId.toString())));
                assistantOrderInfoVO.setOrderType(AssistantOrderEnum.SMALLPRO_ORDER.getMessage())
                        .setOrderTypeCode(AssistantOrderEnum.SMALLPRO_ORDER.getOrderType())
                        .setOrderState(SmallProStatsEnum.getMessageByCode(item.getStats()))
                        .setConsignee(item.getUsername())
                        .setConsigneeName("送修人")
                        .setConsigneePhoneNumber(item.getMobile())
                        .setOrderTime(item.getIndate());
                Optional.ofNullable(item.getUserid()).ifPresent(item01->assistantOrderInfoVO.setUserId(item01.longValue()));

                //门店信息转换以及设置
                Optional.ofNullable(item.getAreaid()).ifPresent(obj->{
                    AreaInfo areaInfo = Optional.ofNullable(areaMap.get(obj)).orElse(new AreaInfo());
                    assistantOrderInfoVO.setAreaCode(areaInfo.getArea());
                });
                List<SmallproBill> smallproBillList = smallproBillMap.get(item.getId());
                if(CollectionUtil.isNotEmpty(smallproBillList)){
                    SmallproBill smallproBill = smallproBillList.get(0);
                    ProductSimpleBO productSimpleBO = productInfoSmallproMap.get(Optional.ofNullable(smallproBill.getPpriceid()).orElse(0L).intValue());
                    assistantOrderInfoVO.setProductName(productSimpleBO.getProductName()).setProductColor(productSimpleBO.getProductColor());
                }
                assistantOrderInfoList.add(assistantOrderInfoVO);
            });
        }
    }

    /**
     * 回收单查询
     * @param req
     * @return
     */
    private void selectRecoveryOrder(QueryCriteriaVO req){
        List<RecoverSub> list = recoverSubService.lambdaQuery().eq(req.getIsOrderNo(),RecoverSub::getSubId, req.getKey()).or().eq(RecoverSub::getSubTel, req.getKey()).list();
        List<AssistantOrderInfoVO> assistantOrderInfoList = req.getAssistantOrderInfoList();
        if(CollectionUtil.isNotEmpty(list)){
            //封装查询areainfo 避免循环查询
            Map<Integer, AreaInfo> areaMap = Optional.ofNullable(areaInfoService.getAreaMap(list.stream().map(RecoverSub::getAreaid).collect(Collectors.toList()))).orElse(new HashMap<>());
            //封装查询recoverBasket 避免循环查询
            Map<Integer, List<RecoverBasketRes>> recoverBasketMap = recoverBasketService.getBasketDta(list.stream().map(RecoverSub::getSubId)
                    .collect(Collectors.toList()))
                    .stream()
                    .collect(Collectors.groupingBy(RecoverBasketRes::getSubId));
            list.forEach(item->{
                //订单信息进行封装
                AssistantOrderInfoVO assistantOrderInfoVO = new AssistantOrderInfoVO();
                Optional.ofNullable(item.getSubId()).ifPresent(subId->assistantOrderInfoVO.setOrderNo(Long.parseLong(subId.toString())));
                assistantOrderInfoVO.setOrderType(AssistantOrderEnum.RECOVERY_ORDER.getMessage())
                        .setOrderTypeCode(AssistantOrderEnum.RECOVERY_ORDER.getOrderType())
                        .setOrderState(RecoverSubCheckEnum.getMessageByCode(item.getSubCheck()))
                        .setConsignee(item.getSubTo())
                        .setConsigneeName("联系人")
                        .setUserId(item.getUserid())
                        .setConsigneePhoneNumber(item.getSubTel())
                        .setOrderTime(item.getDtime());
                //门店信息转换以及设置
                Optional.ofNullable(item.getAreaid()).ifPresent(obj->{
                    AreaInfo areaInfo = Optional.ofNullable(areaMap.get(obj)).orElse(new AreaInfo());
                    assistantOrderInfoVO.setAreaCode(areaInfo.getArea());
                });
                List<RecoverBasketRes> recoverBasketList = recoverBasketMap.get(item.getSubId());
                if(CollectionUtil.isNotEmpty(recoverBasketList)){
                    //首先进行大件商品的获取
                    RecoverBasketRes recoverBasket = recoverBasketList.stream()
                            .filter(RecoverBasketRes::getIsmobile)
                            .findFirst()
                            .orElse(recoverBasketList.get(0));
                    assistantOrderInfoVO.setProductName(recoverBasket.getProductName())
                            .setProductColor(recoverBasket.getProductColor());
                }
                assistantOrderInfoList.add(assistantOrderInfoVO);
            });
        }

    }

    private Map<Integer, ProductSimpleBO> getProductInfoRecoverMarketinfoMap(Map<Long, List<RecoverMarketsubinfo>> recoverMarketsubinfoMap){
        List<Integer> ppids = new ArrayList<>();
        recoverMarketsubinfoMap.forEach((k,v)-> ppids.addAll(v.stream().map(RecoverMarketsubinfo::getPpriceid)
                .filter(Objects::nonNull)
                .collect(Collectors.toList())));
        return productinfoService.getProductMapByPpidsNew(ppids);
    }
    /**
     * 良品单查询
     * @param req
     * @return
     */
    private void selectSecondOrder(QueryCriteriaVO req){
        List<RecoverMarketinfo> list = marketinfoService.lambdaQuery().eq(req.getIsOrderNo(),RecoverMarketinfo::getSubId, req.getKey()).or().eq(RecoverMarketinfo::getSubMobile, req.getKey()).list();
        List<AssistantOrderInfoVO> assistantOrderInfoList = req.getAssistantOrderInfoList();
        if(CollectionUtil.isNotEmpty(list)){
            //封装查询areainfo 避免循环查询
            Map<Integer, AreaInfo> areaMap = areaInfoService.getAreaMap(list.stream().map(RecoverMarketinfo::getAreaid).collect(Collectors.toList()));
            //封装查询Marketsubinfos 避免循环查询
            Map<Long, List<RecoverMarketsubinfo>> recoverMarketsubinfoMap = Optional.ofNullable(marketsubinfoService.lambdaQuery()
                    .in(RecoverMarketsubinfo::getSubId, list.stream()
                            .map(RecoverMarketinfo::getSubId)
                            .collect(Collectors.toList()))
                    .list().stream().collect(Collectors.groupingBy(RecoverMarketsubinfo::getSubId)))
                    .orElse(new HashMap<>());
            //封装查询productInfoRecoverMarketinfoMap 避免循环查询
            Map<Integer, ProductSimpleBO> productInfoRecoverMarketinfoMap = getProductInfoRecoverMarketinfoMap(recoverMarketsubinfoMap);
            list.forEach(item->{
                //订单信息进行封装
                AssistantOrderInfoVO assistantOrderInfoVO = new AssistantOrderInfoVO();
                Optional.ofNullable(item.getSubId()).ifPresent(subId->assistantOrderInfoVO.setOrderNo(Long.parseLong(subId.toString())));
                assistantOrderInfoVO.setOrderType(AssistantOrderEnum.SECOND_ORDER.getMessage())
                        .setOrderTypeCode(AssistantOrderEnum.SECOND_ORDER.getOrderType())
                        .setOrderState(RecoverMarketSubCheckEnum.getMessageByCode(item.getSubCheck()))
                        .setConsignee(item.getSubTo())
                        .setConsigneeName("收货人")
                        .setUserId(item.getUserid())
                        .setConsigneePhoneNumber(item.getSubMobile())
                        .setOrderTime(item.getSubDate());

                //门店信息转换以及设置
                Optional.ofNullable(item.getAreaid()).ifPresent(obj->{
                    AreaInfo areaInfo = Optional.ofNullable(areaMap.get(obj)).orElse(new AreaInfo());
                    assistantOrderInfoVO.setAreaCode(areaInfo.getArea());
                });
                List<RecoverMarketsubinfo> recoverMarketsubinfos = recoverMarketsubinfoMap.get(item.getSubId());
                if(CollectionUtil.isNotEmpty(recoverMarketsubinfos)){
                    //首先进行大件商品的获取
                    RecoverMarketsubinfo marketsubinfo = recoverMarketsubinfos.stream()
                            .filter(RecoverMarketsubinfo::getIsmobile)
                            .findFirst()
                            .orElse(recoverMarketsubinfos.get(0));
                    ProductSimpleBO productSimpleBO = Optional.ofNullable(productInfoRecoverMarketinfoMap.get(marketsubinfo.getPpriceid())).orElse(new ProductSimpleBO());
                    assistantOrderInfoVO.setProductName(productSimpleBO.getProductName())
                            .setProductColor(productSimpleBO.getProductColor());
                }
                assistantOrderInfoList.add(assistantOrderInfoVO);
            });
        }
    }
}
