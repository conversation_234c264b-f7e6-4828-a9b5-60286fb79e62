package com.jiuji.oa.oacore.thirdplatform.store.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ch999.common.util.atlas.AtlasUtil;
import com.ch999.common.util.utils.XtenantJudgeUtil;
import com.doudian.open.api.shop_editStore.data.ShopEditStoreData;
import com.doudian.open.api.shop_editStore.param.OpenTime;
import com.doudian.open.api.shop_editStore.param.ShopEditStoreParam;
import com.doudian.open.api.shop_storeSuspend.data.ShopStoreSuspendData;
import com.doudian.open.api.shop_storeSuspend.param.ShopStoreSuspendParam;
import com.jiuji.cloud.org.service.AreaInfoCloud;
import com.jiuji.cloud.org.vo.response.AreaOpeningRes;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.enums.OaMesTypeEnum;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.common.util.ExcelUtils;
import com.jiuji.oa.oacore.goldseed.po.Ok3wQudao;
import com.jiuji.oa.oacore.goldseed.service.Ok3wQudaoService;
import com.jiuji.oa.oacore.thirdplatform.annotation.AddLogKind;
import com.jiuji.oa.oacore.thirdplatform.common.ThirdPlatformCommonConst;
import com.jiuji.oa.oacore.thirdplatform.common.enums.ChannelKindEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.LogTypeEnum;
import com.jiuji.oa.oacore.thirdplatform.common.enums.PlatfromEnum;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.DoudianFactory;
import com.jiuji.oa.oacore.thirdplatform.doudian.common.factory.MyAccessToken;
import com.jiuji.oa.oacore.thirdplatform.doudian.service.DefaultDouDianService;
import com.jiuji.oa.oacore.thirdplatform.oplog.po.MeituanJdWorkLog;
import com.jiuji.oa.oacore.thirdplatform.oplog.service.MeituanJdWorkLogService;
import com.jiuji.oa.oacore.thirdplatform.order.service.IAreaInfoService;
import com.jiuji.oa.oacore.thirdplatform.order.vo.CreateSystem;
import com.jiuji.oa.oacore.thirdplatform.store.bo.StoreAddBO;
import com.jiuji.oa.oacore.thirdplatform.store.bo.StoreSearchBO;
import com.jiuji.oa.oacore.thirdplatform.store.entity.ChannelKindLink;
import com.jiuji.oa.oacore.thirdplatform.store.entity.JdStore;
import com.jiuji.oa.oacore.thirdplatform.store.entity.Store;
import com.jiuji.oa.oacore.thirdplatform.store.mapper.StoreMapper;
import com.jiuji.oa.oacore.thirdplatform.store.service.JdStoreService;
import com.jiuji.oa.oacore.thirdplatform.store.service.StoreService;
import com.jiuji.oa.oacore.thirdplatform.store.vo.CloseTheStoreVO;
import com.jiuji.oa.oacore.thirdplatform.store.vo.StoreVO;
import com.jiuji.oa.oacore.thirdplatform.taobao.service.TaoBaoService;
import com.jiuji.oa.oacore.thirdplatform.taobao.vo.TaobaoToken;
import com.jiuji.oa.oacore.thirdplatform.tenant.entity.Tenant;
import com.jiuji.oa.oacore.thirdplatform.tenant.service.TenantService;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import com.taobao.api.request.AlibabaAelophyShopUpdateinfoRequest;
import com.taobao.api.request.AlibabaAelophyShopUpdatestatusRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商户配置接口实现1
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@DS("smallpro_write")
public class StoreServiceImpl extends ServiceImpl<StoreMapper, Store> implements StoreService {

    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private MeituanJdWorkLogService meituanJdWorkLogService;
    @Resource
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Resource
    private TenantService tenantService;
    @Resource
    private Ok3wQudaoService qudaoService;
    @Resource
    private SmsService smsService;
    @Resource
    private IAreaInfoService areaInfoService;

    private void collectMsg(Map<String, StringJoiner> joinerJdMap,String tenantCode,String msg){
        StringJoiner stringJoiner = joinerJdMap.get(tenantCode);
        if(ObjectUtil.isNull(stringJoiner)){
            StringJoiner joiner = new StringJoiner(",");
            joiner.add(msg);
            joinerJdMap.put(tenantCode,joiner);
        } else {
            stringJoiner.add(msg);
        }
    }

    /**
     * 京东数据处理
     * @param joinerJdMap
     * @param jdStoreList
     * @param jdStoreListUpdate
     */
    private void handleJd(Map<String, StringJoiner> joinerJdMap,List<JdStore> jdStoreList,List<JdStore> jdStoreListUpdate,Integer closeAreaId,String closeArea){
        for (JdStore item:jdStoreList) {
            Integer areaId = item.getAreaId();
            //首先判断本地门店是否相同
            if(closeAreaId.equals(areaId)){
                item.setIsEnable(Boolean.FALSE);
                jdStoreListUpdate.add(item);
                String msg = "门店" + closeArea + "启用状态因为闭店所以修改为关闭";
                collectMsg(joinerJdMap,item.getTenantCode(),msg);
                continue;
            }
            //判断关联门店是否包含的关系
            String associatedStores = item.getAssociatedStores();
            if(StringUtils.isNotEmpty(associatedStores)){
                List<String> areaList = Arrays.asList(associatedStores.split(","));
                //判断该配置的关联门店不会空并且包含了闭店的门店
                if(CollectionUtils.isNotEmpty(areaList) && areaList.contains(closeArea)){
                    StringJoiner newAssociatedStores = new StringJoiner(",");
                    //过滤除没有闭店的门店
                    areaList.stream().filter(id->{
                        if(StringUtils.isNotEmpty(id) && !id.equals(closeArea)){
                            return Boolean.TRUE;
                        }
                        return Boolean.FALSE;
                    }).forEach(newAssociatedStores::add);
                    //把关联门店修改为没有闭店的门店集合
                    item.setAssociatedStores(newAssociatedStores.toString());
                    jdStoreListUpdate.add(item);
                    String msg = "门店" + closeArea + "因为闭店的操作所以关联门店由：" + associatedStores + "修改为：" + newAssociatedStores + "移除了门店：" + closeArea;
                    collectMsg(joinerJdMap,item.getTenantCode(),msg);
                }
            }
        }
    }


    /**
     * 京东数据处理
     * @param joinerMtMap
     * @param mtStorelist
     * @param mtStoreListUpdate
     */
    private void handleMt( Map<String, StringJoiner> joinerMtMap,List<Store> mtStorelist,List<Store> mtStoreListUpdate,Integer closeAreaId,String closeArea){
        for (Store item:mtStorelist) {
            Integer areaId = item.getAreaId();
            //首先判断本地门店是否相同
            if(closeAreaId.equals(areaId)){
                item.setEnable(Boolean.FALSE);
                mtStoreListUpdate.add(item);
                String msg = "门店" + closeArea + "启用状态因为闭店所以修改为关闭";
                collectMsg(joinerMtMap, item.getTenantCode(),msg);
                continue;
            }
            //判断关联门店是否包含的关系
            Optional.ofNullable(item.getAssociatedStores()).ifPresent(obj->{
                List<String> areaList = Arrays.asList(obj.split(","));
                //判断该配置的关联门店不会空并且包含了闭店的门店
                if(CollectionUtils.isNotEmpty(areaList) && areaList.contains(closeArea)){
                    StringJoiner newAssociatedStores = new StringJoiner(",");
                    //过滤除没有闭店的门店
                    areaList.stream().filter(id->{
                        if(StringUtils.isNotEmpty(id) && !id.equals(closeArea)){
                            return Boolean.TRUE;
                        }
                        return Boolean.FALSE;
                    }).forEach(newAssociatedStores::add);
                    //把关联门店修改为没有闭店的门店集合
                    item.setAssociatedStores(newAssociatedStores.toString());
                    mtStoreListUpdate.add(item);
                    String msg = "门店" + closeArea + "因为闭店的操作所以关联门店由：" + obj + "修改为：" + newAssociatedStores + "移除了门店：" + closeArea;
                    collectMsg(joinerMtMap, item.getTenantCode(),msg);
                }
            });
        }
    }

    /**
     * 闭店
     * @param closeTheStoreVO
     * @return
     */
    @Override
    public String closeTheStore(CloseTheStoreVO closeTheStoreVO)  {
        if(!XtenantJudgeUtil.isJiujiMore()){
            return "输出暂时不开放改功能";
        }
        String userName = abstractCurrentRequestComponent.getCurrentStaffId().getUserName();
        //闭店id
        Integer closeAreaId = closeTheStoreVO.getAreaId();
        String closeArea = closeTheStoreVO.getArea();
        //获取京东门店所有配置信息

        Map<String, StringJoiner> joinerJdMap = new HashMap<>();
        JdStoreService jdStoreService = SpringUtil.getBean(JdStoreService.class);
        List<JdStore> jdStoreList = jdStoreService.lambdaQuery().eq(JdStore::getIsEnable, Boolean.TRUE).list();
        List<JdStore> jdStoreListUpdate = new ArrayList<>();
        //京东处理
        CompletableFuture<Void> runAsyncJd = CompletableFuture.runAsync(() -> handleJd(joinerJdMap, jdStoreList, jdStoreListUpdate, closeAreaId, closeArea));
        //获取美团门店所有配置信息
        Map<String, StringJoiner> joinerMtMap = new HashMap<>();
        List<Store> mtStorelist = this.lambdaQuery().eq(Store::getIsEnable, Boolean.TRUE).eq(Store::getPlatCode, ThirdPlatformCommonConst.THIRD_PLAT_MT).list();
        List<Store> mtStoreListUpdate = new ArrayList<>();
        //美团处理
        CompletableFuture<Void> runAsyncMt = CompletableFuture.runAsync(() -> handleMt(joinerMtMap, mtStorelist, mtStoreListUpdate, closeAreaId, closeArea));
        CompletableFuture<Void> voidCompletableFuture = CompletableFuture.allOf(runAsyncJd, runAsyncMt).whenComplete((result, exception) -> {
            if (ObjectUtil.isNotNull(exception)) {
                log.error("美团京东闭店处理信息异常：" + exception);
            } else {
                //开启事务 切换写库
                MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
                    if (CollectionUtils.isNotEmpty(jdStoreListUpdate)) {
                        //京东 日志根据 tenantCode 进行分组记录
                        Map<String, List<JdStore>> logMap = jdStoreListUpdate.stream().collect(Collectors.groupingBy(JdStore::getTenantCode));
                        //京东日志修改
                        logMap.forEach((k, v) -> Optional.ofNullable(joinerJdMap.get(k)).ifPresent(stringJoiner -> {
                            MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.STORE_UPDATE.getMessage(), stringJoiner.toString(),
                                    userName, LogTypeEnum.STORE_ADD.getCode(), PlatfromEnum.JD.getCode(), k);
                            meituanJdWorkLogService.save(structure);
                        }));
                        //京东数据修改
                        jdStoreListUpdate.forEach(jdStoreService::updateById);
                    }
                    if (CollectionUtils.isNotEmpty(mtStoreListUpdate)) {
                        //美团日志根据 tenantCode 进行分组记录
                        Map<String, List<Store>> logMap = mtStoreListUpdate.stream().collect(Collectors.groupingBy(Store::getTenantCode));
                        //美团日志修改
                        logMap.forEach((k, v) -> Optional.ofNullable(joinerMtMap.get(k)).ifPresent(stringJoiner -> {
                            MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.STORE_UPDATE.getMessage(), stringJoiner.toString(),
                                    userName, LogTypeEnum.STORE_ADD.getCode(), PlatfromEnum.MT.getCode(), k);
                            meituanJdWorkLogService.save(structure);
                        }));
                        //美团数据修改
                        mtStoreListUpdate.forEach(this::updateById);
                    }
                }).commit();
            }
        });
        try {
            voidCompletableFuture.get();
        }catch (Exception e){
            String format = String.format("闭店执行异常传入参数：%s", JSONUtil.toJsonStr(closeTheStoreVO));
            smsService.sendOaMsgTo9Ji(format, "13495", OaMesTypeEnum.YCTZ.getCode().toString());
            log.error("闭店执行异常传入参数：{}",format,e);
            return format;
        }
        return "京东："+ JSONUtil.toJsonStr(joinerJdMap)+"美团："+JSONUtil.toJsonStr(joinerMtMap);
    }

    @Override
    public Store getOneStoreBy(String tenantCode, String platCode, Integer areaId, String storeCode) {
        QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_code", tenantCode);
        queryWrapper.eq("plat_code", platCode);
        if (null != areaId) {
            queryWrapper.eq("area_id", areaId);
        }
        if (null != storeCode) {
            queryWrapper.eq("store_code", storeCode);
        }
        List<Store> list = list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)){
            List<Integer> collect = list.stream().map(Store::getAreaId).collect(Collectors.toList());
            //没有重复元素返回null
            if (collect.isEmpty()){
                return null;
            }else {
                return list.get(0);
            }
        }
        return null;
    }

    /**
     *
     * @param storeAddBO
     */
    private void checkChannel(StoreAddBO storeAddBO){
        String platCode = storeAddBO.getPlatCode();
        //如果不是九机 或者不是小米 那就不进行以下的校验
        if(!XtenantEnum.isJiujiXtenant() || !ThirdPlatformCommonConst.THIRD_PLAT_MI.equals(platCode)){
            return;
        }
        List<StoreAddBO.StoreItemBO> itemList = storeAddBO.getItemList();
        if(CollectionUtils.isEmpty(itemList)){
            return;
        }
        List<Integer> channelIdList = itemList.stream().map(StoreAddBO.StoreItemBO::getLargeChannelId).filter(Objects::nonNull).collect(Collectors.toList());
        channelIdList.addAll(itemList.stream().map(StoreAddBO.StoreItemBO::getSmallChannelId).collect(Collectors.toList()));
        if(CollectionUtils.isEmpty(channelIdList)){
            return;
        }
        List<ChannelKindLink> channelKindByChannelIdList = this.baseMapper.getChannelKindByChannelIdList(channelIdList.stream().distinct().collect(Collectors.toList()));
        if(CollectionUtils.isEmpty(channelKindByChannelIdList)){
            throw new CustomizeException("供应商相关相关信息查询为空，供应商不存在或者不在合作中");
        }
        Map<Integer, List<ChannelKindLink>> map = channelKindByChannelIdList.stream().collect(Collectors.groupingBy(ChannelKindLink::getChannelId));
        itemList.forEach(item->{
            Integer largeChannelId = item.getLargeChannelId();
            if(ObjectUtil.isNotNull(largeChannelId)){
                List<ChannelKindLink> channelKindLinks = map.get(largeChannelId);
                if(CollectionUtils.isEmpty(channelKindLinks)){
                    throw new CustomizeException("供应商:"+largeChannelId+"不存在或不在合作中");
                }
                List<Integer> kindList = channelKindLinks.stream().map(ChannelKindLink::getKind).collect(Collectors.toList());
                if(!kindList.contains(ChannelKindEnum.MOBILE.getCode())){
                    throw new CustomizeException("供应商:"+largeChannelId+",类型:"+ ChannelKindEnum.MOBILE.getMessage()+"不存在或不在合作中");
                }
            }
            Integer smallChannelId = item.getSmallChannelId();
            if(ObjectUtil.isNotNull(smallChannelId)){
                List<ChannelKindLink> channelKindLinks = map.get(smallChannelId);
                if(CollectionUtils.isEmpty(channelKindLinks)){
                    throw new CustomizeException("供应商:"+smallChannelId+"不存在或不在合作中");
                }
                List<Integer> kindList = channelKindLinks.stream().map(ChannelKindLink::getKind).collect(Collectors.toList());
                if(!kindList.contains(ChannelKindEnum.MOBILE_ACCESSORIES.getCode())){
                    throw new CustomizeException("供应商:"+smallChannelId+",类型:"+ ChannelKindEnum.MOBILE_ACCESSORIES.getMessage()+"不存在或不在合作中");
                }
            }

        });
    }



    @Override
    public R saveData(StoreAddBO storeAddBO) {
        List<Store> list = new ArrayList<>();
        List<String> areaCodeList = new ArrayList<>();
        List<String> storeCodeList = new ArrayList<>();
        //数据校验
        checkChannel(storeAddBO);
        for (StoreAddBO.StoreItemBO item : storeAddBO.getItemList()) {
            if (StringUtils.isBlank(item.getAreaCode()) || StringUtils.isBlank(item.getStoreCode())) {
                continue;
            }
            //本次提交数据是否有重复,重复数据直接跳过，以第一条为准
            if(areaCodeList.contains(item.getAreaCode()) || storeCodeList.contains(item.getStoreCode())){
                continue;
            }
            areaCodeList.add(item.getAreaCode());
            storeCodeList.add(item.getStoreCode());
            //校验本地门店是否存在
            R<AreaInfo> localStore = areaInfoClient.getAreaInfoByArea(item.getAreaCode());
            if (null == localStore || null == localStore.getData()) {
                return R.error(String.format("本地门店[%s]错误，无法绑定平台门店[%s]", item.getAreaCode(), item.getStoreCode()));
            }
            Integer localAreaId = localStore.getData().getId();

            Store store = new Store();
            //2021-10-22逻辑，去掉一个本地门店只能对应一个平台门店的校验
            //数据库校验重复
            Store storeInDb = getOneStoreBy(storeAddBO.getTenantCode(), storeAddBO.getPlatCode(), localAreaId,  item.getStoreCode());
            if (null != storeInDb) {
                return R.error(String.format("绑定重复，本地门店[%s]已经绑定过平台门店[%s]", item.getAreaCode(), storeInDb.getStoreCode()));
            }
            store.setPlatCode(storeAddBO.getPlatCode());
            store.setTenantCode(storeAddBO.getTenantCode());
            store.setAreaId(localAreaId);
            store.setStoreCode(item.getStoreCode());
            store.setAssociatedStores(item.getAssociatedStores());
            store.setAssociatedStoresFlag(item.getAssociatedStoresFlag());
            store.setLargeChannelId(item.getLargeChannelId());
            store.setSmallChannelId(item.getSmallChannelId());
            store.setEnable(true);
            store.setDeliveryType(item.getDeliveryType());
            list.add(store);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            List<List<Store>> listList = ListUtils.partition(list, 100);
            for (List<Store> subList : listList) {
                List<Integer> collect = subList.stream().map(Store::getAreaId).collect(Collectors.toList());
                R<List<AreaInfo>> listR = areaInfoClient.listAreaInfo(collect);
                List<AreaInfo> areaInfos = Optional.ofNullable(listR).map(R::getData).orElse(new ArrayList<>());
                //添加日志操作
                Integer platFrom = PlatfromEnum.getCodeByName(storeAddBO.getPlatCode());
                MeituanJdWorkLog structure = meituanJdWorkLogService.structure(LogTypeEnum.STORE_ADD.getMessage(),
                        MessageFormat.format("平台门店编码：{0},本地门店编码：{1}", subList.stream().map(Store::getStoreCode).collect(Collectors.toList()).toString(), areaInfos.stream().map(AreaInfo::getArea).collect(Collectors.toList()).toString()),
                        abstractCurrentRequestComponent.getCurrentStaffId().getUserName(), LogTypeEnum.STORE_ADD.getCode(), platFrom,subList.stream().map(Store::getTenantCode).findFirst().orElse("未查询到商户"));
                //开启事务 切换写库
                MultipleTransaction.build().execute(DataSourceConstants.SMALLPRO_WRITE, () -> {
                    meituanJdWorkLogService.save(structure);
                    baseMapper.saveBatch(subList);
                }).commit();
            }
        }
        return R.success("操作成功");
    }


    @Override
    public Page<StoreVO> listByPage(StoreSearchBO search) {
        Page<StoreVO> page = new Page<>(search.getCurrent(), search.getSize());
        List<StoreVO> list = baseMapper.storeList(search, page);
        if (CollectionUtils.isNotEmpty(list)) {
            if(ThirdPlatformCommonConst.THIRD_PLAT_MI.equals(search.getPlatCode())){
                List<Integer> largeChannelIdList = list.stream().map(StoreVO::getLargeChannelId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
                List<Integer> smallChannelIdList = list.stream().map(StoreVO::getSmallChannelId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
                largeChannelIdList.addAll(smallChannelIdList);
                Map<Integer, Ok3wQudao> qudaoMap = qudaoService.selectChannelList(largeChannelIdList);
                list.forEach(item->{
                    Ok3wQudao LargeChannel = qudaoMap.getOrDefault(Optional.ofNullable(item.getLargeChannelId()).orElse(NumberConstant.ZERO), new Ok3wQudao());
                    Ok3wQudao smallChannel = qudaoMap.getOrDefault(Optional.ofNullable(item.getSmallChannelId()).orElse(NumberConstant.ZERO), new Ok3wQudao());
                    item.setLargeChannelName(LargeChannel.getCompanyJc());
                    item.setSmallChannelName(smallChannel.getCompanyJc());
                });

            }
            page.setRecords(list);
        }
        return page;
    }

    @Override
    public List<String> getSyncStoreCodeList() {
        return baseMapper.getSyncStoreCodeList();
    }
    @Override
    public List<String> getSyncStoreCodeByJiuJiList(String platCode) {
        //获取所有门店
        List<Store> stores = baseMapper.selectList(new LambdaQueryWrapper<Store>().eq(Store::getIsEnable, Boolean.TRUE).in(Store::getPlatCode, platCode));
        //过滤掉未开启的平台
        List<Tenant> tenants = tenantService.list(new LambdaQueryWrapper<Tenant>().eq(Tenant::getIsEnable, Boolean.TRUE).in(Tenant::getPlatCode, platCode));
        List<String> collect = stores.stream().map(Store::getTenantCode).filter(Objects::nonNull).collect(Collectors.toList());
        List<String> collect1 = tenants.stream().map(Tenant::getTenantCode).filter(Objects::nonNull).collect(Collectors.toList());
        //取交集
        Collection<String> intersection = CollUtil.intersection(collect, collect1);
        //取出 开启使用的平台的开启使用的门店
        return stores.stream().filter(storeCode -> CollUtil.contains(intersection, storeCode.getTenantCode())).map(Store::getStoreCode).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public Store getOneStoreByStoreCode(String platCode, String storeCode) {
        QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("plat_code", platCode);
        queryWrapper.eq("store_code", storeCode);
        queryWrapper.eq("is_enable", NumberConstant.ONE);
        List<Store> list = list(queryWrapper);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }

    @Override
    public Store getOneStoreByAreaId(String platCode, Integer areaId) {
        QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("plat_code", platCode);
        queryWrapper.eq("area_id", areaId);
        queryWrapper.eq("is_enable", NumberConstant.ONE);
        List<Store> list = list(queryWrapper);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }

    @Override
    public List<Store> getStoreByAreaIdList(String platCode, List<Integer> areaIdList) {
        List<Store> list = list(new LambdaQueryWrapper<Store>().eq(Store::getPlatCode, platCode).eq(Store::getIsEnable, Boolean.TRUE).in(Store::getAreaId,areaIdList));
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<Store> getOneStoreByStoreCodeV1(String platCode, List<String> storeCode) {
        List<Store> list = list(new LambdaQueryWrapper<Store>().eq(Store::getPlatCode, platCode).eq(Store::getIsEnable, Boolean.TRUE).in(Store::getStoreCode,storeCode));
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list;
    }


    @Override
    public List<String> getNearShops(String area , int distanceIner) {
        //获取到缓存里面的门店信息
        List<AreaInfo> data = areaInfoClient.listAll().getData();
        List<AreaInfo> result = new ArrayList<>();
        String position = null;
        for (AreaInfo datum : data) {
            if (Objects.equals(datum.getArea(),area)){
                //取出门店地址
                position = datum.getPosition();
            }
        }
        //过滤掉空的坐标
        if (StringUtils.isEmpty(position)) {
            return new ArrayList<>();
        }
        //过滤数据
        List<AreaInfo> collect = data.stream().filter(da ->
                StringUtils.isNotEmpty(da.getPosition())
                        && Objects.equals(da.getXtenant(), NumberConstant.ZERO)
                        && Objects.equals(da.getIsWeb(), Boolean.TRUE)
                        && Objects.equals(da.getKind1(), NumberConstant.ONE)
        ).collect(Collectors.toList());
        for (AreaInfo datum : collect) {
            //计算3km以内的门店
            double distance = AtlasUtil.getDistance(position,datum.getPosition());
            //过滤出5km内的门店
            if (distance <= distanceIner || distanceIner == -1) {
                //借用一个字段来临时存放距离
                datum.setKemu((long) distance);
                result.add(datum);
            }
        }
        if (CollUtil.isNotEmpty(result)){
            //根据距离进行排序 由近到远  (字段是借用的！)
            List<String> collect1 = result.stream().sorted(Comparator.comparing(AreaInfo::getKemu).thenComparing(AreaInfo::getRank)).map(AreaInfo::getArea).collect(Collectors.toList());
            collect1.removeIf(re -> Objects.equals(re,area));
            return collect1;
        }
        return new ArrayList<>();
    }

    @Override
    public R exportExcel(StoreSearchBO req, HttpServletResponse response) {
        req.setCurrent(NumberConstant.ONE);
        req.setSize(NumberConstant.FIVE_THOUSAND);
        Page<StoreVO> storeVOPage = listByPage(req);
        List<StoreVO> records = storeVOPage.getRecords();
        LinkedList<String> titles = new LinkedList<>(Arrays.asList("序号", "商户编码", "商户名称",
                "平台门店编码", "本地门店名称", "本地门店编码", "关联门店编码","状态"));
        if(Objects.equals(req.getPlatCode(),ThirdPlatformCommonConst.THIRD_PLAT_MI)){
            titles = new LinkedList<>(Arrays.asList("序号", "商户编码", "商户名称",
                    "平台门店编码", "本地门店名称", "本地门店编码", "默认大件供应商", "默认小件供应商","状态"));
        }
        List<List<Object>> results = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)){
            for (StoreVO record : records) {
                List<Object> export = new LinkedList<>();
                //序号
                export.add(record.getId());
                //商户编码
                export.add(record.getTenantCode());
                //商户名称
                export.add(record.getTenantName());
                //平台门店编码
                export.add(record.getStoreCode());
                //平台门店名称
                export.add(record.getAreaName());
                //本地门店编码
                export.add(record.getAreaCode());
                if(Objects.equals(req.getPlatCode(),ThirdPlatformCommonConst.THIRD_PLAT_MI)){
                    //默认大件供应商
                    export.add(record.getLargeChannelName());
                    //默认小件供应商
                    export.add(record.getSmallChannelName());
                } else {
                    //关联门店编码
                    export.add(record.getAssociatedStores());
                }
                //状态
                export.add(Optional.ofNullable(record.getIsEnable()).orElse(Boolean.FALSE)?"启用":"禁用");
                results.add(export);
            }
        }
        String fileName = "美团门店数据导出-"+ LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMddHHmm")) +".xls";
        if (Objects.equals(req.getPlatCode(),ThirdPlatformCommonConst.THIRD_PLAT_DY)){
            fileName = "抖店门店数据导出-"+ LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMddHHmm")) +".xls";
        } else if(Objects.equals(req.getPlatCode(),ThirdPlatformCommonConst.THIRD_PLAT_MI)){
            fileName = "小米门店数据导出-"+ LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMddHHmm")) +".xls";
        }
        try {
            ExcelUtils.export(response, results, titles, null,
                    fileName, 1);
        } catch (Exception e) {
            log.warn(String.valueOf(e));
            return R.error("导出失败" + e.getMessage());
        }
        return R.success("导出成功!");
    }


    @Override
    public void synchronizationAreaHourV1(String areaId) {
        List<String> platCodes = Arrays.asList(ThirdPlatformCommonConst.THIRD_PLAT_TB, ThirdPlatformCommonConst.THIRD_PLAT_DY);
        platCodes.forEach(platCode -> {
            if (StrUtil.isNotBlank(areaId)) {
                if ("all".equals(areaId)) {
                    // 同步所有门店
                    this.lambdaQuery()
                            .eq(Store::getPlatCode, platCode)
                            .eq(Store::getIsEnable, Boolean.TRUE)
                            .list()
                            .forEach(store -> synchronizationBusinessHoursV1(store, platCode));
                } else {
                    this.lambdaQuery()
                            .eq(Store::getPlatCode, platCode)
                            .eq(Store::getIsEnable, Boolean.TRUE)
                            .in(Store::getAreaId, CommonUtil.toIntList(areaId))
                            .list()
                            .forEach(store -> synchronizationBusinessHoursV1(store, platCode));
                }
            }
        });
    }

    /**
     * 根据opPoiId查询门店
     *
     * @param opPoiId
     * @param platCode
     * @return
     */
    @Override
    public Store getStoreByopPoiId(String tenantCode,String platCode,String opPoiId) {
        return this.baseMapper.selectStoreByopPoiId(tenantCode, platCode, opPoiId);
    }

    @Override
    @DS("smallpro_write")
    public void updateOpPoiId(Store store) {
        this.lambdaUpdate().set(Store::getOpPoiId, store.getOpPoiId()).eq(Store::getId, store.getId()).update();
    }

    /**
     * 根据 areaId 和 platCode 同步营业时间
     */
    private void synchronizationBusinessHoursV1(Store store, String platCode) {
        com.jiuji.oa.oacore.thirdplatform.order.entity.AreaInfo areaInfo = areaInfoService.selectAreaInfoById(String.valueOf(store.getAreaId()));
        if (areaInfo == null) {
            // 找不到区域信息
            return;
        }

        CreateSystem createSystem = new CreateSystem();
        createSystem.setStoreCode(store.getStoreCode())
                .setTenantCode(store.getTenantCode())
                .setStoreName(areaInfo.getAreaName());

        if (Boolean.FALSE.equals(areaInfo.getIsweb())) {
            synchronization(createSystem, platCode, false);
        } else {
            List<AreaOpeningRes.OpeningTime> hoursList = Optional.ofNullable(SpringUtil.getBean(AreaInfoCloud.class)
                            .getAreaOpeningTime(areaInfo.getId()))
                    .map(R::getData).map(AreaOpeningRes::getHoursList).orElse(null);
            setShippingTime(createSystem, hoursList);
            synchronization(createSystem, platCode, true);
        }
    }


    /**
     * 设置营业时间
     */
    private void setShippingTime(CreateSystem createSystem, List<AreaOpeningRes.OpeningTime> hoursList) {
        if (CollUtil.isEmpty(hoursList)) {
            return;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");

        LocalTime startTime = null;
        LocalTime endTime = null;

        Map<Long, String> dayTime = new HashMap<>();

        Map<Integer, AreaOpeningRes.OpeningTime> openingTimeMap = hoursList.stream().collect(Collectors.toMap(AreaOpeningRes.OpeningTime::getKey, Function.identity(), (v1, v2) -> v1));
        for (int i = 0; i < NumberConstant.SEVEN; i++) {
            Integer key = Convert.toInt(Math.pow(2, i));
            if (!openingTimeMap.containsKey(key)) {
                continue;
            }
            AreaOpeningRes.OpeningTime openingTime = openingTimeMap.get(key);
            // 检查日期是否有效
            LocalTime date1 = openingTime.getDate1();
            LocalTime date2 = openingTime.getDate2();

            if (date1 != null && date2 != null) {
                // 限制结束时间
                LocalTime endTimeVal = date2.isAfter(LocalTime.of(21, 30)) ? LocalTime.of(21, 30) : date2;
                try {
                    String timeRange = date1.format(formatter) + StrUtil.DASHED + endTimeVal.format(formatter);
                    dayTime.put((long) i + 1, timeRange);
                    if (endTime == null || endTime.isAfter(endTimeVal)) {
                        startTime = date1;
                        endTime = endTimeVal;
                    }
                } catch (NumberFormatException e) {
                    log.error("营业时间同步三方 日期格式转换失败, date1: {}, date2: {}, openingTime: {}", date1, date2, openingTime);
                }
            }
        }
        createSystem.setShippingTime(dayTime);
        if (startTime != null) {
            createSystem.setStartTime(startTime.format(formatter));
        }
        if (endTime != null) {
            createSystem.setEndTime(endTime.format(formatter));
        }
    }

    /**
     * 同步门店营业时间
     */
    private void synchronization(CreateSystem createSystem, String platCode, boolean isWeb) {
        MeituanJdWorkLog workLog = createWorkLog(platCode, createSystem);

        if (Objects.equals(platCode, ThirdPlatformCommonConst.THIRD_PLAT_TB)) {
            handleTaoBaoSynchronization(createSystem, isWeb, workLog);
        } else if (Objects.equals(platCode, ThirdPlatformCommonConst.THIRD_PLAT_DY)) {
            handleDouDianSynchronization(createSystem, isWeb, workLog);
        }

        meituanJdWorkLogService.saveByLog(workLog.getWorkName(), workLog.getWorkContent(),
                workLog.getCreateUser(), workLog.getLogType(),
                workLog.getPlatform(), workLog.getTenantCode());
    }

    /**
     * 创建工作日志对象
     */
    private MeituanJdWorkLog createWorkLog(String platCode, CreateSystem createSystem) {
        MeituanJdWorkLog workLog = new MeituanJdWorkLog();
        workLog.setPlatform(PlatfromEnum.getCodeByName(platCode));
        workLog.setLogType(LogTypeEnum.STORE_UPDATE.getCode());
        workLog.setWorkName(LogTypeEnum.STORE_UPDATE.getMessage());
        workLog.setTenantCode(createSystem.getTenantCode());
        workLog.setCreateUser("系统");
        workLog.setIsDel(Boolean.FALSE);
        return workLog;
    }

    /**
     * 处理淘宝平台的同步
     */
    private void handleTaoBaoSynchronization(CreateSystem createSystem, boolean isWeb, MeituanJdWorkLog workLog) {
        Tenant tenant = tenantService.getOneTenantBy("34766004", ThirdPlatformCommonConst.THIRD_PLAT_TB);
        TaobaoToken taobaoToken = SpringUtil.getBean(TaoBaoService.class).getTaobaoToken(tenant.getAppKey());
        if (isWeb) {
            // 更新营业时间
            AlibabaAelophyShopUpdateinfoRequest request = new AlibabaAelophyShopUpdateinfoRequest();
            request.setShopInfoUpdateRequest(createShopInfoUpdateRequest(createSystem));
            R<String> result = SpringUtil.getBean(TaoBaoService.class).updateInfo(request, taobaoToken);
            processResult(result, workLog, createSystem.getStoreName(), "营业时间同步成功", "营业时间同步失败");
        } else {
            // 更新门店状态为暂停
            AlibabaAelophyShopUpdatestatusRequest request = new AlibabaAelophyShopUpdatestatusRequest();
            request.setShopStatusUpdateRequest(createShopStatusUpdateRequest(createSystem));
            R<String> result = SpringUtil.getBean(TaoBaoService.class).updateStatus(request, taobaoToken);
            processResult(result, workLog, createSystem.getStoreName(), "暂停营业同步成功", "暂停营业同步失败");
        }
    }

    /**
     * 创建更新营业信息的请求
     */
    private AlibabaAelophyShopUpdateinfoRequest.ShopInfoUpdateRequest createShopInfoUpdateRequest(CreateSystem createSystem) {
        AlibabaAelophyShopUpdateinfoRequest.ShopInfoUpdateRequest request = new AlibabaAelophyShopUpdateinfoRequest.ShopInfoUpdateRequest();
        request.setStoreId(createSystem.getStoreCode());
        request.setStartTime(createSystem.getStartTime());
        request.setEndTime(createSystem.getEndTime());
        request.setChannelSourceType(4L);
        return request;
    }

    /**
     * 创建更新营业状态的请求
     */
    private AlibabaAelophyShopUpdatestatusRequest.ShopStatusUpdateRequest createShopStatusUpdateRequest(CreateSystem createSystem) {
        AlibabaAelophyShopUpdatestatusRequest.ShopStatusUpdateRequest request = new AlibabaAelophyShopUpdatestatusRequest.ShopStatusUpdateRequest();
        request.setStoreId(createSystem.getStoreCode());
        request.setChannelSourceType(4L);
        request.setStatus(-1L);
        return request;
    }


    /**
     * 处理抖音平台的同步
     */
    private void handleDouDianSynchronization(CreateSystem createSystem, boolean isWeb, MeituanJdWorkLog workLog) {
        Tenant tenant = tenantService.getOneTenantBy("164441871", ThirdPlatformCommonConst.THIRD_PLAT_DY);
        MyAccessToken myAccessToken = SpringUtil.getBean(DoudianFactory.class).getMyAccessToken(Convert.toLong(tenant.getTenantCode(), 0L));
        if (isWeb) {
            // 更新营业时间
            ShopEditStoreParam param = new ShopEditStoreParam();
            param.setStoreId(Long.valueOf(createSystem.getStoreCode()));
            OpenTime openTime = new OpenTime();
            openTime.setDayMap(createSystem.getShippingTime());
            param.setOpenTime(openTime);
            R<ShopEditStoreData> result = SpringUtil.getBean(DefaultDouDianService.class).editStore(myAccessToken, param);
            processResult(result, workLog, createSystem.getStoreName(), "营业时间同步成功", "营业时间同步失败");
        } else {
            // 暂停门店
            ShopStoreSuspendParam param = new ShopStoreSuspendParam();
            param.setStoreId(Long.valueOf(createSystem.getStoreCode()));
            R<ShopStoreSuspendData> result = SpringUtil.getBean(DefaultDouDianService.class).storeSuspend(myAccessToken, param);
            processResult(result, workLog, createSystem.getStoreName(), "暂停营业同步成功", "暂停营业同步失败");
        }
    }

    /**
     * 处理平台同步结果
     */
    private void processResult(R<?> result, MeituanJdWorkLog workLog, String storeName, String successMsg, String failMsg) {
        if (!result.isSuccess()) {
            workLog.setWorkContent(storeName + failMsg);
            smsService.sendOaMsgTo9Ji(AddLogKind.SYNCHRONIZATION_BUSINESS_HOURS_V1 + ":" + result.getUserMsg(),
                    "14134", OaMesTypeEnum.YCTZ.getCode().toString());
        } else {
            workLog.setWorkContent(storeName + successMsg);
        }
    }

}
