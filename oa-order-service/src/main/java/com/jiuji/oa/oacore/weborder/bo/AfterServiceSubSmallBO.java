package com.jiuji.oa.oacore.weborder.bo;

import com.jiuji.oa.oacore.weborder.vo.AfterServiceProductVO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @since 2020/4/12
 */
@Data
public class AfterServiceSubSmallBO {
    private Integer cid;
    private Integer ppid;
    private String productName;
    private String productColor;
    private Integer productCount;
    private BigDecimal productPrice;
    private Integer smallProId;
    private String imageUrl;
    private Integer kind;
    private String problem;
    private String outWard;
    private Integer state;
    private Integer isBaoXiu;
    private BigDecimal feiYong;
    private BigDecimal costPrice;
    private Integer yuYueId;
    private Boolean isMobile;
    private String userName;
    private LocalDateTime inDate;
    private LocalDateTime buyDate;
    private Integer subId;
    private Integer areaId;
    private Boolean ispj;
    /**
     * 是否删除
     */
    private Boolean isDel;
    private List<AfterServiceProductVO> productList;
}
