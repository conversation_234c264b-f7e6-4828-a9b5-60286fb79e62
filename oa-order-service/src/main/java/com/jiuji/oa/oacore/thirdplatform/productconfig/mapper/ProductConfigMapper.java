/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.oacore.thirdplatform.productconfig.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.thirdplatform.productconfig.bo.*;
import com.jiuji.oa.oacore.thirdplatform.productconfig.entity.ProductConfig;
import com.jiuji.oa.oacore.thirdplatform.productconfig.vo.*;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 商品关系配置mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("smallpro_write")
public interface ProductConfigMapper extends BaseMapper<ProductConfig> {


    /**
     * 通过mkcId查询 抖音 良品id
     * @param mkcId
     * @return
     */
    List<LpByMkcIdVo> getIdLpByMkcId(@Param("mkcIdList")List<Integer> mkcIdList);

    Page<ListProductConfigsRes> listProductConfigs(@Param("page") Page<ListProductConfigsRes> page, @Param("req") ListProductConfigsReq req);

    /**
     * 分页查询商品关系配置
     *
     * @param search
     * @param page
     * @return
     */
    List<ProductConfigVO> productConfigList(@Param("search") ProductConfigSearchBO search, Page<ProductConfigVO> page);

    /**
     * 批量保存
     * @param subList
     */
    @DS("smallpro_write")
    void saveBatch(List<ProductConfig> subList);

    /**
     * 查询带库存的商品列表
     * @param storeCode
     * @return
     */
    List<ProductConfigVO> selectListWithStock(String storeCode);

    /**
     * 查询带库存的商品列表
     * @param storeCode
     * @param ppids
     * @return
     */
    List<ProductConfigVO> selectListWithStockV1(@Param("storeCodes") List<String> storeCode, @Param("platCode") String platCode, @Param("ppids") List<Integer> ppids);

    /**
     * 查询带库存相关门店的库存总和
     * @param storeCode
     * @param areaId
     * @param ppids
     * @return
     */
    List<ProductLeftCountBO> selectListWithStockByJiuJi(@Param("storeCode") String storeCode, @Param("areaId") List<Integer> areaId, @Param("ppids") List<Integer> ppids, @Param("localAreaId")Integer localAreaId);

    /**
     * 根据id查询商品配置信息
     *
     * @param productLeftCounts
     * @return
     */
    List<ProductConfigVO> productConfigListByIds(@Param("productLeftCounts") List<ProductLeftCountBO> productLeftCounts);

    /**
     * 查询所有商品配置信息
     * @return
     */
    List<ProductConfigVO> productConfigListVo(@Param("platCode") String platCode, @Param("ppids") List<Integer> ppids, @Param("tenantCodes") List<String> tenantCodes);

    /**
     * 根据当前商户 查询配置mkcId商品
     * @param tenantCode tenantCode
     * @param platCode platCode
     * @param mkcIdList mkcIdList
     * @return
     */
    List<ProductConfig> getProductConfigListByMkcIdList(@Param("tenantCode") String tenantCode, @Param("platCode") String platCode, @Param("mkcIdList") List<Integer> mkcIdList);

    /**
     * 检查是否有重复的sku数据
     * @param skuIdList skuIdList
     * @return
     */
    Integer checkSkuIdList(@Param("skuIdList") List<String> skuIdList);

    /**
     * 批量良品库存锁定
     * @param mkcIdList mkcIdList
     * @param type 1锁定状态  2是解锁
     * @param toBasketIds 指定的允许进行锁定
     */
    int lockStockBatch(@Param("mkcIdList") List<Integer> mkcIdList, @Param("type") int type, @Param("toBasketIds") Set<Integer> toBasketIds);

    /**
     * 批量更新良品售出状态
     * @param mkcIds mkcIds
     * @param sellType sellType 1是售出  2是未售出
     * @return
     */
    int updateSellType(@Param("mkcIds") List<Integer> mkcIds,@Param("sellType") Integer sellType);

    /**
     * 获取良品售价
     * @param mkcIds mkcIds
     * @return
     */
    @DS(DataSourceConstants.ERSHOU)
    List<RecoverMkcBO> getPriceByMkcId(@Param("mkcIds") List<Integer> mkcIds);

    @DS("smallpro_write")
    List<ProductKcInpriceVO> getInpriceFromProductKc(@Param("ppidList") List<Integer> ppidList);

    Collection<ProductLeftCountV2BO> selectListWithStockByJiuJiV2(@Param("areaIds") List<Integer> areaIds, @Param("ppids") List<Integer> ppids);

    List<ProductConfigVOV2> productConfigListV2(@Param("search") ProductConfigSearchBOV2 search, @Param("page") Page<ProductConfigVOV2> page);
}
