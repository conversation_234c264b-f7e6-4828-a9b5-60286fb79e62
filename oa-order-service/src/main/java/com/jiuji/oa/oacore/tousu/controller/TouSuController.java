package com.jiuji.oa.oacore.tousu.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.component.UserComponent;
import com.jiuji.oa.oacore.common.group.Default;
import com.jiuji.oa.oacore.common.group.Save;
import com.jiuji.oa.oacore.common.group.Update;
import com.jiuji.oa.oacore.common.res.PageRes;
import com.jiuji.oa.oacore.common.util.ValidatorUtil;
import com.jiuji.oa.oacore.tousu.bo.TouSuProcessBO;
import com.jiuji.oa.oacore.tousu.po.TouSuZenRenRen;
import com.jiuji.oa.oacore.tousu.po.TousuDepart;
import com.jiuji.oa.oacore.tousu.po.TsProcess;
import com.jiuji.oa.oacore.tousu.req.AddProcessReq;
import com.jiuji.oa.oacore.tousu.req.HighOpinionReq;
import com.jiuji.oa.oacore.tousu.req.UpdateWeChatRecordReq;
import com.jiuji.oa.oacore.tousu.res.ComplainWeChatRes;
import com.jiuji.oa.oacore.tousu.res.HighOpinionRes;
import com.jiuji.oa.oacore.tousu.service.*;
import com.jiuji.oa.oacore.tousu.req.TousuPublicReq;
import com.jiuji.oa.oacore.tousu.res.TousuPublicRes;
import com.jiuji.oa.oacore.tousu.vo.req.*;
import com.jiuji.oa.oacore.tousu.vo.res.*;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.constants.IntConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * @Description 投诉类
 * <AUTHOR>
 * @Date 2020/8/27 11:23
 */
@Api(tags = "投诉类")
@RestController
@RequestMapping("inApi/touSu/")
public class TouSuController {
    @Autowired
    private TouSuService touSuService;
    @Autowired
    private TousuAreaService tousuAreaService;
    @Autowired
    private TouSuDepartService touSuDepartService;
    @Resource
    private UserComponent userComponent;
    @Resource
    private ComplainPushRelationService complainPushRelationService;
    @Resource
    private ComplainService complainService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource(name = "redisTemplate4")
    private RedisTemplate<String, String> redisTemplate4;

    @GetMapping("/testRedis")
    public R<List<TouSuModelRes>> testRedis(@RequestParam("key") String key){
        String value = stringRedisTemplate.opsForValue().get(key);
        String value4 = redisTemplate4.opsForValue().get(key);
        return R.success("默认redis：" + value + "\n4号redis：" + value4);
    }

    /**
     * 翻译C#中的touSuList接口
     *
     * @param userid
     * @param curPage
     * @param rows
     * @return
     */
//    @GetMapping(value = "/touSuList")
//    @ApiOperation(value = "投诉列表", httpMethod = "GET")
//    public R<PageRes<TouSuModelRes>> tousuList(long userid, int curPage, int rows) {
//        return R.success(touSuService.touSuList(userid, curPage, rows));
//    }

    /**
     * 翻译C#中的tousuDetail接口
     *
     * @param userid
     * @param id
     * @return
     */
//    @GetMapping("/touSuDetail")
//    @ApiOperation(value = "投诉详情", httpMethod = "GET")
//    public R<TouSuDetail> tousuDetail(@RequestParam(value = "userid", required = true) long userid, @RequestParam(value = "id", required = true) int id) {
//        return R.success(touSuService.touSuDetail(userid, id));
//    }


//    @PostMapping("/touSuDetailModify")
//    @ApiOperation(value = "投诉详情的功能", httpMethod = "POST")
//    public R<String> touSuDetailModify(@RequestBody TouSuReq touSuModel) {
//        return R.success(tousuAreaService.touSuDetailModify(touSuModel))

    /**
     * 翻译C#中的tousuAddQuestion接口
     *
     * @param userid
     * @param id
     * @param content
     * @param attachFiles
     * @return
     */
//    @GetMapping("/touSuAddQuestion")
//    @ApiOperation(value = "新增客户追问", httpMethod = "GET")
//    public R<TouSuResult> tousuAddQuestion(@RequestParam(value = "userid", required = true) long userid,
//                                           @RequestParam(value = "id", required = true) int id,
//                                           @RequestParam(value = "content", required = true) String content,
//                                           @RequestParam(value = "attachFiles", required = true) String attachFiles) {
//        return R.success(touSuService.touSuAddQuestion(userid, id, content, attachFiles));
//    }
//    @PostMapping(value = "/touSuResList")
//    @ApiOperation(value = "投诉列表", httpMethod = "POST")
//    public R<Page<TouSuModel>> touSuResList(@RequestBody TouSuSearchReq touSuReq) {
//        return R.success(tousuAreaService.touSuResList(touSuReq));
//    }

    /**
     * 投诉列表 新版
     *
     * @param touSuReq
     * @return
     */
    @PostMapping(value = "/touSuResListV2")
    @ApiOperation(value = "投诉列表 新版", httpMethod = "POST")
    public R<Page<TouSuModelRes>> touSuResListV2(@RequestBody TouSuModelReq touSuReq) {
        return R.success(tousuAreaService.touSuResListV2(touSuReq));
    }

    /**
     * 投诉列表导出 新版
     *
     * @param touSuReq
     * @return
     */
    @PostMapping(value = "/touSuResListExportV2")
    @ApiOperation(value = "投诉列表 新版", httpMethod = "POST")
    public void touSuResListExportV2(HttpServletResponse response, @RequestBody TouSuModelReq touSuReq) {
        tousuAreaService.touSuResListExportV2(response,touSuReq);
    }

    @GetMapping(value = "/findUserInfo")
    @ApiOperation(value = "搜索会员信息", httpMethod = "GET")
    public R<Object> findUserInfo(@RequestParam(value = "keyWordType") String keywordType, @RequestParam(value = "keyWord") String keyword) {
        return R.success(tousuAreaService.findUserInfo(keywordType, keyword));
    }

    @GetMapping(value = "/findUserInfo/v2")
    @ApiOperation(value = "搜索会员信息", httpMethod = "GET")
    public R<Object> findUserInfoV2(@RequestParam(value = "keyWordType") String keywordType, @RequestParam(value = "keyWord") String keyword) {
        return R.success(tousuAreaService.findUserInfoV2(keywordType, keyword));
    }


    @PostMapping(value = "/addNewTouSu")
    @ApiOperation(value = "添加投诉新", httpMethod = "POST")
    public R<String> addNewTouSu(@RequestBody TouSuNewReq touSuModel) {
        ValidatorUtil.validateEntity(touSuModel, Save.class);
        String res = tousuAreaService.addNewTouSu(touSuModel);
        if ("失败".equals(res)) {
            return R.success(res, "0");
        }
        return R.success("成功", res);
    }


    @GetMapping("/getTouSuCategoryList")
    @ApiOperation(value = "获取投诉分类树列表", httpMethod = "GET")
    public R<List<TouSuTypesRes>> getTouSuCategoryList(@RequestParam(required = false)Integer kind) {
        if(kind==null){
            kind=1;
        }
        return R.success(tousuAreaService.getTouSuCategoryList(kind));
    }


    @GetMapping("/getMemberBasicInfo")
    @ApiOperation(value = "获得会员信息", httpMethod = "GET")
    public R<MemberRes> getMemberBasicInfo(@RequestParam(value = "id") Integer id, @RequestParam(value = "mobile", required = false) String mobile, @RequestParam(value = "tag", required = false) Integer tag) {
        MemberRes memberBasicInfo;
        try {
            memberBasicInfo = touSuDepartService.getMemberBasicInfo(id, mobile, tag);
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
        return R.success(memberBasicInfo);
    }

    @GetMapping("/getTouSuInfo")
    @ApiOperation(value = "投诉基础信息", httpMethod = "GET")
    public R<TouSuRes> getTouSuInfo(@RequestParam(value = "id") Integer id) {
        return touSuDepartService.getTouSuInfo(id);
    }

    @GetMapping("/setArchiveCategory")
    @ApiOperation(value = "保存典型投诉 心声投诉  跟进人", httpMethod = "GET")
    public R<String> setArchiveCategory(@RequestParam(value = "id") Integer id,
                                        @RequestParam(value = "archiveCategory", required = false) Integer archiveCategory,
                                        @RequestParam(value = "isxinsheng", required = false) Boolean isxinsheng,
                                        @RequestParam(value = "processUser", required = false) String processUser,
                                        @RequestParam(value = "showWeb", required = false) Boolean showWeb,
                                        @RequestParam(value = "dealUser", required = false) String dealUser){
        return R.success(tousuAreaService.setArchiveCategory(id, archiveCategory, isxinsheng, processUser, showWeb, dealUser));
    }

    @PostMapping("/setArchiveCategory/v2")
    public R<String> setArchiveCategory(@RequestBody TousuAddProcessUserReq tousuAddProcessUserReq){
        return R.success(tousuAreaService.setArchiveCategoryV2(tousuAddProcessUserReq));
    }

    @PostMapping("/setShowWeb/v1")
    public R<String> setShowWeb(@RequestBody TousuAddProcessUserReq tousuAddProcessUserReq){
        return R.success(tousuAreaService.setShowWeb(tousuAddProcessUserReq));
    }

    @PostMapping("/tousuProcessDeal")
    @ApiOperation(value = "投诉处理跟进", httpMethod = "POST")
    public R<String> tousuProcessDeal(@RequestBody TouSuProcessReq touSuProcess) {
        ValidatorUtil.validateEntity(touSuProcess, Default.class);
        return R.success(touSuDepartService.touSuProcessDeal(touSuProcess));
    }

    @PostMapping("/saveTsDemarcation/v1")
    public R<Boolean> saveTsDemarcation(@RequestBody TouSuDemarcationReq touSuDemarcationReq) {
        ValidatorUtil.validateEntity(touSuDemarcationReq, Default.class);
        return R.success(touSuDepartService.saveTsDemarcation(touSuDemarcationReq));
    }

    @PostMapping("/processDeal")
    @ApiOperation(value = "处理进程", httpMethod = "POST")
    public R<String> processDeal(@RequestBody ProcessReq processReq) {
        ValidatorUtil.validateEntity(processReq, Default.class);
        return R.success(touSuDepartService.processDeal(processReq));
    }

    @GetMapping("/showProcessInfo")
    @ApiOperation(value = "展示进程", httpMethod = "GET")
    public R<List<TsProcess>> showProcessInfo(@RequestParam(value = "id") Integer id,
                                              @RequestParam(value="requestType",required = false) String requestType) {
        return R.success(touSuDepartService.showProcessInfo(id,requestType));
    }

    @PostMapping("/isShowProcessInfo")
    @ApiOperation(value = "勾选显示进程", httpMethod = "POST")
    public R<Boolean> isShowProcessInfo(@RequestBody TouSuProcessBO touSuProcess) {
        ValidatorUtil.validateEntity(touSuProcess, Update.class);
        return touSuDepartService.isShowProcessInfo(touSuProcess.getTsProcesses());
    }

    @PostMapping("/addTouSuDepart")
    @ApiOperation(value = "添加责任划分——门店,部门", httpMethod = "POST")
    public R<String> addTouSuDepart(@RequestBody TousuDepart touSuDepartReq) {
        ValidatorUtil.validateEntity(touSuDepartReq, Default.class);
        return R.success(tousuAreaService.addTouSuDepart(touSuDepartReq));
    }

    @PostMapping("/modifyTouSuDepart")
    @ApiOperation(value = "修改责任划分——门店,部门", httpMethod = "POST")
    public R<String> modifyTouSuDepart(@RequestBody TousuDepart touSuDepartReq) {
        ValidatorUtil.validateEntity(touSuDepartReq, Default.class);
        return tousuAreaService.modifyTouSuDepart(touSuDepartReq);
    }

    @PostMapping("/deleteTouSuDepart")
    @ApiOperation(value = "删除责任划分——门店,部门", httpMethod = "POST")
    public R<String> deleteTouSuDepart(@RequestBody TousuDepart touSuDepartReq) {
        ValidatorUtil.validateEntity(touSuDepartReq, Default.class);
        return tousuAreaService.deleteTouSuDepart(touSuDepartReq);
    }

    @PostMapping("/saveZeRenRen")
    @ApiOperation(value = "保存责任人", httpMethod = "POST")
    public R<String> saveZeRenRen(@RequestBody TouSuZenRenRen zeRenRenList) {
        ValidatorUtil.validateEntity(zeRenRenList, Default.class);
        BigDecimal tousuLosePoint = Optional.ofNullable(zeRenRenList.getTousuLosePoint())
                .orElse(BigDecimal.ZERO)
                .setScale(IntConstant.TWO, RoundingMode.DOWN);
        if (tousuLosePoint.compareTo(BigDecimal.valueOf(99999999.99)) > 0) {
            return R.error("扣分值过大");
        }
        zeRenRenList.setTousuLosePoint(tousuLosePoint);
        return R.success(touSuDepartService.saveZeRenRen(zeRenRenList));
    }

    @PostMapping("/modifyZeRenRen")
    @ApiOperation(value = "修改责任人", httpMethod = "POST")
    public R<String> modifyZeRenRen(@RequestBody TouSuZenRenRen zeRenRenList) {
        ValidatorUtil.validateEntity(zeRenRenList, Default.class);
        BigDecimal tousuLosePoint = Optional.ofNullable(zeRenRenList.getTousuLosePoint())
                .orElse(BigDecimal.ZERO)
                .setScale(IntConstant.TWO, RoundingMode.DOWN);
        if (tousuLosePoint.compareTo(BigDecimal.valueOf(99999999.99)) > 0) {
            return R.error("扣分值过大");
        }
        zeRenRenList.setTousuLosePoint(tousuLosePoint);
        return R.success(touSuDepartService.modifyZeRenRen(zeRenRenList));
    }

    @PostMapping("/deleteZeRenRen")
    @ApiOperation(value = "删除责任人", httpMethod = "POST")
    public R<String> deleteZeRenRen(@RequestBody TouSuZenRenRen zeRenRenList) {
        ValidatorUtil.validateEntity(zeRenRenList, Default.class);
        return R.success(touSuDepartService.deleteZeRenRen(zeRenRenList));
    }

    @GetMapping("/tousuEndAndinvalid")
    @ApiOperation(value = "相同投诉", httpMethod = "GET")
    public R<Boolean> setTousuEndAndinvalid(@RequestParam(value = "id") Integer id, @RequestParam(value = "joinTousuId") Integer joinTousuId, @RequestParam(value = "isSupplier") Boolean isSupplier) {
        return tousuAreaService.setTouSuEndAndInvalid(id, joinTousuId, isSupplier);
    }

    @GetMapping("/getTouSuEndInfo")
    @ApiOperation(value = "投诉结果信息", httpMethod = "GET")
    public R<TouSuEndInfoRes> getTouSuEndInfo(@RequestParam(value = "id") Integer id) {
        return R.success(touSuDepartService.getTouSuEndInfo(id));
    }


    @PostMapping("/sendMa")
    @ApiOperation(value = "发送优惠码", httpMethod = "POST")
    public R<String> sendMa(@RequestBody DiscountsReq discounts) {
        ValidatorUtil.validateEntity(discounts, Default.class);
        R<String> verify = tousuAreaService.verifyBeforeSendMa(discounts);
        if (!verify.isSuccess()) {
            return verify;
        }
        return R.success(tousuAreaService.sendMa(discounts));
    }

    @GetMapping("/sendMsg")
    @ApiOperation(value = "超时未跟进发送信息", httpMethod = "GET")
    public R<String> sendMsg() {
        return R.success(tousuAreaService.sendMsg());
    }

    @PostMapping("/saveTousuCategory")
    @ApiOperation(value = "保存投诉分类", httpMethod = "POST")
    public R<String> saveTouSuCategory(@RequestBody TousuCategoryReq tousuCategoryReq) {
        ValidatorUtil.validateEntity(tousuCategoryReq, Save.class);
        return R.success(tousuAreaService.saveTouSuCategory(tousuCategoryReq));
    }

    @GetMapping("/getCoupleBackByUserId")
    @ApiOperation(value = "判断是否是渠道商", httpMethod = "GET")
    public R<Boolean> getCoupleBackByUserId(@RequestParam(value = "userId") Integer userId) {
        return R.success(tousuAreaService.getCoupleBackByUserId(userId));
    }

//    @PostMapping("/pushMsgCoupleBack")
//    @ApiOperation(value = "每个月给供应商推送消息", httpMethod = "POST")
//    public R<Boolean> pushMsgCoupleBack() {
//        return tousuAreaService.pushMsgCoupleBack();
//    }

    @PostMapping("/getCoupleBackList")
    @ApiOperation(value = "供应商反馈列表", httpMethod = "POST")
    public R<PageRes<CoupleBackRes>> coupleBackList(@RequestBody CoupleBackReq coupleBackReq) {
        return R.success(tousuAreaService.coupleBackList(coupleBackReq));
    }

    @PostMapping("/listTousuPublic")
    @ApiOperation(value = "投诉公示列表", httpMethod = "POST")
    public R<PageRes<TousuPublicRes>> listTousuPublic(@RequestBody TousuPublicReq tousuPublicReq) {
        tousuPublicReq.setShowWeb(true);
        return R.success(tousuAreaService.listTousuPublic(tousuPublicReq));
    }

    @PostMapping("/coupleBackSubmit")
    @ApiOperation(value = "供应商反馈提交", httpMethod = "POST")
    public R<Object> coupleBackSubmit(@RequestBody AddCoupleBackReq coupleBackReq) {
        ValidatorUtil.validateEntity(coupleBackReq, Save.class);
        return R.success(tousuAreaService.addCoupzleBack(coupleBackReq));
    }

    @GetMapping("/getCoupleBack")
    @ApiOperation(value = "供应商反馈详情查询", httpMethod = "GET")
    public R<CoupleBackInfoRes> getCoupleBack(@RequestParam(value = "id") Integer id, @RequestParam(value = "userId") Integer userId) {
        return R.success(tousuAreaService.getCoupleBack(id, userId));
    }

    @GetMapping("/addTouSuOtherInfo")
    @ApiOperation(value = "WebServer添加投诉后续操作", httpMethod = "GET")
    public R<String> addTouSuOtherInfoByWebServer(@RequestParam(value = "tousuId")Integer tousuId, @RequestParam(value = "storeCode")String storeCode) {
        return R.success(tousuAreaService.addTouSuOtherInfoByWebServer(tousuId,storeCode));
    }

    /**
     * 查询投诉页面的浏览量
     * @param url
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/getComplaintPV/v1")
    public R getComplaintPV(@RequestParam(value = "url") String url, @RequestParam(value = "current") Long current, @RequestParam(value = "size") Long size) {
        return R.success(touSuService.getComplaintPV(url, current, size));
    }

    @GetMapping("/delete")
    public R delete(@RequestParam(value = "id") Integer id) {
        // 有gjkp（高级客评）和99权限的可以删除投诉
        OaUserBO oaUserBO = userComponent.getOaUserBO(false);
        Predicate<OaUserBO> biPredicate = (user) ->
                user != null && user.getRank() != null && (user.getRank().contains("gjkp") || user.getRank().contains("99"));
        if (!biPredicate.test(oaUserBO)) {
            return R.error("无权限删除");
        }
        return R.success(complainService.updateComplainStatus(id));
    }

    /**
     * 添加投诉进程日志
     * @param addProcessReq 投诉进程日志
     * @return
     */
    @PostMapping("/addProcess")
    public R<Boolean> addProcess(@RequestBody @Validated AddProcessReq addProcessReq) {
        return R.success(touSuDepartService.addProcess(addProcessReq));
    }


    /**
     * 根据投诉id查询未领取的微信现金记录
     * @param complainId 投诉id
     * @return
     */
    @GetMapping("/getWeChatRecord")
    public R<List<ComplainWeChatRes>> getWeChatRecord(@RequestParam(value = "complainId") Integer complainId) {
        return R.success(complainPushRelationService.getComplainPushRelationsByComplainId(complainId));
    }

    /**
     * 修改投诉微信现金记录成已领取
     * @param recordReq 投诉id
     * @return
     */
    @PostMapping("/updateWeChatRecord")
    public R<Boolean> updateWeChatRecord(@RequestBody @Validated UpdateWeChatRecordReq recordReq) {
        return R.success(complainPushRelationService.updateComplainWeChatRecord(recordReq));
    }

    /**
     * 根据记录id查询微信现金记录状态
     * @param recordId 进程id
     * @return true:已领取 false:未领取
     */
    @GetMapping("/getWeChatRecordStatus")
    public R<Boolean> getWeChatRecordStatus(@RequestParam(value = "recordId") Long recordId) {
        return R.success(complainPushRelationService.getComplainWeChatRecordStatus(recordId));
    }

    /**
     * 修改投诉管理-业务流程图
     */
    @GetMapping("/modifyBusinessFlowChart")
    public R<Boolean> modifyBusinessFlowChart(@RequestParam(value = "image") String image) {

        return touSuService.modifyBusinessFlowChart(image);
    }

    @PostMapping("/highOpinion")
    public R<String> highOpinion(@RequestBody HighOpinionReq req,@RequestHeader(name = "xtenant",required = false) Integer xtenant){
        return tousuAreaService.highOpinion(req,xtenant);
    }
}
