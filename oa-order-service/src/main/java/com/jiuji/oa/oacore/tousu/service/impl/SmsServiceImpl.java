package com.jiuji.oa.oacore.tousu.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.ch999.common.util.tenant.Namespaces;
import com.ch999.common.util.vo.Result;
import com.jiuji.cloud.office.dto.MsgPushDTO;
import com.jiuji.cloud.office.service.MsgPushCloud;
import com.jiuji.oa.afterservice.shouhou.vo.sms.AppInfoVo;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.bo.ZnSendConnBo;
import com.jiuji.oa.oacore.common.component.CurrentRequestComponent;
import com.jiuji.oa.oacore.common.component.UserComponent;
import com.jiuji.oa.oacore.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.oacore.common.config.properties.SmsProperties;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.constant.RequestAttrKeys;
import com.jiuji.oa.oacore.common.constant.UrlConstant;
import com.jiuji.oa.oacore.common.enums.ESmsChannelTypeEnum;
import com.jiuji.oa.oacore.common.enums.JiujiSmsChannelEnum;
import com.jiuji.oa.oacore.common.enums.JiujiTenantEnum;
import com.jiuji.oa.oacore.common.enums.OaMesTypeEnum;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.common.source.InwcfSource;
import com.jiuji.oa.oacore.common.source.appIndex.AppIndexWwwUrlSource;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.common.util.HttpClientUtil;
import com.jiuji.oa.oacore.common.util.SpringContextUtil;
import com.jiuji.oa.oacore.goldseed.service.AreaInfoService;
import com.jiuji.oa.oacore.oaorder.service.OaSysConfigService;
import com.jiuji.oa.oacore.sys.bo.AuthModel;
import com.jiuji.oa.oacore.sys.service.AuthConfigService;
import com.jiuji.oa.oacore.tousu.bo.SmsChannelBo;
import com.jiuji.oa.oacore.tousu.bo.SmsPlatFormBO;
import com.jiuji.oa.oacore.tousu.bo.WeChatTokenBo;
import com.jiuji.oa.oacore.tousu.dao.SmsConfigMapper;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.PlatformMessageUtil;
import com.jiuji.tc.utils.common.TraceIdUtil;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Slf4j
@Service
public class SmsServiceImpl implements SmsService {
    @Resource
    private InwcfSource inwcfSource;
    @Resource
    private AppIndexWwwUrlSource appIndexWwwUrlSource;
    @Resource
    private AreaInfoClient areaInfoClient;
    @Resource
    private SmsProperties smsProperties;
    @Resource
    private MsgPushCloud msgPushCloud;
    @Resource
    private UserComponent userComponent;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private SmsConfigMapper smsConfigMapper;

    @Resource
    private SysConfigClient sysConfigClient;

    private static final String APPID = "wxaaf67ca511a634ab";

    private static final String SECRET = "ae10c9635113ca2bf7d5cd30ffa986e1";


    /**
     * 发送短信
     *
     * @param phone
     * @param content
     * @param sendTime
     * @param areaId
     * @param sender
     * @return
     */
    @Override
    public R<Boolean> sendSms(String phone, String content, String sendTime, Integer areaId, String sender) {
        try {
            String sendServer = "9";
            if (CommonUtil.isNullOrZero(areaId)) {
                R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(areaId);
                if (areaInfoR.getCode() == ResultCode.SUCCESS && areaInfoR.getData() != null) {
                    if (!areaInfoR.getData().getIsSend()) {
                        return R.error("当前地区不允许发送短信");
                    }

                    Integer xteanat = areaInfoR.getData().getXtenant();
                    sendServer = this.getSmsChannelByXtenant(Long.valueOf(xteanat));
                }
            }
            if (StringUtils.isEmpty(sender)) {
                sender = "系统";
            }
            Map<String, String> param = new HashMap<>();
            param.put("phones", phone);
            param.put("content", content);
            param.put("sendtime", sendTime);
            param.put("server", sendServer);
            param.put("sender", sender);

            String smsRet = HttpClientUtil.post(smsProperties.getUrl(), param);
            if (!StringUtils.isBlank(smsRet) && smsRet.contains("True")) {
                return R.success("发送成功", true);
            }
        } catch (Exception e) {
            log.error("短信发送失败,error={}", e.getMessage());
        }
        return R.error(ResultCode.RETURN_ERROR, "短信发送失败");
    }

    /**
     * 发送短信
     *
     * @param phone
     * @param xtenant
     * @param content
     * @param sendTime
     * @param sender
     * @return
     */
    @Override
    public R<Boolean> sendSms(String phone, Integer xtenant, String content, String sendTime, String sender) {
        try {
            String sendServer = "9";
            sendServer = this.getSmsChannelByXtenant(Long.valueOf(xtenant));

            if (StringUtils.isEmpty(sender)) {
                sender = "系统";
            }
            Map<String, String> param = new HashMap<>();
            param.put("phones", phone);
            param.put("content", content);
            param.put("sendtime", sendTime);
            param.put("server", sendServer);
            param.put("sender", sender);
            String url = smsProperties.getUrl();
            String smsRet = HttpClientUtil.post(smsProperties.getUrl(), param);
            if (!StringUtils.isBlank(smsRet) && smsRet.contains("True")) {
                return R.success("发送成功", true);
            }
        } catch (Exception e) {
            log.error("短信发送失败,error={}", e.getMessage());
        }
        return R.error(ResultCode.RETURN_ERROR, "短信发送失败");
    }

    @Resource
    private CurrentRequestComponent currentRequestComponent;

    /**
     * example：
     * {
     *   "phones": "18000559081",
     *   "content": "换手机，找九机，换新补贴，优惠多多",
     *   "appId": "wtjxinXW",
     *   "msgType":1
     * }
     */
    @Override
    public R<Boolean> sendSmsNew(String phone, String content, String sendTime, String sender, Integer msgType, Integer xtenant) {
        try {
            Assert.isFalse(StrUtil.isBlank(smsProperties.getPlatformUrl()),"短信平台地址不能为空");
            Assert.isFalse(StrUtil.length(StrUtil.trim(sendTime))>20,"发送时间格式错误");
            Assert.isFalse(StrUtil.isBlank(content),"发送的短信内容不能为空");
            // 获取当前的用户名密码
            AppInfoVo appInfo = getNcSmsconfig(Objects.isNull(xtenant) ? Namespaces.get() : xtenant);
            Assert.isFalse(ObjectUtil.isNull(appInfo),"短信平台用户信息不能为空");
            Assert.isFalse(StrUtil.isBlank(appInfo.getSecretKey()),"短信平台用户密钥不能为空");
            // 短信内容
            SmsPlatFormBO smsPlatFormBo = new SmsPlatFormBO()
                    .setAppId(appInfo.getAppId())
                    .setPhones(phone)
                    .setContent(content);
            if (Objects.isNull(msgType)) {
                // 渠道id
                smsPlatFormBo.setChannelId(sysConfigClient.getSmsChannel(22, 2).getData());
            } else {
                smsPlatFormBo.setMsgType(msgType);
            }
            // smsPlatFormBo.setChannelId(9);
            log.warn("合作伙伴投诉建议发送sms:{}", smsPlatFormBo.toJson());
            HttpResponse httpResponse = HttpUtil.createPost(StrUtil.format("{}/cloudapi_nc/api/sendmsg?xservicename=sms", smsProperties.getPlatformUrl()))
                    .header("token", DigestUtil.md5Hex(StrUtil.format("sms_{}{}", appInfo.getSecretKey()
                            , LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")))))
                    .body(smsPlatFormBo.toJson())
                    .execute();
            Assert.isTrue(httpResponse.isOk(), StrUtil.format("短信平台接口{}异常", httpResponse.getStatus()));
            Assert.isFalse(StrUtil.isBlank(httpResponse.body()),"短信平台返回结果为空");
            return Optional.ofNullable(JSON.parseObject(httpResponse.body(), R.class))
                    .map(r->{
                        R<Boolean> rr = new R<>(r.getCode(),r.getUserMsg());
                        if(rr.isSuccess()){
                            rr.setData(Boolean.TRUE);
                            rr.setUserMsg("短信发送成功");
                        }
                        if(r.getData() != null){
                            rr.addBusinessLog(String.valueOf(r.getData()));
                        }
                        return rr;
                    })
                    .orElseGet(()->R.error("短信平台发送短信未知错误"));
        } catch (IllegalArgumentException e) {
            return R.error(e.getMessage());
        }
    }

    /**
     * 发送oa消息
     *
     * @param content
     * @param link
     * @param ch999ids
     * @param msgType
     */
    @Override
    public void sendOaMsg(String content, String link, String ch999ids, String msgType) {
        String sendHanderUrl = inwcfSource.getOaMsg(content, link,
                ch999ids, msgType);
        String host = SpringUtil.getBean(SysConfigClient.class).getValueByCode(SysConfigConstant.IN_WCF_HOST).getData();
        Map<String, String> params = new HashMap<>();
        params.put("content", content);
        params.put("link", link);
        params.put("ch999ids", ch999ids);
        params.put("msgType", msgType);
        params.put("act","oaMessagePush");

        if (StringUtils.isNotEmpty(host)) {
            sendHanderUrl = host + "/ajax.ashx";
        }
        if (log.isDebugEnabled()){
            log.debug("oa推送地址: {}, 参数: {}", sendHanderUrl, JSON.toJSONString(params, SerializerFeature.PrettyFormat));
        }
        if (SpringContextUtil.getRequest().map(req -> req.getAttribute(RequestAttrKeys.NOT_NOTICE_USER))
                .map(Convert::toBool).orElse(Boolean.FALSE)){
            //开启不通知开关,不发送通知
            log.debug("不通知开关开启,不进行推送oa消息");
            return;
        }
        HttpClientUtil.post(sendHanderUrl, params);
    }

    /**
     * 发送oa消息
     *
     * @param content
     * @param link
     * @param ch999ids
     * @param msgType
     */
    @Override
    public void sendOaMsg(String content, String link, String ch999ids, OaMesTypeEnum msgType) {
        sendOaMsg(content, link, ch999ids, msgType.getCode().toString());
    }

    /**
     * 发送站内消息
     *
     * @param con
     * @return
     */
    @Override
    public R<Boolean> sendZnMsg(ZnSendConnBo con) {
        if (con == null) {
            return R.error("发送消息内容不能为空");
        }
        String url = inwcfSource.getBasicUrl() + appIndexWwwUrlSource.getZnMsg();
        try {
            Map<String, String> param = new HashMap<>();
            param.put("title", con.getTitle());
            //kind: 1=>活动，4=》其他
            param.put("kind", String.valueOf(con.getKind()));
            param.put("PlatForm", con.getPlatForm());
            if (StringUtils.isNotEmpty(con.getLink())) {
                param.put("link", con.getLink());
            }
            if (StringUtils.isNotEmpty(con.getAppLink())) {
                param.put("AppLink", con.getAppLink());
            }
            if (StringUtils.isNotEmpty(con.getHdimg())) {
                param.put("img", con.getHdimg());
            }
            if (StringUtils.isNotEmpty(con.getEndTime())) {
                param.put("endtime", con.getEndTime());
            }
            param.put("content", con.getContent());
            param.put("ExtraData", con.getExtraData());
            if (8 == con.getKind()) {
                param.put("userid", String.valueOf(0));
                String json = HttpClientUtil.post(url, param);
                if (json != null && json.equals("1")) {
                    return R.success("发送成功", true);
                }
            } else {
                int nowCount = 0;
                String sendUserIds = "";
                if (StringUtils.isNotEmpty(con.getSmsnumber())) {
                    String[] smsArr = con.getSmsnumber().split(",");
                    for (int i = 0; i < smsArr.length; i++) {
                        sendUserIds += smsArr[i].replace("\r", "").replace("\n", "");
                        if (i < smsArr.length - 1) {
                            sendUserIds += ",";
                        }
                        nowCount++;
                        if (nowCount % 2000 == 0 || nowCount == smsArr.length) {
                            param.put("userid", sendUserIds);
                            String json = HttpClientUtil.post(url, param);
                            sendUserIds = "";
                            if (json != null && json.equals("1")) {
                                return R.success("发送成功", true);
                            } else {
                                return R.error("发送失败");
                            }

                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("推送站内消息失败：{}", e.getMessage());
        }
        return R.error("发送失败");
    }

    /**
     * 获取短链
     *
     * @param xtenant
     * @param fullUrl
     * @param des
     * @return
     */
    @Override
    public String getShortUrl(Long xtenant, String fullUrl, String des) {
        Map<String, String> params = new HashMap<>();
        params.put("xtenant", String.valueOf(xtenant));
        params.put("description", des);
        params.put("fullUrl", fullUrl);
        String resultJson = HttpClientUtil.post(UrlConstant.SHORT_URL, params);
        R<String> shortUrlRet = JSON.parseObject(resultJson, new TypeReference<R<String>>() {
        });
        if (shortUrlRet.getCode() == ResultCode.SUCCESS && StringUtils.isNotEmpty(shortUrlRet.getData())) {
            fullUrl = shortUrlRet.getData();
        }
        return fullUrl;
    }

    @Override
    public String getSmsChannelByXtenant(Long xtenant) {

        String smsChannel = "";
        if (Long.valueOf(JiujiTenantEnum.JIUJI_TENANT_YAYA.getCode().longValue()).equals(xtenant)) {
            smsChannel = JiujiSmsChannelEnum.JIUJI_SMS_CHANNEL_YAYA.getCode().toString();
        }
        if (Long.valueOf(JiujiTenantEnum.JIUJI_TENANT_JIUJI.getCode().longValue()).equals(xtenant)) {
            smsChannel = JiujiSmsChannelEnum.JIUJI_SMS_CHANNEL_JIUJI.getCode().toString();
        }
        if (Long.valueOf(JiujiTenantEnum.JIUJI_TENANT_HUAWEI.getCode().longValue()).equals(xtenant)) {
            smsChannel = JiujiSmsChannelEnum.JIUJI_SMS_CHANNEL_HUAWEI.getCode().toString();
        }

        return smsChannel;
    }

    /**
     * 发送内部邮件
     *
     * @param sendTo
     * @param title
     * @param msg
     * @return
     */
    @Override
    public String sendEmail(String sendTo, String title, String msg) {
        String result;
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("operation", "sendEmail");
            params.put("RECEIVE_USER", sendTo);
            params.put("EMAIL_TITLE", title);
            params.put("EMAIL_CONTENT", msg);
            result = HttpUtil.post(smsProperties.getSendInUrl(), params);
        } catch (Exception e) {
            result = "推送站内Emailshibai";
        }
        return result;
    }

    /**
     * 微信模板消息发送
     *
     * @param openId
     * @param templateId
     * @param url
     * @param weChatMsgTemplate
     * @param topColor
     * @param miniProgram
     * @return
     */
    @Override
    public String sendTemplateMsg(String openId, String templateId, String url, JSONObject weChatMsgTemplate,
                                  String topColor, Object miniProgram) {
        if (StringUtils.isEmpty(topColor)) {
            topColor = "#0048a3";
        }
        weChatMsgTemplate.put("touser", openId);
        weChatMsgTemplate.put("url", url);
        weChatMsgTemplate.put("topColor", topColor);
        weChatMsgTemplate.put("templateId", templateId);
        String jsonStr = JSONObject.toJSONString(weChatMsgTemplate);
        String access_token = getAccessToken();
        if (StringUtils.isEmpty(access_token)) {
            return "获取 access_token 出错";
        }
        String sendMsgUrl = String.format(UrlConstant.SEND_MSG, access_token);
        String result = HttpUtil.post(sendMsgUrl, jsonStr);

        return result;
    }

    /**
     * 获取token
     *
     * @return
     */
    @Override
    public String getAccessToken() {
        stringRedisTemplate.delete("WeiXinToken_" + "1");
        String tokenStr = stringRedisTemplate.opsForValue().get("WeiXinToken_" + "1");
        if (StringUtils.isNotBlank(tokenStr)) {
            return tokenStr;
        }
        String url = String.format(UrlConstant.TOKEN_URL, APPID, SECRET);
        String jsonStr = HttpClientUtil.get(url);
        if (jsonStr.contains("access_token")) {
            WeChatTokenBo weChatTokenBo = JSON.parseObject(jsonStr, WeChatTokenBo.class);
            if (weChatTokenBo != null) {
                stringRedisTemplate.opsForValue().set("WeiXinToken_" + "1", weChatTokenBo.getAccess_token(),
                        weChatTokenBo.getExpires_in(), TimeUnit.SECONDS);
                return weChatTokenBo.getAccess_token();
            }
        }
        return "";
    }

    /**
     * 发送oa消息
     *
     * @param message
     * @param ch999Id
     * @param msgType
     * @param link 跳转链接
     */
    @Override
    public void sendOaAppAndWeiXing(String message, String ch999Id, Integer msgType, String link) {
        try {
            //消息推送
            MsgPushDTO msgPushDTO = new MsgPushDTO();
            List<Integer> userIds = Arrays.stream(StrUtil.split(ch999Id, StrUtil.COMMA)).map(Integer::valueOf).collect(Collectors.toList());
            msgPushDTO.setUserIds(userIds);
            msgPushDTO.setMsg(message);
            msgPushDTO.setMsgType(msgType);
            msgPushDTO.setLink(link);
            msgPushDTO.setXtenant(userComponent.getXtenant(true));
            R<Boolean> send = msgPushCloud.send(msgPushDTO);
            if (!send.isSuccess() || !send.getData()) {
                log.error("消息推送失败，msgPushDTO{}", msgPushDTO);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    /**
     * 指定推送到九机oa系统
     *
     * @param content content
     * @param ch999ids ch999ids
     * @param msgType msgType
     */
    @Override
    public void sendOaMsgTo9Ji(String content, String ch999ids, String msgType) {
        //获取日志的连接地址
        sendOaMsgWithHtml(content, "", ch999ids, msgType, "https://moa.9ji.com");
    }


    /**
     * 指定推送到九机oa系统,可以包含html,内部使用,不对外
     *
     * @param content content
     * @param ch999ids ch999ids
     * @param msgType msgType
     */
    @Override
    public void sendOaMsgWithHtml(String content, String link, String ch999ids, String msgType) {
        //获取日志的连接地址
        String host = SpringUtil.getBean(SysConfigClient.class).getValueByCode(SysConfigConstant.MOA_URL).getData();
        if (StrUtil.isBlank(host)){
            log.warn("地址为空,不进行oa推送: content: {} link: {} ch999ids: {} msgType: {}",content,link,ch999ids,msgType);
            return;
        }
        sendOaMsgWithHtml(content, link, ch999ids, msgType, host);
    }

    private void sendOaMsgWithHtml(String content, String link, String ch999ids, String msgType, String host) {
        String sendHarderUrl = StrUtil.format("{}/cloudapi_nc/org_service/api/myMessage/myMessage/oaMessagePush?xservicename=oa-org",host);
        HashMap<String, Object> param = new HashMap<>();
        param.put("act", "oaMessagePush");
        param.put("content", content);
        param.put("link", link);
        param.put("ch999Ids", StrUtil.splitTrim(ch999ids, StringPool.COMMA).stream().map(Convert::toInt).collect(Collectors.toList()));
        param.put("msgType", msgType);
        String paramStr = JSON.toJSONString(param);
        log.debug("推送oa的地址: {},参数: {}", sendHarderUrl,paramStr);
        log.debug("推送oa结果: {}",HttpUtil.post(sendHarderUrl, paramStr));
    }

    @Override
    public void sendOaMsgTo9JiMan(String template, Object... params) {
        String originContent = StrUtil.format(template,params);
        if (StrUtil.isBlank(originContent)){
            return;
        }
        String content = originContent;
        if(StrUtil.isNotBlank(XtenantEnum.getTenantName()) && !content.contains(XtenantEnum.getTenantName())){
            content = StrUtil.format("{}【{}{}】{}",Optional.ofNullable(SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId()).map(OaUserBO::getAreaId)
                    .map(areaId -> SpringUtil.getBean(AreaInfoClient.class).getAreaInfoById(areaId)).filter(R::isSuccess).map(R::getData)
                    .map(AreaInfo::getPrintName).orElse(""), XtenantEnum.getTenantName(), SpringContextUtil.isProduce() ? "" : "测试", content);
        }
        String traceId = MDC.get(TraceIdUtil.TRACE_ID_KEY);
        String markdownContent = content;
        if(StrUtil.isNotBlank(traceId)){
            String logUrl = RRExceptionHandler.getLogUrl("oajava-orderservice", traceId);
            content = StrUtil.format("{}  <a target='_blank' href='{}'>跳转日志</a>",content,logUrl);
            markdownContent = StrUtil.format("{}  [跳转日志]({})",markdownContent,logUrl);
        }
        List<String> paramNotice = SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.LOG_PARAM_NOTICE);
        if(CollUtil.isNotEmpty(paramNotice)){
            String paramNoticeStr = CollUtil.join(paramNotice, StrUtil.LF);
            markdownContent = StrUtil.format("{} \n```\n{}\n```",markdownContent, CommonUtils.cutStringByBytes(paramNoticeStr, 1000));
        }


        sendOaMsgTo9Ji(content, "13685,13495,13682",OaMesTypeEnum.YCTZ.getCode().toString());
        // 推送到企业微信 https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9ffe33c5-326e-480b-ac01-2564c5bc9725
        PlatformMessageUtil.sendWeChatWorkBotMarkdown("9ffe33c5-326e-480b-ac01-2564c5bc9725", markdownContent);
    }

    @DS(DataSourceConstants.OA_NC)
    private AppInfoVo getNcSmsconfig(long xtenant) {

        return smsConfigMapper.getNcSmsconfig(xtenant);
    }

    /**
     * 获取短信通道
     * @param areaId
     * @param channelType
     * @return
     */
    @Override
    public Integer getSmsChannelByTenant(Integer areaId, ESmsChannelTypeEnum channelType) {
        //获取短信通道 九机根据授权获取，输出根据优先获取门店信息表，如果没有则根据授权获取
        AreaInfo areaInfo = Optional.ofNullable(areaInfoClient.getAreaInfoById(areaId).getData()).orElseGet(AreaInfo::new);
        if (areaInfo == null) {
            return null;
        }
        Integer xTenant = areaInfo.getXtenant();
        Integer channel = 0;
        SmsChannelBo smsChannelInfo = new SmsChannelBo();

        AuthConfigService authConfigService = SpringUtil.getBean(AuthConfigService.class);
        OaSysConfigService sysConfigService = SpringUtil.getBean(OaSysConfigService.class);

        if (XtenantEnum.isSaasXtenant()) {
            smsChannelInfo = SpringUtil.getBean(AreaInfoService.class).getSmsChannelById(areaId);
        }
        AuthModel authModel = authConfigService.getAuthConfig().stream().filter(a -> Objects.equals(areaInfo.getAuthorizeId(), a.getId())).findFirst().orElseGet(AuthModel::new);
        switch (channelType) {
            case YXTD:
                if (XtenantEnum.isSaasXtenant()) {
                    channel = smsChannelInfo.getMarketChannel();
                    //先从门店获取，门店获取失败从授权获取
                } else {
                    channel = Optional.ofNullable(sysConfigService.getValueByCodeAndXtenant(SysConfigConstant.MARKETING_CHANNEL, xTenant))
                            .filter(StrUtil::isNotBlank).map(Integer::valueOf).orElse(channel);
                }
                if (ObjectUtil.defaultIfNull(channel, 0) == 0) {
                    channel = Optional.ofNullable(authModel.getMarketChannel()).filter(StrUtil::isNotBlank).map(Integer::valueOf).orElse(channel);
                }
                return channel;
            case YZMTD:
                if (XtenantEnum.isSaasXtenant()) {
                    channel = smsChannelInfo.getVCodeChannel();
                    //先从门店获取，门店获取失败从授权获取
                } else {
                    channel = Optional.ofNullable(sysConfigService.getValueByCodeAndXtenant(SysConfigConstant.VERIFICATION_CODE_CHANNEL, xTenant))
                            .filter(StrUtil::isNotBlank).map(Integer::valueOf).orElse(channel);
                }
                if (ObjectUtil.defaultIfNull(channel, 0) == 0) {
                    channel = Optional.ofNullable(authModel.getVCodeChannel()).filter(StrUtil::isNotBlank).map(Integer::valueOf).orElse(channel);
                }
                return channel;
            default:
                break;
        }

        return channel;
    }

}
