package com.jiuji.oa.oacore.thirdplatform.order.service;

import com.jiuji.oa.oacore.thirdplatform.order.bo.PreparationMealCompleteBO;
import com.jiuji.oa.oacore.thirdplatform.order.bo.SelectMeiTuanOrderDetailBO;
import com.jiuji.oa.oacore.thirdplatform.order.vo.*;
import com.sankuai.meituan.shangou.open.sdk.domain.SystemParam;
import com.sankuai.meituan.shangou.open.sdk.exception.SgOpenException;
import com.sankuai.meituan.shangou.open.sdk.response.SgOpenResponse;

import java.io.IOException;

/**
 * <AUTHOR>
 */
public interface MeiTuanSDKService {
    SystemParam createSystemParam(String orderId);
    /**
     * 获取美团订单详情
     * @param detailBO
     * @return
     * @throws IOException
     * @throws SgOpenException
     */
    SgOpenResponse getMeiTuanSgOpenResponse(SelectMeiTuanOrderDetailBO detailBO) throws IOException, SgOpenException;


    /**
     * 获取美团订单ID 查询是否可以国补
     * @param orderId
     * @return
     * @throws IOException
     * @throws SgOpenException
     */
    SgOpenResponse orderIdentificationGetRequest(String orderId) throws IOException, SgOpenException;

    /**
     * 美团国补订单发票查询
     * @param queryTipsRequest
     * @return
     * @throws IOException
     * @throws SgOpenException
     */
    SgOpenResponse queryTipsRequest(SelectInvoiceReq req) throws IOException, SgOpenException;


    /**
     * 美团发票chuang'chuan
     * @param req
     * @return
     * @throws IOException
     * @throws SgOpenException
     */
    SgOpenResponse saveInvoiceRequest(HandleInvoiceReq req) throws IOException, SgOpenException;

    SgOpenResponse orderIdentificationSaveRequest(NationalSupplementUpSnReq req) throws IOException, SgOpenException;

    /**
     * 拣选完成通知美团
     * @param preparationMealCompleteBO
     * @return
     * @throws IOException
     * @throws SgOpenException
     */
    SgOpenResponse preparationMealComplete(PreparationMealCompleteBO preparationMealCompleteBO) throws IOException, SgOpenException;


    /**
     * 从美团获取取消原因
     * @param orderId
     * @return
     */
    SgOpenResponse cancelReason(String orderId)throws IOException, SgOpenException;


    SgOpenResponse batchPullPhoneNumber(String orderId) throws IOException, SgOpenException;
    SgOpenResponse getOrderPrivacyPhone(String orderId) throws IOException, SgOpenException;

        /**
         * 同步门店营业时间到美团
         * @return
         */
    SgOpenResponse synchronizationBusinessHours(CreateMeiTuanSystem createMeiTuanSystem) throws IOException, SgOpenException;



}
