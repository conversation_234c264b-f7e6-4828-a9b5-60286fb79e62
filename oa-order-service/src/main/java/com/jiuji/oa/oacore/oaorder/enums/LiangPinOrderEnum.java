package com.jiuji.oa.oacore.oaorder.enums;

import com.jiuji.oa.oacore.oaorder.vo.res.LiangPinOrderEnumRes;
import com.jiuji.tc.utils.enums.EnumUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 16:12
 * @Description
 */
@Getter
@AllArgsConstructor
public enum LiangPinOrderEnum {
    SIZE_PIECE(EnumUtil.toEnumVOList(SizePiece.class), "sizePiece", "大小件",false),
    DISTRIBUTION_TYPE(EnumUtil.toEnumVOList(DistributionType.class), "distributionType", "配送方式",false),
    TIME_TYPE(EnumUtil.toEnumVOList(TimeType.class), "timeType", "提交时间",false),
    SUB_CHECK(EnumUtil.toEnumVOList(subCheck.class), "subCheck", "处理状态",false),
    LIMINT_CLINT(EnumUtil.toEnumVOList(LimintClint.class), "limintClint", "搜索类型",false),
    CUSTOMER_TYPE(EnumUtil.toEnumVOList(CustomerType.class), "customerType", "订单类别",false),
    SUB_PAY(EnumUtil.toEnumVOList(SubPayEnum.class), "subPay", "支付方式",true),
    ;
    /**
     * 编码
     */
    private final Object code;

    /**
     * 名称
     */
    private final String name;

    /**
     * 名称
     */
    private final String message;

    private Boolean ismulti;

    public static List<LiangPinOrderEnumRes> toEnumVOList() {
        List<LiangPinOrderEnumRes> enums = new ArrayList<>();
        for (LiangPinOrderEnum temp : LiangPinOrderEnum.values()) {
            LiangPinOrderEnumRes vo = new LiangPinOrderEnumRes();
            vo.setLabel(temp.getMessage());
            vo.setName(temp.getName());
            vo.setValue(temp.getCode());
            vo.setIsmulti(temp.getIsmulti());
            enums.add(vo);
        }
        return enums;
    }

    public static <T extends CodeMessageEnumInterface> String getMessageByCode(
            Class<subCheck> enumClass, Integer code) {
        for (subCheck temp : enumClass.getEnumConstants()) {
            if (temp.getCode() == null) {
                if (code == null) {
                    return temp.getMessage();
                }
            } else if (temp.getCode().equals(code)) {
                return temp.getMessage();
            }
        }
        return null;
    }


    @Getter
    @AllArgsConstructor
    public enum SizePiece implements com.jiuji.tc.utils.enums.CodeMessageEnumInterface {
        //ALL(0, "全部"),
        UNREVIEWED(1, "大件"),
        AUDIT1(2, "小件");

        /**
         * 编码
         */
        private final Integer code;
        /**
         * 名称
         */
        private final String message;
    }

    @Getter
    @AllArgsConstructor
    public enum DistributionType implements com.jiuji.tc.utils.enums.CodeMessageEnumInterface {
        //DELIVERY_NULL(0, "全部"),
        DELIVERY_STORE(1, "到店自取"),
        DELIVERY_JIUJI_EXPRESS(2, "九机快送"),
        DELIVERY_PICK_UP(3, "自提点"),
        DELIVERY_EXPRESS(4, "快递运输"),
        DELIVERY_EXPEDITED(5, "加急配送"),
        DELIVERY_THIRD(6, "第三方派送");

        /**
         * 编码
         */
        private final Integer code;
        /**
         * 名称
         */
        private final String message;
    }


    @Getter
    @AllArgsConstructor
    public enum TimeType implements com.jiuji.tc.utils.enums.CodeMessageEnumInterface {
        SUBMIT_TIME(1, "订机时间"),
        AUDIT_TIME1(2, "出库时间"),
        AUDIT_TIME2(3, "交易时间");

        /**
         * 编码
         */
        private final Integer code;
        /**
         * 名称
         */
        private final String message;
    }


    @Getter
    @AllArgsConstructor
    public enum LimintClint implements com.jiuji.tc.utils.enums.CodeMessageEnumInterface {
        SEARCH_1(1, "良品单号"),
        SEARCH_2(2, "电话号码"),
        SEARCH_3(3, "商品名称"),
        SEARCH_4(4, "库存编号(mkc_id)"),
        SEARCH_5(5, "机器编号(orderid)"),
        SEARCH_6(6, "串号(imei)"),
        SEARCH_7(7, "商品ID"),
        SEARCH_8(8, "skuid(ppid)"),
        SEARCH_9(9, "订单备注"),
        SEARCH_10(10, "交易人"),
        SEARCH_11(11, "销售人");

        /**
         * 编码
         */
        private final Integer code;
        /**
         * 名称
         */
        private final String message;
    }


    @Getter
    @AllArgsConstructor
    public enum subCheck implements com.jiuji.tc.utils.enums.CodeMessageEnumInterface {
        HAVE_IN_HAND(10, "进行中"),
        UNCONFIRMED(11, "未确认"),
        CONFIRMED(1, "已确认"),
        ISSUED(2, "已出库"),
        ARREARS(6, "欠款"),
        WAITING_FOR_CONFIRMATION(5, "等待确认"),
        COMPLETED(3, "已完成"),
        PENDING(7, "待处理"),
        UNSUBSCRIBE(8, "退订"),
        REFUND(9, "退款"),
        DELETED(4, "已删除");

        /**
         * 编码
         */
        private final Integer code;
        /**
         * 名称
         */
        private final String message;
    }

    @Getter
    @AllArgsConstructor
    public enum CustomerType implements com.jiuji.tc.utils.enums.CodeMessageEnumInterface {
        //ALL(0, "全部"),
        CUSTOMER(1, "良品单"),
        RECOVERY(3, "转售单(回收机退回)"),
        SUPPLIER(2, "转售单(供货商)"),
        BIDDER(4, "转售单(竞拍商)");

        /**
         * 编码
         */
        private final Integer code;
        /**
         * 名称
         */
        private final String message;
    }


    @Getter
    @AllArgsConstructor
    public enum StateColor implements com.jiuji.tc.utils.enums.CodeMessageEnumInterface {
        SUBMIT_TIME("#0BBE69", "绿色"),
        AUDIT_TIME1("#F15643", "红色"),
        AUDIT_TIME2("#239DFC", "蓝色");

        /**
         * 编码
         */
        private final String code;
        /**
         * 名称
         */
        private final String message;
    }
}
