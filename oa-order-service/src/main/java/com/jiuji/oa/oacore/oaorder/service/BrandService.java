package com.jiuji.oa.oacore.oaorder.service;

import com.jiuji.oa.oacore.oaorder.po.Brand;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jiuji.oa.oacore.oaorder.vo.res.BrandRes;
import com.jiuji.oa.oacore.promocode.bo.CommonSwitchBO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-06
 */
public interface BrandService extends IService<Brand> {
    /**
     * 通过分类id获取品牌
     * @param cids
     * @return
     */
   List<BrandRes> getBrandsByCid(List<Integer> cids);

    List<CommonSwitchBO> getByIds(String limitIdsStr);

    Map<Integer, List<BrandRes>> getCidAndBrandsMap(List<Integer> cids);
}
