package com.jiuji.oa.oacore.common.constant;

/**
 * 请求attr的key
 * @author: xie<PERSON>ongkun
 * @date: 2022/4/3
 */
public class RequestAttrKeys {
    /**
     * 错误信息临时存到request attribute中 key
     */
    public static final String REQUEST_ATTR_ERROR = "__request_attr_error__";
    /**
     * 业务日志临时存到request attribute中 key
     */
    public static final String REQUEST_ATTR_BUSINESS_LOG = "__request_attr_business_log__";

    /**
     * 用户级别的文件缓存key 结果为包含参数和用户信息的md5值
     */
    public static final String USER_EXPORT_FILE_CACHE_KEY = "__request_user_level_cache__";

    /**
     * 用户级别的文件缓存key 结果为包含参数和用户信息的md5值
     */
    public static final String NOT_NOTICE_USER = "__request_not_notice_user__";

    /**
     * 用户级别的文件缓存key 结果为包含参数和用户信息的md5值
     */
    public static final String FASTJSON_REQUEST_BODY = "__request_fastjson_requestbody_obj__";

    /**
     * 用户级别的文件缓存key 结果为包含参数和用户信息的md5值
     */
    public static final String API_STOP_WATCH = "__request_api_stop_watch__";

    /**
     * 用户信息的来源保存
     */
    public static final String OA_USER_FROM_SOURCE = "__request_attr_oa_user_from_source__";

    /**
     * 当前请求缓存前缀 结合StrUtil.format使用
     */
    public static final String REQUEST_CACHE_PREV = "__request_cache_prev_{}__";

    /**
     * 接口响应计时
     */
    public static final String RESPONSE_STOP_WATCH = "__request_response_stop_watch__";

    /**
     * 宝尊门店id值传递
     */
    public static final String BAOZUN_AREA_ID = "__request_baozun_areaId__";

    /**
     * 日志消息传递
     */
    public static final String LOG_PARAM_NOTICE = "__request_log_param_notice__";
}
