package com.jiuji.oa.oacore.thirdplatform.order.service;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.oaorder.vo.ThirdOrderInfoRes;
import com.jiuji.oa.oacore.thirdplatform.order.bo.*;
import com.jiuji.oa.oacore.thirdplatform.order.entity.Order;
import com.jiuji.oa.oacore.thirdplatform.order.entity.OrderItem;
import com.jiuji.oa.oacore.thirdplatform.order.vo.*;
import com.jiuji.oa.oacore.thirdplatform.order.vo.meituan.ThirdDeliveryMeituanOrderVO;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum;
import com.jiuji.tc.utils.enums.coupon.SubSubTypeEnum;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;

/**
 * 订单管理接口类
 *
 * <AUTHOR>
 */
public interface OrderService extends IService<Order> {

    /**
     * 获取三方平台信息
     * @param orderId
     * @return
     */
    List<Order> getOrderList(String orderId);

    /**
     * 获取三方平台信息
     * @param subId
     * @return
     */
    List<Order> getOrderListBySubId(String subId);

    /**
     * 创建美团订单
     * @param param
     * @return
     */
    R createMeituanOrder(Map<String,Object> param);

    /**
     * 创建第三方订单
     * @param param
     * @return
     */
    R<ThirdPlatformOrderRes> createThirdPlatformOrder(ThirdPlatformOrderReq param);

    /**
     * 从美团获取取消原因
     * @param orderId
     * @return
     */
    String getCancelReason(String orderId);

    /**
     * 分页查询美团订单
     * @param search
     * @return
     */
    Page<OrderVO> listByPage(OrderSearchBO search);

    /**
     * 订单导出
     * @param req
     * @param response
     * @return
     */
    R exportExcel(OrderSearchBO req, HttpServletResponse response);

    /**
     * 查询用户参与x员任选外键活动次数
     * @param userId
     * @param actId
     * @return
     */
    R<Integer> queryUserXSelectYCount(Integer userId, Integer actId);

    /**
     * 查询用户参与x元任选y件 活动次数
     * @param userId    用户id
     * @param actIds     活动ld列表
     * @return  key：活动id，value：参与次数
     */
    Map<String, Integer> queryUserXSelectYCountMap(Integer userId, String actIds);

    /**
     * 获取美团取消订单
     *
     * @param params 美团的请求数据
     * @return
     */
    boolean cancelMeiTuanOrder(Map<String, Object> params);


    /**
     * 同步美团发货状态和配送信息（不支持同步骑手信息）
     *
     * @param param mq出库信息
     */
    void ecommerceOrderLogisticsSync(EcommerceOrderParam param);

    /**
     * 发送消息通知给门店的管理层和销售
     * @param order
     *
     * @return
     */
    R<Object> toMessageNotification(Order order);

    R<Object> toMessageNotification(Order order, Set<Integer> ch999Ids, String msgSuffix);

    /**
     * 发送订单消息通知
     * @param subId
     * @param orderType 0 新机单 1 良品单 2 平台单号
     * @param msg
     * @param userIdList
     * @return
     */
    R<Object> sendOrderNotice(String subId, Integer orderType, String msg, Set<Integer> userIdList);

    /**
     * 构建订单
     * @param order order
     * @param req req
     * @return
     */
    List<OrderItem> buildOrderItemParam(Order order, OrderReq req);

    Integer getOrderAutoId(String platCode, Long orderId, Consumer<LambdaQueryChainWrapper<Order>> wrapperCallback);

    /**
     * 提交OA订单
     * @param id id
     * @param estimateArrivalTime estimateArrivalTime
     * @return
     */
    boolean submitOaOrder(Integer id, Date estimateArrivalTime,OrderExtendVO orderExtendVO);

    void computeAreaIdList(List<Integer> areaIdList, List<String> areaIdListStrings, List<AreaInfo> areaInfos);

    /**
     * 获取下单门店id
     * @param productKcBOS
     * @param areaIdList
     * @param orderItem
     * @return
     */
    Integer computeOrderAreaId(List<ProductKcBO> productKcBOS, List<Integer> areaIdList, List<OrderItem> orderItem);

    /**
     * 提交OA订单 (良品)
     * @param id id
     * @param estimateArrivalTime estimateArrivalTime
     * @return
     */
    boolean submitOaOrderByRecover(Integer id, Date estimateArrivalTime);


    /**
     * 下单即时同步库存到美团
     * @param order
     * @param ppids
     * @param areaInfo
     */
    void orderTimeSyncToMetiuan(Order order, List<Integer> ppids, AreaInfo areaInfo);

    String computeAreaForJD(ComputeAreaForJDReq req);

        /**
         * 自配送商家同步发货状态和配送信息（推荐）
         *
         * @param logisticsSync mq出库信息
         */
    R<Boolean> orderLogisticsSync(LogisticsSync logisticsSync);

    /**
     * 根据id查询订单信息
     */
    List<Order> getListOrderById(String orderId);

    /**
     * 查询美团订单状态
     * @param subId subId
     * @return ture 是取消 false是未取消
     */
    R<Boolean> orderViewStatus(Integer subId);

    /**
     * 一键建单
     *
     * @param search
     * @return
     */
    String summitOaOrderManual(OrderSearchBO search);


    boolean payThirdPlatformOrder(ThirdPlatformOrderPayReq req);

    /**
     * 优惠码使用
     * @param req
     * @return
     */
    R youhuimaUse(YouhuimaUseReq req);

    /**
     * 第三方平台创建oa订单
     * @param req
     * @return
     */
    R<ThirdPlatformOrderRes> createThirdPlatformOaOrder(ThirdPlatformOaOrderReq req);

    /**
     * 取消订单
     *
     * @param param
     * @return
     */
    boolean cancelThirdPlatformOrder(ThirdPlatformOrderCancelReq param);

    /**
     * 取消oa订单
     *
     * @param param
     * @return
     */
    boolean cancelThirdPlatformOaOrder(ThirdPlatformOaOrderCancelReq param);

    /**
     * 更新subId
     * @param req
     * @return
     */
    boolean updateSubIdById(ThirdPlatformOrderUpdateSubIdReq req);

    /**
     * 追加提示消息到订单中
     * @param id
     * @param template
     * @param params
     * @return
     */
    boolean addOrderSubMessage(Integer id, CharSequence template, Object... params);

    /**
     * 模拟用户信息
     */
    OaUserBO simulateUser(AreaInfo areaInfo, String userName);

    /***
     * 查询订单信息
     * @param orderId
     * @param platCode
     * @return
     */
    List<Order> getOrderListByOrderIdAndPlatCode(String orderId, String platCode);

    /**
     * 查询物流信息
     * @param orderId
     * @param thirdPlatDy
     */
    OrderLogisticsBO getLogisticsByOrderId(String orderId, String thirdPlatDy);
    /**
     * 查询物流单跑腿信息
     * @param wuliuId
     */
    LogisticsPtInfoBO getLogisticsPtInfoByWuliuId(Integer wuliuId);

    void scalperWarning(ScalperWarningReq req);

    void lossOrderMessagePush(LossOrderMessagePushParam data);

    /**
     * 获取三方订单信息 通过 subId, subType, businessType
     *
     * @param subId
     * @param subType
     * @param businessType
     * @return
     * @see SubSubTypeEnum
     * @see BusinessTypeEnum
     */
    R<ThirdOrderInfoRes> getThirdOrderInfo(Integer subId, Integer subType, Integer businessType);

    /**
     * 美团专用解析json方法
     *
     * @param obj
     * @param deep di
     * @return
     */
    static Object toJSON(Object obj, int deep) {
        if(deep <0 || obj == null){
            //不再继续递归
            return obj;
        }

        if(obj instanceof String && JSONUtil.isJson((String) obj)){
            return toJSON(JSON.parse((String) obj), deep-1);
        }
        if(obj instanceof List){
            List array = (List) obj;
            for (int i = 0; i < array.size(); i++) {
                Object origin = array.get(i);
                Object json = toJSON(origin, deep - 1);
                if(json != origin){
                    array.set(i, json);
                }
            }
        }else if(obj instanceof Map){
            Map map = (Map) obj;
            Set<Map.Entry> entrySet = map.entrySet();
            entrySet.forEach(entry -> {
                Object json = toJSON(entry.getValue(), deep - 1);
                if(entry.getValue() != json){
                    entry.setValue(json);
                }
            });
        }

        return obj;
    }

    ThirdDeliveryMeituanOrderVO selectThirdDeliveryOrderBySub(Integer subId);
}
