/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.oacore.thirdplatform.order.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.oacore.thirdplatform.order.bo.LogisticsPtInfoBO;
import com.jiuji.oa.oacore.thirdplatform.order.bo.OrderLogisticsBO;
import com.jiuji.oa.oacore.thirdplatform.order.bo.OrderSearchBO;
import com.jiuji.oa.oacore.thirdplatform.order.bo.ProductKcBO;
import com.jiuji.oa.oacore.thirdplatform.order.entity.Order;
import com.jiuji.oa.oacore.thirdplatform.order.vo.OrderVO;
import com.jiuji.oa.oacore.thirdplatform.order.vo.ScalperWarningBO;
import com.jiuji.oa.oacore.thirdplatform.order.vo.UserXSelectYActInfo;
import com.jiuji.oa.oacore.thirdplatform.order.vo.meituan.ThirdDeliveryMeituanOrderVO;
import com.jiuji.tc.foundation.db.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OrderMapper extends BaseMapper<Order> {

    /**
     * 根据id更新支付状态和描述
     * @param status
     * @param message
     * @param id
     * @param subId
     */
    @DS("smallpro_write")
    void updateStatusAndMessageById(@Param("payStatus") Integer status, @Param("message") String message, @Param("id") Integer id,@Param("subId") Integer subId);

    /**
     * 派送单状态修改
     * @param subId
     * @return
     */
    @DS("smallpro_write")
    int updateSubAddress(@Param("subId") Integer subId);





    /**
     * 分页查询订单列表
     * @param search
     * @param page
     * @return
     */
    List<OrderVO> orderList(@Param("search") OrderSearchBO search,@Param("page")  Page<OrderVO> page);

    /**
     *
     * @param userId
     * @param actId
     * @return
     */
    Integer queryUserXSelectYCount(@Param("userId") Integer userId, @Param("actId") Integer actId);


    @SqlParser(filter = true)
    List<UserXSelectYActInfo> queryUserXSelectYCountList(@Param("userId") Integer userId, @Param("actIds") List<String> actIds);


    /**
     * 获取门店的真实库存
     *
     * @param areaId areaId
     * @param ppriceid ppriceid
     */
    @SqlParser(filter = true)
    List<ProductKcBO> getRealInventory(@Param("areaIds") List<Integer> areaId, @Param("ppriceids") List<Integer> ppriceid);

    /**
     * 获取门店的店长与主管的人员列表
     * @param areaId
     * @return
     */
    List<Integer> getPersonneList(@Param("areaId") Integer areaId);

    /**
     * 获取门店人员列表
     * @param areaId
     * @return
     */
    List<Integer> getAllPersonneList(@Param("areaId") Integer areaId);

    /**
     * 分页查询良品订单列表
     * @param search
     * @param page
     * @return
     */
    List<OrderVO> recoverOrderList(OrderSearchBO search, Page<OrderVO> page);

    /**
     * 根据订单id查询抖音订单
     * @param orderId orderId
     * @return
     */
    List<Order> getListOrderById(@Param("orderId") String orderId);

    /**
     * 根据订单id查询抖音订单
     * @param ppidList
     * @return
     */
    List<Integer> getGiftPpidList(List<Integer> ppidList);

    /**
     * 查询物流信息
     * @param orderId
     * @param platCode
     * @return
     */
    OrderLogisticsBO getLogisticsByOrderId(@Param("orderId") String orderId,
                                           @Param("platCode") String platCode);

    /**
     * 查询物流单跑腿配送人信息
     * @param wuliuId
     * @return
     */
    LogisticsPtInfoBO getLogisticsPtInfoByWuliuId(@Param("wuliuId") Integer wuliuId);

    /**
     * 查询订单串号列表
     * @param subId
     * @return
     */
    List<String> getImeiListBySubId(@Param("subId") Long subId);

    /**
     * 查询地址是否存在刷单的嫌疑
     *
     * @param address
     * @param productIds
     * @param platCode
     * @param startTime
     * @return
     */
    ScalperWarningBO getScalperWarning(@Param("address") String address,
                                       @Param("productIds") List<Integer> productIds,
                                       @Param("platCode") String platCode,
                                       @Param("startTime") LocalDateTime startTime
                                       );

    BigDecimal getLossSum(@Param("subId")Long subId);

    BigDecimal getLossSumJd(@Param("subId")Long subId);


    /**
     * 第三方派送美团订单
     * @param subId
     * @return
     */
    ThirdDeliveryMeituanOrderVO selectThirdDeliveryOrderBySub(@Param("subId") Integer subId);

}
