package com.jiuji.oa.oacore.common.util;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.jiuji.fileservice.server.sdk.utils.RSAUtils;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.vo.FileResData;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * 小文件服务器工具
 *
 * <AUTHOR>
 * @date 2022/2/15 10:27
 */
@Slf4j
public final class MiniFileServerUtil {

    private static final String APPID ="190002";
    private static final String PUBLICKEY ="MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALZk8XdJlRaATmb01CrWRbAu2Civz2hNZIW25RgyGCerg5YIMoDLP18iasDIGsc6iJy2YQeOTp60rkmRGJiYAGECAwEAAQ==";

    private static final String imageUrl = "http://**************:9333";
    private static final String IMAGEURL = "https://upload.9xun.com";
    /**
     * 易腾上传当前服务器域名小文件服务器
     *
     * @param file 文件
     * @param ttl 过期时间(小时)
     * @return fid
     * @throws IOException io异常
     */
    public static FileResData uploadFile(File file, Integer ttl) throws IOException {
        return uploadFile(IMAGEURL+"/api/upload/v1", file, ttl);
    }

    public static FileResData uploadFileWithToken(String token,File file, Integer ttl) throws IOException {
        return uploadFileWithToken(token,IMAGEURL+"/api/upload/v1", file, ttl);
    }

    /**
     * 上传小文件服务器
     *
     * @param reqUrl        地址
     * @param file 文件
     * @return fid
     * @throws IOException io异常
     */
    public static FileResData uploadFile(String reqUrl, File file, Integer ttl) throws IOException {
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        String token = getToken(request);
        String boundary = "---" + UUID.randomUUID();
        Map<String, Object> param = new HashMap<>();
        param.put("token", token);
        param.put("app_id", "190002");
        param.put("collection", "staff");
        if (ttl != null) {
            param.put("ttl", ttl);
        }
        String body = HttpUtil.createPost(reqUrl)
                .contentType("multipart/form-data; boundary=" + boundary)
                .form("file",file)
                .form(param)
                .execute()
                .body();
        log.info("body = {}", body);
        R<FileResData> fileResDataResUlt = JSONUtil.toBean(body, new TypeReference<R<FileResData>>() {
        }, true);
        return fileResDataResUlt.getData();
    }


    /**
     * 上传小文件服务器
     *
     * @param reqUrl        地址
     * @param file 文件
     * @return fid
     * @throws IOException io异常
     */
    public static FileResData uploadFileWithToken( String token,String reqUrl, File file, Integer ttl) throws IOException {
        String boundary = "---" + UUID.randomUUID();
        Map<String, Object> param = new HashMap<>();
        param.put("token", token);
        param.put("app_id", "190002");
        param.put("collection", "staff");
        if (ttl != null) {
            param.put("ttl", ttl);
        }
        HttpResponse httpResponse = HttpUtil.createPost(reqUrl)
                .contentType("multipart/form-data; boundary=" + boundary)
                .form("file", file)
                .form(param)
                .execute();
        String body = httpResponse
                .body();
        httpResponse.close();
        log.info("body = {}", body);
        R<FileResData> fileResDataResUlt = JSONUtil.toBean(body, new TypeReference<R<FileResData>>() {
        }, true);
        return fileResDataResUlt.getData();
    }

    public static String getToken(HttpServletRequest request) {

        Integer appId = Integer.valueOf(APPID);
        String publicKey = PUBLICKEY;
        try {
            String xtenant = request.getHeader("xtenant");
            if (xtenant == null) {
                xtenant = "";
            }
            return RSAUtils.encrypt(String.format("%d_%d_%s", appId, System.currentTimeMillis(), xtenant), publicKey);
        } catch (Exception var4) {
            log.error("生成token失败，appId = {}", appId, var4);
        }
        return StrUtil.EMPTY;
    }

    /**
     * file 转 MultipartFile
     *
     * @param file 文件
     * @return MultipartFile
     */
    public static MultipartFile toMultipartFile(File file) {
        MultipartFile result;
        byte[] buffer = new byte[4096];
        int n;
        InputStream inputStream = null;
        OutputStream os = null;
        try {
            FileItemFactory factory = new DiskFileItemFactory((int) file.length(), file.getParentFile());
            FileItem fileItem = factory.createItem(file.getName(), Files.probeContentType(file.toPath()), false, file.getName());
            inputStream = new FileInputStream(file);
            os = fileItem.getOutputStream();
            while ((n = inputStream.read(buffer, 0, 4096)) != -1) {
                os.write(buffer, 0, n);
            }
            result = new CommonsMultipartFile(fileItem);
        } catch (IOException e) {
            throw new CustomizeException("file转MultipartFile异常");
        } finally {
            IOUtils.closeQuietly(inputStream);
            IOUtils.closeQuietly(os);
        }
        return result;
    }

    /**
     * MultipartFile 转 file
     *
     * @param multipartFile 文件
     * @return File
     */
    public static File transferToFile(MultipartFile multipartFile, String excelName) {
        File file = null;
        try {
            String originalFilename = multipartFile.getOriginalFilename();
            String[] filename = originalFilename.split("\\.");
            file=File.createTempFile(excelName  + "temp",  "." + filename[1]);
            multipartFile.transferTo(file);
            file.deleteOnExit();
        } catch (IOException e) {
            log.error("MultipartFile转file异常", e);
        }
        return file;
    }

    /**
     * 通过name获取config propertie
     */
    public static String getPropertyByName(String name) {
        return StringUtils.isEmpty(name) ? "" : SpringUtil.getApplicationContext().getEnvironment()
                .getProperty(name);
    }
}
