/*
 *    Copyright © 2006 - 2020 九机网 All Rights Reserved
 *
 */

package com.jiuji.oa.oacore.thirdplatform.store.bo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jiuji.oa.oacore.thirdplatform.common.enums.PlatfromEnum;
import com.jiuji.tc.utils.fastjson.TrimDeSerializeCodec;
import com.jiuji.oa.oacore.thirdplatform.common.enums.PlatfromEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 商户配置Entity
 *
 * <AUTHOR>
 * @date 2021-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "商户配置")
public class StoreAddBO extends Model<StoreAddBO> {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "商户编码")
    @NotNull(message = "商户编码不能为空")
    @JSONField(deserializeUsing = TrimDeSerializeCodec.class)
    private String tenantCode;

    /**
     * @see PlatfromEnum
     * 平台编码（JD-京东;MT-美团）
     */
    @ApiModelProperty(value = "平台编码")
    @NotBlank(message = "平台编码不能为空")
    @JSONField(deserializeUsing = TrimDeSerializeCodec.class)
    private String platCode;

    private List<StoreItemBO> itemList;

    @Data
    public static class StoreItemBO implements Serializable {
        private static final long serialVersionUID = 2L;
        @ApiModelProperty(value = "本地门店编码")
        @JSONField(deserializeUsing = TrimDeSerializeCodec.class)
        private String areaCode;

        @ApiModelProperty(value = "平台门店编码")
        @JSONField(deserializeUsing = TrimDeSerializeCodec.class)
        private String storeCode;

        @ApiModelProperty(value = "库存关联门店")
        private String associatedStores;

        @ApiModelProperty(value = "库存关联门店")
        private Boolean associatedStoresFlag;

        /**
         * 大件 供应商id
         */
        private Integer largeChannelId;

        /**
         * 小件 供应商id
         */
        private Integer smallChannelId;

        /**
         * 配送方式
         */
        private Integer deliveryType;
    }

}
