package com.jiuji.oa.oacore.yearcardtransfer.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jiuji.oa.oacore.partner.evaluate.vo.req.PageBaseReq;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 苹果GB账号配置
 * <AUTHOR>
 */
@Data
public class AreaGuestBookAccountQuery extends PageBaseReq {
    /**
     * 主键ID
     */
    private Integer configId;

    /**
     * 所属门店
     */
    private List<Integer> showAreaIdList;

    /**
     *  account 账号
     *  inUser  添加人
     */
    private String queryKey;
    private String queryValue;


    /**
     * 启用禁用
     */
    private Boolean enableStatus;


    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",shape = JsonFormat.Shape.STRING)
    private LocalDateTime createTimeStart;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeEnd;








}
