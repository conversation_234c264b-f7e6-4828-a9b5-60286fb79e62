package com.jiuji.oa.oacore.yearcardtransfer.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.jiuji.infra.saas.manager.sdk.v2.response.OrderItem;
import com.jiuji.oa.oacore.common.bo.OaUserBO;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.util.CommonUtil;
import com.jiuji.oa.oacore.goldseed.service.AreaInfoService;
import com.jiuji.oa.oacore.thirdplatform.order.entity.AreaInfo;
import com.jiuji.oa.oacore.thirdplatform.order.service.IAreaInfoService;
import com.jiuji.oa.oacore.yearcardtransfer.entity.AreaGuestBookAccount;

import com.jiuji.oa.oacore.yearcardtransfer.entity.AreaGuestBookAccountLog;
import com.jiuji.oa.oacore.yearcardtransfer.mapper.AreaGuestBookAccountMapper;
import com.jiuji.oa.oacore.yearcardtransfer.service.AreaGuestBookAccountLogService;
import com.jiuji.oa.oacore.yearcardtransfer.service.AreaGuestBookAccountService;
import com.jiuji.oa.oacore.yearcardtransfer.vo.AreaGuestBookAccountQuery;
import com.jiuji.oa.oacore.yearcardtransfer.vo.AreaGuestBookAccountVO;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
public class AreaGuestBookAccountServiceImpl extends ServiceImpl<AreaGuestBookAccountMapper, AreaGuestBookAccount> implements AreaGuestBookAccountService {

    @Resource
    private IAreaInfoService areaInfoService;
    @Resource
    private AreaGuestBookAccountLogService areaGuestBookAccountLogService;


    @Override
    public IPage<AreaGuestBookAccountVO> page(AreaGuestBookAccountQuery req) {
        LambdaQueryWrapper<AreaGuestBookAccount> query = new LambdaQueryWrapper<>();
        if(CommonUtil.isNotNullZero(req.getConfigId())){
            query.eq(AreaGuestBookAccount::getConfigId, req.getConfigId());
        }
        if(null != req.getEnableStatus()){
            query.eq(AreaGuestBookAccount::getEnableStatus, req.getEnableStatus());
        }
        if(CollUtil.isNotEmpty(req.getShowAreaIdList())){
            query.in(AreaGuestBookAccount::getShowAreaId, req.getShowAreaIdList());
        }
        if(null != req.getCreateTimeStart() && null != req.getCreateTimeEnd()){
            query.ge(AreaGuestBookAccount::getCreateTime, req.getCreateTimeStart());
            query.le(AreaGuestBookAccount::getCreateTime, req.getCreateTimeEnd());
        }
        if(StringUtils.isNotBlank(req.getQueryKey()) && StringUtils.isNotBlank(req.getQueryValue())){
            // 去除空格
            req.setQueryValue(req.getQueryValue().trim());
            if("account".equals(req.getQueryKey())){
                query.like(AreaGuestBookAccount::getGuestBookAccount, req.getQueryValue());
            }
            if("inUser".equals(req.getQueryKey())){
                query.eq(AreaGuestBookAccount::getInUser, req.getQueryValue());
            }
        }


        Page<AreaGuestBookAccount> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        page.setDesc("config_id");
        IPage<AreaGuestBookAccount> ipage = baseMapper.selectPage(page, query);

        IPage<AreaGuestBookAccountVO> result = new Page<>();
        BeanUtils.copyProperties(ipage, result);

        if(CollUtil.isEmpty(ipage.getRecords())){
            return result;
        }

        List<Integer> areaIdList = new ArrayList<>();
        List<Integer> areaIdListV1 = ipage.getRecords().stream()
                .filter(e -> StringUtils.isNotBlank(e.getAffiliatedAreaId()))
                .flatMap(vo -> Arrays.stream(vo.getAffiliatedAreaId().split(",")))
                .filter(idStr -> !idStr.trim().isEmpty())
                .map(Integer::parseInt)
                .distinct()
                .collect(Collectors.toList());
        if(CollUtil.isNotEmpty(areaIdListV1)){
            areaIdList.addAll(areaIdListV1);
        }

        List<Integer> showAreaIdList = ipage.getRecords().stream()
                .map(AreaGuestBookAccount::getShowAreaId)
                .distinct()
                .collect(Collectors.toList());
        if(CollUtil.isNotEmpty(showAreaIdList)){
            areaIdList.addAll(showAreaIdList);
        }

        Map<Integer, AreaInfo> areaInfoMap = areaInfoService.getAreaMap(areaIdList);

        List<AreaGuestBookAccountVO> list = Lists.newArrayListWithCapacity(ipage.getRecords().size());
        ipage.getRecords().forEach(e -> {
            AreaGuestBookAccountVO vo = new AreaGuestBookAccountVO();
            // 赋值
            BeanUtils.copyProperties(e, vo);
            // 处理门店信息
            handleAreaInfo(vo, areaInfoMap);

            list.add(vo);
        });
        result.setRecords(list);
        return result;
    }

    /**
     * 处理门店相关信息
     * @param vo
     */
    private void handleAreaInfo(AreaGuestBookAccountVO vo,  Map<Integer, AreaInfo> areaInfoMap){
        // 所属门店
        if(CommonUtil.isNotNullZero(vo.getShowAreaId())){
            AreaInfo showArea = areaInfoMap.get(vo.getShowAreaId());
            if(null != showArea){
                vo.setShowAreaName(showArea.getAreaName());
            }
        }
        // 关联门店
        if(StringUtils.isNotBlank(vo.getAffiliatedAreaId())){
            List<Integer> affiliatedAreaIdList = ListUtil.toList(vo.getAffiliatedAreaId().split(",")).stream().map(Integer::parseInt).collect(Collectors.toList());
            vo.setAffiliatedAreaIdList(affiliatedAreaIdList);

            if(CollUtil.isNotEmpty(affiliatedAreaIdList)){
                List<String> affiliatedAreaCodeList = Lists.newArrayListWithCapacity(affiliatedAreaIdList.size());
                affiliatedAreaIdList.forEach(areaId -> {
                    AreaInfo affiliatedArea = areaInfoMap.get(areaId);
                    if(null != affiliatedArea){
                        affiliatedAreaCodeList.add(affiliatedArea.getArea());
                    }
                });
                if(CollUtil.isNotEmpty(affiliatedAreaCodeList)){
                    vo.setAffiliatedAreaCodeStr(StringUtils.join(affiliatedAreaCodeList, ","));
                }
            }
        }
    }

    private String getAreaCodeStr(String areaIdStr){

        if(StringUtils.isBlank(areaIdStr)){
            return "";
        }

        List<Integer> areaIdList = ListUtil.toList(areaIdStr.split(",")).stream().map(Integer::parseInt).collect(Collectors.toList());
        Map<Integer, AreaInfo> areaInfoMap = areaInfoService.getAreaMap(areaIdList);

        if(CollUtil.isEmpty(areaIdList)){
            return "";
        }

        List<String> areaCodeList = Lists.newArrayListWithCapacity(areaIdList.size());
        areaIdList.forEach(areaId -> {
            AreaInfo affiliatedArea = areaInfoMap.get(areaId);
            if(null != affiliatedArea){
                areaCodeList.add(affiliatedArea.getArea());
            }
        });

        if(CollUtil.isEmpty(areaCodeList)){
            return "";
        }
        return StringUtils.join(areaCodeList, ",");
    }


//    @Override
//    public IPage<AreaGuestBookAccountLog> pageLog(Integer configId, Integer currentPage, Integer pageSize) {
//        LambdaQueryWrapper<AreaGuestBookAccountLog> query = new LambdaQueryWrapper<>();
//        if(CommonUtil.isNotNullZero(configId)){
//            throw new CustomizeException("业务参数为空");
//        }
//        query.eq(AreaGuestBookAccountLog::getBusinessId, configId);
//        query.orderByAsc(AreaGuestBookAccountLog::getCreateTime);
//
//        IPage<AreaGuestBookAccountLog> ipage = new Page<>(currentPage, pageSize);
//        return areaGuestBookAccountLogService.page(ipage, query);
//    }

    @Override
    public AreaGuestBookAccountVO getDetail(Integer configId) {
        if(null == configId){
            throw new CustomizeException("业务参数为空");
        }
        AreaGuestBookAccount account = baseMapper.selectById(configId);
        if(null == account){
            throw new CustomizeException("数据不存在！");
        }

        AreaGuestBookAccountVO vo = new AreaGuestBookAccountVO();
        BeanUtils.copyProperties(account, vo);

        List<Integer> areaIdList = new ArrayList<>();
        if(CommonUtil.isNotNullZero(vo.getShowAreaId())){
            areaIdList.add(vo.getShowAreaId());
        }
        if(StringUtils.isNotBlank(vo.getAffiliatedAreaId())){
            List<Integer> affiliatedAreaIdList = ListUtil.toList(vo.getAffiliatedAreaId().split(",")).stream().map(Integer::parseInt).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(affiliatedAreaIdList)){
                areaIdList.addAll(affiliatedAreaIdList);
            }
        }
        Map<Integer, AreaInfo> areaInfoMap = areaInfoService.getAreaMap(areaIdList);

        // 处理门店信息
        handleAreaInfo(vo, areaInfoMap);

        // 查询日志
        vo.setLogList(areaGuestBookAccountLogService.listByBusinessId(configId));

        return vo;
    }

    @DS("smallpro_write")
    @Override
    public boolean updateEnableStatus(Integer configId, Boolean enableStatus, OaUserBO oaUserBO) {
        if(null == configId || null == enableStatus) {
            throw new CustomizeException("业务参数为空");
        }
        AreaGuestBookAccount account = baseMapper.selectById(configId);
        if(null == account){
            throw new CustomizeException("数据不存在");
        }
        //幂等
        if(enableStatus.equals(account.getEnableStatus())){
            return true;
        }
        AreaGuestBookAccount updateData = new AreaGuestBookAccount();
        updateData.setConfigId(account.getConfigId());
        updateData.setEnableStatus(enableStatus);
        updateData.setUpdateTime(LocalDateTime.now());
        baseMapper.updateById(updateData);

        //日志
        String logStr = (enableStatus ? "启用" : "停用") + "苹果GB账号";
        areaGuestBookAccountLogService.saveLog(configId, oaUserBO.getUserName(), logStr);
        return true;
    }

    @DS("smallpro_write")
    @Override
    public boolean addOrUpdate(AreaGuestBookAccountVO areaGuestBookAccount, OaUserBO oaUserBO) {
        String logStr = null;
        Integer configId;
        if(areaGuestBookAccount.getGuestBookAccount().length() > 50){
            throw new CustomizeException("账号长度不能超过50");
        }
        if(areaGuestBookAccount.getGuestBookPassword().length() > 50){
            throw new CustomizeException("密码长度不能超过50");
        }
        // 添加
        if(null == areaGuestBookAccount.getConfigId()){
            logStr = "新增苹果GB账号";
            // 校验所属门店不能重复
            if(this.checkShowAreaId(areaGuestBookAccount.getShowAreaId())){
                throw new CustomizeException("所属门店已存在，不能重复添加");
            }

            AreaGuestBookAccount account = new AreaGuestBookAccount();
            account.setShowAreaId(areaGuestBookAccount.getShowAreaId());
            account.setShowAreaCode(areaGuestBookAccount.getShowAreaCode());
            account.setSearchAreaCode(areaGuestBookAccount.getShowAreaCode());
            account.setAffiliatedAreaId(areaGuestBookAccount.getAffiliatedAreaId());
            account.setGuestBookAccount(areaGuestBookAccount.getGuestBookAccount());
            account.setGuestBookPassword(areaGuestBookAccount.getGuestBookPassword());
            account.setInUser(oaUserBO.getUserName());
            // 历史问题，这个字段废弃，但是表字段非空，随便给一个值
            account.setId(1);
            account.setCreateTime(LocalDateTime.now());
            boolean isOk = baseMapper.insert(account) > 0;
            if(!isOk){
                throw new CustomizeException("保存数据失败");
            }
            configId = account.getConfigId();
        }else {
            configId = areaGuestBookAccount.getConfigId();

            AreaGuestBookAccount accountDB = baseMapper.selectById(configId);
            if(null == accountDB){
                throw new CustomizeException("数据不存在！");
            }

            AreaGuestBookAccount account = new AreaGuestBookAccount();
            account.setConfigId(configId);
            account.setAffiliatedAreaId(areaGuestBookAccount.getAffiliatedAreaId());
            account.setGuestBookAccount(areaGuestBookAccount.getGuestBookAccount());
            account.setGuestBookPassword(areaGuestBookAccount.getGuestBookPassword());
            account.setUpdateTime(LocalDateTime.now());

            // 校验所属门店不能编辑
            if(!accountDB.getShowAreaId().equals(areaGuestBookAccount.getShowAreaId())){
                throw new CustomizeException("不能编辑所属门店！");
            }
            String txt = "%s由【%s】修改为【%s】";
            List<String> logList = Lists.newArrayList();
            if(!accountDB.getGuestBookAccount().equals(account.getGuestBookAccount())){
                logList.add(String.format(txt,"账号",accountDB.getGuestBookAccount(), account.getGuestBookAccount()) );
            }
            if(!accountDB.getGuestBookPassword().equals(account.getGuestBookPassword())){
                logList.add(String.format(txt,"密码",accountDB.getGuestBookPassword(), account.getGuestBookPassword()) );
            }
            if(!compareIgnoringOrder(accountDB.getAffiliatedAreaId(), account.getAffiliatedAreaId())){
                String oldStr = getAreaCodeStr(accountDB.getAffiliatedAreaId());
                String newStr = getAreaCodeStr(account.getAffiliatedAreaId());
                logList.add(String.format(txt,"关联门店",oldStr, newStr));
            }

            if(CollUtil.isEmpty(logList)){
               return true;
            }

            // 存储数据
            LambdaUpdateWrapper<AreaGuestBookAccount> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AreaGuestBookAccount::getConfigId, configId);
            updateWrapper.set(AreaGuestBookAccount::getAffiliatedAreaId, account.getAffiliatedAreaId());
            updateWrapper.set(AreaGuestBookAccount::getGuestBookAccount, account.getGuestBookAccount());
            updateWrapper.set(AreaGuestBookAccount::getGuestBookPassword, account.getGuestBookPassword());
            updateWrapper.set(AreaGuestBookAccount::getUpdateTime, LocalDateTime.now());
            boolean isOk = this.update(updateWrapper);
            if(!isOk){
                throw new CustomizeException("更新数据失败");
            }
            logStr = StringUtils.join(logList, "；") + "。";
        }

        //日志
        areaGuestBookAccountLogService.saveLog(configId, oaUserBO.getUserName(), logStr);
        return true;
    }

    @Override
    public boolean checkShowAreaId(Integer showAreaId) {
        LambdaQueryWrapper<AreaGuestBookAccount> query  = new LambdaQueryWrapper<>();
        query.eq(AreaGuestBookAccount::getShowAreaId, showAreaId);
        return baseMapper.selectCount(query) > 0;
    }


    public  boolean compareIgnoringOrder(String str1, String str2) {
        if(StringUtils.isBlank(str1) && StringUtils.isBlank(str2)){
            return true;
        }
        if(StringUtils.isBlank(str1) || StringUtils.isBlank(str2)){
            return false;
        }
        // 分割字符串为数字数组
        String[] nums1 = str1.split(",");
        String[] nums2 = str2.split(",");

        // 如果长度不同，直接返回false
        if (nums1.length != nums2.length) {
            return false;
        }

        // 使用Set来比较内容是否相同（Set不关心顺序）
        Set<String> set1 = new HashSet<>(Arrays.asList(nums1));
        Set<String> set2 = new HashSet<>(Arrays.asList(nums2));

        return set1.equals(set2);
    }

}
