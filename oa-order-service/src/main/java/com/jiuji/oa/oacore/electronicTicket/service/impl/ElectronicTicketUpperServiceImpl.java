package com.jiuji.oa.oacore.electronicTicket.service.impl;


import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.jiuji.oa.oacore.common.constant.DataSourceConstants;
import com.jiuji.oa.oacore.common.enums.OaMesTypeEnum;
import com.jiuji.oa.oacore.common.exception.CustomizeException;
import com.jiuji.oa.oacore.common.exception.RRExceptionHandler;
import com.jiuji.oa.oacore.electronicTicket.dto.CustomerInformation;
import com.jiuji.oa.oacore.electronicTicket.dto.Renewalsubsidy;
import com.jiuji.oa.oacore.electronicTicket.dto.TaxPiao;
import com.jiuji.oa.oacore.electronicTicket.entity.ElectronicTicket;
import com.jiuji.oa.oacore.electronicTicket.enums.DeliveryEnum;
import com.jiuji.oa.oacore.electronicTicket.enums.ElectronicTicketEnum;
import com.jiuji.oa.oacore.electronicTicket.enums.OrderTypeEnum;
import com.jiuji.oa.oacore.electronicTicket.mapper.ElectronicTicketUpperMapper;
import com.jiuji.oa.oacore.electronicTicket.service.ElectronicTicketService;
import com.jiuji.oa.oacore.electronicTicket.service.ElectronicTicketUpperService;
import com.jiuji.oa.oacore.goldseed.service.AreaInfoService;
import com.jiuji.oa.oacore.oaorder.enums.PointsOrderSubTypeEnum;
import com.jiuji.oa.oacore.oaorder.enums.SubCheckEnum;
import com.jiuji.oa.oacore.oaorder.enums.SubTypeEnum;
import com.jiuji.oa.oacore.oaorder.po.*;
import com.jiuji.oa.oacore.oaorder.service.*;
import com.jiuji.oa.oacore.tousu.enums.XtenantEnum;
import com.jiuji.oa.oacore.tousu.service.SmsService;
import com.jiuji.oa.oacore.web.vo.*;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ElectronicTicketUpperServiceImpl implements ElectronicTicketUpperService {

    public static final String BUY_DISCOUNT = "会员买赠优惠";
    public static final String PRODUCT_DISCOUNT = "会员套餐优惠";
    public static final String SPECIAL_DISCOUNT = "会员特殊优惠";

    @Resource
    private ElectronicTicketUpperMapper electronicTicketUpperMapper;
    @Resource
    private SubService subService;
    @Resource
    private AreaInfoService areaInfoService;
    @Resource
    private BasketService basketService;
    @Resource
    private ProductMkcService productMkcService;
    @Resource
    private ProductinfoService productinfoService;
    @Resource
    private ShouyingService shouyingService;
    @Resource
    private ShouhouService shouhouService;
    @Resource
    private ElectronicTicketService electronicTicketService;
    @Resource
    private SmsService smsService;
    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public static final String PURCHASE_CARE_AND_IPHONE_KEY="orderService:purchaseCareAndIphone:userIdKey";

    private static final Long DEFAULT_DAY=120L;


    private static final List<Integer> TAX_PIAO_STATE=Arrays.asList(0,1,2,3,4,7,8);



    @Override
    @DS(DataSourceConstants.SMALLPRO_WRITE)
    public R<String> updateSubComment(UpdateSubCommentVO updateSubCommentVO) {
        String remark = updateSubCommentVO.getRemark();
        Integer subId = updateSubCommentVO.getSubId();
        if(StringUtils.isNotEmpty(remark) && ObjectUtil.isNotEmpty(subId)){
            Sub sub = subService.getById(subId);
            if(sub!=null){
                String comment = Optional.ofNullable(sub.getComment()).orElse("");
                boolean update = subService.lambdaUpdate().eq(Sub::getSubId, subId)
                        .set(Sub::getComment, comment + remark)
                        .update();
                if(Boolean.TRUE.equals(update)){
                    return R.success("订单修改成功");
                } else {
                    return R.error("订单修改失败");
                }
            }
        } else {
            return R.error("订单或者备注不能为空");
        }
        return R.error("订单查询为空");
    }

    /**
     * 查询 购买用户为2021年9月至今在九机购买过iPhone13系列产品且同时购买了care+服务的用户
     * @param userId
     * @return
     */
    @Override
    public Set purchaseCareAndIphone(Integer userId) {
        SetOperations setOperations = stringRedisTemplate.opsForSet();
        Set userIdSet = new HashSet<>();
        if(ObjectUtil.isNotEmpty(userId)){
            Boolean member = setOperations.isMember(PURCHASE_CARE_AND_IPHONE_KEY, userId.toString());
            if(member){
                userIdSet.add(userId);
                return userIdSet;
            }
        }

        //缓存获取用户为2021年9月至今在九机购买过iPhone13系列产品且同时购买了care+服务的用户
        userIdSet = Optional.ofNullable(setOperations.members(PURCHASE_CARE_AND_IPHONE_KEY)).orElse(new HashSet<>());
        if(CollectionUtils.isEmpty(userIdSet)){
            List<String> list = Optional.ofNullable(electronicTicketUpperMapper.selectPurchaseCareAndIphoneV2()).orElse(new ArrayList<>());
            setOperations.add(PURCHASE_CARE_AND_IPHONE_KEY, list.toArray());
            stringRedisTemplate.expire(PURCHASE_CARE_AND_IPHONE_KEY, 60, TimeUnit.DAYS);
            userIdSet.addAll(list);
        }
        return userIdSet;
    }

    @Override
    public Set<Integer> purchaseCareAndIphoneV2(Integer userId) {
        Set<Integer> userIdSet = new HashSet<>();
        if (userId == null) {
            return userIdSet;
        }

        Boolean member = stringRedisTemplate.opsForSet().isMember(PURCHASE_CARE_AND_IPHONE_KEY, userId.toString());
        if (Boolean.TRUE.equals(member)) {
            userIdSet.add(userId);
            return userIdSet;
        }

        // 看看缓存里面是不是空的,如果是空的填充数据
        Set<String> redisUserIdSet = Optional.ofNullable(stringRedisTemplate.opsForSet().members(PURCHASE_CARE_AND_IPHONE_KEY)).orElse(new HashSet<>());
        if (CollectionUtils.isEmpty(redisUserIdSet)) {
            List<String> list = Optional.ofNullable(electronicTicketUpperMapper.selectPurchaseCareAndIphoneV3()).orElse(new ArrayList<>());
            int size = list.size();
            stringRedisTemplate.opsForSet().add(PURCHASE_CARE_AND_IPHONE_KEY, list.toArray(new String[size]));
            stringRedisTemplate.expire(PURCHASE_CARE_AND_IPHONE_KEY, 60, TimeUnit.DAYS);
            if (list.contains(userId.toString())) {
                userIdSet.add(userId);
            }
        }
        return userIdSet;
    }

    /**
     * 电子小票查询
     * @param selectElectronicTicketVO
     * @return
     */
    @Override
    public R<ElectronicTicketShow> getElectronicTicket(SelectElectronicTicketVO selectElectronicTicketVO) {
        // 订单查询函数
        Supplier<Sub> getSubFun = () -> subService.lambdaQuery().eq(Sub::getSubId,selectElectronicTicketVO.getOrderNo())
                .eq(selectElectronicTicketVO.getUserId() != null, Sub::getUserid, selectElectronicTicketVO.getUserId()).one();
        //订单查询
        Sub sub = getSubFun.get();
        //主库查询标记
        AtomicBoolean queryMianFlag = new AtomicBoolean(Boolean.FALSE);
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if(sub==null){
            String errorMessage = String.format("九机电子小票【读库】查询为空，查询时间：%s,传入参数为：%s", dtf.format(LocalDateTime.now()), selectElectronicTicketVO.toString());
            log.error(errorMessage);
            //当数据在写库查询查不出来的时候从主库查询
            sub = MultipleTransaction.query(DataSourceConstants.SMALLPRO_WRITE, getSubFun::get);
            if(sub == null){
                String errorMessageSecond = String.format("九机电子小票【主库】查询为空，查询时间：%s,传入参数为：%s", dtf.format(LocalDateTime.now()), selectElectronicTicketVO.toString());
                smsService.sendOaMsgTo9Ji(errorMessageSecond, "13495", OaMesTypeEnum.YCTZ.getCode().toString());
                return R.error("查询订单"+selectElectronicTicketVO.getOrderNo()+"不存在，请核实订单是否存在拆单或合并订单等操作");
            }

            queryMianFlag.getAndSet(Boolean.TRUE);
        }
        if(SubCheckEnum.SUB_CHECK_DELETED.getCode().equals(sub.getSubCheck())){
            return R.error("订单已被删除");
        }
        //客户信息查询
        CustomerInformation customerInformation = electronicTicketUpperMapper.getUserClass(selectElectronicTicketVO.getUserId());
        if(customerInformation==null){
            return R.error("客户信息查询为空");
        }
        //根据订单查询商品信息
        List<Basket> basketList;
        if(queryMianFlag.get()){
            basketList = basketService.selectBasketList(selectElectronicTicketVO.getOrderNo());
            String successMessageSecond = String.format("九机电子小票【主库】查询商量列表条数为:%s，查询时间：%s,传入参数为：%s",basketList.size(), dtf.format(LocalDateTime.now()), selectElectronicTicketVO.toString());
            smsService.sendOaMsgTo9Ji(successMessageSecond, "13495", OaMesTypeEnum.YCTZ.getCode().toString());
        } else {
            basketList = basketService.lambdaQuery()
                    .eq(Basket::getSubId, selectElectronicTicketVO.getOrderNo())
                    .and(item -> item.eq(Basket::getIsdel, Boolean.FALSE).or().isNull(Basket::getIsdel))
                    .list();
        }
        if(CollectionUtils.isEmpty(basketList)){
            return R.error("订单商品已被删除");
        }
        //收集所有的ppid
        List<Integer> ppidList = basketList.stream().map((Basket item)->{
            Long ppriceid = item.getPpriceid();
            if(ppriceid!=null){
                return ppriceid.intValue();
            } else {
                return Integer.MAX_VALUE;
            }
        }).collect(Collectors.toList());
        //查询商品信息
        Map<Integer, Productinfo> productinfoMap = new HashMap<>();
        List<Productinfo> productinfo = productinfoService.getProductinfoByPpid(ppidList);
        if(CollectionUtils.isNotEmpty(productinfo)){
            productinfoMap = productinfo.stream().collect(Collectors.toMap(Productinfo::getPpriceid, Function.identity(), (m1, n2) -> n2));
        }
        ElectronicTicketShow electronicTicketShow = new ElectronicTicketShow();
        //获取标题信息
        ElectronicTicketTitle electronicTicketTitle = getElectronicTicketTitle(selectElectronicTicketVO,sub,customerInformation);
        //获取基本信息
        ElectronicTicketBasic electronicTicketBasic = getElectronicTicketBasic(sub,customerInformation,basketList);
        //获取商品详情
        List<ElectronicTicketProduct> electronicTicketProducts = getproductList(basketList,productinfoMap);
        //获取软件服务
        List<ElectronicTicketSoftwareServices> softwareServicesList = getElectronicTicketSoftwareServices(selectElectronicTicketVO, productinfoMap);
        //获取支付信息
        ElectronicTicketPayInfo electronicTicketPayInfo = getElectronicTicketPayInfo(basketList, sub);

        electronicTicketShow.setElectronicTicketTitle(electronicTicketTitle)
                .setElectronicTicketBasic(electronicTicketBasic)
                .setElectronicTicketSoftwareServices(softwareServicesList)
                .setPayInfo(electronicTicketPayInfo)
                .setProductList(electronicTicketProducts);

        //判断该查询是否已经查询过，如果没有查询过需要保存数据局
        List<ElectronicTicket> electronicTicketList = electronicTicketService.getList(selectElectronicTicketVO.getOrderNo(),OrderTypeEnum.ORDERTYPE_ONE.getCode());
        if(CollectionUtils.isEmpty(electronicTicketList)){
            ElectronicTicket electronicTicket = new ElectronicTicket();
            electronicTicket.setOrderNo(selectElectronicTicketVO.getOrderNo())
                    .setUserId(selectElectronicTicketVO.getUserId())
                    //快照数据保存
                    .setType( OrderTypeEnum.ORDERTYPE_ONE.getCode()).setQueryMessage(JSONUtil.toJsonStr(electronicTicketShow));
            electronicTicketService.saveEntity(electronicTicket);
            //第一次查询核实状态肯定为false
            electronicTicketTitle.setIsVerify(Boolean.FALSE);
        } else {
            //如果不是第一次查询需要根据 电子小票信息状态 来判断 电子小票是否已经核实 的字段
            ElectronicTicket electronicTicket = electronicTicketList.get(0);
            Integer state = Optional.ofNullable(electronicTicket.getState()).orElse(ElectronicTicketEnum.ELECTRONICTICKET_ZERO.getCode());
            if(ElectronicTicketEnum.ELECTRONICTICKET_ONE.getCode().equals(state)){
                electronicTicketTitle.setIsVerify(Boolean.TRUE);
            } else {
                electronicTicketTitle.setIsVerify(Boolean.FALSE);
            }
        }
        return R.success(electronicTicketShow);
    }




    /**
     * 获取支付信息
     * @param basketList
     * @param sub
     * @return
     */
    private ElectronicTicketPayInfo getElectronicTicketPayInfo(List<Basket> basketList,Sub sub){
        ElectronicTicketPayInfo electronicTicketPayInfo = new ElectronicTicketPayInfo();
        //获取商品总价
        BigDecimal yingfuM = Optional.ofNullable(sub.getYingfuM()).orElse(BigDecimal.ZERO);
        Double youhui1M = Optional.ofNullable(sub.getYouhui1M()).orElse(0.00);
        Double jidianM = Optional.ofNullable(sub.getJidianM()).orElse(0.00);
     //   Double coinM = Optional.ofNullable(sub.getCoinM()).orElse(0.00);
     //   Double totalPrice=yingfuM.doubleValue()+youhui1M+jidianM+coinM;
        //获取九机抵扣价
        List<Shouying> shouyingList = shouyingService.lambdaQuery().eq(Shouying::getInuser, "回购换其它")
                .eq(Shouying::getSubId, sub.getSubId())
                .select(Shouying::getSubPay06)
                .list();
        //获取到旧机相抵价
        BigDecimal oldMachineOffsetPrice = new BigDecimal("0.00");
        if(CollectionUtils.isNotEmpty(shouyingList)){
            for (Shouying item: shouyingList) {
                BigDecimal subPay06 = item.getSubPay06();
                oldMachineOffsetPrice=oldMachineOffsetPrice.add(subPay06);
            }
        }
        //获取已付金额
        Double yifuM = Optional.ofNullable(sub.getYifuM()).orElse(0.00);
        /**
         * 开始拼接优惠信息
         * 1.获取会员积分价格
         * 2.获取优惠码价格
         * 3.获取套餐优惠（改价）价格
         * 4.获取换新补贴价格
         * 5.根据价格进行拼接字符串信息
         */
        //1.获取会员积分价格
        BigDecimal jidianMDecimal = BigDecimal.valueOf(jidianM);
        //2.获取优惠码价格
        BigDecimal youhui1MDecimal = BigDecimal.valueOf(youhui1M);
        //3.获取套餐优惠（改价）价格
        BigDecimal setMealDecimal = new BigDecimal("0.00");
        // 套餐优惠价格拆分
        BigDecimal buyDiscountTotal = new BigDecimal("0.00");
        BigDecimal productDiscountTotal = new BigDecimal("0.00");
        BigDecimal specialDiscountTotal = new BigDecimal("0.00");

        //4.获取商品总价格
        BigDecimal totalPrice = new BigDecimal("0.00");
        if(CollectionUtils.isNotEmpty(basketList)){
            for (Basket item: basketList) {
                BigDecimal price1 = Optional.ofNullable(item.getPrice1()).orElse(BigDecimal.ZERO);
                totalPrice=totalPrice.add(price1);
                BigDecimal price = Optional.ofNullable(item.getPrice()).orElse(BigDecimal.ZERO);
                Integer basketCount = Optional.ofNullable(item.getBasketCount()).orElse(0);
                BigDecimal divide = price1.subtract(price);
                setMealDecimal=setMealDecimal.add(divide.multiply(BigDecimal.valueOf(basketCount)));

                // 商品价格折扣标签
                String priceLabel = getProductDiscountLabel(price, price1);
                if(org.apache.commons.lang3.StringUtils.isNotBlank(priceLabel)){
                    BigDecimal  discount = divide.multiply(BigDecimal.valueOf(basketCount));
                    if(BUY_DISCOUNT.equals(priceLabel)){
                        buyDiscountTotal = buyDiscountTotal.add(discount);
                    }
                    if(PRODUCT_DISCOUNT.equals(priceLabel)){
                        productDiscountTotal = productDiscountTotal.add(discount);
                    }
                    if(SPECIAL_DISCOUNT.equals(priceLabel)){
                        specialDiscountTotal = specialDiscountTotal.add(discount);
                    }
                }

            }
        }
        //5.获取换新补贴价格
        BigDecimal renewalsubsidyDecimal = new BigDecimal("0.00");
        List<Renewalsubsidy> renewalsubsidy = electronicTicketUpperMapper.getRenewalsubsidyBySubId(sub.getSubId());
        if(CollectionUtils.isNotEmpty(renewalsubsidy)){
            for (Renewalsubsidy item: renewalsubsidy) {
                renewalsubsidyDecimal=item.getTotal().add(renewalsubsidyDecimal);
            }

        }
        //6.根据价格进行拼接字符串信息
        BigDecimal discountInfoTotal=jidianMDecimal.add(youhui1MDecimal).add(setMealDecimal);
        StringBuilder discountInfo = new StringBuilder();
        if(discountInfoTotal.compareTo(BigDecimal.ZERO)>0){
            discountInfo.append("¥").append(discountInfoTotal).append("=");
        }
        StringJoiner joiner = new StringJoiner("+");
        if(jidianMDecimal.compareTo(BigDecimal.ZERO)>0){
            String str="¥"+jidianMDecimal+"(会员积分)"+"\n";
            joiner.add(str);
        }
        //获取满减优惠金额
        BigDecimal fullReductionAmount  = Optional.ofNullable(electronicTicketUpperMapper.getFullReductionAmount(sub.getSubId())).orElse(BigDecimal.ZERO);
        if(fullReductionAmount.compareTo(BigDecimal.ZERO)>0){
            String str="¥"+fullReductionAmount+"(满减优惠)"+"\n";
            joiner.add(str);
        }
        BigDecimal youhui = youhui1MDecimal.subtract(renewalsubsidyDecimal).subtract(fullReductionAmount);
        if(youhui.compareTo(BigDecimal.ZERO)>0){
            String str="¥"+youhui+"(优惠码)"+"\n";
            joiner.add(str);
        }
        if(setMealDecimal.compareTo(BigDecimal.ZERO)>0){

            String str = "";
            if(productDiscountTotal.compareTo(BigDecimal.ZERO) > 0){
                str = "¥"+ productDiscountTotal + "(" + PRODUCT_DISCOUNT + ")"+"\n";
                joiner.add(str);
            }
            if(specialDiscountTotal.compareTo(BigDecimal.ZERO) > 0){
                str = "¥" + specialDiscountTotal + "(" + SPECIAL_DISCOUNT + ")"+"\n";
                joiner.add(str);
            }
            if(buyDiscountTotal.compareTo(BigDecimal.ZERO) > 0){
                str = "¥" + buyDiscountTotal + "(" + BUY_DISCOUNT + ")"+"\n";
                joiner.add(str);
            }
        }
        if(renewalsubsidyDecimal.compareTo(BigDecimal.ZERO)>0){
            String str="¥"+renewalsubsidyDecimal+"(换新补贴)"+"\n";
            joiner.add(str);
        }
        String joinerStr = joiner.toString();
        if(StringUtils.isNotEmpty(joinerStr)){
            discountInfo.append(joinerStr, 0, joinerStr.length()-1);
        }
        Integer points = NumberConstant.ZERO;
        electronicTicketPayInfo
                .setTotalPrice(totalPrice.doubleValue())
                .setPaidPrice(BigDecimal.valueOf(yifuM))
                .setDiscountInfo(StringUtils.isEmpty(discountInfo.toString())?"0":discountInfo.toString())
                .setCopeWithPrice(yingfuM)
                .setPoints(getPointsBySubId(PointsOrderSubTypeEnum.NEW_MACHINE_ORDER.getCode(), sub.getSubId()))
                .setOldMachineOffsetPrice(oldMachineOffsetPrice);
        return electronicTicketPayInfo;
    }

    /**
     * 根据订单类型获取积分
     *  新机订单 = 1,
     *  良品订单 = 2,
     *  售后预约 = 3,
     *  售后维修 = 4,
     *  小件接件单 = 5,
     *  回收订单 = 6
     * @param subType
     * @param subId
     * @return
     */
    @Override
    public Integer getPointsBySubId(Integer subType,Integer subId){
        try {
            String host = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST)).map(R::getData).filter(StringUtils::isNotEmpty)
                    .orElseThrow(() -> new CustomizeException("获取域名出错"));
            Map<String, Integer> params = new HashMap<>();
            params.put("subType", subType);
            params.put("subId", subId);
            String evidenceUrl = host + "/oaApi.svc/rest/GetExpectJifen";
            HttpResponse evidenceResult = HttpUtil.createPost(evidenceUrl)
                    .body(JSONUtil.toJsonStr(params))
                    .execute();
            if(evidenceResult.isOk()){
                log.warn("调用积分查询生成接口传入参数：{}，返回结果：{}",JSONUtil.toJsonStr(params),evidenceResult.body());
                R result = JSONUtil.toBean(JSONUtil.toJsonStr(evidenceResult.body()), R.class);
                if(result.isSuccess()){
                    Object data = result.getData();
                    if(ObjectUtil.isNull(data)){
                        throw new CustomizeException("调用积分查询生成返回数据为空");
                    } else {
                        return Convert.toInt(data);
                    }
                } else {
                    throw new CustomizeException("调用积分查询失败："+Optional.ofNullable(result.getUserMsg()).orElse(result.getMsg()));
                }
            } else {
                log.warn("调用积分查询生成接口异常传入参数：{}",evidenceUrl);
                throw new CustomizeException("调用积分查询生成接口异常");
            }
        } catch (Exception e){
            String msg = String.format("%s 查询积分异常 单号：%s", PointsOrderSubTypeEnum.getEvaluateJobMessage(subType), subId);
            RRExceptionHandler.logError(msg, subId, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            return NumberConstant.ZERO;
        }
    }


    /**
     * 获取软件服务
     * @param selectElectronicTicketVO
     * @return
     */
    private List<ElectronicTicketSoftwareServices> getElectronicTicketSoftwareServices(SelectElectronicTicketVO selectElectronicTicketVO,Map<Integer, Productinfo> productinfoMap){
        List<ElectronicTicketSoftwareServices> servicesArrayList = new ArrayList<>();
        List<Shouhou> shouhouList = shouhouService.lambdaQuery().eq(Shouhou::getSubId, selectElectronicTicketVO.getOrderNo())
                .eq(Shouhou::getIssoft, Boolean.TRUE)
                .and(item -> item.isNull(Shouhou::getIshuishou).or().eq(Shouhou::getIshuishou, 0))
                .eq(Shouhou::getXianshi, Boolean.TRUE)
                .list();
        if(CollectionUtils.isEmpty(shouhouList)){
            return servicesArrayList;
        }
        for (Shouhou item : shouhouList) {
            Integer ppriceid = item.getPpriceid();
            Productinfo productinfo = Optional.ofNullable(productinfoMap.get(ppriceid)).orElse(new Productinfo());
            ElectronicTicketSoftwareServices electronicTicketSoftwareServices = new ElectronicTicketSoftwareServices();
            electronicTicketSoftwareServices.setProductColor(productinfo.getProductColor())
                    .setProductName(productinfo.getProductName())
                    .setSoftware(Optional.ofNullable(item.getQuestionType()).orElse(item.getProblem()));
            servicesArrayList.add(electronicTicketSoftwareServices);
        }
        return servicesArrayList;
    }

    /**
     * 获取商品详情
     * @param basketList
     * @return
     */
    private List<ElectronicTicketProduct> getproductList(List<Basket> basketList,Map<Integer, Productinfo> productinfoMap){
        List<ElectronicTicketProduct> electronicTicketProductsList = new ArrayList<>();
        if(CollectionUtils.isEmpty(basketList)){
            return electronicTicketProductsList;
        }
        //获取库存信息
        List<Integer> basketIdList = basketList.stream().map(Basket::getBasketId).collect(Collectors.toList());
        Map<Integer, ProductMkc> mkcMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(basketIdList)){
            List<ProductMkc> list = productMkcService.lambdaQuery().in(ProductMkc::getBasketId, basketIdList).list();
            if(CollectionUtils.isNotEmpty(list)){
                mkcMap = list.stream().collect(Collectors.toMap(ProductMkc::getBasketId, Function.identity(), (n1, n2) -> n1));
            }
        }
        for (Basket item: basketList) {
            ElectronicTicketProduct electronicTicketProduct = new ElectronicTicketProduct();
            Boolean ismobile = item.getIsmobile();
            electronicTicketProduct.setIsMobile(ismobile)
                    .setCommodityConfiguration(item.getProductPeizhi())
                    .setPrice1(item.getPrice1())
                    .setPrice(item.getPrice());
            // 商品价格标签
            electronicTicketProduct.setProductDiscountLabel(getProductDiscountLabel(electronicTicketProduct.getPrice(), electronicTicketProduct.getPrice1()));
            Long ppriceid = item.getPpriceid();
            if(ppriceid!=null){
                Productinfo info = Optional.ofNullable(productinfoMap.get(ppriceid.intValue())).orElse(new Productinfo());
                electronicTicketProduct.setSkuId(ppriceid.intValue())
                        .setProductColor(info.getProductColor())
                        .setProductName(info.getProductName());
                if(null != info.getProductid() && null != info.getPpriceid()){
                    // 销售小票提示信息[优先根据订单出货门店对应体系进行校验，没有出货门店使用订单所在门店对应体系进行校验]
                    Integer xtenant = electronicTicketUpperMapper.selectXtenantBySub(item.getSubId().intValue());
                    if(null != xtenant){
                        electronicTicketProduct.setReceiptTip(electronicTicketUpperMapper.selectReceiptTipConfig(info.getProductid(), info.getPpriceid(), xtenant));
                    }
                }
            }
            //判断是否为大件，大件商品还需要赋值商品IMEI和机器编号
            if(ismobile){
                Integer basketId = Optional.ofNullable(item.getBasketId()).orElse(Integer.MAX_VALUE);
                Optional.ofNullable(mkcMap.get(basketId)).ifPresent((ProductMkc obj)->
                    electronicTicketProduct.setImei(obj.getImei())
                            .setMachineNumber(obj.getOrderid())
                );
            }
            electronicTicketProductsList.add(electronicTicketProduct);
        }
        return electronicTicketProductsList;
    }

    /**
     * 获取基本信息
     * @param sub
     * @param customerInformation
     * @return
     */
    private ElectronicTicketBasic getElectronicTicketBasic(Sub sub,CustomerInformation customerInformation,List<Basket> basketList){
        ElectronicTicketBasic electronicTicketBasic = new ElectronicTicketBasic();
        //获取下单时间
        LocalDateTime subDate = sub.getSubDate();
        //获取客户名称
        String userName = Optional.ofNullable(customerInformation.getUserName()).orElse("");
        //获取客户电话
        String mobile = Optional.ofNullable(customerInformation.getMobile()).orElse("");
        //获取销售人员
        if(CollectionUtils.isNotEmpty(basketList)){
            Basket basket = basketList.get(0);
            electronicTicketBasic.setSalesman(basket.getSeller());
        }
        electronicTicketBasic.setOrderTime(subDate)
                .setAreaId(sub.getAreaid())
                //进行电话号码的中间四位进行隐藏
                .setCustomerTelephone(mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2"))
                //只进行姓的显示，隐藏名
                .setCustomerInformation(replaceNameX(userName));
        return electronicTicketBasic;
    }

    /**
     * 商品价格标签
     * @param price 实际支付价格
     * @param priceOriginal 原价
     * 规则：实际支付价格是原价的6折及以上 = 标签：会员套餐优惠，
     * 实际支付价格 是原价的为（不含）-6折（不含）标签：特殊优惠，
     * 实际支付金额是为0元 且 原价大于0 标签：买赠优惠
     * @return
     */
    public String getProductDiscountLabel(BigDecimal price, BigDecimal priceOriginal) {
        if(null == price || null == priceOriginal
                || priceOriginal.compareTo(BigDecimal.ZERO) <= 0
                || price.compareTo(priceOriginal) >= 0){
            return null;
        }
        if (price.compareTo(BigDecimal.ZERO) == 0) {
            return BUY_DISCOUNT;
        }

        BigDecimal sixtyPercent = priceOriginal.multiply(new BigDecimal("0.6"));
        if (price.compareTo(sixtyPercent) >= 0) {
            return PRODUCT_DISCOUNT;
        } else {
            return SPECIAL_DISCOUNT;
        }
    }

    /**
     * 名字加密
     * @param str
     * @return
     */
    public static String replaceNameX(String str){
        if(StringUtils.isEmpty(str)){
            return "";
        }
        String reg = ".{1}";
        StringBuffer sb = new StringBuffer();
        Pattern p = Pattern.compile(reg);
        Matcher m = p.matcher(str);
        int i = 0;
        while(m.find()){
            i++;
            if(i==1){
                continue;
            }
            m.appendReplacement(sb, "*");
        }
        m.appendTail(sb);
        return sb.toString();
    }


    /**
     * 获取标题信息
     * @param selectElectronicTicketVO
     * @return
     */
    private ElectronicTicketTitle getElectronicTicketTitle(SelectElectronicTicketVO selectElectronicTicketVO, Sub sub,CustomerInformation customerInformation){
        ElectronicTicketTitle electronicTicketTitle = new ElectronicTicketTitle();
        //获取购物门店信息
        AreaInfo areaInfo = areaInfoService.getAreaInfoById(sub.getAreaid());
        //判断是不是九机网 xtenant为0
        if(!XtenantEnum.Xtenant_JIUJI.getCode().equals(XtenantEnum.getXtenant())){
            electronicTicketTitle.setPrintName(areaInfo.getPrintName());
        }
        //判断是否可以开发票
        LocalDateTime tradeDate = sub.getTradeDate();
        Integer subCheck = sub.getSubCheck();
        if(tradeDate!=null && subCheck!=null){
            LocalDateTime future = tradeDate.plusDays(DEFAULT_DAY);
            LocalDateTime now = LocalDateTime.now();
            boolean b = now.compareTo(future) < 0 && now.compareTo(tradeDate) > 0;
            if(b && SubCheckEnum.SUB_CHECK_COMPLETED.getCode().equals(subCheck)){
                electronicTicketTitle.setIsInvoice(Boolean.TRUE);
            } else {
                electronicTicketTitle.setIsInvoice(Boolean.FALSE);
            }
        } else {
            electronicTicketTitle.setIsInvoice(Boolean.FALSE);
        }
        //获取到发票状态
        TaxPiao taxPiao = electronicTicketUpperMapper.getTaxPiao(selectElectronicTicketVO.getOrderNo());
        if(taxPiao!=null){
            //状态： -1 已删除  0 待确认办理  1 待审核  8 待修改  2 办理中  3 已开好  4 已完成  5 已作废  6 红冲  7 红冲中
            Integer flag = taxPiao.getFlag();
            if(TAX_PIAO_STATE.contains(flag)){
                //这些发票状态下的订单不让继续开发票
                electronicTicketTitle.setIsInvoice(Boolean.FALSE);
            }
        }
        Integer subtype = sub.getSubtype();
        if(SubTypeEnum.SUB_TYPE_JINGDONG.getCode().equals(subtype)){
            //判断如果是京东到家也是不显示
            electronicTicketTitle.setIsInvoice(Boolean.FALSE);
        }

        //获取配送方式
        Integer delivery = sub.getDelivery();
        electronicTicketTitle.setMembershipLevel(customerInformation.getUserClass())
                .setSubState(sub.getSubCheck())
                .setOrderNo(selectElectronicTicketVO.getOrderNo())
                .setShopArea(areaInfo.getAreaName()+"("+areaInfo.getArea()+")")
                .setDeliveryType(DeliveryEnum.valueOfByCode(delivery))
                .setAreaTelephone(areaInfo.getCompanyTel1());

        //设置送达时间
        electronicTicketTitle.setExpectTime(sub.getExpectTime());
        return electronicTicketTitle;

    }
}
