package com.jiuji.oa.oacore.iqiyi;

import com.google.common.base.Joiner;
import com.jiuji.oa.oacore.common.util.EncodeUtils;
import com.jiuji.oa.oacore.iqiyi.enums.PlatformEnum;
import com.jiuji.oa.oacore.iqiyi.service.IqiyiApiService;
import com.jiuji.oa.oacore.iqiyi.vo.req.IdentificationCheckReq;
import com.jiuji.tc.common.vo.OaApiVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.OaVerifyUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class EncodeUtilsTest {

    @Autowired
    private IqiyiApiService iqiyiApiService;
    @Autowired
    private RedissonClient redisson;

        @Test
        public void testSign() {
            TreeMap<String, Object> paramMap = new TreeMap<>();
            paramMap.put("a", "3");
            paramMap.put("b", "2");
            paramMap.put("c", "1");
            String md5key = "qwer";
            String targetParam = Joiner.on("&").withKeyValueSeparator("=")
                    .useForNull("").join(paramMap);
            // MD5签名工具类,可以自己实现, MD5实现参照
            String targetSign = EncodeUtils.MD5(targetParam + md5key, "UTF-8");
            System.out.println("targetSign: " + targetSign);
        }

        @Test
        public void identificationCheckTest(){
            IdentificationCheckReq identificationCheckReq = new IdentificationCheckReq();
            identificationCheckReq.setPartnerNo("toB_common_test");
            identificationCheckReq.setItem("333");
            identificationCheckReq.setPlatform(PlatformEnum.OTHER.getCode());
            identificationCheckReq.setMobile("18108753351");
            String result = iqiyiApiService.identificationCheck(identificationCheckReq);
            log.info("");
        }

        @Test
        public void oaSignTest(){
            long timeStamp = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
            Map<String,String> param = new HashMap<>();
            param.put("title","测试");
            String sign = OaVerifyUtil.getOaSign("11111", param,timeStamp);
            OaApiVo oaApiVo = new OaApiVo();
            oaApiVo.setTimeStamp(timeStamp);
            oaApiVo.setData(param);
            oaApiVo.setSign(sign);
            R<Boolean> b = OaVerifyUtil.checkSign("11111", oaApiVo);
            System.out.println("");
        }

        @Test
        public void testRedisson() {
            RLock fairLock = redisson.getFairLock("oaorder:createIqiyiOrder:59331491641629616687345222");
            System.out.println(fairLock);
            //解锁
            if (fairLock.isHeldByCurrentThread()) {
                fairLock.unlock();
            }
        }
    }
