package com.jiuji.oa.oacore.oaorder;

import com.jiuji.oa.oacore.common.constant.ThridPartyExpressTypeEnum;
import com.jiuji.oa.oacore.oaorder.res.ProductInfoSimpleVo;
import com.jiuji.oa.oacore.oaorder.service.ProductinfoService;
import com.jiuji.oa.oacore.oaorder.service.WuliuService;
import com.jiuji.oa.oacore.oaorder.vo.req.OnSiteInstallStatisticReq;
import com.jiuji.oa.oacore.oaorder.vo.res.OnSiteInstallStatisticRes;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: gengjiaping
 * @date: 2019/11/19
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class WuliuServiceTest {

    @Autowired
    private WuliuService wuliuService;

    @Autowired
    private ProductinfoService tProductinfoService;

    @Test
    public void calculatePriceTest(){
       BigDecimal feiyong = wuliuService.calculatePrice(5301,6327,new BigDecimal(0.8), ThridPartyExpressTypeEnum.SHUN_FENG.getCode());
       log.info("物流费用={}",feiyong);
    }

    @Test
    public void getOnSiteInstallStatisticListTest(){
        OnSiteInstallStatisticReq param = new OnSiteInstallStatisticReq();
        param.setStartTime("2020-03-01 00:00:00");
        param.setEndTime("2020-03-03 00:00:00");
        List<OnSiteInstallStatisticRes> list = wuliuService.getOnSiteInstallStatisticList(null);
        log.info("");
    }

    @Test
    public void getProductInfoByImeiTest(){
        ProductInfoSimpleVo productInfoSimpleVo = tProductinfoService.getProductInfoByImei("862562048258897");
        log.info("");
    }
}
