package com.jiuji.oa.oacore.thirdplatform.order.service.impl;


import com.alibaba.fastjson.JSON;
import com.jiuji.oa.oacore.thirdplatform.common.ThirdPlatformCommonConst;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 10:20
 * @Description
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
@Profile(value = {"dev"})
@ActiveProfiles("dev")
public class OrderServiceImplTest {
    @Autowired
    private SysConfigClient sysConfigClient;
    @Autowired
    private RestTemplate restTemplate;
    @Test
    public void Test() {
        R<String> conf = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST);
        if (null == conf || StringUtils.isBlank(conf.getData())) {
            log.error("获取inwcf前缀失败:{}", conf);
        }
        String prefix = conf.getData();
        String url = prefix + ThirdPlatformCommonConst.OA_API_SUB_REFUND;
        //构建订单参数，调用oa订单退款接口
        Map<String, Object> map = new HashMap<>(NumberConstant.FIVE);
        map.put("subId", 18062186L);
        map.put("kemu", "81");
        map.put("comment", "测试");
        log.error("调用订单退订接口,链接{},参数{}", url,map);
        ResponseEntity<R> payResponseEntityR = restTemplate.postForEntity(url, map, R.class);
        log.error("调用订单退订接口,返回参数{}", JSON.toJSONString(payResponseEntityR));
    }


}