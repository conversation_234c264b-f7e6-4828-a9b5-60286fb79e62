package com.jiuji.oa.oacore.operator.service.impl;

import junit.framework.TestCase;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 17:03
 * @Description
 */

public class OperatorAccountConfigServiceImplTest {
    public static void main(String[] args) {
        BigDecimal n = null;
        try {
            n = new BigDecimal("-1.2");
        }catch (NumberFormatException e){
            System.out.println("cece");
        }
        System.out.println(n);
    }
}