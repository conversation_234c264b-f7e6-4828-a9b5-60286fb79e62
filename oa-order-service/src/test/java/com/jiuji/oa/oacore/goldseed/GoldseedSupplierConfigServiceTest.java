package com.jiuji.oa.oacore.goldseed;

import com.jiuji.oa.oacore.goldseed.po.GoldseedSupplierConfig;
import com.jiuji.oa.oacore.goldseed.service.GoldseedSupplierConfigService;
import com.jiuji.oa.oacore.goldseed.vo.GoldseedSupplierConfigVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @author: gengjiaping
 * @date: 2019/11/26
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class GoldseedSupplierConfigServiceTest {

    @Autowired
    private GoldseedSupplierConfigService goldseedSupplierConfigService;

    @Test
    public void getByIdTest(){
        GoldseedSupplierConfigVo gsConfig = goldseedSupplierConfigService.getSupplierConfigById(1);
        log.info("");
    }

}
