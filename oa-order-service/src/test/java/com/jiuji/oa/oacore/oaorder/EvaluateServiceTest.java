package com.jiuji.oa.oacore.oaorder;

import com.jiuji.oa.oacore.oaorder.service.EvaluateService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @author: gengjiaping
 * @date: 2020/4/1
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class EvaluateServiceTest {

    @Autowired
    private EvaluateService evaluateService;

    @Test
    public void getEvaluatePagesTest(){
        /*PageRes<EvaluateInfoRes> page = evaluateService.getEvaluatePages(1,20,15, authId);
        log.info("");*/
    }
}
