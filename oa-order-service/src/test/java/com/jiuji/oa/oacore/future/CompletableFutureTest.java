package com.jiuji.oa.oacore.future;

import lombok.SneakyThrows;
import org.junit.Test;

import java.text.MessageFormat;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @description
 * @since 2021/4/13 17:16
 */
public class CompletableFutureTest {
    @SneakyThrows
    @Test
    public void join(){
        CompletableFuture<Integer> iFuture = CompletableFuture.supplyAsync(() -> {
            try {
                System.err.println("开始计算iFuture "+Thread.currentThread().getName());
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return 1;
        });
        CompletableFuture<Integer> i2Future = CompletableFuture.supplyAsync(() -> {
            try {
                System.err.println("开始计算i2Future "+Thread.currentThread().getName());
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return 2;
        });
        Thread.sleep(3000);
        System.err.println(MessageFormat.format("{0}-{1}",iFuture.join(),i2Future.join()));
    }
}
