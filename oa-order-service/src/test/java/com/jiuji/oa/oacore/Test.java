package com.jiuji.oa.oacore;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.google.common.collect.Maps;
import com.jiuji.oa.oacore.operator.po.OperatorBusinessConfig;
import com.jiuji.oa.oacore.operator.vo.req.BusinessConfigUpdateReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @date 2020/12/28 20:48
 **/
@Slf4j
public class Test {
    public static void main(String[] args) throws Exception {
        //source class 自动生成get set
        Method[] methods = ReflectUtil.getMethods(OperatorBusinessConfig.class);
        //循环目标类,进行set
        for (Method method : Arrays.stream(ReflectUtil.getMethods(BusinessConfigUpdateReq.class)).sorted(Comparator.comparing(Method::getName)).collect(Collectors.toList())) {
            if(method.getName().startsWith("set")){
                System.err.println(StrUtil.format( "businessConfigUpdateReq.{}(operatorBusinessConfig.{}());",method.getName()
                        , Arrays.stream(methods).filter(m-> m.getName().startsWith("get")
                                        && ("s"+StrUtil.subSuf(m.getName(),1)).replace("_","")
                                        .equalsIgnoreCase(method.getName().replace("_","")))
                                .findFirst().map(Method::getName).orElse(null)));
            }
        }

        //拼接到请求url参数
//        Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
//        log.info(String.valueOf(timestamp.getTime()));
//        String nonce = RandomUtil.randomString(10);
//        log.info(nonce);
//        String startTime = "2020-12-01 00:00:00";
//        String endTime = "2020-12-02 00:00:00";
//        Integer appId = 14;
//        //appSecret要保密 不要拼接到url 但是要参与签名
//        String appSecret = "pozzdcpxsl2lsk9h0madgwg1qx39fjmx70d43k96q3bex1m27n45nyuic7bp";
//        log.info(appSecret);
//        Map<String, Object> params = Maps.newHashMapWithExpectedSize(4);
//        params.put("timestamp", timestamp.getTime());
//        params.put("nonce", nonce);
//        params.put("startTime", startTime);
//        params.put("endTime", endTime);
//        params.put("appId", appId);
//        params.put("appSecret", appSecret);
//        String sign = Test.createSign(params);
//        log.info(sign);
//
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//        LocalDateTime start = LocalDateTime.parse("2020-01-01 00:00:00", formatter);
//        LocalDateTime end = LocalDateTime.parse("2021-01-01 00:00:00", formatter);
//        LocalDateTime now = LocalDateTime.now();
//        LocalDateTime lastMonth=now.minusMonths(1).minusDays(now.getDayOfMonth()-1);
//        log.info(String.valueOf(lastMonth.getDayOfMonth()));
//        int minusDays = now.minusMonths(1).getDayOfMonth() + now.getDayOfMonth();
//        log.info(String.valueOf(now.getDayOfMonth()));
//        Test.createPurDate();
    }

    //类似微信接口的签名生成方法
    public static String createSign(Map<String, Object> params) {
        StringBuilder sb = new StringBuilder();
        // 将参数以参数名的字典升序排序
        Map<String, Object> sortParams = new TreeMap(params);
        // 遍历排序的字典,并拼接"key=value"格式
        for (Map.Entry<String, Object> entry : sortParams.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue().toString().trim();
            if (!StringUtils.isEmpty(value)) {
                sb.append("&").append(key).append("=").append(value);
            }
        }
        //将签名使用MD5加密并全部字母变为大写
        return SecureUtil.md5(sb.toString().replaceFirst("&", "")).toUpperCase();
    }

    private static void createPurDate() throws Exception {
        //拼接到请求url参数
        Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
        System.out.println(timestamp.getTime());
        String nonce = RandomUtil.randomString(10);
        // log.info(nonce);
        String startTime = "2021-06-06 00:00:00";
        String endTime = "2021-06-16 00:00:00";
        Integer appId =15;
        //appSecret要保密 不要拼接到url 但是要参与签名
        String appSecret = "kAaaa0q2-UHejIFFA88opEE*j&*88ffaw+FF";
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(4);
        params.put("timestamp", timestamp.getTime());
        params.put("nonce", nonce);
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        params.put("appId", appId);
        params.put("appSecret", appSecret);
        String sign = Test.createSign(params);
//        String url="http://localhost:1000/orderservice/open/api/mi/inventory";
        String url="https://moa.zlf.co/cloudapi_nc/orderservice/open/api/mi/sales";
        String param="?startTime="+startTime+"&endTime="+endTime+"&timestamp="+ timestamp.getTime() +"&nonce="+nonce+"&appId="+appId.toString();
        String result=sendPost1(url+param, sign);
        System.out.println(result);
    }


    public static String sendPost1(String postUrl,String sign) throws Exception {
        String url = postUrl;
        System.out.println(url);
        System.out.println(sign);
        String result = "";
        try {
            // System.setProperty("https.protocols", "TLSv1,TLSv1.2,SSLv3");

            HttpRequest getHttpRequest = HttpUtil.createGet(url);
            getHttpRequest.contentType("application/json;charset=UTF-8");
            getHttpRequest.charset("UTF-8");
            getHttpRequest.header("sign",sign);
            getHttpRequest.header("xservicename","oa-orderservice");
            getHttpRequest.header("xtenant","1000");
            HttpResponse execute = getHttpRequest.execute();
            if (execute.isOk()) {
                result = execute.body();
            }else {
                log.info("请求失败>>>"+execute.body());
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.info(e.getMessage());
        }

        return result;

    }
}
