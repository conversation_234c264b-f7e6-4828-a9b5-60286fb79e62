package com.jiuji.oa.oacore.oaorder;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jiuji.oa.oacore.common.enums.EvaluateTypeEnum;
import com.jiuji.oa.oacore.oaorder.dao.EvaluateMapper;
import com.jiuji.oa.oacore.oaorder.dao.SubMapper;
import com.jiuji.oa.oacore.oaorder.po.Evaluate;
import com.jiuji.oa.oacore.oaorder.po.Sub;
import com.jiuji.oa.oacore.oaorder.res.EvaluateCh999UserInfoBO;
import com.jiuji.oa.oacore.oaorder.res.OrderBaseInfoBO;
import com.jiuji.oa.oacore.oaorder.res.OrderBaseInfoRes;
import com.jiuji.oa.oacore.oaorder.res.PjTagsBO;
import com.jiuji.oa.oacore.oaorder.service.EvaluateService;
import com.jiuji.oa.oacore.oaorder.service.buildFactory.OrderBaseInfoFactory;
import com.jiuji.wcf.wcfclient.constant.MethodName;
import com.jiuji.wcf.wcfclient.csharp.gen.oaData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: gengjiaping
 * @date: 2019/11/19
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class OrderBaseInfoServiceTest {

    @Autowired
    private OrderBaseInfoFactory orderBaseInfoFactory;
    @Autowired
    private OAStub oaStub;
    @Autowired
    private SubMapper subMapper;
    @Autowired
    private EvaluateMapper evaluateMapper;
    @Autowired
    private EvaluateService evaluateService;

    @Test
    /*
    测试方法
    第一步：SELECT dbo.encrypt('16430561') 参数为单号 在OA系统-门店日常管理-客户评价
    第二步：http://9ji.cn/KVxxxx xxxx为第一步查出来的值
    测试线上线下订单
    */
    public void testV() {
        IPage<Sub> page = subMapper.selectPage(new Page<Sub>(1, 20), null);
        List<Sub> subs = page.getRecords();
        for (Sub sub : subs) {
            Integer subId = sub.getSubId();
            boolean testOrderBaseInfo = testOrderBaseInfo("V", subId);
            if (!testOrderBaseInfo) {
                System.out.println(false);
                break;
            }
        }
    }

    @Test
    // 上线下订单
    public void testOneV() {
        boolean result = testOrderBaseInfo("V", 16425399);
        System.out.println(result);
    }

    @Test
    // 测试呼叫服务
    public void testC() {
        LambdaQueryWrapper<Evaluate> queryWrapper = new LambdaQueryWrapper<Evaluate>().eq(Evaluate::getEvaluateType, EvaluateTypeEnum.CallService.getCode());
        IPage<Evaluate> page = evaluateService.getPage(new Page<Evaluate>(1, 20), queryWrapper);
        List<Evaluate> evaluates = page.getRecords();
        for (Evaluate evaluate : evaluates) {
            Integer subId = evaluate.getSubId();
            boolean testOrderBaseInfo = testOrderBaseInfo("C", subId);
            if (!testOrderBaseInfo) {
                System.out.println(false);
                break;
            }
        }
    }

    @Test
    // 测试开发批签评价
    public void testD() {
        LambdaQueryWrapper<Evaluate> queryWrapper = new LambdaQueryWrapper<Evaluate>().eq(Evaluate::getEvaluateType, EvaluateTypeEnum.Developer.getCode());
        IPage<Evaluate> page = evaluateService.getPage(new Page<Evaluate>(1, 20), queryWrapper);
        List<Evaluate> evaluates = page.getRecords();
        for (Evaluate evaluate : evaluates) {
            Integer subId = evaluate.getSubId();
            boolean testOrderBaseInfo = testOrderBaseInfo("D", subId);
            if (!testOrderBaseInfo) {
                System.out.println(false);
                break;
            }
        }
    }

    @Test
    // 贴膜售后
    public void testKT() {
        LambdaQueryWrapper<Evaluate> queryWrapper = new LambdaQueryWrapper<Evaluate>().eq(Evaluate::getEvaluateType, EvaluateTypeEnum.FileAfterSale.getCode());
        IPage<Evaluate> page = evaluateService.getPage(new Page<Evaluate>(1, 20), queryWrapper);
        List<Evaluate> evaluates = page.getRecords();
        for (Evaluate evaluate : evaluates) {
            Integer subId = evaluate.getSubId();
            boolean testOrderBaseInfo = testOrderBaseInfo("KT", subId);
            if (!testOrderBaseInfo) {
                System.out.println(false);
                break;
            }
        }
    }

    @Test
    // 面试邀约
    public void testI() {
        LambdaQueryWrapper<Evaluate> queryWrapper = new LambdaQueryWrapper<Evaluate>().eq(Evaluate::getEvaluateType, EvaluateTypeEnum.Interview.getCode());
        IPage<Evaluate> page = evaluateService.getPage(new Page<Evaluate>(1, 20), queryWrapper);
        List<Evaluate> evaluates = page.getRecords();
        for (Evaluate evaluate : evaluates) {
            Integer subId = evaluate.getSubId();
            boolean testOrderBaseInfo = testOrderBaseInfo("I", subId);
            if (!testOrderBaseInfo) {
                System.out.println(false);
                break;
            }
        }
    }

    @Test
    // 面试邀约
    public void testOneI() {
        boolean testOrderBaseInfo = testOrderBaseInfo("I", 6819);
    }

    @Test
    // 良品订单
    public void testL() {
        LambdaQueryWrapper<Evaluate> queryWrapper = new LambdaQueryWrapper<Evaluate>().eq(Evaluate::getEvaluateType, EvaluateTypeEnum.Liangpin.getCode());
        IPage<Evaluate> page = evaluateService.getPage(new Page<Evaluate>(1, 20), queryWrapper);
        List<Evaluate> evaluates = page.getRecords();
        for (Evaluate evaluate : evaluates) {
            Integer subId = evaluate.getSubId();
            boolean testOrderBaseInfo = testOrderBaseInfo("L", subId);
            if (!testOrderBaseInfo) {
                System.out.println(false);
                break;
            }
        }
    }

    @Test
    // 在线客服
    public void testO() {
        LambdaQueryWrapper<Evaluate> queryWrapper = new LambdaQueryWrapper<Evaluate>().eq(Evaluate::getEvaluateType, EvaluateTypeEnum.OnlineServices.getCode());
        IPage<Evaluate> page = evaluateService.getPage(new Page<Evaluate>(1, 20), queryWrapper);
        List<Evaluate> evaluates = page.getRecords();
        for (Evaluate evaluate : evaluates) {
            Integer subId = evaluate.getSubId();
            boolean testOrderBaseInfo = testOrderBaseInfo("O", subId);
            if (!testOrderBaseInfo) {
                System.out.println(false);
                break;
            }
        }
    }

    @Test
    // 回收单
    public void testH() {
        LambdaQueryWrapper<Evaluate> queryWrapper = new LambdaQueryWrapper<Evaluate>().eq(Evaluate::getEvaluateType, EvaluateTypeEnum.Recycle.getCode());
        IPage<Evaluate> page = evaluateService.getPage(new Page<Evaluate>(1, 20), queryWrapper);
        List<Evaluate> evaluates = page.getRecords();
        for (Evaluate evaluate : evaluates) {
            Integer subId = evaluate.getSubId();
            boolean testOrderBaseInfo = testOrderBaseInfo("H", subId);
            if (!testOrderBaseInfo) {
                System.out.println(false);
                break;
            }
        }
    }

    @Test
    // 售后单
    public void testS() {
        LambdaQueryWrapper<Evaluate> queryWrapper = new LambdaQueryWrapper<Evaluate>().eq(Evaluate::getEvaluateType, EvaluateTypeEnum.Shouhou.getCode());
        IPage<Evaluate> page = evaluateService.getPage(new Page<Evaluate>(1, 20), queryWrapper);
        List<Evaluate> evaluates = page.getRecords();
        for (Evaluate evaluate : evaluates) {
            Integer subId = evaluate.getSubId();
            boolean testOrderBaseInfo = testOrderBaseInfo("S", subId);
            if (!testOrderBaseInfo) {
                System.out.println(false);
                break;
            }
        }
    }

    @Test
    // 小件售后
    public void testKX() {
        LambdaQueryWrapper<Evaluate> queryWrapper = new LambdaQueryWrapper<Evaluate>().eq(Evaluate::getEvaluateType, EvaluateTypeEnum.SmallProAfterSale.getCode());
        IPage<Evaluate> page = evaluateService.getPage(new Page<Evaluate>(1, 20), queryWrapper);
        List<Evaluate> evaluates = page.getRecords();
        for (Evaluate evaluate : evaluates) {
            Integer subId = evaluate.getSubId();
            boolean testOrderBaseInfo = testOrderBaseInfo("KX", subId);
            if (!testOrderBaseInfo) {
                System.out.println(false);
                break;
            }
        }
    }

    @Test
    // 软件接件单
    public void testR() {
        LambdaQueryWrapper<Evaluate> queryWrapper = new LambdaQueryWrapper<Evaluate>();
        IPage<Evaluate> page = evaluateService.getPage(new Page<Evaluate>(1, 20), queryWrapper);
        List<Evaluate> evaluates = page.getRecords();
        for (Evaluate evaluate : evaluates) {
            Integer subId = evaluate.getSubId();
            boolean testOrderBaseInfo = testOrderBaseInfo("R", subId);
            if (!testOrderBaseInfo) {
                System.out.println(false);
                break;
            }
        }
    }

    @Test
    // 租机归还
    public void testZ2() {
        LambdaQueryWrapper<Evaluate> queryWrapper = new LambdaQueryWrapper<Evaluate>().eq(Evaluate::getEvaluateType, EvaluateTypeEnum.ZujiBack.getCode());
        IPage<Evaluate> page = evaluateService.getPage(new Page<Evaluate>(1, 20), queryWrapper);
        List<Evaluate> evaluates = page.getRecords();
        for (Evaluate evaluate : evaluates) {
            Integer subId = evaluate.getSubId();
            boolean testOrderBaseInfo = testOrderBaseInfo("Z2", subId);
            if (!testOrderBaseInfo) {
                System.out.println(false);
                break;
            }
        }
    }

    @Test
    // 租机取机
    public void testZ1() {
        LambdaQueryWrapper<Evaluate> queryWrapper = new LambdaQueryWrapper<Evaluate>().eq(Evaluate::getEvaluateType, EvaluateTypeEnum.ZujiGet.getCode());
        IPage<Evaluate> page = evaluateService.getPage(new Page<Evaluate>(1, 20), queryWrapper);
        List<Evaluate> evaluates = page.getRecords();
        for (Evaluate evaluate : evaluates) {
            Integer subId = evaluate.getSubId();
            boolean testOrderBaseInfo = testOrderBaseInfo("Z1", subId);
            if (!testOrderBaseInfo) {
                System.out.println(false);
                break;
            }
        }
    }

    @Test
    // 投诉
    public void testTS() {
        boolean result = testOrderBaseInfo("TS", 2299304);
        System.out.println(result);
    }


    private boolean testOrderBaseInfo(String type, Integer id) {
        oaData.OrderBaseInfoNew wcforderBaseInfo = getWcfOrderBaseInfo(type, id);
        String wcforderBaseInfoStr = JSONObject.toJSONString(wcforderBaseInfo);

        OrderBaseInfoRes orderBaseInfo = getOrderBaseInfo(type, id);
        String orderBaseInfoStr = JSONObject.toJSONString(orderBaseInfo);

        System.out.println("wcf:" + wcforderBaseInfoStr);
        System.out.println("me:" + orderBaseInfoStr);

        return compare(wcforderBaseInfo, orderBaseInfo);
    }

    private OrderBaseInfoRes getOrderBaseInfo(String type, Integer id) {
        return orderBaseInfoFactory.getInstance(type).getOrderBaseInfo(id);
    }

    private oaData.OrderBaseInfoNew getWcfOrderBaseInfo(String type, Integer id) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("type", type);
        param.put("version", "3.0");
        return oaStub.call(MethodName.OA.ORDER_BASE_INFO_NEW, param);
    }

    /**
     * 比对两个对象内容是否一致
     */
    private boolean compare(oaData.OrderBaseInfoNew wcfOrder, OrderBaseInfoRes myOrder) {
        OrderBaseInfoRes wcfOrderBaseInfoRes = new OrderBaseInfoRes();
        BeanUtils.copyProperties(wcfOrder, wcfOrderBaseInfoRes);
        oaData.OrderBaseInfoNew.Info wcfInfo = wcfOrder.getInfo();
        OrderBaseInfoBO myInfo = myOrder.getInfo();
        if (StringUtils.isNotEmpty(wcfInfo.getArea()) && !wcfInfo.getArea().equals(myInfo.getArea())) {
            System.out.println(wcfInfo.getArea());
            System.out.println(myInfo.getArea());
            return false;
        }
        if (!wcfInfo.getHasSoftService().equals(myInfo.getHasSoftService())) {
            System.out.println(wcfInfo.getHasSoftService());
            System.out.println(myInfo.getHasSoftService());
            return false;
        }
        if (StringUtils.isNotEmpty(wcfInfo.getCommendedRemark()) && !wcfInfo.getCommendedRemark().equals(myInfo.getCommendedRemark())) {
            System.out.println(wcfInfo.getCommendedRemark());
            System.out.println(myInfo.getCommendedRemark());
            return false;
        }
        if (wcfInfo.getCommendedScore() != null && !wcfInfo.getCommendedScore().equals(myInfo.getCommendedScore())) {
            System.out.println(wcfInfo.getCommendedScore());
            System.out.println(myInfo.getCommendedScore());
            return false;
        }
        if (StringUtils.isNotEmpty(wcfInfo.getContent()) && !wcfInfo.getContent().equals(myInfo.getContent())) {
            System.out.println(wcfInfo.getContent());
            System.out.println(myInfo.getContent());
            return false;
        }
        if (StringUtils.isNotEmpty(wcfInfo.getSub_id()) && !wcfInfo.getSub_id().equals(myInfo.getSub_id() + "")) {
            System.out.println(wcfInfo.getSub_id());
            System.out.println(myInfo.getSub_id());
            return false;
        }
        if (StringUtils.isNotEmpty(wcfInfo.getSub_send()) && !wcfInfo.getSub_send().equals(myInfo.getSub_send() + "")) {
            System.out.println(wcfInfo.getSub_send());
            System.out.println(myInfo.getSub_send());
            return false;
        }
        if (wcfInfo.getUserclass() != myInfo.getUserclass()) {
            System.out.println(wcfInfo.getUserclass());
            System.out.println(myInfo.getUserclass());
            return false;
        }
        if (wcfInfo.getUserid() != myInfo.getUserid()) {
            System.out.println(wcfInfo.getUserid());
            System.out.println(myInfo.getUserid());
            return false;
        }
        if (StringUtils.isNotEmpty(wcfInfo.getUserName()) && !wcfInfo.getUserName().equals(myInfo.getUserName())) {
            System.out.println(wcfInfo.getUserName());
            System.out.println(myInfo.getUserName());
            return false;
        }

        List<oaData.OrderBaseInfoNew.Info.EvaluateCh999UserInfos> wcfCh999Users = wcfInfo.getEvaluateCh999UserInfos();
        List<EvaluateCh999UserInfoBO> myCh999Users = myInfo.getEvaluateCh999UserInfos();
        Map<String, EvaluateCh999UserInfoBO> myCh999UsersMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(myCh999Users)) {
            myCh999UsersMap = myCh999Users.stream().collect(
                    Collectors.toMap(o -> o.getCh999Id() + ":" + o.getJob(), o -> o, (v1, v2) -> v1));
        }
        if (CollectionUtils.isNotEmpty(wcfCh999Users)) {
            for (oaData.OrderBaseInfoNew.Info.EvaluateCh999UserInfos wcfCh999User : wcfCh999Users) {
                EvaluateCh999UserInfoBO myCh999User = myCh999UsersMap.get(wcfCh999User.getCh999Id() + ":" + wcfCh999User.getJob());
                if (myCh999User == null) {
                    System.out.println("evaluateCh999UserInfos为空！");
                    return false;
                }
                if (wcfCh999User.getJob() != null && !wcfCh999User.getJob().equals(myCh999User.getJob())) {
                    System.out.println("evaluateCh999UserInfos:job-----------------------------");
                    System.out.println(wcfCh999User.getJob());
                    System.out.println(myCh999User.getJob());
                    return false;
                }
                if (wcfCh999User.getName() != null && !wcfCh999User.getName().equals(myCh999User.getName())) {
                    System.out.println("evaluateCh999UserInfos:Name-----------------------------");
                    System.out.println(wcfCh999User.getName());
                    System.out.println(myCh999User.getName());
                    return false;
                }
                if (wcfCh999User.getPicUrl() != null && !wcfCh999User.getPicUrl().equals(myCh999User.getPicUrl())) {
                    System.out.println("evaluateCh999UserInfos:PicUrl-----------------------------");
                    System.out.println(wcfCh999User.getPicUrl());
                    System.out.println(myCh999User.getPicUrl());
                    return false;
                }

                if (wcfCh999User.getQuestion() != null && !wcfCh999User.getQuestion().equals(myCh999User.getQuestion())) {
                    System.out.println("evaluateCh999UserInfos:Question-----------------------------");
                    System.out.println(wcfCh999User.getQuestion());
                    System.out.println(myCh999User.getQuestion());
                    return false;
                }
                if (wcfCh999User.getScore() != null && !wcfCh999User.getScore().equals(myCh999User.getScore())) {
                    System.out.println("evaluateCh999UserInfos:Score-----------------------------");
                    System.out.println(wcfCh999User.getScore());
                    System.out.println(myCh999User.getScore());
                    return false;
                }
                if (CollectionUtils.isNotEmpty(wcfCh999User.getTags())) {
                    List<oaData.OrderBaseInfoNew.Info.EvaluateCh999UserInfos.Tags> wcfTags = wcfCh999User.getTags();
                    Map<Integer, PjTagsBO> myTags = myCh999User.getTags().stream().collect(Collectors.toMap(PjTagsBO::getTagId, o -> o));
                    for (oaData.OrderBaseInfoNew.Info.EvaluateCh999UserInfos.Tags wcfTag : wcfTags) {
                        Integer tagId = wcfTag.getTagId();
                        PjTagsBO myTag = myTags.get(tagId);
                        PjTagsBO wcfTagNew = new PjTagsBO();
                        BeanUtils.copyProperties(wcfTag, wcfTagNew);
                        if (!wcfTagNew.equals(myTag)) {
                            System.out.println("evaluateCh999UserInfos:Tags不一致--------------------------");
                            System.out.println(wcfTag);
                            System.out.println(myTag);
                            return false;
                        }
                    }
                }

                if (wcfCh999User.getZhiwu() != null && !wcfCh999User.getZhiwu().equals(myCh999User.getZhiwu())) {
                    System.out.println("evaluateCh999UserInfos:Zhiwu-----------------------------");
                    System.out.println(wcfCh999User.getZhiwu());
                    System.out.println(myCh999User.getZhiwu());
                    return false;
                }
            }
        }

        List<oaData.OrderBaseInfoNew.Info.EvaluateCh999UserInfos.Tags> wcfTags = wcfInfo.getRecommendationWebsiteTags();
        List<PjTagsBO> myTags = myInfo.getRecommendationWebsiteTags();
        Map<Integer, PjTagsBO> myTagsMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(myTags)) {
            myTagsMap = myTags.stream().collect(Collectors.toMap(PjTagsBO::getTagId, o -> o, (v1, v2) -> v1));
        }
        for (oaData.OrderBaseInfoNew.Info.EvaluateCh999UserInfos.Tags wcfTag : wcfTags) {
            PjTagsBO myTag = myTagsMap.get(wcfTag.getTagId());
            PjTagsBO wcfTagNew = new PjTagsBO();
            BeanUtils.copyProperties(wcfTag, wcfTagNew);
            if (!myTag.equals(wcfTagNew)) {
                System.out.println("recommendationWebsiteTags:推荐分Tags不一致--------------------------");
                System.out.println(wcfTag);
                System.out.println(myTag);
                return false;
            }
        }

        return true;
    }


}
