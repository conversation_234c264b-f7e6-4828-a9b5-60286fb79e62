package com.jiuji.oa.oacore.goldseed;

import com.jiuji.oa.oacore.cloud.WebCloud;
import com.jiuji.oa.oacore.cloud.bo.MiniFileBo;
import com.jiuji.oa.oacore.common.constant.GoldseedConstant;
import com.jiuji.oa.oacore.common.util.FileUtil;
import com.jiuji.oa.oacore.common.vo.FileVo;
import com.jiuji.oa.oacore.goldseed.service.GoldseedDataCompareService;
import com.jiuji.oa.oacore.goldseed.service.HwGoldSeedService;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.InputStream;

/**
 * @author: gengjiaping
 * @date: 2019/11/28
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class HwGoldSeedServiceTest {
    @Resource
    private HwGoldSeedService goldSeedService;

    @Autowired
    private WebCloud webCloud;

    @Autowired
    private GoldseedDataCompareService goldseedDataCompareService;


    @Test
    public void getStorageDataTest(){
        goldSeedService.upDisData(1);
    }

    @Test
    public void SupplierTypesTest(){
       String str = String.format("%02d", 11);
        log.info("");
    }

    @Test
    public void fileUploadTest() throws Exception{
        String fileName = "3231_CN-THRU _2019121201";
        MultipartFile file = FileUtil.fileToMultipartFile(GoldseedConstant.LOCAL_PATH+fileName+GoldseedConstant.FILE_TYPE_TXT);
       if(file== null || file.isEmpty()){
            log.info("文件为空");
       }
        R<MiniFileBo> fileUploadRet = webCloud.uploadFile(file,"oa");
        log.info("");
    }

    @Test
    public void minFileUpload() throws Exception{
        String uploadMiniFileURL = "/submit?collection=oa&count=3";
        String root = "http://*************:9333";
        String filePath = "F:\\goldseed\\201912\\20191219\\3231\\3231_CN_THRU_2019121901.txt";
        InputStream is = new BufferedInputStream(new FileInputStream(filePath));
        FileUtil.minFileUpload(root,uploadMiniFileURL,"3231_CN_THRU_2019121901.txt","text/plain",is);
    }

    @Test
    public void getFileListTest(){
        FileVo fileVo = goldseedDataCompareService.getFileList("2020-02-05");
        log.info("");
    }

}
