package com.jiuji.oa.oacore.oaorder;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.res.SubLogRes;

import com.jiuji.oa.oacore.oaorder.dao.OrderDetailMapper;
import com.jiuji.oa.oacore.oaorder.req.OrderDetailReq;
import com.jiuji.oa.oacore.oaorder.res.*;

import com.jiuji.oa.oacore.oaorder.service.OrderDetailService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.config.DynamicContextHolder;
import com.jiuji.wcf.wcfclient.constant.MethodName;
import com.jiuji.wcf.wcfclient.csharp.gen.oaData;
import com.jiuji.wcf.wcfclient.csharp.gen.recovermodel.recoverResult;
import com.jiuji.wcf.wcfclient.csharp.gen.recovermodel.recover_sub;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: gengjiaping
 * @date: 2019/11/19
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class OrderDetailServiceTest {

    @Autowired
    private SubLogsCloud subLogsCloud;
    @Autowired
    private OrderDetailMapper orderDetailMapper;
    @Autowired
    private OrderDetailService orderDetailService;
    @Autowired
    private OAStub oaStub;

    @Test
    public void calculatePriceTest() {
        R<List<SubLogRes>> subLogsListJSON = subLogsCloud.getSubLogsListJSON(Integer.parseInt("1440542"));
        System.out.println(subLogsListJSON.getData().get(0));
    }

    @Test
    public void calculatePriceTest2() {
        String str = "·转来自：[km]<a href='/addOrder/editOrder?SubID=399946&inputbid=1414917&showDel=1' " +
                "target='_blank'>399946-1414917</a>";
        String pattern = "wuliutrackShow((.*?),(d+),(.*?))";
        List<AreaListBO> allArea = orderDetailMapper.getAllArea();
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(str);
        System.out.println(m.matches());
        String group = m.group();
        String group1 = m.group(1);
    }

    @Test
    public void history() {
        DynamicContextHolder.setDB("oanewHis");
        ArrivalTimeBO sdate = orderDetailMapper.getSdate("7167805");
        System.out.println(sdate);
        DynamicContextHolder.setDB("oanew");
        ArrivalTimeBO sdate2 = orderDetailMapper.getSdate("7167805");
        System.out.println(sdate2);
    }

    @Test
    public void getJson() {
        String str1 = "{\n" +
                "  \"result_\": {\n" +
                "    \"stats\": 1,\n" +
                "    \"result\": null,\n" +
                "    \"data\": null\n" +
                "  },\n" +
                "  \"sub\": {\n" +
                "    \"sub_id\": 9083950,\n" +
                "    \"sub_date\": \"2017-12-21T13:12:24\",\n" +
                "    \"sub_check\": 3,\n" +
                "    \"subCheckName\": \"已完成\",\n" +
                "    \"sub_to\": \"杨丽利\",\n" +
                "    \"sub_tel\": \"\",\n" +
                "    \"sub_adds\": \"[马登镇]云南省大理州剑川县马登镇向善村123号\",\n" +
                "    \"sub_pay\": 1,\n" +
                "    \"subpayName\": \"网上支付\",\n" +
                "    \"comment\": \"\",\n" +
                "    \"sub_mobile\": \"13769235528\",\n" +
                "    \"area\": \"dc\",\n" +
                "    \"areaid\": 16,\n" +
                "    \"zitidianID\": \"\",\n" +
                "    \"userid\": 0,\n" +
                "    \"wuliucompany\": \"中通\",\n" +
                "    \"wuliuNo\": \"539029274585\",\n" +
                "    \"online_pay\": null,\n" +
                "    \"cityid\": 532931,\n" +
                "    \"delivery\": 4,\n" +
                "    \"deliveryName\": \"快递运输\",\n" +
                "    \"UserName\": null,\n" +
                "    \"userclass\": null,\n" +
                "    \"cityName\": \"云南省&nbsp;&nbsp;大理州&nbsp;&nbsp;剑川县\",\n" +
                "    \"subtype\": 0,\n" +
                "    \"yingfuM\": 980.00,\n" +
                "    \"jifens\": 98.0,\n" +
                "    \"yifuM\": 980.00,\n" +
                "    \"feeM\": 0.00,\n" +
                "    \"youhui1M\": 0.00,\n" +
                "    \"jidianM\": 0.00,\n" +
                "    \"Inuser\": null,\n" +
                "    \"youhuima_\": null,\n" +
                "    \"tradeDate\": \"2017-12-21T13:41:30\",\n" +
                "    \"tradeDate1\": \"2017-12-31T23:28:56\",\n" +
                "    \"subTypeName\": \"app\",\n" +
                "    \"zitifankuan\": 0.0,\n" +
                "    \"islock\": false,\n" +
                "    \"fromid\": 0,\n" +
                "    \"FreightFree\": false,\n" +
                "    \"sDate\": null,\n" +
                "    \"sTime\": null,\n" +
                "    \"isShop\": false,\n" +
                "    \"ch999_id\": null,\n" +
                "    \"beiState\": 0,\n" +
                "    \"isDebug\": null,\n" +
                "    \"isNahuo\": false,\n" +
                "    \"nahuoID\": null,\n" +
                "    \"expectTime\": \"2017-12-24T11:31:00\",\n" +
                "    \"SplitExpectTime\": null,\n" +
                "    \"coinM\": 0.00,\n" +
                "    \"save_money\": 0.0,\n" +
                "    \"userDate\": \"2017-12-23T00:00:00\",\n" +
                "    \"userTime\": 3,\n" +
                "    \"isSpecial\": 1,\n" +
                "    \"autoDelTime\": \"2017-12-22T13:14:00\",\n" +
                "    \"smallShop\": false,\n" +
                "    \"smallShopDsc\": null,\n" +
                "    \"ch999Userid\": 0,\n" +
                "    \"piaofid\": \"\",\n" +
                "    \"piaoid\": 0,\n" +
                "    \"piaohead\": null,\n" +
                "    \"customType\": null,\n" +
                "    \"piaoType\": 0,\n" +
                "    \"piaoStatus\": 0,\n" +
                "    \"isVip\": 0,\n" +
                "    \"isPj\": 0,\n" +
                "    \"shopType\": null,\n" +
                "    \"paisongAreaId\": null,\n" +
                "    \"dingjing\": 0.00,\n" +
                "    \"isException\": false,\n" +
                "    \"marketingId\": null\n" +
                "  },\n" +
                "  \"basket\": [\n" +
                "    {\n" +
                "      \"sub_id\": 0,\n" +
                "      \"basketid\": 22969711,\n" +
                "      \"ppriceid\": 57928,\n" +
                "      \"giftid\": 0,\n" +
                "      \"price\": 980.00,\n" +
                "      \"price1\": 980.00,\n" +
                "      \"basket_count\": 1,\n" +
                "      \"product_name\": \"华为 荣耀V9 play（JMM-AL10）全网通高配版\",\n" +
                "      \"product_color\": \"极光蓝 4GB+32GB\",\n" +
                "      \"product_img\": \"20170906163725175.jpg\",\n" +
                "      \"basket_date\": null,\n" +
                "      \"PlanId\": null,\n" +
                "      \"type\": \"0\",\n" +
                "      \"Mobile\": null,\n" +
                "      \"PackageType\": null,\n" +
                "      \"isptype\": null,\n" +
                "      \"ContractPeroid\": null,\n" +
                "      \"mkcInfo\": [\n" +
                "        {\n" +
                "          \"text\": null,\n" +
                "          \"mkc_id\": 60533155,\n" +
                "          \"ExpectToAreaTime\": null\n" +
                "        }\n" +
                "      ],\n" +
                "      \"haoma\": \"\",\n" +
                "      \"ismobile\": true,\n" +
                "      \"peizhi\": \"一充电器一数据线（内置电池，3000mAh，无耳机）\",\n" +
                "      \"imei\": \"865552039044841\",\n" +
                "      \"isHalfBuy\": false,\n" +
                "      \"halfBuyCount\": 0\n" +
                "    }\n" +
                "  ],\n" +
                "  \"zitiinfo\": null,\n" +
                "  \"areainfo\": null,\n" +
                "  \"logs\": [\n" +
                "    {\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 9083950,\n" +
                "      \"Comment\": \"发货10天以上，订单自动从【出库】更改为【已完成】\",\n" +
                "      \"InUser\": \"系统\",\n" +
                "      \"DTime\": \"2017-12-31T23:30:28.645+08:00\",\n" +
                "      \"Type\": 1.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 9083950,\n" +
                "      \"Comment\": \"交易完成,感谢您在九机网购物，欢迎您再次光临\",\n" +
                "      \"InUser\": \"系统\",\n" +
                "      \"DTime\": \"2017-12-31T23:30:28.642+08:00\",\n" +
                "      \"Type\": 1.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 9083950,\n" +
                "      \"Comment\": \"商品已由【中通】快递发出，快递单号为：539029274585\",\n" +
                "      \"InUser\": \"王闵婕\",\n" +
                "      \"DTime\": \"2017-12-24T11:32:15.138+08:00\",\n" +
                "      \"Type\": 1.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 9083950,\n" +
                "      \"Comment\": \"您的商品已经验货完成，正在等待配送。\",\n" +
                "      \"InUser\": \"王闵婕\",\n" +
                "      \"DTime\": \"2017-12-24T11:32:13.119+08:00\",\n" +
                "      \"Type\": 1.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 9083950,\n" +
                "      \"Comment\": \"销售明细单打印\",\n" +
                "      \"InUser\": \"邱瑶瑶\",\n" +
                "      \"DTime\": \"2017-12-21T13:43:03.654+08:00\",\n" +
                "      \"Type\": 1.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 9083950,\n" +
                "      \"Comment\": \"物流信息：中通快递，物流单号：539029274585\",\n" +
                "      \"InUser\": \"邱瑶瑶\",\n" +
                "      \"DTime\": \"2017-12-21T13:43:03.57+08:00\",\n" +
                "      \"Type\": 1.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 9083950,\n" +
                "      \"Comment\": \"物流信息：中通，物流单号：539029274585\",\n" +
                "      \"InUser\": \"邱瑶瑶\",\n" +
                "      \"DTime\": \"2017-12-21T13:43:03.548+08:00\",\n" +
                "      \"Type\": 1.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 9083950,\n" +
                "      \"Comment\": \"订单确认操作！\",\n" +
                "      \"InUser\": \"王荞焕\",\n" +
                "      \"DTime\": \"2017-12-21T13:17:30.424+08:00\",\n" +
                "      \"Type\": 1.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 9083950,\n" +
                "      \"Comment\": \"您的订单正在等待出库。\",\n" +
                "      \"InUser\": \"系统\",\n" +
                "      \"DTime\": \"2017-12-21T13:14:31.744+08:00\",\n" +
                "      \"Type\": 1.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 9083950,\n" +
                "      \"Comment\": \"收到支付宝：980.00元，交易号：2017122121001004840206448140收到货款，订单自动从【未确认】转为【已确认】状态\",\n" +
                "      \"InUser\": \"系统\",\n" +
                "      \"DTime\": \"2017-12-21T13:14:31.645+08:00\",\n" +
                "      \"Type\": 1.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 9083950,\n" +
                "      \"Comment\": \"订单提交成功，等待付款。\",\n" +
                "      \"InUser\": \"系统\",\n" +
                "      \"DTime\": \"2017-12-21T13:13:57.941+08:00\",\n" +
                "      \"Type\": 1.0,\n" +
                "      \"showType\": true\n" +
                "    }\n" +
                "  ],\n" +
                "  \"categoryLogs\": [\n" +
                "    {\n" +
                "      \"logCategory\": \"提交订单\",\n" +
                "      \"wlCompany\": null,\n" +
                "      \"wlNo\": null,\n" +
                "      \"wlId\": null,\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 0,\n" +
                "      \"Comment\": \"订单提交成功，等待付款。\",\n" +
                "      \"InUser\": \"系统\",\n" +
                "      \"DTime\": \"2017-12-21T13:13:57.941+08:00\",\n" +
                "      \"Type\": 0.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"logCategory\": \"订单付款\",\n" +
                "      \"wlCompany\": null,\n" +
                "      \"wlNo\": null,\n" +
                "      \"wlId\": null,\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 0,\n" +
                "      \"Comment\": \"收到支付宝：980.00元，交易号：2017122121001004840206448140收到货款，订单自动从【未确认】转为【已确认】状态\",\n" +
                "      \"InUser\": \"系统\",\n" +
                "      \"DTime\": \"2017-12-21T13:14:31.645+08:00\",\n" +
                "      \"Type\": 0.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"logCategory\": \"商品配送\",\n" +
                "      \"wlCompany\": null,\n" +
                "      \"wlNo\": null,\n" +
                "      \"wlId\": null,\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 0,\n" +
                "      \"Comment\": \"您的订单正在等待出库。\",\n" +
                "      \"InUser\": \"系统\",\n" +
                "      \"DTime\": \"2017-12-21T13:14:31.744+08:00\",\n" +
                "      \"Type\": 0.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"logCategory\": \"其他信息\",\n" +
                "      \"wlCompany\": null,\n" +
                "      \"wlNo\": null,\n" +
                "      \"wlId\": null,\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 0,\n" +
                "      \"Comment\": \"订单确认操作！\",\n" +
                "      \"InUser\": \"王荞焕\",\n" +
                "      \"DTime\": \"2017-12-21T13:17:30.424+08:00\",\n" +
                "      \"Type\": 0.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"logCategory\": \"商品配送\",\n" +
                "      \"wlCompany\": null,\n" +
                "      \"wlNo\": null,\n" +
                "      \"wlId\": null,\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 0,\n" +
                "      \"Comment\": \"物流信息：中通，物流单号：539029274585\",\n" +
                "      \"InUser\": \"邱瑶瑶\",\n" +
                "      \"DTime\": \"2017-12-21T13:43:03.548+08:00\",\n" +
                "      \"Type\": 0.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"logCategory\": \"商品配送\",\n" +
                "      \"wlCompany\": null,\n" +
                "      \"wlNo\": null,\n" +
                "      \"wlId\": null,\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 0,\n" +
                "      \"Comment\": \"物流信息：中通快递，物流单号：539029274585\",\n" +
                "      \"InUser\": \"邱瑶瑶\",\n" +
                "      \"DTime\": \"2017-12-21T13:43:03.57+08:00\",\n" +
                "      \"Type\": 0.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"logCategory\": \"其他信息\",\n" +
                "      \"wlCompany\": null,\n" +
                "      \"wlNo\": null,\n" +
                "      \"wlId\": null,\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 0,\n" +
                "      \"Comment\": \"销售明细单打印\",\n" +
                "      \"InUser\": \"邱瑶瑶\",\n" +
                "      \"DTime\": \"2017-12-21T13:43:03.654+08:00\",\n" +
                "      \"Type\": 0.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"logCategory\": \"商品配送\",\n" +
                "      \"wlCompany\": null,\n" +
                "      \"wlNo\": null,\n" +
                "      \"wlId\": null,\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 0,\n" +
                "      \"Comment\": \"您的商品已经验货完成，正在等待配送。\",\n" +
                "      \"InUser\": \"王闵婕\",\n" +
                "      \"DTime\": \"2017-12-24T11:32:13.119+08:00\",\n" +
                "      \"Type\": 0.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"logCategory\": \"商品配送\",\n" +
                "      \"wlCompany\": null,\n" +
                "      \"wlNo\": null,\n" +
                "      \"wlId\": null,\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 0,\n" +
                "      \"Comment\": \"商品已由【中通】快递发出，快递单号为：539029274585\",\n" +
                "      \"InUser\": \"王闵婕\",\n" +
                "      \"DTime\": \"2017-12-24T11:32:15.138+08:00\",\n" +
                "      \"Type\": 0.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"logCategory\": \"交易完成\",\n" +
                "      \"wlCompany\": null,\n" +
                "      \"wlNo\": null,\n" +
                "      \"wlId\": null,\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 0,\n" +
                "      \"Comment\": \"交易完成,感谢您在九机网购物，欢迎您再次光临\",\n" +
                "      \"InUser\": \"系统\",\n" +
                "      \"DTime\": \"2017-12-31T23:30:28.642+08:00\",\n" +
                "      \"Type\": 0.0,\n" +
                "      \"showType\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"logCategory\": \"订单变更\",\n" +
                "      \"wlCompany\": null,\n" +
                "      \"wlNo\": null,\n" +
                "      \"wlId\": null,\n" +
                "      \"_id\": 0,\n" +
                "      \"ID\": 0,\n" +
                "      \"SubID\": 0,\n" +
                "      \"Comment\": \"发货10天以上，订单自动从【出库】更改为【已完成】\",\n" +
                "      \"InUser\": \"系统\",\n" +
                "      \"DTime\": \"2017-12-31T23:30:28.645+08:00\",\n" +
                "      \"Type\": 0.0,\n" +
                "      \"showType\": true\n" +
                "    }\n" +
                "  ],\n" +
                "  \"type\": \"my\",\n" +
                "  \"reoverSub\": null,\n" +
                "  \"subCh999UserList\": [\n" +
                "    {\n" +
                "      \"ch999_id\": 1750,\n" +
                "      \"job\": 6,\n" +
                "      \"ch999_name\": \"周金\",\n" +
                "      \"evaluteID\": 0,\n" +
                "      \"headImg\": \"https://img2.ch999img.com/newstatic/1217/ff020981895a28.jpeg\",\n" +
                "      \"isReword\": false,\n" +
                "      \"avgPinFen\": \"NaN\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"Attachmentses\": []\n" +
                "}";
        JSONObject j = new JSONObject(true);
        System.out.println(j.parse(str1));
    }

    @Test
    public void testjson() {
        /*OrderDetailBO orderDetail = getOrderDetailBO(subBO.getSubId(), subBO.getUserId());
        String s = JSONObject.toJSONString(orderDetail);
        System.out.println(s);*/
    }

    @Test
    public void testEqual() {

        List<SubBO> subBOs = orderDetailMapper.getTestSub();
        for (SubBO subBO : subBOs) {
            BigDecimalValueFilter bigDecimalValueFilter = new BigDecimalValueFilter();

            oaData.ResultOrderDetai wcfOrderDetail = getWcfOrderDetail(Long.valueOf(subBO.getSubId()),
                    (int)subBO.getUserId());
            RoundAvgPf(wcfOrderDetail);
            String codeC = JSONObject.toJSONString(wcfOrderDetail, bigDecimalValueFilter);
            /*String codeC = JSON.toJSONStringWithDateFormat(wcfOrderDetail, "yyyy-MM-dd'T'HH:mm:ss",
                    SerializerFeature.WriteDateUseDateFormat);*/
            //,SerializerFeature.WriteMapNullValue
            OrderDetailBO orderDetail = getOrderDetailBO(subBO.getSubId(), subBO.getUserId());
            operateOrder(orderDetail);
            String codeMy = JSONObject.toJSONString(orderDetail, bigDecimalValueFilter);
            JsonParser parser1 = new JsonParser();
            JsonObject obj1 = (JsonObject)parser1.parse(codeC);
            JsonParser parser2 = new JsonParser();
            JsonObject obj2 = (JsonObject)parser2.parse(codeMy);
            if (!obj1.equals(obj2)) {
                System.out.println(codeC);
                System.out.println(codeMy);
            }
        }
    }

    private void operateOrder(OrderDetailBO orderDetail) {
        SubBO sub = orderDetail.getSub();
        if (null != sub) {
            sub.setUserDate(null);
        }
        RecoverResultBO reoverSub = orderDetail.getReoverSub();
        if(null!=reoverSub){
            ReCoverSubBO sub1 = reoverSub.getSub();
            if(null!=sub1){
                sub1.setDTime(null);
            }
        }
    }

    @Test
    public void testww() {
        OrderDetailBO orderDetail = getOrderDetailBO(9156423, 3499200l);
        System.out.println("ss");

    }

    private void RoundAvgPf(oaData.ResultOrderDetai wcfOrderDetail) {
        try {
            List<oaData.subCh999User> subCh999UserList = wcfOrderDetail.getSubCh999UserList();
            if (null != subCh999UserList) {
                for (oaData.subCh999User subCh999User : subCh999UserList) {
                    if (!"非数字".equals(subCh999User.getAvgPinFen()) && !"NaN".equals(subCh999User.getAvgPinFen())) {
                        BigDecimal bigDecimal = new BigDecimal(subCh999User.getAvgPinFen());
                        subCh999User.setAvgPinFen(bigDecimal.setScale(2, RoundingMode.HALF_DOWN).toString());
                    } else if ("NaN".equals(subCh999User.getAvgPinFen())) {
                        subCh999User.setAvgPinFen("非数字");
                    }
                }
            }
            oaData.sub sub = wcfOrderDetail.getSub();
            if (null != sub) {
                sub.setUserDate(null);
            }
            recoverResult reoverSub = wcfOrderDetail.getReoverSub();
            if(null!=reoverSub){
                recover_sub sub1 = reoverSub.getSub();
                if(null!=sub1){
                    sub1.setDtime(null);
                }
            }
        } catch (Exception e) {
            System.out.println(wcfOrderDetail.getSubCh999UserList());
        }
    }

    private OrderDetailBO getOrderDetailBO(int subId, long userId) {
        OrderDetailReq orderDetailReq = new OrderDetailReq();
        orderDetailReq.setSubId(String.valueOf(subId));
        orderDetailReq.setZiTiId("");
        orderDetailReq.setUserId(userId);
        orderDetailReq.setXTenant(0);
        return orderDetailService.getOrderDetail(orderDetailReq);
    }

    private oaData.ResultOrderDetai getWcfOrderDetail(Long sub_id, Integer userid) {
        Map<String, Object> param = new HashMap<>();
        param.put("sub_id", sub_id);
        param.put("userid", userid);
        param.put("zitiID", "");
        param.put("recover_subId", 0);
        return oaStub.call(MethodName.OA.ORDER_DETAIL, param);
    }


}
