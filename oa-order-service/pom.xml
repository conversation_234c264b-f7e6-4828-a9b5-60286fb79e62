<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>oa-order</artifactId>
        <groupId>com.jiuji.oa</groupId>
        <version>1.0.4-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>oa-order-service</artifactId>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <!--test-->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.6.2</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>1.7.0</version>
        </dependency>

        <dependency>
            <groupId>com.jiuji.wcf</groupId>
            <artifactId>wcfclient</artifactId>
            <version>1.11.88</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jiuji.oa</groupId>
            <artifactId>oa-order-vo</artifactId>
            <version>${parent.version}</version>
        </dependency>
        <dependency>
            <artifactId>oa-salary-vo</artifactId>
            <groupId>com.jiuji.oa</groupId>
            <version>1.0.7-SNAPSHOT</version>
        </dependency>
        <dependency>
            <artifactId>orginfo-stub</artifactId>
            <groupId>com.jiuji.oa</groupId>
            <version>1.2.60-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jiuji.oaapi</groupId>
            <artifactId>oaapi-cloud</artifactId>
            <version>0.1.7-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jiuji.infra</groupId>
            <artifactId>common-utils</artifactId>
            <version>0.0.6-SNAPSHOT</version>
        </dependency>
        <dependency>
            <artifactId>foundation-db-starter</artifactId>
            <groupId>com.jiuji.tc</groupId>
            <version>${jiuji.foundation.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ecwid.consul</groupId>
            <artifactId>consul-api</artifactId>
            <version>1.4.2</version>
        </dependency>

        <dependency>
            <groupId>com.jiuji.infra</groupId>
            <artifactId>common-seata</artifactId>
            <version>0.1</version>
        </dependency>
        <dependency>
            <groupId>com.jiuji.product</groupId>
            <artifactId>product-cloud</artifactId>
            <version>0.0.7</version>
        </dependency>
        <dependency>
            <artifactId>foundation-message-starter</artifactId>
            <groupId>com.jiuji.tc</groupId>
            <version>${jiuji.foundation.version}</version>
        </dependency>
        <dependency>
            <artifactId>oa-office-stub</artifactId>
            <groupId>com.jiuji.oa.office</groupId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!--spring cloud-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <!--切换到consul依赖项-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-consul-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-starter-redis</artifactId>
            <version>2.5.14</version>
        </dependency>
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-starter-redis-lettuce</artifactId>
            <version>2.5.11</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-el</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-websocket</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>9.0.54</version>
            <exclusions>
                <exclusion>
                    <artifactId>tomcat-annotations-api</artifactId>
                    <groupId>org.apache.tomcat</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-el</artifactId>
            <version>9.0.54</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-websocket</artifactId>
            <version>9.0.54</version>
            <exclusions>
                <exclusion>
                    <artifactId>tomcat-embed-core</artifactId>
                    <groupId>org.apache.tomcat.embed</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <!--<dependency>-->
        <!--<groupId>org.springframework.boot</groupId>-->
        <!--<artifactId>spring-boot-starter-data-mongodb</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>${swagger.version}</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>${swagger.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jiuji.oa</groupId>
            <artifactId>nc-segments-stub</artifactId>
            <version>1.7-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jiuji.oa</groupId>
            <artifactId>oa-train-stub</artifactId>
            <version>1.0.16-SNAPSHOT</version>
        </dependency>



        <!--增加swagger-bootstrap-ui-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-ui</artifactId>
            <version>3.0.2</version>
        </dependency>

        <!--hutool java工具类库-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatisplus.version}</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>${mybatisplus.version}</version>
        </dependency>

        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.1.10</version>
        </dependency>
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.3</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-mock</artifactId>
            <version>2.0.8</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>3.17</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.17</version>
        </dependency>
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>3.8.2</version>
        </dependency>
        <dependency>
            <artifactId>foundation-rabbitmq-starter</artifactId>
            <groupId>com.jiuji.tc</groupId>
            <version>${jiuji.foundation.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jiuji.oa</groupId>
            <artifactId>loginfo-stub</artifactId>
            <version>1.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jiuji.cloud</groupId>
            <artifactId>oa-office-cloud</artifactId>
            <version>1.0.7-SNAPSHOT</version>
        </dependency>
        <!--prometheus JVM监控数据上报-->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-core</artifactId>
            <version>1.5.1</version>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <version>1.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
<!--        抖音sdk-->
        <dependency>
            <groupId>com.doudian</groupId>
            <artifactId>doudian-sdk-java</artifactId>
        </dependency>
        <!-- saasmanager-->
        <dependency>
            <groupId>com.jiuji.infra</groupId>
            <artifactId>saas-manager-utils</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.jcraft/jsch -->
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.55</version>
        </dependency>
        <dependency>
            <groupId>com.jiuji.oa.stock</groupId>
            <artifactId>baozun-util</artifactId>
        </dependency>
        <!--父pom中管理的依赖-->
        <dependency>
            <artifactId>common-vo</artifactId>
            <groupId>com.jiuji.tc</groupId>
        </dependency>
        <dependency>
            <artifactId>common-utils</artifactId>
            <groupId>com.jiuji.tc</groupId>
        </dependency>
        <!-- Log4j2 异步支持 -->
        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
        </dependency>
        <!-- 日志 Log4j2 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <!--日志  log4j2 升级-->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-jul</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.dubasdey</groupId>
            <artifactId>log4j2-jsonevent-layout</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <!-- easyexcel 依赖 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jiuji.cloud</groupId>
            <artifactId>after-cloud</artifactId>
        </dependency>
        <dependency>
            <groupId>com.seed</groupId>
            <artifactId>seed</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.openapi</groupId>
            <artifactId>sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan</groupId>
            <artifactId>MtOpJavaSDK</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jiuji.cloud</groupId>
            <artifactId>org-cloud</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou</groupId>
            <artifactId>sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kuaishou</groupId>
            <artifactId>sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ch999.common</groupId>
            <artifactId>utils</artifactId>
        </dependency>
        <!--延迟队列接入-->
        <dependency>
            <groupId>com.jiuji.infra</groupId>
            <artifactId>common-delay-queue</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jiuji.oa</groupId>
            <artifactId>oa-order-stub</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <artifactId>oa-stock-cloud</artifactId>
            <groupId>com.jiuji.stock</groupId>
        </dependency>
        <dependency>
            <groupId>com.taobao</groupId>
            <artifactId>sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.30</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <!-- 跳过单元测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>

            <!-- Docker maven plugin -->
            <plugin>
            <groupId>com.spotify</groupId>
            <artifactId>docker-maven-plugin</artifactId>
            <version>1.2.2</version>
            <configuration>
                <dockerDirectory>src/main/docker</dockerDirectory>
                <resources>
                    <resource>
                        <targetPath>/</targetPath>
                        <directory>${project.build.directory}</directory>
                        <include>${project.build.finalName}.jar</include>
                        <include>classes/configTemplate/</include>
                    </resource>
                </resources>
            </configuration>
            <!-- Docker maven plugin -->
            </plugin>
        </plugins>
    </build>

</project>
